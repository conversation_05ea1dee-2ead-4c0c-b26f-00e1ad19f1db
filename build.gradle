plugins {
    id 'org.springframework.boot' version '2.2.11.RELEASE'
    id 'io.spring.dependency-management' version '1.0.10.RELEASE'
    id 'java'
    id 'war'
}

group = 'com.xylink'
version = '0.0.1'
sourceCompatibility = '1.8'

repositories {
    maven { 
        url "http://maven.xylink.com:8081/nexus/content/groups/public/" 
        allowInsecureProtocol = true
    }
}

bootWar {
    archiveFileName = 'ROOT.war'
}
ext['logback.version'] = '1.2.10'
configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    resolutionStrategy.eachDependency { DependencyResolveDetails details ->
        if (details.requested.group == 'com.fasterxml.jackson.core') {
            // 修复高危漏洞
            details.useVersion '2.14.2'
        }
        if (details.requested.group == 'org.apache.tomcat.embed') {
            // 修复高危漏洞
            details.useVersion '9.0.60'
        }
    }
}

configurations {
    compile.exclude group:'org.apache.logging.log4j'
}

configure(allprojects) {

}

compileJava {
	dependsOn 'processResources'
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    providedRuntime 'org.springframework.boot:spring-boot-starter-tomcat'
    implementation 'org.springframework.boot:spring-boot-starter-data-ldap'
    implementation 'org.apache.httpcomponents:httpclient:4.5.5'
    implementation("com.google.guava:guava:30.1-jre")
    
    // 升级到兼容的JUnit 5版本
    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    
    // 升级JUnit 5到兼容Eclipse的版本
    testImplementation platform('org.junit:junit-bom:5.8.2')
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'org.junit.platform:junit-platform-launcher'
    testImplementation 'org.mockito:mockito-core'
    testImplementation 'org.mockito:mockito-junit-jupiter'
}

test {
    useJUnitPlatform()
}
