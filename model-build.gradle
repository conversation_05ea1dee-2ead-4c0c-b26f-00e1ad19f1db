Properties properties = new Properties()
properties.load(project.rootProject.file('info.properties').newDataInputStream())
def profile = project.hasProperty('profile')? project['profile'] : 'dev'
def artifact_type = ''
if(profile == 'dev') {
     artifact_type = '-SNAPSHOT'
}
version = properties.getProperty("artifact_version")+artifact_type

buildscript {
     repositories {
          maven {
               url = 'http://**************:8081/nexus/content/groups/public/'
          }
     }
}

apply plugin: 'java'
apply plugin: 'maven'
apply plugin: 'idea'
apply plugin: 'maven-publish'

sourceCompatibility = 1.7
targetCompatibility = 1.7

sourceSets {
     main.java.srcDirs = ['src/main/java/com/ainemo/contact/model']
     main.resources.srcDirs = []
}

compileJava {
     dependsOn 'processResources'
}
repositories {
     maven {
          url = 'http://**************:8081/nexus/content/groups/public/'
     }
}


dependencies {
     compile("org.apache.commons:commons-lang3:3.0")
}


task wrapper(type: Wrapper) {
     gradleVersion = '2.10'
}

publishing {
     publications {
          maven(MavenPublication) {
               groupId 'com.ainemo'
               artifactId 'contact-model'
               version version
               from components.java
          }
     }
     repositories {
          maven {
               if(project.version.endsWith('-SNAPSHOT')) {
                    url "http://**************:8081/nexus/content/repositories/snapshots/"
               } else {
                    url "http://**************:8081/nexus/content/repositories/releases/"
               }
               credentials {
                    username = properties.getProperty("repo_username")
                    password = properties.getProperty("repo_password")
               }
          }
     }
}
