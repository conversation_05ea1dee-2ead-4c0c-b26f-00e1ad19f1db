

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
        <html lang="en">
    <!-- GENERATED FILE, DO NOT EDIT, EDIT THE XML FILE IN xdocs INSTEAD! -->
    <head>
      <META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
        <title>Apache Ant - Subversion Ant Library</title>
        <link type="text/css" href="../../page.css" rel="stylesheet">
        </head>

    <body>
      <p class="navpath">
        <script src="../../breadcrumbs.js" language="JavaScript" type="text/javascript"></script>
      </p>

      <div class="logobar">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td align="left"><img border="0" alt="Apache Ant site" src="../../images/group-logo.gif"></td>
            <td align="center" width="100%"><img alt="Apache Ant logo" border="0" src="../../images/project-logo.gif"></td>
            <td align="right">
              <form target="_blank" onsubmit="q.value = query.value + ' site:ant.apache.org'" action="http://www.google.com/search" method="get">
                <table summary="search" border="0" cellspacing="0" cellpadding="0" bgcolor="#4C6C8F">
                  <tr>
                    <td colspan="3"><img height="10" width="1" alt="" src="../../images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img height="1" width="1" alt="" src="../../images/spacer.gif"></td>
                    <td nowrap="nowrap" class="searchcaption">
                      <input name="q" type="hidden">
                      <input size="15" id="query" type="text">
                      <img height="1" width="5" alt="" src="../../images/spacer.gif">
                      <input name="Search" value="Search" type="submit">
                      <br>
                      the Apache Ant site
                    </td>
                    <td><img height="1" width="1" alt="" src="../../images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img alt="" border="0" height="10" width="9" src="../../images/search-left.gif"></td>
                    <td><img height="1" width="1" alt="" src="../../images/spacer.gif"></td>
                    <td><img alt="" border="0" height="10" width="9" src="../../images/search-right.gif"></td>
                  </tr>
                </table>
              </form>
            </td>
          </tr>
        </table>
      </div>

                  <div class="tab">
              <table summary="tab bar" border="0" cellpadding="0" cellspacing="0">
                <tr>
                                  <td width="5"><img alt="" height="8" width="8" src="../../images/spacer.gif"></td><td valign="bottom">
                      <table summary="non selected tab" style="height: 1.4em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                          <td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="../../images/tab-left.gif"></td><td valign="middle" bgcolor="#B2C4E0"><a href="../../index.html"><font size="2" face="Arial, Helvetica, Sans-serif">Home</font></a></td><td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="../../images/tab-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                                    <td width="8"><img alt="" height="5" width="8" src="../../images/spacer.gif"></td><td valign="bottom">
                      <table summary="selected tab" style="height: 1.5em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                        <td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="../../images/tabSel-left.gif"></td><td valign="middle" bgcolor="#4C6C8F"><font color="#ffffff" size="2" face="Arial, Helvetica, Sans-serif"><b>Projects</b></font></td><td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="../../images/tabSel-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                            </tr>
              </table>
            </div>

      <div class="bluebar"></div>
                                                              
  <div class="menucontainer">

    <div class="menu">
      <ul>
              <li class="menuheader">Projects
          <ul>
                            <li>
                                    <a href="../../projects/index.html">Welcome</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Ant Libraries
          <ul>
                            <li>
                                    <a href="../../antlibs/index.html">Introduction</a>
                                </li>
                            <li>
                                    <a href="../../antlibs/charter.html">Charter</a>
                                </li>
                            <li>
                                    <a href="../../antlibs/proper.html">Ant Libraries</a>
                                </li>
                            <li>
                                    <a href="../../antlibs/sandbox.html">Sandbox Ant Libraries</a>
                                </li>
                            <li>
                                    <a href="http://ant.apache.org/antlibs/bindownload.cgi">Binary Distributions</a>
                                </li>
                            <li>
                                    <a href="http://ant.apache.org/antlibs/srcdownload.cgi">Source Distributions</a>
                                </li>
                      </ul>
        </li>
            </ul>
    </div>
    <img style="float: left" height="10" width="10" border="0" alt="" src="../../images/menu-left.gif">
    <img style="float: right" height="10" width="10" border="0" alt="" src="../../images/menu-right.gif">
  </div>
      <div class="lightbluebar">&nbsp;</div>
  <div class="main">
  <div class="content">
    <h1 class="title">Subversion Ant Library</h1>
            <h3 class="section">
      <a name="Idea"></a>
      Idea
    </h3>
                        <p>The main purpose of this Ant library is to provide the same
      level of support that Ant provides for CVS.  This means the
      tasks are wrappers on top of the command line client (read: you
      still need to install an svn client) and there is not much more
      than running the executable and creating some reports.</p>
                                <p>If you are looking for projects that aim at more, there are
      better alternatives, for example <a href="http://subclipse.tigris.org/svnant.html">Subclipse's Ant
      task</a> or <a href="http://tmate.org/svn/ant.html">JavaSVN</a>.</p>
                        <h3 class="section">
      <a name="Tasks"></a>
      Tasks
    </h3>
                              <h4 class="subsection">
        <a name="svn"></a>
        svn
      </h4>
                        <p>A very thin layer on top of the command line executable,
        comparable to <a href="http://ant.apache.org/manual/CoreTasks/cvs.html">the CVS
        task</a>.</p>
                                                          <h4 class="subsection">
        <a name="changelog"></a>
        changelog
      </h4>
                        <p>Creates a log of change comments between two revisions,
        comparable to <a href="http://ant.apache.org/manual/CoreTasks/changelog.html">CvsChangeLog</a>.</p>
                                                          <h4 class="subsection">
        <a name="*diff"></a>
        *diff
      </h4>
                        <p><code>&lt;tagdiff&gt;</code> creates a differences report
        for the changes between two tags or branches.</p>
                                <p><code>&lt;revisiondiff&gt;</code> creates a differences report
        for the changes between two revisions.</p>
                                <p>Together comparable to <a href="http://ant.apache.org/manual/CoreTasks/cvstagdiff.html">CvsTagDiff</a>.</p>
                                            <h3 class="section">
      <a name="Examples"></a>
      Examples
    </h3>
        
    </div>
  </div>

        <p class="copyright">
        <script type="text/javascript" language="JavaScript"><!--
                document.write(" - "+"Last Published: " + document.lastModified);
              //  -->
        </script>
      </p>
    </body>
  </html>











