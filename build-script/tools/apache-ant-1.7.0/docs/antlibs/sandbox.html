

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
        <html lang="en">
    <!-- GENERATED FILE, DO NOT EDIT, EDIT THE XML FILE IN xdocs INSTEAD! -->
    <head>
      <META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
        <title>Apache Ant - Ant Libraries - The Sandbox</title>
        <link type="text/css" href="../page.css" rel="stylesheet">
        </head>

    <body>
      <p class="navpath">
        <script src="../breadcrumbs.js" language="JavaScript" type="text/javascript"></script>
      </p>

      <div class="logobar">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td align="left"><img border="0" alt="Apache Ant site" src="../images/group-logo.gif"></td>
            <td align="center" width="100%"><img alt="Apache Ant logo" border="0" src="../images/project-logo.gif"></td>
            <td align="right">
              <form target="_blank" onsubmit="q.value = query.value + ' site:ant.apache.org'" action="http://www.google.com/search" method="get">
                <table summary="search" border="0" cellspacing="0" cellpadding="0" bgcolor="#4C6C8F">
                  <tr>
                    <td colspan="3"><img height="10" width="1" alt="" src="../images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img height="1" width="1" alt="" src="../images/spacer.gif"></td>
                    <td nowrap="nowrap" class="searchcaption">
                      <input name="q" type="hidden">
                      <input size="15" id="query" type="text">
                      <img height="1" width="5" alt="" src="../images/spacer.gif">
                      <input name="Search" value="Search" type="submit">
                      <br>
                      the Apache Ant site
                    </td>
                    <td><img height="1" width="1" alt="" src="../images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img alt="" border="0" height="10" width="9" src="../images/search-left.gif"></td>
                    <td><img height="1" width="1" alt="" src="../images/spacer.gif"></td>
                    <td><img alt="" border="0" height="10" width="9" src="../images/search-right.gif"></td>
                  </tr>
                </table>
              </form>
            </td>
          </tr>
        </table>
      </div>

                  <div class="tab">
              <table summary="tab bar" border="0" cellpadding="0" cellspacing="0">
                <tr>
                                  <td width="5"><img alt="" height="8" width="8" src="../images/spacer.gif"></td><td valign="bottom">
                      <table summary="non selected tab" style="height: 1.4em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                          <td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="../images/tab-left.gif"></td><td valign="middle" bgcolor="#B2C4E0"><a href="../index.html"><font size="2" face="Arial, Helvetica, Sans-serif">Home</font></a></td><td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="../images/tab-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                                    <td width="8"><img alt="" height="5" width="8" src="../images/spacer.gif"></td><td valign="bottom">
                      <table summary="selected tab" style="height: 1.5em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                        <td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="../images/tabSel-left.gif"></td><td valign="middle" bgcolor="#4C6C8F"><font color="#ffffff" size="2" face="Arial, Helvetica, Sans-serif"><b>Projects</b></font></td><td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="../images/tabSel-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                            </tr>
              </table>
            </div>

      <div class="bluebar"></div>
                                                              
  <div class="menucontainer">

    <div class="menu">
      <ul>
              <li class="menuheader">Projects
          <ul>
                            <li>
                                    <a href="../projects/index.html">Welcome</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Ant Libraries
          <ul>
                            <li>
                                    <a href="../antlibs/index.html">Introduction</a>
                                </li>
                            <li>
                                    <a href="../antlibs/charter.html">Charter</a>
                                </li>
                            <li>
                                    <a href="../antlibs/proper.html">Ant Libraries</a>
                                </li>
                            <li>
                                    <a href="../antlibs/sandbox.html">Sandbox Ant Libraries</a>
                                </li>
                            <li>
                                    <a href="http://ant.apache.org/antlibs/bindownload.cgi">Binary Distributions</a>
                                </li>
                            <li>
                                    <a href="http://ant.apache.org/antlibs/srcdownload.cgi">Source Distributions</a>
                                </li>
                      </ul>
        </li>
            </ul>
    </div>
    <img style="float: left" height="10" width="10" border="0" alt="" src="../images/menu-left.gif">
    <img style="float: right" height="10" width="10" border="0" alt="" src="../images/menu-right.gif">
  </div>
      <div class="lightbluebar">&nbsp;</div>
  <div class="main">
  <div class="content">
    <h1 class="title">Ant Libraries - The Sandbox</h1>
            <h3 class="section">
      <a name="Ant Libraries - The Sandbox"></a>
      Ant Libraries - The Sandbox
    </h3>
                        <p>The sandbox is the place where new Ant Libraries start their
      life, it is a playground for Ant committers and other
      contributors who find committers to sponsor their ideas.</p>
                                <p>The sandbox is no dumping ground.  If a Sandbox Ant Library
      fails to attract interest within a reasonable amount of time, it
      gets removed from the sandbox.</p>
                        <h3 class="section">
      <a name="Current Sandbox Ant Libraries"></a>
      Current Sandbox Ant Libraries
    </h3>
                              <h4 class="subsection">
        <a name="GenDoc - Generate the manual for Ant Tasks from their sources"></a>
        GenDoc - Generate the manual for Ant Tasks from their sources
      </h4>
                        <p>Most of the information needed for writing the manual is inside the sources: attributes, nested elements (especially
        inherited one). GenDoc collects these information and generates the manual as xml page. Following steps transform this
        xml into the final format (HTML in the first step, PDF may follow).</p>
                                      <table class="ForrestTable" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          SVN URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://svn.apache.org/repos/asf/ant/sandbox/antlibs/gendoc/">http://svn.apache.org/repos/asf/ant/sandbox/antlibs/gendoc/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          ViewSVN:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://svn.apache.org/viewvc/ant/sandbox/antlibs/gendoc/">http://svn.apache.org/viewvc/ant/sandbox/antlibs/gendoc/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Ant compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.7.x
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Added to sandbox:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          2005-04-15
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Sponsoring Committers
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
           
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="HTTP - tasks for handling HTTP requests"></a>
        HTTP - tasks for handling HTTP requests
      </h4>
                        <p>This antlib contains tasks to make the basic HTTP requests: get, post, head, put, with Basicauthentication.</p>
                                      <table class="ForrestTable" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          SVN URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://svn.apache.org/repos/asf/ant/sandbox/antlibs/http/">http://svn.apache.org/repos/asf/ant/sandbox/antlibs/http/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          ViewSVN:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://svn.apache.org/viewvc/ant/sandbox/antlibs/http/">http://svn.apache.org/viewvc/ant/sandbox/antlibs/http/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Ant compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.7.x
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Added to sandbox:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          2006-06-27
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Sponsoring Committers
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
            
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="VSS - Microsoft Visual SourceSafe Tasks"></a>
        VSS - Microsoft Visual SourceSafe Tasks
      </h4>
                        <p>This antlib provides an interface to the Microsoft Visual SourceSafe SCM. The original tasks 
        (org.apache.tools.ant.taskdefs.optional.vss) have been expanded upon in this antlib. 
        Some fixes to issues in the original tasks have also been incorporated.</p>
                                      <table class="ForrestTable" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          SVN URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://svn.apache.org/repos/asf/ant/sandbox/antlibs/vss/">http://svn.apache.org/repos/asf/ant/sandbox/antlibs/vss/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          ViewSVN:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://svn.apache.org/viewvc/ant/sandbox/antlibs/vss/">http://svn.apache.org/viewvc/ant/sandbox/antlibs/vss/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Ant compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.7.x
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Added to sandbox:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          2006-04-26  
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Sponsoring Committers
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
            
      </td>
      </tr>
          </table>
                                    
    </div>
  </div>

        <p class="copyright">
        <script type="text/javascript" language="JavaScript"><!--
                document.write(" - "+"Last Published: " + document.lastModified);
              //  -->
        </script>
      </p>
    </body>
  </html>











