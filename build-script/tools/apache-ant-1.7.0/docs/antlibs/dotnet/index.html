

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
        <html lang="en">
    <!-- GENERATED FILE, DO NOT EDIT, EDIT THE XML FILE IN xdocs INSTEAD! -->
    <head>
      <META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
        <title>Apache Ant - .NET Ant Library</title>
        <link type="text/css" href="../../page.css" rel="stylesheet">
        </head>

    <body>
      <p class="navpath">
        <script src="../../breadcrumbs.js" language="JavaScript" type="text/javascript"></script>
      </p>

      <div class="logobar">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td align="left"><img border="0" alt="Apache Ant site" src="../../images/group-logo.gif"></td>
            <td align="center" width="100%"><img alt="Apache Ant logo" border="0" src="../../images/project-logo.gif"></td>
            <td align="right">
              <form target="_blank" onsubmit="q.value = query.value + ' site:ant.apache.org'" action="http://www.google.com/search" method="get">
                <table summary="search" border="0" cellspacing="0" cellpadding="0" bgcolor="#4C6C8F">
                  <tr>
                    <td colspan="3"><img height="10" width="1" alt="" src="../../images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img height="1" width="1" alt="" src="../../images/spacer.gif"></td>
                    <td nowrap="nowrap" class="searchcaption">
                      <input name="q" type="hidden">
                      <input size="15" id="query" type="text">
                      <img height="1" width="5" alt="" src="../../images/spacer.gif">
                      <input name="Search" value="Search" type="submit">
                      <br>
                      the Apache Ant site
                    </td>
                    <td><img height="1" width="1" alt="" src="../../images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img alt="" border="0" height="10" width="9" src="../../images/search-left.gif"></td>
                    <td><img height="1" width="1" alt="" src="../../images/spacer.gif"></td>
                    <td><img alt="" border="0" height="10" width="9" src="../../images/search-right.gif"></td>
                  </tr>
                </table>
              </form>
            </td>
          </tr>
        </table>
      </div>

                  <div class="tab">
              <table summary="tab bar" border="0" cellpadding="0" cellspacing="0">
                <tr>
                                  <td width="5"><img alt="" height="8" width="8" src="../../images/spacer.gif"></td><td valign="bottom">
                      <table summary="non selected tab" style="height: 1.4em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                          <td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="../../images/tab-left.gif"></td><td valign="middle" bgcolor="#B2C4E0"><a href="../../index.html"><font size="2" face="Arial, Helvetica, Sans-serif">Home</font></a></td><td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="../../images/tab-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                                    <td width="8"><img alt="" height="5" width="8" src="../../images/spacer.gif"></td><td valign="bottom">
                      <table summary="selected tab" style="height: 1.5em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                        <td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="../../images/tabSel-left.gif"></td><td valign="middle" bgcolor="#4C6C8F"><font color="#ffffff" size="2" face="Arial, Helvetica, Sans-serif"><b>Projects</b></font></td><td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="../../images/tabSel-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                            </tr>
              </table>
            </div>

      <div class="bluebar"></div>
                                                              
  <div class="menucontainer">

    <div class="menu">
      <ul>
              <li class="menuheader">Projects
          <ul>
                            <li>
                                    <a href="../../projects/index.html">Welcome</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Ant Libraries
          <ul>
                            <li>
                                    <a href="../../antlibs/index.html">Introduction</a>
                                </li>
                            <li>
                                    <a href="../../antlibs/charter.html">Charter</a>
                                </li>
                            <li>
                                    <a href="../../antlibs/proper.html">Ant Libraries</a>
                                </li>
                            <li>
                                    <a href="../../antlibs/sandbox.html">Sandbox Ant Libraries</a>
                                </li>
                            <li>
                                    <a href="http://ant.apache.org/antlibs/bindownload.cgi">Binary Distributions</a>
                                </li>
                            <li>
                                    <a href="http://ant.apache.org/antlibs/srcdownload.cgi">Source Distributions</a>
                                </li>
                      </ul>
        </li>
            </ul>
    </div>
    <img style="float: left" height="10" width="10" border="0" alt="" src="../../images/menu-left.gif">
    <img style="float: right" height="10" width="10" border="0" alt="" src="../../images/menu-right.gif">
  </div>
      <div class="lightbluebar">&nbsp;</div>
  <div class="main">
  <div class="content">
    <h1 class="title">.NET Ant Library</h1>
            <h3 class="section">
      <a name=".NET Ant Library 1.0"></a>
      .NET Ant Library 1.0
    </h3>
                        <h3>November 6, 2006 - Apache .NET Ant Library 1.0
      Available</h3>
                                <p>Apache .NET Ant Library 1.0 is now available for
      download as <a href="http://ant.apache.org/antlibs/bindownload.cgi">binary</a>
      or <a href="http://ant.apache.org/antlibs/srcdownload.cgi">source</a>
      release.</p>
                        <h3 class="section">
      <a name="Idea"></a>
      Idea
    </h3>
                        <p>This library doesn't strive to replace NAnt or MSBuild, its
      main purpose is to help those of us who work on projects
      crossing platform boundaries.  With this library you can use Ant
      to build and test the Java as well as the .NET parts of your
      project.</p>
                                <p>This library provides a special version of the
      <code>&lt;exec&gt;</code> task tailored to run .NET executables.
      On Windows it will assume the Microsoft framework is around and
      run the executable directly, while it will invoke Mono on any
      other platform.  Of course you can override these
      assumptions.</p>
                                <p>Based on this a few tasks to run well known .NET utilities
      from within Ant are provided, namely tasks to run <a href="http://www.nunit.org/">NUnit</a>, <a href="http://nant.sf.net/">NAnt</a>, <a href="http://forums.microsoft.com/MSDN/ShowForum.aspx?ForumID=27&amp;SiteID=1">MSBuild</a>
      and the <a href="http://wix.sf.net/">Wix</a> toolkit.</p>
                                <p>The initial .NET tasks of Ant (compiler tasks for C#, J# and VB.NET
      for example) have also been moved to this Antlib and will see further
      development here.</p>
                        <h3 class="section">
      <a name="Tasks"></a>
      Tasks
    </h3>
                              <h4 class="subsection">
        <a name="dotnetexec"></a>
        dotnetexec
      </h4>
                        <p>Runs a .NET executable.</p>
                                                          <h4 class="subsection">
        <a name="nunit"></a>
        nunit
      </h4>
                        <p>Runs NUnit tests.</p>
                                                          <h4 class="subsection">
        <a name="nant"></a>
        nant
      </h4>
                        <p>Invokes NAnt, either on an external file or a build file
        snippet contained inside your Ant build file.</p>
                                                          <h4 class="subsection">
        <a name="msbuild"></a>
        msbuild
      </h4>
                        <p>Invokes MSBuild, either on an external file or a build file
        snippet contained inside your Ant build file.</p>
                                                          <h4 class="subsection">
        <a name="wix"></a>
        wix
      </h4>
                        <p>Invokes the candle and light executables of the WiX toolkit
          in order to create MSI installers from within Ant.</p>
                                            <h3 class="section">
      <a name="Examples"></a>
      Examples
    </h3>
                              <h4 class="subsection">
        <a name="nant"></a>
        nant
      </h4>
                        <pre class="code">
&lt;project xmlns:dn=&quot;antlib:org.apache.ant.dotnet&quot;&gt;
  &lt;dn:nant&gt;
    &lt;build&gt;
      &lt;echo message=&quot;Hello world&quot;/&gt;
    &lt;/build&gt;
  &lt;/dn:nant&gt;
&lt;/project&gt;
</pre>
                                <p>runs NAnt on the embedded <code>&lt;echo&gt;</code>
        task, output looks like</p>
                                <pre class="code">
Buildfile: test.xml
[dn:nant] NAnt 0.85 (Build 0.85.1932.0; rc3; 16.04.2005)
[dn:nant] Copyright (C) 2001-2005 Gerry Shaw
[dn:nant] http://nant.sourceforge.net
[dn:nant] 
[dn:nant] Buildfile: file:///c:/DOKUME~1/STEFAN~1.BOD/LOKALE~1/Temp/build1058451555.xml
[dn:nant] Target framework: Microsoft .NET Framework 1.1
[dn:nant] 
[dn:nant]      [echo] Hello world
[dn:nant] 
[dn:nant] BUILD SUCCEEDED
[dn:nant] 
[dn:nant] Total time: 0.2 seconds.

BUILD SUCCESSFUL
Total time: 2 seconds</pre>
                                                          <h4 class="subsection">
        <a name="msbuild"></a>
        msbuild
      </h4>
                        <pre class="code">
&lt;project xmlns:dn=&quot;antlib:org.apache.ant.dotnet&quot;&gt;
  &lt;dn:msbuild&gt;
    &lt;build&gt;
      &lt;Message Text=&quot;Hello world&quot;
        xmlns=&quot;http://schemas.microsoft.com/developer/msbuild/2003&quot;/&gt;
    &lt;/build&gt;
  &lt;/dn:msbuild&gt;
&lt;/project&gt;</pre>
                                <p>runs MSBuild on the embedded <code>&lt;Message&gt;</code>
        task, output looks like</p>
                                <pre class="code">
Buildfile: test.xml
[dn:msbuild] Microsoft (R) Build Engine Version 2.0.50727.42
[dn:msbuild] [Microsoft .NET Framework, Version 2.0.50727.42]
[dn:msbuild] Copyright (C) Microsoft Corporation 2005. All rights reserved.

[dn:msbuild] Build started 15.12.2005 20:21:56.
[dn:msbuild] __________________________________________________
[dn:msbuild] Project &quot;c:\Dokumente und Einstellungen\stefan.bodewig\Lokale Einstellungen\Temp\build1543310185.xml&quot; (default targets):

[dn:msbuild] Target generated-by-ant:
[dn:msbuild]     Hello world

[dn:msbuild] Build succeeded.
[dn:msbuild]     0 Warning(s)
[dn:msbuild]     0 Error(s)

[dn:msbuild] Time Elapsed 00:00:00.10

BUILD SUCCESSFUL
Total time: 0 seconds
</pre>
                                    
    </div>
  </div>

        <p class="copyright">
        <script type="text/javascript" language="JavaScript"><!--
                document.write(" - "+"Last Published: " + document.lastModified);
              //  -->
        </script>
      </p>
    </body>
  </html>











