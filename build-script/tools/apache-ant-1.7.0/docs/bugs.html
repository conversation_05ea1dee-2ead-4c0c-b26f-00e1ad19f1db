

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
        <html lang="en">
    <!-- GENERATED FILE, DO NOT EDIT, EDIT THE XML FILE IN xdocs INSTEAD! -->
    <head>
      <META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
        <title>Apache Ant - Bug database</title>
        <link type="text/css" href="./page.css" rel="stylesheet">
          <meta name="author" content="<PERSON>">
  <meta name="email" content="">
      </head>

    <body>
      <p class="navpath">
        <script src="./breadcrumbs.js" language="JavaScript" type="text/javascript"></script>
      </p>

      <div class="logobar">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td align="left"><img border="0" alt="Apache Ant site" src="./images/group-logo.gif"></td>
            <td align="center" width="100%"><img alt="Apache Ant logo" border="0" src="./images/project-logo.gif"></td>
            <td align="right">
              <form target="_blank" onsubmit="q.value = query.value + ' site:ant.apache.org'" action="http://www.google.com/search" method="get">
                <table summary="search" border="0" cellspacing="0" cellpadding="0" bgcolor="#4C6C8F">
                  <tr>
                    <td colspan="3"><img height="10" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td nowrap="nowrap" class="searchcaption">
                      <input name="q" type="hidden">
                      <input size="15" id="query" type="text">
                      <img height="1" width="5" alt="" src="./images/spacer.gif">
                      <input name="Search" value="Search" type="submit">
                      <br>
                      the Apache Ant site
                    </td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-left.gif"></td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-right.gif"></td>
                  </tr>
                </table>
              </form>
            </td>
          </tr>
        </table>
      </div>

                  <div class="tab">
              <table summary="tab bar" border="0" cellpadding="0" cellspacing="0">
                <tr>
                                  <td width="8"><img alt="" height="5" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="selected tab" style="height: 1.5em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                        <td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-left.gif"></td><td valign="middle" bgcolor="#4C6C8F"><font color="#ffffff" size="2" face="Arial, Helvetica, Sans-serif"><b>Home</b></font></td><td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                                    <td width="5"><img alt="" height="8" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="non selected tab" style="height: 1.4em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                          <td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-left.gif"></td><td valign="middle" bgcolor="#B2C4E0"><a href="./projects/index.html"><font size="2" face="Arial, Helvetica, Sans-serif">Projects</font></a></td><td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                            </tr>
              </table>
            </div>

      <div class="bluebar"></div>
                    
  <div class="menucontainer">

    <div class="menu">
      <ul>
              <li class="menuheader">Apache Ant
          <ul>
                            <li>
                                    <a href="./index.html">Welcome</a>
                                </li>
                            <li>
                                    <a href="./license.html">License</a>
                                </li>
                            <li>
                                    <a href="./antnews.html">News</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Documentation
          <ul>
                            <li>
                                    <a href="./manual/index.html">Manual</a>
                                </li>
                            <li>
                                    <a href="./projects.html">Related Projects</a>
                                </li>
                            <li>
                                    <a href="./external.html">External Tools and Tasks</a>
                                </li>
                            <li>
                                    <a href="./resources.html">Resources</a>
                                </li>
                            <li>
                                    <a href="./faq.html">Frequently Asked Questions</a>
                                </li>
                            <li>
                                    <a href="http://wiki.apache.org/ant/FrontPage">Wiki</a>
                                </li>
                            <li>
                                    <a href="./problems.html">Having Problems?</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Download
          <ul>
                            <li>
                                    <a href="http://ant.apache.org/bindownload.cgi">Binary Distributions</a>
                                </li>
                            <li>
                                    <a href="http://ant.apache.org/srcdownload.cgi">Source Distributions</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Contributing
          <ul>
                            <li>
                                    <a href="./mail.html">Mailing Lists</a>
                                </li>
                            <li>
                                    <a href="./svn.html">Subversion Repositories</a>
                                </li>
                            <li>
                                    <a href="./nightlies.html">Nightly Builds</a>
                                </li>
                            <li>
                                    <a href="./bugs.html">Bug Database</a>
                                </li>
                            <li>
                                    <a href="http://www.apache.org/foundation/contributing.html">Donations</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Project Management
          <ul>
                            <li>
                                    <a href="./contributors.html">Contributors</a>
                                </li>
                            <li>
                                    <a href="./mission.html">Apache Ant Mission</a>
                                </li>
                            <li>
                                    <a href="./bylaws.html">Project Bylaws</a>
                                </li>
                            <li>
                                    <a href="./legal.html">Legal</a>
                                </li>
                      </ul>
        </li>
            </ul>
    </div>
    <img style="float: left" height="10" width="10" border="0" alt="" src="./images/menu-left.gif">
    <img style="float: right" height="10" width="10" border="0" alt="" src="./images/menu-right.gif">
  </div>
      <div class="lightbluebar">&nbsp;</div>
  <div class="main">
  <div class="content">
    <h1 class="title">Bug database</h1>
            <h3 class="section">
      <a name="Bug Database"></a>
      Bug Database
    </h3>
                        <p>
           This page gives you some bookmarks to use the Bugzilla <a href="http://issues.apache.org/bugzilla/">
            Apache Bug Database</a>.
         </p>
                                <p>
           This link <a href="http://issues.apache.org/">issues.apache.org</a> connects you
           to the complete list of Apache Bug Database systems.
         </p>
                                      <h4 class="subsection">
        <a name="Has It Been Reported?"></a>
        Has It Been Reported?
      </h4>
                        <p>
            If the current nightly build doesn't resolve your problem, it is
            possible that someone else has reported the issue. It is time to
            look at .  This system is easy to use, and it will
            let you search the <a href="http://issues.apache.org/bugzilla/buglist.cgi?bug_status=UNCONFIRMED&amp;bug_status=NEW&amp;bug_status=ASSIGNED&amp;bug_status=REOPENED&amp;email1=&amp;emailtype1=substring&amp;emailassigned_to1=1&amp;email2=&amp;emailtype2=substring&amp;emailreporter2=1&amp;bugidtype=include&amp;bug_id=&amp;changedin=&amp;votes=&amp;chfieldfrom=&amp;chfieldto=Now&amp;chfieldvalue=&amp;product=Ant&amp;short_desc=&amp;short_desc_type=substring&amp;long_desc=&amp;long_desc_type=substring&amp;bug_file_loc=&amp;bug_file_loc_type=substring&amp;keywords=&amp;keywords_type=anywords&amp;field0-0-0=noop&amp;type0-0-0=noop&amp;value0-0-0=&amp;order=bugs.bug_id">
            currently open</a> and resolved bugs to see if your problem has
            already been reported. If your problem has been reported, you can
            see whether any of the developers have commented, suggesting
            workarounds, or the reason for the bug, etc. Or you may have
            information to add (see about creating and modifying bug reports
            below), in which case, go right ahead and add the information.
            If you don't have any additional information, you may just want
            to vote for this bug, and perhaps
            add yourself to the <code>CC</code> list to follow the progress
            of this bug.
         </p>
                                <p><a href="http://issues.apache.org/bugzilla/buglist.cgi?query_format=advanced&amp;short_desc_type=allwordssubstr&amp;short_desc=&amp;product=Ant&amp;long_desc_type=substring&amp;long_desc=&amp;bug_file_loc_type=allwordssubstr&amp;bug_file_loc=&amp;keywords_type=allwords&amp;keywords=&amp;bug_status=NEW&amp;bug_status=ASSIGNED&amp;bug_status=REOPENED&amp;emailassigned_to1=1&amp;emailtype1=substring&amp;email1=&amp;emailassigned_to2=1&amp;emailreporter2=1&amp;emailcc2=1&amp;emailtype2=substring&amp;email2=&amp;bugidtype=include&amp;bug_id=&amp;votes=&amp;chfieldfrom=&amp;chfieldto=Now&amp;chfieldvalue=&amp;field0-0-0=noop&amp;type0-0-0=noop&amp;value0-0-0=">Open Ant bugs by order of priority</a>.</p>
                                <p><a href="http://issues.apache.org/bugzilla/buglist.cgi?query_format=advanced&amp;short_desc_type=allwordssubstr&amp;short_desc=&amp;product=Ant&amp;long_desc_type=substring&amp;long_desc=&amp;bug_file_loc_type=allwordssubstr&amp;bug_file_loc=&amp;keywords_type=allwords&amp;keywords=&amp;bug_status=NEW&amp;bug_status=ASSIGNED&amp;bug_status=REOPENED&amp;emailassigned_to1=1&amp;emailtype1=substring&amp;email1=&amp;emailassigned_to2=1&amp;emailreporter2=1&amp;emailcc2=1&amp;emailtype2=substring&amp;email2=&amp;bugidtype=include&amp;bug_id=&amp;votes=&amp;chfieldfrom=&amp;chfieldto=Now&amp;chfieldvalue=&amp;field0-0-0=noop&amp;type0-0-0=noop&amp;value0-0-0=&amp;order=bugs.votes,bugs.priority%2Cbugs.bug_severity&amp;query_based_on=">Open Ant bugs by number of votes</a>.</p>
                                                          <h4 class="subsection">
        <a name="Filing a Bug Report"></a>
        Filing a Bug Report
      </h4>
                        <p>
            Please read our document about <a href="problems.html">problems</a>
            before deciding that there is an unreported
            bug in Ant.
        </p>
                                <p>
            You have a few choices at this point. You can send
            an email to the <code>Employee</code> mailing list
            to see if
            others have encountered your issue and find out how they may
            have worked around it. If after some discussion, you feel it
            is time to create
            a bug report, this is a simple operation in the bug database.
            Please try to provide as much information as possible in order
            to assist the developers in resolving the bug. Please try to enter
            correct values for the various inputs when creating the bug, such
            as which version of Ant you are running, and on which platform,
            etc. Once the bug is created, you can also add attachments to
            the bug report.
         </p>
                                <p>
            What information should you include in your bug report? The
            easiest bugs to fix are those that are most easily reproducible,
            so it is really helpful if you can produce a small test case that
            exhibits the problem. In this case, you would attach the build file
            and any other files necessary to reproduce the problem, probably
            packed together in an archive. If you can't produce a test case,
            you should try to include a snippet from your build file and the
            relevant sections from the verbose or debug output from Ant. Try
            to include the header information where Ant states the version,
            the OS and VM information, etc. As debug output is likely to be
            very large, it's best to remove any output that is not
            relevant. Once the bug is entered into the bug database, you
            will be kept informed by email about progress on the bug. If
            you receive email asking for further information, please try to
            respond, as it will aid in the resolution of your bug.
         </p>
                                <p>
            To create the bug report hit this
            <a href="http://issues.apache.org/bugzilla/enter_bug.cgi?product=Ant">
            link</a>.
         </p>
                                                          <h4 class="subsection">
        <a name="Asking for an Enhancement"></a>
        Asking for an Enhancement
      </h4>
                        <p>
            Sometimes, you may find that Ant just doesn't do what you need it
            to. It isn't a bug, as such, since Ant is working the way it is
            supposed to work. Perhaps it is some additional functionality for
            a task that hasn't been thought of yet, or maybe a completely new
            task. For these situations, you will
            want to raise an <i>enhancement request</i>. Enhancement requests
            are managed using the same Apache Bug Database described above.
            These are just a different type of bug report. If you look in the
            bug database, you will see that one of the severity settings for
            a bug is "Enhancement". Just fill the bug report in,
            set the severity of the bug to "Enhancement", and
            state in the description how you would like to have Ant enhanced.
            Again, you should first check whether there are any existing
            enhancment requests that cover your needs. If so, just add your
            vote to these.
         </p>
                                <p>
          <a href="http://issues.apache.org/bugzilla/enter_bug.cgi?product=Ant&amp;bug_severity=enhancement">
          Create an enhancement report</a>

        </p>
                                                          <h4 class="subsection">
        <a name="Fixing the Bug"></a>
        Fixing the Bug
      </h4>
                        <p>
            If you aren't satisfied with just filing a bug report, you can
            try to find the cause of the problem and provide a fix yourself.
            The best way to do that is by working with the latest code from Subversion.
            Alternatively, you can work with the source code available from the
            <a href="http://ant.apache.org/srcdownload.cgi">
            source distributions</a>. If you
            are going to tackle the problem at this level, you may want to
            discuss some details first on the <code>dev</code>
            mailing list. Once you have a fix for the problem, you may submit
            the fix as a <i>patch</i> to either the
            <code>dev</code> mailing
            list, or enter the bug database as described above and attach the
            patch to the bug report. Using the bug database has the advantage
            of being able to track the progress of your patch.
         </p>
                                <p>
            If you have a patch to submit and are sending it to the
            <code>dev</code> mailing list,
            prefix "[PATCH]"
            to your message subject (this is also a good idea for
            a subject line in the bug database).
            Please include any relevant bug numbers.
            Patch files should be created with the <code>-u</code>
            option of the
            <code>diff</code> or <code>svn diff</code> command. For
            example:<br /><br />
            <font face="verdana" size="-1">
            diff -u Javac.java.orig Javac.java &gt; javac.diffs<br /><br />
            </font>
            or, if you have source from Subversion:<br /><br />
            <font face="verdana" size="-1">
            svn diff Javac.java &gt; javac.diffs<br /><br />
            </font>

           Note: You should give your patch files meaningful names.
           This makes it easier for developers who need to apply a number
           of different patch files.
        </p>
                                    
    </div>
  </div>

        <p class="copyright">
        <script type="text/javascript" language="JavaScript"><!--
                document.write(" - "+"Last Published: " + document.lastModified);
              //  -->
        </script>
      </p>
    </body>
  </html>











