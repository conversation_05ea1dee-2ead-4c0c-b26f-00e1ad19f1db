

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
        <html lang="en">
    <!-- GENERATED FILE, DO NOT EDIT, EDIT THE XML FILE IN xdocs INSTEAD! -->
    <head>
      <META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
        <title>Apache Ant - External Tools and Tasks</title>
        <link type="text/css" href="./page.css" rel="stylesheet">
          <meta name="author" content="<PERSON>">
  <meta name="email" content="<EMAIL>">
      </head>

    <body>
      <p class="navpath">
        <script src="./breadcrumbs.js" language="JavaScript" type="text/javascript"></script>
      </p>

      <div class="logobar">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td align="left"><img border="0" alt="Apache Ant site" src="./images/group-logo.gif"></td>
            <td align="center" width="100%"><img alt="Apache Ant logo" border="0" src="./images/project-logo.gif"></td>
            <td align="right">
              <form target="_blank" onsubmit="q.value = query.value + ' site:ant.apache.org'" action="http://www.google.com/search" method="get">
                <table summary="search" border="0" cellspacing="0" cellpadding="0" bgcolor="#4C6C8F">
                  <tr>
                    <td colspan="3"><img height="10" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td nowrap="nowrap" class="searchcaption">
                      <input name="q" type="hidden">
                      <input size="15" id="query" type="text">
                      <img height="1" width="5" alt="" src="./images/spacer.gif">
                      <input name="Search" value="Search" type="submit">
                      <br>
                      the Apache Ant site
                    </td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-left.gif"></td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-right.gif"></td>
                  </tr>
                </table>
              </form>
            </td>
          </tr>
        </table>
      </div>

                  <div class="tab">
              <table summary="tab bar" border="0" cellpadding="0" cellspacing="0">
                <tr>
                                  <td width="8"><img alt="" height="5" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="selected tab" style="height: 1.5em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                        <td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-left.gif"></td><td valign="middle" bgcolor="#4C6C8F"><font color="#ffffff" size="2" face="Arial, Helvetica, Sans-serif"><b>Home</b></font></td><td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                                    <td width="5"><img alt="" height="8" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="non selected tab" style="height: 1.4em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                          <td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-left.gif"></td><td valign="middle" bgcolor="#B2C4E0"><a href="./projects/index.html"><font size="2" face="Arial, Helvetica, Sans-serif">Projects</font></a></td><td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                            </tr>
              </table>
            </div>

      <div class="bluebar"></div>
                    
  <div class="menucontainer">

    <div class="menu">
      <ul>
              <li class="menuheader">Apache Ant
          <ul>
                            <li>
                                    <a href="./index.html">Welcome</a>
                                </li>
                            <li>
                                    <a href="./license.html">License</a>
                                </li>
                            <li>
                                    <a href="./antnews.html">News</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Documentation
          <ul>
                            <li>
                                    <a href="./manual/index.html">Manual</a>
                                </li>
                            <li>
                                    <a href="./projects.html">Related Projects</a>
                                </li>
                            <li>
                              <span class="sel">External Tools and Tasks</span>
                              </li>
                            <li>
                                    <a href="./resources.html">Resources</a>
                                </li>
                            <li>
                                    <a href="./faq.html">Frequently Asked Questions</a>
                                </li>
                            <li>
                                    <a href="http://wiki.apache.org/ant/FrontPage">Wiki</a>
                                </li>
                            <li>
                                    <a href="./problems.html">Having Problems?</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Download
          <ul>
                            <li>
                                    <a href="http://ant.apache.org/bindownload.cgi">Binary Distributions</a>
                                </li>
                            <li>
                                    <a href="http://ant.apache.org/srcdownload.cgi">Source Distributions</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Contributing
          <ul>
                            <li>
                                    <a href="./mail.html">Mailing Lists</a>
                                </li>
                            <li>
                                    <a href="./svn.html">Subversion Repositories</a>
                                </li>
                            <li>
                                    <a href="./nightlies.html">Nightly Builds</a>
                                </li>
                            <li>
                                    <a href="./bugs.html">Bug Database</a>
                                </li>
                            <li>
                                    <a href="http://www.apache.org/foundation/contributing.html">Donations</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Project Management
          <ul>
                            <li>
                                    <a href="./contributors.html">Contributors</a>
                                </li>
                            <li>
                                    <a href="./mission.html">Apache Ant Mission</a>
                                </li>
                            <li>
                                    <a href="./bylaws.html">Project Bylaws</a>
                                </li>
                            <li>
                                    <a href="./legal.html">Legal</a>
                                </li>
                      </ul>
        </li>
            </ul>
    </div>
    <img style="float: left" height="10" width="10" border="0" alt="" src="./images/menu-left.gif">
    <img style="float: right" height="10" width="10" border="0" alt="" src="./images/menu-right.gif">
  </div>
      <div class="lightbluebar">&nbsp;</div>
  <div class="main">
  <div class="content">
    <h1 class="title">External Tools and Tasks</h1>
            <h3 class="section">
      <a name="External Tools and Tasks"></a>
      External Tools and Tasks
    </h3>
                        <p>This page lists external resources for Apache Ant: <a href="#Tasks">Tasks</a>, <a href="#Compiler%20Implementations">Compiler Implementations</a>,
      <a href="#IDE%20and%20Editor%20Integration">IDE integration
      tools</a>, <a href="#Source%20Control%20ystems">Source Control
      Systems</a>, loggers, you name it. If you've written
      something that should be included, please post all relevant
      information to one of the mailing lists.  For details, see the
      <a href="faq.html#adding-external-tasks">FAQ</a>.</p>
                                <p>Nothing listed here is directly supported by the Ant
      developers (therefore '<i>external</i> tools and tasks'),
      if you encounter any problems with them, please use
      the contact information.</p>
                        <h3 class="section">
      <a name="Tasks"></a>
      Tasks
    </h3>
                              <h4 class="subsection">
        <a name="AJC"></a>
        AJC
      </h4>
                        <p><a href="http://www.eclipse.org/aspectj/">AspectJ</a> is an
          aspect-oriented extension to Java.  This task compiles a
          source tree using the AspectJ compiler -- AJC.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.3
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.eclipse.org/aspectj/">http://www.eclipse.org/aspectj/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.eclipse.org/aspectj/">project mailing lists</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Anakia"></a>
        Anakia
      </h4>
                        <p>Actually, Anakia is more than just an Ant task, it is a an
        XML transformation tool based on JDOM, Velocity and Ant.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.2 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://jakarta.apache.org/velocity/anakia.html">http://jakarta.apache.org/velocity/anakia.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://jakarta.apache.org/site/mail2.html">Velocity mailing lists</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Andariel"></a>
        Andariel
      </h4>
                        <p>Andariel is a set of tasks designed to help the generation of HTML
        (and other markup languages) pages from Ant. Includes a XPath processor,
        an image information retriever, and others.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5.4 and newer
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://andariel.uworks.net/">http://andariel.uworks.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          josep DOT rio AT uworks DOT net
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License 1.1
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Ant2Svg"></a>
        Ant2Svg
      </h4>
                        <p>Ant2Svg creates a graphical representation of an Ant build file.
        The graphical representation is in the form of a Scalable Vector
        Graphics (SVG) file that can be displayed in a web browser. This
        simplified SVG depiction helps the developer understand build file
        structure and identify extraneous or missing dependencies.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6.1 and higher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.spiritedsw.com/ant2svg/">http://www.spiritedsw.com/ant2svg/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          loney &lt;at&gt; spiritedsw &lt;dot&gt; com
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          The Apache Software License 2.0
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="ant4eclipse"></a>
        ant4eclipse
      </h4>
                        <p>ant4eclipse provides a set of Ant tasks to make several
                configurations from the Eclipse IDE available in Ant
                buildscripts. The tasks are aimed to avoid redundancy between
                Eclipse and Ant configurations in order to build small but
                powerful build systems for the continuous integration
                process</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6.1 and higher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://ant4eclipse.sf.net">http://ant4eclipse.sf.net</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sourceforge.net/mail/?group_id=137377">
              ant4eclipse Employee mailing list</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Sun Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Antcount"></a>
        Antcount
      </h4>
                        <p>Antcount is a set of filters that can be used to gather statistics
           from files or resources. It is mainly used for log files analysis.
           It allows to:<ul>
             <li>count inputs (lines, strings)</li>
             <li>count occurrences of each input</li>
             <li>calculate average, max and min values of floats in input</li>
           </ul>
           Antcount also includes some useful filters to:<ul>
             <li>stop filtering: read everything but write nothing</li>
             <li>echo input to the console or to a file. This allows users to create
                 several files at once</li>
             <li>split the stream in two for parallel processing</li>
           </ul>
        </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6.2 and higher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://antcount.sourceforge.net/">http://antcount.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://antcount.sourceforge.net/contacts.html">Patrick Martin</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          LGPL
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="AntDoc"></a>
        AntDoc
      </h4>
                        <p>AntDoc is a tool that generates HTML documentation from Ant
        buildfiles; the generated HTML is inspired from what javadoc
        yields.  AntDocGUI offers a simple Ant target launcher named
        AntDoc GUI. Ant targets may be launched from the generated
        AntDoc HTML pages. Integration to various IDEs is in
        progress.</p>
                                <p>AntDoc can be run via an Ant task, AntDoc GUI can be run
        via an Ant task, or via a JVM launch.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4.1 and higher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://antdoc.free.fr/">http://antdoc.free.fr/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Edouard Mercier
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          The Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="AntDoclet"></a>
        AntDoclet
      </h4>
                        <p>AntDoclet is a tool to automatically generate documentation out of
        your Ant Tasks' source code.</p>
                                <p>It is implemented as a Javadoc doclet, and generates reference
        documentation and other deliverables from the source code of your
        custom Ant Tasks/Types.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6+ (not tested on earlier versions)
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://antdoclet.neuroning.com/">http://antdoclet.neuroning.com/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://neuroning.com/">Fernando Dobladez</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GPL
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Anteater"></a>
        Anteater
      </h4>
                        <p>Anteater is a set of Ant tasks for the functional testing of websites
          and web services (functional testing being; hit a URL and ensure the
          response meets certain criteria). Can test HTTP params, response
          codes, XPath, regexp and Relax NG expressions. Includes HTML reporting
          (based on junitreport) and a hierarchical grouping system for quickly
          configuring large test scripts.
        </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://aft.sourceforge.net/">http://aft.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://lists.sourceforge.net/lists/listinfo/aft-devel">developer
                mailing list</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="AntForm"></a>
        AntForm
      </h4>
                        <p>Provides a java/swing form-based input scheme for
        configuring ant properties and launching ant targets.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6.2.
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://antforms.sourceforge.net/">http://antforms.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">René Ghosh</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Antmerge"></a>
        Antmerge
      </h4>
                        <p>Provides simple inheritance between ant files</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Tested with 1.5. Should work with all versions.
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.russet.org.uk/antmerge.html">http://www.russet.org.uk/antmerge.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Phillip Lord</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU Lesser General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="AntPrettyBuild"></a>
        AntPrettyBuild
      </h4>
                        <p>
          Ant Pretty Build is a tool to easily show and run Ant buildfiles directly from
        within a browser window. It consists of a single XSL file that will generate,
        on the fly, in the browser, from the .xml buildfile, a pretty interface showing
        project name, description, properties and targets, etc. sorted or unsorted,
          allowing to load/modify/add properties, run the whole project, or run selected
        set of targets in a specific order, with the ability to modify logger/logfile,
        mode and add more libs or command line arguments.
        </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          All Ant versions
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://antprettybuild.sourceforge.net/">Ant Pretty Build Homepage</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://antprettybuild.sourceforge.net">Charbel BITAR</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache License V2.0
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="AntSpaces"></a>
        AntSpaces
      </h4>
                        <p>AntSpaces provides Ant integration with JavaSpaces. This
        allows you to coordinate Ant tasks via JavaSpaces, pull out
        work units from a JavaSpace for distributed Ant tasks to work
        on, and so forth.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://www.oopsconsultancy.com/software/antspaces/">http://www.oopsconsultancy.com/software/antspaces/</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          antspaces at oopsconsultancy.com
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="AntTimerTask"></a>
        AntTimerTask
      </h4>
                        <p><a href="http://www.jeckle.de/freeStuff/AntTimerTask/index.html">Timer</a>
        is task for measuring the time elapsed to complete other
        tasks</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://www.jeckle.de/freeStuff/AntTimerTask/index.html">http://www.jeckle.de/freeStuff/AntTimerTask/index.html</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Lesser GNU Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Ant Web Start Task"></a>
        Ant Web Start Task
      </h4>
                        <p>Ant Web Start Task is an Ant task allowing developers to
        package a desktop application as a WAR (Web Application
        Archive) to be distributed over the net via Java Web Start</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 and above
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://ant-jnlp-war.sourceforge.net/">http://ant-jnlp-war.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License 2.0
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="AntXtras"></a>
        AntXtras
      </h4>
                        <p>A collection of powerful Ant extensions components
        organized into five categories: fixture-control,
        execution-rules, flow-control, feedback, and helpers.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://antxtras.sourceforge.net/">AntXtras Home</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://antxtras.sourceforge.net">SSMC</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU Lesser General Public License (LGPL 2.1)
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Build Number"></a>
        Build Number
      </h4>
                        <p> Build Number is a tool to track software artifacts such as files and automatically assign
        proper version/build numbers to them. It ensures that two different artifacts will have different
        version/build numbers, but identical artifacts/builds will be assigned the same number. It doesn't
        take version management away from you and doesn't replace your build process, but rather plugs
        into the process and introduces version/build number governance by defining who is in charge of
        which part of version number. With Build Number you are still in charge of the head of the version
        number. E.g. you may decide to have 4 numbers in your version (major, minor, interface, implementation)
        and you want to manage the two first numbers (major and minor). Build Number will take care of the
        tedium of managing the last two numbers (interface and implementation). </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Tested with Ant 1.5.4 and 1.6.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.hammurapi.biz/products/buildnumber">http://www.hammurapi.biz/products/buildnumber</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.hammurapi.biz/hammurapi-biz/ef/xmenu/contact.html">Project Contact Page</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Commercial
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Checkstyle"></a>
        Checkstyle
      </h4>
                        <p>Checkstyle is a development tool to help programmers write
        Java code that adheres to a coding standard. Its purpose is to
        automate the process of checking Java code, and to spare
        humans of this boring (but important) task.</p>
                                <p>Checkstyle can be run via an Ant task or a command line
        utility.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4.1 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://checkstyle.sourceforge.net/">http://checkstyle.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Oliver Burn</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Starting with release 2.0 the license is the GNU
            Lesser General Public License.  Prior releases were under
            the GNU General Public License.
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="ChownTask"></a>
        ChownTask
      </h4>
                        <p>ChownTask is an Ant task to change ownership of files on
        Unix.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.3 and up
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://sourceforge.net/projects/chowntask/">http://sourceforge.net/projects/chowntask/</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Wilfred Springer</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="CleanImports"></a>
        CleanImports
      </h4>
                        <p>Removes unneeded imports. Formats your import
        sections. Flags ambiguous imports.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.3 and up
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.cleanimports.tombrus.nl">http://www.cleanimports.tombrus.nl</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Tom Brus</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Clover"></a>
        Clover
      </h4>
                        <p>Clover is an Ant-based Code Coverage tool. It can be used
        seamlessly with Ant-based projects. It provides method,
        statement, and branch coverage analysis, and has rich
        reporting in XML, HTML or via a Swing GUI.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4.1 or greater
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.cenqua.com/clover/">http://www.cenqua.com/clover/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Commercial, free licenses available for open source
            projects.
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="CMSDeploy"></a>
        CMSDeploy
      </h4>
                        <p><a href="http://cmsdeploy.sourceforge.net">CMSDeploy</a> is
        an Apache Ant Task to submit files and templates to Vignette
        CMS.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5.1 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://cmsdeploy.sourceforge.net">http://cmsdeploy.sourceforge.net</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU Lesser General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Cocoon Task"></a>
        Cocoon Task
      </h4>
                        <p>This task allows the generation of static web pages and
        sites using Apache Cocoon in off-line mode.</p>
                                <p>It allows the configuration information for Cocoon to be
        included within the Ant build file, and is thus
        able to take advantage of Ant properties.</p>
                                <p>The task shares its code with the Cocoon Command Line, which
        means that this task will instantly take
        advantage of any new functionality added there.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5.3 and above
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://cocoon.apache.org/2.1/">http://cocoon.apache.org/2.1/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Documentation:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://cocoon.apache.org/2.1/userdocs/offiline/ant.html">http://cocoon.apache.org/2.1/userdocs/offline/ant.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:users.at.cocoon.apache.org">users at cocoon.apache.org</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Configure"></a>
        Configure
      </h4>
                        <p>Recursive build support (call ant on every package level,
        and only build files in that package or in that package and
        everything below) with seperation of source and output.</p>
                                <p>The task generates build files in any subdirectory (except
        for CVS-directories) for you. Only place one build.xml file in
        the top and call target 'setup' or
        'rescan'.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.2 and 1.3
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.dsdelft.nl/~lemval/ant/">http://www.dsdelft.nl/~lemval/ant/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">M.J.P. van Leeuwen</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          License derived from Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="CVSGrab"></a>
        CVSGrab
      </h4>
                        <p>A little CVS client that can be useful when people are
        behind corporate firewall that blocks any cvs
        communications. It uses the ViewCVS web interface to access
        the CVS repository via standard http, and downloads all the
        files present in it.</p>
                                <p>It works from the command line or as an Ant task.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.3 or higher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://cvsgrab.sourceforge.net/">http://cvsgrab.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">CVSGrab
            Employee mailing list</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          LGPL
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Dependencies"></a>
        Dependencies
      </h4>
                        <p>The dependencies task manages a set of external dependencies which
            may be downloaded from a remote repository,
             such as ibiblio.org. Uses a local cache to avoid repeated
            downloads.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Tested with 1.5.1, should work with 1.4+.
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.httpunit.org/doc/dependencies.html">http://www.httpunit.org/doc/dependencies.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Russell Gold</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          MIT License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Dependency Finder"></a>
        Dependency Finder
      </h4>
                        <p>Dependency Finder extracts dependencies and OO metrics from
        Java class files produced by most Java compilers. It can compute
        API differences between versions;  no sources needed. It includes
        Ant tasks, web, Swing, and command-line interfaces, with XSL
        stylesheets for formatting output.</p>
                                <p>You can use it to extract dependencies between packages, classes,
        or even methods, or any combination thereof.  You can use Perl
        regular expressions to filter the information and pinpoint only
        what you need.  There is even a Web Application version (WAR file)
        so a whole group of developers can share a common view.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Tested with 1.5.3, should work with 1.4+.
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://depfind.sourceforge.net/">http://depfind.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Jean Tessier</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          BSD-like License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Doxygen task"></a>
        Doxygen task
      </h4>
                        <p>There are two Ant tasks for running the Doxygen
        documentation system.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.1 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.bgw.org/projects/java/ant/">http://www.bgw.org/projects/java/ant/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Kyle R. Burton</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software Foundation License
      </td>
      </tr>
          </table>
                                <p>and</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://ant-doxygen.sourceforge.net/">http://ant-doxygen.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Karthik A Kumar</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software Foundation License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="DTDDoc"></a>
        DTDDoc
      </h4>
                        <p>DTDDoc is here to help you to document your DTD's efficiently. It is a
        straightforward extension of the javadoc concept to the DTD file format.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://dtddoc.sourceforge.net/">http://dtddoc.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sourceforge.net/mail/?group_id=53704">Project Mailing List</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          X11 (Open Source)
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="EMMA"></a>
        EMMA
      </h4>
                        <p>EMMA is an open-source toolkit for measuring and reporting
        Java code coverage. EMMA distinguishes itself from other tools
        by going after a unique feature combination: support large-scale
        enterprise software development while keeping individual developers
        work fast and iterative at the same time.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://emma.sourceforge.net/">http://emma.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Mailinglist</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Common Public License 1.0
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="FMPP"></a>
        FMPP
      </h4>
                        <p>FMPP is a general-purpose text file preprocessor tool that
        uses FreeMarker templates. It is particularly designed for
        HTML preprocessor, for the generation of complete (static)
        homepages: directory structure that contains HTML-s, image
        files, etc. But of course it can be used to generate source
        code or whatever text files. FMPP is extendable with Java
        classes to pull data from any data sources (XML file,
        database, etc.) and embed the data into the generated
        files.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5.1
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://fmpp.sourceforge.net/">http://fmpp.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          BSD License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="GenJar"></a>
        GenJar
      </h4>
                        <p>Builds a JAR file based on class dependencies rather than simply the contents of a directory</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4 alpha (built after 2001/08/04) and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://genjar.sourceforge.net/">http://genjar.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Jesse Stockall</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software Foundation License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Grand"></a>
        Grand
      </h4>
                        <p>Grand is a set of tools to create a visual representation of Ant target
          dependencies. It works by taking an Ant build file and creating a "dot" file. It
          differs from the existing tools by relying on the Ant API rather than XML parsing to
          get the dependencies. It includes many advanced features such as filtering or
          rendering depending on the target's nature. Also features a SWT based GUI.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.ggtools.net/grand/">http://www.ggtools.net/grand/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Christophe Labouisse
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          BSD License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Greebo"></a>
        Greebo
      </h4>
                        <p>Greebo is an Ant-task for downloading dependency files
        (currently only jars) from a network to a specified directory,
        much like Maven. It supports multiple local and remote
        repositories with either flat or maven-like structures. It can
        read the dependency list from a Maven project file, a
        maven-like dependency file, or directly from the build.xml
        file.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://greebo.sourceforge.net/">http://greebo.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sourceforge.net/mail/?group_id=73733">project mailing lists</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="GroboUtils CodeCoverage"></a>
        GroboUtils CodeCoverage
      </h4>
                        <p>The CodeCoverage sub-project of GroboUtils provides a 100%
        pure Java code coverage tool.  It uses pre-execution class file
        recompilation, and generates XML files containing the coverage
        statistics.  It does not require any advanced VM setup to generate
        coverage numbers.
        </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5.1
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://groboutils.sourceforge.net/codecoverage/">http://groboutils.sourceforge.net/codecoverage/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sourceforge.net/mail/?group_id=22594">project mailing lists</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          MIT License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Hammurapi"></a>
        Hammurapi
      </h4>
                        <p>Java code review tool. Performs automated code
        review. Contains 111 inspectors which check different aspects
        of code quality including coding standards, EJB, threading,
        ...</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Tested with Ant 1.5.x and 1.6.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.hammurapi.biz/products/hammurapi">http://www.hammurapi.biz/products/hammurapi</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.hammurapi.biz/hammurapi-biz/ef/xmenu/contact.html">Project Contact Page</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License (GPL)
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="HelpStudioAnt"></a>
        HelpStudioAnt
      </h4>
                        <p>This task allows for HelpStudio projects to be created via Ant.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6.0 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://helpstudioant.sourceforge.net">http://helpstudioant.sourceforge.net</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="IDLDepend"></a>
        IDLDepend
      </h4>
                        <p>idldepend is a task that (re)generates Java sources to be
        created from CORBA/IDL files.</p>
                                <p>It parses the IDL file and determines the Java files that
        must be generated, taking in account the modifications that
        can happen due to command line parameters.  If any of the Java
        files are missing or older than the source IDL specification,
        it launches the specified compiler.  The compilers of Orbacus,
        Jacorb, OpenORB, Orbix2k and Sun'JDK distributions are
        supported.</p>
                                <p>To speed up the process and avoid unnecesary re-parsing, it
        keeps the dependencies in intermediate files.  This task does
        not launch the javac compiler as well, that is, its output are
        Java files and not the final bytecode.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4 or later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://grasia.fdi.ucm.es/~luismi/idldepend/">http://grasia.fdi.ucm.es/~luismi/idldepend/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          free source, no license restrictions
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Image"></a>
        Image
      </h4>
                        <p>Image task generates and transforms images. It exposes the
        imaging capability available in Java2D, Java Advanced Imaging,
        ImageIO, etc., as set of nested elements.</p>
                                <p>Image transformations such as "resize"
        (scale),"overlay" (one image on another),
        "border" (add a border), "text" (text on
        image), "crop" (a sub-image of a bigger image),
        "rotate", "grayscale" (change a color
        image to shades of gray).<br />
        Now it supports transparency (making images translucent), a
        bestfit option for Resize, simple support for images within
        a security-constraint, a preliminary support (if pjatools.jar
        is available) for saving files as GIF and some other fixes.</p>
                                <p>IMPORTANT: You will need the PMIW (Poor Man's Imaging Wrapper) jar
        for all the operations and the pjatools jar for GIF encoding/ saving
        support.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.mullassery.com/software/ANT/">http://www.mullassery.com/software/ANT/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          pmiw jar
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.mullassery.com/software/PMIW/">http://www.mullassery.com/software/PMIW/</a>
                (Poor Man's Imaging Wrapper)
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          pjatools jar
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.eteks.com/pja/en/">http://www.eteks.com/pja/en/</a>
                (pjatools for GIF encoding support)
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.mullassery.com">Abey Mullassery</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Importscrubber"></a>
        Importscrubber
      </h4>
                        <p>Removes unnecessary import statements from a Java source code file.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.3
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://importscrubber.sourceforge.net/">http://importscrubber.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Tom Copeland</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          LGPL
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="ImTask"></a>
        ImTask
      </h4>
                        <p>ImTask is a task to allow one to send an Instant
        Message. Currently supports yahoo!, AIM, and Jabber</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 or higher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://imtask.sourceforge.net/">http://imtask.sourceforge.net</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Jon Madison</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU LGPL
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Incanto"></a>
        Incanto
      </h4>
                        <p>Ant tasks to provide support for Oracle database tools
        (such as SQL*Plus, Import, Export)</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://incanto.sourceforge.net/">http://incanto.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Alexander Karnstedt</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache License, Version 2.0
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="IsDirValidator"></a>
        IsDirValidator
      </h4>
                        <p>Checks whether a given directory structure conforms to
        certain rules that are defined via nested elements of the
        task.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://isvalidator.sourceforge.net/en/isDirValidator.htm">http://isvalidator.sourceforge.net/en/isDirValidator.htm</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Iñigo Serrano</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Ivy"></a>
        Ivy
      </h4>
                        <p>Ivy is a simple yet powerful dependency manager featuring
        continuous integration, dependencies of dependencies
        management, multiple repositories including ibiblio and high
        performance (use of a local cache).</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          1.5.1 or superior
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://ivy.jayasoft.org/">http://ivy.jayasoft.org/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          contact at jayasoft dot org
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          BSD License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="J2ME Ant Tasks"></a>
        J2ME Ant Tasks
      </h4>
                        <p>There are different sets of tasks to help build <a href="http://java.sun.com/j2me/">Java 2 Platform, Micro
        Edition</a> (J2ME) applications.</p>
                                      <h5 class="subsection">
        <a name="Antenna"></a>
        *** Antenna ***
      </h5>
                        <p>Antenna provides a set of Ant tasks suitable for developing
          wireless Java applications targeted at the Mobile Information
          Device Profile (MIDP). With Antenna, you can compile,
          preverify, package, obfuscate, and run your MIDP applications
          (aka MIDlets), manipulate Java Application Descriptor (JAD)
          files, as well as convert JAR files to PRC files designed to
          run on MIDP for Palm OS. Deployment is supported via a
          deployment task and a corresponding HTTP servlet for
          Over-the-Air (OTA) provisioning. A small preprocessor allows
          to generate different variants of a MIDlet from a single
          source.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4.1 or later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://antenna.sourceforge.net/">http://antenna.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Jörg Pleumann</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU Lesser General Public License
      </td>
      </tr>
          </table>
                                                    <h5 class="subsection">
        <a name="Antic"></a>
        *** Antic ***
      </h5>
                        <p>Antic is a freely available task for packaging J2ME
          applications. It produces both the Jar and Jad files in a
          single step. This allows *all* entries to be correclty set in
          the jad file, including the size of the jar file that is
          produced. This task has been used and tested extensively with
          Sun's Wireless Toolkit and also the Nokia SDK and
          emulators.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.smartkey.co.uk/tools/antic/antic.html">http://www.smartkey.co.uk/tools/antic/antic.html</a>
              
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">smartkey.co.uk</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License
      </td>
      </tr>
          </table>
                                                    <h5 class="subsection">
        <a name="Dave's J2ME Tasks"></a>
        *** Dave's J2ME Tasks ***
      </h5>
                        <p>This set supports CLDC and the K Virtual Machine (KVM):</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.3
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.dribin.org/dave/j2me_ant/">http://www.dribin.org/dave/j2me_ant/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Dave Dribin</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License
      </td>
      </tr>
          </table>
                                                    <h5 class="subsection">
        <a name="J2ME Polish"></a>
        *** J2ME Polish ***
      </h5>
                        <p>J2ME Polish is an Ant-based tool for the creation of
          MIDP applications. It covers the whole circle of preprocessing, compiling,
          obfuscation, preverifying, packaging and JAD-creation. J2ME Polish is
          ideal for creating device optimized applications with its powerful
          preprocessing capabilities and the integrated device database.
          With J2ME Polish no hardcoded values are needed and the portability of an
          application is not sacrificed, even though highly optimized applications are
          created from a single source.
          <br />
          It contains a logging framework and an optional MIDP-compatible GUI
          which can be designed using the web-standard CSS. With the J2ME Polish GUI
          you can even use MIDP/2.0 features on MIDP/1.0 phones.
          </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4.1 or later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.j2mepolish.org/">http://www.j2mepolish.org/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Enough Software</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License; commercial licenses available
      </td>
      </tr>
          </table>
                                                    <h5 class="subsection">
        <a name="Stampysoft's J2ME Tasks"></a>
        *** Stampysoft's J2ME Tasks ***
      </h5>
                        <p>And this set works with the J2ME Wireless Toolkit and MIDP
          for PalmOS:</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.3
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.stampysoft.com/ant/">http://www.stampysoft.com/ant/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Josh Eckels</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          MIT License
      </td>
      </tr>
          </table>
                                                                        <h4 class="subsection">
        <a name="Jacson"></a>
        Jacson
      </h4>
                        <p>Jacson is a configurable and plugable tool (much like Ant
        itself) to create filters for text (line based) files without
        programming.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Jacson has been used and tested with 1.5.1, should
            work with 1.4+
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://jacson.sourceforge.net/">http://jacson.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU Library or Lesser General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Jalopy"></a>
        Jalopy
      </h4>
                        <p>An Ant Plug-in for the Java Source Code Formatter
        Jalopy.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4 (or higher)
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://jalopy.sourceforge.net/">http://jalopy.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://jalopy.sf.net/contact.html">http://jalopy.sf.net/contact.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Starting with release 1.0 Beta 6 the license is the
            BSD License.  Prior releases were under the GNU General
            Public License.
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="JarBundler"></a>
        JarBundler
      </h4>
                        <p>JarBundler is a task that generates Mac OS X native Java
        Application Bundles.  It is fully configurable and can be used
        to generate Mac OS X application bundles from any supported
        Java platform, making it ideal for targeting multiple
        platforms with one build.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.loomcom.com/jarbundler/">http://www.loomcom.com/jarbundler/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Seth Morabito</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="JarPatch"></a>
        JarPatch
      </h4>
                        <p>JarPatch is a task that generates a zip file resulting of
        the diff between the content of 2 jar files.</p>
                                <p>The resulting diff file can be use as a patch for a
        previous installation (just ensure that the generated
        patch.zip file is located on the CLASSPATH before the patched
        oldJar jar file)</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://perso.club-internet.fr/sjobic/ant/">http://perso.club-internet.fr/sjobic/ant/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Norbert Barbosa</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          LGPL License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Java+ Precompile Task"></a>
        Java+ Precompile Task
      </h4>
                        <p>Java+ is an open source Java preprocessor that adds these
        features to any Java compiler:</p>
                                <ul>
          <li>Multi-line strings with executable inclusions like Perl
          and Ruby. It eliminates the need for JSP or ASP and their
          need for Java compilers on deployment servers (a security
          concern) while adding no overhead in either space or
          time. </li>

          <li>Optionally supports localization by segregating Java+
          strings into ResourceBundle files with invarient keys based
          on the hash code of the strings's value. This is handled
          automatically and transparently; no intervention is
          required.</li>

          <li>Fast. Negligible impact on build times. By default,
          skips inputs whose outputs are up to date to avoid
          triggering recompilations.</li>

          <li>Pure Java code, portable to any platform, with
          graphical, shell and ant interfaces.</li>

          <li>Simple, general, recursive, digraph-driven string
          syntax. Digraph characters are Employee-selectable.</li>
        </ul>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5.1
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://virtualschool.edu/java+/">http://virtualschool.edu/java+/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Brad Cox</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          BSD-like License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Java2Html"></a>
        Java2Html
      </h4>
                        <p>There are two different tools both named Java2HTML that
        process Java source code and generate syntax highlighted
        documentation from it.  Both include Ant tasks to run
        them.</p>
                                <p>Java2Html library for converting java source files
        or snipplets to syntax highlighted html, rtf, tex and
        others.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.java2html.de/">http://www.java2html.de/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License
      </td>
      </tr>
          </table>
                                <p>Java2HTML is a simple-to-use tool which converts a bunch of
        Java Source Code into a colourized and browsable HTML
        representation.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          1.5.1 onwards
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.java2html.com/java2html_ant_task.html">http://www.java2html.com/java2html_ant_task.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          FreeWare
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Javamake"></a>
        Javamake
      </h4>
                        <p>A task to compile Java sources and manage class file
        dependencies. Functionality is equivalent to that of standard
        Javac and Depend tasks combined, with improved dependency
        checking.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4.1
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.experimentalstuff.com/Technologies/JavaMake/index.html">http://www.experimentalstuff.com/Technologies/JavaMake/index.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Mikhail Dmitriev</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          BSD-like License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="javarec"></a>
        javarec
      </h4>
                        <p>Ant tasks that generate record classes for VisualAge for
        Java from Cobol copy books.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://glezen.org/javarec/">http://glezen.org/javarec/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Paul Glezen</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="JCSC"></a>
        JCSC
      </h4>
                        <p>JCSC is a Java Coding Standard Checker which also features
        the generation of some code metrics. It is a command line tool
        with an Ant task to scan whole package trees. The result can
        viewed in an JavaDoc style web page.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant &gt;= 1.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://jcsc.sourceforge.net/">http://jcsc.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Ralph Jocham</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Jdiff"></a>
        Jdiff
      </h4>
                        <p>A task that generates an HTML report of all the packages, classes,
        constructors, methods, and fields which have been removed, added or
        changed in any way, including their documentation, when two APIs are
        compared. </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            <a href="http://javadiff.sourceforge.net/">
            http://javadiff.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Task Documentation:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            <a href="http://cvs.sourceforge.net/viewcvs.py/*checkout*/javadiff/jdiff/jdiff.html?rev=HEAD&amp;content-type=text/html#JDiffAntTask">
            (in CVS)</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          LGPL
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="JFlex"></a>
        JFlex
      </h4>
                        <p>JFlex is a lexical analyzer generator (also known as
        scanner generator) for Java, written in Java.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://jflex.de/">http://jflex.de/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.jflex.de/mailing.html">jflex-users mailing list</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License (GPL)
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="JindentTask"></a>
        JindentTask
      </h4>
                        <p>JindentTask is a very straightforward wrapping of the
        Jindent tool, a vendor code beautifier. It enables to use
        Jindent natively from Ant.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4.1 and higher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://the.edouard.mercier.free.fr/Jindent_content.php">http://the.edouard.mercier.free.fr/Jindent_content.php</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Edouard Mercier
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Jing Task"></a>
        Jing Task
      </h4>
                        <p> Validates XML files against the RELAX NG alternative to XML Schema.
        The Jing task for Ant allows you to efficiently validate
        multiple files against multiple RELAX NG patterns and integrate
        RELAX NG validation with other XML processing.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.thaiopensource.com/relaxng/jing-ant.html">
            http://www.thaiopensource.com/relaxng/jing-ant.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          BSD-like
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="jMetra"></a>
        jMetra
      </h4>
                        <p>jMetra is a tool for collecting code metrics across a
        project lifecycle and compiling the results into
        JavaDoc-styled documentation to analyze project metrics over
        time.  jMetra is best utilized by integrating it with your
        project's scheduled build process.</p>
                                <p>It works from the command line or using several provided
        Ant tasks.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4 and higher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://www.jmetra.com/">http://www.jmetra.com/</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="mailto:<EMAIL>">R Todd Newton
              </a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Commercial, free licenses for open source projects and
            evaluations.
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="JMX4Ant"></a>
        JMX4Ant
      </h4>
                        <p>JMX4Ant provides tasks for integration with JMX (Java Management
        Extensions). It provides tasks for getting and setting attributes
        of MBeans, invoking their methods and much more.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4 and higher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://jmx4ant.sourceforge.net/">http://jmx4ant.sourceforge.net/</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="mailto:<EMAIL>">Brian Dueck</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License 1.1
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="JNI"></a>
        JNI
      </h4>
                        <p>

          JNI is a free toolkit that makes easy work of
          integrating Java and C through the Java Native
          Interface (JNI). It includes a code generator that
          generates both Java "proxy" classes to access C
          "peer" classes, and C "proxy" classes to access
          Java "peer" classes or interfaces. It also
          includes a core library with a simplified JVM
          interface as well as "helper" classes to ease
          working with the JNI data types. The code
          generation is driven by an XML project file that
          can be created with the assistance of the GUI
          Project Manager. The code generation can be
          invoked either from Ant or from the
          GUI. Includes a comprehensive printable PDF User
          Guide and plenty of examples.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://jnipp.sf.net/">http://jnipp.sf.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Phillip E. Trewhella</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU LGPL
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="JReleaseInfo"></a>
        JReleaseInfo
      </h4>
                        <p>Sometimes you are interested at runtime to have information
        from build time. This may be the build date, a build number or
        the version.  The JReleaseInfo Ant Task generates a java
        source file with getter methods for any desired and provided
        properties.  Furthermore, it can automatically generate a
        viewer (which can e.g.  be used as main-class in a library jar
        file) that shows the included release information.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://jreleaseinfo.sourceforge.net/">HomePage on SourceForge</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sourceforge.net/projects/jreleaseinfo/">Forums/Tracker on SourceForge</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="JRun Ant Tasks"></a>
        JRun Ant Tasks
      </h4>
                        <p>JRun 4 SP1 ships with lib/jrun-ant-tasks.jar, which defines
        three Ant tasks: jrun, jrunapp, and jrunjmx.  Documentation
        for the tasks can be found in JRun under
        docs/ant/jrun.html.</p>
                                <p>Note that the service pack must be installed on top of an
        existing JRun 4 installation.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4 or higher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://dynamic.macromedia.com/bin/MM/software/trial/hwswrec.jsp?product=jrun_sp">http://dynamic.macromedia.com/bin/MM/software/trial/hwswrec.jsp?product=jrun_sp</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Brian Deitte</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Commercial
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="JudoScript Ant Task"></a>
        JudoScript Ant Task
      </h4>
                        <p>The &lt;judoscript&gt; task is an easy way to embed JudoScript
           code in the Ant build script. The tag format is quite simple. You can
           either embed code directly, or can specify an external JudoScript program
           file as the <code>src</code> attribute value. Parameters can be specified
           as the <code>params</code> attribute; this is applicable to both embedded
           code and external files.
        </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 or higher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.judoscript.com/articles/ant.html">http://www.judoscript.com/articles/ant.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">James Jianbo Huang</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Lesser GNU Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Just4log Ant Task"></a>
        Just4log Ant Task
      </h4>
                        <p>Just4log is a ant task to optimize JVM bytecode with regards
        for Logs ( be it, Log4j, Apache Commons or JDK 1.4 )
        It depends on apache BCEL for Bytecode engineering.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5.2 or higher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://just4log.sourceforge.net">http://just4log.sourceforge.net</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Lucas Bruand</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache License 1.1.
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Kanaputs"></a>
        Kanaputs
      </h4>
                        <p>Kanaputs is a parser for java based scripting. It is an
        interpreter for Java. With Kanaputs you can use Java as an
        interpreted language: no more compilation, each instruction is
        executed when you write it.  It is a small programmation
        language to make script files above Java.</p>
                                <p>Kanaputs Ant Task provides a way to add any kind of
        programmatic features in your Ant script. The code you insert
        stays OS independent (because Kanaputs uses Java) and is
        completely integrated with Ant as you can give Ant properties
        to the Kanaputs code and get back the results in other
        properties.</p>
                                <p>Moreover, as you can invoke any kind of Java code with
        Kanaputs, you can popup windows from your Ant file to ask the
        Employee to do a choice.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.2 and above
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://www.kanaputs.org/">http://www.kanaputs.org/</a> <br />
              <a href="http://www.kanaputs.org/ant.html">http://www.kanaputs.org/ant.html</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="mailto:<EMAIL>"><EMAIL></a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Freeware
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="LaTeX Task"></a>
        LaTeX Task
      </h4>
                        <p>Simple Task to use (PDF)LaTeX, BibTeX, Makeindex and GlossTeX to
        create your documentation.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6.2 and above
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://www.dokutransdata.de/">http://www.dokutransdata.de/</a><br />
              <a href="http://www.dokutransdata.de/ant_latex/">http://www.dokutransdata.de/ant_latex/</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="mailto:<EMAIL>"><EMAIL></a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Freeware
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Macker"></a>
        Macker
      </h4>
                        <p>A build-time architectural testing tool, designed
        to maintain clean layering / tiering / modularity.
        Macker works against compiled class files, checking
        dependencies between classes against a set of
        pattern-based access rules you specify for your
        project in an XML rules file.  Macker doesn't presume
        anything about your architecture -- you write the
        rules, and Macker keeps you honest about them.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 and higher (1.4 untested but may work)
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://innig.net/macker/">http://innig.net/macker/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://innig.net/macker/contact.html">Paul Cantrell</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU GPL 2.0
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="mtxslt"></a>
        mtxslt
      </h4>
                        <p>The mtxslt (Multi-XSLT) extends the standard Ant "xslt/style" task
        to make it easy to use multiple XSLT engines during the same build.
        This is useful for regression testing of XSLT scripts against several
        engines.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://mtxslt.sourceforge.net/">http://mtxslt.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Anthony B. Coates</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software Foundation License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="nsd2LaTeX Task"></a>
        nsd2LaTeX Task
      </h4>
                        <p>Simple Task to use nsd2ltx to build your Nassi-Shneiderman diagrams.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6.2 and above
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://www.dokutransdata.de/">http://www.dokutransdata.de/</a><br />
              <a href="http://www.dokutransdata.de/ant_nsd2ltx/">http://www.dokutransdata.de/ant_nsd2ltx/</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="mailto:<EMAIL>"><EMAIL></a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Freeware
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Nurflugel AntScript Visualizer"></a>
        Nurflugel AntScript Visualizer
      </h4>
                        <p>The Nurflugel AntScript Visualizer takes your build file,
        finds any imported build files, and shows all relationships
        between targets, taskdefs, macrodefs, Ant and Antcalls; output
        options include PDF, SVG, and PNG.  Many options including
        grouping by build file, inclusion/exclusion of targets,
        taskdefs, imports, etc.  Installation is via Java WebStart, so
        you'll always have the freshest version available.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.1 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.nurflugel.com/webstart/AntScriptVisualizer/">http://www.nurflugel.com/webstart/AntScriptVisualizer/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          dbullard &lt;at&gt; nurflugel &lt;dot&gt; com (email
            will receive a challenge to weed out spam)
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Orangevolt Ant Tasks"></a>
        Orangevolt Ant Tasks
      </h4>
                        <p>Orangevolt ANT Tasks is a collection of Tasks for Apache
        Ant.</p>
                                <p>The Orangevolt Ant Tasks collections provides a bunch of
        Appplication Deployment related Tasks from windows specific
        tasks (registry access, executable generation), *nix specific
        tasks (kde/gnome shortcut generation) to many useful utility
        tasks like jnlp generation.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 or above
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sourceforge.net/projects/ovanttasks">http://sourceforge.net/projects/ovanttasks</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License (GPL)
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="pack"></a>
        pack
      </h4>
                        <p>pack is a task to build the smallest possible JAR to link
        and run one or more classes.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sadun-util.sourceforge.net/pack.html">http://sadun-util.sourceforge.net/pack.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Cristiano Sadun</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU Lesser General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="PCT"></a>
        PCT
      </h4>
                        <p>PCT is a task to compile Progress code, and in a more general
        way, to deal with Progress procedures and databases.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://pct.sourceforge.net">http://pct.sourceforge.net</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Gilles QUERRET</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Licence:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software Foundation License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="PesterCat Ant Toolkit"></a>
        PesterCat Ant Toolkit
      </h4>
                        <p>PesterCat is a web testing tool that was designed to
           perform functional testing for web applications. The PesterCat
           Ant Toolkit contains tasks to playback test scripts and create
           HTML reports.
         </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.pestercat.com/">http://www.pestercat.com/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Commercial
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="PMD"></a>
        PMD
      </h4>
                        <p>PMD checks Java source code for unused variables,
        unnecessary object creation, etc</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4 and higher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://pmd.sf.net/">http://pmd.sf.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Tom Copeland</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software Foundation License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="PRes"></a>
        PRes
      </h4>
                        <p>PRes is short for Property Resources and will generate a Java source
           file from name=value pair .property files which can be compiled like any
           other class.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 and later (may work with earlier)
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://mseries.sourceforge.net">http://mseries.sourceforge.net</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://web.ukonline.co.uk/mseries/contact.html">MSeries</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          LGPL
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="ProGuard"></a>
        ProGuard
      </h4>
                        <p><a href="http://proguard.sourceforge.net/">ProGuard</a> is
        a free Java class file shrinker and obfuscator.  It can detect
        and remove unused classes, fields, methods, and attributes. It
        can then rename the remaining classes, fields, and methods
        using short meaningless names.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Tested with 1.5. Should work with all versions.
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://proguard.sourceforge.net/">http://proguard.sourceforge.net/</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://proguard.sourceforge.net/feedback.html">Feedback Page</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="properties2java"></a>
        properties2java
      </h4>
                        <p>Properties2Java is an Ant task for automatic conversion of
        java ".properties" files to ".java" files extending the
        java.util.ListResourceBundle.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6 or above
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://properties2java.jayefem.de/">http://properties2java.jayefem.de/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Jan-Friedrich Mutter</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License 2.0
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Purge"></a>
        Purge
      </h4>
                        <p>Purge deletes all but the most recent few files from a fileset.
        For example: if you have generated files (logs, .ear, .war, .jar
        etc) accumulating in a directory, the purge task will allow you
        to delete the older files, keeping just the most recent ones.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4 or above
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.dallaway.com/ant/">http://www.dallaway.com/ant/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Richard Dallaway</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          LGPL
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="RefactorIT"></a>
        RefactorIT
      </h4>
                        <p>RefactorIT includes an Ant task for metrics and audits.
        RefactorIT is a Java refactoring, audit and metrics tool.
        It plugs into major Java IDEs, also runs stand-alone with
        a GUI and a command line interface.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.refactorit.com/">http://www.refactorit.com/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Commercial. (Free for accredited open source products, see
            <a href="http://www.refactorit.com/osc">http://www.refactorit.com/osc</a>.)
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Reflectant Task"></a>
        Reflectant Task
      </h4>
                        <p>This is a task for reflection invocation from within ant build file.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 or above
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sourceforge.net/projects/reflectant/">http://sourceforge.net/projects/reflectant/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          The Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="rundoc"></a>
        rundoc
      </h4>
                        <p>A task designed to help with the single-sourcing of program
        documentation. Rundoc replaces special commands
        (in the format <i>@@rundoc:command param1 param2...@@</i>) embedded
        within text files with their output in a
        specified format. Currently, only Docbook format is supported.</p>
                                <p>Rundoc was written to keep sample code output in program
        documentation synchronized with the actual output
        of the current code, by running the referenced code when the
        documentation is built.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Tested with 1.6  Should work with all versions.
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.martiansoftware.com/lab/index.html#rundoc">http://www.martiansoftware.com/lab/index.html#rundoc</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.martiansoftware.com/contact.html">Marty Lamb</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Revised BSD
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="SerialVer"></a>
        SerialVer
      </h4>
                        <p>SerialVer adds the Java serialver functionality to Apache
        Ant.  This project adds Tasks and FilterReaders to get, to
        insert and to modify the serialVersionUID in the source code
        of a serializable class.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5.1 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://serialver.sourceforge.net/">http://serialver.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="https://lists.sourceforge.net/lists/listinfo/serialver-development">developer mailing list</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Simian"></a>
        Simian
      </h4>
                        <p>Simian (Similarity Analyser) identifies duplication in Java,
        C#, C, CPP, COBOL, JSP, HTML source code and even plain text files.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5.1 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.redhillconsulting.com.au/products/simian/">
            http://www.redhillconsulting.com.au/products/simian/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">
            <EMAIL> (User Mailinglist)
            </a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Commercial, Free Licenses available for Non-Commercial Projects
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="SmartAnalyzer"></a>
        SmartAnalyzer
      </h4>
                        <p>Powerful analysis of dependencies between Java classes.
        Only affected classes will be recompiled and it can be used
        with any bytecode compiler.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Ant version 1.5.1
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://smartanalyzer.sourceforge.net/">http://smartanalyzer.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sourceforge.net/projects/smartanalyzer">support at sourceforge project page</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public Licence (GPL)
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="snip"></a>
        snip
      </h4>
                        <p>A task designed to help with the single-sourcing of program documentation.
        Snip extracts snippets of text from files, placing them into properties in the Ant project.
        These properties can then be used by any other Ant task, and are particularly useful when
        referenced by &lt;filter&gt;s within the &lt;copy&gt; task.</p>
                                <p>Snip was originally written to keep snippets of sample code in API documentation synchronized
        with the actual sample classes.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Tested with 1.5.1.  Should work with all versions.
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.martiansoftware.com/lab/index.html#snip">http://www.martiansoftware.com/lab/index.html#snip</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.martiansoftware.com/contact.html">Marty Lamb</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Revised BSD
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="SQL Compiler (SQLC)"></a>
        SQL Compiler (SQLC)
      </h4>
                        <p>SQL Compiler (SQLC) compiles database metadata and SQL statements into data access and data transfer classes.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Tested with Ant 1.5.4 and 1.6.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.hammurapi.biz/products/sqlc">http://www.hammurapi.biz/products/sqlc</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.hammurapi.biz/hammurapi-biz/ef/xmenu/contact.html">Project Contact Page</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          LGPL
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="SQLUnit"></a>
        SQLUnit
      </h4>
                        <p>SQLUnit is a regression and unit testing harness for testing
         database stored procedures. The test suite is written as an XML file.
         The SQLUnit harness itself is written in Java and uses the JUnit unit
         testing framework to convert the XML test specifications to JDBC calls
         and compare the results generated from the calls with the specified
         results. It also provides the &lt;sqlunit&gt; task to run the tests
         from a build script.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Tested with Ant 1.6
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sqlunit.sourceforge.net">http://sqlunit.sourceforge.net</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sourceforge.net/forum/?group_id=77832">Project forums</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License (GPL)
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Styler"></a>
        Styler
      </h4>
                        <p>The styler task makes useful combinations of XSLT transformations
        easy to specify in an Ant build file. Like the built-in Ant task
        style, styler can apply a single transformation to a set of XML files.
        But it can also:</p>
                                <ul>
          <li>handle multiple transformations, in parallel or pipelined.</li>
          <li>enable transformations that split or merge files</li>
          <li>process non-XML files, especially HTML (based on JTidy)</li>
          <li>apply non-XSLT transformation, especially "regular
          fragmentations"</li>
          <li>use any custom XMLReader or XMLFilter class to handle new file
          formats and transformation techniques.</li>
        </ul>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.langdale.com.au/styler/">http://www.langdale.com.au/styler/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Arnold deVos</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          LGPL
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Syntax"></a>
        Syntax
      </h4>
                        <p>Transforms source files into HTML documents with syntax
        highlighting. It can handle a variety of source files
        including Java, HTML, C/C++, SQL, and Java properties.  Colors
        for elements are specified using cascading style sheets.  The
        output can be templated for easy integration with a site's
        look and feel.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://ostermiller.org/syntax/ant.html">http://ostermiller.org/syntax/ant.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://ostermiller.org/contact.pl?regarding=Syntax+Highlighting">Stephen Ostermiller</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License (GPL)
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="TestSetGenerator"></a>
        TestSetGenerator
      </h4>
                        <p>The TestSetGenerator is an ant task for generating property files with
        testsets based on the results of SQL queries and validation plug-ins. Very
        usefull when building unit tests that make use of changing datasets. This
        task is an extension of the Ant SQL task. Hsqldb is used for both the
        examples and the unittests.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          ANT 1.4 (or later)
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://anttestsetgen.sourceforge.net/">http://anttestsetgen.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GPL
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Tidy Imports (Tim)"></a>
        Tidy Imports (Tim)
      </h4>
                        <p>Tim is a handy utility that can be executed on the command
        line or via Ant that automatically formats your import
        declarations. Tim is capable of removing unused imports,
        expanding or collapsing imports and even organising them into
        pre-determined groups.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.3 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.chive.com/tim.htm">http://www.chive.com/tim.htm</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Commercial
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="TiniAnt"></a>
        TiniAnt
      </h4>
                        <p>TiniAnt is an Ant task to support building applications for
        the <a href="http://www.ibutton.com/TINI/">TINI</a>.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.2 to 1.4.1
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://tiniant.sourceforge.net/">http://tiniant.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Sean Kelly</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          BSD-like license
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Transformica"></a>
        Transformica
      </h4>
                        <p>Transformica is a versatile and extensible code generator.
           Supports multiple source models including database metadata, Java source files, grammar
           files and custom models.
        </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Tested with Ant 1.5.x and 1.6.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.hammurapi.biz/products/transformica">http://www.hammurapi.biz/products/transformica</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.hammurapi.biz/hammurapi-biz/ef/xmenu/contact.html">Project Contact Page</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          LGPL
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Venus Application Publisher's (Vamp) Ant Task Suite"></a>
        Venus Application Publisher's (Vamp) Ant Task Suite
      </h4>
                        <p>Venus Application Publisher's (Vamp) Ant Task Suite allows
        you to sign and package your applications into relocatable Web
        Archives that you can drop into your web server for
        single-click launching using Java Web Start or into single
        Java Archive installers that serve up their content through a
        built-in, multi-threaded, ultra light-weight web server.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.2 and 1.3
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.geocities.com/vamp201/ant.html">http://www.geocities.com/vamp201/ant.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Gerald Bauer</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Version_Tool"></a>
        Version_Tool
      </h4>
                        <p>A versioning tool for Ant.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5.1
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://ant.ryangrier.com/">http://ant.ryangrier.com/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          BSD license
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="VPP"></a>
        VPP
      </h4>
                        <p>VPP provides general file preprocessing support based on
        the Velocity Template Engine.  The core functionality is
        provided as a filter for use with tasks that supports filter
        chains.  Also included are replacement tasks for &lt;copy&gt; and
        &lt;javac&gt; that integrate support for preprocessing.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          1.5.1 and above
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://vpp.sourceforge.net/">http://vpp.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          BSD license
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="WOProject"></a>
        WOProject
      </h4>
                        <p>WOProject provides a set of tools to work with
        <a href="http://webobjects.com/">WebObjects 5.1</a>
        independent from platform and IDE. It significantly
        improves developer productivity
        and makes complex project structures more flexible compared to
        traditional Makefile-based approach.
        </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://objectstyle.org/woproject/">http://objectstyle.org/woproject/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Andrus Adamchik</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache license
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="WSDLValidate"></a>
        WSDLValidate
      </h4>
                        <p>WSDLValidate is, as it sounds, a tool to validate WSDL files.
        <a href="http://dev.eclipse.org/viewcvs/indextech.cgi/~checkout~/wsvt-home/docs/articles/wsdl20Validator/wsdlvalidateant.html">WSDLValidate</a>
        is similar in configuration to the optional Ant task <a href="http://ant.apache.org/manual/OptionalTasks/xmlvalidate.html">XMLValidate</a>.
        WSDLValidate can optionally validate a WSDL document against the <a href="http://www.ws-i.org">WS-I Basic Profile</a>.
        </p>
                                <p>WSDLValidate is available as an Ant task, an Eclipse plug-in and a
        command line utility.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 or later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.eclipse.org/wsvt">http://www.eclipse.org/wsvt</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.eclipse.org/wsvt">Project newsgroup and
            mailing list</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www-124.ibm.com/developerworks/oss/CPLv1.0.htm">Common Public
            License (CPL)</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Xcluder"></a>
        Xcluder
      </h4>
                        <p>xcluder is an XML Inclusions (XInclude) task for Apache Ant.
        Offers the choice of using Xerces or Elliotte Rusty Harold's XOM API.
        </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4 or later. XInclude compliance depends on the
            underlying Xerces or XOM used. Xerces 2.5.0 and above works fine,
            but please note that 2.6.1 and 2.6.2 processed the
            http://www.w3.org/2003/XInclude, now obsolete by
            http://www.w3.org/2001/XInclude used by the latest
            Candidate Recommendation (13 April 2004).
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sourceforge.net/projects/xcluder">http://sourceforge.net/projects/xcluder</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License (GPL), GNU Library or Lesser
            General Public License (LGPL)
            
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="XDoclet"></a>
        XDoclet
      </h4>
                        <p>XDoclet is an extended Javadoc Doclet engine for use in Ant.
           It lets you create custom Javadoc @tags and based on those tags
           generates source code or other files (such as xml-ish deployment
           descriptors). Templates and matching tasks are provided to generate
           EJB and web application deployment descriptors.
           </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sourceforge.net/projects/xdoclet/">http://sourceforge.net/projects/xdoclet/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          BSD license
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="XInclude"></a>
        XInclude
      </h4>
                        <p><a href="http://www.jeckle.de/freeStuff/xia/index.html">XInclude</a> is
          a W3C standardized vocabulary for including arbitrary text or XML
          documents in other XML documents. This task performes the inclusion
          using an existing XInclude implementation</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5.2
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://www.jeckle.de/freeStuff/xia/index.html">http://www.jeckle.de/freeStuff/xia/index.html</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Lesser GNU Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="XMLReleaseNotes (XRN)"></a>
        XMLReleaseNotes (XRN)
      </h4>
                        <p>This framework is a release notes framework that enables to
        generate textual release notes from an XML file. This is an
        open framework that enables to integrate the information
        coming from VSC and bug tracking solutions, for instance.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://xmlreleasenotes.free.fr/">http://xmlreleasenotes.free.fr/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Edouard Mercier
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="XmlTask"></a>
        XmlTask
      </h4>
                        <p>XmlTask provides a simple means to modify XML documents
        without having to learn XSLT. A simple path reference to an
        XML node specifies the node you want to change, and how you
        want to allow XML insertion and removal, or attribute
        changes. The emphasis is on providing the simplest means to
        perform common XML replacements</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.oopsconsultancy.com/software/xmltask/">http://www.oopsconsultancy.com/software/xmltask/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">xmltask-users at lists.sourceforge.net</a> 
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="yGuard"></a>
        yGuard
      </h4>
                        <p>yGuard is a free Java(TM) Bytecode Obfuscator Task that
        needs no external script or project files. It can completely
        be configured and run through the Ant build script. The task
        supports multiple Jar files at once and makes use of
        patternsets and regular expressions to specify elements, which
        should be left unobfuscated.  Additionally it can be used to
        produce patches for obfuscated applications that have already
        been deployed.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.yworks.com/en/products_yguard_about.htm">http://www.yworks.com/en/products_yguard_about.htm</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Library: LGPL, Task: Commercial
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Zelix KlassMaster"></a>
        Zelix KlassMaster
      </h4>
                        <p>The task ZKMTask allows the Zelix KlassMaster Java obfuscator to be integrated into an Ant build.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4.1 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.zelix.com/klassmaster/docs/buildToolApi.html">http://www.zelix.com/klassmaster/docs/buildToolApi.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Commercial
      </td>
      </tr>
          </table>
                                            <h3 class="section">
      <a name="Compiler Implementations"></a>
      Compiler Implementations
    </h3>
                              <h4 class="subsection">
        <a name="Generics (JSR14) Early-Access Compiler Adapter"></a>
        Generics (JSR14) Early-Access Compiler Adapter
      </h4>
                        <p>This is an Ant compiler-adapter that allows you to use the
        normal <code>&lt;javac&gt;</code> task plus Sun's early-access
        compiler to compile Generics-enabled Java code.  (This is only
        necessary until JDK1.5 is released.)</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.madbean.com/blog/3/">http://www.madbean.com/blog/3/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Matt Quail &lt;spud[at]madbean[dot]com&gt;
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Public Domain
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="miniRMI &lt;code&gt;&amp;lt;rmic&amp;gt;&lt;/code&gt; implementation"></a>
        miniRMI <code>&lt;rmic&gt;</code> implementation
      </h4>
                        <p>miniRMI is a freeware opensource library that serves as a
        lightweight replacement for the original java.rmi packages and
        is suitable especially for applets. Ant 1.4+
        <code>&lt;rmic&gt;</code> adapter included.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://dione.zcu.cz/~toman40/miniRMI/">http://dione.zcu.cz/~toman40/miniRMI/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Petr Toman</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Gnu Lesser Public License
      </td>
      </tr>
          </table>
                                            <h3 class="section">
      <a name="IDE and Editor Integration"></a>
      IDE and Editor Integration
    </h3>
                              <h4 class="subsection">
        <a name="AntFarm"></a>
        AntFarm
      </h4>
                        <p>A plugin that integrates Ant into the jEdit editor.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          bundles Ant 1.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://plugins.jedit.org/plugins/?AntFarm">http://plugins.jedit.org/plugins/?AntFarm</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">jEdit developers mailinglist</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="AntMan"></a>
        AntMan
      </h4>
                        <p>An AddIn that integrates Ant with the JDeveloper IDE</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4.1
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.erudra.com/antman/index.html">http://www.erudra.com/antman/index.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Ashok Sridhar</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="AntRunner"></a>
        AntRunner
      </h4>
                        <p>An OpenTool that integrates Ant into the JBuilder IDE
        (version 5 and later).</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.2 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://antrunner.sourceforge.net/">http://antrunner.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Dirk Schnelle</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="AntWork"></a>
        AntWork
      </h4>
                        <p>A plugin that integrates Ant into the Jext editor.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.2 and 1.3
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="ftp://jext.sourceforge.net/pub/jext/plugins/AntWork.zip">ftp://jext.sourceforge.net/pub/jext/plugins/AntWork.zip</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Klaus Hartlage</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Eclipse"></a>
        Eclipse
      </h4>
                        <p>Eclipse is a universal tool platform with Ant integration.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              Ant 1.3 - 1.4.1
              Bundles Ant 1.6.2 as of Eclipse 3.0.1 (tested with Ant 1.5.4 - 1.6.2)
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://www.eclipse.org/">http://www.eclipse.org/</a>
              or
              <a href="http://dev.eclipse.org/viewcvs/index.cgi/~checkout~/platform-ant-home/index.html">
                http://dev.eclipse.org/viewcvs/index.cgi/~checkout~/platform-ant-home/index.html
              </a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="news://news.eclipse.org/eclipse.platform">news://news.eclipse.org/eclipse.platform</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Common Public License Version 1.0
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Gel"></a>
        Gel
      </h4>
                        <p>Java IDE with support for Ant.  Gel is a native Microsoft
        Windows software.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4 and above
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.gexperts.com/gel.html">http://www.gexperts.com/gel.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://groups.yahoo.com/group/gelide/">Project Mailing List</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Freeware
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="IntelliJ IDEA 5.0"></a>
        IntelliJ IDEA 5.0
      </h4>
                        <p>Java IDE with refactoring support and Ant integration.
      The IDE has special editing and navigation support for Ant.
      </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          bundles Ant 1.6.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.intellij.com/idea/">http://www.intellij.com/idea/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Commercial; Academic and OpenSource licenses available.
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="JBuilder"></a>
        JBuilder
      </h4>
                        <p>Borland JBuilder
        comes with built-in Ant support, including build file editing.
        Some versions come with Ant debugging support.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Bundles Ant 1.6
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.borland.com/jbuilder/index.html">http://www.borland.com/jbuilder/index.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Commercial; foundation edition is free.
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="JDEE"></a>
        JDEE
      </h4>
                        <p>The Java Development Environment for Emacs (JDEE) supports
        Apache Ant as one of three built-in ways to build your
        applications.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.2 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://jdee.sunsite.dk/">http://jdee.sunsite.dk/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">JDEE Mailing list.</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="NetBeans"></a>
        NetBeans
      </h4>
                        <p>A module that integrates Ant into the NetBeans IDE, as well as derivative products such as Sun Java
        Studio (formerly Forte for Java and Sun ONE Studio) and Sun Java Studio Creator.
        This IDE uses Ant as its <i>sole</i> means of building applications,
        with custom tasks and an Ant-aware editor.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          bundles 1.6.5 for NetBeans 5.0 and 5.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://ant.netbeans.org/">http://ant.netbeans.org/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Common Development and Distribution License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Optistic IDX Java IDE"></a>
        Optistic IDX Java IDE
      </h4>
                        <p>Java IDE with deep Ant integration. IDX is a native Microsoft Windows program.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          bundles Ant 1.6
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://www.optistic.com/idx">http://optistic.com/idx</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="mailto:<EMAIL>"><EMAIL></a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Commercial; Academic and OpenSource licenses available.
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Oracle9i JDeveloper"></a>
        Oracle9i JDeveloper
      </h4>
                        <p>Java IDE with support for Ant.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5.1
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://otn.oracle.com/products/jdev/">http://otn.oracle.com/products/jdev/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Commercial
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Virtual Ant"></a>
        Virtual Ant
      </h4>
                        <p>
          Instead of manually creating build scripts in XML, Virtual Ant provides a fully virtual file system
          where you can run your tasks in real time and see the results. Everything that you do is recorded and
          turned into an Ant build script.
        </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              Ant 1.6.5 onwards
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://www.placidsystems.com/virtualant/">http://www.placidsystems.com/virtualant/</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="mailto:<EMAIL>"><EMAIL></a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Commercial; OpenSource licenses available too.
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="WebSphere Studio Application Developer"></a>
        WebSphere Studio Application Developer
      </h4>
                        <p>WSAD features Ant integrate by virtue of being built on the Eclipse tools platform.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          bundles Ant 1.4.1
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Article:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www7b.software.ibm.com/wsdd/library/techarticles/0203_searle/searle1.html">Ant Integration Part1</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Commercial
      </td>
      </tr>
          </table>
                                            <h3 class="section">
      <a name="Source Control Systems"></a>
      Source Control Systems
    </h3>
                        <p>There are several integration with SCM systems. Some are
      <a href="manual/tasksoverview.html#scm">built in</a>. But some are available as
      external libraries. Here a list of task libraries we are aware of:</p>
                                      <h4 class="subsection">
        <a name="Surround SCM"></a>
        Surround SCM
      </h4>
                        <p>These are tasks that allow users to access Surround SCM
         functionality from within Ant build scripts.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.seapine.com/scmresources.php#integration">
            Surround SCM Resource Center</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Seapine Support</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU Lesser General Public License
      </td>
      </tr>
          </table>
                                    
    </div>
  </div>

        <p class="copyright">
        <script type="text/javascript" language="JavaScript"><!--
                document.write(" - "+"Last Published: " + document.lastModified);
              //  -->
        </script>
      </p>
    </body>
  </html>











