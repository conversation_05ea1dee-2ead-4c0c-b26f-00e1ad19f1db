

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
        <html lang="en">
    <!-- GENERATED FILE, DO NOT EDIT, EDIT THE XML FILE IN xdocs INSTEAD! -->
    <head>
      <META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
        <title>Apache Ant - News</title>
        <link type="text/css" href="./page.css" rel="stylesheet">
          <meta name="author" content="<PERSON>">
  <meta name="email" content="">
        <meta name="author" content="<PERSON>">
  <meta name="email" content="">
        <meta name="author" content="Magesh Umasankar">
  <meta name="email" content="">
        <meta name="author" content="<PERSON>">
  <meta name="email" content="">
      </head>

    <body>
      <p class="navpath">
        <script src="./breadcrumbs.js" language="JavaScript" type="text/javascript"></script>
      </p>

      <div class="logobar">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td align="left"><img border="0" alt="Apache Ant site" src="./images/group-logo.gif"></td>
            <td align="center" width="100%"><img alt="Apache Ant logo" border="0" src="./images/project-logo.gif"></td>
            <td align="right">
              <form target="_blank" onsubmit="q.value = query.value + ' site:ant.apache.org'" action="http://www.google.com/search" method="get">
                <table summary="search" border="0" cellspacing="0" cellpadding="0" bgcolor="#4C6C8F">
                  <tr>
                    <td colspan="3"><img height="10" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td nowrap="nowrap" class="searchcaption">
                      <input name="q" type="hidden">
                      <input size="15" id="query" type="text">
                      <img height="1" width="5" alt="" src="./images/spacer.gif">
                      <input name="Search" value="Search" type="submit">
                      <br>
                      the Apache Ant site
                    </td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-left.gif"></td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-right.gif"></td>
                  </tr>
                </table>
              </form>
            </td>
          </tr>
        </table>
      </div>

                  <div class="tab">
              <table summary="tab bar" border="0" cellpadding="0" cellspacing="0">
                <tr>
                                  <td width="8"><img alt="" height="5" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="selected tab" style="height: 1.5em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                        <td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-left.gif"></td><td valign="middle" bgcolor="#4C6C8F"><font color="#ffffff" size="2" face="Arial, Helvetica, Sans-serif"><b>Home</b></font></td><td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                                    <td width="5"><img alt="" height="8" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="non selected tab" style="height: 1.4em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                          <td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-left.gif"></td><td valign="middle" bgcolor="#B2C4E0"><a href="./projects/index.html"><font size="2" face="Arial, Helvetica, Sans-serif">Projects</font></a></td><td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                            </tr>
              </table>
            </div>

      <div class="bluebar"></div>
                    
  <div class="menucontainer">

    <div class="menu">
      <ul>
              <li class="menuheader">Apache Ant
          <ul>
                            <li>
                                    <a href="./index.html">Welcome</a>
                                </li>
                            <li>
                                    <a href="./license.html">License</a>
                                </li>
                            <li>
                              <span class="sel">News</span>
                              </li>
                      </ul>
        </li>
              <li class="menuheader">Documentation
          <ul>
                            <li>
                                    <a href="./manual/index.html">Manual</a>
                                </li>
                            <li>
                                    <a href="./projects.html">Related Projects</a>
                                </li>
                            <li>
                                    <a href="./external.html">External Tools and Tasks</a>
                                </li>
                            <li>
                                    <a href="./resources.html">Resources</a>
                                </li>
                            <li>
                                    <a href="./faq.html">Frequently Asked Questions</a>
                                </li>
                            <li>
                                    <a href="http://wiki.apache.org/ant/FrontPage">Wiki</a>
                                </li>
                            <li>
                                    <a href="./problems.html">Having Problems?</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Download
          <ul>
                            <li>
                                    <a href="http://ant.apache.org/bindownload.cgi">Binary Distributions</a>
                                </li>
                            <li>
                                    <a href="http://ant.apache.org/srcdownload.cgi">Source Distributions</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Contributing
          <ul>
                            <li>
                                    <a href="./mail.html">Mailing Lists</a>
                                </li>
                            <li>
                                    <a href="./svn.html">Subversion Repositories</a>
                                </li>
                            <li>
                                    <a href="./nightlies.html">Nightly Builds</a>
                                </li>
                            <li>
                                    <a href="./bugs.html">Bug Database</a>
                                </li>
                            <li>
                                    <a href="http://www.apache.org/foundation/contributing.html">Donations</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Project Management
          <ul>
                            <li>
                                    <a href="./contributors.html">Contributors</a>
                                </li>
                            <li>
                                    <a href="./mission.html">Apache Ant Mission</a>
                                </li>
                            <li>
                                    <a href="./bylaws.html">Project Bylaws</a>
                                </li>
                            <li>
                                    <a href="./legal.html">Legal</a>
                                </li>
                      </ul>
        </li>
            </ul>
    </div>
    <img style="float: left" height="10" width="10" border="0" alt="" src="./images/menu-left.gif">
    <img style="float: right" height="10" width="10" border="0" alt="" src="./images/menu-right.gif">
  </div>
      <div class="lightbluebar">&nbsp;</div>
  <div class="main">
  <div class="content">
    <h1 class="title">News</h1>
            <h3 class="section">
      <a name="Ant 1.7.0"></a>
      Ant 1.7.0
    </h3>
                        <h3>December 19, 2006 - Ant 1.7.0 Available</h3>
                                <p>Apache Ant 1.7.0 is now available for <a href="http://ant.apache.org/bindownload.cgi">download</a>.</p>
                                <p> Ant 1.7 introduces a resource framework. Some of the core ant 
    tasks such as &lt;copy/&gt; are now able to process not only file 
    system resources but also zip entries, tar entries, paths, ... 
    Resource collections group resources, and can be further 
    combined with operators such as union and intersection. This 
    can be extended by custom resources and custom tasks using resources.</p>
                                <p>
    Ant 1.7 starts outsourcing of optional tasks to Antlibs. 
    The .NET antlib in preparation will replace the .NET optional tasks which ship in Ant.
    Support for the version control system Subversion will be only provided as an antlib to
    be released shortly. 
    </p>
                                <p>Ant 1.7 fixes also a large number of bugs.</p>
                                <p>Ant 1.7 has some initial support for Java6 features.</p>
                        <h3 class="section">
      <a name=".NET Ant Library 1.0Beta1"></a>
      .NET Ant Library 1.0Beta1
    </h3>
                        <h3>November 6, 2006 - Apache .NET Ant Library 1.0 Available</h3>
                                <p>Apache .NET Ant Library 1.0 is now available for <a href="http://ant.apache.org/antlibs/bindownload.cgi">download</a>.</p>
                                <p>This Ant Library contains support for tools like NUnit as well
    as the "old .NET tasks of Ant's core.  It has been tested
    Microsoft's frameworks as well as Mono.</p>
                                <p>For more information see the <a href="antlibs/dotnet/">Antlib's
    home page</a></p>
                        <h3 class="section">
      <a name="AntUnit 1.0Beta2"></a>
      AntUnit 1.0Beta2
    </h3>
                        <h3>October 29, 2006 - Apache AntUnit 1.0Beta2 Available</h3>
                                <p>Apache AntUnit 1.0Beta1 is now available for <a href="http://ant.apache.org/antlibs/bindownload.cgi">download</a>.</p>
                                <p>This Ant Library contains tasks to test Ant tasks using Ant
    instead of JUnit.  For more information see the <a href="antlibs/antunit/">AntUnit home page</a>.</p>
                        <h3 class="section">
      <a name="AntUnit 1.0Beta1"></a>
      AntUnit 1.0Beta1
    </h3>
                        <h3>September 22, 2006 - Apache AntUnit 1.0Beta1 Available</h3>
                                <p>Apache AntUnit 1.0Beta1 is now available for <a href="http://ant.apache.org/antlibs/bindownload.cgi">download</a>.</p>
                                <p>This Ant Library contains tasks to test Ant tasks using Ant
    instead of JUnit.  For more information see the <a href="antlibs/antunit/">AntUnit home page</a>.</p>
                        <h3 class="section">
      <a name=".NET Ant Library 1.0Beta1"></a>
      .NET Ant Library 1.0Beta1
    </h3>
                        <h3>September 13, 2006 - Apache .NET Ant Library 1.0Beta1 Available</h3>
                                <p>Apache .NET Ant Library 1.0Beta1 is now available for <a href="http://ant.apache.org/antlibs/bindownload.cgi">download</a>.</p>
                                <p>This Ant Library contains support for tools like NUnit as well
    as the "old .NET tasks of Ant's core.  It has been tested
    Microsoft's frameworks as well as Mono.</p>
                                <p>For more information see the <a href="antlibs/dotnet/">Antlib's
    home page</a></p>
                        <h3 class="section">
      <a name="Ant 1.6.5"></a>
      Ant 1.6.5
    </h3>
                        <h3>June 2, 2005 - Ant 1.6.5 Available</h3>
                                <p>Apache Ant 1.6.5 is now available for <a href="http://ant.apache.org/bindownload.cgi">download</a>.</p>
                                <p>This is a bug fix release.</p>
                        <h3 class="section">
      <a name="Ant 1.6.4"></a>
      Ant 1.6.4
    </h3>
                        <h3>May 19, 2005 - Ant 1.6.4 Available</h3>
                                <p>Apache Ant 1.6.4 is now available for <a href="http://archive.apache.org/dist/ant/">download</a>.</p>
                                <p>This is a bug fix release.</p>
                        <h3 class="section">
      <a name="Ant 1.6.3"></a>
      Ant 1.6.3
    </h3>
                        <h3>April 28, 2005 - Ant 1.6.3 Available</h3>
                                <p>Apache Ant 1.6.3 is now available for <a href="http://archive.apache.org/dist/ant/">download</a>.</p>
                                <p>There is a large list of fixed bugs and enhancements.</p>
                                <p>Some of the bugs affecting the embedded use of Ant are fixed.</p>
                        <h3 class="section">
      <a name="Antidote Retired"></a>
      Antidote Retired
    </h3>
                        <h3>April 4th, 2005 - The Apache Ant Project Retires Antidote, the
    Ant GUI</h3>
                                <p>The Antidote subproject was once started to provide a GUI for
    Ant at a time where IDE support for Ant was far from usable.
    Unfortunately it never attracted a developer community of its
    own.</p>
                                <p>At the same time IDE support for Ant has become ubiquitous by
    now and there is little reason to have a GUI just for Ant.  This
    makes it even less likely that volunteers will start to spend time
    working on it.</p>
                                <p>Antidote's development has been stalled for years now, despite
    some efforts to rejuvenate it by single developers.  Therefore the
    Ant developers have chosen to retire Antidote.</p>
                                <p>Antidote will no longer be developed by the Ant project; its
    CVS module will be shut down.</p>
                                <p>If you are interested in Antidote's sources to learn from or
    build on it, you can find snapshots at <a href="http://archive.apache.org/ant/antidote/">http://archive.apache.org/ant/antidote/</a>.</p>
                        <h3 class="section">
      <a name="Ant 1.6.2"></a>
      Ant 1.6.2
    </h3>
                        <h3>July 16, 2004 - Ant 1.6.2 Available</h3>
                                <p>Apache Ant 1.6.2 available for <a href="http://archive.apache.org/dist/ant/">download</a>.</p>
                                <p>Nested elements for namespaced tasks and types may belong to the
Ant default namespace as well as the task's or type's namespace.</p>
                                <p>All exceptions thrown by tasks are now wrapped in a
buildexception giving the location in the buildfile of the task.</p>
                                <p>Ant 1.6.2 fixes a large number of bugs and adds a number of
features which were asked for by users on Bugzilla.</p>
                        <h3 class="section">
      <a name="Wiki Migration"></a>
      Wiki Migration
    </h3>
                        <h3>February 29, 2004</h3>
                                <p>The Ant Wiki pages have been migrated to their 
      <a href="http://wiki.apache.org/ant/">new home</a> on the Apache
      Wiki farm.
    </p>
                        <h3 class="section">
      <a name="Ant 1.6.1"></a>
      Ant 1.6.1
    </h3>
                        <h3>February 12, 2004 - Ant 1.6.1 Available</h3>
                                <p>Apache Ant 1.6.1 is still available for
      <a href="http://archive.apache.org/dist/ant/">download</a>.
     </p>
                                <p>The ASF Board has approved the new Apache License 2.0.
    For a copy of that license, please see
    <a href="http://www.apache.org/licenses/">
    http://www.apache.org/licenses/</a>.</p>
                                <p>The Ant 1.6.1 release is delivered with the
    Apache License 2.0.</p>
                                <p>Ant 1.6.1 fixes several bugs, most notably the handling
    of the default namespace for nested elements.</p>
                                <p>Ant 1.6.1 also introduces initial support for compiling with
    Java 1.5.</p>
                        <h3 class="section">
      <a name="Ant 1.6.0"></a>
      Ant 1.6.0
    </h3>
                        <h3>December 18, 2003 - Ant 1.6.0 Available</h3>
                                <p>Apache Ant 1.6.0 is still available for
      <a href="http://archive.apache.org/dist/ant/">download</a>.
     </p>
                                <p>As
    we've already said in the announcements of Ant 1.5.4, this release
    requires JDK 1.2 or later to run.</p>
                                <p>Ant 1.6.0 adds a lot of new features, most prominently support
    for XML namespaces as well as a new concept of Ant libraries that
    makes use of namespaces to avoid name clashes of custom tasks.
    For a longer list of fixed bugs and new features see the release
    notes.</p>
                                <p>If you find anything that hasn't been covered in the manual (I bet you
did) or could be explained better, feel free to help us out in the
<a href="http://wiki.apache.org/ant/NewAntFeaturesInDetail">Wiki</a>.</p>
                        <h3 class="section">
      <a name="Ant 1.5.4"></a>
      Ant 1.5.4
    </h3>
                        <h3>August 12, 2003 - Ant 1.5.4 Available</h3>
                                <p>Apache Ant 1.5.4 is still available for
     <a href="http://archive.apache.org/dist/ant/">download</a>.
    </p>
                                <p>This is a minor bugfix release that fixes a problem with the
    <code>javah</code> task on JDK 1.4.2 and a couple of bugs in the
    Visual Age for Java intergration tasks.  If you don't use javah or
    VAJ, there is no reason to upgrade.</p>
                                <div class="warning">
    <div class="label">Note</div>
    <div class="content">Ant 1.5.4 is the last release that supports
      JDK 1.1. Ant 1.6.0 requires JDK 1.2 or
      later.
    </div>
    </div>
                        <h3 class="section">
      <a name="Java Pro 2003 Readers Choice Award"></a>
      Java Pro 2003 Readers Choice Award
    </h3>
                        <a href="http://www.ftponline.com/javapro/">
      <img style="padding: 5px" src="images/jp_rcwinner_2003.gif" alt="" border="0" height="80" width="139" align="right" /></a>
                                <h3>June 11th, 2003: Ant wins a Java Pro readers' choice award</h3>
                                <p>
      Ant has won the Java Pro 2003 Readers' Choice Award for 
    </p>
                                <p>
      <strong>Most Valuable Java Deployment Technology</strong>.
    </p>
                                <p>
      Thanks to Java Pro and all its readers. You can read about
      these 
      <a href="http://www.ftponline.com/reports/javaone/2003/awards/">awards</a>
      at the Java Pro website.
    </p>
                        <h3 class="section">
      <a name="JDJ Editors Choice Award"></a>
      JDJ Editors Choice Award
    </h3>
                        <a href="http://sys-con.com/java/article2a.cfm?id=2059&amp;count=1734&amp;tot=6&amp;page=2"><img src="images/JDJEditorsChoiceAward.jpg" alt="" border="0" align="right" /></a>
                                <h3>June 2003: Ant wins JDJ Editors' Choice Award</h3>
                                <p>
"Ant is the hammer of the Java world: without it, civilization might have progressed, but much more slowly than it has. Ant is one of the most useful build tools I have ever had the pleasure to use." - Joe Ottinger
    </p>
                        <h3 class="section">
      <a name="Ant keeps on winning!"></a>
      Ant keeps on winning!
    </h3>
                        <a href="http://www.javaworld.com/"><img src="images/jw_ec_logo_winner2003.gif" alt="" border="0" height="108" width="252" align="right" /></a>
                                <h3>June 9th, 2003: Ant wins the JavaWorld Editors' Choice Award</h3>
                                <p>
    Ant has won the JavaWorld Editors' Choice Award for
    </p>
                                <p>
    <strong>Most Useful Java Community-Developed Technology</strong>
    </p>
                                <p>
    for the second time in a row! Read the
    <a href="http://www.javaworld.com/javaworld/jw-06-2003/jw-0609-eca.html"> full article</a> -- or jump directly to the bit about
    <a href="http://www.javaworld.com/javaworld/jw-06-2003/jw-0609-eca-p4.html"> our award</a> <code>:)</code></p>
                        <h3 class="section">
      <a name="Ant 1.5.3"></a>
      Ant 1.5.3
    </h3>
                        <h3>April 9, 2003 - Ant 1.5.3 Available</h3>
                                <p>Apache Ant 1.5.3 is still available for
     <a href="http://archive.apache.org/dist/ant/">download</a>.
    </p>
                        <h3 class="section">
      <a name="Ant 1.5.2"></a>
      Ant 1.5.2
    </h3>
                        <h3>March 3, 2003 - Ant 1.5.2 Available!</h3>
                                <p>The final version of Ant 1.5.2 is available for
     <a href="http://archive.apache.org/dist/ant/">download</a>.
     If you have any feedback on this release, feel free to join the 
     discussion on the dev and Employee mailing lists.
     </p>
                        <h3 class="section">
      <a name="Ant Top Level Project"></a>
      Ant Top Level Project
    </h3>
                        <h3>November 18, 2002</h3>
                                <p>The Apache board <a href="mission.html">created</a>
       the Apache Ant top level project. Ant has now migrated from the Jakarta
       project into an Apache project of its own. This is primarily an
       organizational change and will not affect the technical aspects of
       the project. Ant retains a strong association with the Apache
       Jakarta project. One effect of this change is that the Ant webpage
       is now located at <a href="http://ant.apache.org/">http://ant.apache.org/</a>
     </p>
                        <h3 class="section">
      <a name="Ant 1.5.1"></a>
      Ant 1.5.1
    </h3>
                        <h3>October 3, 2002 - Ant 1.5.1 Available !</h3>
                                <p>The final version of Ant 1.5.1 is still available for
     <a href="http://archive.apache.org/dist/ant/">
     download</a>. If you have any feedback on this release, feel free to join the 
     discussion on the ant-dev and ant-Employee mailing lists.
     </p>
                        <h3 class="section">
      <a name="Ant 1.5"></a>
      Ant 1.5
    </h3>
                        <h3>July 15, 2002 - Fix for Cygwin problem in wrapper script available</h3>
                                <p>The wrapper script of Ant 1.5 needs to be replaced with a new
      version for Cygwin users.  See the <a href="faq.html#1.5-cygwin-sh">FAQ</a> for details.</p>
                                <h3>July 10, 2002 - Ant 1.5 Released!</h3>
                                <p>The final version of Ant 1.5 is now available for 
     <a href="http://archive.apache.org/dist/ant/">
     download</a>. If you have any feedback on this release, feel free to join the 
     discussion on the ant-dev and ant-Employee mailing lists.
     </p>
                        <h3 class="section">
      <a name="Ant wins again!"></a>
      Ant wins again!
    </h3>
                        <a href="http://www.sdmagazine.com/"><img src="images/sdm_productivity_award.gif" alt="" border="0" height="108" width="181" align="right" /></a>
                                <h3>Apr 29, 2002: Ant wins <em>Software Development</em> magazine's
    2002 Productivity Award.</h3>
                                <p>
    Ant has been awarded a <strong>2002 Productivity Award</strong> by
    <a href="http://www.sdmagazine.com/"><em>Software Development</em></a>
    magazine.  Read the
    <a href="http://www.sdmagazine.com/jolts/press_release_4-26-02.htm">
    press release</a> for more information and the full list of winners.
    </p>
                        <h3 class="section">
      <a name="Ant has won!"></a>
      Ant has won!
    </h3>
                        <a href="http://www.javaworld.com/"><img src="images/jw_ec_logo_winner2002.gif" alt="" border="0" height="108" width="252" align="right" /></a>
                                <h3>Mar 26, 2002: Ant wins the JavaWorld Editors' Choice
    Award</h3>
                                <p>
    Ant has won the JavaWorld Editors' Choice Award for
    <strong>Most Useful Java Community-Developed Technology</strong>.
    Read the
    <a href="http://www.javaworld.com/javaworld/jw-03-2002/jw-0326-awards.html "> full article</a> -- or jump directly to the bit about
    <a href="http://www.javaworld.com/javaworld/jw-03-2002/jw-0326-awards-p3.html"> our award</a> <code>:)</code></p>
                        <h3 class="section">
      <a name="Java 1.4 Support"></a>
      Java 1.4 Support
    </h3>
                        <h3>Feb 15, 2002: Java 1.4 Support</h3>
                                <p>
      Java 1.4 has now been released by Sun. The latest Ant source supports 
      the new <tt>assert</tt> statement in the compiler task via the <tt>source</tt> 
      attribute. It also contains a compatibility fix needed for some ant tasks
      on Java 1.4 over Windows XP. If you have problems running Ant 1.4.1 on WinXP/Java 1.4,
      please use a recent build or compile your own version from the source tree. 
    </p>
                        <h3 class="section">
      <a name="See our new logo!"></a>
      See our new logo!
    </h3>
                        <h3>Have a look at our new cool logo!</h3>
                        <h3 class="section">
      <a name="Ant 1.4.1"></a>
      Ant 1.4.1
    </h3>
                        <h3>11 October 2001 Ant 1.4.1 released !</h3>
                                <p>Please visit the 
     <a href="http://archive.apache.org/dist/ant/">
     download area</a>. 
     </p>
                        <h3 class="section">
      <a name="Best-Practices Profile of Ant at Sun's Dot-Com Builder"></a>
      Best-Practices Profile of Ant at Sun's Dot-Com Builder
    </h3>
                        <p>Sun has released an introductory article on Ant on their
      Dot-Com Builder site on May 30 2001. See <a href="http://dcb.sun.com/practices/profiles/ant.jsp">http://dcb.sun.com/practices/profiles/ant.jsp</a></p>
                
    </div>
  </div>

        <p class="copyright">
        <script type="text/javascript" language="JavaScript"><!--
                document.write(" - "+"Last Published: " + document.lastModified);
              //  -->
        </script>
      </p>
    </body>
  </html>











