

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
        <html lang="en">
    <!-- GENERATED FILE, DO NOT EDIT, EDIT THE XML FILE IN xdocs INSTEAD! -->
    <head>
      <META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
        <title>Apache Ant - Related Projects</title>
        <link type="text/css" href="./page.css" rel="stylesheet">
          <meta name="author" content="<PERSON>">
  <meta name="email" content="<EMAIL>">
      </head>

    <body>
      <p class="navpath">
        <script src="./breadcrumbs.js" language="JavaScript" type="text/javascript"></script>
      </p>

      <div class="logobar">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td align="left"><img border="0" alt="Apache Ant site" src="./images/group-logo.gif"></td>
            <td align="center" width="100%"><img alt="Apache Ant logo" border="0" src="./images/project-logo.gif"></td>
            <td align="right">
              <form target="_blank" onsubmit="q.value = query.value + ' site:ant.apache.org'" action="http://www.google.com/search" method="get">
                <table summary="search" border="0" cellspacing="0" cellpadding="0" bgcolor="#4C6C8F">
                  <tr>
                    <td colspan="3"><img height="10" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td nowrap="nowrap" class="searchcaption">
                      <input name="q" type="hidden">
                      <input size="15" id="query" type="text">
                      <img height="1" width="5" alt="" src="./images/spacer.gif">
                      <input name="Search" value="Search" type="submit">
                      <br>
                      the Apache Ant site
                    </td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-left.gif"></td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-right.gif"></td>
                  </tr>
                </table>
              </form>
            </td>
          </tr>
        </table>
      </div>

                  <div class="tab">
              <table summary="tab bar" border="0" cellpadding="0" cellspacing="0">
                <tr>
                                  <td width="8"><img alt="" height="5" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="selected tab" style="height: 1.5em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                        <td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-left.gif"></td><td valign="middle" bgcolor="#4C6C8F"><font color="#ffffff" size="2" face="Arial, Helvetica, Sans-serif"><b>Home</b></font></td><td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                                    <td width="5"><img alt="" height="8" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="non selected tab" style="height: 1.4em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                          <td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-left.gif"></td><td valign="middle" bgcolor="#B2C4E0"><a href="./projects/index.html"><font size="2" face="Arial, Helvetica, Sans-serif">Projects</font></a></td><td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                            </tr>
              </table>
            </div>

      <div class="bluebar"></div>
                    
  <div class="menucontainer">

    <div class="menu">
      <ul>
              <li class="menuheader">Apache Ant
          <ul>
                            <li>
                                    <a href="./index.html">Welcome</a>
                                </li>
                            <li>
                                    <a href="./license.html">License</a>
                                </li>
                            <li>
                                    <a href="./antnews.html">News</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Documentation
          <ul>
                            <li>
                                    <a href="./manual/index.html">Manual</a>
                                </li>
                            <li>
                              <span class="sel">Related Projects</span>
                              </li>
                            <li>
                                    <a href="./external.html">External Tools and Tasks</a>
                                </li>
                            <li>
                                    <a href="./resources.html">Resources</a>
                                </li>
                            <li>
                                    <a href="./faq.html">Frequently Asked Questions</a>
                                </li>
                            <li>
                                    <a href="http://wiki.apache.org/ant/FrontPage">Wiki</a>
                                </li>
                            <li>
                                    <a href="./problems.html">Having Problems?</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Download
          <ul>
                            <li>
                                    <a href="http://ant.apache.org/bindownload.cgi">Binary Distributions</a>
                                </li>
                            <li>
                                    <a href="http://ant.apache.org/srcdownload.cgi">Source Distributions</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Contributing
          <ul>
                            <li>
                                    <a href="./mail.html">Mailing Lists</a>
                                </li>
                            <li>
                                    <a href="./svn.html">Subversion Repositories</a>
                                </li>
                            <li>
                                    <a href="./nightlies.html">Nightly Builds</a>
                                </li>
                            <li>
                                    <a href="./bugs.html">Bug Database</a>
                                </li>
                            <li>
                                    <a href="http://www.apache.org/foundation/contributing.html">Donations</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Project Management
          <ul>
                            <li>
                                    <a href="./contributors.html">Contributors</a>
                                </li>
                            <li>
                                    <a href="./mission.html">Apache Ant Mission</a>
                                </li>
                            <li>
                                    <a href="./bylaws.html">Project Bylaws</a>
                                </li>
                            <li>
                                    <a href="./legal.html">Legal</a>
                                </li>
                      </ul>
        </li>
            </ul>
    </div>
    <img style="float: left" height="10" width="10" border="0" alt="" src="./images/menu-left.gif">
    <img style="float: right" height="10" width="10" border="0" alt="" src="./images/menu-right.gif">
  </div>
      <div class="lightbluebar">&nbsp;</div>
  <div class="main">
  <div class="content">
    <h1 class="title">Related Projects</h1>
            <h3 class="section">
      <a name="Related Projects"></a>
      Related Projects
    </h3>
                        <p>Nothing listed here is directly supported by the Ant
      developers, if you encounter any problems with them, please use
      the contact information.</p>
                                      <h4 class="subsection">
        <a name="AndroMDA"></a>
        AndroMDA
      </h4>
                        <p>AndroMDA is a code generator tool that follows the Model
        Driven Architecture (MDA) paradigm. It takes a UML model from
        a CASE-tool and generates classes and deployable components
        (J2EE or other) specific for your application
        architecture.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          1.4.1 and above
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.andromda.org/">http://www.AndroMDA.org/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sourceforge.net/mail/?group_id=73047">project mailing lists</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          BSD license
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="AntContrib"></a>
        AntContrib
      </h4>
                        <p>The Ant-Contrib project is a collection of Employee supplied
        task (like an <code>&lt;if&gt;</code> task) and a development
        playground for experimental tasks like a C/C++ compilation
        task for different compilers.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          1.4.1 and above
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://ant-contrib.sourceforge.net/">http://ant-contrib.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sourceforge.net/mail/?group_id=36177">project mailing list</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software Foundation License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Antelope"></a>
        Antelope
      </h4>
                        <p>A GUI for running Ant and editing build files, can run as
        stand-alone or as a plugin to jEdit. In addition to running
        targets, Antelope can generate performance statistics and can
        trace/display a target's execution path without actually
        executing the target.</p>
                                <p>Includes several additional tasks: Assert, If/Else,
        Try/Catch/Finally, Switch, Variable, Stopwatch, Limit, Math,
        Post, SSH, SCP, AntFetch, AntCallBack.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 and higher.
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://antelope.tigris.org/">http://antelope.tigris.org/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Dale Anson</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software Foundation License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="AntHill"></a>
        AntHill
      </h4>
                        <p>Anthill is a build tool that promotes a controlled build
        process by ensuring that every build reflects the source
        repository contents and tagging the repository with a unique
        build number after every build. Anthill also encourages the
        sharing of knowledge within an organization by automatically
        updating a project intranet site with artifacts from the
        latest build.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          bundles Ant 1.3, is compatible with Ant 1.3 to 1.4.1
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.urbancode.com/projects/anthill/">http://www.urbancode.com/projects/anthill/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Maciej Zawadzki</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Mozilla-like license
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Antigen"></a>
        Antigen
      </h4>
                        <p>Antigen (Ant Installer Generator) is a tool to take an Ant build script, combine it with a GUI
        and wrap it up as an executable jar file. Its main use is for creating graphical, ant-based installers.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          bundles Ant 1.6.2
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://antigen.sourceforge.net/">http://antigen.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Jon Tayler</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Academic Free License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="AntInstaller"></a>
        AntInstaller
      </h4>
                        <p>Builds MSI style installers (with command line option)
        using Ant as Back end. UI developed by writing an XML install
        descriptor.  Runtime launched from scripts or an all inclusive
        Jar.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6.1 others not tested
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://antinstaller.sf.net/">http://antinstaller.sf.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          LGPL but in the process of of moving to Apache2.0 on request
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Antlion"></a>
        Antlion
      </h4>
                        <p>The Antlion Project adds value to Ant build scripts by providing
        tasks which centralizes the library dependencies, and enables
        projects to define dependencies upon other projects.</p>
                                <p>External dependencies may be loaded from a custom local
        repository or Maven-like remote repositories. Antlion handles
        the generation of properties, filesets, and paths.</p>
                                <p>Inter-project dependencies allow for building the other
        project's files if they aren't already built.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://antlion.sourceforge.net/">http://antlion.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="https://sourceforge.net/mail/?group_id=93410">Project mailing list</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache License, Version 2.0
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Antworks"></a>
        Antworks
      </h4>
                        <p>The antworks project is a set of tools and standardized targets that
        greatly simplifies using ant in your project.</p>
                                <p>
            The driver behind antworks is Importer. Importer is an extension to the
            ant import task that will download and
            cache an ant build.xml file and it's associated resources called
            antlets.  Antlets are available for Java compiling
            and packaging,  JUnit, Forrest, J2EE and
            <a href="http://antworks.sourceforge.net/antlets/">more</a>.
            </p>
                                <p>
            See the <a href="http://antworks.sourceforge.net/start.html ">Getting Started</a>
            guide for more information.
            </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          1.6 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://antworks.sourceforge.net/">http://antworks.sourceforge.net/index.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://lists.sourceforge.net/lists/listinfo/antworks-developers">Antworks Developers mailing lists</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          The Apache License 2.0
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="BuildMonkey"></a>
        BuildMonkey
      </h4>
                        <p>BuildMonkey is a Web-based automated build dashboard, with upload
           capability and google web search. It schedules the running of Ant
           build scripts - checking sources out of CM - and makes the results
           available centrally.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5.4 or later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.buildmonkey.com/">http://www.buildmonkey.com/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>"><EMAIL></a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Freeware, commercial/support licences available
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="CruiseControl"></a>
        CruiseControl
      </h4>
                        <p>CruiseControl is a tool for setting up a continuous build
        process.  CruiseControl provides an Ant wrapper and a set of
        tasks to automate the checkout/build/test cycle. CruiseControl
        also comes bundled with a servlet for viewing the status of
        the current build, as well as previous build results.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.2 and higher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://cruisecontrol.sourceforge.net/">http://cruisecontrol.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://cruisecontrol.sourceforge.net/contact.html">Project Mailing Lists and Administrators</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Release 1.0 has been licensed under the GNU General Public
            License.  Starting with release 1.1 the license has been
            changed to a BSD-like license.
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Invicta"></a>
        Invicta
      </h4>
                        <p>Invicta is a build management tool. Using simple project definition files,
        it generates powerful build scripts (such as ANT) while hiding their
        complexity. Invicta is a modular framework that allows developing additional
        components and output types.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.5 and higher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://invicta.sf.net/">http://invicta.sf.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://invicta.sf.net/contact.html">Project Mailing Lists and Administrators</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          LGPL.
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="JAM - JavaGen Ant Modules"></a>
        JAM - JavaGen Ant Modules
      </h4>
                        <p>JAM is a modular Ant toolkit for developing and testing Java/J2EE
        applications. JAM supports EJB and Servlet/JSP development using XDoclet,
        JUnit, Cactus, Maven, Castor and MDA/UML code generation on various J2EE
        servers including JBoss.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.javagen.com/jam.jsp">http://www.javagen.com/jam.jsp</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.javagen.com/feedback.do">Feedback</a> <br />
                <a href="http://www.javagen.com/bugs.do">Bug Reports</a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GPL
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Krysalis Centipede"></a>
        Krysalis Centipede
      </h4>
                        <p>The Centipede admin told us, that that project
           "is no more" and that "Antworks has taken it place."
        </p>
                                                          <h4 class="subsection">
        <a name="Leafcutter"></a>
        Leafcutter
      </h4>
                        <p>Leafcutter is an API which allows you to execute Ant tasks from Java code. <br />
           Leafcutter is useful as: <ul>
           <li>A way of integrating Ant tasks into existing Java programs. </li>
           <li>A wholesale alternative to standard Ant for process automation. </li>
           </ul>
        </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <i>unknown</i>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="https://leafcutter.dev.java.net/">https://leafcutter.dev.java.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="https://leafcutter.dev.java.net/servlets/ProjectForumView">Discussion Forum</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache Software Foundation License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="luntbuild"></a>
        luntbuild
      </h4>
                        <p>Luntbuild is an open source build automation and management
        tool based on Apache Ant. Builds are setup through concepts of
        projects, views, schedules, modules, etc. All configurations
        and monitoring tasks is performed from a clean web
        interface. It supports schedules builds, force builds,
        rebuilds, clean build, increment build, etc.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.x
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.sourceforge.net/projects/luntbuild/">http://www.sourceforge.net/projects/luntbuild/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sourceforge.net/projects/luntbuild/">luntbuild project page</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Opensource
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="NAnt"></a>
        NAnt
      </h4>
                        <p>NAnt is a .NET based build tool. In theory it is kind of
        like make without make's wrinkles. In practice it's a lot like
        Ant.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          compatible in spirit.
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://nant.sourceforge.net/">http://nant.sourceforge.net/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sourceforge.net/mail/?group_id=31650">project mailing list</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Parabuild"></a>
        Parabuild
      </h4>
                        <p>Parabuild is an automated multiplatform build management server.
        Parabuild helps software teams and organizations of all sizes reduce
        risks of project failures and increase productivity by providing provides
        automatic continuous integration builds and stable scheduled builds.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.3 and later
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.viewtier.com/products/parabuild.htm">http://www.viewtier.com/products/parabuild.htm</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.viewtier.com/about_us.htm">http://www.viewtier.com/about_us.htm</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Commercial
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Rant"></a>
        Rant
      </h4>
                        <p>Rant stands for Remote Ant. It is a distributed build
        system that allows an Ant build file to launch builds on other
        systems and receive exceptions should they occur.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://sourceforge.net/projects/remoteant/">http://sourceforge.net/projects/remoteant/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Chris Nelson</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          MIT License
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Savant"></a>
        Savant
      </h4>
                        <p>Savant helps simplify builds and codebases by handling the
        resolution of project dependencies automatically. Savant supports
        Maven style dependency downloads and various other methods of
        retrieving dependencies, including fetching files from CVS modules.
        Savant goes a step further than other dependency solutions and provides
        the means for multiple internal projects to build each other in order
        to resolve inter-project dependencies.</p>
                                <p>Savant can be used via various Ant types and tasks as well as used
        from any Java application including those that do not make use of Ant.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6.1
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.inversoft.com/">http://www.inversoft.com/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.inversoft.com/contact.html">http://www.inversoft.com/contact.html</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          LGPL
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="WebTest"></a>
        WebTest
      </h4>
                        <p>WebTest is a free open source tool for automated testing of web applications.
        It is a set of powerful Ant tasks allowing to call web pages, mimic Employee actions
        (clicking links, filling forms, ...) and verify the results.
        The generated reports give comprehensive information on success and failure of the test steps.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.6.5
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://webtest.canoo.com/">http://webtest.canoo.com/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://lists.canoo.com/mailman/listinfo/webtest/">project mailing list</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Apache like license
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="XML Publication"></a>
        XML Publication
      </h4>
                        <p>XML Publication is a set of tools to generate Web pages
        from desktop documents or other structured documents using
        XSLT and Ant.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.4
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://XMLpublication.org/">http://XMLpublication.org/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Jean-Marc Vanel</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          GNU General Public License.
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="yEd"></a>
        yEd
      </h4>
                        <p>yEd is a freeware multi-purpose graph and diagram editor
        that runs on the Java 2 platform. It provides an import filter
        for Ant build scripts that makes it possible to conveniently
        display and browse the dependencies between the different targets
        of the build file. This is especially useful for debugging and
        understanding large build files.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Compatibility:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ant 1.x
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.yworks.com/products/yed/">http://www.yworks.com/products/yed/</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Contact:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.yworks.com/en/company_contact.htm">yWorks Support</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          License:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Freeware
      </td>
      </tr>
          </table>
                                    
    </div>
  </div>

        <p class="copyright">
        <script type="text/javascript" language="JavaScript"><!--
                document.write(" - "+"Last Published: " + document.lastModified);
              //  -->
        </script>
      </p>
    </body>
  </html>











