body {
    background-color: #FFFFFF;
    color: #000000;
    margin: 0px 0px 0px 0px;
    font-family: Verdana, Helvetica, sans-serif;
    font-size : 90%;
}

a:link { color: #0F3660; }
a:visited { color: #009999; }
a:active { color: #000066; }
a:hover { color: #000066; }

.menucontainer {
    float: left;
    background-color: #4C6C8F;
    margin: 0px 5px;
    width: 250px;
}

.menu {
    font-size : 90%;
    padding: 3px 8px 5px 3px;
    border-right: 1px solid #294563;
    border-left: 1px solid #294563;
}

.menu a:link { color: #FFFFFF;  text-decoration : none;  }
.menu a:visited { color: #FFFFFF; text-decoration : none; }
.menu a:hover { color: #FFCC00; text-decoration : none; }
.menu ul { margin: 0px 0px 0px 20px; padding: 0px; }
.menu li  { list-style-image: url('images/label.gif'); font-weight : bold; }
.menu ul ul li .sel { list-style-image: url('images/current.gif'); font-weight : normal; }
.menu ul ul li  { list-style-image: url('images/page.gif'); font-weight : normal; }

.menuheader {
    color: #CFDCED;
}

.sel {
    color: #ffcc00;
}

.tab { font-size : 85%; border: 0; background-color: #294563;}
.tab a:link {   text-decoration : none;  }
.tab a:visited { text-decoration : none; color: #2A4A6D }
.tab a:hover { color: #000066; }

table .title { background-color: #FFFFFF; width:100%; border: 0px; }
.dida { font-size: 80%; }

.pre { white-space: pre;}
.nowrap { white-space: nowrap;}

.main {
    margin-left: 280px;
    margin-right: 5px;
}

.content {
    padding: 5px 5px 5px 10px;
    font : small Verdana, Helvetica, sans-serif;
    font-size : 90%;
}

.content .ForrestTable { width: 100%; background-color: #7099C5; color: #ffffff; font-size : 90%;}
.content .ForrestTable caption { text-align: left; color: black; font-weight: bold; }
.content .ForrestTable th { text-align: center; }
.content .ForrestTable td { background-color: #f0f0ff; color: black; }

.content .externals { width: 80%; background-color: #7099C5; color: #ffffff; font-size : 90%;}
.content .externals caption { text-align: left; color: black; font-weight: bold; }
.content .externals th { width: 120px; text-align: right; }
.content .externals td { background-color: #f0f0ff; color: black; }



.frame { margin: 5px 20px 5px 20px; font-size: 90%; }
.frame .content { margin: 0px; }

.note { border: solid 1px #7099C5; background-color: #f0f0ff; }
.note .label { background-color: #7099C5; color: #ffffff; }

.warning { border: solid 1px #D00000; background-color: #fff0f0; }
.warning .label { background-color: #D00000; color: #ffffff; }

.fixme { border: solid 1px #C6C600; background-color: #FAF9C3; }
.fixme .label { background-color: #C6C600; color: #ffffff; }

.code { border-color: #CFDCED; border-style: solid; border-width: 1px; }
.codefrag {	font-family: "Courier New", Courier, monospace; }

.highlight { background-color: yellow; }

.minitoc {margin: 5px 5px 5px 40px;}

.dtdElement { width: 100%;	font-size: 90%; background-color : #ffffff; }

.dtdTag {    color: #990000; text-transform : uppercase;  font-style : normal;  font-size : 120%;  font-weight : bold; }

.section {
    font-family: Verdana, Helvetica, sans-serif;
    background-color: #294563;
    color: #ffffff;
    font-weight: bold;
    padding: 2px;
    margin-top: 20px;
    clear: right;
}

.subsection {
    font-family: arial,helvetica,sanserif;
    background-color: #4C6C8F;
    color: #ffffff;
    font-weight: bold;
    padding: 2px;
    clear: right;
}

.toc {
    font-family: arial,helvetica,sanserif;
    background-color: #4C6C8F;
    color: #ffffff;
    font-weight: bold;
    padding: 2px;
}

.faq {
    font-family: arial,helvetica,sanserif;
    background-color: #4C6C8F;
    color: #ffffff;
    font-weight: bold;
    padding: 2px;
}

.navpath {
    font-family: arial,helvetica,sanserif;
    background-color: #CFDCED;
    padding: 2px 6px;
    margin: 0px 0px 0px 0px;
    font-size: 90%;
    border-bottom: 2px solid #4C6C8F;
}

.title {
    font-family: Verdana, Helvetica, sans-serif;
}

.copyright {
    font-family: arial,helvetica,sanserif;
    font-size: 90%;
    background-color: #CFDCED;
    clear: both;
    text-align: center;
    margin: 0px;
    border-top: thin solid #4C6C8F;
}

.bluebar {
    padding: 5px 5px 5px 10px;
    background-color: #4C6C8F;
    margin: 0px;
}

.lightbluebar {
    padding: 5px 5px 5px 10px;
    background-color: #CFDCED;
    margin: 0px;
    border-top: 1px solid #294563;
    border-bottom: 1px solid #294563;
}

.logobar {
    background-color: #294563;
    padding-right: 10px;
    margin: 0px;
}

.searchcaption {
    color: #FFFFFF;
    text-align: left;
    font-family: arial,helvetica,sanserif;
    font-size: 90%;
    background-color: #4C6C8F;
    margin: 0px;
}

@media print {
   .menu {
     display: none;
   }
}
