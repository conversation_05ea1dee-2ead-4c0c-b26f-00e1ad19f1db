<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Chgrp Task</title>
</head>

<body>

<h2><a name="Chgrp">Chgrp</a></h2>
<p><em>Since Ant 1.6.</em></p>
<h3>Description</h3>

<p>Changes the group of a file or all files inside specified
directories. Right now it has effect only under Unix.  The group
attribute is equivalent to the corresponding argument for the chgrp
command.</p>

<p><a href="../CoreTypes/fileset.html">FileSet</a>s, 
<a href="../CoreTypes/dirset.html">DirSet</a>s or <a
href="../CoreTypes/filelist.html">FileList</a>s can be specified using
nested <code>&lt;fileset&gt;</code>, <code>&lt;dirset&gt;</code> and 
<code>&lt;filelist&gt;</code> elements.</p>

<p>Starting with Ant 1.7, this task supports arbitrary <a
href="../CoreTypes/resources.html#collection">Resource Collection</a>s
as nested elements.</p>

<p>By default this task will use a single invocation of the underlying
chgrp command.  If you are working on a large number of files this may
result in a command line that is too long for your operating system.
If you encounter such problems, you should set the maxparallel
attribute of this task to a non-zero value.  The number to use highly
depends on the length of your file names (the depth of your directory
tree) and your operating system, so you'll have to experiment a
little.  POSIX recommends command line length limits of at least 4096
characters, this may give you an approximation for the number you
could use as initial value for these experiments.</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">the file or directory of which the group must be
    changed.</td>
    <td valign="top" valign="middle">Yes, unless nested
      <code>&lt;fileset|filelist|dirset&gt;</code>
      elements are specified</td>
  </tr>
  <tr>
    <td valign="top">group</td>
    <td valign="top">the new group.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">parallel</td>
    <td valign="top">process all specified files using a single
      <code>chgrp</code> command. Defaults to true.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">type</td>
    <td valign="top">One of <i>file</i>, <i>dir</i> or
      <i>both</i>. If set to <i>file</i>, only the group of
      plain files are going to be changed. If set to <i>dir</i>, only
      the directories are considered.<br>
      <strong>Note:</strong> The type attribute does not apply to
      nested <i>dirset</i>s - <i>dirset</i>s always implicitly
      assume type to be <i>dir</i>.</td>
    <td align="center" valign="top">No, default is <i>file</i></td>
  </tr>
  <tr>
    <td valign="top">maxparallel</td>
    <td valign="top">Limit the amount of parallelism by passing at
      most this many sourcefiles at once.  Set it to &lt;= 0 for
      unlimited.  Defaults to unlimited.</td>
    <td align="center" valign="top">No</td>

  </tr>
  <tr>
    <td valign="top">verbose</td>
    <td valign="top">Whether to print a summary after execution or not.
      Defaults to <code>false</code>.</td>
    <td align="center" valign="top">No</td>
  </tr>

</table>
<h3>Examples</h3>
  <blockquote><pre>
&lt;chgrp file=&quot;${dist}/start.sh&quot; group=&quot;coders&quot;/&gt;
</pre>
</blockquote>
<p>makes the &quot;start.sh&quot; file belong to the coders group on a
UNIX system.</p>
<blockquote>
<pre>
&lt;chgrp group=&quot;coders&quot;&gt;
  &lt;fileset dir=&quot;${dist}/bin&quot; includes=&quot;**/*.sh&quot;/&gt;
&lt;/chgrp&gt;
</pre>
</blockquote>
<p>makes all &quot;.sh&quot; files below <code>${dist}/bin</code>
belong to the coders group on a UNIX system.</p>
<blockquote>
<pre>
&lt;chgrp group=&quot;coders&quot;&gt;
  &lt;fileset dir=&quot;shared/sources1&quot;&gt;
    &lt;exclude name=&quot;**/trial/**&quot;/&gt;
  &lt;/fileset&gt;
  &lt;fileset refid=&quot;other.shared.sources&quot;/&gt;
&lt;/chgrp&gt;
</pre>
</blockquote>
<p>makes all files below <code>shared/sources1</code> (except those
below any directory named trial) belong to the coders group on a UNIX 
system. In addition all files belonging to a FileSet
with <code>id</code> <code>other.shared.sources</code> get the same
group.</p>

<blockquote>
<pre>
&lt;chgrp group=&quot;webdev&quot; type=&quot;file&quot;&gt;
  &lt;fileset dir=&quot;/web&quot;&gt;
    &lt;include name=&quot;**/*.test.jsp&quot;/&gt;
    &lt;include name=&quot;**/*.new&quot;/&gt;
  &lt;/fileset&gt;
  &lt;dirset dir=&quot;/web&quot;&gt;
    &lt;include name=&quot;**/test_*&quot;/&gt;
  &lt;/dirset&gt;
&lt;/chmod&gt;
</pre>
</blockquote>

<p>makes all <code>.test.jsp</code>, and <code>.new</code> files belong to 
group webdev. Directories beginning with <code>test_</code> also will belong 
to webdev, but if there is a directory that ends in <code>.new</code> or a file
that begins with <code>test_</code> it will be unaffected.</p>




</body>
</html>

