<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Image Task</title>
</head>

<body>

<h2><a name="image">Image</a></h2>
<h3>Description</h3>
<p>Applies a chain of image operations on a set of files.</p>
<p>Requires Java Advanced Image API from Sun.</p>

<h5>Overview of used datatypes</h5>
<img src="image-classdiagram.gif" border="0" alt="Class-Diagram">

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top"> failonerror </td>
    <td valign="top"> Boolean value. If false, note errors to the output but keep going. </td>
    <td align="center"> no (defaults to <i>true</i>) </td>
  </tr>
  <tr>
    <td valign="top"> srcdir </td>
    <td valign="top"> Directory containing the images. </td>
    <td align="center"> yes, unless nested fileset is used </td>
  </tr>
  <tr>
    <td valign="top"> encoding </td>
    <td valign="top"> Image encoding type. <br>
      Valid (caseinsensitive) are: jpg, jpeg, tif, tiff
    </td>
    <td align="center"> no (defaults to <i>JPEG</i>) </td>
  </tr>
  <tr>
    <td valign="top"> overwrite </td>
    <td valign="top"> Boolean value. Sets whether or not to overwrite
      a file if there is naming conflict.
    </td>
    <td align="center"> no (defaults to <i>false</i>) </td>
  </tr>
  <tr>
    <td valign="top"> gc </td>
    <td valign="top"> Boolean value. Enables garbage collection after
      each image processed.
    </td>
    <td align="center"> no (defaults to <i>false</i>) </td>
  </tr>
  <tr>
    <td valign="top"> destdir </td>
    <td valign="top"> Directory where the result images are stored. </td>
    <td align="center"> no (defaults to value of <i>srcdir</i>) </td>
  </tr>
  <!-- attributes inherited from MatchingTask -->
  <tr>
    <td valign="top">includes</td>
    <td valign="top">comma- or space-separated list of patterns of files that must be
      included. All files are included when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">includesfile</td>
    <td valign="top">the name of a file. Each line of this file is
      taken to be an include pattern</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top"> excludes</td>
    <td valign="top">comma- or space-separated list of patterns of files that must be
      excluded. No files (except default excludes) are excluded when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">excludesfile</td>
    <td valign="top">the name of a file. Each line of this file is
      taken to be an exclude pattern</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">defaultexcludes</td>
    <td valign="top">indicates whether default excludes should be used or not
      (&quot;yes&quot;/&quot;no&quot;). Default excludes are used when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top"> caseSensitive </td>
    <td valign="top"> Boolean value. Sets case sensitivity of the file system. </td>
    <td align="center"> no (defaults to <i>false</i>) </td>
  </tr>
  <tr>
    <td valign="top"> followSymlinks </td>
    <td valign="top"> Boolean value. Sets whether or not symbolic links should be followed. </td>
    <td align="center"> no (defaults to <i>true</i>) </td>
  </tr>
</table>

<h3>Parameters specified as nested elements</h3>
<p>This task forms an implicit <a href="../CoreTypes/fileset.html">FileSet</a> and
supports all attributes of <code>&lt;fileset&gt;</code> as well as the
nested <code>&lt;include&gt;</code>, <code>&lt;exclude&gt;</code> and
<code>&lt;patternset&gt;</code> elements.</p>


<h4>ImageOperation</h4>
<p>Adds an ImageOperation to chain.</p>
<h5>Nested Elements</h5>
ImageOperation can handle nested Rotate, Draw, Rectangle, Text and Scale objects.

<h4>Rotate</h4>
<p>Adds a Rotate ImageOperation to chain.</p>
<h5>Parameters</h5>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top"> angle </td>
    <td valign="top"> Float value. Sets the angle of rotation in degrees. </td>
    <td align="center"> no (defaults to <i>0.0F</i>) </td>
  </tr>
</table>

<h4>Scale</h4>
<p>Adds a Scale ImageOperation to chain.</p>
<h5>Parameters</h5>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
    <td valign="top"> proportions </td>
    <td valign="top"> Sets which dimension to control proportions from. Valid values are:<ul>
        <li>&quot;ignore&quot; -  treat the dimensions independently.</li>
        <li>&quot;height&quot; - keep proportions based on the width.</li>
        <li>&quot;width&quot; - keep proportions based on the height.</li>
        <li>&quot;cover&quot; - keep proportions and fit in the supplied dimensions.</li>
        <li>&quot;fit&quot; - keep proportions and cover the supplied dimensions.</li>
    </ul></td>
    <td align="center"> no (defaults to <i>ignore</i>) </td>
  <tr>
    <td valign="top"> width </td>
    <td valign="top"> Sets the width of the image, either as an integer or a %. </td>
        <!-- todo: if integer, what kind? cm, px, inches, ... -->
    <td align="center"> no (defaults to <i>100%</i>) </td>
  </tr>
  <tr>
    <td valign="top"> height </td>
    <td valign="top"> Sets the height of the image, either as an integer or a %. </td>
        <!-- todo: if integer, what kind? cm, px, inches, ... -->
    <td align="center"> no (defaults to <i>100%</i>) </td>
  </tr>
</table>

<h4>Draw</h4>
<p>Adds a Draw ImageOperation to chain. DrawOperation DataType objects can be
nested inside the Draw object.</p>
<h5>Parameters</h5>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top"> xloc </td>
    <td valign="top"> X-Position where to draw nested image elements. </td>
    <td align="center"> no (defaults to <i>0</i>) </td>
  </tr>
  <tr>
    <td valign="top"> yloc </td>
    <td valign="top"> Y-Position where to draw nested image elements. </td>
    <td align="center"> no (defaults to <i>0</i>) </td>
  </tr>
</table>

<h3>Examples</h3>

<blockquote><pre>
&nbsp;&lt;image destdir="samples/low" overwrite="yes"&gt;
&nbsp;&nbsp;&nbsp;&nbsp; &lt;fileset dir="samples/full"&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &lt;include name="**/*.jpg"/&gt;
&nbsp;&nbsp;&nbsp;&nbsp; &lt;/fileset&gt;
&nbsp;&nbsp;&nbsp;&nbsp; &lt;scale width="160" height="160" proportions="fit"/&gt;
&nbsp;&lt;/image&gt;
</pre></blockquote>
<p>Create thumbnails of my images and make sure they all fit within the 160x160 size whether the
image is portrait or landscape.</p>

<blockquote><pre>
&lt;image srcdir="src" includes="*.png"&gt;
    &lt;scale proportions="width" width="40"/&gt;
&lt;/image&gt;
</pre></blockquote>
<p>Creates a thumbnail for all PNG-files in <i>src</i> in the size of 40 pixel keeping the proportions
and stores the <i>src</i>.</p>

<blockquote><pre>
&lt;image srcdir="src" destdir="dest" includes="*.png"&gt;
    &lt;scale proportions="width" width="40"/&gt;
&lt;/image&gt;
</pre></blockquote>
<p>Same as above but stores the result in <i>dest</i>.</p>

<blockquote><pre>
</pre></blockquote>



</body>
</html>


