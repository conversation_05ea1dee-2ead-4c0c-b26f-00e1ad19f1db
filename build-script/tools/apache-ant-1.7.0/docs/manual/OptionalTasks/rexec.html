<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>RExec Task</title>
</head>

<body>

<h2><a name="rexec">RExec</a></h2>
<h3>Description</h3>
Task to automate a remote rexec session. Just like the Telnet task,
it uses nested <tt>&lt;read&gt;</tt> to indicate strings to wait for, and
<tt>&lt;write&gt;</tt> tags to specify text to send to the remote process.

<p><b>Note:</b> This task depends on external libraries not included in the Ant distribution.
See <a href="../install.html#librarydependencies">Library Dependencies</a> for more information.</p>

<p>You can specify the commands you want to execute as nested elements
or via the command attribute, we recommend you use the command
attribute.  If you use the command attribute, you must use the
username and password attributes as well.</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <th>Attribute</th>
    <th>Values</th>
    <th>Required</th>
  </tr>
  <tr>
     <td>userid</td>
     <td>the login id to use on the remote server.</td>
     <td>No</td>
  </tr>
  <tr>
     <td>password</td>
     <td>the login password to use on the remote server.</td>
     <td>No</td>
  </tr>
  <tr>
     <td>server</td>
     <td>the address of the remote rexec server.</td>
     <td>Yes</td>
  </tr>
  <tr>
     <td>command</td>
     <td>the command to execute on the remote server.</td>
     <td>No</td>
  </tr>
  <tr>
     <td>port</td>
     <td>the port number of the remote rexec server. Defaults to port 512 in BSD Unix systems.</td>
     <td>No</td>
  </tr>
  <tr>
     <td>timeout</td>
     <td>set a default timeout to wait for a response. Specified in seconds. Default is no timeout.</td>
     <td>No</td>
  </tr>
</table>
<h3><a name="nested">Nested Elements</a></h3>
The input to send to the server, and responses to wait for, are
described as nested elements.  

<h4>read</h4>

<p>declare (as a text child of this element) a string to wait for.
The element supports the timeout attribute, which overrides any
timeout specified for the task as a whole. It also has a <tt>string</tt>
attribute, which is an alternative to specifying the string as 
a text element.
</p>
<i>It is not necessary to declare a closing <code>&lt;read&gt;</code> element like for the Telnet task. The connection is not broken until the command has completed and
the input stream (output of the command) is terminated.
</i>
<h4>write</h4>

<p>describes the text to send to the server. The <tt>echo</tt> boolean
attribute controls whether the string is echoed to the local log; 
this is "true" by default
</p>
<h3>Example</h3>
A simple example of connecting to a server and running a command.

<blockquote><pre>
&lt;rexec userid=&quot;bob&quot; password=&quot;badpass&quot; server=&quot;localhost&quot; command=&quot;ls&quot;/&gt;
</pre></blockquote>

The task can be used with other ports as well:
<blockquote><pre>
&lt;rexec port=&quot;80&quot; userid=&quot;bob&quot; password=&quot;badpass&quot; server=&quot;localhost&quot; command=&quot;ls&quot;/&gt;
</pre></blockquote>



</body>
</html>

