<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>ReplaceRegExp Task</title>
</head>
<body>

<h2><a name="replaceregexp">ReplaceRegExp</a></h2>
<h3>Description</h3>
<p>ReplaceRegExp is a directory based task for replacing the
occurrence of a given regular expression with a substitution pattern
in a selected file or set of files.</p>

<p>The output file is only written if it differs from the existing
file.  This prevents spurious rebuilds based on unchanged files which
have been regenerated by this task.</p> 

<p>Similar to <a href="../CoreTypes/mapper.html#regexp-mapper">regexp
type mappers</a> this task needs a supporting regular expression
library and an implementation of
<code>org.apache.tools.ant.util.regexp.Regexp</code>.
See details in the documentation of the <a href="../CoreTypes/regexp.html#implementation">Regexp Type</a>. </p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">file for which the regular expression should be replaced.</td>
    <td align="center">Yes if no nested <code>&lt;fileset&gt;</code> is used</td>
  </tr>
  <tr>
    <td valign="top">match</td>
    <td valign="top">The regular expression pattern to match in the file(s)</td>
    <td align="center">Yes, if no nested <code>&lt;regexp&gt;</code> is used</td>
  </tr>
  <tr>
    <td valign="top">replace</td>
    <td valign="top">The substitution pattern to place in the file(s) in place
                     of the regular expression.</td>
    <td align="center">Yes, if no nested <code>&lt;substitution&gt;</code> is used</td>
  </tr>
  <tr>
    <td valign="top">flags</td>
    <td valign="top">The flags to use when matching the regular expression.  For more
                     information, consult the Perl5 syntax<br>
                     g : Global replacement.  Replace all occurrences found<br>
                     i : Case Insensitive.  Do not consider case in the match<br>
                     m : Multiline.  Treat the string as multiple lines of input, using "^" and "$" as the start or end of any line, respectively, rather than start or end of string.<br>
                     s : Singleline.  Treat the string as a single line of input, using "." to match any character, including a newline, which normally, it would not match.<br>
    </td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">byline</td>
    <td valign="top">Process the file(s) one line at a time, executing the replacement
                     on one line at a time (<i>true/false</i>).  This is useful if you
                     want to only replace the first occurrence of a regular expression on
                     each line, which is not easy to do when processing the file as a whole.
                     Defaults to <i>false</i>.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">encoding</td>
    <td valign="top">The encoding of the file. <em>since Ant 1.6</em></td>
    <td align="center">No - defaults to default JVM encoding</td>
  </tr>
</table>
<h3>Examples</h3>
<pre>  &lt;replaceregexp file=&quot;${src}/build.properties&quot;
                         match=&quot;OldProperty=(.*)&quot;
                         replace=&quot;NewProperty=\1&quot;
                         byline=&quot;true&quot;/&gt;
</pre>
<p>replaces occurrences of the property name &quot;OldProperty&quot;
 with &quot;NewProperty&quot; in a properties file, preserving the existing
value, in the file <code>${src}/build.properties</code></p>

<h3>Parameters specified as nested elements</h3>
<p>This task supports a nested <a href="../CoreTypes/fileset.html">FileSet</a>
   element.</p>
<p>This task supports a nested <i><a href="../CoreTypes/regexp.html">Regexp</a></i> element to specify
   the regular expression.  You can use this element to refer to a previously
   defined regular expression datatype instance.</p>
<blockquote>
     &lt;regexp id="id" pattern="alpha(.+)beta"/&gt;<br>
     &lt;regexp refid="id"/&gt;
</blockquote>
<p>This task supports a nested <i>Substitution</i> element to specify
   the substitution pattern.  You can use this element to refer to a previously
   defined substitution pattern datatype instance.</p>
<blockquote>
     &lt;substitution id="id" expression="beta\1alpha"/&gt;<br>
     &lt;substitution refid="id"/&gt;
</blockquote>
<h3>Examples</h3>
<blockquote>
  <pre>
&lt;replaceregexp byline=&quot;true&quot;&gt;
  &lt;regexp pattern=&quot;OldProperty=(.*)&quot;/&gt;
  &lt;substitution expression=&quot;NewProperty=\1&quot;/&gt;
  &lt;fileset dir=&quot;.&quot;&gt;
   &lt;includes=&quot;*.properties&quot;/&gt;
  &lt;/fileset&gt;
 &lt;/replaceregexp&gt;
</pre></blockquote>
<p>replaces occurrences of the property name &quot;OldProperty&quot;
 with &quot;NewProperty&quot; in a properties file, preserving the existing
value, in all files ending in <code>.properties</code> in the current directory</p>

<blockquote>
<pre>&lt;replaceregexp match="\s+" replace=" " flags="g" byline="true"&gt;
    &lt;fileset dir="${html.dir}" includes="**/*.html"/&gt;
&lt;/replaceregexp&gt;
</pre></blockquote>
<p>replaces all whitespaces (blanks, tabs, etc) by one blank remaining the
line separator. So with input

<blockquote>
<pre>
&lt;html&gt;    &lt;body&gt;
&lt;&lt;TAB&gt;&gt;&lt;h1&gt;    T E S T   &lt;/h1&gt;  &lt;&lt;TAB&gt;&gt;    
&lt;&lt;TAB&gt;&gt; &lt;/body&gt;&lt;/html&gt;
</pre></blockquote>
would converted to
<pre>
&lt;html&gt; &lt;body&gt;
 &lt;h1&gt; T E S T &lt;/h1&gt; &lt;/body&gt;&lt;/html&gt;
</pre>
</p>


</body>
</html>

