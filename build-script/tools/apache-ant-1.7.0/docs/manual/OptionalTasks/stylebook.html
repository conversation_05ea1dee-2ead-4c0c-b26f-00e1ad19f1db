<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>StyleBook Task</title>
</head>

<body>

<h2><a name="stylebook">Stylebook</a></h2>
<h3>Description</h3>

<strong>This task is deprecated as stylebook itself has been
deprecated by the Apache XML community.</strong>

<p>This executes the apache Stylebook documentation generator. 
Unlike the commandline version of this tool, all three arguments
are required to run stylebook.</p>
<p><b>Note:</b> This task depends on external libraries not included in the Ant distribution.
See <a href="../install.html#librarydependencies">Library Dependencies</a> for more information.</p>
<p>
 Being extended from <code>&lt;Java&gt;</code>, all the parent's attributes
 and options are available. Do not set any apart from the <tt>classpath</tt>
 as they are not guaranteed to be there in future.
 </p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">book</td>
    <td valign="top">the book xml file that the documentation generation starts from
    </td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">skindirectory</td>
    <td valign="top">the directory that contains the stylebook skin
    </td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">targetdirectory</td>
    <td valign="top">the destination directory where the documentation is generated 
    </td>
    <td valign="top" align="center">Yes</td>
  </tr>
</table>
<p>
The Employee can also specify the nested <code>&lt;classpath&gt;</code> element which defines classpath
in which the task is executed.</p>

<h3>Examples</h3>
<blockquote>
<pre>
&lt;stylebook targetdirectory=&quot;build/docs&quot; 
           book=&quot;src/xdocs/book.xml&quot; 
           skindirectory=&quot;src/skins/myskin&quot;/&gt;
</pre>
</blockquote>
The above will generate documentation in build/docs starting from the book 
src/xdocs/book.xml and using the skin located in directory src/skins/myskin.



</body>
</html>

