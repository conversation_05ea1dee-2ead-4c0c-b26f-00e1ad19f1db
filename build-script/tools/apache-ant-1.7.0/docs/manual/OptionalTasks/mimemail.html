<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>MimeMail Task</title>
</head>

<body>

<h2><a name="mimemail">MimeMail</a></h2>

<h3><i>Deprecated</i></h3>
<p><i>This task has been deprecated.  Use the <a href="../CoreTasks/mail.html">mail</a> task instead.</i></p>

<h3>Description</h3>
<p>Sends SMTP mail with MIME attachments.
<a href="http://java.sun.com/products/javamail/index.html">JavaMail</a>
and <a href="http://java.sun.com/products/javabeans/glasgow/jaf.html">Java
Activation Framework</a> are required for this task.</p>
<p>Multiple files can be attached using <a href="../CoreTypes/fileset.html">FileSets.</a></p>
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">message</td>
    <td valign="top">The message body</td>
    <td valign="top" align="center" rowspan="2">No, but only one of of 'message' or
      'messageFile' may be specified.&nbsp; If not specified, a fileset must be
      provided.</td>
  </tr>
  <tr>
    <td valign="top">messageFile</td>
    <td valign="top">A filename to read and used as the message body</td>
  </tr>
  <tr>
    <td valign="top">messageMimeType</td>
    <td valign="top">MIME type to use for 'message' or 'messageFile' when
      attached.</td>
    <td align="center" valign="top">No, defaults to "text/plain"</td>
  </tr>
  <tr>
    <td valign="top">tolist</td>
    <td valign="top">Comma-separated list of To: recipients</td>
    <td valign="top" align="center" rowspan="3">Yes, at least one of 'tolist', 'cclist',
      or 'bcclist' must be specified.</td>
  </tr>
  <tr>
    <td valign="top">cclist</td>
    <td valign="top">Comma-separated list of CC: recipients</td>
    <td valign="top" align="center">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top">bcclist</td>
    <td valign="top">Comma-separated list of BCC: recipients</td>
    <td valign="top" align="center">&nbsp;</td>
  </tr>
  <tr>
    <td valign="top">mailhost</td>
    <td valign="top">Host name of the mail server.</td>
    <td valign="top" align="center">No, default to &quot;localhost&quot;</td>
  </tr>
  <tr>
    <td valign="top">subject</td>
    <td valign="top">Email subject line.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">from</td>
    <td valign="top">Email address of sender.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">failonerror</td>
    <td valign="top">Stop the build process if an error occurs sending the
      e-mail.</td>
    <td valign="top" align="center">No, default to &quot;true&quot;</td>
  </tr>
</table>
<h3>Examples</h3>
<p><b>Send a single HTML file as the body of a message</b></p>
<pre>    &lt;mimemail messageMimeType=&quot;text/html&quot; messageFile=&quot;overview-summary.html&quot;
        tolist=&quot;you&quot; subject=&quot;JUnit Test Results: December 13 2006&quot; from=&quot;me&quot;/&gt;</pre>
<p><b>Sends all files in a directory as attachments</b></p>
<pre>    &lt;mimemail message=&quot;See attached files&quot; tolist=&quot;you&quot; subject=&quot;Attachments&quot; from=&quot;me&quot;&gt;
        &lt;fileset dir=&quot;.&quot;&gt;
            &lt;include name=&quot;dist/*.*&quot;/&gt;
        &lt;/fileset&gt;
    &lt;/mimemail&gt;
</pre>


</body>
</html>

