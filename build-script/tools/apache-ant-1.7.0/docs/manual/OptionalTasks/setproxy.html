<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
    
<html>
<head>
  <meta http-equiv="Content-Language" content="en-us">
  <title>Setproxy
 Task</title>
</head>

<body bgcolor="#ffffff" text="#000000" link="#525D76"
      alink="#525D76" vlink="#525D76">

<table border="0" width="100%" cellspacing="4">

  <!-- <PERSON>GE HEADER -->
  <tr>
    <td>
      <table border="0" width="100%"><tr>
          <td valign="bottom">
            <font size="+3" face="arial,helvetica,sanserif"><strong>Setproxy
 Task</strong></font>
            <br><font face="arial,helvetica,sanserif">Sets Java's web proxy properties, so that tasks and code run in the same JVM can have through-the-firewall access to remote web sites, and remote ftp sites.</font>
          </td>
          <td>
            <!-- PROJECT LOGO -->
            <a href="http://ant.apache.org/">
              <img src="../../images/ant_logo_large.gif" align="right" alt="Apache Ant" border="0">
            </a>
          </td>
      </tr></table>
    </td>
  </tr>

  <!-- START RIGHT SIDE MAIN BODY -->
  <tr>
    <td  valign="top" align="left">

          <!-- Applying task/long-description -->
    <!-- Start Description -->
    <table border="0" cellspacing="0" cellpadding="2" width="100%">
      <tr><td>&nbsp;</td></tr>

      <tr><td bgcolor="#525D76">
        <font color="#ffffff" face="arial,helvetica.sanserif">
          <a name="description">
          <strong>Description</strong></a></font>
      </td></tr>

      <tr><td><blockquote>
        Sets Java's web proxy properties, so that tasks and code run in the same JVM can have through-the-firewall access to remote web sites, and remote ftp sites. You can nominate an http and ftp proxy, or a socks server, reset the server settings, or do nothing at all. <p> Examples <pre>&lt;setproxy/&gt;</pre> do nothing <pre>&lt;setproxy proxyhost="firewall"/&gt;</pre> set the proxy to firewall:80 <pre>&lt;setproxy proxyhost="firewall" proxyport="81"/&gt;</pre> set the proxy to firewall:81 <pre>&lt;setproxy proxyhost=""/&gt;</pre> stop using the http proxy; don't change the socks settings <pre>&lt;setproxy socksproxyhost="socksy"/&gt;</pre> use socks via socksy:1080 <pre>&lt;setproxy socksproxyhost=""/&gt;</pre> stop using the socks server. <p> You can set a username and password for http with the <tt>proxyHost</tt> and <tt>proxyPassword</tt> attributes. On Java1.4 and above these can also be used against SOCKS5 servers. </p>
      </blockquote></td></tr>

    </table>
    <!-- End Description -->

 <!-- Ignore -->



    <!-- Start Attributes -->
    <table border="0" cellspacing="0" cellpadding="2" width="100%">
      <tr><td>&nbsp;</td></tr>
      <tr><td bgcolor="#525D76">
        <font color="#ffffff" face="arial,helvetica.sanserif">
          <a name="attributes">
          <strong>Parameters</strong></a></font>
      </td></tr>
      <tr><td><blockquote>
        <table>
          <tr>
        <td bgcolor="#cccccc" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif"><b>Attribute</b></font>
        </td>
        <td bgcolor="#cccccc" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif"><b>Description</b></font>
        </td>
        <td bgcolor="#cccccc" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif"><b>Type</b></font>
        </td>
        <td bgcolor="#cccccc" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif"><b>Requirement</b></font>
        </td>
          </tr>
    <!-- Attribute Group -->    
    
    <!-- Attribute Group -->    
        <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">nonproxyhosts</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">A list of hosts to bypass the proxy on. These should be separated with the vertical bar character '|'. Only in Java 1.4 does ftp use this list. e.g. fozbot.corp.sun.com|*.eng.sun.com</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">String</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left" rowspan="7">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Optional</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">proxyhost</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">the HTTP/ftp proxy host. Set this to "" for the http proxy option to be disabled</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">String</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">proxypassword</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Set the password for the proxy. Used only if the proxyUser is set.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">String</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">proxyport</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">the HTTP/ftp proxy port number; default is 80</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">int</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">proxyuser</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">set the proxy Employee. Probably requires a password to accompany this setting. Default=""</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">String</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">socksproxyhost</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">The name of a Socks server. Set to "" to turn socks proxying off.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">String</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">socksproxyport</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Set the ProxyPort for socks connections. The default value is 1080</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">int</font>
        </td>
    </tr>


        </table>
      </blockquote></td></tr>

    </table>
    <!-- End Attributes -->

    <!-- Start Elements -->
    <table border="0" cellspacing="0" cellpadding="2" width="100%">
      <tr><td>&nbsp;</td></tr>

      <tr><td bgcolor="#525D76">
        <font color="#ffffff" face="arial,helvetica.sanserif">
          <a name="elements">
          <strong>Parameters as nested elements</strong></a></font>
      </td></tr>

      <tr><td><blockquote>

      </blockquote></td></tr>

    </table>
    <!-- End Elements -->


    </td>
  </tr>
  <!-- END RIGHT SIDE MAIN BODY -->

</table>

</body>
</html>
