<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>
  <head><link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Native2Ascii Task</title></head>
  <body>
    <h2>Native2Ascii</h2>

    <h3>Description:</h3>

    <p>
      Converts files from native encodings to ASCII with escaped Unicode.
      A common usage is to convert source files maintained in a native
      operating system encoding, to ASCII prior to compilation.
    </p>

    <p>
      Files in the directory <em>src</em>
      are converted from a native encoding to ASCII.
      By default, all files in the directory are converted.
      However, conversion may be limited to selected files using
      <em>includes</em> and <em>excludes</em> attributes.
      For more information on file matching patterns,
      see the section on
      <a href="../dirtasks.html#directorybasedtasks">directory based tasks</a>.
      If no <em>encoding</em> is specified,
      the default encoding for the JVM is used.
      If <em>ext</em> is specified, then output files are renamed
      to use it as a new extension.
      More sophisticated file name translations can be achieved using a nested
      <em><code>&lt;mapper&gt;</code></em> element. By default an
      <a href="../CoreTypes/mapper.html#identity-mapper">identity mapper</a> will be used.
      If <em>dest</em> and <em>src</em> point to the same directory,
      the <em>ext</em> attribute or a nested <em><code>&lt;mapper&gt;</code></em>
      is required.
    </p>

    <p>
      This task forms an implicit <a href="../CoreTypes/fileset.html">File Set</a>,
      and supports all attributes of <code>&lt;fileset&gt;</code>
      (<code>dir</code> becomes <code>src</code>) as well as
      nested <code>&lt;include&gt;</code>, <code>&lt;exclude&gt;</code>,
      and <code>&lt;patternset&gt;</code> elements.
    </p>

    <p>It is possible to use different converters. This can be selected
      with the <code>implementation</code> attribute.
      <a name="implementationvalues">Here are the choices</a>:</p>
    <ul>
      <li>default - the default converter (kaffe or sun) for the platform.</li>
      <li>sun (the standard converter of the JDK)</li>
      <li>kaffe (the standard converter of <a href="http://www.kaffe.org" target="_top">Kaffe</a>)</li>
    </ul>

    <table border="1" cellpadding="2" cellspacing="0">
      <tr>
        <td><b>Attribute</b></td>
        <td><b>Description</b></td>
        <td><b>Required</b></td>
      </tr>
      <tr>
        <td>reverse</td>
        <td>Reverse the sense of the conversion,
          i.e. convert from ASCII to native <b>only supported by the
            sun converter</b></td>
        <td align="center">No</td>
      </tr>
      <tr>
        <td>encoding</td>
        <td>The native encoding the files are in
          (default is the default encoding for the JVM)</td>
        <td align="center">No</td>
      </tr>
      <tr>
        <td>src</td>
        <td>The directory to find files in (default is <em>basedir</em>)</td>
        <td align="center">No</td>
      </tr>
      <tr>
        <td>dest</td>
        <td>The directory to output file to</td>
        <td align="center">Yes</td>
      </tr>
      <tr>
        <td>ext</td>
        <td>File extension to use in renaming output files</td>
        <td align="center">No</td>
      </tr>
      <tr>
        <td>defaultexcludes</td>
        <td>indicates whether default excludes should be used or not
          (&quot;yes&quot;/&quot;no&quot;).
          Default excludes are used when omitted.
        </td>
        <td align="center">No</td>
      </tr>
      <tr>
        <td>includes</td>
        <td>comma- or space-separated list of patterns of files that must be
          included. All files are included when omitted.</td>
        <td align="center">No</td>
      </tr>
      <tr>
        <td>includesfile</td>
        <td>the name of a file. Each line of this file is
          taken to be an include pattern</td>
        <td align="center">No</td>
      </tr>
      <tr>
        <td>excludes</td>
        <td>comma- or space-separated list of patterns of files that must be excluded.
          No files (except default excludes) are excluded when omitted.</td>
        <td align="center">No</td>
      </tr>
      <tr>
        <td>excludesfile</td>
        <td>the name of a file. Each line of this file is
          taken to be an exclude pattern</td>
        <td align="center">No</td>
      </tr>
      <tr>
        <td valign="top">implementation</td>
        <td valign="top">The converter implementation to use.
          If this attribute is not set, the default converter for the
          current VM will be used.  (See the above <a
          href="#implementationvalues">list</a> of valid converters.)</td>
        <td align="center" valign="top">No</td>
      </tr>
    </table>

<h3>Parameters specified as nested elements</h3>

<h4>arg</h4>

<p>You can specify additional command line arguments for the converter
with nested <code>&lt;arg&gt;</code> elements.  These elements are
specified like <a href="../using.html#arg">Command-line Arguments</a>
but have an additional attribute that can be used to enable arguments
only if a given converter implementation will be used.</p>

<table border="1" cellpadding="2" cellspacing="0">
<tr>
  <td width="12%" valign="top"><b>Attribute</b></td>
  <td width="78%" valign="top"><b>Description</b></td>
  <td width="10%" valign="top"><b>Required</b></td>
</tr>
  <tr>
    <td valign="top">value</td>
    <td align="center" rowspan="4">See
    <a href="../using.html#arg">Command-line Arguments</a>.</td>
    <td align="center" rowspan="4">Exactly one of these.</td>
  </tr>
  <tr>
    <td valign="top">line</td>
  </tr>
  <tr>
    <td valign="top">file</td>
  </tr>
  <tr>
    <td valign="top">path</td>
  </tr>
  <tr>
    <td valign="top">implementation</td>
    <td>Only pass the specified argument if the chosen converter
    implementation matches the value of this attribute.  Legal values
    are the same as those in the above <a
    href="#implementationvalues">list</a> of valid compilers.)</td>
    <td align="center">No</td>
  </tr>
</table>

    <h3>Examples</h3>

    <pre>
&lt;native2ascii encoding=&quot;EUCJIS&quot; src=&quot;srcdir&quot; dest=&quot;srcdir&quot;
   includes=&quot;**/*.eucjis&quot; ext=&quot;.java&quot;/&gt;
    </pre>

    <p>
      Converts all files in the directory <em>srcdir</em>
      ending in <code>.eucjis</code> from the EUCJIS encoding to ASCII
      and renames them to end in <code>.java</code>.
    </p>

<pre>
&lt;native2ascii encoding=&quot;EUCJIS&quot; src=&quot;native/japanese&quot; dest=&quot;src&quot;
   includes=&quot;**/*.java&quot;/&gt;
</pre>

    <p>
      Converts all the files ending in <code>.java</code>
      in the directory <em>native/japanese</em> to ASCII,
      placing the results in the directory <em>src</em>.
      The names of the files remain the same.
    </p>
  </body>

</html>
