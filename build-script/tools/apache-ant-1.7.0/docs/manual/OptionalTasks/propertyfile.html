<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>
<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>PropertyFile Task</title>
</head>

<body>

<h1>PropertyFile</h1>
<p>by</p>
<!-- Names are in alphabetical order, on last name -->
<ul>
  <li><PERSON> (<a href="mailto:<EMAIL>"><EMAIL></a>)</li>
  <li><PERSON> (<a href="mailto:<EMAIL>"><EMAIL>/au</a>)</li>
</ul>

<hr>
<h2>Table of Contents</h2>
<ul>
  <li><a href="#introduction">Introduction</a></li>
  <li><a href="#proptask">PropertyFile Task</a></li>
  <li><a href="#entryElement">Entry Task</a></li>
</ul>

<hr>
<h2><a name="introduction">Introduction</a></h2>
<p>Ant provides an optional task for editing property files.  This is very useful
when wanting to make unattended modifications to configuration files for application
servers and applications.  Currently, the task maintains a working property file with
the ability to add properties or make changes to existing ones.  However, any comments
are lost.</p>

<hr>
<h2><a name="proptask">PropertyFile Task</a></h2>
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
<tr>
  <td width="12%" valign="top"><b>Attribute</b></td>
  <td width="78%" valign="top"><b>Description</b></td>
  <td width="10%" valign="top"><b>Required</b></td>
</tr>
<tr>
  <td width="12%" valign="top">file</td>
  <td width="78%" valign="top">Location of the property file to be edited</td>
  <td width="10%" valign="top">Yes</td>
</tr>
<tr>
  <td width="12%" valign="top">comment</td>
  <td width="78%" valign="top">Header for the file itself</td>
  <td width="10%" valign="top">no</td>
</tr>
</table>

<h3>Parameters specified as nested elements</h3>
<h4><a name="entryElement">Entry</a></h4>
<p>Use nested <code>&lt;entry&gt;</code>
elements to specify actual modifications to the property file itself.</p>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">key</td>
    <td valign="top">Name of the property name/value pair</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">value</td>
    <td valign="top">Value to set (=), to add (+) or subtract (-)</td>
    <td valign="top" align="center" rowspan="2">At least one must be specified</td>
  </tr>
  <tr>
    <td valign="top">default</td>
    <td valign="top">Initial value to set for a property if it is not
                     already defined in the property file.<br>
                     For type date, an additional keyword is allowed: &quot;now&quot;</td>
    </tr>
  <tr>
    <td valign="top">type</td>
    <td valign="top">Regard the value as : int, date or string (default)</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">operation</td>
    <td valign="top">&quot;+&quot; or &quot;=&quot; (default) for all datatypes<br>&quot;-&quot; (for date and int only).<br>
    </td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">pattern</td>
    <td valign="top">For int and date type only. If present, Values will
                     be parsed and formatted accordingly.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">unit</td>
    <td valign="top">The unit of the value to be applied to date +/- operations.
                     Valid Values are:
                     <ul>
                        <li>millisecond</li>
                        <li>second</li>
                        <li>minute</li>
                        <li>hour</li>
                        <li>day (default)</li>
                        <li>week</li>
                        <li>month</li>
                        <li>year</li>
                     </ul>
                     This only applies to date types using a +/- operation.
    </td>
    <td align="center" valign="top">No</td>
  </tr>
</table>
<p>The rules used when setting a property value are shown below.&nbsp; The
operation occurs <b>after</b> these rules are considered.</p>

<ul>
  <li>If only value is specified, the property is set to it regardless of its
  previous value.</li>
  <li>If only default is specified and the property previously existed in the
  property file, it is unchanged.</li>
  <li>If only default is specified and the property did not exist in the
  property file, the property is set to default.</li>
  <li>If value and default are both specified and the property previously
  existed in the property file, the property is set to value.</li>
  <li>If value and default are both specified and the property did not exist in
  the property file, the property is set to default.</li>
</ul>
<p>&nbsp;</p>

<h3>Examples</h3>

<p>The following changes the my.properties file.  Assume my.properties look like:</p>

<pre># A comment
akey=novalue</pre>

<p>After running, the file would now look like
</p>
<pre>#Thu Nov 02 23:41:47 EST 2000
akey=avalue
adate=2000/11/02 23\:41
anint=1
formated.int=0014
formated.date=028 17\:34
</pre>
<p>
The slashes conform to the expectations of the Properties class.  The file will be stored in a manner so that each character is examined and escaped if necessary.  Note that the original comment is now lost.  Please keep this in mind when running this task against heavily commented properties files.  It may be best to have a commented version in the source tree, copy it to a deployment area, and then run the modifications on the copy.  Future versions of PropertyFile will hopefully eliminate this shortcoming.
</p>

<blockquote><pre>&lt;propertyfile
    file=&quot;my.properties&quot;
    comment=&quot;My properties&quot;&gt;
  &lt;entry  key=&quot;akey&quot; value=&quot;avalue&quot;/&gt;
  &lt;entry  key=&quot;adate&quot; type=&quot;date&quot; value=&quot;now&quot;/&gt;
  &lt;entry  key=&quot;anint&quot; type=&quot;int&quot; operation=&quot;+&quot;/&gt;
  &lt;entry  key=&quot;formated.int&quot; type=&quot;int&quot; default=&quot;0013&quot; operation=&quot;+&quot; pattern=&quot;0000&quot;/&gt;
  &lt;entry  key=&quot;formated.date&quot; type=&quot;date&quot; value=&quot;now&quot; pattern=&quot;DDD HH:mm&quot;/&gt;
&lt;/propertyfile&gt;
</pre></blockquote>
<p>
To produce dates relative from today :</p>
<blockquote><pre>&lt;propertyfile
    file=&quot;my.properties&quot;
    comment=&quot;My properties&quot;&gt;
  &lt;entry  key=&quot;formated.date-1&quot;
      type=&quot;date&quot; default=&quot;now&quot; pattern=&quot;DDD&quot;
      operation=&quot;-&quot; value=&quot;1&quot;/&gt;
  &lt;entry  key=&quot;formated.tomorrow&quot;
      type=&quot;date&quot; default=&quot;now&quot; pattern=&quot;DDD&quot;
      operation=&quot;+&quot; value=&quot;1&quot;/&gt;
&lt;/propertyfile&gt;
</pre></blockquote>

<p>
Concatenation of strings :</p>
<blockquote><pre>&lt;propertyfile
    file=&quot;my.properties&quot;
    comment=&quot;My properties&quot;&gt;
  &lt;entry  key=&quot;progress&quot; default=&quot;&quot; operation=&quot;+&quot; value=&quot;.&quot;/&gt;
&lt;/propertyfile&gt;
</pre></blockquote>
<p>Each time called, a &quot;.&quot; will be appended to &quot;progress&quot;
</p>

</body>
</html>
