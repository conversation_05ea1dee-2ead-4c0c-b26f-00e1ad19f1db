<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Chown Task</title>
</head>

<body>

<h2><a name="Chown">Chown</a></h2>
<p><em>Since Ant 1.6.</em></p>
<h3>Description</h3>

<p>Changes the owner of a file or all files inside specified
directories. Right now it has effect only under Unix.  The owner
attribute is equivalent to the corresponding argument for the chown
command.</p>

<p><a href="../CoreTypes/fileset.html">FileSet</a>s, 
<a href="../CoreTypes/dirset.html">DirSet</a>s or <a
href="../CoreTypes/filelist.html">FileList</a>s can be specified using
nested <code>&lt;fileset&gt;</code>, <code>&lt;dirset&gt;</code> and 
<code>&lt;filelist&gt;</code> elements.</p>

<p>Starting with Ant 1.7, this task supports arbitrary <a
href="../CoreTypes/resources.html#collection">Resource Collection</a>s
as nested elements.</p>

<p>By default this task will use a single invocation of the underlying
chown command.  If you are working on a large number of files this may
result in a command line that is too long for your operating system.
If you encounter such problems, you should set the maxparallel
attribute of this task to a non-zero value.  The number to use highly
depends on the length of your file names (the depth of your directory
tree) and your operating system, so you'll have to experiment a
little.  POSIX recommends command line length limits of at least 4096
characters, this may give you an approximation for the number you
could use as initial value for these experiments.</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">the file or directory of which the owner must be
    changed.</td>
    <td valign="top" valign="middle">Yes or nested
    <code>&lt;fileset/list&gt;</code> elements.</td>
  </tr>
  <tr>
    <td valign="top">owner</td>
    <td valign="top">the new owner.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">parallel</td>
    <td valign="top">process all specified files using a single
      <code>chown</code> command. Defaults to true.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">type</td>
    <td valign="top">One of <i>file</i>, <i>dir</i> or
      <i>both</i>. If set to <i>file</i>, only the owner of
      plain files are going to be changed. If set to <i>dir</i>, only
      the directories are considered.<br>
      <strong>Note:</strong> The type attribute does not apply to
      nested <i>dirset</i>s - <i>dirset</i>s always implicitly
      assume type to be <i>dir</i>.</td>
    <td align="center" valign="top">No, default is <i>file</i></td>
  </tr>
  <tr>
    <td valign="top">maxparallel</td>
    <td valign="top">Limit the amount of parallelism by passing at
      most this many sourcefiles at once.  Set it to &lt;= 0 for
      unlimited.  Defaults to unlimited.</td>
    <td align="center" valign="top">No</td>

  </tr>
  <tr>
    <td valign="top">verbose</td>
    <td valign="top">Whether to print a summary after execution or not.
      Defaults to <code>false</code>.</td>
    <td align="center" valign="top">No</td>
  </tr>

</table>
<h3>Examples</h3>
<blockquote><pre>
&lt;chown file="${dist}/start.sh" owner="coderjoe"/&gt;
</pre>
</blockquote>
<p>makes the "start.sh" file belong to coderjoe on a
UNIX system.</p>
<blockquote>
<pre>
    &lt;chown owner="coderjoe"&gt;
      &lt;fileset dir="${dist}/bin" includes="**/*.sh"/&gt;
    &lt;/chown&gt;
</pre>
</blockquote>
<p>makes all ".sh" files below <code>${dist}/bin</code>
belong to coderjoe on a UNIX system.</p>
<blockquote>
<pre>
&lt;chown owner="coderjoe"&gt;
  &lt;fileset dir="shared/sources1"&gt;
    &lt;exclude name="**/trial/**"/&gt;
  &lt;/fileset&gt;
  &lt;fileset refid="other.shared.sources"/&gt;
&lt;/chown&gt;
</pre>
</blockquote>
<p>makes all files below <code>shared/sources1</code> (except those
below any directory named trial) belong to coderjoe on a UNIX 
system. In addition all files belonging to a FileSet
with <code>id</code> <code>other.shared.sources</code> get the same
owner.</p>

<blockquote>
<pre>
&lt;chown owner="webadmin" type="file"&gt;
  &lt;fileset dir="/web"&gt;
    &lt;include name="**/*.cgi"/&gt;
    &lt;include name="**/*.old"/&gt;
  &lt;/fileset&gt;
  &lt;dirset dir="/web"&gt;
    &lt;include name="**/private_*"/&gt;
  &lt;/dirset&gt;
&lt;/chmod&gt;
</pre>
</blockquote>

<p>makes cgi scripts, files with a <code>.old</code> extension or
directories beginning with <code>private_</code> belong to the Employee named
webadmin. A directory ending in <code>.old</code> or a file beginning with 
<code>private_</code> would remain unaffected.</p>



</body>
</html>

