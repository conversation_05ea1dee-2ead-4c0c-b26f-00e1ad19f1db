<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Sound Task</title>
</head>

<body>

<h2><a name="sound">Sound</a></h2>
<h3>Description</h3>
<p>Plays a sound-file at the end of the build, according to whether
the build failed or succeeded. You can specify either a specific
sound-file to play, or, if a directory is specified, the
<code>&lt;sound&gt;</code> task will randomly select a file to play.
Note: At this point, the random selection is based on all the files
in the directory, not just those ending in appropriate suffixes
for sound-files, so be sure you only have sound-files in the 
directory you specify.</p>
<p>
Unless you are running on Java 1.3 or later, you need the Java Media Framework
on the classpath (javax.sound). 
</p>


<h3>Nested Elements</h3>
<h4>success</h4>
<p>Specifies the sound to be played if the build succeeded.</p>
<h4>fail</h4>
<p>Specifies the sound to be played if the build failed.</p>

<h3>Nested Element Parameters</h3>
<p>
The following attributes may be used on the <code>&lt;success&gt;</code> 
and <code>&lt;fail&gt;</code> elements:</p>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">source</td>
    <td valign="top">the path to a sound-file directory, or the name of a
specific sound-file, to be played.
    </td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">loops</td>
    <td valign="top">the number of extra times to play the sound-file;
      default is <code>0</code>.
    </td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">duration</td>
    <td valign="top">the amount of time (in milliseconds) to play
      the sound-file.
    </td>
    <td valign="top" align="center">No</td>
  </tr>
</table>

<h3>Examples</h3>
<blockquote>
<pre>
&lt;target name=&quot;fun&quot; if=&quot;fun&quot; unless=&quot;fun.done&quot;&gt;
  &lt;sound&gt;
    &lt;success source=&quot;/Users/<USER>/sounds/bell.wav&quot;/&gt;
    &lt;fail source=&quot;/Users/<USER>/sounds/ohno.wav&quot; loops=&quot;2&quot;/&gt;
  &lt;/sound&gt;
  &lt;property name=&quot;fun.done&quot; value=&quot;true&quot;/&gt;
&lt;/target&gt;
</pre>
</blockquote>
plays the <code>bell.wav</code> sound-file if the build succeeded, or
the <code>ohno.wav</code> sound-file if the build failed, three times,
if the <code>fun</code> property is set to <code>true</code>.
If the target
is a dependency of an &quot;initialization&quot; target that other
targets depend on, the
<code>fun.done</code> property prevents the target from being executed
more than once.
<blockquote>
<pre>
&lt;target name=&quot;fun&quot; if=&quot;fun&quot; unless=&quot;fun.done&quot;&gt;
  &lt;sound&gt;
    &lt;success source=&quot;//intranet/sounds/success&quot;/&gt;
    &lt;fail source=&quot;//intranet/sounds/failure&quot;/&gt;
  &lt;/sound&gt;
  &lt;property name=&quot;fun.done&quot; value=&quot;true&quot;/&gt;
&lt;/target&gt;
</pre>
</blockquote>
randomly selects a sound-file to play when the build succeeds or fails.



</body>
</html>

