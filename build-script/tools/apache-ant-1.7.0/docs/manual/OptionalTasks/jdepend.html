<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<!doctype html public "-//w3c//dtd html 4.0 transitional//en">

<html>
<head>
   <link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>JDepend Task</title>
   <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
   <meta http-equiv="Content-Language" content="en-us">
</head>

<body>

<h2><a NAME="JDepend"></a>JDepend</h2>

<h3>Description</h3>

<P>Invokes the <a href="http://www.clarkware.com/software/JDepend.html">JDepend</a> parser.</P>

<P>This parser &quot;traverses a set of Java source file directories and generates design quality metrics for each Java package&quot;.
It allows to &quot;automatically measure the quality of a design in terms of its extensibility, reusability, and maintainability to
effectively manage and control package dependencies.&quot;</P>

<p>Source file directories are defined by nested
<code>&lt;sourcespath&gt;</code>; Class file directories are defined
by nested <code>&lt;classesespath&gt;</code>, see <a
href="#nested">nested elements</a>.</p>

<p>Optionally, you can also set the <code>outputfile</code> name where the output is stored. By default the task writes its report to the standard output.</P>

<p> The task requires at least the JDepend 1.2 version. </p>

<h3>Parameters</h3>

<table BORDER=1 CELLSPACING=0 CELLPADDING=2 >
  <tr>
    <td VALIGN=TOP><b>Attribute</b></td>
    <td VALIGN=TOP><b>Description</b></td>
    <td ALIGN=CENTER VALIGN=TOP><b>Required</b></td>
  </tr>
  <tr>
    <td VALIGN=TOP>outputfile</td>
    <td VALIGN=TOP>The output file name. If not set, the output is printed on the standard output.</td>
    <td ALIGN=CENTER VALIGN=TOP>No</td>
  </tr>
  <tr>
    <td VALIGN=TOP>format</td>
    <td VALIGN=TOP>The format to write the output in. The default is "text", the alternative is "xml"</td>
    <td ALIGN=CENTER VALIGN=TOP>No</td>
  </tr>
  <tr>
    <td VALIGN=TOP>fork</td>
    <td VALIGN=TOP>Run the tests in a separate VM.</td>
    <td ALIGN=CENTER VALIGN=TOP>No, default is "off"</td>
  </tr>
  <tr>
    <td VALIGN=TOP>haltonerror</td>
    <td VALIGN=TOP>Stop the build process if an error occurs during the jdepend analysis.</td>
    <td ALIGN=CENTER VALIGN=TOP>No, default is "off"</td>
  </tr>
  <tr>
    <td VALIGN=TOP>timeout</td>
    <td VALIGN=TOP>Cancel the operation if it doesn't finish in the given time (measured in milliseconds). (Ignored if fork is disabled.)</td>
    <td ALIGN=CENTER VALIGN=TOP>No</td>
  </tr>
  <tr>
    <td VALIGN=TOP>jvm</td>
    <td VALIGN=TOP>The command used to invoke the Java Virtual Machine, default is 'java'. The command is resolved by java.lang.Runtime.exec(). (Ignored if fork is disabled.)</td>
    <td ALIGN=CENTER VALIGN=TOP>No, default "java"</td>
  </tr>
  <tr>
    <td VALIGN=TOP>dir</td>
    <td VALIGN=TOP>The directory to invoke the VM in. (Ignored if fork is disabled)</td>
    <td ALIGN=CENTER VALIGN=TOP>No</td>
  </tr>
  <tr>
    <td VALIGN=TOP>includeruntime</td>
    <td VALIGN=TOP>Implicitly add the classes required to run jdepend
    in forked mode. (Ignored if fork is disabled). Since ant 1.6.</td>
    <td ALIGN=CENTER VALIGN=TOP>No, default is "no".</td>
  </tr>
  <tr>
    <td VALIGN=TOP>classpathref</td>
    <td VALIGN=TOP>the classpath to use, given as reference to a PATH defined elsewhere.</td>
    <td ALIGN=CENTER VALIGN=TOP>No</td>
  </tr>
</table>

<h3><a name="nested">Nested Elements</a></h3>

<p><code>jdepend</code> supports four nested elements:
<code>&lt;classpath&gt;, &lt;classespath&gt; </code> and
<code>&lt;sourcespath&gt;</code>, that represent <a
href="../using.html#path">PATH like structures</a>, and
<code>&lt;exclude&gt;</code>.</p>

<p><code>&lt;sourcespath&gt;</code> is used to define the paths of the
source code to analyze, but it is deprecated. With version 2.5 of
JDepend, only class files are analyzed. The nested element
<code>&lt;classespath&gt;</code> replaces <code>&lt;sourcespath&gt;</code> and is used to define
the paths of compiled class code to analyze; the <code>&lt;sourcespath&gt;</code>
variable is still available in case you are using an earlier version
of JDepend.  The <code>&lt;exclude&gt;</code> element can be used to set packages
to ignore (requires JDepend 2.5 or above).</p>

<h3>Examples</h3>

<blockquote>
<pre>
&lt;jdepend classpathref="base.path"&gt;
&nbsp;&nbsp;&nbsp; &lt;classespath&gt;
&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; &lt;pathelement location="build"/&gt;
&nbsp;&nbsp;&nbsp; &lt;/classespath&gt;
&lt;/jdepend&gt;

</pre>
</blockquote>

<p>This invokes JDepend on the <code>build</code> directory, writing
the output on the standard output. The classpath is defined using a
classpath reference.</p>

<blockquote>
<pre>
&lt;jdepend outputfile="docs/jdepend.xml" fork="yes" format="xml"&gt;
&nbsp;&nbsp;&nbsp; &lt;sourcespath&gt;
&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; &lt;pathelement location="src"/&gt;
&nbsp;&nbsp;&nbsp; &lt;/sourcespath&gt;
&nbsp;&nbsp;&nbsp; &lt;classpath&gt;
&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; &lt;pathelement location="classes"/&gt;
&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; &lt;pathelement location="lib/jdepend.jar"/&gt;
&nbsp;&nbsp;&nbsp; &lt;/classpath&gt;
&lt;/jdepend&gt;
</pre>
</blockquote>

<p>This invokes JDepend in a separate VM on the <code>src</code> and
<code>testsrc</code> directories, writing the output to the
<code>&lt;docs/jdepend.xml&gt;</code> file in xml format. The
classpath is defined using nested elements. </p>

<blockquote>
<pre>
&lt;jdepend classpathref="base.path"&gt;
&nbsp;&nbsp;&nbsp; &lt;exclude name="java.*&gt;
&nbsp;&nbsp;&nbsp; &lt;exclude name="javax.*&gt;
&nbsp;&nbsp;&nbsp; &lt;classespath&gt;
&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; &lt;pathelement location="build"/&gt;
&nbsp;&nbsp;&nbsp; &lt;/classespath&gt;
&lt;/jdepend&gt;
</pre>
</blockquote>

<p>This invokes JDepend with the build directory as the base for class
files to analyze, and will ignore all classes in the java.* and
javax.* packages.</p>


</body>
</html>


