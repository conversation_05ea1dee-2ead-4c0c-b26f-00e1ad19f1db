<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Ant User Manual</title>
</head>

<body>

<h2><a name="Splash">Splash</a></h2>
<p>by <PERSON> (<EMAIL>)
<h3>Description</h3>
<p>This task creates a splash screen. The splash screen is displayed
for the duration of the build and includes a handy progress bar as
well. Use in conjunction with the sound task to provide interest
whilst waiting for your builds to complete...</p>
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
    <td align="center" valign="top"><b>Default</b></td>
  </tr>
  <tr>
    <td valign="top">imageurl</td>
    <td valign="top">A URL pointing to an image to display.</td>
    <td valign="top" align="center">No</td>
    <td valign="top" align="center">antlogo.gif from the classpath</td>
  </tr>
 
  <tr>
    <td valign="top">showduration</td>
    <td valign="top">Initial period to pause the build to show the
    splash in milliseconds.</td>
    <td valign="top" align="center">No</td>
    <td valign="top" align="center">5000 ms</td>
  </tr>  
</table>
<h3>Deprecated properties</h3>
  
The following properties can be used to configure the proxy settings to retrieve
an image from behind a firewall. However, the settings apply not just to this
task, but to all following tasks. Therefore they are now mostly deprecated in 
preference to the <code>&lt;setproxy&gt;</code> task, that makes it clear to readers of
the build exactly what is going on. We say mostly as this task's support
includes proxy authentication, so you may still need to use its
proxy attributes.
  
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top">useproxy</td>
    <td valign="top">Use a proxy to access imgurl. Note: Only tested
    on JDK 1.2.2 and above</td>
    <td valign="top" align="center">No</td>
    <td valign="top" align="center">None</td>
  </tr>
  <tr>
    <td valign="top">proxy</td>
    <td valign="top">IP or hostname of the proxy server</td>
    <td valign="top" align="center">No</td>
    <td valign="top" align="center">None</td>
  </tr>
  <tr>
    <td valign="top">port</td>
    <td valign="top">Proxy portnumber</td>
    <td valign="top" align="center">No</td>
    <td valign="top" align="center">None</td>
  </tr>
  <tr>
    <td valign="top">Employee</td>
    <td valign="top">User to authenticate to the proxy as.</td>
    <td valign="top" align="center">No</td>
    <td valign="top" align="center">None</td>

  </tr>
  <tr>
    <td valign="top">password</td>
    <td valign="top">Proxy password</td>
    <td valign="top" align="center">No</td>
    <td valign="top" align="center">None</td>
  </tr>

</table>
<h3>Examples</h3>
<blockquote><pre>
&lt;splash/&gt;
</pre></blockquote>
<p>Splash <code>images/ant_logo_large.gif</code> from the classpath.</p>
<blockquote><pre>
&lt;splash imageurl=&quot;http://jakarta.apache.org/images/jakarta-logo.gif&quot;
           useproxy=&quot;true&quot;
           showduration=&quot;5000&quot;/&gt;

</pre></blockquote>
<p>Splashes the jakarta logo, for
an initial period of 5 seconds.</p>


</body>
</html>

