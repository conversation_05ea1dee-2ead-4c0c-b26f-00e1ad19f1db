<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Telnet Task</title>
</head>

<body>

<h2><a name="telnet">Telnet</a></h2>
<h3>Description</h3>
Task to automate a remote telnet session. The task uses 
nested <tt>&lt;read&gt;</tt> to indicate strings to wait for, and
<tt>&lt;write&gt;</tt> tags to specify text to send.  

<p>If you do specify a userid and password, the system will 
assume a common unix prompt to wait on. This behavior can be easily over-ridden.</p>
<p><b>Note:</b> This task depends on external libraries not included in the Ant distribution.
See <a href="../install.html#librarydependencies">Library Dependencies</a> for more information.</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <th>Attribute</th>
    <th>Values</th>
    <th>Required</th>
  </tr>
  <tr>
     <td>userid</td>
     <td>the login id to use on the telnet server.</td>
     <td>Only if password is specified</td>
  </tr>
  <tr>
     <td>password</td>
     <td>the login password to use on the telnet server.</td>
     <td>Only if userid is specified</td>
  </tr>
  <tr>
     <td>server</td>
     <td>the address of the remote telnet server.</td>
     <td>Yes</td>
  </tr>
  <tr>
     <td>port</td>
     <td>the port number of the remote telnet server. Defaults to port 23.</td>
     <td>No</td>
  </tr>
  <tr>
     <td>initialCR</td>
     <td>send a cr after connecting (&quot;yes&quot;). Defaults to &quot;no&quot;.</td>
     <td>No</td>
  </tr>
  <tr>
     <td>timeout</td>
     <td>set a default timeout to wait for a response. Specified in seconds. Default is no timeout.</td>
     <td>No</td>
  </tr>
</table>
<h3><a name="nested">Nested Elements</a></h3>
The commands to send to the server, and responses to wait for, are
described as nested elements.  

<h4>read</h4>

<p>declare (as a text child of this element) a string to wait for.
The element supports the timeout attribute, which overrides any
timeout specified for the task as a whole. It also has a <tt>string</tt>
attribute, which is an alternative to specifying the string as 
a text element.
</p>
<i>Always declare an opening and closing
<code>&lt;read&gt;</code> element to ensure that statements are not sent before
the connection is ready, and that the connection is not broken before
the final command has completed.
</i>
<h4>write</h4>

<p>describes the text to send to the server. The <tt>echo</tt> boolean
attribute controls whether the string is echoed to the local log; 
this is "true" by default
</p>
<h3>Examples</h3>
A simple example of connecting to a server and running a command.  This assumes
 a prompt of &quot;ogin:&quot; for the userid, and a prompt of &quot;assword:&quot;
 for the password.

<blockquote><pre>
&lt;telnet userid=&quot;bob&quot; password=&quot;badpass&quot; server=&quot;localhost&quot;&gt;
   &lt;read&gt;/home/<USER>/read&gt;
   &lt;write&gt;ls&lt;/write&gt;
   &lt;read string=&quot;/home/<USER>/&gt;
&lt;/telnet&gt;
</pre></blockquote>

This task can be rewritten as:
<blockquote><pre>
&lt;telnet server=&quot;localhost&quot;&gt;
   &lt;read&gt;ogin:&lt;/read&gt;
   &lt;write&gt;bob&lt;/write&gt;
   &lt;read&gt;assword:&lt;/read&gt;
   &lt;write&gt;badpass&lt;/write&gt;
   &lt;read&gt;/home/<USER>/read&gt;
   &lt;write&gt;ls&lt;/write&gt;
   &lt;read&gt;/home/<USER>/read&gt;
&lt;/telnet&gt;
</pre></blockquote>

A timeout can be specified at the <code>&lt;telnet&gt;</code> level or at the <code>&lt;read&gt;</code> level. 
This will connect, issue a sleep command that is suppressed from displaying and wait 
10 seconds before quitting.
<blockquote><pre>
&lt;telnet userid=&quot;bob&quot; password=&quot;badpass&quot; server=&quot;localhost&quot; timeout=&quot;20&quot;&gt;
   &lt;read&gt;/home/<USER>/read&gt;
   &lt;write echo=&quot;false&quot;&gt;sleep 15&lt;/write&gt;
   &lt;read timeout=&quot;10&quot;&gt;/home/<USER>/read&gt;
&lt;/telnet&gt;
</pre></blockquote>

The task can be used with other ports as well:
<blockquote><pre>
&lt;telnet port=&quot;80&quot; server=&quot;localhost&quot; timeout=&quot;20&quot;&gt;
   &lt;read/&gt;
   &lt;write&gt;GET / http/0.9&lt;/write&gt;
   &lt;write/&gt;
   &lt;read timeout=&quot;10&quot;&gt;&amp;lt;/HTML&amp;gt;&lt;/read&gt;
&lt;/telnet&gt;
</pre></blockquote>
<p>
To use this task against the WinNT telnet service, you need to configure the service to use 
classic authentication rather than NTLM negotiated authentication. 
This can be done in the Telnet Server Admin app: 
select "display/change registry settings", then "NTLM", then set the value of NTLM to 1.
</p>


</body>
</html>

