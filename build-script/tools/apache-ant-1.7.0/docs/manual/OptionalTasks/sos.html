<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>

  <meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
  <link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>SOS Tasks</title>

</head>
<body>

<div align="center">
<h1>SourceOffSite Tasks User Manual</h1>

<div align="left">by
<ul>
<li><a href="mailto:<EMAIL>"><PERSON> Stockall</a></li>
</ul>
Version 1.1 2002/01/23
<br>
<br>

<hr width="100%" size="2">
<h2>Contents</h2>

<ul>
    <li><a href="#intro">Introduction</a></li>
    <li><a href="#tasks">The Tasks</a></li>

</ul>
<br>

<h2><a name="intro">Introduction</a> </h2>

<p>These tasks provide an interface to the <a href="http://msdn.microsoft.com/ssafe/default.asp" target="_top">
Microsoft Visual SourceSafe</a> SCM via <a href="http://www.sourcegear.com">
SourceGear's</a> <a href="http://sourcegear.com/sos/index.htm">SourceOffSite</a>
product. SourceOffSite is an add-on to Microsoft's VSS, that allows remote
development teams and tele-commuters that need fast and secure read/write
access to a centralized SourceSafe database via any TCP/IP connection. SOS
provides Linux ,Solaris &amp; Windows clients. The
<code> org.apache.tools.ant.taskdefs.optional.sos</code>
package consists  of a simple framework to support SOS functionality as well
as some Ant tasks  encapsulating frequently used SOS commands.  Although it
is possible to use  these commands on the desktop,  they were primarily intended
to be used by  automated build systems. These tasks have been tested with
SourceOffSite  version 3.5.1 connecting to VisualSourceSafe 6.0. The tasks
have been tested with Linux, Solaris &amp; Windows2000.</p>

<h2><a name="tasks">The Tasks</a> </h2>

<table border="0" cellspacing="0" cellpadding="3">
       <tbody>
         <tr>
           <td><a href="#SOSGet">sosget</a></td>
           <td>Retrieves a read-only copy of the specified project or file.</td>
         </tr>
         <tr>
           <td><a href="#SOSLabel">soslabel</a></td>
           <td>Assigns a label to the specified project.</td>
         </tr>
         <tr>
           <td><a href="#SOSCheckIn">soscheckin</a></td>
           <td>Updates VSS with changes made to a checked out file or project,
           and unlocks the VSS master copy.</td>
         </tr>
         <tr>
           <td><a href="#SOSCheckOut">soscheckout</a></td>
           <td>Retrieves a read-write copy of the specified project
           or file, locking the&nbsp;VSS master copy</td>
         </tr>

  </tbody>
</table>
     <br>

<hr width="100%" size="2">
<h2>Task Descriptions</h2>

<h2><a name="SOSGet"></a>SOSGet<br>
     </h2>
<h3>Description</h3>
             Task to perform GET commands with SOS<br>
<h3>Parameters</h3>
     </div>
     </div>

<table border="1">
     <tbody>
       <tr>
          <th>Attribute</th>
          <th>Values</th>
          <th>Required</th>
        </tr>
        <tr>
          <td>soscmd</td>
          <td>Directory which contains soscmd(.exe) <br>
          soscmd(.exe) must be in the path if this is not specified</td>
          <td>No</td>
        </tr>
        <tr>
           <td>vssserverpath</td>
           <td>path to the srcsafe.ini  - eg. \\server\vss\srcsafe.ini</td>
           <td>Yes</td>
        </tr>
        <tr>
           <td>sosserverpath</td>
           <td>address &amp; port of the SOS server  - eg. ***********:8888</td>
           <td>Yes</td>
        </tr>
        <tr>
           <td>projectpath</td>
           <td>SourceSafe project path - eg. $/SourceRoot/Project1</td>
           <td>Yes</td>
        </tr>
        <tr>
           <td>file</td>
           <td>Filename to act upon<br>
           If no file is specified then act upon the project</td>
           <td>No</td>
        </tr>
        <tr>
           <td>username</td>
           <td>SourceSafe username</td>
           <td>Yes</td>
        </tr>
        <tr>
           <td>password</td>
           <td>SourceSafe password</td>
           <td>No</td>
        </tr>
        <tr>
           <td>localpath</td>
           <td>Override the working directory and get to the specified path</td>
           <td>No</td>
        </tr>
        <tr>
           <td>soshome</td>
           <td>The path to the SourceOffSite home directory</td>
           <td>No</td>
        </tr>
        <tr>
           <td>nocompress</td>
           <td>true or false - disable compression</td>
           <td>No</td>
        </tr>
        <tr>
           <td>recursive</td>
           <td>true or false - Only works with the GetProject command</td>
           <td>No</td>
        </tr>
        <tr>
           <td>version</td>
           <td>a version number to get - Only works with the GetFile command</td>
           <td>No</td>
        </tr>
        <tr>
           <td>label</td>
           <td>a label version to get - Only works with the GetProject command</td>
           <td>No</td>
        </tr>
        <tr>
           <td>nocache</td>
           <td>true or false - Only needed if SOSHOME is set as an environment variable</td>
           <td>No</td>
        </tr>
        <tr>
           <td>verbose</td>
           <td>true or false - Status messages are displayed</td>
           <td>No</td>
        </tr>
  </tbody>
</table>

<h3>Example</h3>

<pre>
&lt;sosget verbose=&quot;true&quot;
        recursive=&quot;true&quot;
        username=&quot;build&quot;
        password=&quot;build&quot;
        localpath=&quot;tmp&quot;
        projectpath=&quot;$/SourceRoot/project1&quot;
        sosserverpath=&quot;************:8888&quot;
        vssserverpath=&quot;d:\vss\srcsafe.ini&quot;/&gt;
</pre>
<small>Connects to a SourceOffsite server on ************:8888 with
build,build as the username &amp; password. The SourceSafe  database resides
on the same box as the SOS server  &amp; the VSS database  is at
&quot;d:\vss\srcsafe.ini&quot; Does a recursive GetProject on
$/SourceRoot/project1, using tmp as the working
directory. </small><br>
<br>

<hr width="100%" size="2">
<h2><a name="SOSLabel"></a>SOSLabel</h2>

<h3>Description</h3>
             Task to perform Label commands with SOS<br>
<h3>Parameters</h3>

<table border="1">
    <tbody><tr>
      <th>Attribute</th>
      <th>Values</th>
      <th>Required</th>
    </tr>
    <tr>
      <td>soscmd</td>
      <td>Directory which contains soscmd(.exe) <br>
      soscmd(.exe) must be in the path if this is not specified</td>
      <td>No</td>
    </tr>
    <tr>
       <td>vssserverpath</td>
       <td>path to the srcsafe.ini  - eg. \\server\vss\srcsafe.ini</td>
       <td>Yes</td>
    </tr>
    <tr>
       <td>sosserverpath</td>
       <td>address and port of the SOS server  - eg. ***********:8888</td>
       <td>Yes</td>
    </tr>
    <tr>
       <td>projectpath</td>
       <td>SourceSafe project path - eg. $/SourceRoot/Project1</td>
       <td>Yes</td>
    </tr>
    <tr>
       <td>username</td>
       <td>SourceSafe username</td>
       <td>Yes</td>
    </tr>
    <tr>
       <td>password</td>
       <td>SourceSafe password</td>
       <td>No</td>
    </tr>
    <tr>
       <td>label</td>
       <td>The label to apply to a project</td>
       <td>Yes</td>
    </tr>
    <tr>
       <td>comment</td>
       <td>A comment to be applied to all files being labeled</td>
       <td>No</td>
    </tr>
    <tr>
       <td>verbose</td>
       <td>true or false - Status messages are displayed</td>
       <td>No</td>
    </tr>
  </tbody>
</table>

<h3>Example</h3>
<pre>
&lt;soslabel username=&quot;build&quot;
          password=&quot;build&quot;
          label=&quot;test label&quot;
          projectpath=&quot;$/SourceRoot/project1&quot;
          sosserverpath=&quot;************:8888&quot;
          vssserverpath=&quot;d:\vss\srcsafe.ini&quot;/&gt;
</pre>

<small>Connects to a SourceOffsite server on ************:8888 with
build,build as the username &amp; password. The SourceSafe database resides
on the same box as  the  SOS server  &amp; the VSS database is at
&quot;d:\vss\srcsafe.ini&quot;. Labels the $/SourceRoot/project1
project with &quot;test label&quot;.</small><br>
<br>

<hr width="100%" size="2"><br>

<h2><a name="SOSCheckIn"></a>SOSCheckIn</h2>

<h3>Description</h3>
        Task to perform CheckIn commands with SOS<br>
<h3>Parameters</h3>
<table border="1">
       <tbody>
    <tr>
      <th>Attribute</th>
      <th>Values</th>
      <th>Required</th>
    </tr>
    <tr>
      <td>soscmd</td>
      <td>Directory which contains soscmd(.exe) <br>
      soscmd(.exe) must be in the path if this is not specified</td>
      <td>No</td>
    </tr>
    <tr>
       <td>vssserverpath</td>
       <td>path to the srcsafe.ini  - eg. \\server\vss\srcsafe.ini</td>
       <td>Yes</td>
    </tr>
    <tr>
       <td>sosserverpath</td>
       <td>address and port of the SOS server  - eg. ***********:8888</td>
       <td>Yes</td>
    </tr>
    <tr>
       <td>projectpath</td>
       <td>SourceSafe project path - eg. $/SourceRoot/Project1</td>
       <td>Yes</td>
    </tr>
    <tr>
       <td>file</td>
       <td>Filename to act upon<br> If no file is specified then act upon the project</td>
       <td>No</td>
    </tr>
    <tr>
       <td>username</td>
       <td>SourceSafe username</td>
       <td>Yes</td>
    </tr>
    <tr>
       <td>password</td>
       <td>SourceSafe password</td>
       <td>No</td>
    </tr>
    <tr>
       <td>localpath</td>
       <td>Override the working directory and get to the specified path</td>
       <td>No</td>
    </tr>
    <tr>
       <td>soshome</td>
       <td>The path to the SourceOffSite home directory</td>
       <td>No</td>
    </tr>
    <tr>
       <td>nocompress</td>
       <td>true or false - disable compression</td>
       <td>No</td>
    </tr>
    <tr>
       <td>recursive</td>
       <td>true or false - Only works with the CheckOutProject command</td>
       <td>No</td>
    </tr>
    <tr>
       <td>nocache</td>
       <td>true or false - Only needed if SOSHOME is set as an environment variable</td>
       <td>No</td>
    </tr>
    <tr>
       <td>verbose</td>
       <td>true or false - Status messages are displayed</td>
       <td>No</td>
    </tr>
    <tr><td>comment</td>
       <td>A comment to be applied to all files being checked in</td>
       <td>No</td>
    </tr>
 </tbody>
</table>

<h3>Example</h3>
<pre>
&lt;soscheckin username=&quot;build&quot;
            password=&quot;build&quot;
            file=&quot;foobar.txt&quot;
            verbose=&quot;true&quot;
            comment=&quot;comment abc&quot;
            projectpath=&quot;$/SourceRoot/project1&quot;
            sosserverpath=&quot;server1:8888&quot;
            vssserverpath=&quot;\\server2\vss\srcsafe.ini&quot;/&gt;
</pre>

<small>Connects to a SourceOffsite server on server1:8888 with build,build as
the username &amp; password. The SourceSafe database resides on a different
box (server2) &amp; the VSS database is on a share called
&quot;vss&quot;. Checks-in only the &quot;foobar.txt&quot; file adding
a comment of &quot;comment abc&quot;. Extra status messages will be
displayed on screen.</small><br>
<br>

<hr width="100%" size="2">
<h2><a name="SOSCheckOut"></a>SOSCheckOut</h2>

<h3>Description</h3>
       Task to perform CheckOut commands with SOS<br>

<h3>Parameters</h3>

<table border="1">
       <tbody>
    <tr>
      <th>Attribute</th>
      <th>Values</th>
      <th>Required</th>
    </tr>
    <tr>
      <td>soscmd</td>
      <td>Directory which contains soscmd(.exe) <br>
      soscmd(.exe) must be in the path if this is not specified</td>
      <td>No</td>
    </tr>
    <tr>
       <td>vssserverpath</td>
       <td>path to the srcsafe.ini  - eg. \\server\vss\srcsafe.ini</td>
       <td>Yes</td>
    </tr>
    <tr>
       <td>sosserverpath</td>
       <td>address and port of the SOS server  - eg. ***********:8888</td>
       <td>Yes</td>
    </tr>
    <tr>
       <td>projectpath</td>
       <td>SourceSafe project path - eg. $/SourceRoot/Project1</td>
       <td>Yes</td>
    </tr>
    <tr>
       <td>file</td>
       <td>Filename to act upon<br> If no file is specified then act upon the project</td>
       <td>No</td>
    </tr>
    <tr>
       <td>username</td>
       <td>SourceSafe username</td>
       <td>Yes</td>
    </tr>
    <tr>
       <td>password</td>
       <td>SourceSafe password</td>
       <td>No</td>
    </tr>
    <tr>
       <td>localpath</td>
       <td>Override the working directory and get to the specified path</td>
       <td>No</td>
    </tr>
    <tr>
       <td>soshome</td>
       <td>The path to the SourceOffSite home directory</td>
       <td>No</td>
    </tr>
    <tr>
       <td>nocompress</td>
       <td>true or false - disable compression</td>
       <td>No</td>
    </tr>
    <tr>
       <td>recursive</td>
       <td>true or false - Only works with the CheckOutProject command</td>
       <td>No</td>
    </tr>
    <tr>
       <td>nocache</td>
       <td>true or false - Only needed if SOSHOME is set as an environment variable</td>
       <td>No</td>
    </tr>
    <tr>
       <td>verbose</td>
       <td>true or false - Status messages are displayed</td>
       <td>No</td>
    </tr>
  </tbody>
</table>
     <br>

<h3>Example</h3>
<pre>
&lt;soscheckout soscmd=&quot;/usr/local/bin&quot;
             verbose=&quot;true&quot;
             username=&quot;build&quot;
             password=&quot;build&quot;
             projectpath=&quot;$/SourceRoot/project1&quot;
             sosserverpath=&quot;************:8888&quot;
             vssserverpath=&quot;\\server2\vss\srcsafe.ini&quot;/&gt;
</pre>

<small>Connects to a SourceOffsite server on server1:8888 with build,build as
the username &amp; password. The SourceSafe database resides on a different
box (server2) &amp; the VSS database is on a share called
&quot;vss&quot;. Checks-out &quot;project1&quot;, Only the
&quot;project1&quot; directory will be locked as the recursive option
was not set. Extra status messages will be displayed on screen. The
soscmd(.exe) file to be used resides in /usr/local/bin.</small><br>
<br>

</body>
</html>
