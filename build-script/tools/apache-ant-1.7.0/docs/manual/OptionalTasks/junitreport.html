<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>
<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>JUnitReport Task</title>
</head>

<body>

<h2><a name="junitreport">JUnitReport</a></h2>
Merge the individual XML files generated by the JUnit task and eventually apply
a stylesheet on the resulting merged document to provide a browsable report of
the testcases results.
<p><strong>Note:</strong> This task depends on external libraries not included in
the Ant distribution.  See <a href="../install.html#librarydependencies">
Library Dependencies</a> for more information.</p>

<h3>Requirements</h3>

<p>The task needs Apache <a
href="http://xml.apache.org/xalan-j/">Xalan 2.4.1+ or Xalan XSLTC</a>
(JDK 1.4 contains a version of Xalan-J 2.x while JDK 1.5 ships with a
version of XSLTC). Starting from JDK 1.4.2-01 it ships with a bundled
Xalan-J 2.4.1+, meaning that JDK version prior to 1.4.2-01 won't work
out of the box. The table below summarize the compatibility status. 
</p>
<table border="1" cellpadding="2" cellspacing="0">
<tr><th>Xalan</th><th>Sun JDK Bundle</th><th>Status<th></tr> 
<tr><td>2.4.1+</td><td>JDK 1.4.2-01+</td><td>OK</td></tr>
<tr><td>XSLTC</td><td>JDK 1.5.x</td><td>OK</td></tr>
<tr><td>2.x</td><td>JDK 1.4.x</td><td>DEPRECATED<br><i>Use /Users/<USER>/dev/asf/ant-core/bootstrap/etc/junit-frames-xalan1.xsl
<br> Upgrade Xalan using the JDK endorsement mechanism</i></td></tr>
</table>

<p>With Ant 1.6.2 we had to decide between supporting Xalan-J 1/Xalan J 2.4.1-
and Xalan 2.4.1+/XSLTC, since there was no way to support both couples at the same
time.</p>
<p>With Ant 1.7 we had to drop support Xalan-J 1, since Xalan-J 1 has not 
available anymore for quite some time.</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td width="12%" valign="top"><b>Attribute</b></td>
    <td width="78%" valign="top"><b>Description</b></td>
    <td width="10%" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">tofile</td>
    <td valign="top">The name of the XML file that will aggregate all individual
      XML testsuite previously generated by the JUnit task.</td>
    <td align="center" valign="top">No. Default to TESTS-TestSuites.xml</td>
  </tr>
  <tr>
    <td valign="top">todir</td>
    <td valign="top">The directory where should be written the file resulting
      from the individual XML testsuite aggregation.</td>
    <td align="center" valign="top">No. Default to current directory</td>
  </tr>
</table>
<h3><a name="nested">Nested Elements</a></h3>
<h4>fileset</h4>
<p><code>junitreport</code> collects individual xml files generated by the JUnit
task using the nested <a href="../CoreTypes/fileset.html"><code>&lt;FileSet&gt;</code></a>
element.</p>
<h4>report</h4>
<p>Generate a browsable report based on the document created by the merge.</p>
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td width="12%" valign="top"><b>Attribute</b></td>
    <td width="78%" valign="top"><b>Description</b></td>
    <td width="10%" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">format</td>
    <td valign="top">The format of the generated report. Must be &quot;noframes&quot;
      or &quot;frames&quot;.</td>
    <td align="center" valign="top">No, default to &quot;frames&quot;</td>
  </tr>
  <tr>
    <td valign="top">styledir</td>
    <td valign="top">The directory where the stylesheets are defined. They must
      be conforming to the following conventions:
      <ul>
        <li>frames format: the stylesheet must be named <i>junit-frames.xsl</i>.</li>
        <li>noframes format: the stylesheet must be named <i>junit-noframes.xsl</i>.</li>
      </ul>
    </td>
    <td align="center" valign="top">No. Default to embedded stylesheets.</td>
  </tr>
  <tr>
    <td valign="top">todir</td>
    <td valign="top">The directory where the files resulting from the
      transformation should be written to.</td>
    <td align="center" valign="top">No. Default to current directory</td>
  </tr>
</table>
<p> Ant assumes the following concerning the <tt>frames</tt> and <tt>noframes</tt> formats :</p>
<p>The <tt>frames</tt> format uses
a stylesheet which is generating output <em>only</em> by redirecting.</p>
<p>The
<tt>noframes</tt> format does not use redirecting and generates one
file called <tt>junit-noframes.html</tt>.</p>
<p>Custom versions of <tt>junit-frames.xsl</tt> or <tt>junit-noframes.xsl</tt> must adhere to the above conventions.</p>

<h3>Nested Element of the report tag</h3>
<h4>param</h4>
<em>Since Ant 1.7</em>the report tag supports nested param tags.
These tags can pass XSL parameters to the stylesheet.
<h3>Parameters</h3>
<table width="60%" border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">name</td>
    <td valign="top">Name of the XSL parameter</td>
    <td align="center" valign="top">Yes</td>
  </tr>
  <tr>
    <td valign="top">expression</td>
    <td valign="top">Text value to be placed into the param.<br>
    Was originally intended to be an XSL expression.</td>
    <td align="center" valign="top">Yes</td>
  </tr>
  <tr>
    <td valign="top">if</td>
    <td valign="top">The param will only passed if this property is set.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">unless</td>
    <td valign="top">The param will only passed unless this property is set.</td>
    <td align="center" valign="top">No</td>
  </tr>

</table>
<h3>Example of report</h3>
<blockquote>
  <pre>&lt;junitreport todir=&quot;./reports&quot;&gt;
  &lt;fileset dir=&quot;./reports&quot;&gt;
    &lt;include name=&quot;TEST-*.xml&quot;/&gt;
  &lt;/fileset&gt;
  &lt;report format=&quot;frames&quot; todir=&quot;./report/html&quot;/&gt;
&lt;/junitreport&gt;
</pre>
</blockquote>
<p>would generate a <tt>TESTS-TestSuites.xml</tt> file in the directory <tt>reports</tt> and
generate the default framed report in the directory <tt>report/html</tt>.</p>
<h3>Example of report with xsl params</h3>
<blockquote>
  <pre>
&lt;junitreport todir="${outputdir}"&gt;
    &lt;fileset dir="${jrdir}"&gt;
        &lt;include name="TEST-*.xml"/&gt;
    &lt;/fileset&gt;
    &lt;report todir="${outputdir}/html"
        styledir="junitreport"
        format="frames"&gt;
        &lt;param name="key1" expression="value1"/&gt;
        &lt;param name="key2" expression="value2"/&gt;
    &lt;/report&gt;
&lt;/junitreport&gt;
  </pre>
  </blockquote>
<p>This example requires a file called <tt>junitreport/junit-frames.xsl</tt>.
    The XSL parameters key1 and key2 will be passed to the XSL transformation.</p>

</body>

</html>
