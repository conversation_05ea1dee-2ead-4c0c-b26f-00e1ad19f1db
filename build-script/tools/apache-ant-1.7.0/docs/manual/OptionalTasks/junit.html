<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>
<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>JUnit Task</title>
</head>
<body>

<h2><a name="junit">JUnit</a></h2>
<h3>Description</h3>

<p>This task runs tests from the JUnit testing framework. The latest
version of the framework can be found at
<a href="http://www.junit.org">http://www.junit.org</a>.
This task has been tested with JUnit 3.0 up to JUnit 3.8.2; it won't
work with versions prior to JUnit 3.0. It also works with JUnit 4.0, including
"pure" JUnit 4 tests using only annotations and no <code>JUnit4TestAdapter</code>.</p>
<p><strong>Note:</strong> This task depends on external libraries not included
in the Ant distribution.  See <a href="../install.html#librarydependencies">
Library Dependencies</a> for more information.
</p>
<p>
<strong>Note</strong>:
You must have <code>junit.jar</code> available.
You can do one of:
</p>
<ol>
<li>
Put both <code>junit.jar</code> and <code>ant-junit.jar</code> in
<code>ANT_HOME/lib</code>.
</li>
<li>
Do not put either in <code>ANT_HOME/lib</code>, and instead
include their locations in your <code>CLASSPATH</code> environment variable.
</li>
<li>
Add both JARs to your classpath using <code>-lib</code>.
</li>
<li>
Specify the locations of both JARs using
a <code>&lt;classpath&gt;</code> element in a <code>&lt;taskdef&gt;</code> in the build file.
</li>
<li>
Leave <code>ant-junit.jar</code> in its default location in <code>ANT_HOME/lib</code>
but include <code>junit.jar</code> in the <code>&lt;classpath&gt;</code> passed
to <code>&lt;junit&gt;</code>. <em>(since Ant 1.7)</em>
</li>
</ol>
<p>
See <a href="../../faq.html#delegating-classloader" target="_top">the
FAQ</a> for details.
</p>

<p>Tests are defined by nested <code>test</code> or
<code>batchtest</code> tags (see <a href="#nested">nested
elements</a>).</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
<tr>
  <td width="12%" valign="top"><b>Attribute</b></td>
  <td width="78%" valign="top"><b>Description</b></td>
  <td width="10%" valign="top"><b>Required</b></td>
</tr>
  <tr>
    <td valign="top">printsummary</td>
    <td valign="top">Print one-line statistics for each testcase. Can
      take the values <code>on</code>,
      <code>off</code>, and
      <code>withOutAndErr</code>.
      <code>withOutAndErr</code> is the same
      as <code>on</code> but also includes the output of the test
      as written to <code>System.out</code> and <code>System.err</code>.</td>
    <td align="center" valign="top">No; default is <code>off</code>.</td>
  </tr>
  <tr>
    <td valign="top">fork</td>
    <td valign="top">Run the tests in a separate VM.</td>
    <td align="center" valign="top">No; default is <code>off</code>.</td>
  </tr>
  <tr>
    <td valign="top">forkmode</td>
    <td valign="top">Controls how many Java Virtual Machines get
    created if you want to fork some tests.  Possible values are
    &quot;perTest&quot; (the default), &quot;perBatch&quot; and
    &quot;once&quot;.  &quot;once&quot; creates only a single Java VM
    for all tests while &quot;perTest&quot; creates a new VM for each
    TestCase class.  &quot;perBatch&quot; creates a VM for each nested
    <code>&lt;batchtest&gt;</code> and one collecting all nested
    <code>&lt;test&gt;</code>s.  Note that only tests with the same
    settings of <code>filtertrace</code>, <code>haltonerror</code>,
    <code>haltonfailure</code>, <code>errorproperty</code> and
    <code>failureproperty</code> can share a VM, so even if you set
    <code>forkmode</code> to &quot;once&quot;, Ant may have to create
    more than a single Java VM.  This attribute is ignored for tests
    that don't get forked into a new Java VM.  <em>since Ant 1.6.2</em></td>
    <td align="center" valign="top">No; default is <code>perTest</code>.</td>
  </tr>
  <tr>
    <td valign="top">haltonerror</td>
    <td valign="top">Stop the build process if an error occurs during the test
       run.</td>
    <td align="center" valign="top">No; default is <code>off</code>.</td>
  </tr>
<tr>
    <td valign="top">errorproperty</td>
    <td valign="top">The name of a property to set in the event of an error.</td>
    <td align="center" valign="top">No</td>
</tr>
  <tr>
    <td valign="top">haltonfailure</td>
    <td valign="top">Stop the build process if a test fails (errors are
      considered failures as well).</td>
    <td align="center" valign="top">No; default is <code>off</code>.</td>
  </tr>
<tr>
    <td valign="top">failureproperty</td>
    <td valign="top">The name of a property to set in the event of a failure
      (errors are considered failures as well).</td>
    <td align="center" valign="top">No.</td>
</tr>
  <tr>
    <td valign="top">filtertrace</td>
    <td valign="top">Filter out Junit and Ant stack frames from error and failure stack traces.</td>
    <td align="center" valign="top">No; default is <code>on</code>.</td>
  </tr>
  <tr>
    <td valign="top">timeout</td>
    <td valign="top">Cancel the individual tests if they don't finish
      in the given time (measured in milliseconds).  Ignored if
      <code>fork</code> is disabled.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">maxmemory</td>
    <td valign="top">Maximum amount of memory to allocate to the forked VM.
      Ignored if <code>fork</code> is disabled.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">jvm</td>
    <td valign="top">The command used to invoke the Java Virtual Machine,
      default is 'java'.  The command is resolved by
      <code>java.lang.Runtime.exec()</code>.
      Ignored if <code>fork</code> is disabled.</td>
    <td align="center" valign="top">No; default is <code>java</code>.</td>
  </tr>
  <tr>
    <td valign="top">dir</td>
    <td valign="top">The directory in which to invoke the VM. Ignored if
      <code>fork</code> is disabled.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">newenvironment</td>
    <td valign="top">Do not propagate the old environment when new
      environment variables are specified. Ignored if <code>fork</code> is
      disabled.</td>
    <td align="center" valign="top">No; default is <code>false</code>.</td>
  </tr>
  <tr>
    <td valign="top">includeantruntime</td>
    <td valign="top">Implicitly add the Ant classes required to run
      the tests and JUnit to the classpath in forked mode.
      <b>Note:</b> Please read the <a
      href="../../faq.html#junit-no-runtime-xml">Ant FAQ</a> if you
      want to set this to <code>false</code> and use the XML formatter
      at the same time.</td>
    <td align="center" valign="top">No; default is <code>true</code>.</td>
  </tr>
  <tr>
    <td valign="top">showoutput</td>
    <td valign="top">Send any output generated by tests to Ant's
      logging system as well as to the formatters.  By default only the
      formatters receive the output.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">outputtoformatters</td>
    <td valign="top">
      <em>Since Ant 1.7.0.</em><br/>
      Send any output generated by tests to the test formatters.
      This is "true" by default.
    </td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">tempdir</td>
    <td valign="top">Where Ant should place temporary files.
      <em>Since Ant 1.6</em>.</td>
    <td align="center" valign="top">No; default is the project's base
      directory.</td>
  </tr>
  <tr>
    <td valign="top">reloading</td>
    <td valign="top">Whether or not a new classloader should be instantiated for each test case.<br>
    Ignore if <code>fork</code> is set to true.
      <em>Since Ant 1.6</em>.</td>
    <td align="center" valign="top">No; default is <code>true</code>.</td>
  </tr>
  <tr>
    <td valign="top">clonevm</td>
    <td valign="top">If set to true true, then all system properties
      and the bootclasspath of the forked Java Virtual Machine will be
      the same as those of the Java VM running Ant.  Default is
      &quot;false&quot; (ignored if fork is disabled).
      <em>since Ant 1.7</em></td>
    <td align="center" valign="top">No</td>
  </tr>
</table>

<p>By using the <code>errorproperty</code> and <code>failureproperty</code>
attributes, it is possible to
perform setup work (such as starting an external server), execute the test,
clean up, and still fail the build in the event of a failure.</p>

<p>The <code>filtertrace</code> attribute condenses error and failure
stack traces before reporting them.
It works with both the plain and XML formatters.  It filters out any lines
that begin with the following string patterns:<pre>
   "junit.framework.TestCase"
   "junit.framework.TestResult"
   "junit.framework.TestSuite"
   "junit.framework.Assert."
   "junit.swingui.TestRunner"
   "junit.awtui.TestRunner"
   "junit.textui.TestRunner"
   "java.lang.reflect.Method.invoke("
   "sun.reflect."
   "org.apache.tools.ant."</pre></p>

<h3><a name="nested">Nested Elements</a></h3>

<p>The <code>&lt;junit&gt;</code> task
supports a nested <code>&lt;classpath&gt;</code>
element that represents a <a href="../using.html#path">PATH like
structure</a>.</p>

<p>As of Ant 1.7, this classpath may be used to refer to <code>junit.jar</code>
as well as your tests and the tested code.

<h4>jvmarg</h4>

<p>If <code>fork</code> is enabled, additional parameters may be passed to
the new VM via nested <code>&lt;jvmarg&gt;</code> elements. For example:</p>

<pre>
&lt;junit fork=&quot;yes&quot;&gt;
  &lt;jvmarg value=&quot;-Djava.compiler=NONE&quot;/&gt;
  ...
&lt;/junit&gt;
</pre>

<p>would run the test in a VM without JIT.</p>

<p><code>&lt;jvmarg&gt;</code> allows all attributes described in <a
href="../using.html#arg">Command-line Arguments</a>.</p>

<h4>sysproperty</h4>

<p>Use nested <code>&lt;sysproperty&gt;</code> elements to specify system
properties required by the class. These properties will be made available
to the VM during the execution of the test (either ANT's VM or the forked VM,
if <code>fork</code> is enabled).
The attributes for this element are the same as for <a href="../CoreTasks/exec.html#env">environment variables</a>.</p>

<pre>
&lt;junit fork=&quot;no&quot;&gt;
  &lt;sysproperty key=&quot;basedir&quot; value=&quot;/Users/<USER>/dev/asf/ant-core&quot;/&gt;
  ...
&lt;/junit&gt;
</pre>

<p>would run the test in ANT's VM and make the <code>basedir</code> property
available to the test.</p>

<h4>syspropertyset</h4>

<p>You can specify a set of properties to be used as system properties
with <a href="../CoreTypes/propertyset.html">syspropertyset</a>s.</p>

<p><em>since Ant 1.6</em>.</p>

<h4>env</h4>

<p>It is possible to specify environment variables to pass to the
forked VM via nested <code>&lt;env&gt;</code> elements. For a description
of the <code>&lt;env&gt;</code> element's attributes, see the
description in the <a href="../CoreTasks/exec.html#env">exec</a> task.</p>

<p>Settings will be ignored if <code>fork</code> is disabled.</p>

<h4>bootclasspath</h4>

<p>The location of bootstrap class files can be specified using this
<a href="../using.html#path">PATH like structure</a> - will be ignored
if <i>fork</i> is not <code>true</code> or the target VM doesn't
support it (i.e. Java 1.1).</p>

<p><em>since Ant 1.6</em>.</p>

<h4>permissions</h4>
<p>Security permissions can be revoked and granted during the execution of the 
class via a nested <i>permissions</i> element. For more information please
see <a href="../CoreTypes/permissions.html">permissions</a></p>

<p>Settings will be ignored if fork is enabled.</p>

<p><em>since Ant 1.6</em>.</p>
 
<h4>assertions</h4>

<p>You can control enablement of Java 1.4 assertions with an
<a href="../CoreTypes/assertions.html"><tt>&lt;assertions&gt;</tt></a>
subelement.</p>
 
<p>Assertion statements are currently ignored in non-forked mode.</p>

<p><em>since Ant 1.6.</em></p>

<h4>formatter</h4>

<p>The results of the tests can be printed in different
formats. Output will always be sent to a file, unless you set the
<code>usefile</code> attribute to <code>false</code>.
The name of the file is determined by the
name of the test and can be set by the <code>outfile</code> attribute
of <code>&lt;test&gt;</code>.</p>

<p>There are three predefined formatters - one prints the test results
in XML format, the other emits plain text.  The formatter named
<code>brief</code> will only print detailed information for testcases
that failed, while <code>plain</code> gives a little statistics line
for all test cases.  Custom formatters that need to implement
<code>org.apache.tools.ant.taskdefs.optional.junit.JUnitResultFormatter</code>
can be specified.</p>

<p>If you use the XML formatter, it may not include the same output
that your tests have written as some characters are illegal in XML
documents and will be dropped.</p>

<p><b>Note:</b> Please read the <a
href="../../faq.html#junit-no-runtime-xml">Ant FAQ</a> if you want to
set the fork attribute to <code>true</code>, the includeAntRuntime
attribute to <code>false</code> and use the XML formatter at the same
time.</p>

<table border="1" cellpadding="2" cellspacing="0">
<tr>
  <td width="12%" valign="top"><b>Attribute</b></td>
  <td width="78%" valign="top"><b>Description</b></td>
  <td width="10%" valign="top"><b>Required</b></td>
</tr>
  <tr>
    <td valign="top">type</td>
    <td valign="top">Use a predefined formatter (either
      <code>xml</code>, <code>plain</code>, or <code>brief</code>).</td>
    <td align="center" rowspan="2">Exactly one of these.</td>
  </tr>
  <tr>
    <td valign="top">classname</td>
    <td valign="top">Name of a custom formatter class.</td>
  </tr>
  <tr>
    <td valign="top">extension</td>
    <td valign="top">Extension to append to the output filename.</td>
    <td align="center">Yes, if <code>classname</code> has been used.</td>
  </tr>
  <tr>
    <td valign="top">usefile</td>
    <td valign="top">Boolean that determines whether output should be
      sent to a file.</td>
    <td align="center">No; default is <code>true</code>.</td>
  </tr>
  <tr>
    <td valign="top">if</td>
    <td valign="top">Only use formatter if the named property is set.</td>
    <td align="center">No; default is <code>true</code>.</td>
  </tr>
  <tr>
    <td valign="top">unless</td>
    <td valign="top">Only use formatter if the named property is <b>not</b> set.</td>
    <td align="center">No; default is <code>true</code>.</td>
  </tr>  
</table>

<h4>test</h4>

<p>Defines a single test class.</p>

<table border="1" cellpadding="2" cellspacing="0">
<tr>
  <td width="12%" valign="top"><b>Attribute</b></td>
  <td width="78%" valign="top"><b>Description</b></td>
  <td width="10%" valign="top"><b>Required</b></td>
</tr>
  <tr>
    <td valign="top">name</td>
    <td valign="top">Name of the test class.</td>
    <td align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">fork</td>
    <td valign="top">Run the tests in a separate VM.
      Overrides value set in <code>&lt;junit&gt;</code>.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">haltonerror</td>
    <td valign="top">Stop the build process if an error occurs during the test
       run. Overrides value set in <code>&lt;junit&gt;</code>.</td>
    <td align="center" valign="top">No</td>
  </tr>
<tr>
    <td valign="top">errorproperty</td>
    <td valign="top">The name of a property to set in the event of an error.
      Overrides value set in <code>&lt;junit&gt;</code>.</td>
    <td align="center" valign="top">No</td>
</tr>
  <tr>
    <td valign="top">haltonfailure</td>
    <td valign="top">Stop the build process if a test fails (errors are
      considered failures as well).  Overrides value set in
      <code>&lt;junit&gt;</code>.</td>
    <td align="center" valign="top">No</td>
  </tr>
<tr>
    <td valign="top">failureproperty</td>
    <td valign="top">The name of a property to set in the event of a failure
      (errors are considered failures as well). Overrides value set in
      <code>&lt;junit&gt;</code>.</td>
    <td align="center" valign="top">No</td>
</tr>
  <tr>
    <td valign="top">filtertrace</td>
    <td valign="top">Filter out Junit and Ant stack frames from error and failure stack
    traces.  Overrides value set in <code>&lt;junit&gt;</code>.</td>
    <td align="center" valign="top">No; default is <code>on</code>.</td>
  </tr>
  <tr>
    <td valign="top">todir</td>
    <td valign="top">Directory to write the reports to.</td>
    <td align="center" valign="top">No; default is the current directory.</td>
  </tr>
  <tr>
    <td valign="top">outfile</td>
    <td valign="top">Base name of the test result. The full filename is
      determined by this attribute and the extension of
      <code>formatter</code>.</td>
    <td align="center" valign="top">No; default is
      <code>TEST-</code><em>name</em>, where <em>name</em> is the name of
      the test specified in the <code>name</code> attribute.</td>
  </tr>
  <tr>
    <td valign="top">if</td>
    <td valign="top">Only run test if the named property is set.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">unless</td>
    <td valign="top">Only run test if the named property is <b>not</b> set.</td>
    <td align="center" valign="top">No</td>
  </tr>
</table>

<p>Tests can define their own formatters via nested
<code>&lt;formatter&gt;</code> elements.</p>

<h4>batchtest</h4>

<p>Define a number of tests based on pattern matching.</p>

<p><code>batchtest</code> collects the included <a href="../CoreTypes/resources.html">resources</a> from any number
of nested <a
href="../CoreTypes/resources.html#collection">Resource Collection</a>s. It then
generates a test class name for each resource that ends in
<code>.java</code> or <code>.class</code>.</p>

<p>Any type of Resource Collection is supported as a nested element,
prior to Ant 1.7 only <code>&lt;fileset&gt;</code> has been
supported.</p>

<table border="1" cellpadding="2" cellspacing="0">
<tr>
  <td width="12%" valign="top"><b>Attribute</b></td>
  <td width="78%" valign="top"><b>Description</b></td>
  <td width="10%" valign="top"><b>Required</b></td>
</tr>
  <tr>
    <td valign="top">fork</td>
    <td valign="top">Run the tests in a separate VM.
      Overrides value set in <code>&lt;junit&gt;</code>.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">haltonerror</td>
    <td valign="top">Stop the build process if an error occurs during the test
       run. Overrides value set in <code>&lt;junit&gt;</code>.</td>
    <td align="center" valign="top">No</td>
  </tr>
<tr>
    <td valign="top">errorproperty</td>
    <td valign="top">The name of a property to set in the event of an error.
      Overrides value set in <code>&lt;junit&gt;</code>.</td>
    <td align="center" valign="top">No</td>
</tr>
  <tr>
    <td valign="top">haltonfailure</td>
    <td valign="top">Stop the build process if a test fails (errors are
      considered failures as well).  Overrides value set in
      <code>&lt;junit&gt;</code>.</td>
    <td align="center" valign="top">No</td>
  </tr>
<tr>
    <td valign="top">failureproperty</td>
    <td valign="top">The name of a property to set in the event of a failure
      (errors are considered failures as well). Overrides value set in
      <code>&lt;junit&gt;</code></td>
    <td align="center" valign="top">No</td>
</tr>
  <tr>
    <td valign="top">filtertrace</td>
    <td valign="top">Filter out Junit and Ant stack frames from error and failure stack
    traces.  Overrides value set in <code>&lt;junit&gt;</code>.</td>
    <td align="center" valign="top">No; default is <code>on</code>.</td>
  </tr>
  <tr>
    <td valign="top">todir</td>
    <td valign="top">Directory to write the reports to.</td>
    <td align="center" valign="top">No; default is the current directory.</td>
  </tr>
  <tr>
    <td valign="top">if</td>
    <td valign="top">Only run tests if the named property is set.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">unless</td>
    <td valign="top">Only run tests if the named property is <strong>not</strong> set.</td>
    <td align="center" valign="top">No</td>
  </tr>
</table>

<p>Batchtests can define their own formatters via nested
<code>&lt;formatter&gt;</code> elements.</p>

<h3>Examples</h3>

<pre>
&lt;junit&gt;
  &lt;test name="my.test.TestCase"/&gt;
&lt;/junit&gt;
</pre>

<p>Runs the test defined in <code>my.test.TestCase</code> in the same
VM. No output will be generated unless the test fails.</p>

<pre>
&lt;junit printsummary="yes" fork="yes" haltonfailure="yes"&gt;
  &lt;formatter type="plain"/&gt;
  &lt;test name="my.test.TestCase"/&gt;
&lt;/junit&gt;
</pre>

<p>Runs the test defined in <code>my.test.TestCase</code> in a
separate VM.  At the end of the test, a one-line summary will be
printed. A detailed report of the test can be found in
<code>TEST-my.test.TestCase.txt</code>. The build process will be
stopped if the test fails.</p>

<pre>
&lt;junit printsummary="yes" haltonfailure="yes"&gt;
  &lt;classpath&gt;
    &lt;pathelement location="build/testcases"/&gt;
    &lt;pathelement path="/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-launcher.jar:/System/Library/Frameworks/JavaVM.framework/Versions/1.5.0/Classes/.compatibility/14compatibility.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/activation.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/ant-antunit-1.0Beta2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/antlrall.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bcel.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bsf-2.4.0.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bsh-2.0b4.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bsh-core-2.0b4.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/commons-logging-api.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/commons-logging.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/commons-net-1.4.0.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jai_codec.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jai_core.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jakarta-oro-2.0.8.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jakarta-regexp-1.2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jdepend.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/js-1.6R3.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jsch-0.1.29.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/junit-3.8.2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jython.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/log4j-1.2.9.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/mail.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/NetRexxC.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/NetRexxR.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/resolver.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/starteam-sdk.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/stylebook-1.0-b2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/weblogic.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/weblogicaux.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/weblogicclasses.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xalan.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xalan1.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xercesSamples.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xmlParserAPIs.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xsltc.jar:/Users/<USER>/dev/asf/ant-core/lib/optional:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-antlr.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-bcel.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-bsf.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-log4j.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-oro.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-regexp.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-resolver.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-commons-logging.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-commons-net.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jai.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-javamail.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jdepend.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jmf.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jsch.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-junit.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-launcher.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-netrexx.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-nodeps.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-starteam.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-stylebook.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-swing.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-testutil.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-trax.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-weblogic.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/xercesImpl.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/xml-apis.jar"/&gt;
  &lt;/classpath&gt;

  &lt;formatter type="plain"/&gt;

  &lt;test name="my.test.TestCase" haltonfailure="no" outfile="result"&gt;
    &lt;formatter type="xml"/&gt;
  &lt;/test&gt;

  &lt;batchtest fork="yes" todir="${reports.tests}"&gt;
    &lt;fileset dir="${src.tests}"&gt;
      &lt;include name="**/*Test*.java"/&gt;
      &lt;exclude name="**/AllTests.java"/&gt;
    &lt;/fileset&gt;
  &lt;/batchtest&gt;
&lt;/junit&gt;
</pre>

<p>Runs <code>my.test.TestCase</code> in the same VM, ignoring the
given CLASSPATH; only a warning is printed if this test fails. In
addition to the plain text test results, for this test a XML result
will be output to <code>result.xml</code>.  
Then, for each matching file in the directory defined for
<code>${src.tests}</code> a
test is run in a separate VM. If a test fails, the build process is
aborted. Results are collected in files named
<code>TEST-</code><em>name</em><code>.txt</code> and written to
<code>${reports.tests}</code>.</p>


</body>
</html>
