<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Javah Task</title>
</head>

<body>

<h2><a name="javah">Javah</a></h2>
<h3>Description</h3>
<p>Generates JNI headers from a Java class.</p>
<p> When this task executes, it will generate the C header and source files that
are needed to implement native methods. JNI operates differently depending on
whether <a href="http://java.sun.com/j2se/1.3/docs/tooldocs/win32/javah.html">JDK1.2</a>
(or later) or <a href="http://java.sun.com/products/jdk/1.1/docs/tooldocs/win32/javah.html">pre-JDK1.2</a>
systems are used.</p>

<p>It is possible to use different compilers. This can be selected
with the <code>implementation</code> attribute.  <a
name="implementationvalues">Here are the choices</a>:</p>
<ul>
  <li>default - the default compiler (kaffeh or sun) for the platform.</li>
  <li>sun (the standard compiler of the JDK)</li>
  <li>kaffeh (the native standard compiler of <a href="http://www.kaffe.org" target="_top">Kaffe</a>)</li>
</ul>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td valign="top" align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">class</td>
    <td valign="top">the fully-qualified name of the class (or classes,
      separated by commas)</td>
    <td align="center" valign="top">Yes</td>
  </tr>
  <tr>
    <td valign="top">outputFile</td>
    <td valign="top">concatenates the resulting header or source files for all the classes listed into this file</td>
    <td align="center" valign="middle" rowspan="2">Yes</td>
  </tr>
  <tr>
    <td valign="top">destdir</td>
    <td valign="top">sets the directory where javah saves the header files or the
      stub files.</td>
  </tr>
  <tr>
    <td valign="top">force</td>
    <td valign="top">specifies that output files should always be written (JDK1.2 only)</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">old</td>
    <td valign="top">specifies that old JDK1.0-style header files should be generated
      (otherwise output file contain JNI-style native method      function prototypes) (JDK1.2 only)</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">stubs</td>
    <td valign="top">generate C declarations from the Java object file (used with old)</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">verbose</td>
    <td valign="top">causes Javah to print a message concerning the status     of the generated files</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">classpath</td>
    <td valign="top">the classpath to use.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">bootclasspath</td>
    <td valign="top">location of bootstrap class files.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">extdirs</td>
    <td valign="top"> location of installed extensions.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">implementation</td>
    <td valign="top">The compiler implementation to use.  If this
    attribute is not set, the default compiler for the current VM
    will be used.  (See the above <a
    href="#implementationvalues">list</a> of valid compilers.)</td>
    <td align="center" valign="top">No</td>
  </tr>
</table>
<p>Either outputFile or destdir must be supplied, but not both.&nbsp;</p>

<h3>Parameters specified as nested elements</h3>

<h4>arg</h4>

<p>You can specify additional command line arguments for the compiler
with nested <code>&lt;arg&gt;</code> elements.  These elements are
specified like <a href="../using.html#arg">Command-line Arguments</a>
but have an additional attribute that can be used to enable arguments
only if a given compiler implementation will be used.</p>

<table border="1" cellpadding="2" cellspacing="0">
<tr>
  <td width="12%" valign="top"><b>Attribute</b></td>
  <td width="78%" valign="top"><b>Description</b></td>
  <td width="10%" valign="top"><b>Required</b></td>
</tr>
  <tr>
    <td valign="top">value</td>
    <td align="center" rowspan="4">See
    <a href="../using.html#arg">Command-line Arguments</a>.</td>
    <td align="center" rowspan="4">Exactly one of these.</td>
  </tr>
  <tr>
    <td valign="top">line</td>
  </tr>
  <tr>
    <td valign="top">file</td>
  </tr>
  <tr>
    <td valign="top">path</td>
  </tr>
  <tr>
    <td valign="top">implementation</td>
    <td>Only pass the specified argument if the chosen compiler
    implementation matches the value of this attribute.  Legal values
    are the same as those in the above <a
    href="#implementationvalues">list</a> of valid compilers.)</td>
    <td align="center">No</td>
  </tr>
</table>

<h3>Examples</h3>
<pre>  &lt;javah destdir=&quot;c&quot; class=&quot;org.foo.bar.Wibble&quot;/&gt;</pre>
<p>makes a JNI header of the named class, using the JDK1.2 JNI model. Assuming
the directory 'c' already exists, the file <tt>org_foo_bar_Wibble.h</tt>
is created there. If this file already exists, it is left unchanged.</p>
<pre>  &lt;javah outputFile=&quot;wibble.h&quot;&gt;
    &lt;class name=&quot;org.foo.bar.Wibble,org.foo.bar.Bobble&quot;/&gt;
  &lt;/javah&gt;</pre>
<p>is similar to the previous example, except the output is written to a file
called <tt>wibble.h</tt>
in the current directory.</p>
<pre>  &lt;javah destdir=&quot;c&quot; force=&quot;yes&quot;&gt;
    &lt;class name=&quot;org.foo.bar.Wibble&quot;/&gt;
    &lt;class name=&quot;org.foo.bar.Bobble&quot;/&gt;
    &lt;class name=&quot;org.foo.bar.Tribble&quot;/&gt;
  &lt;/javah&gt;</pre>
<p>writes three header files, one for each of the classes named. Because the
force option is set, these header files are always written when the Javah task
is invoked, even if they already exist.</p>
<pre>  &lt;javah destdir=&quot;c&quot; verbose=&quot;yes&quot; old=&quot;yes&quot; force=&quot;yes&quot;&gt;
    &lt;class name=&quot;org.foo.bar.Wibble&quot;/&gt;
    &lt;class name=&quot;org.foo.bar.Bobble&quot;/&gt;
    &lt;class name=&quot;org.foo.bar.Tribble&quot;/&gt;
  &lt;/javah&gt;
  &lt;javah destdir=&quot;c&quot; verbose=&quot;yes&quot; stubs=&quot;yes&quot; old=&quot;yes&quot; force=&quot;yes&quot;&gt;
    &lt;class name=&quot;org.foo.bar.Wibble&quot;/&gt;
    &lt;class name=&quot;org.foo.bar.Bobble&quot;/&gt;
    &lt;class name=&quot;org.foo.bar.Tribble&quot;/&gt;
  &lt;/javah&gt;</pre>
<p>writes the headers for the three classes using the 'old' JNI format, then
writes the corresponding .c stubs. The verbose option will cause Javah to
describe its progress.</p>


</body>

</html>
