<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>jarlib-manifest Task</title>
</head>

<body>

<h2><a name="jarlib-manifest">jarlib-manifest</a></h2>
<h3>Description</h3>
<p>Task to generate a manifest that declares all the dependencies
 in manifest. The dependencies are determined by looking in the
 specified path and searching for Extension / "Optional Package"
 specifications in the manifests of the jars.</p>

<p>Note that this task
works with extensions as defined by the "Optional Package" specification.
 For more information about optional packages, see the document
<em>Optional Package Versioning</em> in the documentation bundle for your
Java2 Standard Edition package, in file
<code>guide/extensions/versioning.html</code> or online at
<a href="http://java.sun.com/j2se/1.3/docs/guide/extensions/versioning.html">
http://java.sun.com/j2se/1.3/docs/guide/extensions/versioning.html</a>.</p>
<p>See the Extension and ExtensionSet documentation for further details</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">destfile</td>
    <td valign="top">The file to generate Manifest into</td>
    <td valign="top" align="center">Yes.</td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>

<h4>extension</h4>
 <p><a href="../OptionalTypes/extension.html">Extension</a> the extension
 that this library implements.</p>

<h4>depends</h4>
 <p><a href="../OptionalTypes/extensionset.html">ExtensionSet</a>s containing
 all dependencies for jar.</p>

<h4>options</h4>
 <p><a href="../OptionalTypes/extensionset.html">ExtensionSet</a>s containing
 all optional dependencies for jar. (Optional dependencies will be used if
 present else they will be ignored)</p>

<h3>Examples</h3>
<p><b>Basic Manifest generated for single Extension</b></p>
<pre>
&lt;extension id=&quot;e1&quot;
    extensionName=&quot;MyExtensions&quot;
    specificationVersion=&quot;1.0&quot;
    specificationVendor=&quot;Peter Donald&quot;
    implementationVendorID=&quot;vv&quot;
    implementationVendor=&quot;Apache&quot;
    implementationVersion=&quot;2.0&quot;
    implementationURL=&quot;http://somewhere.com&quot;/&gt;

&lt;jarlib-manifest destfile=&quot;myManifest.txt&quot;&gt;
    &lt;extension refid=&quot;e1&quot;/&gt;
&lt;/jarlib-manifest&gt;
</pre>

<p><b>Search for extension in fileset</b></p>
<p><b>A large example with required and optional dependencies</b></p>
<pre>
&lt;extension id=&quot;e1&quot;
    extensionName=&quot;MyExtensions&quot;
    specificationVersion=&quot;1.0&quot;
    specificationVendor=&quot;Peter Donald&quot;
    implementationVendorID=&quot;vv&quot;
    implementationVendor=&quot;Apache&quot;
    implementationVersion=&quot;2.0&quot;
    implementationURL=&quot;http://somewhere.com&quot;/&gt;

&lt;extensionSet id=&quot;option.ext&quot;&gt;
    &lt;libfileset dir=&quot;lib/option&quot;&gt;
        &lt;include name=&quot;**/*.jar&quot;/&gt;
    &lt;/libfileset&gt;
&lt;/extensionSet&gt;

&lt;extensionSet id=&quot;depends.ext&quot;&gt;
    &lt;libfileset dir=&quot;lib/required&quot;&gt;
        &lt;include name=&quot;*.jar&quot;/&gt;
    &lt;/libfileset&gt;
&lt;/extensionSet&gt;

&lt;jarlib-manifest destfile=&quot;myManifest.txt&quot;&gt;
    &lt;extension refid=&quot;e1&quot;/&gt;
    &lt;depends refid=&quot;depends.ext&quot;/&gt;
    &lt;options refid=&quot;option.ext&quot;/&gt;
&lt;/jarlib-manifest&gt;
</pre>



</body>
</html>

