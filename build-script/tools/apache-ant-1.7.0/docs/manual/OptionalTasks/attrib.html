<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Ant User Manual</title>
</head>

<body>

<h2><a name="attrib">Attrib</a></h2>
<p><em>Since Ant 1.6.</em></p>
<h3>Description</h3>

<p>Changes the attributes of a file or all files inside specified
directories.  Right now it has effect only under Windows. Each of the
4 possible permissions has its own attribute, matching the arguments
for the attrib command.</p>

<p><a href="../CoreTypes/fileset.html">FileSet</a>s, 
<a href="../CoreTypes/dirset.html">DirSet</a>s or <a
href="../CoreTypes/filelist.html">FileList</a>s can be specified using
nested <code>&lt;fileset&gt;</code>, <code>&lt;dirset&gt;</code> and 
<code>&lt;filelist&gt;</code> elements.</p>

<p>Starting with Ant 1.7, this task supports arbitrary <a
href="../CoreTypes/resources.html#collection">Resource Collection</a>s
as nested elements.</p>

<p>By default this task will use a single invocation of the underlying
attrib command.  If you are working on a large number of files this
may result in a command line that is too long for your operating
system.  If you encounter such problems, you should set the
maxparallel attribute of this task to a non-zero value.  The number to
use highly depends on the length of your file names (the depth of your
directory tree), so you'll have to experiment a little.</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">the file or directory of which the permissions must be
    changed.</td>
    <td valign="top" valign="middle">Yes or nested
    <code>&lt;fileset/list&gt;</code> elements.</td>
  </tr>
  <tr>
    <td valign="top">readonly</td>
    <td valign="top">the readonly permission.</td>
    <td valign="top" rowspan="4">at least one of the four. </td>
  </tr>
  <tr>
    <td valign="top">archive</td>
    <td valign="top">the archive permission.</td>
  </tr>
  <tr>
    <td valign="top">system</td>
    <td valign="top">the system permission.</td>
  </tr>
  <tr>
    <td valign="top">hidden</td>
    <td valign="top">the hidden permission.</td>
  </tr>
  <tr>
    <td valign="top">type</td>
    <td valign="top">One of <i>file</i>, <i>dir</i> or <i>both</i>. If set to
      <i>file</i>, only the permissions of plain files are going to be changed.
      If set to <i>dir</i>, only the directories are considered.<br>
      <strong>Note:</strong> The type attribute does not apply to
      nested <i>dirset</i>s - <i>dirset</i>s always implicitly
      assume type to be <i>dir</i>.</td>
    <td align="center" valign="top">No, default is <i>file</i></td>
  </tr>
  <tr>
    <td valign="top">verbose</td>
    <td valign="top">Whether to print a summary after execution or not.
      Defaults to <code>false</code>.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">parallel</td>
    <td valign="top">process all specified files using a single
      <code>chmod</code> command. Defaults to true.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">maxparallel</td>
    <td valign="top">Limit the amount of parallelism by passing at
      most this many sourcefiles at once.  Set it to &lt;= 0 for
      unlimited.  Defaults to unlimited.  <em>Since Ant 1.6.</em></td>
    <td align="center" valign="top">No</td>
  </tr>
</table>
<h3>Examples</h3>
  <blockquote>
<pre>&lt;attrib file=&quot;${dist}/run.bat&quot; readonly=&quot;true&quot; hidden=&quot;true&quot;/&gt;</pre>
</blockquote>
<p>makes the &quot;run.bat&quot; file read-only and hidden.</p>
<blockquote>
  <pre>&lt;attrib readonly=&quot;false&quot;&gt;
  &lt;fileset dir=&quot;${meta.inf}&quot; includes=&quot;**/*.xml&quot;/&gt;
&lt;attrib&gt;
</pre>
</blockquote>
<p>makes all &quot;.xml&quot; files below <code>${meta.inf}</code> readable.</p>
<blockquote>
  <pre>
&lt;attrib readonly=&quot;true&quot; archive=&quot;true&quot;&gt;
  &lt;fileset dir=&quot;shared/sources1&quot;&gt;
    &lt;exclude name=&quot;**/trial/**&quot;/&gt;
  &lt;/fileset&gt;
  &lt;fileset refid=&quot;other.shared.sources&quot;/&gt;
&lt;/attrib&gt;
</pre>
</blockquote>
<p>makes all files below <code>shared/sources1</code> (except those below any
  directory named trial) read-only and archived. In addition all files belonging
  to a FileSet with <code>id</code> <code>other.shared.sources</code> get the
  same attributes.</p>


</body>
</html>

