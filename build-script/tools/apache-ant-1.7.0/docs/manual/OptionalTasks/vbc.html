<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
    
<html>
<head>
  <meta http-equiv="Content-Language" content="en-us">
  <title>Vbc
 Task</title>
</head>

<body bgcolor="#ffffff" text="#000000" link="#525D76"
      alink="#525D76" vlink="#525D76">

<table border="0" width="100%" cellspacing="4">

  <!-- <PERSON>GE HEADER -->
  <tr>
    <td>
      <table border="0" width="100%"><tr>
          <td valign="bottom">
            <font size="+3" face="arial,helvetica,sanserif"><strong>Vbc
 Task</strong></font>
            <br><font face="arial,helvetica,sanserif">This task compiles Visual Basic.NET source into executables or modules.  <p>For historical reasons the pattern <code>**/*.vb</code> is preset as includes list and you can not override it with an explicit includes attribute.  Use nested <code>&lt;src&gt;</code> elements instead of the basedir attribute if you need more control.</p></font>
          </td>
          <td>
            <!-- PROJECT LOGO -->
            <a href="http://ant.apache.org/">
              <img src="../../images/ant_logo_large.gif" align="right" alt="Apache Ant" border="0">
            </a>
          </td>
      </tr></table>
    </td>
  </tr>

  <!-- START RIGHT SIDE MAIN BODY -->
  <tr>
    <td  valign="top" align="left">

          <!-- Applying task/long-description -->
    <!-- Start Description -->
    <table border="0" cellspacing="0" cellpadding="2" width="100%">
      <tr><td>&nbsp;</td></tr>

      <tr><td bgcolor="#525D76">
        <font color="#ffffff" face="arial,helvetica.sanserif">
          <a name="description">
          <strong>Description</strong></a></font>
      </td></tr>

      <tr><td><blockquote>
        This task compiles Visual Basic.NET source into executables or modules. The task requires vbc.exe on the execute path, unless it or an equivalent program is specified in the <tt>executable</tt> parameter <p> All parameters are optional: <code>&lt;vbc/&gt;</code> should suffice to produce a debug build of all *.vb files. <p> The task is a directory based task, so attributes like <tt>includes=&quot;**\/*.vb&quot;</tt> and <tt>excludes=&quot;broken.vb&quot;</tt> can be used to control the files pulled in. By default, all *.vb files from the project folder down are included in the command. When this happens the destFile -if not specified- is taken as the first file in the list, which may be somewhat hard to control. Specifying the output file with <tt>destfile</tt> is prudent. </p> <p> Also, dependency checking only works if destfile is set. As with <code>&lt;csc&gt;</code> nested <tt>src</tt> filesets of source, reference filesets, definitions and resources can be provided. <p> Example </p> <pre>&lt;vbc optimize=&quot;true&quot; debug=&quot;false&quot; warnLevel=&quot;4&quot; targetType=&quot;exe&quot; definitions=&quot;RELEASE&quot; excludes=&quot;src/unicode_class.vb&quot; mainClass = &quot;MainApp&quot; destFile=&quot;NetApp.exe&quot; optionExplicit=&quot;true&quot; optionCompare=&quot;text&quot; references="System.Xml,System.Web.Xml" &gt; &lt;reference file="${testCSC.dll}"/&gt; &lt;define name="RELEASE"/&gt; &lt;define name="DEBUG" if="debug.property"/&gt; &lt;define name="def3" unless="def2.property"/&gt; &lt;/vbc&gt; </pre>
      </blockquote></td></tr>

    </table>
    <!-- End Description -->

    <!-- Start Attributes -->
    <table border="0" cellspacing="0" cellpadding="2" width="100%">
      <tr><td>&nbsp;</td></tr>
      <tr><td bgcolor="#525D76">
        <font color="#ffffff" face="arial,helvetica.sanserif">
          <a name="attributes">
          <strong>Parameters</strong></a></font>
      </td></tr>
      <tr><td><blockquote>
        <table>
          <tr>
        <td bgcolor="#cccccc" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif"><b>Attribute</b></font>
        </td>
        <td bgcolor="#cccccc" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif"><b>Description</b></font>
        </td>
        <td bgcolor="#cccccc" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif"><b>Type</b></font>
        </td>
        <td bgcolor="#cccccc" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif"><b>Requirement</b></font>
        </td>
          </tr>
    <!-- Attribute Group -->    
    
    <!-- Attribute Group -->    
        <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">additionalmodules</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Semicolon separated list of modules to refer to.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">String</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left" rowspan="24">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Optional</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">debug</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">set the debug flag on or off.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">boolean</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">destdir</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Set the destination directory of files to be compiled.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">File</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">destfile</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Set the name of exe/library to create.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">File</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">executable</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">set the name of the program, overriding the defaults. Can be used to set the full path to a program, or to switch to an alternate implementation of the command, such as the Mono or Rotor versions -provided they use the same command line arguments as the .NET framework edition</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">String</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">extraoptions</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Any extra options which are not explicitly supported by this task.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">String</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">failonerror</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">If true, fail on compilation errors.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">boolean</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">imports</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Declare global imports for namespaces in referenced metadata files.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">String</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">includedefaultreferences</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">If true, automatically includes the common assemblies in dotnet, and tells the compiler to link in mscore.dll. set the automatic reference inclusion flag on or off this flag controls the /nostdlib option in CSC</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">boolean</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">mainclass</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Sets the name of main class for executables.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">String</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">optimize</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">If true, enables optimization flag.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">boolean</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">optioncompare</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Specify binary- or text-style string comparisons. Defaults to "binary"</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">String</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">optionexplicit</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Whether to require explicit declaration of variables.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">boolean</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">optionstrict</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Enforce strict language semantics.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">boolean</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">referencefiles</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Path of references to include. Wildcards should work.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Path</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">references</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Semicolon separated list of DLLs to refer to.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">String</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">removeintchecks</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Whether to remove integer checks. Default false.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">boolean</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">rootnamespace</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Specifies the root namespace for all type declarations.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">String</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">srcdir</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Set the source directory of the files to be compiled.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">File</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">targettype</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">set the target type to one of exe|library|module|winexe</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">"exe", "library", "module", "winexe"</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">utf8output</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">If true, require all compiler output to be in UTF8 format.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">boolean</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">warnlevel</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Level of warning currently between 1 and 4 with 4 being the strictest.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">int</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">win32icon</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Set the filename of icon to include.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">File</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">win32res</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Sets the filename of a win32 resource (.RES) file to include. This is not a .NET resource, but what Windows is used to.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">File</font>
        </td>
    </tr>


        </table>
      </blockquote></td></tr>

    </table>
    <!-- End Attributes -->

    <!-- Start Elements -->
    <table border="0" cellspacing="0" cellpadding="2" width="100%">
      <tr><td>&nbsp;</td></tr>

      <tr><td bgcolor="#525D76">
        <font color="#ffffff" face="arial,helvetica.sanserif">
          <a name="elements">
          <strong>Parameters as nested elements</strong></a></font>
      </td></tr>

      <tr><td><blockquote>
    <!-- Start Element -->
    <table border="0" cellspacing="0" cellpadding="2" width="100%">
      <tr><td>&nbsp;</td></tr>
      <tr><td bgcolor="#828DA6">
        <font color="#ffffff" face="arial,helvetica.sanserif" size="-1">
          <strong>define</strong> (org.apache.tools.ant.taskdefs.optional.dotnet.DotnetDefine)</font>
      </td></tr>
      <tr><td><blockquote>
        add a define to the list of definitions
      </blockquote></td></tr>
    </table>
    <!-- End Element -->
    <!-- Start Element -->
    <table border="0" cellspacing="0" cellpadding="2" width="100%">
      <tr><td>&nbsp;</td></tr>
      <tr><td bgcolor="#828DA6">
        <font color="#ffffff" face="arial,helvetica.sanserif" size="-1">
          <strong>reference</strong> (org.apache.tools.ant.types.FileSet)</font>
      </td></tr>
      <tr><td><blockquote>
        add a new reference fileset to the compilation
      </blockquote></td></tr>
    </table>
    <!-- End Element -->
    <!-- Start Element -->
    <table border="0" cellspacing="0" cellpadding="2" width="100%">
      <tr><td>&nbsp;</td></tr>
      <tr><td bgcolor="#828DA6">
        <font color="#ffffff" face="arial,helvetica.sanserif" size="-1">
          <strong>resource</strong> (org.apache.tools.ant.taskdefs.optional.dotnet.DotnetResource)</font>
      </td></tr>
      <tr><td><blockquote>
        link or embed a resource
      </blockquote></td></tr>
    </table>
    <!-- End Element -->
    <!-- Start Element -->
    <table border="0" cellspacing="0" cellpadding="2" width="100%">
      <tr><td>&nbsp;</td></tr>
      <tr><td bgcolor="#828DA6">
        <font color="#ffffff" face="arial,helvetica.sanserif" size="-1">
          <strong>src</strong> (org.apache.tools.ant.types.FileSet)</font>
      </td></tr>
      <tr><td><blockquote>
        add a new source directory to the compile
      </blockquote></td></tr>
    </table>
    <!-- End Element -->

      </blockquote></td></tr>

    </table>
    <!-- End Elements -->


    </td>
  </tr>
  <!-- END RIGHT SIDE MAIN BODY -->

</table>

</body>
</html>
