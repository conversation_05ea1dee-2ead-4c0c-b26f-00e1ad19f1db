<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Borland EJB Tasks</title>
</head>

<body>

<h2><a name="log">BorlandDeploy</a>Tool</h2>
<p>by <PERSON><PERSON> (<a href="mailto:<EMAIL>"><EMAIL></a>)</p>


<h3>Description</h3>
<p>The BorlandDeployTool is a vendor specific nested element for the Ejbjar optional task.</p>
<p>BorlandDeploymentTool is dedicated to the Borland Application Server 4.5.x and Borland
  Enterprise Server 5.x. It  generates and compiles the stubs and skeletons for all ejb described into the
  Deployment Descriptor, builds the jar file including the support files and
  verify whether the produced jar is valid or not.</p>

<p>Benoit Moussaud maintains a separate <a
href="http://www.moussaud.org/ejbjar.html">FAQ</a> for this task at
his homepage.</p>

<h3>Borland element</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top" width="63"><b>Attribute</b></td>
    <td valign="top" width="915"><b>Description</b></td>
    <td align="center" valign="top" width="62"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top" width="63">destdir</td>
    <td valign="top" width="915">The base directory into which the generated borland
      ready jar files are deposited</td>
    <td align="center" valign="middle" width="62">yes</td>
  </tr>
  <tr>
    <td valign="top" width="63">debug</td>
    <td valign="top" width="915">If true, turn on the debug mode for each borland
      tools (java2iiop, iastool ...) default = false</td>
    <td align="center" valign="middle" width="62">no</td>
  </tr>
  <tr>
    <td valign="top" width="63">verify</td>
    <td valign="top" width="915">If true, turn on the verification at the end
      of the jar production (default = false)</td>
    <td align="center" valign="middle" width="62">no</td>
  </tr>
  <tr>
    <td valign="top" width="63">verifyargs</td>
    <td valign="top" width="915">extra parameter for verify command</td>
    <td align="center" valign="middle" width="62">no</td>
  </tr>
  <tr>
    <td valign="top" width="63">suffix</td>
    <td valign="top" width="915">String value appended to the basename of the
      deployment descriptor to create the filename of the Borland EJB jar file.</td>
    <td align="center" valign="middle" width="62">No, defaults to '-ejb.jar'.</td>
  </tr>
  <tr>
    <td valign="top" width="63">basdtd</td>
    <td valign="top" width="915"><b>Deprecated</b>. Defines the location of the
      DTD which covers the Borland specific deployment descriptors.
      This should not be necessary if you have borland in your classpath. If you
      do not, you should use a nested
      <a href="ejb.html#ejbjar-dtd"><code>&lt;dtd&gt;</code></a> element, described
      in the ejbjar task documentation.</td>
    <td align="center" valign="middle" width="62">no</td>
  </tr>
  <tr>
    <td valign="top" width="63">ejbdtd</td>
    <td valign="top" width="915"><b>Deprecated</b>. Defines the location of the
      ejb-jar DTD in the class hierarchy. This should not be necessary
      if you have borland in your classpath. If you do not, you should use a
      nested <a href="ejb.html#ejbjar-dtd"><code>&lt;dtd&gt;</code></a> element,
      described in the ejbjar task
      documentation. </td>
    <td align="center" valign="middle" width="62">no</td>
  </tr>
  <tr>
    <td valign="top" width="63">generateclient </td>
    <td valign="top" width="915">If true, turn on the generation of the corresponding
      ejbjar (default = false)</td>
    <td align="center" valign="middle" width="62">no</td>
  </tr>
  <tr>
    <td valign="top" width="63">version</td>
    <td valign="top" width="915">set the Borland Application Version.
            <ul>
              <li>4 means B.A.S (Borland Application Server) 4.x, target will add ejb-inprise.xml file</li>
              <li>5 means B.E.S (Borland Application Server) 5.x, target will add ejb-borland.xml file</li>
            </ul>
          </td>
    <td align="center" valign="middle" width="62">No, defaults to 4</td>
  </tr>
  <tr>
    <td valign="top" width="63">java2iiopParams </td>
    <td valign="top" width="915">If filled, the params are added to the java2iiop command (ex: -no_warn_missing_define)</td>
    <td align="center" valign="middle" width="62">no</td>
  </tr>

</table>

<h3>Examples</h3>
<p>The following build.xml snippet is an example of how to use Borland element
  into the ejbjar task</p>
<pre>    &lt;ejbjar srcdir=&quot;build/classes&quot; basejarname=&quot;vsmp&quot; descriptordir=&quot;${rsc.dir}/hrmanager&quot;&gt;
        &lt;borland destdir=&quot;lib&quot; verify=&quot;on&quot; generateclient=&quot;on&quot; version=&quot;5&quot;&gt;
          &lt;classpath refid=&quot;classpath&quot;/&gt;
        &lt;/borland&gt;
        &lt;include name=&quot;**\ejb-jar.xml&quot;/&gt;
        &lt;support dir=&quot;build/classes&quot;&gt;
          &lt;include name=&quot;demo\*.class&quot;/&gt;
          &lt;include name=&quot;demo\helper\*.class&quot;/&gt;
         &lt;/support&gt;
     &lt;/ejbjar&gt;</pre>
<pre>The borland element will generate into the lib dir an ejb jar file using the deployment descriptor placed into the ${rsc.dir}/hrmanager directory.
The verify phase is turned on and the generate client phase as well.
</pre>

<h3>&nbsp;</h3>

</body>
</html>

