<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>
<head><link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>WLJSPC Task</title></head>
<body>
<h1>wljspc</h1>
<h3>Description</h3>
<p>Class to precompile JSP's using weblogic's jsp compiler (weblogic.jspc)</p>
Tested only on Weblogic 4.5.1 - NT4.0 and Solaris 5.7,5.8<br>
<h3>Parameters</h3>
<table border="1" cellPadding="2" cellSpacing="0">
  <tbody>
    <tr>
      <th>Attribute</th>
      <th>Values</th>
      <th>Required</th>
    </tr>
    <tr>
      <td>src</td>
      <td>root of source tree for JSP, ie, the document root for your weblogic server</td>
      <td>Yes</td>
    </tr>
    <tr>
      <td>
dest</td>
      <td> root of destination directory, what you have set as WorkingDir in the weblogic properties</td>
      <td>Yes</td>
    </tr>
    <tr>
      <td>
package</td>
      <td> start package name under which your JSP's would be compiled</td>
      <td>Yes</td>
    </tr>
    <tr>
      <td>
classpath</td>
      <td>Class path to use when compiling jsp's</td>
      <td>Yes</td>
    </tr>
  </tbody>
</table>
<p>
<br>

A classpath should be set which contains the weblogic classes as well as all application classes<br>
referenced by the JSP. The system classpath is also appended when the jspc is called, so you may<br>
choose to put everything in the classpath while calling Ant. However, since presumably the JSP's will reference<br>
classes being build by Ant, it would be better to explicitly add the classpath in the task<br>
<br>
The task checks timestamps on the JSP's and the generated classes, and compiles<br>
only those files that have changed.<br>
<br>
It follows the weblogic naming convention of putting classes in<br>
<b> _dirName/_fileName.class for dirname/fileName.jsp   </b><br>
<br>
</p>
<h3><br>
Example<br>
</h3>
<p>
<pre>
&lt;target name="jspcompile" depends="compile"&gt;
  &lt;wljspc src="c:\\weblogic\\myserver\\public_html" dest="c:\\weblogic\\myserver\\serverclasses" package="myapp.jsp"&gt
    &lt;classpath&gt;
      &lt;pathelement location="${weblogic.classpath}"/&gt;
      &lt;pathelement path="${compile.dest}"/&gt;
    &lt;/classpath&gt;
  &lt;/wljspc&gt;
&lt;/target&gt;
</pre>

</p>

<h3>Limitations</h3>
<ul>
<li>This works only on weblogic 4.5.1</li>
<li>It compiles the files thru the Classic compiler only.</li>
<li>Since it is my experience that weblogic jspc throws out of memory error on being given too
many files at one go, it is called multiple times with one jsp file each.</li>
</ul>

</body>
</html>
