<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>XMLValidate Task</title>
</head>

<body>

<h2><a name="xmlvalidate">XMLValidate</a></h2>
<h3>Description</h3>

<p>This task checks that XML files are valid (or only well formed). The
task uses the SAX2 parser implementation provided by JAXP by default
(probably the one that is used by Ant itself), but one can specify any
SAX1/2 parser if needed.</p>

<p>This task supports the use of nested
  <li><a href="../CoreTypes/xmlcatalog.html"><tt>&lt;xmlcatalog&gt;</tt></a> elements</li>
  <li><tt>&lt;dtd&gt;</tt> elements which are used to resolve DTDs and entities</li>
  <li><tt>&lt;attribute&gt;</tt> elements which are used to set features on the parser.
      These can be any number of
      <a href="http://www.saxproject.org/apidoc/org/xml/sax/package-summary.html#package_description"><tt>http://xml.org/sax/features/</tt></a>
      or other features that your parser may support.</li>
  <li><tt>&lt;property&gt;</tt> elements, containing string properties
</p>

<p><b>Warning</b> : JAXP creates by default a non namespace aware parser.
The <tt>"http://xml.org/sax/features/namespaces"</tt> feature is set
by default to <tt>false</tt> by the JAXP implementation used by ant. To validate
a document containing namespaces,
set the namespaces feature to <tt>true</tt> explicitly by nesting the following element:
<pre>
  &lt;attribute name="http://xml.org/sax/features/namespaces" value="true"/&gt;
</pre>
If you are using for instance a <tt>xsi:noNamespaceSchemaLocation</tt> attribute in your XML files,
you will need this namespace support feature.
</p>
<p>If you are using a parser not generated by JAXP, by using the <tt>classname</tt> attribute of xmlvalidate, this warning
may not apply.</p>


<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">the file(s) you want to check. (optionally can use an embedded fileset)</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">lenient</td>
    <td valign="top">
      if true, only check the XML document is well formed
        (ignored if the specified parser is a SAX1 parser)
    </td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">classname</td>
    <td valign="top">the parser to use.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">classpathref</td>
    <td valign="top">where to find the parser class. Optionally can use an embedded <tt>&lt;classpath&gt;</tt> element.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">failonerror</td>
    <td valign="top">fails on a error if set to true (defaults to true).</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">warn</td>
    <td valign="top">log parser warn events.</td>
    <td align="center" valign="top">No</td>
  </tr>
</table>

<h3><a name="nested">Nested Elements</a></h3>
<h4>dtd</h4>
<p>
<tt>&lt;dtd&gt;</tt> is used to specify different locations for DTD resolution.
</p>
<table border="1" cellpadding="2" cellspacing="0">
<tr>
  <td width="12%" valign="top"><b>Attribute</b></td>
  <td width="78%" valign="top"><b>Description</b></td>
  <td width="10%" valign="top"><b>Required</b></td>
</tr>
  <tr>
    <td valign="top">publicId</td>
    <td valign="top">Public ID of the DTD to resolve</td>
    <td align="center" valign="top">Yes</td>
  </tr>
  <tr>
    <td valign="top">location</td>
    <td valign="top">Location of the DTD to use, which can be a file,
    a resource, or a URL</td>
    <td align="center" valign="top">Yes</td>
  </tr>
</table>
<h4>xmlcatalog</h4>
<p>The <a href="../CoreTypes/xmlcatalog.html"><tt>&lt;xmlcatalog&gt;</tt></a>
element is used to perform entity resolution.</p>
<h4>attribute</h4>
<p>The <tt>&lt;attribute&gt;</tt> element is used to set parser features.<br>
Features usable with the xerces parser are defined here :
 <a href="http://xml.apache.org/xerces-j/features.html">Setting features</a><br>

SAX features are defined here:
 <a href="http://www.saxproject.org/apidoc/org/xml/sax/package-summary.html#package_description"><tt>http://xml.org/sax/features/</tt></a><br>
 </p>
<table border="1" cellpadding="2" cellspacing="0">
<tr>
  <td width="12%" valign="top"><b>Attribute</b></td>
  <td width="78%" valign="top"><b>Description</b></td>
  <td width="10%" valign="top"><b>Required</b></td>
</tr>
  <tr>
    <td valign="top">name</td>
    <td valign="top">The name of the feature</td>
    <td align="center" valign="top">Yes</td>
  </tr>
  <tr>
    <td valign="top">value</td>
    <td valign="top">The boolean value of the feature</td>
    <td align="center" valign="top">Yes</td>
  </tr>
</table>
</p>

<h4>property</h4>
<p>The <tt>&lt;property&gt;</tt> element is used to set properties.
These properties are defined here for the xerces XML parser implementation :
 <a href="http://xml.apache.org/xerces-j/properties.html">XML Parser properties</a>
Properties can be used to set the schema used to validate the XML file.
</p>
<table border="1" cellpadding="2" cellspacing="0">
<tr>
  <td width="12%" valign="top"><b>Attribute</b></td>
  <td width="78%" valign="top"><b>Description</b></td>
  <td width="10%" valign="top"><b>Required</b></td>
</tr>
  <tr>
    <td valign="top">name</td>
    <td valign="top">The name of the feature</td>
    <td align="center" valign="top">Yes</td>
  </tr>
  <tr>
    <td valign="top">value</td>
    <td valign="top">The string value of the property</td>
    <td align="center" valign="top">Yes</td>
  </tr>
</table>
</p>


<h3>Examples</h3>
<pre>
&lt;xmlvalidate file="toto.xml"/&gt;
</pre>
Validate toto.xml
<pre>
&lt;xmlvalidate failonerror="no" lenient="yes" warn="yes"
             classname="org.apache.xerces.parsers.SAXParser"&gt;
             classpath="lib/xerces.jar"&gt;
  &lt;fileset dir="src" includes="style/*.xsl"/&gt;
&lt;/xmlvalidate&gt;
</pre>
Validate all .xsl files in src/style, but only warn if there is an error, rather than
halt the build.
<pre>

&lt;xmlvalidate file="struts-config.xml" warn="false"&gt;
  &lt;dtd publicId="-//Apache Software Foundation//DTD Struts Configuration 1.0//EN"
       location="struts-config_1_0.dtd"/&gt;
&lt;/xmlvalidate&gt;
</pre>

Validate a struts configuration, using a local copy of the DTD. 
<pre> 
&lt;xmlvalidate failonerror="no"&gt;
  &lt;fileset dir="${project.dir}" includes="**/*.xml"/&gt;
  &lt;xmlcatalog refid="mycatalog"/&gt;
&lt;/xmlvalidate&gt;
</pre>

Scan all XML files in the project, using a predefined catalog to map URIs to local files.
<pre>
&lt;xmlvalidate failonerror="no"&gt;
  &lt;fileset dir="${project.dir}" includes="**/*.xml"/&gt;
  &lt;xmlcatalog&gt;
       &lt;dtd
         publicId=&quot;-//ArielPartners//DTD XML Article V1.0//EN&quot;
         location=&quot;com/arielpartners/knowledgebase/dtd/article.dtd&quot;/&gt;
  &lt;/xmlcatalog&gt;
&lt;/xmlvalidate&gt;
</pre>
Scan all XML files in the project, using the catalog defined inline.

<pre>
&lt;xmlvalidate failonerror="yes" lenient="no" warn="yes"&gt;
  &lt;fileset dir="xml" includes="**/*.xml"/&gt;
  &lt;attribute name="http://xml.org/sax/features/validation" value="true"/&gt;
  &lt;attribute name="http://apache.org/xml/features/validation/schema"  value="true"/&gt;
  &lt;attribute name="http://xml.org/sax/features/namespaces" value="true"/&gt;
&lt;/xmlvalidate&gt;
</pre>
Validate all .xml files in xml directory with the parser configured to perform XSD validation.
Note: The parser must support the feature
<code>http://apache.org/xml/features/validation/schema</code>.
The <a href="schemavalidate.html">schemavalidate</a> task is better for validating
W3C XML Schemas, as it extends this task with the right options automatically enabled,
and makes it easy to add a list of schema files/URLs to act as sources.

<pre>
<!-- Converts path to URL format -->
&lt;pathconvert dirsep="/" property="xsd.file"&gt;
&lt;path&gt;
   &lt;pathelement location="xml/doc.xsd"/&gt;
&lt;/path&gt;
&lt;/pathconvert&gt;

&lt;xmlvalidate file="xml/endpiece-noSchema.xml" lenient="false"
  failonerror="true" warn="true"&gt;
  &lt;attribute name="http://apache.org/xml/features/validation/schema"
  value="true"/&gt;
  &lt;attribute name="http://xml.org/sax/features/namespaces" value="true"/&gt;
  &lt;property
  name="http://apache.org/xml/properties/schema/external-noNamespaceSchemaLocation"
  value="${xsd.file}"/&gt;
&lt;/xmlvalidate&gt;
</pre>
<br>
Validate the file xml/endpiece-noSchema.xml against the schema xml/doc.xsd.
<br>


</body>
</html>

