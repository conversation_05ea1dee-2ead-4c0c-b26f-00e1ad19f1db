<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>jarlib-resolve Task</title>
</head>

<body>

<h2><a name="jarlib-resolve">jarlib-resolve</a></h2>
<h3>Description</h3>
<p>Try to locate a jar to satisfy an extension and place
 location of jar into property. The task allows you to
 add a number of resolvers that are capable of locating a
 library for a specific extension. Each resolver will be attempted
 in specified order until library is found or no resolvers are left.
 If no resolvers are left and failOnError is true then a BuildException
 will be thrown.</p>

<p>Note that this task
works with extensions as defined by the "Optional Package" specification.
 For more information about optional packages, see the document
<em>Optional Package Versioning</em> in the documentation bundle for your
Java2 Standard Edition package, in file
<code>guide/extensions/versioning.html</code> or online at
<a href="http://java.sun.com/j2se/1.3/docs/guide/extensions/versioning.html">
http://java.sun.com/j2se/1.3/docs/guide/extensions/versioning.html</a>.</p>
<p>See the Extension and ExtensionSet documentation for further details</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">property</td>
    <td valign="top">The name of property to set to library location.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">failOnError</td>
    <td valign="top">True if failure to locate library should result in build exception.</td>
    <td valign="top" align="center">No, defaults to true.</td>
  </tr>
  <tr>
    <td valign="top">checkExtension</td>
    <td valign="top">True if libraries returned by nested resolvers should be checked to see if
    they supply extension.</td>
    <td valign="top" align="center">No, defaults to true.</td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>

<h4>extension</h4>
 <p><a href="../OptionalTypes/extension.html">Extension</a> the extension
 to resolve. Must be present</p>

<h4>location</h4>
 <p>The location sub element allows you to look for a library in a
 location relative to project directory.</p>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">location</td>
    <td valign="top">The pathname of library.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
</table>

<h4>url</h4>
 <p>The url resolver allows you to download a library from a URL to a
 local file.</p>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">url</td>
    <td valign="top">The URL to download.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">destfile</td>
    <td valign="top">The file to download URL into.</td>
    <td valign="top" align="center">No, But one of destfile or
    destdir must be present</td>
  </tr>
  <tr>
    <td valign="top">destdir</td>
    <td valign="top">The directory in which to place downloaded file.</td>
    <td valign="top" align="center">No, But one of destfile or
    destdir must be present</td>
  </tr>
</table>

<h4>ant</h4>
 <p>The ant resolver allows you to run a ant build file to generate a library.</p>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">antfile</td>
    <td valign="top">The build file.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">destfile</td>
    <td valign="top">The file that the ant build creates.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">target</td>
    <td valign="top">The target to run in build file.</td>
    <td valign="top" align="center">No</td>
  </tr>
</table>

<h3>Examples</h3>
<p><b>Resolve Extension to file.</b> If file does not exist or file
   does not implement extension then throw an exception.</p>
<pre>
  &lt;extension id=&quot;dve.ext&quot;
    extensionName=&quot;org.realityforge.dve&quot;
    specificationVersion=&quot;1.2&quot;
    specificationVendor=&quot;Peter Donald&quot;/&gt;

  &lt;jarlib-resolve property="dve.library"&gt;
    &lt;extension refid="dve.ext"/&gt;
    &lt;location location="/opt/jars/dve.jar"/&gt;
  &lt;/jarlib-resolve&gt;
</pre>

<p><b>Resolve Extension to url.</b> If url does not exist or can not write
   to destfile or files does not implement extension then throw an exception.</p>
<pre>
  &lt;extension id=&quot;dve.ext&quot;
    extensionName=&quot;org.realityforge.dve&quot;
    specificationVersion=&quot;1.2&quot;
    specificationVendor=&quot;Peter Donald&quot;/&gt;

  &lt;jarlib-resolve property="dve.library"&gt;
    &lt;extension refid="dve.ext"/&gt;
    &lt;url url="http://www.realityforge.net/jars/dve.jar" destfile="lib/dve.jar"/&gt;
  &lt;/jarlib-resolve&gt;
</pre>

<p><b>Resolve Extension to file produce by ant build.</b> If file does not get produced
   or ant file is missing or build fails then throw an exception (Note does not check
   that library implements extension).</p>
<pre>
  &lt;extension id=&quot;dve.ext&quot;
    extensionName=&quot;org.realityforge.dve&quot;
    specificationVersion=&quot;1.2&quot;
    specificationVendor=&quot;Peter Donald&quot;/&gt;

  &lt;jarlib-resolve property="dve.library" checkExtension="false"&gt;
    &lt;extension refid="dve.ext"/&gt;
    &lt;ant antfile="../dve/build.xml" target="main" destfile="lib/dve.jar"/&gt;
  &lt;/jarlib-resolve&gt;
</pre>

<p><b>Resolve Extension via multiple methods.</b> First check local file to see if it implements
  extension. If it does not then try to build it from source in parallel directory. If that
  fails then finally try to download it from a website. If all steps fail then throw a build
  exception.</p>
<pre>
  &lt;extension id=&quot;dve.ext&quot;
    extensionName=&quot;org.realityforge.dve&quot;
    specificationVersion=&quot;1.2&quot;
    specificationVendor=&quot;Peter Donald&quot;/&gt;

  &lt;jarlib-resolve property="dve.library"&gt;
    &lt;extension refid="dve.ext"/&gt;
    &lt;location location="/opt/jars/dve.jar"/&gt;
    &lt;ant antfile="../dve/build.xml" target="main" destfile="lib/dve.jar"/&gt;
    &lt;url url="http://www.realityforge.net/jars/dve.jar" destfile="lib/dve.jar"/&gt;
  &lt;/jarlib-resolve&gt;
</pre>



</body>
</html>

