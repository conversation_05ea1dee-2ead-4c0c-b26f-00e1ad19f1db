<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>Proxy Configuration</title>
</head>

<body>
<h2>Proxy Configuration</h2>

<p>
This page discussing proxy issues on command-line ant.
Consult your IDE documentation for IDE-specific information upon proxy setup.
</p>

<p>

All tasks running in Ant's JVM share the same HTTP/FTP/Socks
proxy configuration.
</p>

<p>
    When any task tries to retrieve content from an HTTP page, including the
    <code>&lt;get&gt;</code> task, any automated URL retrieval in
    an XML/XSL task, or any third-party task that uses the <code>java.net.URL</code>
    classes, the proxy settings may make the difference between success and failure.
</p>
<p>
    Anyone authoring a build file behind a blocking firewall will immediately appreciate
    the problems and may want to write a build file to deal with the problem, but
    users of third party build build files may find that the build file itself
    does not work behind the firewall.
</p>
<p>
    This is a long standing problem with Java and Ant. The only way to fix
    it is to explictly configure Ant with the proxy settings, either
    by passing down the proxy details as JVM properties, or to
    tell Ant on a Java1.5+ system to have the JVM work it out for itself.

</p>



<h3>Java1.5+ proxy support (new for Ant1.7)</h3>
<p>
    When Ant starts up, if the <code>-autoproxy</code>
    command is supplied, Ant sets the
    <code>java.net.useSystemProxies</code> system property. This tells 
    a Java1.5+ JVM to use the current set of property settings of the host
    environment. Other JVMs, such as the Kaffe and Apache Harmony runtimes,
    may also use this property in future.
    It is ignored on the Java1.4 and earlier runtimes.
</p>
<p>
    This property maybe enough to give command-line Ant
    builds network access, although in practise the results
    are somewhat disappointing.
</p>
<p>
    We are not entirely sure where it reads the property settings from. 
    For windows, it probably reads the appropriate bits of the registry. For
    Unix/Linux it may use the current Gnome2 settings. 
    
<p>
    One limitation of this feature, other than requiring a 1.5+ JVM,
    is that it is not dynamic. A long-lasting build hosted on a laptop will
    not adapt to changes in proxy settings.
</p>


<p>
    It is has also been reported a breaking the IBM Java 5 JRE on AIX,
    and does not appear to work reliably on Linux.
    Other odd things can go wrong, like Oracle JDBC drivers or pure Java SVN clients.
</p>

<p>
    To make the <code>-autproxy</code> option the default, add it to the environment variable
    <code>ANT_ARGS</code>, which contains a list of arguments to pass to Ant on every
    command line run.
</p>

<h3>JVM options</h3>
<p>
    Any JVM can have its proxy options explicitly configured by passing
    the appropriate <code>-D</code> system property options to the runtime.
    Ant can be configured through all its shell scripts via the
    <code>ANT_OPTS</code> environment variable, which is a list of options to
    supply to Ant's JVM:
</p>
<p>
  For bash:
</p>  
<pre>
    export ANT_OPTS="-Dhttp.proxyHost=proxy -Dhttp.proxyPort=8080"
</pre>
  For csh/tcsh:
<pre>
    setenv ANT_OPTS "-Dhttp.proxyHost=proxy -Dhttp.proxyPort=8080"
</pre>
<p>
  For Windows, set the ANT_OPTS environment variable in the appropriate "MyComputer" 
  properties dialog box.
</p>
<p>
  This mechanism works across Java versions, is cross-platform and reliable. 
  Once set, all build files run via the command line will automatically have
  their proxy setup correctly, without needing any build file changes. It also
  apparently overrides Ant's automatic proxy settings options.
</p>
<p>  
  It is limited in the following ways:
</p>  
  <ol>
  <li>Does not work under IDEs. These need their own proxy settings changed</li>
  <li>Not dynamic enough to deal with laptop configuration changes.</li>
  </ol>

<h3>SetProxy Task</h3>
<p>
    The <a href="OptionalTasks/setproxy.html">setproxy task</a> can be used to
    explicitly set a proxy in a build file. This manipulates the many proxy 
    configuration properties of a JVM, and controls the proxy settings for all 
    network operations in the same JVM from that moment. 
</p>
<p>
    If you have a build file that is only to be used in-house, behind a firewall, on
    an older JVM, <i>and you cannot change Ant's JVM proxy settings</i>, then
    this is your best option. It is ugly and brittle, because the build file now contains
    system configuration information. It is also hard to get this right across
    the many possible proxy options of different users (none, HTTP, SOCKS).
</p>


<p>
    Note that proxy configurations set with this task will probably override
    any set by other mechanisms. It can also be used with fancy tricks to 
    only set a proxy if the proxy is considered reachable:
</p>

<pre>
  &lt;target name="probe-proxy" depends="init"&gt;
    &lt;condition property="proxy.enabled"&gt;
      &lt;and&gt;
        &lt;isset property="proxy.host"/&gt;
        &lt;isreachable host="${proxy.host}"/&gt;
      &lt;/and&gt;
    &lt;/condition&gt;
  &lt;/target&gt;

  &lt;target name="proxy" depends="probe-proxy" if="proxy.enabled"&gt;
    &lt;property name="proxy.port" value="80"/&gt;
    &lt;property name="proxy.Employee" value=""/&gt;
    &lt;property name="proxy.pass" value=""/&gt;
    &lt;setproxy proxyhost="${proxy.host}" proxyport="${proxy.port}"
      proxyuser="${proxy.Employee}" proxypassword="${proxy.pass}"/&gt;
  &lt;/target&gt;
</pre>

<h3>Summary and conclusions</h3>
<p>
There are three ways to set up proxies in Ant.
</p>
<ol>
<li>With Ant1.7 using the <code>-autoproxy</code> parameter.</li>
<li>Via JVM system properties -set these in the ANT_ARGS environment variable.</li>
<li>Via the &lt;setproxy&gt; task.</li>
</ol>


<h4>Further reading</h4>

<ul>
<li><a href="http://java.sun.com/j2se/1.5.0/docs/guide/net/properties.html">
Java Networking Properties</a>. Notice how not all proxy settings are documented
there.
<li><a href="http://blogs.sun.com/roller/resources/jcc/Proxies.pdf">Proxies</a>
</li>
</ul>

</body>
</html>
