<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>Apache Ant User Manual</title>
<base target="mainFrame">
</head>

<body>

<h2><a href="toc.html" target="navFrame">Table of Contents</a></h2>

<h3>Using Ant</h3>
<a href="using.html#buildfile">Writing a Simple Buildfile</a><br>
&nbsp;&nbsp;<a href="using.html#projects">Projects</a><br>
&nbsp;&nbsp;<a href="using.html#targets">Targets</a><br>
&nbsp;&nbsp;<a href="using.html#tasks">Tasks</a><br>
&nbsp;&nbsp;<a href="using.html#properties">Properties</a><br>
&nbsp;&nbsp;<a href="using.html#built-in-props">Built-in Properties</a><br>
&nbsp;&nbsp;<a href="using.html#example">Example Buildfile</a><br>
&nbsp;&nbsp;<a href="using.html#filters">Token Filters</a><br>
&nbsp;&nbsp;<a href="using.html#path">Path-like Structures</a><br>
&nbsp;&nbsp;<a href="using.html#arg">Command-line Arguments</a><br>
&nbsp;&nbsp;<a href="using.html#references">References</a><br>
&nbsp;&nbsp;<a href="using.html#external-tasks">Use of external tasks</a><br>

</body>
</html>

