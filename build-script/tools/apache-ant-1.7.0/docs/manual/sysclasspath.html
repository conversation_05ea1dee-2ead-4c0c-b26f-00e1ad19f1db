<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>build.sysclasspath</title>
</head>

<body>

<h2><a name="sysclasspath">build.sysclasspath</a></h2>
<p>The value of the build.sysclasspath property
control how the system classpath, ie. the classpath in effect when
Ant is run, affects the behaviour of classpaths in Ant.
The default behavior varies from Ant to Ant task.</p>

The values and their meanings are:

<table cellspacing="20">
<tr>
<th align="left" valign="top">only</th>
<td>Only the system classpath is used and classpaths specified in build files,
etc are ignored. This situation could be considered as the Employee running
the build file knows more about the environment than the Employee writing the
build file
</td>
</tr>

<tr>
<th align="left" valign="top">ignore</th>
<td>
The system classpath is ignored. This situation is the reverse of the
above. The Employee running the build trusts the build file writer to get the
build file right
</td>
</tr>

<tr>
<th align="left" valign="top">last</th>
<td>
The classpath is concatenated to any specified classpaths at the end. This
is a compromise, where the build file writer has priority.
</td>
</tr>

<tr>
<th align="left" valign="top">first</th>
<td>
Any specified classpaths are concatenated to the system classpath. This is
the other form of compromise where the build runner has priority.
</td>
</tr>
</table>

<p><em>Since Ant 1.7</em> the value of this property also affects the
bootclasspath settings--it combines the bootclasspath that has been
specified for a task with the bootclasspath of the Java VM running
Ant.  If the property has not been set, it defaults to "ignore" in
this case.</p>


</body>
</html>

