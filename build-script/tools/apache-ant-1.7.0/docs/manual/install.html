<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>Installing Ant</title>
</head>

<body>
<h1>Installing Ant</h1>
<h2><a name="getting">Getting Ant</a></h2>
<h3>Binary Edition</h3>
<p>The latest stable version of Ant is available from the Ant web page <a
href="http://ant.apache.org/">http://ant.apache.org/</a>.

<h3>As a binary in an RPM Package</h3>

<p>Consult the <a href="#jpackage">jpackage</a> section below.</p>

<h3>Bundled in IDEs</h3>
<p>
    All the main Java IDEs ship with Ant, products such as Eclipse, NetBeans
    and IntelliJ IDEA. If you install Ant this way you usually get the most recent
    release of Ant at the time the IDE was released. Some of the IDEs (Eclipse
    and NetBeans in particular) ship with extra tasks that only work if 
    IDE-specific tools are on Ant's path. To use these on command-line versions
    of Ant, the relevant JARs need to be added to the command-line Ant as
    extra libraries/tasks. Note that if it is an IDE task or extension that is
    not behaving, the Ant team is unable to field bug reports. Try the IDE mailing
    lists first, who will cross-file bugs if appropriate.
</p>
<p>
    IDE's can invariably be pointed at different Ant installations. This lets
    developers upgrade to a new release of Ant, and eliminate inconsistencies 
    between command-line and IDE Ant. 
</p>

<h3>Bundled in Java applications</h3>

<p>
    Many Java applications, most particularly application servers, ship with 
    a version of Ant. These are primarily for internal use by the application,
    using the Java APIs to delegate tasks such as JSP page compilation to the Ant
    runtime. Such distributions are usually unsupported by everyone. Particularly
    troublesome are those products that non only ship with their own Ant release,
    they add their own version of ANT.BAT or ant.sh to the PATH. If Ant starts 
    behaving wierdly after installing something, try the 
    <a href="#diagnostics">diagnostics</a> advice.
</p>

<h3>Source Edition</h3>

<p>If you prefer the source edition, you can download the source for the latest 
Ant release from 
<a href="http://ant.apache.org/srcdownload.cgi">http://ant.apache.org/srcdownload.cgi</a>.

If you prefer the leading-edge code, you can access
the code as it is being developed via SVN. The Ant website has details on
<a href="http://ant.apache.org/svn.html" target="_top">accessing SVN</a>. 
All bug fixes will go in against the HEAD of the source tree, and the first
response to many bugreps will be "have you tried the latest version". 
Don't be afraid to download and build a prererelease edition, as everything
other than new features are usually stable.
    </p>
<p>
    
    
See the section <a href="#buildingant">Building Ant</a> on how to
build Ant from the source code.
You can also access the
<a href="http://svn.apache.org/viewcvs.cgi/ant/" target="_top">
Ant SVN repository</a> on-line. </p>

<hr>
<h2><a name="sysrequirements">System Requirements</a></h2>
Ant has been used successfully on many platforms, including Linux,
commercial flavours of Unix such as Solaris and HP-UX,
Windows NT-platforms, OS/2 Warp, Novell Netware 6, OpenVMS and MacOS X.
The platforms used most for development are, in no particular order,
Linux, MacOS X, Windows XP and Unix; these are therefore that platforms
that tend to work best. As of Ant1.7, Windows 9x is no longer supported.
<p>
To build and use Ant, you must have a JAXP-compliant XML parser installed and
available on your classpath, such as Xerces.</p>
<p>
The binary distribution of Ant includes the latest version of the
<a href="http://xml.apache.org/xerces2-j/index.html">Apache Xerces2</a> XML parser.
Please see
<a href="http://java.sun.com/xml/" target="_top">http://java.sun.com/xml/</a>
for more information about JAXP.
If you wish to use a different JAXP-compliant parser, you should remove
<code>xercesImpl.jar</code> and <code>xml-apis.jar</code>
from Ant's <code>lib</code> directory.
<p>
You can then either put the JARs of your preferred parser into Ant's
<code>lib</code> directory or put the jars on the system classpath. 
Some parts of Ant will fail if you use an old parser, especially one
that is not namespace-aware. In particular, avoid the Crimson parser.</p>

<p>Tip: "ant -diagnostics" will list the XML parser used and its location.</p>

<p>
For the current version of Ant, you will also need a JDK installed on
your system, version 1.2 or later required, 1.5 or later strongly recommended. 
The later the version of Java , the more Ant tasks you get.
</p>
<p>
<strong>Note #2: </strong>If a JDK is not present, only the JRE runtime, then many tasks will not work.
</p>

<h3>Open Source Java Runtimes</h3>
<p>
    The Ant team strongly supports users running Ant on Kaffe and other
    open source Java runtimes, and so strives to have a product that works
    well on those platforms. What appears to work well is Kaffe with 
    Gnu Classpath and the Xerces and Xalan libraries. 
</p>

<hr>
<h2><a name="installing">Installing Ant</a></h2>
<p>The binary distribution of Ant consists of the following directory layout:
<pre>
  ant
   +--- README, LICENSE, fetch.xml, other text files. //basic information
   +--- bin  // contains launcher scripts
   |
   +--- lib  // contains Ant jars plus necessary dependencies
   |
   +--- docs // contains documentation
   |      |
   |      +--- images  // various logos for html documentation
   |      |
   |      +--- manual  // Ant documentation (a must read ;-)
   |
   +--- etc // contains xsl goodies to:
            //   - create an enhanced report from xml output of various tasks.
            //   - migrate your build files and get rid of 'deprecated' warning
            //   - ... and more ;-)
</pre>

Only the <code>bin</code> and <code>lib</code> directories are
required to run Ant.

To install Ant, choose a directory and copy the distribution
files there. This directory will be known as ANT_HOME.
</p>

<table width="80%">
<tr>
  <td colspan="2">
    <b>Windows 95, Windows 98 &amp; Windows ME Note:</b>
  </td>
</tr>
<tr>
  <td width="5%">&nbsp;</td>
  <td><i>
On these systems, the script used to launch Ant will have
problems if ANT_HOME is a long filename (i.e. a filename which is not
of the format known as &quot;8.3&quot;). This is due to
limitations in the OS's handling of the <code>&quot;for&quot;</code>
batch-file statement. It is recommended, therefore, that Ant be
installed in a <b>short</b>, 8.3 path, such as C:\Ant. </i>
  </td>
</tr>
<tr>
  <td width="5%">&nbsp;</td>
  <td>
    <p>On these systems you will also need to configure more environment
       space to cater for the environment variables used in the Ant lauch script.
       To do this, you will need to add or update the following line in
       the <code>config.sys</code> file
    </p>
    <p><code>shell=c:\command.com c:\ /p /e:32768</code></p>
  </td>
</tr>
</table>

<h3>Setup</h3>
<p>
Before you can run Ant there is some additional set up you
will need to do unless you are installing the <a href="#jpackage">RPM 
version from jpackage.org</a>:</p>
<ul>
<li>Add the <code>bin</code> directory to your path.</li>
<li>Set the <code>ANT_HOME</code> environment variable to the
directory where you installed Ant.  On some operating systems, Ant's
startup scripts can guess <code>ANT_HOME</code> (Unix dialects and
Windows NT/2000), but it is better to not rely on this behavior.</li>
<li>Optionally, set the <code>JAVA_HOME</code> environment variable
(see the <a href="#advanced">Advanced</a> section below).
This should be set to the directory where your JDK is installed.</li>
</ul>
<p><strong>Note:</strong> Do not install Ant's ant.jar file into the lib/ext
directory of the JDK/JRE. Ant is an application, whilst the extension
directory is intended for JDK extensions. In particular there are security
restrictions on the classes which may be loaded by an extension.</p>

<table width="80%">
<tr>
  <td colspan="2">
    <b>Windows Note:</b>
  </td>
</tr>
<tr>
  <td width="5%">&nbsp;</td>
  <td>
    The ant.bat script makes use of three environment variables -
    ANT_HOME, CLASSPATH and JAVA_HOME. <b>Ensure</b> that if
    these variables are set, they do <b><u>not</u></b> have quotes (either
    ' or &quot;) and they do <b><u>not</u></b> end with \ or with /.
  </td>
</tr>
</table>

<h3><a name="optionalTasks">Optional Tasks</a></h3>
<p>Ant supports a number of optional tasks. An optional task is a task which
typically requires an external library to function. The optional tasks are
packaged together with the core Ant tasks.</p>

<p>The external libraries required by each of the optional tasks is detailed
in the <a href="#librarydependencies">Library Dependencies</a> section. These external
libraries must be added to Ant's classpath, in any of the following ways 
</p>
<ul>
    <li>In ANT_HOME/lib. This makes the JAR files available to all
    Ant users and builds</li>
    
    <li>
        In /Users/<USER>/.ant/lib . This is a new feature since Ant1.6, 
        and allows different users to add new libraries to Ant. All JAR files
        added to this directory are available to command-line Ant.
    </li>
    
    <li>
        On the command line with a <code>-lib</code> parameter. This lets
        you add new JAR files on a case-by-case basis.
    </li>
    
    <li>In the CLASSPATH environment variable. Avoid this; it makes
        the JAR files visible to <i>all</i> Java applications, and causes
        no end of support calls.
    </li>
</ul>

<p>
    IDEs have different ways of adding external JAR files and third-party tasks
    to Ant. Usually it is done by some configuration dialog. Sometimes JAR files
    added to a project are automatically added to ant's classpath.
</p>

<h3><a name="classpath">The CLASSPATH environment variable</a></h3>
<p>

The CLASSPATH environment variable is a source of many Ant support queries. As
the round trip time for diagnosis on the Ant Employee mailing list can be slow, and
because filing bug reports complaining about 'ant.bat' not working will be
rejected by the developers as WORKSFORME "this is a configuration problem, not a
bug", you can save yourself a lot of time and frustration by following some
simple steps.

</p>
<ol>

<li>Do not ever set CLASSPATH. Ant does not need it, it only causes confusion
and breaks things.

</li>

<li>If you ignore the previous rule, do not ever, ever, put quotes in the
CLASSPATH, even if there is a space in a directory. This will break Ant, and it
is not needed. </li>

<li>If you ignore the first rule, do not ever, ever, have a trailing backslash
in a CLASSPATH, as it breaks Ant's ability to quote the string. Again, this is
not needed for the correct operation of the CLASSPATH environment variable, even
if a DOS directory is to be added to the path. </li>

<li>You can stop Ant using the CLASSPATH environment variable by setting the
<code>-noclasspath</code> option on the command line. This is an easy way
to test for classpath-related problems.</li>

</ol>

<p>

The usual symptom of CLASSPATH problems is that ant will not run with some error
about not being able to find <code>org.apache.tools.Ant.main</code>, or, if you have got the
quotes/backslashes wrong, some very weird Java startup error. To see if this is
the case, run <code>ant -noclasspath</code> or unset the CLASSPATH environment
variable.

</p>


<h3><a name="proxy">Proxy Configuration</a></h3>

<p> Many Ant built-in and third-party tasks use network connections to retrieve
files from HTTP servers. If you are behind a firewall with a proxy server, then
Ant needs to be configured with the proxy. Here are the different ways to do
this. </p>

<ul>

<li><b>With Java1.5</b><br>. 

When you run Ant on Java1.5, it tries to use the automatic proxy setup
mechanism. If this works -and it is a big if, as we see little evidence of it
doing so on Linux or WinXP-, then your proxy is set up without you doing
anything. You can disable this feature with the <code>-noproxy</code> option.

</li>

<li><b>With explicit JVM properties.</b><br>.

These are documented <a
href="http://java.sun.com/j2se/1.5.0/docs/guide/net/properties.html">by Sun</a>,
and control the proxy behaviour of the entire JVM. To set them in Ant, declare
them in the <code>ANT_OPTS</code> environment variable. This is the best option
for a non-mobile system. For a laptop, you have to change these settings as you
roam.

</li>

<li><b>In the build file itself</b><br>

If you are writing an build file that is always to be used behind the firewall,
the &lt;setproxy&gt; task lets you configure the proxy (which it does by setting
the JVM properties). If you do this, we strongly recommend using ant properties
to define the proxy host, port, etc, so that individuals can override the
defaults.</li>

</ul>

<p> The Ant team acknowledges that this is unsatisfactory. Until the JVM
automatic proxy setup works properly everywhere, explicit JVM options via
ANT_ARGS are probably the best solution. Setting properties on Ant's
command line do not work, because those are <i>Ant properties</i> being set, not
JVM options. This means the following does not set up the command line:

</p>

<pre>ant -Dhttp.proxyHost=proxy -Dhttp.proxyPort=81</pre>

<p> All it does is set up two Ant properties.</p> 

<p>One other troublespot with
proxies is with authenticating proxies. Ant cannot go beyond what the JVM does
here, and as it is very hard to remotely diagnose, test and fix proxy-related
problems, users who work behind a secure proxy will have to spend much time
configuring the JVM properties until they are happy. </p>


<h3><a name="windows">Windows and OS/2</a></h3>
<p>Assume Ant is installed in <code>c:\ant\</code>. The following sets up the
environment:</p>
<pre>set ANT_HOME=c:\ant
set JAVA_HOME=c:\jdk-********
set PATH=%PATH%;%ANT_HOME%\bin</pre>

<h3>Linux/Unix (bash)</h3>
<p>Assume Ant is installed in <code>/usr/local/ant</code>. The following sets up
the environment:</p>
<pre>export ANT_HOME=/usr/local/ant
export JAVA_HOME=/usr/local/jdk-********
export PATH=${PATH}:${ANT_HOME}/bin</pre>

<h3>Linux/Unix (csh)</h3>
<pre>setenv ANT_HOME /usr/local/ant
setenv JAVA_HOME /usr/local/jdk/jdk-********
set path=( $path $ANT_HOME/bin )</pre>

<p>
Having a symbolic link set up to point to the JVM/JSK version makes updates more seamless. </p>
<a name="jpackage"></a>
<h3>RPM version from jpackage.org</h3>
<p>
The <a href="http://www.jpackage.org">JPackage project</a> distributes an RPM version of Ant.  
With this version, it is not necessary to set <code> JAVA_HOME </code>or 
<code> ANT_HOME </code>environment variables and the RPM installer will correctly
place the Ant executable on your path.
</p>
  <p>
    <b>NOTE:</b> <em>Since Ant 1.7.0</em>, if the <code>ANT_HOME</code>
    environment variable is set, the jpackage distribution will be
    ignored.
  </p>
  <p>
Optional jars for the JPackage version are handled in two ways.  The easiest, and
best way is to get these external libraries from JPackage if JPackage has them 
available.  (Note: for each such library, you will have to get both the external 
package itself (e.g. <code>oro-2.0.8-2jpp.noarch.rpm</code>) and the small library that links 
ant and the external package (e.g. <code>ant-apache-oro-1.6.2-3jpp.noarch.rpm</code>).
</p><p>
However, JPackage does not package proprietary software, and since some of the
optional packages depend on proprietary jars, they must be handled as follows.  
This may violate the spirit of JPackage, but it is necessary if you need these proprietary packages.
For example, suppose you want to install support for starteam, which jpackage does not 
support:
<ol>
<li>Decide where you want to deploy the extra jars.  One option is in <code>$ANT_HOME/lib</code>, 
which, for JPackage is usually <code>/usr/share/ant/lib</code>.  Another, less messy option
is to create an <code>.ant/lib</code> subdirectory of your home directory and place your 
non-jpackage ant jars there, thereby avoiding mixing jpackage
libraries with non-jpacakge stuff in the same folder.
More information on where Ant finds its libraries is available 
<a href="http://ant.apache.org/manual/running.html#libs">here</a></li>
<li>Download a non-jpackage binary distribution from the regular 
    <a href="http://ant.apache.org/bindownload.cgi">Apache Ant site</a></li>
<li>Unzip or untar the distribution into a temporary directory</li>
<li>Copy the linking jar, in this case <code>ant-starteam.jar</code>, into the library directory you 
chose in step 1 above.</li>
<li>Copy the proprietary jar itself into the same directory.</li>
</ol>
Finally, if for some reason you are running on a system with both the JPackage and Apache versions of Ant
available, if you should want to run the Apache version (which will have to be specified with an absolute file name,
not found on the path), you should use Ant's <code>--noconfig</code> command-line switch to avoid JPackage's classpath mechanism.


<h3><a name="advanced">Advanced</a></h3>

<p>There are lots of variants that can be used to run Ant. What you need is at
least the following:</p>
<ul>
<li>The classpath for Ant must contain <code>ant.jar</code> and any jars/classes
needed for your chosen JAXP-compliant XML parser.</li>
<li>When you need JDK functionality
(such as for the <a href="CoreTasks/javac.html">javac</a> task or the
<a href="CoreTasks/rmic.html">rmic</a> task), then <code>tools.jar</code>
must be added. The scripts supplied with Ant,
in the <code>bin</code> directory, will add
the required JDK classes automatically, if the <code>JAVA_HOME</code>
environment variable is set.</li>

<li>When you are executing platform-specific applications, such as the
<a href="CoreTasks/exec.html">exec</a> task or the
<a href="CoreTasks/cvs.html">cvs</a> task, the property <code>ant.home</code>
must be set to the directory containing where you installed Ant. Again
this is set by the Ant scripts to the value of the ANT_HOME environment
variable.</li>
</ul>
The supplied ant shell scripts all support an <tt>ANT_OPTS</tt>
environment variable which can be used to supply extra options
to ant. Some of the scripts also read in an extra script stored
in the users home directory, which can be used to set such options. Look
at the source for your platform's invocation script for details.

<hr>
<h2><a name="buildingant">Building Ant</a></h2>
<p>To build Ant from source, you can either install the Ant source distribution
or checkout the ant module from SVN.</p>
<p>Once you have installed the source, change into the installation
directory.</p>

<p>Set the <code>JAVA_HOME</code> environment variable
to the directory where the JDK is installed.
See <a href="#installing">Installing Ant</a>
for examples on how to do this for your operating system. </p>

<p><b>Note</b>: The bootstrap process of Ant requires a greedy
compiler like Sun's javac or jikes.  It does not work with gcj or
kjc.</p>

<p>Make sure you have downloaded any auxiliary jars required to
build tasks you are interested in. These should be
added to the <code>lib/optional</code>
directory of the source tree.
See <a href="#librarydependencies">Library Dependencies</a>
for a list of JAR requirements for various features.
Note that this will make the auxiliary JAR
available for the building of Ant only. For running Ant you will
still need to
make the JARs available as described under
<a href="#installing">Installing Ant</a>.</p>

<p>Your are now ready to build Ant:</p>
<blockquote>
  <p><code>build -Ddist.dir=&lt;<i>directory_to_contain_Ant_distribution</i>&gt; dist</code>&nbsp;&nbsp;&nbsp;&nbsp;(<i>Windows</i>)</p>
  <p><code>sh build.sh -Ddist.dir=&lt;<i>directory_to_contain_Ant_distribution</i>&gt; dist</code>&nbsp;&nbsp;&nbsp;&nbsp;(<i>Unix</i>)</p>
</blockquote>

<p>This will create a binary distribution of Ant in the directory you specified.</p>

<p>The above action does the following:</p>
<ul>

<li>If necessary it will bootstrap the Ant code. Bootstrapping involves the manual
compilation of enough Ant code to be able to run Ant. The bootstrapped Ant is
used for the remainder of the build steps. </li>

<li>Invokes the bootstrapped Ant with the parameters passed to the build script. In
this case, these parameters define an Ant property value and specify the &quot;dist&quot; target
in Ant's own <code>build.xml</code> file.</li>
    
<li>Create the ant.jar and ant-launcher.jar JAR files</li>
    
<li>Create optional JARs for which the build had the relevant libraries. If 
a particular library is missing from ANT_HOME/lib/optional, then the matching
ant- JAR file will not be created. For example, ant-junit.jar is only built
if there is a junit.jar in the optional directory.</li>    
</ul>

<p>On most occasions you will not need to explicitly bootstrap Ant since the build
scripts do that for you. If however, the build file you are using makes use of features
not yet compiled into the bootstrapped Ant, you will need to manually bootstrap.
Run <code>bootstrap.bat</code> (Windows) or <code>bootstrap.sh</code> (UNIX)
to build a new bootstrap version of Ant.</p>

If you wish to install the build into the current <code>ANT_HOME</code>
directory, you can use:
<blockquote>
  <p><code>build install</code>&nbsp;&nbsp;&nbsp;&nbsp;(<i>Windows</i>)</p>
  <p><code>sh build.sh install</code>&nbsp;&nbsp;&nbsp;&nbsp;(<i>Unix</i>)</p>
</blockquote>

You can avoid the lengthy Javadoc step, if desired, with:
<blockquote>
  <p><code>build install-lite</code>&nbsp;&nbsp;&nbsp;&nbsp;(<i>Windows</i>)</p>
  <p><code>sh build.sh install-lite</code>&nbsp;&nbsp;&nbsp;&nbsp;(<i>Unix</i>)</p>
</blockquote>
This will only install the <code>bin</code> and <code>lib</code> directories.
<p>Both the <code>install</code> and
<code>install-lite</code> targets will overwrite
the current Ant version in <code>ANT_HOME</code>.</p>

<hr>
<h2><a name="librarydependencies">Library Dependencies</a></h2>
<p>The following libraries are needed in Ant's classpath 
if you are using the
indicated feature. Note that only one of the regexp libraries is
needed for use with the mappers
(and Java 1.4 and higher includes a regexp implementation which
Ant will find automatically).
You will also need to install the particular
Ant optional jar containing the task definitions to make these
tasks available. Please refer to the <a href="#optionalTasks">
Installing Ant / Optional Tasks</a> section above.</p>

<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td><b>Jar Name</b></td>
    <td><b>Needed For</b></td>
    <td><b>Available At</b></td>
  </tr>
  <tr>
    <td>An XSL transformer like Xalan</td>
    <td>style task</td>
    <td>
    <b>If you use JDK 1.4+, an XSL transformer is already included, so you need not do anything special.</b><br>
    <ul><li>XALAN : <a href="http://xml.apache.org/xalan-j/index.html"
    target="_top">http://xml.apache.org/xalan-j/index.html</a></li>
    </ul>
    </td>
  </tr>
  <tr>
    <td>jakarta-regexp-1.3.jar</td>
    <td>regexp type with mappers</td>
    <td><a href="http://jakarta.apache.org/regexp/" target="_top">http://jakarta.apache.org/regexp/</a></td>
  </tr>
  <tr>
    <td>jakarta-oro-2.0.8.jar</td>
    <td>regexp type with mappers and the perforce tasks<br>
    To use the FTP task,
you need jakarta-oro 2.0.8 or later, and <a href="#commons-net">commons-net</a></td>
    <td><a href="http://jakarta.apache.org/oro/" target="_top">http://jakarta.apache.org/oro/</a></td>
  </tr>
  <tr>
    <td>junit.jar</td>
    <td><code>&lt;junit&gt;</code> task. May be in classpath passed to task rather than Ant's classpath.</td>
    <td><a href="http://www.junit.org/" target="_top">http://www.junit.org/</a></td>
  </tr>
  <tr>
    <td>xalan.jar</td>
    <td>junitreport task</td>
    <td><a href="http://xml.apache.org/xalan-j/" target="_top">http://xml.apache.org/xalan-j/</a></td>
  </tr>
  <tr>
    <td>stylebook.jar</td>
    <td>stylebook task</td>
    <td>CVS repository of <a href="http://xml.apache.org/cvs.html" target="_top">http://xml.apache.org/cvs.html</a></td>
  </tr>
  <tr>
    <td>antlr.jar</td>
    <td>antlr task</td>
    <td><a href="http://www.antlr.org/" target="_top">http://www.antlr.org/</a></td>
  </tr>
  <tr>
    <td>bsf.jar</td>
    <td>script task
      <p>
        <strong>Note</strong>: Ant 1.6 and later require Apache BSF, not
        the IBM version.  I.e. you need BSF 2.3.0-rc1 or later.
      </p>
      <p>
        <strong>Note</strong>: BSF 2.4.0 is needed to use a post 1.5R3 version
        of rhino's javascript.
      </p>
      <p>
        <strong>Note</strong>: BSF 2.4.0 uses jakarata-commons-logging
        so it needs the commons-logging.jar.
      </p>
      </td>
    <td><a href="http://jakarta.apache.org/bsf/" target="_top">http://jakarta.apache.org/bsf/</a></td>
  </tr>
  <tr>
    <td>Groovy jars</td>
    <td>Groovy with script and scriptdef tasks<br>
      You need to get the groovy jar and two asm jars from a groovy
      installation. The jars are groovy-[version].jar, asm-[vesion].jar and
      asm-util-[version].jar and antlr-[version].jar.
      As of groovy version 1.0-JSR-06, the jars are
      groovy-1.0-JSR-06.jar, antlr-2.7.5.jar, asm-2.2.jar and asm-util-2.2.jar.
      Alternatively one may use the embedded groovy jar file.
      This is located in the embedded directory of the groovy distribution.
      This bundles all the needed jar files into one jar file.
      It is called groovy-all-[version].jar.
    </td>
    <td>
      <a href="http://groovy.codehaus.org/">http://groovy.codehaus.org/</a>
      <br>
      The asm jars are also available from the creators of asm -
      <a href="http://asm.objectweb.org/">http://asm.objectweb.org/</a>
    </td>
  </tr>
  <tr>
    <td>netrexx.jar</td>
    <td>netrexx task, Rexx with the script task</td>
    <td><a href="http://www.ibm.com/software/awdtools/netrexx/download.html" target="_top">
        http://www.ibm.com/software/awdtools/netrexx/download.html</a></td>
  </tr>
  <tr>
    <td>js.jar</td>
    <td>Javascript with script task<br>
    If you use Apache BSF 2.3.0-rc1, you must use rhino 1.5R3 (later
    versions of BSF (e.g. version 2.4.0) work with 1.5R4 and higher).</td>
    <td><a href="http://www.mozilla.org/rhino/" target="_top">http://www.mozilla.org/rhino/</a></td>
  </tr>
  <tr>
    <td>jython.jar</td>
    <td>Python with script task<br>
    Warning : jython.jar also contains classes from jakarta-oro.
    Remove these classes if you are also using jakarta-oro.</td>
    <td><a href="http://jython.sourceforge.net/" target="_top">http://jython.sourceforge.net/</a></td>
  </tr>
  <tr>
    <td>jpython.jar</td>
    <td>Python with script task <b>deprecated, jython is the prefered engine</b></td>
    <td><a href="http://www.jpython.org/" target="_top">http://www.jpython.org/</a></td>
  </tr>
  <tr>
    <td>jacl.jar and tcljava.jar</td>
    <td>TCL with script task</td>
    <td><a href="http://www.scriptics.com/software/java/" target="_top">http://www.scriptics.com/software/java/</a></td>
  </tr>
  <tr>
    <td>BeanShell JAR(s)</td>
    <td>BeanShell with script task.
      <br>
    <strong>Note</strong>: Ant requires BeanShell version 1.3 or
      later</td>
    <td><a href="http://www.beanshell.org/" target="_top">http://www.beanshell.org/</a></td>
  </tr>
  <tr>
    <td>jruby.jar</td>
    <td>Ruby with script task</td>
    <td><a href="http://jruby.sourceforge.net/" target="_top">http://jruby.sourceforge.net/</a></td>
  </tr>
  <tr>
    <td>judo.jar</td>
    <td>Judoscript with script task</td>
    <td><a href="http://www.judoscript.com/index.html" target="_top">http://www.judoscript.com/index.html</a></td>
  </tr>
  <tr>
    <td>commons-logging.jar</td>
    <td>CommonsLoggingListener</td>
    <td><a href="http://jakarta.apache.org/commons/logging/index.html"
           target="_top">http://jakarta.apache.org/commons/logging/index.html</a></td>
  </tr>
  <tr>
    <td>log4j.jar</td>
    <td>Log4jListener</td>
    <td><a href="http://jakarta.apache.org/log4j/docs/index.html"
           target="_top">http://jakarta.apache.org/log4j/docs/index.html</a></td>
  </tr>
  <tr>
    <td><a name="commons-net">commons-net.jar</a></td>
    <td>ftp, rexec and telnet tasks<br>
    jakarta-oro 2.0.8 or later is required together with commons-net 1.4.0.<br>
    For all users, a minimum version of commons-net of 1.4.0 is recommended.  Earlier 
    versions did not support the full range of configuration options, and 1.4.0 is needed
    to compile Ant.
    </td>
    <td><a href="http://jakarta.apache.org/commons/net/index.html"
           target="_top">http://jakarta.apache.org/commons/net/index.html</a></td>
  </tr>
  <tr>
    <td>bcel.jar</td>
    <td>classfileset data type,
        JavaClassHelper used by the ClassConstants filter reader and
        optionally used by ejbjar for dependency determination
    </td>
    <td><a href="http://jakarta.apache.org/bcel/" target="_top">http://jakarta.apache.org/bcel/</a></td>
  </tr>
  <tr>
    <td>mail.jar</td>
    <td>Mail task with Mime encoding, and the MimeMail task</td>
    <td><a href="http://java.sun.com/products/javamail/"
        target="_top">http://java.sun.com/products/javamail/</a></td>
  </tr>
  <tr>
    <td>jsse.jar</td>
    <td>
Support for SMTP over TLS/SSL <br>
in the Mail task<br>
Already included Java 1.4+</td>
    <td><a href="http://java.sun.com/products/jsse/"
        target="_top">http://java.sun.com/products/jsse/</a></td>
  </tr>
  <tr>
    <td>activation.jar</td>
    <td>Mail task with Mime encoding, and the MimeMail task</td>
    <td><a href="http://java.sun.com/products/javabeans/glasgow/jaf.html"
        target="_top">http://java.sun.com/products/javabeans/glasgow/jaf.html</a></td>
  </tr>
  <tr>
    <td>jdepend.jar</td>
    <td>jdepend task</td>
    <td><a href="http://www.clarkware.com/software/JDepend.html"
        target="_top">http://www.clarkware.com/software/JDepend.html</a></td>
  </tr>
  <tr>
    <td>resolver.jar <b>1.1beta or later</b></td>
    <td>xmlcatalog datatype <em>only if support for external catalog files is desired</em></td>
    <td><a href="http://xml.apache.org/commons/"
    target="_top">http://xml.apache.org/commons/</a>.</td>
  </tr>
  <tr>
    <td>jsch.jar <b>0.1.29 or later</b></td>
    <td>sshexec and scp tasks</td>
    <td><a href="http://www.jcraft.com/jsch/index.html"
        target="_top">http://www.jcraft.com/jsch/index.html</a></td>
  </tr>
  <tr>
    <td>JAI - Java Advanced Imaging</td>
    <td>image task</td>
    <td><a href="http://java.sun.com/products/java-media/jai/"
        target="_top">http://java.sun.com/products/java-media/jai/</a></td>
  </tr>
  <tr>
    <td>Starteam SDK</td>
    <td>Starteam version management tasks</td>
    <td><a href="http://www.borland.com/downloads/download_starteam.html"
        target="_top">http://www.borland.com/downloads/download_starteam.html</a></td>
  </tr>
</table>
<br>
<h2><a name="Troubleshooting">Troubleshooting</a></h2>


<h3><a name="diagnostics">Diagnostics</a></h3>

<p> Ant has a built in diagnostics feature. If you run <code>ant
-diagnostics</code> ant will look at its internal state and print it out. This
code will check and print the following things. </p>

<ul>

<li>Where Ant is running from. Sometimes you can be surprised.</li>    
    
<li>The version of ant.jar and of the ant-*.jar containing the optional tasks -
    and whether they match</li>

<li>Which JAR files are int ANT_HOME/lib

<li>Which optional tasks are available. If a task is not listed as being
available, either it is not present, or libraries that it depends on are
absent.</li>


<li>XML Parser information</li>

<li>JVM system properties
</li>

<li>The status of the temp directory. If this is not writeable, or its clock is
horribly wrong (possible if it is on a network drive), a lot of tasks will fail
with obscure error messages.</li>

<li>The current time zone as Java sees it. If this is not what it should be for
your location, then dependency logic may get confused.

</ul>

<p>
    Running <code>ant -diagnostics</code> is a good way to check that ant is 
    installed. It is also a first step towards self-diagnosis of any problem. 
    Any configuration problem reported to the Employee mailing list will probably
    result ins someone asking you to run the command and show the results, so 
    save time by using it yourself.
</p>

<p>
    For under-IDE diagostics, use the &lt;diagnostics&gt; task to run the same 
    tests as an ant task. This can be added to a diagnostics target in a build 
    file to see what tasks are available under the IDE, what the XML parser and 
    classpath is, etc. 
</p>

<h3><a name="ant-Employee">Employee mailing list</a></h3>

<p> If you cannot get Ant installed or working, the Ant Employee mailing list is the
best place to start with any problem. Please do your homework first, make sure
that it is not a <a href="#classpath">CLASSPATH</a> problem, and run a <a
href="#diagnostics">diagnostics check</a> to see what Ant thinks of its own
state. Why the Employee list, and not the developer list?
Because there are more users than developers, so more people who can help you. </p>

<p>

Please only file a bug report against Ant for a configuration/startup problem if
there really is a fixable bug in Ant related to configuration, such as it not
working on a particular platform, with a certain JVM version, etc, or if you are
advised to do it by the Employee mailing list.
</p>




</body>
</html>
