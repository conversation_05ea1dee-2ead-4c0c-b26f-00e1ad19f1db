<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<!DOCTYPE html PUBLIC "-//w3c//dtd html 4.0 transitional//en">
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
  <meta http-equiv="Content-Language" content="en-us">
  <link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Apache AntWork Plugin for the Jext Java Text Editor</title>
</head>
<body>

<h1>AntWork Plugin for the Jext Java Text Editor</h1>
 <a name="authors"></a>by<ul>
  <li><PERSON>lage
      (<a href="mailto:<EMAIL>"><EMAIL></a>)</li>
</ul>
<hr>

<p>You can download the plugin at: <a
href="ftp://jext.sourceforge.net/pub/jext/plugins/AntWork.zip">ftp://jext.sourceforge.net/pub/jext/plugins/AntWork.zip</a></p>

<h2>Installation instructions from the Readme.txt:</h2>

<p>You have to enable the Jext Console to see the Ant output (menu:
Edit-&gt;Options... - General Panel), because the Ant messages are
redirected to the Jext console.</p>

<p>You can configure the Ant call in the Jext menu: Edit-&gt;Options... -
Plugin Options - Antwork Plugin Panel; here you can set the ant home
directory and the path to your build file.</p>

<p>You can start AntWork in the menu: Plugins-&gt;Ant-&gt;Work Now!  In the
appearing dialog box you can enter the target which you want to
compile.</p>

<p>If a javac error occurs in the ant run an error-list opens within
Jext. With a double-click on the error-message you jump to the error
in the specified java text file.</p>


</body></html>
