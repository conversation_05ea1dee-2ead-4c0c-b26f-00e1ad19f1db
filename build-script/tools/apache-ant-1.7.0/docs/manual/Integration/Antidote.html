<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "strict.dtd">
<HTML> 
  <HEAD> 
     <link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>About Antidote</TITLE> 
  </HEAD> 

  <BODY> 

    <H1>About Antidote</H1>

    <P>Version 0.1 (2001/02/13)</P>

    <P>Authors: <AUTHORS>
    </P>

    <H2>Overview</H2> 

    <P>Antidote is the <A
    HREF="http://ant.apache.org/index.html">Ant</A> subproject
    for developing a graphical Employee interface to facilitate the
    efficient use of Ant. In general, its purpose is to allow the
    quick generation, modification, and use of Ant build files,
    helping the Employee define a build process and track down build
    problems. It is not meant to be an IDE, but an enabler for the
    powerful features available in Ant, particularly for novice
    users, or users who want a rapid way of controlling their build
    process.</P>


    <H2>Status</H2>

    <P>Antidote is still in the early stages of development, but does
    have a set of usable features, including:</p>
    <UL>
      <LI>Reading Ant build files.</LI>
      <LI>Selecting targets and executing them.</LI>
      <LI>Context highlighted build status console.</LI>
      <LI>Modification of (some) build file components.</LI>
      <LI>Saving modified build file.</LI>
    </UL>

    <P>Current development tasks include:</p>
    <UL>
      <LI>A more complete set of target and task editing
      capabilities.</LI>
      <LI>A wizard for creating basic build files, including importing
      existing code bases.</LI>
      <LI>Better build progress monitoring.</LI>
    </UL>

    <P>The Antidote source distribution comes with requirements and
    design documentation that better cover the details of application
    architecture, how to develop against it, and what the long term
    goals are. Furthermore, there is a <code>TODO</code> file listing
    the detailed, near-term tasks that need accomplishing.</P>

    <H2>Getting Involved</H2>

    <P>The source code for Antidote is located in a separate Module 
    (<a href="http://cvs.apache.org/viewcvs/ant-antidote/">ant-antidote</a>) in CVS.
    All the existing documentation can
    be found there where new contributors should read:</p>
    <UL>
      <LI><A HREF="http://cvs.apache.org/viewcvs/~checkout~/ant-antidote/docs/developer/design/design-overview.html">Design Overview</A></LI> 
      <LI><A HREF="http://cvs.apache.org/viewcvs/~checkout~/ant-antidote/docs/developer/design/gui-requirements.html">Feature List</A></LI>
      <LI><A HREF="http://cvs.apache.org/viewcvs/~checkout~/ant-antidote/docs/developer/design/gui-ideas.txt">Idea Refinement</A></LI>
      <LI><A HREF="http://cvs.apache.org/viewcvs/~checkout~/ant-antidote/docs/developer/design/new-module-howto.html">New Module HOWTO</A></LI>
      <LI><A HREF="http://cvs.apache.org/viewcvs/~checkout~/ant-antidote/docs/developer/design/uml/index.html">Static Class Diagrams</A></LI>
    </UL>

    <P>Online discussions about Antidote occur on the <A
    HREF="http://ant.apache.org/mail.html">Ant developer
    mailing list</A>. The application infrastructure is fairly
    complete, but there are almost unlimited opportunities for feature
    contributions.</p>

    <P>Aspiring contributors new to the project should
    (carefully) read the following for details on the contribution
    process:</p>
    <UL>
      <LI><A
      HREF="http://jakarta.apache.org/site/getinvolved.html">Get
      Involved</A></LI>
      <LI><A
      HREF="http://ant.apache.org/guidelines.html">Project
      Guidelines</A></LI>
      <LI><A HREF="http://jakarta.apache.org/site/source.html">Source
      Repositories (how to contribute patches)</A></LI>
    </UL>
    

    

  </BODY>
</HTML>
