<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">

<html>
<head>
  <link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>FilterChains and FilterReaders</title>
</head>

<body>

<h2>FilterChains and FilterReaders</h2>
Consider the flexibility of Unix pipes.  If you wanted,
for example, to copy just those lines that contained the
string blee from the first 10 lines of a text file 'foo'
(<em>you wouldn't want to filter a binary file</em>)
to a file 'bar', you would do something like:<p>
<code>
cat foo|head -n10|grep blee &gt; bar
</code><p>
Ant was not flexible enough.  There was no way for the
<code>&lt;copy&gt;</code> task to do something similar.  If you wanted
the <code>&lt;copy&gt;</code> task to get the first 10 lines, you would have
had to create special attributes:<p>
<code>
&lt;copy file=&quot;foo&quot; tofile=&quot;bar&quot; head=&quot;10&quot; contains=&quot;blee&quot;/&gt;
</code><p>
The obvious problem thus surfaced: Ant tasks would not be able
to accommodate such data transformation attributes as they would
be endless.  The task would also not know in which order these
attributes were to be interpreted.  That is, must the task execute the
contains attribute first and then the head attribute or vice-versa?
What Ant tasks needed was a mechanism to allow pluggable filter (data
transformer) chains.  Ant would provide a few filters for which there
have been repeated requests.  Users with special filtering needs
would be able to easily write their own and plug them in.<p>

The solution was to refactor data transformation oriented
tasks to support FilterChains.  A FilterChain is a group of
ordered FilterReaders.  Users can define their own FilterReaders
by just extending the java.io.FilterReader class.  Such custom
FilterReaders can be easily plugged in as nested elements of
<code>&lt;filterchain&gt;</code> by using <code>&lt;filterreader&gt;</code> elements.
<p>
Example:
<blockquote><pre>
&lt;copy file=&quot;${src.file}&quot; tofile=&quot;${dest.file}&quot;&gt;
  &lt;filterchain&gt;
    &lt;filterreader classname=&quot;your.extension.of.java.io.FilterReader&quot;&gt;
      &lt;param name=&quot;foo&quot; value=&quot;bar&quot;/&gt;
    &lt;/filterreader&gt;
    &lt;filterreader classname=&quot;another.extension.of.java.io.FilterReader&quot;&gt;
      &lt;classpath&gt;
        &lt;pathelement path="${classpath}"/&gt;
      &lt;/classpath&gt;
      &lt;param name=&quot;blah&quot; value=&quot;blee&quot;/&gt;
      &lt;param type=&quot;abra&quot; value=&quot;cadabra&quot;/&gt;
    &lt;/filterreader&gt;
  &lt;/filterchain&gt;
&lt;/copy&gt;
</pre></blockquote>

Ant provides some built-in filter readers.  These filter readers
can also be declared using a syntax similar to the above syntax.
However, they can be declared using some simpler syntax also.<p>
Example:
<blockquote><pre>
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;src.file.head&quot;&gt;
  &lt;filterchain&gt;
    &lt;headfilter lines=&quot;15&quot;/&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>
is equivalent to:
<blockquote><pre>
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;src.file.head&quot;&gt;
  &lt;filterchain&gt;
    &lt;filterreader classname=&quot;org.apache.tools.ant.filters.HeadFilter&quot;&gt;
      &lt;param name=&quot;lines&quot; value=&quot;15&quot;/&gt;
    &lt;/filterreader&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

The following built-in tasks support nested <code>&lt;filterchain&gt;</code> elements.<br>
<a href="../CoreTasks/concat.html">Concat</a>,<br>
<a href="../CoreTasks/copy.html">Copy</a>,<br>
<a href="../CoreTasks/loadfile.html">LoadFile</a>,<br>
<a href="../CoreTasks/loadproperties.html">LoadProperties</a>,<br>
<a href="../CoreTasks/move.html">Move</a><br><br>

A FilterChain is formed by defining zero or more of the following
nested elements.<br>
<a href="#filterreader">FilterReader</a><br>
<a href="#classconstants">ClassConstants</a><br>
<a href="#escapeunicode">EscapeUnicode</a><br>
<a href="#expandproperties">ExpandProperties</a><br>
<a href="#headfilter">HeadFilter</a><br>
<a href="#linecontains">LineContains</a><br>
<a href="#linecontainsregexp">LineContainsRegExp</a><br>
<a href="#prefixlines">PrefixLines</a><br>
<a href="#replacetokens">ReplaceTokens</a><br>
<a href="#stripjavacomments">StripJavaComments</a><br>
<a href="#striplinebreaks">StripLineBreaks</a><br>
<a href="#striplinecomments">StripLineComments</a><br>
<a href="#tabstospaces">TabsToSpaces</a><br>
<a href="#tailfilter">TailFilter</a><br>
<a href="#deletecharacters">DeleteCharacters</a><br>
<a href="#concatfilter">ConcatFilter</a><br>
<a href="#tokenfilter">TokenFilter</a><br>
<a href="../CoreTasks/fixcrlf.html">FixCRLF</a><br>

<h3><a name="filterreader">FilterReader</a></h3>

The filterreader element is the generic way to
define a filter.  User defined filter elements are
defined in the build file using this.  Please note that
built in filter readers can also be defined using this
syntax.

A FilterReader element must be supplied with a class name as
an attribute value.  The class resolved by this name must
extend java.io.FilterReader.  If the custom filter reader
needs to be parameterized, it must implement
org.apache.tools.type.Parameterizable.

<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Attribute</b></td>
    <td vAlign=top><b>Description</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>classname</td>
    <td vAlign=top>The class name of the filter reader.</td>
    <td vAlign=top align="center">Yes</td>
  </tr>
</table>

<p>
<h4>Nested Elements:</h4>
<code>&lt;filterreader&gt;</code> supports <code>&lt;classpath&gt;</code> and <code>&lt;param&gt;</code>
as nested elements.  Each <code>&lt;param&gt;</code> element may take in the following
attributes - name, type and value.
<p>
The following FilterReaders are supplied with the default
distribution.

<h3><a name="classconstants">ClassConstants</a></h3>
<p>
  This filters basic constants defined in a Java Class,
  and outputs them in lines composed of the format <i>name</i>=<i>value</i>.
  This filter uses the <em>bcel</em> library to understand the Java Class file.
  See <a href="../install.html#librarydependencies">Library Dependencies</a>.
<p>
  <p>
    <em><b>Important:</b></em>
    This filter is different from most of the other filters.
    Most of the filters operate on a sequence of characters.
    This filter operates on the sequence of bytes that makes up
    a class. However the bytes arrive to the filter as a sequence
    of characters. This means that one must be careful on the
    choice of character encoding to use. Most encoding lose information
    on conversion from an arbitary sequence of bytes to characters
    and back again to bytes. In particular the usual default
    character encodings (CP152 and UTF-8) do.
    For this reason, <em>since Ant 1.7</em>, the character
    encoding <b>ISO-8859-1</b> is used to convert from characters back to
    bytes, so one <b>has</b> to use this encoding for reading the java
    class file.
<h4>Example:</h4>

This loads the basic constants defined in a Java class as Ant properties.

<blockquote><pre>
&lt;loadproperties srcfile="foo.class" encoding="ISO-8859-1"&gt;
  &lt;filterchain&gt;
    &lt;classconstants/&gt;
  &lt;/filterchain&gt;
&lt;/loadproperties&gt;
</pre></blockquote>

This loads the constants from a Java class file as Ant properties,
prepending the names with a prefix.

    <blockquote><pre>
&lt;loadproperties srcfile="build/classes/org/acme/bar.class"
                encoding="ISO-8859-1"&gt;
  &lt;filterchain&gt;
    &lt;classconstants/&gt;
    &lt;prefixlines prefix="ini."/&gt;
  &lt;/filterchain&gt;
&lt;/loadproperties&gt;
</pre></blockquote>
<h3><a name="escapeunicode">EscapeUnicode</a></h3>
<p>
This filter converts its input by changing all non US-ASCII characters
into their equivalent unicode escape backslash u plus 4 digits.</p>

<p><em>since Ant 1.6</em></p>

<h4>Example:</h4>

This loads the basic constants defined in a Java class as Ant properties.
<blockquote><pre>
&lt;loadproperties srcfile=&quot;non_ascii_property.properties&quot;&gt;
  &lt;filterchain&gt;
    &lt;filterreader classname=&quot;org.apache.tools.ant.filters.EscapeUnicode&quot;/&gt;
  &lt;/filterchain&gt;
&lt;/loadproperties&gt;
</pre></blockquote>

Convenience method:
<blockquote><pre>
&lt;loadproperties srcfile=&quot;non_ascii_property.properties&quot;&gt;
  &lt;filterchain&gt;
    &lt;escapeunicode/&gt;
  &lt;/filterchain&gt;
&lt;/loadproperties&gt;
</pre></blockquote>

<h3><a name="expandproperties">ExpandProperties</a></h3>
<p>
If the data contains data that represents Ant
properties (of the form ${...}), that is substituted
with the property's actual value.
<p>
<h4>Example:</h4>

This results in the property modifiedmessage holding the value
&quot;All these moments will be lost in time, like teardrops in the rain&quot;
<blockquote><pre>
&lt;echo
  message=&quot;All these moments will be lost in time, like teardrops in the ${weather}&quot;
  file=&quot;loadfile1.tmp&quot;
  /&gt;
&lt;property name=&quot;weather&quot; value=&quot;rain&quot;/&gt;
&lt;loadfile property=&quot;modifiedmessage&quot; srcFile=&quot;loadfile1.tmp&quot;&gt;
  &lt;filterchain&gt;
    &lt;filterreader classname=&quot;org.apache.tools.ant.filters.ExpandProperties&quot;/&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

Convenience method:
<blockquote><pre>
&lt;echo
  message=&quot;All these moments will be lost in time, like teardrops in the ${weather}&quot;
  file=&quot;loadfile1.tmp&quot;
  /&gt;
&lt;property name=&quot;weather&quot; value=&quot;rain&quot;/&gt;
&lt;loadfile property=&quot;modifiedmessage&quot; srcFile=&quot;loadfile1.tmp&quot;&gt;
  &lt;filterchain&gt;
    &lt;expandproperties/&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

<h3><a name="headfilter">HeadFilter</a></h3>

This filter reads the first few lines from the data supplied to it.

<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Parameter Name</b></td>
    <td vAlign=top><b>Parameter Value</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>lines</td>
    <td vAlign=top align="center">Number of lines to be read.
    Defaults to &quot;10&quot; <br> A negative value means that all lines are
    passed (useful with <i>skip</i>)</td>
    <td vAlign=top align="center">No</td>
  </tr>
  <tr>
    <td vAlign=top>skip</td>
    <td vAlign=top align="center">Number of lines to be skipped (from the beginning).
    Defaults to &quot;0&quot;</td>
    <td vAlign=top align="center">No</td>
  </tr>
</table>
<p>
<h4>Example:</h4>

This stores the first 15 lines of the supplied data in the property src.file.head
<blockquote><pre>
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;src.file.head&quot;&gt;
  &lt;filterchain&gt;
    &lt;filterreader classname=&quot;org.apache.tools.ant.filters.HeadFilter&quot;&gt;
      &lt;param name=&quot;lines&quot; value=&quot;15&quot;/&gt;
    &lt;/filterreader&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

Convenience method:
<blockquote><pre>
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;src.file.head&quot;&gt;
  &lt;filterchain&gt;
    &lt;headfilter lines=&quot;15&quot;/&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

This stores the first 15 lines, skipping the first 2 lines, of the supplied data
in the property src.file.head. (Means: lines 3-17)
<blockquote><pre>
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;src.file.head&quot;&gt;
  &lt;filterchain&gt;
    &lt;headfilter lines=&quot;15&quot; skip=&quot;2&quot;/&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

See the testcases for more examples (<i>src\etc\testcases\filters\head-tail.xml</i> in the
source distribution).

<h3><a name="linecontains">LineContains</a></h3>

This filter includes only those lines that contain all the Employee-specified
strings.

<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Parameter Type</b></td>
    <td vAlign=top><b>Parameter Value</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>contains</td>
    <td vAlign=top align="center">Substring to be searched for.</td>
    <td vAlign=top align="center">Yes</td>
  </tr>
  <tr>
    <td vAlign=top>negate</td>
    <td vAlign=top align="center">Whether to select
      <i>non-</i>matching lines only. <b>Since Ant 1.7</b></td>
    <td vAlign=top align="center">No</td>
  </tr>
</table>
<p>
<h4>Example:</h4>

This will include only those lines that contain <code>foo</code> and
<code>bar</code>.
<blockquote><pre>
&lt;filterreader classname=&quot;org.apache.tools.ant.filters.LineContains&quot;&gt;
  &lt;param type=&quot;contains&quot; value=&quot;foo&quot;/&gt;
  &lt;param type=&quot;contains&quot; value=&quot;bar&quot;/&gt;
&lt;/filterreader&gt;
</pre></blockquote>

Convenience method:
<blockquote><pre>
&lt;linecontains&gt;
  &lt;contains value=&quot;foo&quot;/&gt;
  &lt;contains value=&quot;bar&quot;/&gt;
&lt;/linecontains&gt;
</pre></blockquote>

Negation:
<blockquote><pre>
&lt;filterreader classname=&quot;org.apache.tools.ant.filters.LineContains&quot;&gt;
  &lt;param type=&quot;negate&quot; value=&quot;true&quot;/&gt;
  &lt;param type=&quot;contains&quot; value=&quot;foo&quot;/&gt;
  &lt;param type=&quot;contains&quot; value=&quot;bar&quot;/&gt;
&lt;/filterreader&gt;
</pre></blockquote>
<i>or</i>
<blockquote><pre>
&lt;linecontains negate=&quot;true&quot;&gt;
  &lt;contains value=&quot;foo&quot;/&gt;
  &lt;contains value=&quot;bar&quot;/&gt;
&lt;/linecontains&gt;
</pre></blockquote>

<h3><a name="linecontainsregexp">LineContainsRegExp</a></h3>

Filter which includes only those lines that contain the Employee-specified
regular expression matching strings.

<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Parameter Type</b></td>
    <td vAlign=top><b>Parameter Value</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>regexp</td>
    <td vAlign=top align="center">Regular expression to be searched for.</td>
    <td vAlign=top align="center">Yes</td>
  </tr>
  <tr>
    <td vAlign=top>negate</td>
    <td vAlign=top align="center">Whether to select
      <i>non-</i>matching lines only. <b>Since Ant 1.7</b></td>
    <td vAlign=top align="center">No</td>
  </tr>
</table>

See <a href="regexp.html">Regexp Type</a> for the description of the nested element regexp and of
the choice of regular expression implementation.
<h4>Example:</h4>

This will fetch all those lines that contain the pattern <code>foo</code>
<blockquote><pre>
&lt;filterreader classname=&quot;org.apache.tools.ant.filters.LineContainsRegExp&quot;&gt;
  &lt;param type=&quot;regexp&quot; value=&quot;foo*&quot;/&gt;
&lt;/filterreader&gt;
</pre></blockquote>

Convenience method:
<blockquote><pre>
&lt;linecontainsregexp&gt;
  &lt;regexp pattern=&quot;foo*&quot;/&gt;
&lt;/linecontainsregexp&gt;
</pre></blockquote>

Negation:
<blockquote><pre>
&lt;filterreader classname=&quot;org.apache.tools.ant.filters.LineContainsRegExp&quot;&gt;
  &lt;param type=&quot;negate&quot; value=&quot;true&quot;/&gt;
  &lt;param type=&quot;regexp&quot; value=&quot;foo*&quot;/&gt;
&lt;/filterreader&gt;
</pre></blockquote>
<i>or</i>
<blockquote><pre>
&lt;linecontainsregexp negate=&quot;true&quot;&gt;
  &lt;regexp pattern=&quot;foo*&quot;/&gt;
&lt;/linecontainsregexp&gt;
</pre></blockquote>

<h3><a name="prefixlines">PrefixLines</a></h3>

Attaches a prefix to every line.

<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Parameter Name</b></td>
    <td vAlign=top><b>Parameter Value</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>prefix</td>
    <td vAlign=top align="center">Prefix to be attached to lines.</td>
    <td vAlign=top align="center">Yes</td>
  </tr>
</table>
<p>
<h4>Example:</h4>

This will attach the prefix <code>Foo</code> to all lines.
<blockquote><pre>
&lt;filterreader classname=&quot;org.apache.tools.ant.filters.PrefixLines&quot;&gt;
  &lt;param name=&quot;prefix&quot; value=&quot;Foo&quot;/&gt;
&lt;/filterreader&gt;
</pre></blockquote>

Convenience method:
<blockquote><pre>
&lt;prefixlines prefix=&quot;Foo&quot;/&gt;
</pre></blockquote>

<h3><a name="replacetokens">ReplaceTokens</a></h3>

This filter reader replaces all strings that are
sandwiched between begintoken and endtoken with
Employee defined values.

<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Parameter Type</b></td>
    <td vAlign=top><b>Parameter Name</b></td>
    <td vAlign=top><b>Parameter Value</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>tokenchar</td>
    <td vAlign=top>begintoken</td>
    <td vAlign=top>Character marking the
    beginning of a token.  Defaults to @</td>
    <td vAlign=top align="center">No</td>
  </tr>
  <tr>
    <td vAlign=top>tokenchar</td>
    <td vAlign=top>endtoken</td>
    <td vAlign=top>Character marking the
    end of a token.  Defaults to @</td>
    <td vAlign=top align="center">No</td>
  </tr>
  <tr>
    <td vAlign=top>token</td>
    <td vAlign=top>User defined String.</td>
    <td vAlign=top>User defined search String.</td>
    <td vAlign=top align="center">Yes</td>
  </tr>
  <tr>
    <td vAlign=top>propertiesfile</td>
    <td vAlign=top>Not applicable.</td>
    <td vAlign=top>Properties file to take tokens from.</td>
    <td vAlign=top align="center">No</td>
  </tr>
</table>
<p>

<h4>Example:</h4>

This replaces occurrences of the string &#64;DATE&#64; in the data
with today's date and stores it in the property ${src.file.replaced}
<blockquote><pre>
&lt;tstamp/&gt;
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;${src.file.replaced}&quot;&gt;
  &lt;filterchain&gt;
    &lt;filterreader classname=&quot;org.apache.tools.ant.filters.ReplaceTokens&quot;&gt;
      &lt;param type=&quot;token&quot; name=&quot;DATE&quot; value=&quot;December 13 2006&quot;/&gt;
    &lt;/filterreader&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

Convenience method:
<blockquote><pre>
&lt;tstamp/&gt;
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;${src.file.replaced}&quot;&gt;
  &lt;filterchain&gt;
    &lt;replacetokens&gt;
      &lt;token key=&quot;DATE&quot; value=&quot;December 13 2006&quot;/&gt;
    &lt;/replacetokens&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

This will treat each properties file entry in sample.properties as a token/key pair :
<blockquote><pre>
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;${src.file.replaced}&quot;&gt;
  &lt;filterchain&gt;
    &lt;filterreader classname=&quot;org.apache.tools.ant.filters.ReplaceTokens&quot;&gt;
      &lt;param type=&quot;propertiesfile&quot; value=&quot;sample.properties&quot;/&gt;
    &lt;/filterreader&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
&lt;/filterchain&gt;
</pre></blockquote>

<h3><a name="stripjavacomments">StripJavaComments</a></h3>

This filter reader strips away comments from the data,
using Java syntax guidelines.  This filter does not
take in any parameters.
<p>
<h4>Example:</h4>

<blockquote><pre>
&lt;loadfile srcfile=&quot;${java.src.file}&quot; property=&quot;${java.src.file.nocomments}&quot;&gt;
  &lt;filterchain&gt;
    &lt;filterreader classname=&quot;org.apache.tools.ant.filters.StripJavaComments&quot;/&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

Convenience method:
<blockquote><pre>
&lt;loadfile srcfile=&quot;${java.src.file}&quot; property=&quot;${java.src.file.nocomments}&quot;&gt;
  &lt;filterchain&gt;
    &lt;stripjavacomments/&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

<h3><a name="striplinebreaks">StripLineBreaks</a></h3>

This filter reader strips away specific characters
from the data supplied to it.

<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Parameter Name</b></td>
    <td vAlign=top><b>Parameter Value</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>linebreaks</td>
    <td vAlign=top align="center">Characters that are to
    be stripped out.  Defaults to &quot;\r\n&quot;</td>
    <td vAlign=top align="center">No</td>
  </tr>
</table>
<p>
<h4>Examples:</h4>

This strips the '\r' and '\n' characters.
<blockquote><pre>
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;${src.file.contents}&quot;&gt;
  &lt;filterchain&gt;
    &lt;filterreader classname=&quot;org.apache.tools.ant.filters.StripLineBreaks&quot;/&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

Convenience method:
<blockquote><pre>
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;${src.file.contents}&quot;&gt;
  &lt;filterchain&gt;
    &lt;striplinebreaks/&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

This treats the '(' and ')' characters as line break characters and
strips them.
<blockquote><pre>
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;${src.file.contents}&quot;&gt;
  &lt;filterchain&gt;
    &lt;filterreader classname=&quot;org.apache.tools.ant.filters.StripLineBreaks&quot;&gt;
      &lt;param name=&quot;linebreaks&quot; value=&quot;()&quot;/&gt;
    &lt;/filterreader&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

<h3><a name="striplinecomments">StripLineComments</a></h3>

This filter removes all those lines that begin with strings
that represent comments as specified by the Employee.

<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Parameter Type</b></td>
    <td vAlign=top><b>Parameter Value</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>comment</td>
    <td vAlign=top align="center">Strings that identify a line as a comment
    when they appear at the start of the line.</td>
    <td vAlign=top align="center">Yes</td>
  </tr>
</table>
<p>
<h4>Examples:</h4>

This removes all lines that begin with #, --, REM, rem and //
<blockquote><pre>
&lt;filterreader classname=&quot;org.apache.tools.ant.filters.StripLineComments&quot;&gt;
  &lt;param type=&quot;comment&quot; value=&quot;#&quot;/&gt;
  &lt;param type=&quot;comment&quot; value=&quot;--&quot;/&gt;
  &lt;param type=&quot;comment&quot; value=&quot;REM &quot;/&gt;
  &lt;param type=&quot;comment&quot; value=&quot;rem &quot;/&gt;
  &lt;param type=&quot;comment&quot; value=&quot;//&quot;/&gt;
&lt;/filterreader&gt;
</pre></blockquote>

Convenience method:
<blockquote><pre>
&lt;striplinecomments&gt;
  &lt;comment value=&quot;#&quot;/&gt;
  &lt;comment value=&quot;--&quot;/&gt;
  &lt;comment value=&quot;REM &quot;/&gt;
  &lt;comment value=&quot;rem &quot;/&gt;
  &lt;comment value=&quot;//&quot;/&gt;
&lt;/striplinecomments&gt;
</pre></blockquote>

<h3><a name="tabstospaces">TabsToSpaces</a></h3>

This filter replaces tabs with spaces

<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Parameter Name</b></td>
    <td vAlign=top><b>Parameter Value</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>tablength</td>
    <td vAlign=top align="center">Defaults to &quot;8&quot;</td>
    <td vAlign=top align="center">No</td>
  </tr>
</table>
<p>
<h4>Examples:</h4>

This replaces tabs in ${src.file} with spaces.
<blockquote><pre>
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;${src.file.notab}&quot;&gt;
  &lt;filterchain&gt;
    &lt;filterreader classname=&quot;org.apache.tools.ant.filters.TabsToSpaces&quot;/&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

Convenience method:
<blockquote><pre>
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;${src.file.notab}&quot;&gt;
  &lt;filterchain&gt;
    &lt;tabstospaces/&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

<h3><a name="tailfilter">TailFilter</a></h3>

This filter reads the last few lines from the data supplied to it.

<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Parameter Name</b></td>
    <td vAlign=top><b>Parameter Value</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>lines</td>
    <td vAlign=top align="center">Number of lines to be read.
    Defaults to &quot;10&quot; <br> A negative value means that all lines are
    passed (useful with <i>skip</i>)</td>
    <td vAlign=top align="center">No</td>
  </tr>
  <tr>
    <td vAlign=top>skip</td>
    <td vAlign=top align="center">Number of lines to be skipped (from the end).
    Defaults to &quot;0&quot; </td>
    <td vAlign=top align="center">No</td>
  </tr>
</table>
<p>

<h4>Background:</h4>
With HeadFilter and TailFilter you can extract each part of a text file you want.
This graphic shows the dependencies:

<table cellSpacing=0 cellPadding=2 border=1>
<tr>
 <th> Content </th>
 <th></th>
 <th></th>
 <th></th>
 <th> Filter </th>
</tr>
<tr>
 <td> Line 1 </td>
 <td rowspan="2" bgcolor="#C0C0C0">&nbsp;</td>
 <td rowspan="9" bgcolor="#FF00FF">&nbsp;</td>
 <td rowspan="4">&nbsp;</td>
 <td rowspan="11">
    <table>
    <tr>
     <td bgcolor="#C0C0C0">&nbsp;</td>
     <td><pre>&lt;filterchain&gt;
    &lt;headfilter lines="2"/&gt;
&lt;/filterchain&gt;</pre></td>
    </tr>
    <tr>
     <td bgcolor="#FF00FF">&nbsp;</td>
     <td><pre>&lt;filterchain&gt;
    &lt;tailfilter lines="-1" skip="2"/&gt;
&lt;/filterchain&gt;</pre></td>
    </tr>
    <tr>
     <td bgcolor="#008000">&nbsp;</td>
     <td><pre>&lt;filterchain&gt;
    &lt;headfilter lines="-1" skip="2"/&gt;
&lt;/filterchain&gt;</pre></td>
    </tr>
    <tr>
     <td bgcolor="#0000FF">&nbsp;</td>
     <td><pre>&lt;filterchain&gt;
    &lt;headfilter lines="-1" skip="2"/&gt;
    &lt;tailfilter lines="-1" skip="2"/&gt;
&lt;/filterchain&gt;</pre></td>
    </tr>
    <tr>
     <td bgcolor="#00FF00">&nbsp;</td>
     <td><pre>&lt;filterchain&gt;
    &lt;tailfilter lines="2"/&gt;
&lt;/filterchain&gt;</pre></td>
    </tr>
    </table>
 </td>
</tr>
<tr>
 <td> Line 2 </td>
</tr>
<tr>
 <td> Line 3 </td>
 <td rowspan="9" bgcolor="#008000">&nbsp;</td>
</tr>
<tr>
 <td> Line 4 </td>
</tr>
<tr>
 <td> Line 5 </td>
 <td rowspan="3" bgcolor="#0000FF">&nbsp;</td>
</tr>
<tr>
 <td> Lines ... </td>
</tr>
<tr>
 <td> Line 95 </td>
</tr>
<tr>
 <td> Line 96 </td>
 <td rowspan="4">&nbsp;</td>
</tr>
<tr>
 <td> Line 97 </td>
</tr>
<tr>
 <td> Line 98 </td>
 <td rowspan="2" bgcolor="#00FF00">&nbsp;</td>
</tr>
<tr>
 <td> Line 99 </td>
</tr>
</table>



<h4>Examples:</h4>

This stores the last 15 lines of the supplied data in the property ${src.file.tail}
<blockquote><pre>
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;${src.file.tail}&quot;&gt;
  &lt;filterchain&gt;
    &lt;filterreader classname=&quot;org.apache.tools.ant.filters.TailFilter&quot;&gt;
      &lt;param name=&quot;lines&quot; value=&quot;15&quot;/&gt;
    &lt;/filterreader&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

Convenience method:
<blockquote><pre>
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;${src.file.tail}&quot;&gt;
  &lt;filterchain&gt;
    &lt;tailfilter lines=&quot;15&quot;/&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>


This stores the last 5 lines of the first 15 lines of the supplied
data in the property ${src.file.mid}
<blockquote><pre>
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;${src.file.mid}&quot;&gt;
  &lt;filterchain&gt;
    &lt;filterreader classname=&quot;org.apache.tools.ant.filters.HeadFilter&quot;&gt;
      &lt;param name=&quot;lines&quot; value=&quot;15&quot;/&gt;
    &lt;/filterreader&gt;
    &lt;filterreader classname=&quot;org.apache.tools.ant.filters.TailFilter&quot;&gt;
      &lt;param name=&quot;lines&quot; value=&quot;5&quot;/&gt;
    &lt;/filterreader&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

Convenience method:
<blockquote><pre>
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;${src.file.mid}&quot;&gt;
  &lt;filterchain&gt;
    &lt;headfilter lines=&quot;15&quot;/&gt;
    &lt;tailfilter lines=&quot;5&quot;/&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>


This stores the last 10 lines, skipping the last 2 lines, of the supplied data
in the property src.file.head. (Means: if supplied data contains 60 lines,
lines 49-58 are extracted)
<blockquote><pre>
&lt;loadfile srcfile=&quot;${src.file}&quot; property=&quot;src.file.head&quot;&gt;
  &lt;filterchain&gt;
    &lt;tailfilter lines=&quot;10&quot; skip=&quot;2&quot;/&gt;
  &lt;/filterchain&gt;
&lt;/loadfile&gt;
</pre></blockquote>

<h3><a name="deletecharacters">DeleteCharacters</a></h3>

  <p>This filter deletes specified characters.</p>
  <p><em>since Ant 1.6</em></p>
  <p>This filter is only available in the convenience form.</p>

<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Parameter Name</b></td>
    <td vAlign=top><b>Parameter Value</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>chars</td>
    <td vAlign=top>
      The characters to delete. This attribute is
      <a href="#backslash">backslash enabled</a>.
    </td>
    <td vAlign=top align="center">Yes</td>
  </tr>
</table>
<p>
<h4>Examples:</h4>

Delete tabs and returns from the data.
<blockquote><pre>
&lt;deletecharacters chars="\t\r"/&gt;
</pre></blockquote>

<h3><a name="concatfilter">ConcatFilter</a></h3>
  <p>This filter prepends or appends the content file to the filtered files.</p>
  <p><em>since Ant 1.6</em></p>
<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Parameter Name</b></td>
    <td vAlign=top><b>Parameter Value</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>prepend</td>
    <td vAlign=top>
      The name of the file which content should be prepended to the file.
    </td>
    <td vAlign=top align="center">No</td>
  </tr>
  <tr>
    <td vAlign=top>append</td>
    <td vAlign=top>
      The name of the file which content should be appended to the file.
    </td>
    <td vAlign=top align="center">No</td>
  </tr>
</table>
<p>

<h4>Examples:</h4>

Do nothing:
<blockquote><pre>
&lt;filterchain&gt;
    &lt;concatfilter/&gt;
&lt;/filterchain&gt;
</pre></blockquote>

Adds a license text before each java source:
<blockquote><pre>
&lt;filterchain&gt;
    &lt;concatfilter prepend="apache-license-java.txt"/&gt;
&lt;/filterchain&gt;
</pre></blockquote>



<h3><a name="tokenfilter">TokenFilter</a></h3>
This filter tokenizes the inputstream into strings and passes these
strings to filters of strings. Unlike the other filterreaders, this does
not support params, only convenience methods are implemented.
The tokenizer and the string filters are defined by nested elements.
<p><em>since Ant 1.6</em></p>
<p>
Only one tokenizer element may be used, the LineTokenizer is the
default if none are specified.  A tokenizer
splits the input into token strings and trailing delimiter strings.
<p>
There may be zero or more string filters. A string filter processes
a token and either returns a string or a null.
It the string is not null it is passed to the next filter. This
proceeds until all the filters are called.
If a string is returned after all the filters, the string is
outputs with its associated token delimitier
(if one is present).
The trailing delimiter may be overridden by the <i>delimOutput</i>
attribute.
<p>
<a name="backslash"><em>blackslash interpretation</em></a>
A number of attributes (including <i>delimOutput</i>) interpret
backslash escapes. The following are understood: \n, \r, \f, \t
and \\.


<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Attribute</b></td>
    <td vAlign=top><b>Description</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>delimOutput</td>
    <td vAlign=top>
     This overrides the tokendelimiter
        returned by the tokenizer if it is not empty. This
     attribute is backslash enabled.
</td>
    <td vAlign=top align="center">No</td>
  </tr>
</table>
<p>

    The following tokenizers are provided by the default distribution.
    <p>
        <a href="#linetokenizer">LineTokenizer</a><br>
        <a href="#filetokenizer">FileTokenizer</a><br>
        <a href="#stringtokenizer">StringTokenizer</a><br>
    </p>

    The following string filters are provided by the default distribution.
    <p>
        <a href="#replacestring">ReplaceString</a><br>
        <a href="#containsstring">ContainsString</a><br>
        <a href="#replaceregex">ReplaceRegex</a><br>
        <a href="#containsregex">ContainsRegex</a><br>
        <a href="#trim">Trim</a><br>
        <a href="#ignoreblank">IgnoreBlank</a><br>
        <a href="#filterdeletecharacters">DeleteCharacters</a><br>
    </p>

    The following string filters are provided by the optional distribution.
    <p>
        <a href="#scriptfilter">ScriptFilter</a><br>
    </p>

Some of the filters may be used directly within a filter chain. In this
case a tokenfilter is created implicitly. An extra attribute "byline"
is added to the filter to specify whether to use a linetokenizer
(byline="true") or a filetokenizer (byline="false"). The default
is "true".
<p>

<p><b><em><a name="linetokenizer">LineTokenizer</a></em></b></p>
This tokenizer splits the input into lines.
The tokenizer delimits lines
by "\r", "\n" or "\r\n".
This is the default tokenizer.
<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Attribute</b></td>
    <td vAlign=top><b>Description</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>includeDelims</td>
    <td vAlign=top>
      Include the line endings in the token.
      Default is false.
    </td>
    <td vAlign=top align="center">No</td>
  </tr>
</table>
<h4>Examples:</h4>

Convert input current line endings to unix style line endings.
<blockquote><pre>
&lt;tokenfilter delimoutput=&quot;\n&quot;/&gt;
</pre></blockquote>


Remove blank lines.
<blockquote><pre>
&lt;tokenfilter&gt;
    &lt;ignoreblank/&gt;
&lt;/tokenfilter&gt;

</pre></blockquote>

<p><b><em><a name="filetokenizer">FileTokenizer</a></em></b></p>
This tokenizer treats <b>all</b> the input as a token. So be
careful not to use this on very large input.
<h4>Examples:</h4>

Replace the first occurrence of package with //package.
<blockquote><pre>
&lt;tokenfilter&gt;
      &lt;filetokenizer/&gt;
      &lt;replaceregex pattern="([\n\r]+[ \t]*|^[ \t]*)package"
                    flags="s"
                    replace="\1//package"/&gt;
&lt;/tokenfilter&gt;
</pre></blockquote>

<p><b><em><a name="stringtokenizer">StringTokenizer</a></em></b></p>
This tokenizer is based on java.util.StringTokenizer.
It splits up the input into strings separated by white space, or
by a specified list of delimiting characters.
If the stream starts with delimiter characters, the first
token will be the empty string (unless the <i>delimsaretokens</i>
attribute is used).

<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Attribute</b></td>
    <td vAlign=top><b>Description</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>delims</td>
    <td vAlign=top>The delimiter characters. White space
      is used if this is not set. (White space is defined
      in this case by java.lang.Character.isWhitespace()).
    </td>
    <td vAlign=top align="center">No</td>
  </tr>
  <tr>
    <td valign="top">delimsaretokens</td>
    <td valign="top">If this is true,
      each delimiter character is returned as a token.
      Default is false.
    </td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">suppressdelims</td>
    <td valign="top">
      If this is true, delimiters are not returned.
      Default is false.
    </td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td vAlign=top>includeDelims</td>
    <td vAlign=top>
      Include the delimiters in the token.
      Default is false.
    </td>
    <td vAlign=top align="center">No</td>
  </tr>
</table>

<h4>Examples:</h4>

Surround each non space token with a "[]".

<blockquote><pre>
&lt;tokenfilter&gt;
    &lt;stringtokenizer/&gt;
    &lt;replaceregex pattern="(.+)" replace="[\1]"/&gt;
&lt;/tokenfilter&gt;

</pre></blockquote>

<p><b><em><a name="replacestring">ReplaceString</a></em></b></p>
This is a simple filter to replace strings.
This filter may be used directly within a filterchain.

<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Attribute</b></td>
    <td vAlign=top><b>Description</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>from</td>
    <td vAlign=top>The string that must be replaced.</td>
    <td vAlign=top align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">to</td>
    <td valign="top">The new value for the replaced string. When omitted
      an empty string is used.
    </td>
    <td valign="top" align="center">No</td>
  </tr>
</table>

<h4>Examples:</h4>

Replace "sun" with "moon".

<blockquote><pre>
&lt;tokenfilter&gt;
    &lt;replacestring from="sun" to="moon"/&gt;
&lt;/tokenfilter&gt;
</pre></blockquote>

<p><b><em><a name="containsstring">ContainsString</a></em></b></p>
This is a simple filter to filter tokens that contains
a specified string.

<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Attribute</b></td>
    <td vAlign=top><b>Description</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>contains</td>
    <td vAlign=top>The string that the token must contain.</td>
    <td vAlign=top align="center">Yes</td>
  </tr>
</table>

<h4>Examples:</h4>

Include only lines that contain "foo";

<blockquote><pre>
&lt;tokenfilter&gt;
    &lt;containsstring contains="foo"/&gt;
&lt;/tokenfilter&gt;

</pre></blockquote>

<p><b><em><a name="replaceregex">ReplaceRegex</a></em></b></p>
This string filter replaces regular expressions.
This filter may be used directly within a filterchain.
<p>
 See <a href="regexp.html#implementation">Regexp Type</a>
concerning the choice of the implementation.
</p>

<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Attribute</b></td>
    <td vAlign=top><b>Description</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>pattern</td>
    <td vAlign=top>The regular expression pattern to match in
      the token.</td>
    <td vAlign=top align="center">Yes</td>
  </tr>
  <tr>
    <td vAlign=top>replace</td>
    <td vAlign=top>The substitution pattern to replace the matched
      regular expression. When omitted an empty string is used.</td>
    <td vAlign=top align="center">No</td>
  </tr>
  <tr>
    <td vAlign=top>flags</td>
    <td vAlign=top>See
<a href="../OptionalTasks/replaceregexp.html">ReplaceRegexp</a>
for an explanation of regex flags.</td>
    <td vAlign=top align="center">No</td>
  </tr>
</table>
<h4>Examples:</h4>

Replace all occurrences of "hello" with "world", ignoring case.

<blockquote><pre>
&lt;tokenfilter&gt;
    &lt;replaceregex pattern="hello" replace="world" flags="gi"/&gt;
&lt;/tokenfilter&gt;

</pre></blockquote>

<p><b><em><a name="containsregex">ContainsRegex</a></em></b></p>
This filters strings that match regular expressions.
The filter may optionally replace the matched regular expression.
This filter may be used directly within a filterchain.
<p>
See
<a href="regexp.html#implementation">Regexp Type</a>
concerning the choice of regular expression implementation.
</p>
<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Attribute</b></td>
    <td vAlign=top><b>Description</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>pattern</td>
    <td vAlign=top>The regular expression pattern to match in
      the token.</td>
    <td vAlign=top align="center">Yes</td>
  </tr>
  <tr>
    <td vAlign=top>replace</td>
    <td vAlign=top>The substitution pattern to replace the matched
      regular expression. When omitted the orignal token is returned.
    </td>
    <td vAlign=top align="center">No</td>
  </tr>
  <tr>
    <td vAlign=top>flags</td>
    <td vAlign=top>See
<a href="../OptionalTasks/replaceregexp.html">ReplaceRegexp</a>
for an explanation of regex flags.</td>
    <td vAlign=top align="center">No</td>
  </tr>
</table>

<h4>Examples:</h4>

Filter lines that contain "hello" or "world", ignoring case.

<blockquote><pre>
&lt;tokenfilter&gt;
    &lt;containsregex pattern="(hello|world)" flags="i"/&gt;
&lt;/tokenfilter&gt;

</pre></blockquote>

This example replaces lines like "SUITE(TestSuite, bits);" with
"void register_bits();" and removes other lines.

<blockquote><pre>
&lt;tokenfilter&gt;
    &lt;containsregex
        pattern="^ *SUITE\(.*,\s*(.*)\s*\).*"
        replace="void register_\1();"/&gt;
&lt;/tokenfilter&gt;
</pre></blockquote>

<p><b><em><a name="trim">Trim</a></em></b></p>
This filter trims whitespace from the start and end of
tokens.
This filter may be used directly within a filterchain.
<p><b><em><a name="ignoreblank">IgnoreBlank</a></em></b></p>
This filter removes empty tokens.
This filter may be used directly within a filterchain.
<p><b><em><a name="filterdeletecharacters">DeleteCharacters</a></em></b></p>
This filter deletes specified characters from tokens.

<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Attribute</b></td>
    <td vAlign=top><b>Description</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>chars</td>
    <td vAlign=top>The characters to delete. This attribute
      is backslash enabled.</td>
    <td vAlign=top align="center">Yes</td>
  </tr>
</table>

<h4>Examples:</h4>

Delete tabs from lines, trim the lines and removes empty lines.

<blockquote><pre>
&lt;tokenfilter&gt;
    &lt;deletecharacters chars="\t"/&gt;
    &lt;trim/&gt;
    &lt;ignoreblank/&gt;
&lt;/tokenfilter&gt;

</pre></blockquote>

<p><b><em><a name="scriptfilter">ScriptFilter</a></em></b></p>
This is an optional filter that executes a script in a
<a href="http://jakarta.apache.org/bsf" target="_top">Apache BSF</a>
  or
  <a href="https://scripting.dev.java.net">JSR 223</a>
supported language.</p>
See the <a href="../OptionalTasks/script.html">Script</a> task for
an explanation of scripts and dependencies.
</p>
<p>
The script is provided with an object <i>self</i> that has
getToken() and setToken(String) methods.
The getToken() method returns the current token. The setToken(String)
method replaces the current token.
</p>

This filter may be used directly within a filterchain.<p>
<table cellSpacing=0 cellPadding=2 border=1>
  <tr>
    <td vAlign=top><b>Attribute</b></td>
    <td vAlign=top><b>Description</b></td>
    <td vAlign=top align="center"><b>Required</b></td>
  </tr>
  <tr>
    <td vAlign=top>language</td>
    <td vAlign=top> The programming language the script is written in.
Must be a supported Apache BSF or JSR 223 language</td>
    <td vAlign=top align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">manager</td>
    <td valign="top">
      The script engine manager to use.
      See the <a href="../OptionalTasks/script.html">script</a> task
      for using this attribute.
    </td>
    <td valign="top" align="center">No - default is "auto"</td>
  </tr>
  <tr>
    <td vAlign=top>src</td>
    <td vAlign=top>The location of the script as a file, if not inline
    </td>
    <td vAlign=top align="center">No</td>
  </tr>
  <tr>
    <td valign="top">setbeans</td>
    <td valign="top">whether to have all properties, references and targets as
      global variables in the script.</td>
    <td valign="top" align="center">No, default is "true".</td>
  </tr>
  <tr>
    <td valign="top">classpath</td>
    <td valign="top">
      The classpath to pass into the script.
    </td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">classpathref</td>
    <td valign="top">The classpath to use, given as a
       <a href="../using.html#references">reference</a> to a path defined elsewhere.
    <td align="center" valign="top">No</td>
  </tr>
</table>
  <p>
    This filter can take a nested &lt;classpath&gt; element.
    See the <a href="../OptionalTasks/script.html">script</a> task
    on how to use this element.
  </p>
<h4>Examples:</h4>

Convert to uppercase:
<blockquote><pre>
&lt;tokenfilter&gt;
    &lt;scriptfilter language="javascript"&gt;
        self.setToken(self.getToken().toUpperCase());
    &lt;/scriptfilter&gt;
&lt;/tokenfilter&gt;
</pre></blockquote>

Remove lines containing the string "bad" while
copying text files:
  <blockquote>
    <pre>
&lt;copy todir="dist"&gt;
  &lt;fileset dir="src" includes="**/*.txt"/&gt;
  &lt;filterchain&gt;
    &lt;scriptfilter language="beanshell"&gt;
        if (self.getToken().indexOf("bad") != -1) {
            self.setToken(null);
        }
    &lt;/scriptfilter&gt;
  &lt;/filterchain&gt;
&lt;/copy&gt;
    </pre>
  </blockquote>

<h4>Custom tokenizers and string filters</h4>

Custom string filters and tokenizers may be plugged in by
extending the interfaces org.apache.tools.ant.filters.TokenFilter.Filter
and org.apache.tools.ant.util.Tokenizer respectly.

They are defined the build file using <code>&lt;typedef/&gt;</code>. For
example a string filter that capitalizes words may be declared as:
<blockquote><pre>
package my.customant;
import org.apache.tools.ant.filters.TokenFilter;

public class Capitalize
    implements TokenFilter.Filter
{
    public String filter(String token) {
        if (token.length() == 0)
            return token;
        return token.substring(0, 1).toUpperCase() +
                token.substring(1);
   }
}
</pre></blockquote>

This may be used as follows:
<blockquote><pre>
  &lt;typedef type="capitalize" classname="my.customant.Capitalize"
           classpath="my.customant.path"/&gt;
  &lt;copy file="input" tofile="output"&gt;
    &lt;filterchain&gt;
      &lt;tokenfilter&gt;
        &lt;stringtokenizer/&gt;
        &lt;capitalize/&gt;
      &lt;/tokenfilter&gt;
    &lt;/filterchain&gt;
  &lt;/copy&gt;
</pre></blockquote>

</body></html>
