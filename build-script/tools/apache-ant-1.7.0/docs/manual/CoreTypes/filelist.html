<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>FileList Type</title>
</head>

<body>

<h2><a name="filelist">FileList</a></h2> 

<p>FileLists are explicitly named lists of files.  Whereas FileSets
act as filters, returning only those files that exist in the file
system and match specified patterns, FileLists are useful for
specifying files that may or may not exist.  Multiple files are
specified as a list of files, relative to the specified directory,
with no support for wildcard expansion (filenames with wildcards will be
included in the list unchanged).
FileLists can appear inside tasks that support this feature or as stand-alone
types.
</p>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">dir</td>
    <td valign="top">The base directory of this FileList.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">files</td>
    <td valign="top">The list of file names. This is a list of
    file name separated by whitespace, or by commas.</td>
    <td valign="top" align="center">
      Yes, unless there is a nested file element</td>
  </tr>
</table>
  <h4>Nested Element: file</h4>
  <p>
    This represents a file name. The nested element allows filenames containing
    white space and commas.
  </p>
  <p><em>Since Ant 1.6.2</em></p>
  <table border="1" cellpadding="2" cellspacing="0">
    <tr>
      <td valign="top"><b>Attribute</b></td>
      <td valign="top"><b>Description</b></td>
      <td align="center" valign="top"><b>Required</b></td>
    </tr>
    <tr>
      <td valign="top">name</td>
      <td valign="top">The name of the file.</td>
      <td valign="top" align="center">Yes</td>
    </tr>
</table>  
<h4>Examples</h4>
<blockquote><pre>
&lt;filelist 
    id=&quot;docfiles&quot; 
    dir=&quot;${doc.src}&quot;
    files=&quot;foo.xml,bar.xml&quot;/&gt; 
</pre></blockquote>

<p>The files <code>${doc.src}/foo.xml</code> and
<code>${doc.src}/bar.xml</code>.  Note that these files may not (yet)
actually exist.
</p>

<blockquote><pre>
&lt;filelist 
    id=&quot;docfiles&quot; 
    dir=&quot;${doc.src}&quot;
    files=&quot;foo.xml
           bar.xml&quot;/&gt; 
</pre></blockquote>

<p>Same files as the example above.</p>

<blockquote><pre>
&lt;filelist refid=&quot;docfiles&quot;/&gt; 
</pre></blockquote>

<p>Same files as the example above.</p>

<blockquote><pre>
&lt;filelist 
    id=&quot;docfiles&quot; 
    dir=&quot;${doc.src}&quot;&gt;
    &lt;file name="foo.xml"/&gt;
    &lt;file name="bar.xml"/&gt;
&lt;/filelist&gt;
</pre></blockquote>

<p>Same files as the example above.</p>



</body>
</html>

