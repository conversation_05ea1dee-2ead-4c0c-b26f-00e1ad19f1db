<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Regexp Type</title>
</head>

<body>

<h2><a name="regexp">Regexp</a></h2>
<p>
Regexp represents a regular expression.
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">pattern</td>
    <td valign="top">regular expression pattern</td>
    <td valign="top" align="center">Yes</td>
  </tr>
</table>

<h3>Examples</h3>
<blockquote><pre>
     &lt;regexp id="myregexp" pattern="alpha(.+)beta"/&gt;<br>
</pre></blockquote>
<p>
Defines a regular expression for later use with id myregexp.
</p>
<blockquote><pre>
     &lt;regexp refid="myregexp"/&gt;<br>
</pre></blockquote>
<p>
Use the regular expression with id myregexp.
</p>
<h3><a name="implementation">Choice of regular expression implementation</a></h3>
<p>
Ant comes with
wrappers for
<a href="http://java.sun.com/j2se/1.4/docs/api/java/util/regex/package-summary.html" target="_top">the java.util.regex package of JDK 1.4</a>,
<a href="http://jakarta.apache.org/regexp/" target="_top">jakarta-regexp</a>
and <a href="http://jakarta.apache.org/oro/" target="_top">jakarta-ORO</a>,
See <a href="../install.html#librarydependencies">installation dependencies</a>
 concerning the supporting libraries.</p>
<p>
The property <code>ant.regexp.regexpimpl</code> governs which regular expression implementation will be chosen.
Possible values for this property are :
<ul>
<li>
org.apache.tools.ant.util.regexp.Jdk14RegexpRegexp
</li>
<li>
org.apache.tools.ant.util.regexp.JakartaOroRegexp
</li>
<li>
org.apache.tools.ant.util.regexp.JakartaRegexpRegexp
</li>
</ul>
It can also be another implementation of the interface <code>org.apache.tools.ant.util.regexp.Regexp</code>.
If <code>ant.regexp.regexpimpl</code> is not defined, ant checks in the order Jdk14Regexp, JakartaOroRegexp,
 JakartaRegexp for the availability of the corresponding library. The first of these 3 which is found will be used.</p>
<p>
There are cross-platform issues for matches related to line terminator.
For example if you use $ to anchor your regular expression on the end of a line
the results might be very different depending on both your platform and the regular
expression library you use. It is 'highly recommended' that you test your pattern on
both Unix and Windows platforms before you rely on it.
<ul>
    <li>Jakarta Oro defines a line terminator as '\n' and is consistent with Perl.</li>
    <li>Jakarta RegExp uses a system-dependant line terminator.</li>
    <li>JDK 1.4 uses '\n', '\r\n', '\u0085', '\u2028', '\u2029' as a default
    but is configured in the wrapper to use only '\n' (UNIX_LINE)</li>
</ul>
<em>We <b>strongly</b> recommend that you use Jakarta Oro.</em>
</p>
<h3>Usage</h3>
The following tasks and types use the Regexp type :
<ul>
<li><a href="../OptionalTasks/replaceregexp.html">ReplaceRegExp task</a></li>
<li><a href="filterchain.html#linecontainsregexp">LineContainsRegexp filter</a></li>
</ul>
<p>
These string filters also use the mechanism of regexp to choose a regular expression implementation :
</p>
<ul>
<li><a href="filterchain.html#containsregex">ContainsRegex string filter</a></li>
<li><a href="filterchain.html#replaceregex">ReplaceRegex string filter</a></li>
</ul>

</body>
</html>
