<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Redirector Type</title>
</head>

<body>

<h2><a name="redirector">I/O redirection</a></h2>
<p>For many tasks, input and output can be defined in a fairly
straightforward fashion.  The <a href="../CoreTasks/exec.html">exec</a>
task, used to execute an external process, stands as a very
basic example.  The executed process may accept input, produce
output, or do either or both depending upon various circumstances.
Output may be classified as &quot;output&quot; or as &quot;error
output.&quot;  The <code>&lt;redirector&gt;</code> type provides a concrete means
of redirecting input and output featuring the use of
<a href="./mapper.html">File Mapper</a>s to specify
source (input) and destination (output/error) files.  <em>Since Ant 1.6.2</em>
<p>The <code>&lt;redirector&gt;</code> element accepts the following attributes:</p>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">output</td>
    <td valign="top">Name of a file to which output should be written.
      If the error stream is not also redirected to a file or property,
      it will appear in this output.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">error</td>
    <td valign="top">The file to which the standard error of the
      command should be redirected.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">logError</td>
    <td valign="top">This attribute is used when you wish to see
      error output in Ant's log and you are redirecting output to
      a file/property. The error output will not be included in
      the output file/property. If you redirect error with the
      <i>error</i> or <i>errorProperty</i> attributes, this will
      have no effect.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">append</td>
    <td valign="top">Whether output and error files should be
      appended to rather than overwritten. Defaults to
      <code>false</code>.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">createemptyfiles</td>
    <td valign="top">Whether output and error files should be
      created even when empty.  Defaults to <code>true</code>.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">outputproperty</td>
    <td valign="top">The name of a property in which the output of the
      command should be stored. Unless the error stream is redirected
      to a separate file or stream, this property will include the
      error output.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">errorproperty</td>
    <td valign="top">The name of a property in which the standard error
      of the command should be stored.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">input</td>
    <td valign="top">A file from which the executed command's standard input
      is taken. This attribute is mutually exclusive with the
      <i>inputstring</i> attribute.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">inputstring</td>
    <td valign="top">A string which serves as the input stream for the
      executed command. This attribute is mutually exclusive with the
      <i>input</i> attribute.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">inputencoding</td>
    <td valign="top">The input encoding.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">outputencoding</td>
    <td valign="top">The output encoding.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">errorencoding</td>
    <td valign="top">The error encoding.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">alwayslog</td>
    <td valign="top">Always send to the log in addition to
        any other destination. <i>Since Ant 1.6.3</i>.
    </td>
    <td align="center" valign="top">No, default is <code>false</code></td>
  </tr>
  <tr>
    <td valign="top">loginputstring</td>
    <td valign="top">Controls the display of <i>inputstring</i>'s value in
        log messages. Set to <code>false</code> when sending sensitive data
        (e.g. passwords) to external processes. <i>Since Ant 1.6.3</i>.
    </td>
    <td align="center" valign="top">No, default is <code>true</code></td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>
<h4>inputmapper</h4>
<p>A single <a href="./mapper.html">File Mapper</a> used to redirect process
input.  Multiple mapping results should concatenate all mapped files as input.
Mapping will ordinarily be performed on a task-specified sourcefile;
consult the documentation of the individual task for more details.
A nested <code>&lt;inputmapper&gt;</code> is not compatible with either of the
<i>input</i> or <i>inputstring</i> attributes.</p>
<h4>outputmapper</h4>
<p>A single <a href="./mapper.html">File Mapper</a> used to redirect process
output.  Mapping will ordinarily be performed on a task-specified sourcefile;
consult the documentation of the individual task for more details.
A nested <code>&lt;outputmapper&gt;</code> is not compatible with the
<i>output</i> attribute.</p>
<h4>errormapper</h4>
<p>A single <a href="./mapper.html">File Mapper</a> used to redirect error
output.  Mapping will ordinarily be performed on a task-specified sourcefile;
consult the documentation of the individual task for more details.
A nested <code>&lt;errormapper&gt;</code> is not compatible with the
<i>error</i> attribute.</p>
<h4>inputfilterchain</h4>
<p>A <a href="./filterchain.html">FilterChain</a> can be
applied to the process input.</p>
<h4>outputfilterchain</h4>
<p>A <a href="./filterchain.html">FilterChain</a> can be
applied to the process output.</p>
<h4>errorfilterchain</h4>
<p>A <a href="./filterchain.html">FilterChain</a> can be
applied to the error output.</p>
<h3>Usage</h3>
Tasks known to support I/O redirection:
<ul>
<li><a href="../CoreTasks/exec.html">Exec</a></li>
<li><a href="../CoreTasks/apply.html">Apply</a></li>
<li><a href="../CoreTasks/java.html">Java</a></li>
</ul>
<p>The expected behavior of a <code>&lt;redirector&gt;</code> is to a great degree
dependent on the supporting task.  Any possible points of confusion
should be noted at the task level.</p>


</body>
</html>

