<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Mapper Type</title>
</head>

<body>

<h2><a name="mapper">Mapping File Names</a></h2>
<p>Some tasks take source files and create target files. Depending on
the task, it may be quite obvious which name a target file will have
(using <a href="../CoreTasks/javac.html">javac</a>, you know there will be
<code>.class</code> files for your <code>.java</code> files) - in
other cases you may want to specify the target files, either to help
Ant or to get an extra bit of functionality.</p>
<p>While source files are usually specified as <a
href="fileset.html">fileset</a>s, you don't specify target files directly -
instead, you tell Ant how to find the target file(s) for one source file. An
instance of <code>org.apache.tools.ant.util.FileNameMapper</code> is
responsible for this. It constructs target file names based on rules
that can be parameterized with <code>from</code> and <code>to</code>
attributes - the exact meaning of which is implementation-dependent.</p>
<p>These instances are defined in <code>&lt;mapper&gt;</code> elements
with the following attributes:</p>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">type</td>
    <td valign="top">specifies one of the built-in implementations.</td>
    <td rowspan="2" align="center" valign="middle">Exactly one of these</td>
  </tr>
  <tr>
    <td valign="top">classname</td>
    <td valign="top">specifies the implementation by class name.</td>
  </tr>
  <tr>
    <td valign="top">classpath</td>
    <td valign="top">the classpath to use when looking up
      <code>classname</code>.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">classpathref</td>
    <td valign="top">the classpath to use, given as <a
      href="../using.html#references">reference</a> to a path defined elsewhere.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">from</td>
    <td valign="top">the <code>from</code> attribute for the given
      implementation.</td>
    <td align="center" valign="top">Depends on implementation.</td>
  </tr>
  <tr>
    <td valign="top">to</td>
    <td valign="top">the <code>to</code> attribute for the given
      implementation.</td>
    <td align="center" valign="top">Depends on implementation.</td>
  </tr>
</table>
<p>Note that Ant will not automatically convert / or \ characters in
the <code>to</code> and <code>from</code> attributes to the correct
directory separator of your current platform.  If you need to specify
this separator, use <code>/</code> instead.
  For the regexpmapper, <code>/</code> will not work,
as on windows it is the '\' character, and this is an escape character
for regular expressions, one should use the <code>handledirsep</code> attribute
instead.
</p>
<h3>Parameters specified as nested elements</h3>
<p>The classpath can be specified via a nested
<code>&lt;classpath&gt;</code>, as well - that is,
a <a href="../using.html#path">path</a>-like structure.</p>
<p><b>Since Ant 1.6.2,</b> nested File Mappers can
be supplied via either <CODE>&lt;mapper&gt;</CODE> elements or
<a href="../CoreTasks/typedef.html"><code>&lt;typedef&gt;</code></a>'d
implementations of <CODE>org.apache.tools.ant.util.FileNameMapper</CODE>.
If nested File Mappers are specified by either means, the mapper will be
implicitly configured as a <a href="#composite-mapper">composite mapper</a>.
</p>
<hr>
<h3>The built-in mapper types are:</h3>
<p>All built-in mappers are case-sensitive.</p>
<p><b>As of Ant 1.6.2,</b> each of the built-in mapper implementation
  types is directly accessible using a specific tagname. This makes it
  possible for filename mappers to support attributes in addition to
  the generally available <i>to</i> and <i>from</i>.<br>
  The <code>&lt;mapper type|classname=&quot;...&quot;&gt;</code> usage
  form remains valid for reasons of backward compatibility.</p>
<h4><a name="identity-mapper">identity</a></h4>
<p>The target file name is identical to the source file name. Both
<code>to</code> and <code>from</code> will be ignored.</p>
<b>Examples:</b>
<blockquote><pre>
&lt;mapper type=&quot;identity&quot;/&gt;
&lt;identitymapper/&gt;
</pre></blockquote>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Source file name</b></td>
    <td valign="top"><b>Target file name</b></td>
  </tr>
  <tr>
    <td valign="top"><code>A.java</code></td>
    <td valign="top"><code>A.java</code></td>
  </tr>
  <tr>
    <td valign="top"><code>foo/bar/B.java</code></td>
    <td valign="top"><code>foo/bar/B.java</code></td>
  </tr>
  <tr>
    <td valign="top"><code>C.properties</code></td>
    <td valign="top"><code>C.properties</code></td>
  </tr>
  <tr>
    <td valign="top"><code>Classes/dir/dir2/A.properties</code></td>
    <td valign="top"><code>Classes/dir/dir2/A.properties</code></td>
  </tr>
</table>
<h4><a name="flatten-mapper">flatten</a></h4>
<p>The target file name is identical to the source file name, with all
leading directory information stripped off. Both <code>to</code> and
<code>from</code> will be ignored.</p>
<b>Examples:</b>
<blockquote><pre>
&lt;mapper type=&quot;flatten&quot;/&gt;
&lt;flattenmapper/&gt;
</pre></blockquote>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Source file name</b></td>
    <td valign="top"><b>Target file name</b></td>
  </tr>
  <tr>
    <td valign="top"><code>A.java</code></td>
    <td valign="top"><code>A.java</code></td>
  </tr>
  <tr>
    <td valign="top"><code>foo/bar/B.java</code></td>
    <td valign="top"><code>B.java</code></td>
  </tr>
  <tr>
    <td valign="top"><code>C.properties</code></td>
    <td valign="top"><code>C.properties</code></td>
  </tr>
  <tr>
    <td valign="top"><code>Classes/dir/dir2/A.properties</code></td>
    <td valign="top"><code>A.properties</code></td>
  </tr>
</table>
<h4><a name="merge-mapper">merge</a></h4>
<p>The target file name will always be the same, as defined by
<code>to</code> - <code>from</code> will be ignored.</p>
<h5>Examples:</h5>
<blockquote><pre>
&lt;mapper type=&quot;merge&quot; to=&quot;archive.tar&quot;/&gt;
&lt;mergemapper to=&quot;archive.tar&quot;/&gt;
</pre></blockquote>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Source file name</b></td>
    <td valign="top"><b>Target file name</b></td>
  </tr>
  <tr>
    <td valign="top"><code>A.java</code></td>
    <td valign="top"><code>archive.tar</code></td>
  </tr>
  <tr>
    <td valign="top"><code>foo/bar/B.java</code></td>
    <td valign="top"><code>archive.tar</code></td>
  </tr>
  <tr>
    <td valign="top"><code>C.properties</code></td>
    <td valign="top"><code>archive.tar</code></td>
  </tr>
  <tr>
    <td valign="top"><code>Classes/dir/dir2/A.properties</code></td>
    <td valign="top"><code>archive.tar</code></td>
  </tr>
</table>
<h4><a name="glob-mapper">glob</a></h4>
<p>Both <code>to</code> and <code>from</code> define patterns that may
contain at most one <code>*</code>. For each source file that matches
the <code>from</code> pattern, a target file name will be constructed
from the <code>to</code> pattern by substituting the <code>*</code> in
the <code>to</code> pattern with the text that matches the
<code>*</code> in the <code>from</code> pattern. Source file names
that don't match the <code>from</code> pattern will be ignored.</p>
<b>Examples:</b>
<blockquote><pre>
&lt;mapper type=&quot;glob&quot; from=&quot;*.java&quot; to=&quot;*.java.bak&quot;/&gt;
&lt;globmapper from=&quot;*.java&quot; to=&quot;*.java.bak&quot;/&gt;
</pre></blockquote>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Source file name</b></td>
    <td valign="top"><b>Target file name</b></td>
  </tr>
  <tr>
    <td valign="top"><code>A.java</code></td>
    <td valign="top"><code>A.java.bak</code></td>
  </tr>
  <tr>
    <td valign="top"><code>foo/bar/B.java</code></td>
    <td valign="top"><code>foo/bar/B.java.bak</code></td>
  </tr>
  <tr>
    <td valign="top"><code>C.properties</code></td>
    <td valign="top">ignored</td>
  </tr>
  <tr>
    <td valign="top"><code>Classes/dir/dir2/A.properties</code></td>
    <td valign="top">ignored</td>
  </tr>
</table>
<blockquote><pre>
&lt;mapper type=&quot;glob&quot; from=&quot;C*ies&quot; to=&quot;Q*y&quot;/&gt;
&lt;globmapper from=&quot;C*ies&quot; to=&quot;Q*y&quot;/&gt;
</pre></blockquote>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Source file name</b></td>
    <td valign="top"><b>Target file name</b></td>
  </tr>
  <tr>
    <td valign="top"><code>A.java</code></td>
    <td valign="top">ignored</td>
  </tr>
  <tr>
    <td valign="top"><code>foo/bar/B.java</code></td>
    <td valign="top">ignored</td>
  </tr>
  <tr>
    <td valign="top"><code>C.properties</code></td>
    <td valign="top"><code>Q.property</code></td>
  </tr>
  <tr>
    <td valign="top"><code>Classes/dir/dir2/A.properties</code></td>
    <td valign="top"><code>Qlasses/dir/dir2/A.property</code></td>
  </tr>
</table>
  <p>
    The globmapper mapper can take the following extra attributes.
  </p>
  <table border="1" cellpadding="2" cellspacing="0">
    <tr>
      <td valign="top"><b>Attribute</b></td>
      <td valign="top"><b>Description</b></td>
      <td align="center" valign="top"><b>Required</b></td>
    </tr>
    <tr>
      <td valign="top">casesensitive</td>
      <td valign="top">
        If this is false, the mapper will ignore case when matching the glob pattern.
        This attribute can be true or false, the default is true.
      <em>Since Ant 1.6.3.</em>
      </td>
      <td align="center" valign="top">No</td>
    </tr>
    <tr>
      <td valign="top">handledirsep</td>
      <td valign="top">
        If this is specified, the mapper will ignore the difference between the normal
        directory separator characters - \ and /.
        This attribute can be true or false, the default is false.
        This attribute is useful for cross-platform build files.
        <em>Since Ant 1.6.3.</em>
        <td align="center" valign="top">No</td>
      </tr>
    </table>
    <p>
      An example:
    </p>
    <pre>
      &lt;pathconvert property="x" targetos="unix"&gt;
        &lt;path path="Aj.Java"/&gt;
        &lt;mapper&gt;
        &lt;chainedmapper&gt;
          &lt;flattenmapper/&gt;
          &lt;globmapper from="a*.java" to="*.java.bak" casesensitive="no"/&gt;
        &lt;/chainedmapper&gt;
        &lt;/mapper&gt;
      &lt;/pathconvert&gt;
      &lt;echo&gt;x is ${x}&lt;/echo&gt;
    </pre>
    <p>
      will output "x is j.java.bak".
    </p>
    <p>
      and
    </p>
    <pre>
      &lt;pathconvert property="x" targetos="unix"&gt;
        &lt;path path="d/e/f/j.java"/&gt;
        &lt;mapper&gt;
          &lt;globmapper from="/Users/<USER>/dev/asf/ant-core\d/e\*" to="*" ignoredirchar="yes"/&gt;
        &lt;/mapper&gt;
      &lt;/pathconvert&gt;
      &lt;echo&gt;x is ${x}&lt;/echo&gt;
    </pre>
    <p>
      will output "x is f/j.java".
    </p>

<h4><a name="regexp-mapper">regexp</a></h4>
<p>Both <code>to</code> and <code>from</code> define regular
expressions. If the source file name matches the <code>from</code>
pattern, the target file name will be constructed from the
<code>to</code> pattern, using <code>\0</code> to <code>\9</code> as
back-references for the full
match (<code>\0</code>) or the matches of the subexpressions in
parentheses.
Source
files not matching the <code>from</code> pattern will be ignored.</p>
<p>Note that you need to escape a dollar-sign (<code>$</code>) with
another dollar-sign in Ant.</p>
<p>The regexp mapper needs a supporting library and an implementation
of <code>org.apache.tools.ant.util.regexp.RegexpMatcher</code> that
hides the specifics of the library. Ant comes with implementations for
<a href="http://java.sun.com/j2se/1.4/docs/api/java/util/regex/package-summary.html"  target="_top">the java.util.regex package of JDK 1.4 or higher</a>,
<a href="http://jakarta.apache.org/regexp/" target="_top">jakarta-regexp</a> and <a
href="http://jakarta.apache.org/oro/" target="_top">jakarta-ORO</a>. If you compile
from sources and plan to use one of them, make sure the libraries are
in your <code>CLASSPATH</code>. For information about using <a
href="http://www.cacas.org/~wes/java/" target="_top">gnu.regexp</a> or <a
href="http://www.crocodile.org/~sts/Rex/" target="_top">gnu.rex</a> with Ant, see <a
href="http://marc.theaimsgroup.com/?l=ant-dev&m=97550753813481&w=2" target="_top">this</a>
article.</p>
<p>This means, you need one of the supported regular expression
  libraries <strong>and</strong>
  the corresponding <code>ant-[jakarta-oro, jakarta-regexp, apache-oro, apache-regexp}.jar</code>
from the Ant release you are using.
Make sure, both will be loaded from the same
classpath, that is either put them into your <code>CLASSPATH</code>,
<code>ANT_HOME/lib</code> directory or a nested
<code>&lt;classpath&gt;</code> element of the mapper - you cannot have
<code>ant-[jakarta-oro, jakarta-regexp, apache-oro, apache-regexp].jar</code> in <code>ANT_HOME/lib</code>
 and the library
in a nested <code>&lt;classpath&gt;</code>.</p>
<p>Ant will choose the regular-expression library based on the
following algorithm:</p>
<ul>
<li>If the system property
<code>ant.regexp.matcherimpl</code> has been set, it is taken as the
name of the class implementing
<code>org.apache.tools.ant.util.regexp.RegexpMatcher</code> that
should be used.</li>
<li>If it has not been set, first try the JDK 1.4 classes, then
jakarta-ORO and finally try jakarta-regexp.</li>
</ul>

<b>Examples:</b>
<blockquote><pre>
&lt;mapper type=&quot;regexp&quot; from=&quot;^(.*)\.java$&quot; to=&quot;\1.java.bak&quot;/&gt;
&lt;regexpmapper from=&quot;^(.*)\.java$&quot; to=&quot;\1.java.bak&quot;/&gt;
</pre></blockquote>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Source file name</b></td>
    <td valign="top"><b>Target file name</b></td>
  </tr>
  <tr>
    <td valign="top"><code>A.java</code></td>
    <td valign="top"><code>A.java.bak</code></td>
  </tr>
  <tr>
    <td valign="top"><code>foo/bar/B.java</code></td>
    <td valign="top"><code>foo/bar/B.java.bak</code></td>
  </tr>
  <tr>
    <td valign="top"><code>C.properties</code></td>
    <td valign="top">ignored</td>
  </tr>
  <tr>
    <td valign="top"><code>Classes/dir/dir2/A.properties</code></td>
    <td valign="top">ignored</td>
  </tr>
</table>
<blockquote><pre>
&lt;mapper type=&quot;regexp&quot; from=&quot;^(.*)/([^/]+)/([^/]*)$&quot; to=&quot;\1/\2/\2-\3&quot;/&gt;
&lt;regexpmapper from=&quot;^(.*)/([^/]+)/([^/]*)$&quot; to=&quot;\1/\2/\2-\3&quot;/&gt;
</pre></blockquote>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Source file name</b></td>
    <td valign="top"><b>Target file name</b></td>
  </tr>
  <tr>
    <td valign="top"><code>A.java</code></td>
    <td valign="top">ignored</td>
  </tr>
  <tr>
    <td valign="top"><code>foo/bar/B.java</code></td>
    <td valign="top"><code>foo/bar/bar-B.java</code></td>
  </tr>
  <tr>
    <td valign="top"><code>C.properties</code></td>
    <td valign="top">ignored</td>
  </tr>
  <tr>
    <td valign="top"><code>Classes/dir/dir2/A.properties</code></td>
    <td valign="top"><code>Classes/dir/dir2/dir2-A.properties</code></td>
  </tr>
</table>
<blockquote><pre>
&lt;mapper type="regexp" from="^(.*)\.(.*)$" to="\2.\1"/&gt;
&lt;regexpmapper from="^(.*)\.(.*)$&" to="\2.\1"/&gt;
</pre></blockquote>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Source file name</b></td>
    <td valign="top"><b>Target file name</b></td>
  </tr>
  <tr>
    <td valign="top"><code>A.java</code></td>
    <td valign="top"><code>java.A</code></td>
  </tr>
  <tr>
    <td valign="top"><code>foo/bar/B.java</code></td>
    <td valign="top"><code>java.foo/bar/B</code></td>
  </tr>
  <tr>
    <td valign="top"><code>C.properties</code></td>
    <td valign="top"><code>properties.C</code></td>
  </tr>
  <tr>
    <td valign="top"><code>Classes/dir/dir2/A.properties</code></td>
    <td valign="top"><code>properties.Classes/dir/dir2/A</code></td>
  </tr>
</table>
<blockquote><pre>
&lt;mapper type="regexp" from="^(.*?)(\$[^/\\\.]*)?\.class$" to="\1.java"/&gt;
&lt;regexpmapper from="^(.*?)(\$[^/\\\.]*)?\.class$" to="\1.java"/&gt;
</pre></blockquote>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Source file name</b></td>
    <td valign="top"><b>Target file name</b></td>
  </tr>
  <tr>
    <td valign="top"><code>ClassLoader.class</code></td>
    <td valign="top"><code>ClassLoader.java</code></td>
  </tr>
  <tr>
    <td valign="top"><code>java/lang/ClassLoader.class</code></td>
    <td valign="top"><code>java/lang/ClassLoader.java</code></td>
  </tr>
  <tr>
    <td valign="top"><code>java\lang\ClassLoader$1.class</code></td>
    <td valign="top"><code>java\lang\ClassLoader.java</code></td>
  </tr>
  <tr>
    <td valign="top"><code>java/lang/ClassLoader$foo$1.class</code></td>
    <td valign="top"><code>java/lang/ClassLoader.java</code></td>
  </tr>
</table>
  <p>
    The regexpmapper mapper can take the following extra attributes.
  </p>
  <table border="1" cellpadding="2" cellspacing="0">
    <tr>
      <td valign="top"><b>Attribute</b></td>
      <td valign="top"><b>Description</b></td>
      <td align="center" valign="top"><b>Required</b></td>
    </tr>
    <tr>
      <td valign="top">casesensitive</td>
      <td valign="top">
        If this is false, the mapper will ignore case when matching the pattern.
        This attribute can be true or false, the default is true.
      <em>Since Ant 1.6.3.</em>
      </td>
      <td align="center" valign="top">No</td>
    </tr>
    <tr>
      <td valign="top">handledirsep</td>
      <td valign="top">
        If this is specified, the mapper will treat a \ character in a filename
        as a / for the purposes of matching.
        This attribute can be true or false, the default is false.
        This attribute is useful for cross-platform build files.
        <em>Since Ant 1.6.3.</em>
        <td align="center" valign="top">No</td>
      </tr>
    </table>
    <p>
      An example:
    </p>
    <pre>
      &lt;pathconvert property="x" targetos="unix"&gt;
        &lt;path path="Aj.Java"/&gt;
        &lt;chainedmapper&gt;
          &lt;flattenmapper/&gt;
          &lt;regexpmapper from="a(.*)\.java" to="\1.java.bak" casesensitive="no"/&gt;
        &lt;/chainedmapper&gt;
      &lt;/pathconvert&gt;
      &lt;echo&gt;x is ${x}&lt;/echo&gt;
    </pre>
    <p>
      will output "x is j.java.bak".
    </p>
    <p>
      and
    </p>
    <pre>
    &lt;pathconvert property="hd.prop" targetos="windows"&gt;
      &lt;path path="d\e/f\j.java"/&gt;
      &lt;chainedmapper&gt;
        &lt;regexpmapper from="/Users/<USER>/dev/asf/ant-core/d/e/(.*)" to="\1" handledirsep="yes"/&gt;
      &lt;/chainedmapper&gt;
    &lt;/pathconvert&gt;
    </pre>
    <p>
      will set <code>hd.prop</code> to "f\j.java".
    </p>
<h4><a name="package-mapper">package</a></h4>
<p>Sharing the same syntax as the <a href="#glob-mapper">glob mapper</a>,
the package mapper replaces
directory separators found in the matched source pattern with dots in the target
pattern placeholder. This mapper is particularly useful in combination
with <code>&lt;uptodate&gt;</code> and <code>&lt;junit&gt;</code> output.</p>
<b>Example:</b>
<blockquote><pre>
&lt;mapper type="package" from="*Test.java" to="TEST-*Test.xml"/&gt;
&lt;packagemapper from="*Test.java" to="TEST-*Test.xml"/&gt;
</pre></blockquote>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Source file name</b></td>
    <td valign="top"><b>Target file name</b></td>
  </tr>
  <tr>
    <td valign="top"><code>org/apache/tools/ant/util/PackageMapperTest.java</code></td>
    <td valign="top"><code>TEST-org.apache.tools.ant.util.PackageMapperTest.xml</code></td>
  </tr>
  <tr>
    <td valign="top"><code>org/apache/tools/ant/util/Helper.java</code></td>
    <td valign="top">ignored</td>
  </tr>
</table>
<h4><a name="unpackage-mapper">unpackage (since Ant 1.6.0)</a></h4>
  <p>This mapper is the inverse of the <a href="#package-mapper">package</a> mapper.
    It replaces the dots in a package name with directory separators. This
    is useful for matching XML formatter results against their JUnit test
    test cases. The mapper shares the sample syntax
    as the <a href="#glob-mapper">glob mapper</a>.
  </p>
<b>Example:</b>
<blockquote><pre>
&lt;mapper type="unpackage" from="TEST-*Test.xml" to="${test.src.dir}/*Test.java"&gt;
&lt;unpackagemapper from="TEST-*Test.xml" to="${test.src.dir}/*Test.java"&gt;
</pre></blockquote>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Source file name</b></td>
    <td valign="top"><b>Target file name</b></td>
  </tr>
  <tr>
    <td valign="top"><code>TEST-org.acme.AcmeTest.xml</code></td>
    <td valign="top"><code>${test.src.dir}/org/acme/AcmeTest.java</code></td>
  </tr>
</table>
<h4><a name="composite-mapper">composite (since Ant 1.6.2)</a></h4>
  <p>This mapper implementation can contain multiple nested mappers.
    File mapping is performed by passing the source filename to each nested
    <code>&lt;mapper&gt;</code> in turn, returning all results.
    The <i>to</i> and <i>from</i> attributes are ignored.</p>
<b>Examples:</b>
<blockquote><pre>
&lt;compositemapper&gt;
  &lt;identitymapper/&gt;
  &lt;packagemapper from="*.java" to="*"/&gt;
&lt;/compositemapper&gt;
</pre></blockquote>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Source file name</b></td>
    <td valign="top"><b>Target file names</b></td>
  </tr>
  <tr>
    <td valign="center" rowspan="2"><code>foo/bar/A.java</code></td>
    <td valign="top"><code>foo/bar/A.java</code></td>
  </tr>
  <tr>
    <td valign="top"><code>foo.bar.A</code></td>
  </tr>
</table>
  <p>The composite mapper has no corresponding
    <code>&lt;mapper <b>type</b>&gt;</code> attribute.
  </p>
<h4><a name="chained-mapper">chained (since Ant 1.6.2)</a></h4>
  <p>This mapper implementation can contain multiple nested mappers.
    File mapping is performed by passing the source filename to the first
    nested mapper, its results to the second, and so on.  The target filenames
    generated by the last nested mapper comprise the ultimate results of the
    mapping operation.  The <i>to</i> and <i>from</i> attributes are ignored.</p>
<b>Examples:</b>
<blockquote><pre>
&lt;chainedmapper&gt;
  &lt;flattenmapper/&gt;
  &lt;globmapper from="*" to="new/path/*"/&gt;
  &lt;mapper&gt;
    &lt;globmapper from="*" to="*1"/&gt;
    &lt;globmapper from="*" to="*2"/&gt;
  &lt;/mapper&gt;
&lt;/chainedmapper&gt;
</pre></blockquote>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Source file name</b></td>
    <td valign="top"><b>Target file names</b></td>
  </tr>
  <tr>
    <td valign="center" rowspan="2"><code>foo/bar/A.java</code></td>
    <td valign="top"><code>new/path/A.java1</code></td>
  </tr>
  <tr>
    <td valign="top"><code>new/path/A.java2</code></td>
  </tr>
  <tr>
    <td valign="center" rowspan="2"><code>boo/far/B.java</code></td>
    <td valign="top"><code>new/path/B.java1</code></td>
  </tr>
  <tr>
    <td valign="top"><code>new/path/B.java2</code></td>
  </tr>
</table>
  <p>The chained mapper has no corresponding
    <code>&lt;mapper <b>type</b>&gt;</code> attribute.
  </p>

    <!--                                        -->
    <!--             Filter Mapper              -->
    <!--                                        -->

<h4><a name="filter-mapper">filtermapper (since Ant 1.6.3)</a></h4>
    <p>
      This mapper implementation applies a <a href="filterchain.html">filterchain</a>
      to the source file name.
    </p>
<b>Examples:</b>
<blockquote><pre>
&lt;filtermapper&gt;
  &lt;replacestring from="\" to="/"/&gt;
&lt;/filtermapper&gt;
</pre></blockquote>

<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Source file name</b></td>
    <td valign="top"><b>Target file names</b></td>
  </tr>
  <tr>
    <td valign="center"><code>foo\bar\A.java</code></td>
    <td valign="top"><code>foo/bar/A.java</code></td>
  </tr>
</table>
<blockquote><pre>
&lt;filtermapper&gt;
  &lt;scriptfilter language="beanshell"&gt;
    self.setToken(self.getToken().toUpperCase());
  &lt;/scriptfilter&gt;
&lt;/filtermapper&gt;
</pre></blockquote>

<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Source file name</b></td>
    <td valign="top"><b>Target file names</b></td>
  </tr>
  <tr>
    <td valign="center"><code>foo\bar\A.java</code></td>
    <td valign="top"><code>FOO\BAR\A.JAVA</code></td>
  </tr>
</table>

  <p>The filtermapper has no corresponding
    <code>&lt;mapper <b>type</b>&gt;</code> attribute.
  </p>

    <!--                                        -->
    <!--             Script Mapper              -->
    <!--                                        -->

<h4><a name="script-mapper">scriptmapper (since Ant 1.7)</a></h4>
<p>
This mapper executes a script written in <a href="http://jakarta.apache.org/bsf" target="_top">Apache BSF</a>
or
  <a href="https://scripting.dev.java.net">JSR 223</a>
supported language, once per file to map.</p>
The script can be declared inline or in a specified file.
</p>
<p>
See the <a href="../OptionalTasks/script.html">Script</a> task for
an explanation of scripts and dependencies.
</p>

  <table border="1" cellpadding="2" cellspacing="0">
    <tr>
      <td valign="top"><b>Attribute</b></td>
      <td valign="top"><b>Description</b></td>
      <td align="center" valign="top"><b>Required</b></td>
    </tr>
    <tr>
      <td valign="top">language</td>
      <td valign="top">
        Scripting language
      </td>
      <td align="center" valign="top">Yes</td>
    </tr>
    <tr>
      <td valign="top">manager</td>
      <td valign="top">
        The script engine manager to use.
        See the <a href="../OptionalTasks/script.html">script</a> task
        for using this attribute.
      </td>
      <td valign="top" align="center">No - default is "auto"</td>
    </tr>
    <tr>
      <td valign="top">src</td>
      <td valign="top">
        File containing the script
      </td>
      <td align="center" valign="top">No</td>
    </tr>
    <tr>
      <td valign="top">setbeans</td>
      <td valign="top">whether to have all properties, references and targets as
        global variables in the script.</td>
      <td valign="top" align="center">No, default is "true".</td>
    </tr>
    <tr>
      <td valign="top">classpath</td>
      <td valign="top">
        The classpath to pass into the script.
      </td>
      <td align="center" valign="top">No</td>
    </tr>
    <tr>
      <td valign="top">classpathref</td>
      <td valign="top">The classpath to use, given as a
        <a href="../using.html#references">reference</a> to a path defined elsewhere.
        <td align="center" valign="top">No</td>
      </tr>
    </table>
  <p>
    This filename mapper can take a nested &lt;classpath&gt; element.
    See the <a href="../OptionalTasks/script.html">script</a> task
    on how to use this element.
  </p>

<p>
  <b>Example:</b>
</p>
<blockquote><pre>
&lt;scriptmapper language="javascript"&gt;
  self.addMappedName(source.toUpperCase());
  self.addMappedName(source.toLowerCase());
&lt;/scriptmapper&gt;
</pre></blockquote>

<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Source file name</b></td>
    <td valign="top"><b>Target file names</b></td>
  </tr>
  <tr>
    <td valign="center"><code>foo\bar\A.java</code></td>
    <td valign="top"><code>FOO\BAR\A.JAVA foo\bar\a.java</code></td>
  </tr>
</table>

<p>
To use this mapper, the scripts need access to the source file,
and the ability to return multiple mappings. Here are the relevant beans and
their methods. The script is called once for every source file, with the
list of mapped names reset after every invocation.

  <table border="1" cellpadding="2" cellspacing="0">
    <tr>
      <td valign="top"><b>Script bean</b></td>
      <td valign="top"><b>Description</b></td>
    </tr>
    <tr>
      <td valign="top"><code>source: String</code></td>
      <td valign="top">
        The file/path to map
      </td>
    </tr>
    <tr>
      <td valign="top">self</td>
      <td valign="top">
        the scriptmapper itself
      </td>
    </tr>
    <tr>
      <td valign="top"><code>self.addMappedName(String name)</code></td>
      <td valign="top">
        Add a new mapping
      </td>
    </tr>
    <tr>
      <td valign="top"><code>self.clear()</code></td>
      <td valign="top">
        Reset the list of files.
      </td>
    </tr>
    </table>

  <p>The scriptmapper has no corresponding
    <code>&lt;mapper <b>type</b>&gt;</code> attribute.
  </p>



</body>
</html>
