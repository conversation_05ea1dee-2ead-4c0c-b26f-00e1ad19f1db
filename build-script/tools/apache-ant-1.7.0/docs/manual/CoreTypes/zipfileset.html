<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
  <meta http-equiv="Content-Language" content="en-us">
  <link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>ZipFileSet Type</title>
</head>
<body>
<h2><a name="fileset">ZipFileSet</a></h2>

<p>A <code>&lt;zipfileset&gt;</code> is a special form of a <code>&lt;<a
 href="fileset.html">fileset</a>&gt;</code> which can behave in 2
different ways : <br>
</p>
<ul>
  <li>When the <span style="font-style: italic;">src</span> attribute
  is used - or a nested resource collection has been specified
  (<em>since Ant 1.7</em>), the zipfileset is populated with 
  zip entries found in the file <span style="font-style:
  italic;">src</span>.<br>
  </li>
  <li>When the <span style="font-style: italic;">dir</span> attribute
is used, the zipfileset is populated with filesystem files found under <span
 style="font-style: italic;">dir</span>.<br>
  </li>
</ul>
<p><code>&lt;zipfileset&gt;</code> supports all attributes of <code>&lt;<a
 href="fileset.html">fileset</a>&gt;</code>
in addition to those listed below.<br>
</p>
<p>Since Ant 1.6, a zipfileset can be defined with the <span
 style="font-style: italic;">id </span>attribute and referred to with
the <span style="font-style: italic;">refid</span> attribute.<br>
</p>
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tbody>
    <tr>
      <td valign="top"><b>Attribute</b></td>
      <td valign="top"><b>Description</b></td>
      <td valign="top" align="center"><b>Required</b></td>
    </tr>
    <tr>
      <td valign="top">prefix</td>
      <td valign="top">all files in the fileset are prefixed with that 
path in the archive.</td>
      <td align="center" valign="top">No</td>
    </tr>
    <tr>
      <td valign="top">fullpath</td>
      <td valign="top">the file described by the fileset is placed at  
that exact location in the archive.</td>
      <td align="center" valign="top">No</td>
    </tr>
    <tr>
      <td valign="top">src</td>
      <td valign="top">may be used in place of the <i>dir</i> attribute
 to specify a zip file whose contents will be extracted and     included
in the archive.</td>
      <td align="center" valign="top">No</td>
    </tr>
    <tr>
      <td valign="top">filemode</td>
      <td valign="top">A 3 digit octal string, specify the Employee, group
and other modes in the standard Unix fashion.  Only applies to    
plain files.  Default is 644. <em>since Ant 1.5.2</em>.</td>
      <td align="center" valign="top">No</td>
    </tr>
    <tr>
      <td valign="top">dirmode</td>
      <td valign="top">A 3 digit octal string, specify the Employee, group
and other modes in the standard Unix fashion.  Only applies to    
directories.  Default is 755. <em>since Ant 1.5.2</em>.</td>
      <td align="center" valign="top">No</td>
    </tr>
    <tr>
      <td valign="top">encoding</td>
      <td valign="top">The character encoding to use for filenames
      inside the zip file.  For a list of possible values see <a
      href="http://java.sun.com/j2se/1.5.0/docs/guide/intl/encoding.doc.html">http://java.sun.com/j2se/1.5.0/docs/guide/intl/encoding.doc.html</a>.
      Defaults to the platform's default character encoding.
      <b>Only supported by zipfileset.</b></td>
      <td align="center" valign="top">No</td>
    </tr>
  </tbody>
</table>
<p>The <i>fullpath</i> attribute can only be set for filesets that
represent a single file. The <i>prefix</i> and <i>fullpath</i>
attributes cannot both be set on the same fileset.</p>
<p>When using the <i>src</i> attribute, include and exclude patterns
may be used to specify a subset of the archive for inclusion in the
archive as with the <i>dir</i> attribute.</p>

<p>Please note that currently only the <a
href="../CoreTasks/tar.html">tar</a> and <a
href="../CoreTaks/zip.html">zip</a> tasks use the permission.</p>

<h3>Parameters specified as nested elements</h3>

<h4>any file system based <a href="resources.html">resource</a> or
single element resource collection</h4>

<p>The specified resource will be used as src.</p>

<h4>Examples</h4>
<blockquote>
  <pre>  &lt;zip destfile="${dist}/manual.zip"&gt;<br>    &lt;zipfileset dir="htdocs/manual" prefix="docs/Employee-guide"/&gt;<br>    &lt;zipfileset dir="." includes="ChangeLog27.txt" fullpath="docs/ChangeLog.txt"/&gt;<br>    &lt;zipfileset src="examples.zip" includes="**/*.html" prefix="docs/examples"/&gt;<br>  &lt;/zip&gt;<br></pre>
  <p>zips all files in the <code>htdocs/manual</code> directory into
the <code>docs/Employee-guide</code> directory in the archive, adds the
file <code>ChangeLog27.txt</code> in the current directory as <code>docs/ChangeLog.txt</code>,
and includes all the html files in <code>examples.zip</code> under <code>docs/examples</code>.
The archive might end up containing the files:</p>
  <code> docs/Employee-guide/html/index.html<br>
docs/ChangeLog.txt<br>
docs/examples/index.html<br>
  </code></blockquote>


</body>
</html>
