<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Assertions type</title>
</head>

<body>

<h2><a name="assertions">Assertions</a></h2>
<p>
The <tt>assertions</tt> type enables or disables the Java 1.4 assertions feature,
on a whole Java program, or components of a program. It can be used
in <a href="../CoreTasks/java.html"><code>&lt;java&gt;</code></a> and
<a href="../OptionalTasks/junit.html"><code>&lt;junit&gt;</code></a> to add extra validation to code.  

<p>
Assertions are covered in the 
<a href="http://java.sun.com/j2se/1.4.2/docs/guide/lang/assert.html">J2SDK 1.4 documentation</a>,
and the
<a href="http://java.sun.com/docs/books/jls/assert-spec.html">Java Language Specification</a>.

<p>
The key points to note are that a <tt>java.lang.AssertionError</tt>
is thrown when an assertion fails, and that the facility is only available 
on Java 1.4 and later. To enable assertions one must set <tt>source="1.4"</tt>
(or later) in <tt>&lt;javac&gt;</tt> when the source is being compiled, and
that the code must contain <tt>assert</tt> statements to be tested. The
result of such an action is code that neither compiles or runs on earlier
versions of Java. For this reason Ant itself currently contains no assertions.
<p>

When assertions are enabled (or disabled) in a task through nested 
assertions elements, the class loader or command line is modified with the 
appropriate options. This means that the JVM executed must be a Java 1.4
or later JVM, even if there are no assertions in the code. Attempting to
enable assertions on earlier VMs will result in an "Unrecognized option" 
error and the JVM will not start.  

<p>
<h4>Attributes</h4>
<p>


</p>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">enableSystemAssertions</td>
    <td valign="top">Flag to turn system assertions on or off.</td>
    <td valign="top" align="center">No; default is "unspecified"</td>
  </tr>
</table>
<p>
When system assertions have been neither enabled nor disabled, then
the JVM is not given any assertion information - the default action of the
 current JVMs is to disable system assertions. 
<p>
Note also that there is no apparent documentation for what parts of the
JRE come with useful assertions.

<h3>Nested elements</h3>

<h4>enable</h4>
<p>
Enable assertions in portions of code.
If neither a package nor class is specified, assertions are turned on in <i>all</i> (Employee) code.
</p>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">class</td>
    <td valign="top">The name of a class on which to enable assertions.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">package</td>
    <td valign="top">
    The name of a package in which to enable assertions on all classes. (Includes subpackages.)
    Use "<tt>...</tt>" for the anonymous package.
    </td>
    <td valign="top" align="center">No</td>
  </tr>
</table>

<h4>disable</h4>
<p>
Disable assertions in portions of code.

</p>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">class</td>
    <td valign="top">The name of a class on which to disable assertions.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">package</td>
    <td valign="top">
    The name of a package in which to disable assertions on all classes. (Includes subpackages.)
    Use "<tt>...</tt>" for the anonymous package.
    </td>
    <td valign="top" align="center">No</td>
  </tr>
</table>
<p>

Because assertions are disabled by default, it only makes sense to disable
assertions where they have been enabled in a parent package.


<h4>Examples</h4>

<h5>Example: enable assertions in all Employee classes</h5>

All classes not in the JRE (i.e. all non-system classes) will have assertions turned on.
<pre>
&lt;assertions&gt;
  &lt;enable/&gt;
&lt;/assertions&gt;
</pre>

<h5>Example: enable a single class</h5>

Enable assertions in a class called Test
<pre>
&lt;assertions&gt;
  &lt;enable class="Test"/&gt;
&lt;/assertions&gt;
</pre>

<h5>Example: enable a package</h5>

Enable assertions in the <tt>org.apache</tt> package
and all packages starting with the <tt>org.apache.</tt> prefix
<pre>
&lt;assertions&gt;
  &lt;enable package="org.apache"/&gt;
&lt;/assertions&gt;
</pre>

<h5>Example: System assertions</h5>

Example: enable system assertions and assertions in all <tt>org.apache</tt> packages except
for Ant (but including <tt>org.apache.tools.ant.Main</tt>)
<pre>
&lt;assertions enableSystemAssertions="true"&gt;
  &lt;enable package="org.apache"/&gt;
  &lt;disable package="org.apache.tools.ant"/&gt;
  &lt;enable class="org.apache.tools.ant.Main"/&gt;
&lt;/assertions&gt;
</pre>

<h5>Example: disabled and anonymous package assertions</h5>

Disable system assertions; enable those in the anonymous package
<pre>
&lt;assertions enableSystemAssertions="false"&gt;
  &lt;enable package="..."/&gt;
&lt;/assertions&gt;
</pre>


<h5>Example: referenced assertions</h5>

This type is a datatype, so you can declare assertions and use them later

<pre>
&lt;assertions id="project.assertions"&gt;
  &lt;enable package="org.apache.test"/&gt;
&lt;/assertions&gt;

&lt;assertions refid="project.assertions"/&gt;
</pre>


</body>
</html>
