<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
    
<html>
<head>
  <meta http-equiv="Content-Language" content="en-us">
  <title>Whichresource
 Task</title>
</head>

<body bgcolor="#ffffff" text="#000000" link="#525D76"
      alink="#525D76" vlink="#525D76">

<table border="0" width="100%" cellspacing="4">

  <!-- PAGE HEADER -->
  <tr>
    <td>
      <table border="0" width="100%"><tr>
          <td valign="bottom">
            <font size="+3" face="arial,helvetica,sanserif"><strong>Whichresource
 Task</strong></font>
            <br><font face="arial,helvetica,sanserif">Find a class or resource on the supplied classpath, or the system classpath if none is supplied.</font>
          </td>
          <td>
            <!-- PROJECT LOGO -->
            <a href="http://ant.apache.org/">
              <img src="../../images/ant_logo_large.gif" align="right" alt="Apache Ant" border="0">
            </a>
          </td>
      </tr></table>
    </td>
  </tr>

  <!-- START RIGHT SIDE MAIN BODY -->
  <tr>
    <td  valign="top" align="left">

          <!-- Applying task/long-description -->
    <!-- Start Description -->
    <table border="0" cellspacing="0" cellpadding="2" width="100%">
      <tr><td>&nbsp;</td></tr>

      <tr><td bgcolor="#525D76">
        <font color="#ffffff" face="arial,helvetica.sanserif">
          <a name="description">
          <strong>Description</strong></a></font>
      </td></tr>

      <tr><td><blockquote>
        Find a class or resource on the supplied classpath, or the system classpath if none is supplied. The named property is set if the item can be found. For example <pre> &lt;whichresource resource="/log4j.properties" property="log4j.url" &gt; </pre>
      </blockquote></td></tr>

    </table>
    <!-- End Description -->

 <!-- Ignore -->



    <!-- Start Attributes -->
    <table border="0" cellspacing="0" cellpadding="2" width="100%">
      <tr><td>&nbsp;</td></tr>
      <tr><td bgcolor="#525D76">
        <font color="#ffffff" face="arial,helvetica.sanserif">
          <a name="attributes">
          <strong>Parameters</strong></a></font>
      </td></tr>
      <tr><td><blockquote>
        <table>
          <tr>
        <td bgcolor="#cccccc" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif"><b>Attribute</b></font>
        </td>
        <td bgcolor="#cccccc" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif"><b>Description</b></font>
        </td>
        <td bgcolor="#cccccc" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif"><b>Type</b></font>
        </td>
        <td bgcolor="#cccccc" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif"><b>Requirement</b></font>
        </td>
          </tr>
    <!-- Attribute Group -->    
        <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">property</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">the property to fill with the URL of the resource or class</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">String</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left" rowspan="1">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Required</font>
        </td>
    </tr>

    <!-- Attribute Group -->    
        <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">class</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">name the class to look for</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">String</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left" rowspan="2">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Exactly one of these two</font>
        </td>
    </tr>
    <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">resource</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">name the resource to look for</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">String</font>
        </td>
    </tr>

    <!-- Attribute Group -->    
        <!-- Attribute -->
    <tr>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">classpath</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Set the classpath to be used for this compilation.</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Path</font>
        </td>
        <td bgcolor="#eeeeee" valign="top" align="left" rowspan="1">
          <font color="#000000" size="-1" face="arial,helvetica,sanserif">Optional</font>
        </td>
    </tr>


        </table>
      </blockquote></td></tr>

    </table>
    <!-- End Attributes -->

    <!-- Start Elements -->
    <table border="0" cellspacing="0" cellpadding="2" width="100%">
      <tr><td>&nbsp;</td></tr>

      <tr><td bgcolor="#525D76">
        <font color="#ffffff" face="arial,helvetica.sanserif">
          <a name="elements">
          <strong>Parameters as nested elements</strong></a></font>
      </td></tr>

      <tr><td><blockquote>
    <!-- Start Element -->
    <table border="0" cellspacing="0" cellpadding="2" width="100%">
      <tr><td>&nbsp;</td></tr>
      <tr><td bgcolor="#828DA6">
        <font color="#ffffff" face="arial,helvetica.sanserif" size="-1">
          <strong>classpath</strong> (org.apache.tools.ant.types.Path)</font>
      </td></tr>
      <tr><td><blockquote>
        Adds a path to the classpath.
 <!-- Ignore -->
 <!-- Ignore -->

      </blockquote></td></tr>
    </table>
    <!-- End Element -->

      </blockquote></td></tr>

    </table>
    <!-- End Elements -->


    </td>
  </tr>
  <!-- END RIGHT SIDE MAIN BODY -->

</table>

</body>
</html>
