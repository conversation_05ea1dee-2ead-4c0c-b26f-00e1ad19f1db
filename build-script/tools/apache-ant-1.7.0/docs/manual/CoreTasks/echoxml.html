<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>EchoXML Task</title>
</head>

<body>

<h2>EchoXML</h2>
<h3>Description</h3>
<p>Echo nested XML to the console or a file. <b>Since Ant 1.7</b></p>
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">The file to receive the XML. If omitted nested
      XML will be echoed to the log.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">append</td>
    <td valign="top">Whether to append <code>file</code>, if specified.</td>
    <td valign="top" align="center">No</td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>
Nested XML content is required.

<h3>Examples</h3>
<pre>&lt;echoxml file=&quot;subbuild.xml&quot;&gt;
  &lt;project default=&quot;foo&quot;&gt;
    &lt;target name=&quot;foo&quot;&gt;
      &lt;echo&gt;foo&lt;/echo&gt;
    &lt;/target&gt;
  &lt;/project&gt;
&lt;/echoxml&gt;
</pre>
<p>Creates an Ant buildfile, <code>subbuild.xml</code>.</p>

</body>
</html>

