<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>
<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>XmlProperty Task</title>
</head>

<body>

<h2><a name="xmlproperty">XmlProperty</a></h2>
<h3>Description</h3>
<p>
Loads property values from a well-formed xml file. There are no other restrictions
than "well-formed". You can choose the layout you want. For example this XML property file:
<pre>
  &lt;root&gt;
    &lt;properties&gt;
      &lt;foo&gt;bar&lt;/foo&gt;
    &lt;/properties&gt;
  &lt;/root&gt;
</pre>
is roughly equivalent to this Java property file:
<pre>
  root.properties.foo = bar
</pre>

<p>
By default, this load
does <em>no</em> processing of the input.  In particular, unlike the
<a href="property.html">Property task</a>, property references
(i.e., <samp>${foo}</samp>) are not resolved.
<p>
<a name="semanticAttributes">
<h3>Semantic Attributes</h3>
</a>
Input processing can be enabled by using the <b>semanticAttributes</b>
attribute.  If this attribute is set to <i>true</i> (its default is
<i>false</i>), the following processing occurs as the input XML file
is loaded:
<ul>
  <li>Property references are resolved.</li>
  <li>The following attriubtes are treated differently:
    <ul>
      <li><b>id</b>: The property is associated with the given id value.</li>
      <li><b>location</b>: The property is treated as a file location</li>
      <li><b>refid</b>: The property is set to the value of the
          referenced property.</li>
      <li><b>value</b>: The property is set to the value indicated.</li>
    </ul>
  </li>
  <li><a href="../using.html#path">Path-like Structures</a> can be defined
      by use of the following attributes:
    <ul>
      <li><b>pathid</b>: The given id is used to identify a path.  The
          nested XML tag name is ignored.  Child elements can be used
          (XML tag names are ignored) to identify elements of the path.</li>
    </ul>
  </li>
</ul>
<p>
For example, with semantic attribute processing enabled, this XML property
file:
<pre>
  &lt;root&gt;
    &lt;properties&gt;
      &lt;foo location="bar"/&gt;
      &lt;quux&gt;${root.properties.foo}&lt;/quux&gt;
    &lt;/properties&gt;
  &lt;/root&gt;
</pre>
is roughly equivalent to the following fragments in a build.xml file:
<pre>
  &lt;property name="root.properties.foo" location="bar"/&gt;
  &lt;property name="root.properties.quux" value="${root.properties.foo}"/&gt;
</pre>

</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">The XML file to parse.</td>
    <td valign="top" align="center">Yes, or a nested resource collection.</td>
  </tr>
  <tr>
    <td valign="top">prefix</td>
    <td valign="top">The prefix to prepend to each property</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">keepRoot</td>
    <td valign="top">Keep the xml root tag as the
                     first value in the property name.</td>
    <td valign="top" align="center">No, default is <i>true</i>.</td>
  </tr>
  <tr>
    <td valign="top">validate</td>
    <td valign="top">Validate the input file (e.g. by a DTD). Otherwise the XML must only be well-formed.</td>
    <td valign="top" align="center">No, default is <i>false</i>.</td>
  </tr>
  <tr>
    <td valign="top">collapseAttributes</td>
    <td valign="top">Treat attributes as nested elements.</td>
    <td valign="top" align="center">No, default is <i>false</i>.</td>
  </tr>
  <tr>
    <td valign="top">semanticAttributes</td>
    <td valign="top">Enable special handling of certain attribute names.
                     See the <a href="#semanticAttributes">Semantic Attributes</a>
                     section for more information.</td>
    <td valign="top" align="center">No, default is <i>false</i>.</td>
  </tr>
  <tr>
    <td valign="top">includeSemanticAttribute</td>
    <td valign="top">Include the semantic attribute name
                     as part of the property name.  Ignored if
                     <i>semanticAttributes</i> is not set to <i>true</i>.
                     See the <a href="#semanticAttributes">Semantic Attributes</a>
                     section for more information.</td>
    <td valign="top" align="center">No, default is <i>false</i>.</td>
  </tr>
  <tr>
    <td valign="top">rootDirectory</td>
    <td valign="top">The directory to use for resolving file references. Ignored
                     if <i>semanticAttributes</i> is not set to <i>true</i>.</td>
    <td valign="top" align="center">No, default is <i>/Users/<USER>/dev/asf/ant-core</i>.</td>
  </tr>
</table>

<h3><a name="nested">Nested Elements</a></h3>
<h4>xmlcatalog</h4>
<p>The <a href="../CoreTypes/xmlcatalog.html"><tt>&lt;xmlcatalog&gt;</tt></a>
element is used to perform entity resolution.</p>

<h4>any <a href="../CoreTypes/resources.html">resource</a> or single element
resource collection</h4>

<p>The specified resource will be used as input.</p>

<a name="examples">
<h3>Examples</h3>
</a>

<h4>Non-semantic Attributes</h4>

<p>Here is an example xml file that does not have any semantic attributes.</p>

<pre>
   &lt;root-tag myattr="true"&gt;
    &lt;inner-tag someattr="val"&gt;Text&lt;/inner-tag&gt;
    &lt;a2&gt;&lt;a3&gt;&lt;a4&gt;false&lt;/a4&gt;&lt;/a3&gt;&lt;/a2&gt;
   &lt;/root-tag&gt;
</pre>

<h5>default loading</h5>
<p>This entry in a build file:
<pre>   &lt;xmlproperty file="somefile.xml"/&gt;</pre>
is equivalent to the following properties:
<pre>
   root-tag(myattr)=true
   root-tag.inner-tag=Text
   root-tag.inner-tag(someattr)=val
   root-tag.a2.a3.a4=false
</pre>

<h5>collapseAttributes=false</h5>
<p>This entry in a build file:
<pre>   &lt;xmlproperty file="somefile.xml" collapseAttributes="true"/&gt;</pre>
is equivalent to the following properties:
<pre>
   root-tag.myattr=true
   root-tag.inner-tag=Text
   root-tag.inner-tag.someatt=val
   root-tag.a2.a3.a4=false
</pre>

<h5>keepRoot=false</h5>
<p>This entry in a build file:
<pre>   &lt;xmlproperty file="somefile.xml" keepRoot="false"/&gt;</pre>
is equivalent to the following properties:
<pre>
   inner-tag=Text
   inner-tag(someattr)=val
   a2.a3.a4=false
</pre>

<h4>Semantic Attributes</h4>

<p>Here is an example xml file that has semantic attributes.</p>
<pre>
  &lt;root-tag&gt;
    &lt;version value="0.0.1"/&gt;
    &lt;build folder="build"&gt;
      &lt;classes id="build.classes" location="${build.folder}/classes"/&gt;
      &lt;reference refid="build.classes"/&gt;
    &lt;/build&gt;
    &lt;compile&gt;
      &lt;classpath pathid="compile.classpath"&gt;
        &lt;pathelement location="build/classes"/&gt;
      &lt;/classpath&gt;
    &lt;/compile&gt;
    &lt;run-time&gt;
      &lt;jars&gt;*.jar&lt;/jars&gt;
      &lt;classpath pathid="run-time.classpath"&gt;
        &lt;path refid="compile.classpath"/&gt;
        &lt;pathelement path="${run-time.jars}"/&gt;
      &lt;/classpath&gt;
    &lt;/run-time&gt;
  &lt;/root-tag&gt;
</pre>

<h5>default loading (semanticAttributes=true)</h5>
<p>This entry in a build file:
<pre>   &lt;xmlproperty file="somefile.xml"
                semanticAttributes="true"/&gt;</pre>
is equivalent to the following entries in a build file:
<pre>
  &lt;property name="version" value="0.0.1"/&gt;
  &lt;property name="build.folder" value="build"/&gt;
  &lt;property name="build.classes" location="${build.folder}/classes" id="build.classes"/&gt;
  &lt;property name="build.reference" refid="build.classes"/&gt;

  &lt;property name="run-time.jars" value="*.jar/&gt;

  &lt;classpath id="compile.classpath"&gt;
    &lt;pathelement location="build/classes"/&gt;
  &lt;/classpath&gt;

  &lt;classpath id="run-time.classpath"&gt;
    &lt;path refid="compile.classpath"/&gt;
    &lt;pathelement path="${run-time.jars}"/&gt;
  &lt;/classpath&gt;
</pre>

<h5>includeSemanticAttribute="true"</h5>
<p>This entry in a build file:
<pre>   &lt;xmlproperty file="somefile.xml"
                semanticAttributes="true"
                includeSemanticAttribute="true"/&gt;
</pre>
is equivalent to the following entries in a build file:
<pre>
  &lt;property name="version.value" value="0.0.1"/&gt;
  &lt;property name="build.folder" value="build"/&gt;
  &lt;property name="build.classes.location" location="${build.folder}/classes"/&gt;
  &lt;property name="build.reference.refid" refid="build.location"/&gt;

  &lt;property name="run-time.jars" value="*.jar/&gt;

  &lt;classpath id="compile.classpath"&gt;
    &lt;pathelement location="build/classes"/&gt;
  &lt;/classpath&gt;

  &lt;classpath id="run-time.classpath"&gt;
    &lt;path refid="compile.classpath"/&gt;
    &lt;pathelement path="${run-time.jars}"/&gt;
  &lt;/classpath&gt;
</pre>



</body>
</html>
