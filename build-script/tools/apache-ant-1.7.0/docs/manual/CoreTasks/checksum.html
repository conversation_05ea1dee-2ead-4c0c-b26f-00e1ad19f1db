<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Checksum Task</title>
</head>

<body>

<h2><a name="checksum">Checksum</a></h2>
<h3>Description</h3>
<p>
Generates checksum for files.  This task can also be used to
perform checksum verifications.
</p>

<p>Note that many popular message digest functions - including MD5 and
SHA-1 - have been broken recently.  If you are going to use the task
to create checksums used in an environment where security is
important, please take some time to investigate the algorithms offered
by your JCE provider.  Note also that some JCE providers like the one
by <a href="http://www.bouncycastle.org/">The Legion of the Bouncy
Castle</a>, the <a href="http://www.gnu.org/software/gnu-crypto/">GNU
project</a> or <a
href="http://jce.iaik.tugraz.at/products/01_jce/index.php">the
Technical University Graz</a> offer more digest algorithms than those
built-in into your JDK.</p>

<p>
Warning: the case of the extension is that of the algorithm used.
If you ask for "SHA1", you get a .SHA1 extension; if you ask for "sha1", you
get a file ending in .sha1. The Java Crypto Engines are case-insensitive
in matching algorithms, so choose a name to match your desired output extension,
or set the <tt>fileext</tt> attribute.
</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">The file to generate checksum for.</td>
    <td valign="top" align="center">One of either <var>file</var> or
    at least one nested (filesystem-only) resource collection.</td>
  </tr>
  <tr>
    <td valign="top">todir</td>
    <td valign="top">The root directory where checksums should be written.</td>
    <td valign="top" align="center">No. If not specified, checksum files
      will be written to the same directory as the files themselves.
      <em>since Ant 1.6</em>
    </td>
  </tr>
  <tr>
    <td valign="top">algorithm</td>
    <td valign="top">Specifies the algorithm to be used to
      compute the checksum. Defaults to &quot;MD5&quot;.
      Other popular algorithms like &quot;SHA&quot; may be used
      as well.
    </td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">provider</td>
    <td valign="top">Specifies the provider of the algorithm.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">fileext</td>
    <td valign="top">The generated checksum file's name will be the
    original filename with the fileext added to it.
    Defaults to a "." and the algorithm name being used.
    </td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">property</td>
    <td valign="top">This attribute can mean two different things, it
    depends on the presence of the verifyproperty attribute.<br>
    <b>If you don't set the verifyproperty attribute</b>, property
    specifies the name of the property to be set with the generated
    checksum value.<br>
    <b>If you set the verifyproperty attribute</b>, property specifies
    the checksum you expect to be generated (the checksum itself, not
    a name of a property containing the checksum).<br>
    This cannot be specified when fileext is being used or when the
    number of files for which checksums is to be generated is greater
    than 1.
    </td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">pattern</td>
    <td valign="top">Specifies the pattern to use as a pattern
    suitable for <a
    href="http://java.sun.com/j2se/1.4.2/docs/api/java/text/MessageFormat.html">MessageFormat</a>
    where <code>{0}</code> is replaced with the checksum and
    <code>{1}</code> with the file name.</td>
    <td valign="top" align="center">No - default is &quot;{0}&quot;.</td>
  </tr>
  <tr>
    <td valign="top">format</td>
    <td valign="top">Specifies the pattern to use as one of a
    well-known format.  Supported values are &quot;CHECKSUM&quot;
    (only the checksum itself, the default), &quot;MD5SUM&quot; the
    format of GNU textutils md5sum and &quot;SVF&quot; the format of
    *BSDs md5 command.</td>
    <td valign="top" align="center">No - default is &quot;CHECKSUM&quot;.</td>
  </tr>
  <tr>
    <td valign="top">totalproperty</td>
    <td valign="top">If specified, this attribute specifies the name of
      the property that will hold a checksum of all the checksums and
      file paths. The individual checksums and the relative paths to the files
      within the resource collections in which they are defined will be used to
      compute this checksum. (The file separators in the paths will be
      converted to '/' before computation to ensure platform portability).
      <em>since Ant 1.6</em>
    </td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">forceoverwrite</td>
    <td valign="top">Overwrite existing files even if the destination
      files are newer. Defaults to &quot;no&quot;.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">verifyproperty</td>
    <td valign="top">Specifies the name of the property to be set
    with &quot;true&quot; or &quot;false&quot; depending upon whether
    the generated checksum matches the existing checksum.  When
    this is set, the generated checksum is not written to a file or
    property, but rather, the content of the file or property is used to
    check against the generated checksum.
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">readbuffersize</td>
    <td valign="top">The size of the buffer (in bytes) to use when
    reading a file. Defaults to &quot;8192&quot; - you may get a
    better performance on big files if you increase this value.</td>
    <td valign="top" align="center">No</td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>

<h4>resource collection</h4>
 <p>
 <a href="../CoreTypes/resources.html#collection">Resource collections</a> are
 used to select files for which checksums should be generated.
 </p>

<h3>Examples</h3>
<p><b>Example 1</b></p>
<blockquote><pre>&lt;checksum file=&quot;foo.bar&quot;/&gt;</pre></blockquote>
Generates a MD5 checksum for foo.bar and stores the checksum in the destination file
foo.bar.MD5.  foo.bar.MD5 is overwritten only if foo.bar is newer than itself.

<p><b>Example 2</b></p>
<blockquote><pre>&lt;checksum file=&quot;foo.bar&quot; forceOverwrite=&quot;yes&quot;/&gt;</pre></blockquote>
Generates a MD5 checksum for foo.bar and stores the checksum in foo.bar.MD5.
If foo.bar.MD5 already exists, it is overwritten.

<p><b>Example 3</b></p>
<blockquote><pre>&lt;checksum file=&quot;foo.bar&quot; property=&quot;foobarMD5&quot;/&gt;</pre></blockquote>
Generates a MD5 checksum for foo.bar and stores it in the Project Property foobarMD5.

<p><b>Example 4</b></p>
<blockquote><pre>&lt;checksum file=&quot;foo.bar&quot; verifyProperty=&quot;isMD5ok&quot;/&gt;</pre></blockquote>
Generates a MD5 checksum for foo.bar, compares it against foo.bar.MD5 and sets
isMD5ok to either true or false, depending upon the result.

<p><b>Example 5</b></p>
<blockquote><pre>&lt;checksum file=&quot;foo.bar&quot; algorithm=&quot;SHA&quot; fileext=&quot;asc&quot;/&gt;</pre></blockquote>
Generates a SHA checksum for foo.bar and stores the checksum in the destination file
foo.bar.asc.  foo.bar.asc is overwritten only if foo.bar is newer than itself.

<p><b>Example 6</b></p>
<blockquote><pre>
&lt;checksum file=&quot;foo.bar&quot; property=&quot;${md5}&quot; verifyProperty=&quot;isEqual&quot;/&gt;
</pre></blockquote>
Generates a MD5 checksum for foo.bar, compares it against the value of the property
md5, and sets isEqual to either true or false, depending upon the result.

<p><b>Example 7</b></p>
<blockquote><pre>
&lt;checksum&gt;
  &lt;fileset dir=&quot;.&quot;&gt;
    &lt;include name=&quot;foo*&quot;/&gt;
  &lt;/fileset&gt;
&lt;/checksum&gt;
</pre></blockquote>
Works just like Example 1, but generates a .MD5 file for every file that begins with the name foo.

<p><b>Example 8</b></p>
<blockquote><pre>
&lt;condition property=&quot;isChecksumEqual&quot;&gt;
  &lt;checksum&gt;
    &lt;fileset dir=&quot;.&quot;&gt;
      &lt;include name=&quot;foo.bar&quot;/&gt;
    &lt;/fileset&gt;
  &lt;/checksum&gt;
&lt;/condition&gt;
</pre></blockquote>
Works like Example 4, but only sets isChecksumEqual to true, if the
checksum matches - it will never be set to false.  This example
demonstrates use with the Condition task.


<h3>Note:</h3>
When working with more than one file, if condition and/or verifyproperty is used,
the result will be true only if the checksums matched correctly for all files being
considered.



</body>
</html>

