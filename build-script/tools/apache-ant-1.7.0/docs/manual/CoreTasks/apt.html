<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html lang="en-us"><head>
<meta http-equiv="Content-Language" content="en-us"><link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Apt Task</title></head>

<body>

<h2><a name="Apt">Apt</a></h2>
<h3>Description</h3>
<p>Runs the annotation processor tool (apt), and then optionally compiles
   the original code, and any generated source code. This task requires Java 1.5.
   It may work on later versions, but this cannot be confirmed until those
   versions ship. Be advised that the Apt tool does appear to be an unstable
   part of the JDK framework, so may change radically in future versions. 
   In particular it is likely to be obsolete in JDK 6, which can run annotation
   processors as part of javac.
   If the &lt;apt&gt; task does break when upgrading JVM, please
   check to see if there is a more recent version of Ant that tracks
   any changes.</p>


<p>This task inherits from the <a href="javac.html">Javac Task</a>, and thus
   supports nearly all of the same attributes, and subelements.  
   There is one special case, the <tt>fork</tt> attribute, which is present
   but which can only be set to <tt>true</tt>. That is, apt only works as
   a forked process.
 </p>
 <p>
   In addition, it supports
   the following addition items:</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tbody><tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">compile</td>
    <td valign="top">After running the Apt, should the code be compiled.  (see the
                     <code>-nocompile</code> flag on the Apt executable)</td>
    <td align="center" valign="top">No, defaults to false.</td>
  </tr>
  <tr>
    <td valign="top">factory</td>
    <td valign="top">The fully qualified classname of the AnnotationProcessFactory to be used
                     to construct annotation processors.  This represents the <code>-factory</code>
                     command line flag of the Apt executable.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">factorypathref</td>
    <td valign="top">The reference id of the path used to find the classes needed by the
                     AnnotationProcessorFactory (and the location of the factory itself).
                     This represents the <code>-factorypath</code> flag on the Apt executable.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">preprocessdir</td>
    <td valign="top">The directory used for preprocessing.  This is the directory where the
                     generated source code will be place.  This represents the <code>-s</code> flag on
                     the Apt executable.</td>
    <td align="center" valign="top">No</td>
  </tr>
</tbody></table>

<h3>Parameters specified as nested elements</h3>


<h4>factorypath</h4>

<p>You can specify the path used to find the classes needed by the AnnotationProcessorFactory
   at runtime, using this element.  It is represents as a generic path like structure.  This
   represents the <code>-factorypath</code> flag on the Apt executable.</p>


<h4>option</h4>

<p>Used to represent a generic option to pass to Apt.  This represents the <code>-A</code> flag on the
   Apt executable.  You can specify zero or more <code>&lt;option&gt;</code> elements.</p>

<table border="1" cellpadding="2" cellspacing="0">
<tbody><tr>
  <td valign="top" width="12%"><b>Attribute</b></td>
  <td valign="top" width="78%"><b>Description</b></td>
  <td valign="top" width="10%"><b>Required</b></td>
</tr>
  <tr>
    <td valign="top">name</td>
    <td align="center">The name of the option</td>
    <td align="center">Yes.</td>
  </tr>
  <tr>
    <td valign="top">value</td>
    <td align="center">The value to set the option to</td>
    <td align="center">Yes.</td>
  </tr>
</tbody></table>

<h3>Examples</h3>
<blockquote><pre>
&lt;apt srcdir="${src}"
     destdir="${build}"
     classpath="xyz.jar"
     debug="on"
     compile="true"
     factory="com.mycom.MyAnnotationProcessorFactory"
     factorypathref="my.factorypath.id"
     preprocessdir="${preprocess.dir}"&gt;
&lt;/apt&gt;
</pre></blockquote>
<p>compiles all <code>.java</code> files under the <code>${src}</code>
directory, and stores
the <code>.class</code> files in the <code>${build}</code> directory.
The classpath used includes <code>xyz.jar</code>, and compiling with
debug information is on.  It also forces the generated source code to
be compiled.  The generated source code will be placed in
<code>${preprocess.dir}</code> directory, using the class
<code>com.mycom.MyAnnotationProcessorFactory</code> to supply
AnnotationProcessor instances.</p>


<h3>Notes</h3>

<p>
The inherited "fork" attribute is set to true by default; please do not change it.
</p>

<p>
The inherited "compiler" attribute is ignored, as it is forced to use the Apt compiler
</p>

<p>Using the Apt compiler with the "compile" option set to "true"
   forces you to use Sun's Apt compiler, which will use the JDK's Javac compiler.
   If you wish to use another compiler, you will first need run the Apt processor
   with the "compile" flag set to "false", and then use a
   <code>&lt;javac&gt;</code> task to compile first your original source code, and then the
   generated source code:</p>

<blockquote><pre>
&lt;apt srcdir="${src}"
     destdir="${build}"
     classpath="xyz.jar"
     debug="true"
     compile="false"
     factory="com.mycom.MyAnnotationProcessorFactory"
     factorypathref="my.factorypath.id"
     preprocessdir="${preprocess.dir}"&gt;
&lt;/apt&gt;

&lt;javac srcdir="${src}"
       destdir="${build}"
       classpath="xyz.jar"
       debug="on"/&gt;

&lt;javac srcdir="${preprocess.dir}"
       destdir="${build}"
       classpath="xyz.jar"
       debug="true"/&gt;
</pre></blockquote>

This may involve more build file coding, but the speedup gained from switching
to jikes may justify the effort.
<p>
</p>

</body></html>
