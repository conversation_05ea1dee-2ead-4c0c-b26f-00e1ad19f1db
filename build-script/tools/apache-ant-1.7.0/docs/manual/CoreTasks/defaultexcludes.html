<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>DefaultExcludes Task</title>
</head>

<body>

<h2><a name="echo">DefaultExcludes</a></h2>

<p><em>since Ant 1.6</em></p>

<h3>Description</h3>
<p>Alters the default excludes for all subsequent processing in the
build, and prints out the current default excludes if desired.

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">echo</td>
    <td valign="top">whether or not to print out the default excludes.(defaults to false)</td>
    <td valign="top" align="center">attribute "true" required if no
                                    other attribute specified</td>
  </tr>
  <tr>
    <td valign="top">default</td>
    <td valign="top">go back to hard wired default excludes</td>
    <td valign="top" align="center">attribute "true" required if no
    if no other attribute is specified</td>
  </tr>
  <tr>
    <td valign="top">add</td>
    <td valign="top">the pattern to add to the default excludes</td>
    <td valign="top" align="center">if no other attribute is specified</td>
  </tr>
  <tr>
    <td valign="top">remove</td>
    <td valign="top">remove the specified pattern from the default excludes</td>
    <td valign="top" align="center">if no other attribute is specified</td>
  </tr>
</table>

<h3>Examples</h3>

<p>Print out the default excludes</p>

<pre>  &lt;defaultexcludes echo=&quot;true&quot;/&gt;</pre>

<p>Print out the default excludes and exclude all *.bak files in
<strong>all</strong> further processing</p>

<pre>  &lt;defaultexcludes echo=&quot;true&quot; add=&quot;**/*.bak&quot;/&gt;</pre>

<p>Silently allow several fileset based tasks to operate on emacs
backup files and then restore normal behavior</p>

<pre>
  &lt;defaultexcludes remove=&quot;**/*~&quot;/&gt;

  (do several fileset based tasks here)

  &lt;defaultexcludes default=&quot;true&quot;/&gt;
</pre>

<h3>Notes</h3>
By default the pattern <tt>**/.svn</tt> and <tt>**/.svn/**</tt> are set as default 
excludes. With version 1.3 Subversion supports the 
<a target="_blank" href="http://subversion.tigris.org/svn_1.3_releasenotes.html">&quot;_svn hack&quot;</a>.
That means, that the svn-libraries evaluate environment variables and use <i>.svn</i> 
or <i>_svn</i> directory regarding to that value. We had chosen not to evaluate environment variables to
get a more reliable build. Instead you have to change the settings by yourself by changing
the exclude patterns:
<pre>
  &lt;defaultexcludes remove=&quot;**/.svn&quot;/&gt;
  &lt;defaultexcludes remove=&quot;**/.svn/**&quot;/&gt;
  &lt;defaultexcludes add=&quot;**/_svn&quot;/&gt;
  &lt;defaultexcludes add=&quot;**/_svn/**&quot;/&gt;
</pre>




</body>
</html>
