<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>GenKey Task</title>
</head>

<body>

<h2><a name="genkey">GenKey</a></h2>
<h3>Description</h3>
<p>Generates a key in a keystore. </p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">alias</td>
    <td valign="top">the alias to add under</td>
    <td valign="top" align="center">Yes.</td>
  </tr>
  <tr>
    <td valign="top">storepass</td>
    <td valign="top">password for keystore integrity. Must
	be at least 6 characters long</td>
    <td valign="top" align="center">Yes.</td>
  </tr>
  <tr>
    <td valign="top">keystore</td>
    <td valign="top">keystore location</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">storetype</td>
    <td valign="top">keystore type</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">keypass</td>
    <td valign="top">password for private key (if different)</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">sigalg</td>
    <td valign="top">the algorithm to use in signing</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">keyalg</td>
    <td valign="top">the method to use when generating name-value pair</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">verbose</td>
    <td valign="top">(true | false) verbose output when signing</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">dname</td>
    <td valign="top">The distinguished name for entity</td>
    <td valign="top" align="center">Yes if dname element unspecified</td>
  </tr>
  <tr>
    <td valign="top">validity</td>
    <td valign="top">(integer) indicates how many days certificate is valid</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">keysize</td>
    <td valign="top">(integer) indicates the size of key generated</td>
    <td valign="top" align="center">No</td>
  </tr>
</table>

<p>Alternatively you can specify the distinguished name by creating a
sub-element named dname and populating it with param elements that
have a name and a value. When using the subelement it is automatically
encoded properly and commas (&quot;<code>,</code>&quot;) are replaced
with &quot;<code>\,</code>&quot;.</p>

<p>The following two examples are identical: </p>

<h3>Examples</h3>
<blockquote><pre>
&lt;genkey alias=&quot;apache-group&quot; storepass=&quot;secret&quot; 
  dname=&quot;CN=Ant Group, OU=Jakarta Division, O=Apache.org, C=US&quot;/&gt;
</pre></blockquote>

<blockquote>
  <pre>
&lt;genkey alias=&quot;apache-group&quot; storepass=&quot;secret&quot; &gt;
  &lt;dname&gt;
    &lt;param name=&quot;CN&quot; value=&quot;Ant Group&quot;/&gt;
    &lt;param name=&quot;OU&quot; value=&quot;Jakarta Division&quot;/&gt;
    &lt;param name=&quot;O&quot;  value=&quot;Apache.Org&quot;/&gt;
    &lt;param name=&quot;C&quot;  value=&quot;US&quot;/&gt;
  &lt;/dname&gt;
&lt;/genkey&gt;</pre>
</blockquote>


</body>
</html>

