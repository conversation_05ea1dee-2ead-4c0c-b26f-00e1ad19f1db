<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Common</title>
</head>

<body>

<h2><a name="javac">Common Attributes of all Tasks</a></h2>
<p>All tasks share the following attributes:</p>

<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">id</td>
    <td valign="top">Unique identifier for this task instance, can be
       used to reference this task in scripts.</td>
    <td valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">taskname</td>
    <td valign="top">A different name for this task instance - will
      show up in the logging output.</td>
    <td valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">description</td>
    <td valign="top">Room for your comments</td>
    <td valign="top">No</td>
  </tr>
</table>



</body>
</html>

