<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>
<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>LoadFile Task</title>
</head>

<body>


<h2><a name="loadfile">LoadFile</a></h2>
<h3>Description</h3>
<p>
Specialization of <a href="loadresource.html">loadresource</a> that
works on files exclusively and provides a srcFile attribute for
convenience.  Unless an encoding is specified, the encoding of the
current locale is used.
</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">srcFile</td>
    <td valign="top">source file</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">property</td>
    <td valign="top">property to save to</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">encoding</td>
    <td valign="top">encoding to use when loading the file</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">failonerror</td>
    <td valign="top">Whether to halt the build on failure</td>
    <td align="center" valign="top">No, default "true"</td>
  </tr>
  <tr>
    <td valign="top">quiet</td>
    <td valign="top">Do not display a diagnostic message (unless Ant has been 
    invoked with the <code>-verbose</code> or <code>-debug</code>
    switches) or modify the exit status to reflect an error. Setting this to 
    "true" implies setting failonerror to "false".
      <em>Since Ant 1.7.0.</em>
    </td>
    <td align="center" valign="top">No, default "false"</td>
  </tr>

</table>
<p>
The LoadFile task supports nested <a href="../CoreTypes/filterchain.html">
FilterChain</a>s.

<h3>Examples</h3>
<pre>    &lt;loadfile property="message"
      srcFile="message.txt"/&gt;
</pre>
Load file message.txt into property "message"; an <tt>&lt;echo&gt;</tt>
can print this.  This is identical to
<pre>    &lt;loadresource property="message"&gt;
       &lt;file file="message.txt"/&gt;
    &lt;/loadresource&gt;
</pre>
</p>

<pre>    &lt;loadfile property="encoded-file"
      srcFile="loadfile.xml"
      encoding="ISO-8859-1"/&gt;
</pre>
Load a file using the latin-1 encoding

<pre>    &lt;loadfile
      property="optional.value"
      srcFile="optional.txt"
      failonerror="false"/&gt;
</pre>
Load a file, don't fail if it is missing (a message is printed, though)

<pre>    &lt;loadfile
      property="mail.recipients"
      srcFile="recipientlist.txt"&gt;
      &lt;filterchain&gt;
        &lt;<a href="../CoreTypes/filterchain.html#striplinebreaks">striplinebreaks</a>/&gt;
      &lt;/filterchain&gt;
    &lt;/loadfile&gt;
</pre>
Load a property which can be used as a parameter for another task (in this case mail),
merging lines to ensure this happens.

<pre>    &lt;loadfile
      property="system.configuration.xml"
      srcFile="configuration.xml"&gt;
        &lt;filterchain&gt;
          &lt;<a href="../CoreTypes/filterchain.html#expandproperties">expandproperties</a>/&gt;
        &lt;/filterchain&gt;
    &lt;/loadfile&gt;
</pre>
Load an XML file into a property, expanding all properties declared
in the file in the process.




</body>
</html>

