<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>AntStructure Task</title>
</head>

<body>

<h2><a name="antstructure">AntStructure</a></h2>
<h3>Description</h3> 

<p>Generates an DTD for Ant buildfiles which contains information
about all tasks currently known to <PERSON><PERSON>.</p>

<p>Actually the DTD will not be a real DTD for buildfiles since <PERSON><PERSON>'s
usage of XML cannot be captured with a DTD.  Several elements in Ant
can have different attribute lists depending on the element that
contains them.  &quot;fail&quot; for example can be <a
href="fail.html">the task</a> or the nested child element of the <a
href="../OptionalTasks/sound.html">sound</a> task.  Don't consider the
generated DTD something to rely upon.</p>

<p>Also note that the DTD generated by this task is incomplete, you can
always add XML entities using <a
href="taskdef.html"><code>&lt;taskdef&gt;</code></a> or <a
href="typedef.html"><code>&lt;typedef&gt;</code></a>. See <a
href="http://www.sdv.fr/pages/casa/html/ant-dtd.en.html"
target="_top">here</a> for a way to get around this problem.</p>
<p>This task doesn't know about required attributes, all will be
listed as <code>#IMPLIED</code>.</p>

<p><em>Since Ant 1.7</em> custom structure printers can be used
instead of the one that emits a DTD.  In order to plug in your own
structure, you have to implement the interface
<code>org.apache.tools.ant.taskdefs.AntStructure.StructurePrinter</code>
and <code>&lt;typedef&gt; your class and use the new type as a nested
element of this task - see the example below.</code>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">output</td>
    <td valign="top">file to write the DTD to.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
</table>
<h3>Examples</h3>
<blockquote><pre>
&lt;antstructure output=&quot;project.dtd&quot; /&gt;
</pre></blockquote>

<p><b>Emitting your own structure instead of a DTD</b></p>

<p>First you need to implement the interface</p>

<pre>
package org.example;
import org.apache.tools.ant.taskdefs.AntStructure;
public class MyPrinter implements AntStructure.StructurePrinter {
    ...
}
</pre>

<p>and then use it via typedef</p>

<pre>
  &lt;typedef name="myprinter" classname="org.example.MyPrinter" /&gt;
  &lt;antstructure output="project.my"&gt;
    &lt;myprinter /&gt;
  &lt;/antstructure&gt;
</pre>

<p>Your own StructurePrinter can accept attributes and nested elements
just like any other Ant type or task.</p>

</body>
</html>
