<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>
<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>LoadProperties Task</title>
</head>

<body>


<h2><a name="loadproperties">LoadProperties</a></h2>
<h3>Description</h3>
<p>
Load a file's contents as Ant properties.  This is equivalent
to <code>&lt;property file|resource=&quot;...&quot;/&gt;</code> except that it
supports nested <code>&lt;filterchain&gt;</code> elements.
Also if the file is missing, the build is halted with an error, rather
than a warning being printed.
</p>

<p>If you want to simulate <a href="property.html">property</a>'s
prefix attribute, please use <a
href="../CoreTypes/filterchain.html#prefixlines">prefixlines</a>
filter.</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">srcFile</td>
    <td valign="top">source file</td>
    <td valign="top" rowspan="2" align="center">One of these or a
          nested resource</td>
  </tr>
  <tr>
    <td valign="top">resource</td>
    <td valign="top">the resource name of the property file</td>
  </tr>
  <tr>
    <td valign="top">encoding</td>
    <td valign="top">encoding to use when loading the file</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">classpath</td>
    <td valign="top">the classpath to use when looking up a resource.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">classpathref</td>
    <td valign="top">the classpath to use when looking up a resource,
      given as <a href="../using.html#references">reference</a>
      to a <code>&lt;path&gt;</code> defined elsewhere..</td>
    <td align="center" valign="top">No</td>
  </tr>
</table>

<h3>Parameters specified as nested elements</h3>

<h4>any <a href="../CoreTypes/resources.html">resource</a> or single element
resource collection</h4>

<p>The specified resource will be used as src. <em>Since Ant 1.7</em></p>

<h4><a href="../CoreTypes/filterchain.html">FilterChain</a></h4>

<h4>classpath</h4>

<p>for use with the <i>resource</i> attribute.</p>

<h3>Examples</h3>
<pre>    &lt;loadproperties srcFile="file.properties"/&gt;
</pre>
or
<pre>
    &lt;loadproperties&gt;
      &lt;file file="file.properties"/&gt;
    &lt;/loadproperties&gt;
</pre>
Load contents of file.properties as Ant properties.

<pre>    &lt;loadproperties srcFile="file.properties"&gt;
      &lt;filterchain&gt;
        &lt;<a href="../CoreTypes/filterchain.html#linecontains">linecontains</a>&gt;
          &lt;contains value=&quot;import.&quot;/&gt;
        &lt;/linecontains&gt;
      &lt;/filterchain&gt;
    &lt;/loadproperties&gt;
</pre>
Read the lines that contain the string &quot;import.&quot;
from the file &quot;file.properties&quot; and load them as
Ant properties.

<pre>
    &lt;loadproperties&gt;
      &lt;<a href="../CoreTypes/resources.html#gzipresource">gzipresource</a>&gt;
        &lt;<a href="../CoreTypes/resources.html#url">url</a> url="http://example.org/url.properties.gz"/&gt;
      &lt;/gzipresource&gt;
    &lt;/loadproperties&gt;
</pre>
Load contents of http://example.org/url.properties.gz, uncompress it
on the fly and load the contents as Ant properties.



</body>
</html>

