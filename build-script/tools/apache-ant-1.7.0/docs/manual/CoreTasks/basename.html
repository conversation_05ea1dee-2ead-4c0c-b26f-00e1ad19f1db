<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Basename Task</title>
</head>

<body>

<h2><a name="echo">Basename</a></h2>
<h3>Description</h3>
<p>
Task to determine the basename of a specified file, optionally minus a
specified suffix.
</p>
<p>
When this task executes, it will set the specified property to the
value of the last path element of the specified file. If <code>file</code> is a
directory, the basename will be the last directory element. If
<code>file</code> is a full-path, relative-path, or simple filename,
the basename will be the simple file name, without any directory elements.
</p>
<p> 
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">The path to take the basename of.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">property</td>
    <td valign="top">The name of the property to set.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">suffix</td>
    <td valign="top">The suffix to remove from the resulting basename
      (specified either with or without the &quot;<code>.</code>&quot;).</td>
    <td valign="top" align="center">No</td>
  </tr>
</table>

<h3>Examples</h3>
<blockquote><pre>
&lt;basename property=&quot;jar.filename&quot; file=&quot;${lib.jarfile}&quot;/&gt;
</pre></blockquote>
will set <code>jar.filename</code> to
<code>myjar.jar</code>, if <code>lib.jarfile</code> is defined as either a
full-path filename (eg., <code>/usr/local/lib/myjar.jar</code>),
a relative-path filename (eg., <code>lib/myjar.jar</code>),
or a simple filename (eg., <code>myjar.jar</code>).
<blockquote><pre>
&lt;basename property=&quot;cmdname&quot; file=&quot;D:/usr/local/foo.exe&quot;
          suffix=&quot;.exe&quot;/&gt;
</pre></blockquote>
will set <code>cmdname</code> to <code>foo</code>.
<blockquote><pre>
&lt;property environment=&quot;env&quot;/&gt;
&lt;basename property=&quot;temp.dirname&quot; file=&quot;${env.TEMP}&quot;/&gt;
</pre></blockquote>

will set <code>temp.dirname</code> to the last directory element of
the path defined for the <code>TEMP</code> environment variable.</p>



</body>
</html>

