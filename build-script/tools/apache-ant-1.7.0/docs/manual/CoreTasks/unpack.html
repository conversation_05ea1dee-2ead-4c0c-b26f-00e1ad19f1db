<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>GUnzip/BUnzip2 Task</title>
</head>

<body>

<h2><a name="unpack">GUnzip/BUnzip2</a></h2>
<h3>Description</h3>
<p>Expands a resource packed using GZip or BZip2.</p>

<p>If <i>dest</i> is a directory the name of the destination file is
the same as <i>src</i> (with the &quot;.gz&quot; or &quot;.bz2&quot;
extension removed if present). If <i>dest</i> is omitted, the parent
dir of <i>src</i> is taken. The file is only expanded if the source
resource is newer than the destination file, or when the destination file
does not exist.</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">src</td>
    <td valign="top">the file to expand.</td>
    <td align="center" valign="top">Yes, or a nested resource collection.</td>
  </tr>
  <tr>
    <td valign="top">dest</td>
    <td valign="top">the destination file or directory.</td>
    <td align="center" valign="top">No</td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>

<h4>any <a href="../CoreTypes/resources.html">resource</a> or single element
resource collection</h4>

<p>The specified resource will be used as src.</p>

<h3>Examples</h3>
<blockquote><pre>
&lt;gunzip src=&quot;test.tar.gz&quot;/&gt;
</pre></blockquote>
<p>expands <i>test.tar.gz</i> to <i>test.tar</i></p>
<blockquote><pre>
&lt;bunzip2 src=&quot;test.tar.bz2&quot;/&gt;
</pre></blockquote>
<p>expands <i>test.tar.bz2</i> to <i>test.tar</i></p>
<blockquote><pre>
&lt;gunzip src=&quot;test.tar.gz&quot; dest=&quot;test2.tar&quot;/&gt;
</pre></blockquote>
<p>expands <i>test.tar.gz</i> to <i>test2.tar</i></p>
<blockquote><pre>
&lt;gunzip src=&quot;test.tar.gz&quot; dest=&quot;subdir&quot;/&gt;
</pre></blockquote>
<p>expands <i>test.tar.gz</i> to <i>subdir/test.tar</i> (assuming
subdir is a directory).</p>
<blockquote><pre>
&lt;gunzip dest=&quot;.&quot;&gt;
  &lt;url url="http://example.org/archive.tar.gz"/&gt;
&lt;/gunzip&gt;
</pre></blockquote>
<p>downloads <i>http://example.org/archive.tar.gz</i> and expands it
to <i>archive.tar</i> in the project's basedir on the fly.</p>

<h3>Related tasks</h3>

<pre>
&lt;gunzip src="some-archive.gz" dest="some-dest-dir"/&gt;
</pre>

is identical to

<pre>
&lt;copy todir="some-dest-dir"&gt;
  &lt;gzipresource&gt;
    &lt;file file="some-archive.gz"/&gt;
  &lt;/gzipresource&gt;
  &lt;mapper type="glob" from="*.gz" to="*"/&gt;
&lt;/copy&gt;
</pre>

<p>The same is also true for <code>&lt;bunzip2&gt;</code> and
<code>&lt;bzip2resource&gt;</code>.  <code>&lt;copy&gt;</code> offers
additional features like <a
href="../CoreTypes/filterchains.html">filtering files</a> on the fly,
allowing a file to be mapped to multiple destinations, preserving the
last modified time or a configurable file system timestamp
granularity.</p>



</body>
</html>
