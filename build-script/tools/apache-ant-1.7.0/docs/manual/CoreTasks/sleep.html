<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Sleep Task</title>
</head>

<body>

<h2><a name="sleep">Sleep</a></h2>
<h3>Description</h3>
<p> A task for sleeping a short period of time, useful when a build or deployment 
  process requires an interval between tasks.</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr> 
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr> 
    <td valign="top">hours</td>
    <td valign="top">hours to to add to the sleep time</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr> 
    <td valign="top">minutes</td>
    <td valign="top"> minutes to add to the sleep time</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr> 
    <td valign="top">seconds</td>
    <td valign="top">seconds to add to the sleep time</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr> 
    <td valign="top">milliseconds</td>
    <td valign="top">milliseconds to add to the sleep time</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr> 
    <td valign="top">failonerror</td>
    <td valign="top">flag controlling whether to break the build on an error. 
    </td>
    <td align="center" valign="top">No</td>
  </tr>
</table>
<p>The sleep time is the sum of specified values, hours, minutes seconds and milliseconds. 
  A negative value can be supplied to any of them provided the total sleep time 
  is positive</p>
<p>Note that sleep times are always hints to be interpred by the OS how it feels 
  - small times may either be ignored or rounded up to a minimum timeslice. Note 
  also that the system clocks often have a fairly low granularity too, which complicates 
  measuring how long a sleep actually took.</p>
<h3>Examples</h3>
<pre>   &lt;sleep milliseconds=&quot;10&quot;/&gt;</pre>
Sleep for about 10 mS. 
<pre>   &lt;sleep seconds=&quot;2&quot;/&gt;</pre>
Sleep for about 2 seconds. 
<pre>   &lt;sleep hours=&quot;1&quot; minutes=&quot;-59&quot; seconds=&quot;-58&quot;/&gt;</pre>
<p>Sleep for one hour less 59:58, or two seconds again </p>
<pre>   &lt;sleep/&gt;</pre>
Sleep for no time at all. This may yield the CPU time to another thread or process. 


</body>
</html>

