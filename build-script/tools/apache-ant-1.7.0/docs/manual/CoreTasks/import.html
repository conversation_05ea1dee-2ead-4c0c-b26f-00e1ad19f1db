<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
  <meta http-equiv="Content-Language" content="en-us">
  <link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Import Task</title>
  <link rel="stylesheet" type="text/css" href="../stylesheets/antmanual.css">
</head>
<body>
  <h2><a name="import">Import</a></h2>
  <h3>Description</h3>
  <p>
    Imports another build file into the current project.
  </p>
  <p>
    On execution it will read another Ant file into
    the same Project. This means that it basically works like the 
    <a href="http://ant.apache.org/faq.html#xml-entity-include">Entity
      Includes as explained in the Ant FAQ</a>, as if the imported file was
    contained in the importing file, minus the top <code>&lt;project&gt;</code>
    tag.
  </p>
  <p>
    The import task may only be used as a top-level task. This means that
    it may not be used in a target.
  </p>
  <p>
There are two further functional aspects that pertain to this task and
that are not possible with entity includes:
<ul>
  <li>target overriding</li>
  <li>special properties</li>
</ul>
  </p>
<h4>Target overriding</h4>

<p>If a target in the main file is also present in at least one of the
imported files, the one from the main file takes precedence.</p>

<p>So if I import for example a <i>docsbuild.xml</i> file named <b>builddocs</b>,
that contains a &quot;<b>docs</b>&quot; target, I can redefine it in my main
buildfile and that is the one that will be called. This makes it easy to
keep the same target name, so that the overriding target is still called
by any other targets--in either the main or imported buildfile(s)--for which
it is a dependency, with a different implementation. The target from <i>docsbuild.xml</i> is
made available by the name &quot;<b>builddocs</b><b>.docs</b>&quot;.
This enables the new implementation to call the old target, thus
<i>enhancing</i> it with tasks called before or after it.</p>

<h4>Special Properties</h4>

<p>Imported files are treated as they are present in the main
buildfile. This makes it easy to understand, but it makes it impossible
for them to reference files and resources relative to their path.
Because of this, for every imported file, Ant adds a property that
contains the path to the imported buildfile. With this path, the
imported buildfile can keep resources and be able to reference them
relative to its position.</p>

<p>So if I import for example a <i>docsbuild.xml</i> file named <b>builddocs</b>,
I can get its path as <b>ant.file.builddocs</b>, similarly to the <b>ant.file</b>
property of the main buildfile.</p>

<p>Note that &quot;builddocs&quot; is not the filename, but the name attribute
present in the imported project tag.</p>
  <p>
    If import file does not have a name attribute, the ant.file.projectname
    property will not be set.
  </p>

<h4>Resolving files against the imported file</h4>

<p>Suppose your main build file called <code>importing.xml</code>
imports a build file <code>imported.xml</code>, located anywhere on
the file system, and <code>imported.xml</code> reads a set of
properties from <code>imported.properties</code>:</p>

<pre>&lt;!-- importing.xml --&gt;
&lt;project name="importing" basedir="." default="..."&gt;
&nbsp; &lt;import file="${path_to_imported}/imported.xml"/&gt;
&lt;/project&gt;

&lt;!-- imported.xml --&gt;
&lt;project name="imported" basedir="." default="..."&gt;
&nbsp; &lt;property file="imported.properties"/&gt;
&lt;/project&gt;
</pre>

<p>This snippet however will resolve <code>imported.properties</code>
against the basedir of <code>importing.xml</code>, because the basedir
of <code>imported.xml</code> is ignored by Ant. The right way to use
<code>imported.properties</code> is:</p>

<pre>
&lt;!-- imported.xml --&gt;
&lt;project name="imported" basedir="." default="..."&gt;
&nbsp; &lt;dirname property="imported.basedir" file="${ant.file.imported}"/&gt;
&nbsp; &lt;property file="${imported.basedir}/imported.properties"/&gt;
&lt;/project&gt;
</pre>

<p>As explained above <code>${ant.file.imported}</code> stores the
path of the build script, that defines the project called
<code>imported</code>, (in short it stores the path to
<code>imported.xml</code>) and <a
href="dirname.html"><code>&lt;dirname&gt;</code></a> takes its
directory. This technique also allows <code>imported.xml</code> to be
used as a standalone file (without being imported in other
project).</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tbody>
    <tr>
      <td valign="top"><b>Attribute</b></td>
      <td valign="top"><b>Description</b></td>
      <td align="center" valign="top"><b>Required</b></td>
    </tr>
    <tr>
      <td valign="top">
        file
      </td>
      <td valign="top">
        The file to import. If this is a relative file name, the file name will be resolved
        relative to the <i>importing</i> file. <b>Note</b>, this is unlike most other
        ant file attributes, where relative files are resolved relative to /Users/<USER>/dev/asf/ant-core.
      </td>
      <td valign="top" align="center">Yes</td>
    </tr>
    <tr>
      <td valign="top">
        optional
      </td>
      <td valign="top">
        If true, do not stop the build if the file does not exist,
        default is false.
      </td>
      <td valign="top" align="center">No</td>
    </tr>
  </tbody>
</table>

<h3>Examples</h3>
<pre>&nbsp; &lt;import file=&quot;../common-targets.xml&quot;/&gt;
</pre>

<p>Imports targets from the common-targets.xml file that is in a parent
directory.</p>

<pre>&nbsp; &lt;import file=&quot;${deploy-platform}.xml&quot;/&gt;
</pre>

<p>Imports the project defined by the property deploy-platform</p>


</body>
</html>
