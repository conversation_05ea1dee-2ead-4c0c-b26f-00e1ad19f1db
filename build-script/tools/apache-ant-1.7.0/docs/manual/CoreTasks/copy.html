<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Copy Task</title>
</head>

<body>

<h2><a name="copy">Copy</a></h2>
<h3>Description</h3>
<p>Copies a file or resource collection to a new file or directory.  By default, files are
only copied if the source file is newer than the destination file,
or when the destination file does not exist.  However, you can explicitly
overwrite files with the <code>overwrite</code> attribute.</p>

<p><a href="../CoreTypes/resources.html#collection">Resource
Collection</a>s are used to select a group of files to copy.  To use a
resource collection, the <code>todir</code> attribute must be set.</p>

<p>
<strong>Note: </strong>If you employ filters in your copy operation,
you should limit the copy to text files. Binary files will be corrupted
by the copy operation.
This applies whether the filters are implicitly defined by the
<a href="filter.html">filter</a> task or explicitly provided to the copy
operation as <a href="../CoreTypes/filterset.html">filtersets</a>.
  <em>See <a href="#encoding">encoding note</a></em>.
</p>
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">The file to copy.</td>
    <td valign="top" align="center">Yes, unless a nested
    resource collection element is used.</td>
  </tr>
  <tr>
    <td valign="top">preservelastmodified</td>
    <td valign="top">Give the copied files the same last modified
      time as the original source files.</td>
    <td valign="top" align="center">No; defaults to false.</td>
  </tr>
  <tr>
    <td valign="top">tofile</td>
    <td valign="top">The file to copy to.</td>
    <td valign="top" align="center" rowspan="2">With the <code>file</code>
      attribute, either <code>tofile</code> or <code>todir</code> can be used.
      With nested resource collection elements, if the number of included files
      is greater than 1, or if only the <code>dir</code> attribute is
      specified in the <code>&lt;fileset&gt;</code>, or if the
      <code>file</code> attribute is also specified, then only
      <code>todir</code> is allowed.</td>
  </tr>
  <tr>
    <td valign="top">todir</td>
    <td valign="top">The directory to copy to.</td>
  </tr>
  <tr>
    <td valign="top">overwrite</td>
    <td valign="top">Overwrite existing files even if the destination
      files are newer.</td>
    <td valign="top" align="center">No; defaults to false.</td>
  </tr>
  <tr>
    <td valign="top">filtering</td>
    <td valign="top">Indicates whether token filtering using the global
      build-file filters should take place during the copy.
      <em>Note</em>: Nested <code>&lt;filterset&gt;</code> elements will
      always be used, even if this attribute is not specified, or its value is
      <code>false</code> (<code>no</code>, or <code>off</code>).</td>
    <td valign="top" align="center">No; defaults to false.</td>
  </tr>
  <tr>
    <td valign="top">flatten</td>
    <td valign="top">Ignore the directory structure of the source files,
      and copy all files into the directory specified by the <code>todir</code>
      attribute.  Note that you can achieve the same effect by using a
      <a href="../CoreTypes/mapper.html#flatten-mapper">flatten mapper</a>.</td>
    <td valign="top" align="center">No; defaults to false.</td>
  </tr>
  <tr>
    <td valign="top">includeEmptyDirs</td>
     <td valign="top">Copy any empty directories included in the FileSet(s).
     </td>
     <td valign="top" align="center">No; defaults to true.</td>
  </tr>
  <tr>
    <td valign="top">failonerror</td>
     <td valign="top">If false, log a warning message, but do not stop the
       build, when the file to copy does not exist or one of the nested
       filesets points to a directory that doesn't exist or an error occurs
       while copying.
     </td>
     <td valign="top" align="center">No; defaults to true.</td>
  </tr>
  <tr>
    <td valign="top">verbose</td>
     <td valign="top">Log the files that are being copied.</td>
     <td valign="top" align="center">No; defaults to false.</td>
  </tr>
  <tr>
    <td valign="top">encoding</td>
    <td valign="top">The encoding to assume when filter-copying the
    files. <em>since Ant 1.5</em>.</td>
    <td align="center">No - defaults to default JVM encoding</td>
  </tr>
  <tr>
    <td valign="top">outputencoding</td>
    <td valign="top">The encoding to use when writing the files.
    <em>since Ant 1.6</em>.</td>
    <td align="center">No - defaults to the value of the encoding
    attribute if given or the default JVM encoding otherwise.</td>
  </tr>
  <tr>
    <td valign="top">enablemultiplemappings</td>
    <td valign="top">
      If true the task will process to all the mappings for a
      given source path. If false the task will only process
      the first file or directory. This attribute is only relevant
      if there is a mapper subelement.
      <em>since Ant 1.6</em>.</td>
    <td align="center">No - defaults to false.</td>
  </tr>
  <tr>
    <td valign="top">granularity</td>
    <td valign="top">The number of milliseconds leeway to give before
    deciding a file is out of date. This is needed because not every
    file system supports tracking the last modified time to the
    millisecond level. Default is 1 second, or 2 seconds on DOS
    systems.  This can also be useful if source and target files live
    on separate machines with clocks being out of sync.  <em>since Ant
    1.6.2</em>.</td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>

<h4>fileset or any other resource collection</h4>
<p><a href="../CoreTypes/resources.html#collection">Resource
Collection</a>s are used to select groups of files to copy.  To use a
resource collection, the <code>todir</code> attribute must be set.</p>
<p>Prior to Ant 1.7 only <code>&lt;fileset&gt;</code> has been
supported as a nested element.</p>

<h4>mapper</h4>
 <p>You can define filename transformations by using a nested <a
 href="../CoreTypes/mapper.html">mapper</a> element. The default mapper used by
 <code>&lt;copy&gt;</code> is the <a
 href="../CoreTypes/mapper.html#identity-mapper">identity mapper</a>.</p>
  <p>
    <em>Since Ant 1.6.3</em>,
    one can use a filenamemapper type in place of the mapper element.
  </p>

<p>Note that the source name handed to the mapper depends on the
resource collection you use.  If you use <code>&lt;fileset&gt;</code>
or any other collection that provides a base directory, the name
passed to the mapper will be a relative filename, relative to the base
directory.  In any other case the absolute filename of the source will
be used.</p>

<h4>filterset</h4>
 <p><a href="../CoreTypes/filterset.html">FilterSet</a>s are used to replace
tokens in files that are copied.
 To use a FilterSet, use the nested <code>&lt;filterset&gt;</code> element.</p>

<h4>filterchain</h4>
<p>The Copy task supports nested <a href="../CoreTypes/filterchain.html">
FilterChain</a>s.</p>

<p>
If <code>&lt;filterset&gt;</code> and <code>&lt;filterchain&gt;</code> elements are used inside the
same <code>&lt;copy&gt;</code> task, all <code>&lt;filterchain&gt;</code> elements are processed first
followed by <code>&lt;filterset&gt;</code> elements.
</p>

<h3>Examples</h3>
<p><b>Copy a single file</b></p>
<pre>
  &lt;copy file=&quot;myfile.txt&quot; tofile=&quot;mycopy.txt&quot;/&gt;
</pre>
<p><b>Copy a single file to a directory</b></p>
<pre>
  &lt;copy file=&quot;myfile.txt&quot; todir=&quot;../some/other/dir&quot;/&gt;
</pre>
<p><b>Copy a directory to another directory</b></p>
<pre>
  &lt;copy todir=&quot;../new/dir&quot;&gt;
    &lt;fileset dir=&quot;src_dir&quot;/&gt;
  &lt;/copy&gt;
</pre>
<p><b>Copy a set of files to a directory</b></p>
<pre>
  &lt;copy todir=&quot;../dest/dir&quot;&gt;
    &lt;fileset dir=&quot;src_dir&quot;&gt;
      &lt;exclude name=&quot;**/*.java&quot;/&gt;
    &lt;/fileset&gt;
  &lt;/copy&gt;

  &lt;copy todir=&quot;../dest/dir&quot;&gt;
    &lt;fileset dir=&quot;src_dir&quot; excludes=&quot;**/*.java&quot;/&gt;
  &lt;/copy&gt;
</pre>
<p><b>Copy a set of files to a directory, appending
<code>.bak</code> to the file name on the fly</b></p>
<pre>
  &lt;copy todir=&quot;../backup/dir&quot;&gt;
    &lt;fileset dir=&quot;src_dir&quot;/&gt;
    &lt;globmapper from=&quot;*&quot; to=&quot;*.bak&quot;/&gt;
  &lt;/copy&gt;
</pre>

<p><b>Copy a set of files to a directory, replacing @TITLE@ with Foo Bar
in all files.</b></p>
<pre>
  &lt;copy todir=&quot;../backup/dir&quot;&gt;
    &lt;fileset dir=&quot;src_dir&quot;/&gt;
    &lt;filterset&gt;
      &lt;filter token=&quot;TITLE&quot; value=&quot;Foo Bar&quot;/&gt;
    &lt;/filterset&gt;
  &lt;/copy&gt;
</pre>

<p><b>Collect all items from the current CLASSPATH setting into a
destination directory, flattening the directory structure.</b></p>
<pre>
  &lt;copy todir=&quot;dest&quot; flatten=&quot;true&quot;&gt;
    &lt;path&gt;
      &lt;pathelement path=&quot;/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-launcher.jar:/System/Library/Frameworks/JavaVM.framework/Versions/1.5.0/Classes/.compatibility/14compatibility.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/activation.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/ant-antunit-1.0Beta2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/antlrall.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bcel.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bsf-2.4.0.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bsh-2.0b4.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bsh-core-2.0b4.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/commons-logging-api.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/commons-logging.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/commons-net-1.4.0.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jai_codec.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jai_core.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jakarta-oro-2.0.8.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jakarta-regexp-1.2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jdepend.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/js-1.6R3.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jsch-0.1.29.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/junit-3.8.2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jython.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/log4j-1.2.9.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/mail.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/NetRexxC.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/NetRexxR.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/resolver.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/starteam-sdk.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/stylebook-1.0-b2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/weblogic.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/weblogicaux.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/weblogicclasses.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xalan.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xalan1.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xercesSamples.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xmlParserAPIs.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xsltc.jar:/Users/<USER>/dev/asf/ant-core/lib/optional:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-antlr.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-bcel.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-bsf.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-log4j.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-oro.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-regexp.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-resolver.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-commons-logging.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-commons-net.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jai.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-javamail.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jdepend.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jmf.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jsch.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-junit.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-launcher.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-netrexx.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-nodeps.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-starteam.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-stylebook.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-swing.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-testutil.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-trax.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-weblogic.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/xercesImpl.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/xml-apis.jar&quot;/&gt;
    &lt;/path&gt;
  &lt;/copy&gt;
</pre>

<p><b>Copies some resources to a given directory.</b></p>
<pre>
  &lt;copy todir=&quot;dest&quot; flatten=&quot;true&quot;&gt;
    &lt;resources&gt;
      &lt;file file=&quot;src_dir/file1.txt&quot;/&gt;
      &lt;url url=&quot;http://ant.apache.org/index.html&quot;/&gt;
    &lt;/resources&gt;
  &lt;/copy&gt;
</pre>

<p><b>Copies the two newest resources into a destination directory.</b></p>
<pre>
  &lt;copy todir=&quot;dest&quot; flatten=&quot;true&quot;&gt;
    &lt;first count=&quot;2&quot;&gt;
      &lt;sort&gt;
        &lt;date xmlns=&quot;antlib:org.apache.tools.ant.types.resources.comparators&quot;/&gt;
        &lt;resources&gt;
          &lt;file file=&quot;src_dir/file1.txt&quot;/&gt;
          &lt;file file=&quot;src_dir/file2.txt&quot;/&gt;
          &lt;file file=&quot;src_dir/file3.txt&quot;/&gt;
          &lt;url url=&quot;http://ant.apache.org/index.html&quot;/&gt;
        &lt;/resources&gt;
      &lt;/sort&gt;
    &lt;/first&gt;
  &lt;/copy&gt;
</pre>


<p><strong>Unix Note:</strong> File permissions are not retained when files
are copied; they end up with the default <code>UMASK</code> permissions
instead. This
is caused by the lack of any means to query or set file permissions in the
current Java runtimes. If you need a permission-preserving copy function,
use <code>&lt;exec executable="cp" ... &gt;</code> instead.
</p>

<p><strong>Windows Note:</strong> If you copy a file to a directory
where that file already exists, but with different casing,
the copied file takes on the case of the original. The workaround is to
<a href="delete.html">delete</a>
the file in the destination directory before you copy it.
</p>
  <p>
    <strong><a name="encoding">Important Encoding Note:</a></strong>
    The reason that binary files when filtered get corrupted is that
    filtering involves reading in the file using a Reader class. This
    has an encoding specifing how files are encoded. There are a number
    of different types of encoding - UTF-8, UTF-16, Cp1252, ISO-8859-1,
    US-ASCII and (lots) others. On Windows the default character encoding
    is Cp1252, on Unix it is usually UTF-8. For both of these encoding
    there are illegal byte sequences (more in UTF-8 than for Cp1252).
  </p>
  <p>
    How the Reader class deals with these illegal sequences is up to the
    implementation
    of the character decoder. The current Sun Java implemenation is to
    map them to legal characters. Previous Sun Java (1.3 and lower) threw
    a MalformedInputException. IBM Java 1.4 also thows this exception.
    It is the mapping of the characters that cause the corruption.
  </p>
  <p>
    On Unix, where the default is normally UTF-8, this is a <em>big</em>
    problem, as it is easy to edit a file to contain non US Ascii characters
    from ISO-8859-1, for example the Danish oe character. When this is
    copied (with filtering) by Ant, the character get converted to a
    question mark (or some such thing).
  </p>
  <p>
    There is not much that Ant can do. It cannot figure out which
    files are binary - a UTF-8 version of Korean will have lots of
    bytes with the top bit set. It is not informed about illegal
    character sequences by current Sun Java implementions.
  </p>
  <p>
    One trick for filtering containing only US-ASCII is to
    use the ISO-8859-1 encoding. This does not seem to contain
    illegal character sequences, and the lower 7 bits are US-ASCII.
    Another trick is to change the LANG environment variable from
    something like "us.utf8" to "us".
  </p>
    



</body></html>
