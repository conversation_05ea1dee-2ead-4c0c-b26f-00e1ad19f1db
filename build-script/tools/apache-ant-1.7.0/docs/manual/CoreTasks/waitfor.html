<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>WaitFor Task</title>
</head>

<body>

<h2>Waitfor</h2>
<h3>Description</h3>
<p>Blocks execution until a set of specified conditions become true. This is intended
  to be used with the <a href="parallel.html">parallel</a> task to
  synchronize a set of processes.</p>
<p>The conditions to wait for are defined in <a href="waitfor.html#nested">nested elements</a>,
if multiple conditions are specified, then the task will wait until all conditions are true..</p>
<p></p>
<p>If both maxwait and maxwaitunit are not specified, the maxwait is 3 minutes (180000 milliseconds).</p>
<p>If the <code>timeoutproperty</code> attribute has been set, a
property of that name will be created if the condition didn't come
true within the specified time.</p>
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">maxwait</td>
    <td valign="top">The maximum amount of time to wait for all the required conditions
      to become true before failing the task. Defaults to 180000 maxwaitunits.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">maxwaitunit</td>
    <td valign="top">The unit of time that must be used to interpret the value of the
    maxwait attribute.  Defaults to millisecond.
                     Valid Values are
                     <ul>
                        <li>millisecond</li>
                        <li>second</li>
                        <li>minute</li>
                        <li>hour</li>
                        <li>day</li>
                        <li>week</li>
                     </ul>
      </td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">checkevery</td>
    <td valign="top">The amount of time to wait between each test of the conditions.
      Defaults to 500 checkeveryunits.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">checkeveryunit</td>
    <td valign="top">The unit of time that must be used to interpret the value of the
    checkevery attribute.  Defaults to millisecond.
                     Valid Values are
                     <ul>
                        <li>millisecond</li>
                        <li>second</li>
                        <li>minute</li>
                        <li>hour</li>
                        <li>day</li>
                        <li>week</li>
                     </ul>
    </td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">timeoutproperty</td>
    <td valign="top">the name of the property to set if maxwait has
      been exceeded.</td>
    <td valign="top" align="center">No</td>
  </tr>
</table>
<h3><a name="nested">Nested Elements</a></h3>

<p>The available conditions that satisfy the
<code>&lt;waitfor&gt;</code> task are the same as those for the
<a href="condition.html"><code>&lt;condition&gt;</code></a> task. See
<a href="conditions.html">here</a> for the full list.</p>

<h3>Examples</h3>
<blockquote><pre>
&lt;waitfor maxwait="30" maxwaitunit="second"&gt;
        &lt;available file="errors.log"/&gt;
&lt;/waitfor&gt;
</pre></blockquote>
<p>waits up to 30 seconds for a file called errors.log to appear.</p>
<blockquote><pre>
&lt;waitfor maxwait="3" maxwaitunit="minute" checkevery="500"&gt;
        &lt;http url="http://localhost/myapp/index.html"/&gt;
&lt;/waitfor&gt;
</pre></blockquote>
<p>waits up to 3 minutes (and checks every 500 milliseconds) for a web server on localhost
  to serve up the specified URL.</p>
<blockquote><pre>
&lt;waitfor maxwait="10" maxwaitunit="second"&gt;
        &lt;and&gt;
            &lt;socket server="dbserver" port="1521"/&gt;
            &lt;http url="http://webserver/mypage.html"/&gt;
        &lt;/and&gt;
&lt;/waitfor&gt;
</pre></blockquote>
<p>waits up to 10 seconds for a server on the dbserver machine to begin listening
  on port 1521 and for the http://webserver/mypage.html web page
  to become available.</p>


</body>
</html>

