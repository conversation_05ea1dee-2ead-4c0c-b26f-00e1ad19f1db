<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>CVSPass Task</title>
</head>

<body>

<h2><a name="cvs">cvspass</a></h2>
<h3>Description</h3>
<p>Adds entries to a .cvspass file. Adding entries to this file has the same affect as a cvs login command.</p>

<p><b>CVSNT Note</b>: CVSNT prefers users to store the passwords
inside the registry.  If the task doesn't seem to work for you, the
most likely reason is that CVSNT ignores your .cvspass file
completely.  See <a
href="http://issues.apache.org/bugzilla/show_bug.cgi?id=21657#c5">bug
zilla report 21657</a> for recommended workarounds.</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">cvsroot</td>
    <td valign="top">the CVS repository to add an entry for.</td>
    <td align="center" valign="top">Yes</td>
  </tr>
  <tr>
    <td valign="top">password</td>
    <td valign="top">Password to be added to the password file.</td>
    <td align="center" valign="top">Yes</td>
  </tr>
  <tr>
    <td valign="top">passfile</td>
    <td valign="top">Password file to add the entry to.</td>
    <td align="center" valign="top">No, default is <code>~/.cvspass</code>.</td>
  </tr>
</table>
<h3>Examples</h3>
<pre>  &lt;cvspass cvsroot=&quot;:pserver:<EMAIL>:/home/<USER>
       password=&quot;anoncvs&quot;
  /&gt;</pre>
<p>Adds an entry into the ~/.cvspass password file.</p>


</body>
</html>

