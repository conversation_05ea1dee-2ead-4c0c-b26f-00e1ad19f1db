<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Dirname Task</title>
</head>

<body>

<h2><a name="echo">Dirname</a></h2>
<h3>Description</h3>
<p>
Task to determine the directory path of a specified file.
</p>
<p> 
When this task executes, it will set the specified property to the
value of the specified file (or directory) up to, but not including,
the last path element. If the specified file is a path that ends in a
filename, the filename will be dropped. If the specified file is just
a filename, the directory will be the current directory.
</p>
  <p>
    <em>Note:</em> This is not the same as the UNIX dirname command, which is
    defined as "strip non-directory suffix from filename". &lt;dirname&gt;
    determines the full directory path of the specified file.
  </p>
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">The path to take the dirname of.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">property</td>
    <td valign="top">The name of the property to set.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
</table>

<h3>Examples</h3>
<pre>  &lt;dirname property=&quot;antfile.dir&quot; file=&quot;/Users/<USER>/dev/asf/ant-core/build.xml&quot;/&gt;</pre>
will set <code>antfile.dir</code> to the directory path for
<code>/Users/<USER>/dev/asf/ant-core/build.xml</code>.
<pre>  &lt;dirname property=&quot;foo.dirname&quot; file=&quot;foo.txt&quot;/&gt;</pre>
will set <code>foo.dirname</code> to the project's basedir.</p>



</body>
</html>

