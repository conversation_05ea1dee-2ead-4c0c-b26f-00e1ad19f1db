<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Move Task</title>
</head>

<body>

<h2><a name="move">Move</a></h2>
<h3>Description</h3>
<p>Moves a file to a new file or directory, or collections of files to
a new directory.  By default, the
destination file is overwritten if it already exists.  When <var>overwrite</var> is
turned off, then files are only moved if the source file is newer than
the destination file, or when the destination file does not exist.</p>

<p><a href="../CoreTypes/resources.html#collection">Resource
Collection</a>s are used to select a group of files to move.  Only
file system based resource collections are supported, this includes <a
href="../CoreTypes/fileset.html">fileset</a>s, <a
href="../CoreTypes/filelist.html">filelist</a> and <a
href="../using.html#path">path</a>.  Prior to Ant 1.7 only
<code>&lt;fileset&gt;</code> has been supported as a nested element.
To use a resource collection, the <code>todir</code> attribute must be
set.</p>

<p><b>Since Ant 1.6.3</b>, the <i>file</i> attribute may be used to move
(rename) an entire directory.  If <i>tofile</i> denotes an existing file, or
there is a directory by the same name in <i>todir</i>, the action will fail.
</p>
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">the file or directory to move</td>
    <td valign="top" align="center">One of <var>file</var> or
     at least one nested resource collection element</td>
  </tr>
  <tr>
    <td valign="top">preservelastmodified</td>
    <td valign="top">Give the moved files the same last modified
      time as the original source files.
      (<em>Note</em>: Ignored on Java 1.1)</td>
    <td valign="top" align="center">No; defaults to false.</td>
  </tr>
  <tr>
    <td valign="top">tofile</td>
    <td valign="top">the file to move to</td>
    <td valign="top" align="center" rowspan="2">With the <var>file</var> attribute,
    either <var>tofile</var> or <var>todir</var> can be used.  With nested filesets,
    if the fileset size is greater than 1 or if the only entry in the fileset is a
    directory or if the <var>file</var> attribute is already specified, only
    <var>todir</var> is allowed</td>
  </tr>
  <tr>
    <td valign="top">todir</td>
    <td valign="top">the directory to move to</td>
  </tr>
  <tr>
    <td valign="top">overwrite</td>
    <td valign="top">overwrite existing files even if the destination
      files are newer (default is &quot;true&quot;)</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">filtering</td>
    <td valign="top">indicates whether token filtering should take place during
      the move.  See the <a href="filter.html">filter</a> task for a description of
      how filters work.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">flatten</td>
    <td valign="top">ignore directory structure of source directory,
      copy all files into a single directory, specified by the <var>todir</var>
      attribute (default is &quot;false&quot;).Note that you can achieve the
      same effect by using a <a href="../CoreTypes/mapper.html#flatten-mapper">flatten mapper</a></td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">includeEmptyDirs</td>
	 <td valign="top">Copy empty directories included with the nested FileSet(s).
	   Defaults to &quot;yes&quot;.</td>
	 <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">failonerror</td>
     <td valign="top">If false, log a warning message, but do not stop the
       build, when the file to copy does not exist or one of the nested
       filesets points to a directory that doesn't exist or an error occurs
       while moving.
     </td>
     <td valign="top" align="center">No; defaults to true.</td>
  </tr>
  <tr>
    <td valign="top">verbose</td>
     <td valign="top">Log the files that are being moved.</td>
     <td valign="top" align="center">No; defaults to false.</td>
  </tr>
  <tr>
    <td valign="top">encoding</td>
    <td valign="top">The encoding to assume when filter-copying the
    files. <em>since Ant 1.5</em>.</td>
    <td align="center">No - defaults to default JVM encoding</td>
  </tr>
  <tr>
    <td valign="top">outputencoding</td>
    <td valign="top">The encoding to use when writing the files.
    <em>since Ant 1.6</em>.</td>
    <td align="center">No - defaults to the value of the encoding
    attribute if given or the default JVM encoding otherwise.</td>
  </tr>
  <tr>
    <td valign="top">enablemultiplemapping</td>
    <td valign="top">
      If true the task will process to all the mappings for a
      given source path. If false the task will only process
      the first file or directory. This attribute is only relevant
      if there is a mapper subelement.
      <em>since Ant 1.6</em>.</td>
    <td align="center">No - defaults to false.</td>
  </tr>
  <tr>
    <td valign="top">granularity</td>
    <td valign="top">The number of milliseconds leeway to give before
    deciding a file is out of date. This is needed because not every
    file system supports tracking the last modified time to the
    millisecond level. Default is 0 milliseconds, or 2 seconds on DOS
    systems.  This can also be useful if source and target files live
    on separate machines with clocks being out of sync.  <em>since Ant
    1.6</em>.</td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>
<h4>mapper</h4>
<p>You can define file name transformations by using a nested <a
href="../CoreTypes/mapper.html">mapper</a> element. The default mapper used by
<code>&lt;move&gt;</code> is the <a
href="../CoreTypes/mapper.html#identity-mapper">identity</a>.</p>
<p>Note that the source name handed to the mapper depends on the
resource collection you use.  If you use <code>&lt;fileset&gt;</code>
or any other collection that provides a base directory, the name
passed to the mapper will be a relative filename, relative to the base
directory.  In any other case the absolute filename of the source will
be used.</p>
<h4>filterchain</h4>
<p>The Move task supports nested <a href="../CoreTypes/filterchain.html">
FilterChain</a>s.</p>

<p>
If <code>&lt;filterset&gt;</code> and <code>&lt;filterchain&gt;</code> elements are used inside the
same <code>&lt;move&gt;</code> task, all <code>&lt;filterchain&gt;</code> elements are processed first
followed by <code>&lt;filterset&gt;</code> elements.
</p>

<h3>Examples</h3>
<p><b>Move a single file (rename a file)</b></p>
<pre>
  &lt;move file=&quot;file.orig&quot; tofile=&quot;file.moved&quot;/&gt;
</pre>
<p><b>Move a single file to a directory</b></p>
<pre>
  &lt;move file=&quot;file.orig&quot; todir=&quot;dir/to/move/to&quot;/&gt;
</pre>
<p><b>Move a directory to a new directory</b></p>
<pre>
  &lt;move todir=&quot;new/dir/to/move/to&quot;&gt;
    &lt;fileset dir=&quot;src/dir&quot;/&gt;
  &lt;/move&gt;
</pre>
  <i>or, since Ant 1.6.3:</i>
<pre>
  &lt;move file=&quot;src/dir&quot; tofile=&quot;new/dir/to/move/to&quot;/&gt;
</pre>
<p><b>Move a set of files to a new directory</b></p>
<pre>
  &lt;move todir=&quot;some/new/dir&quot;&gt;
    &lt;fileset dir=&quot;my/src/dir&quot;&gt;
      &lt;include name=&quot;**/*.jar&quot;/&gt;
      &lt;exclude name=&quot;**/ant.jar&quot;/&gt;
    &lt;/fileset&gt;
  &lt;/move&gt;
</pre>
<p><b>Move a list of files to a new directory</b></p>
<pre>
  &lt;move todir=&quot;some/new/dir&quot;&gt;
    &lt;filelist dir=&quot;my/src/dir&quot;&gt;
      &lt;file name="file1.txt"/&gt;
      &lt;file name="file2.txt"/&gt;
    &lt;/filelist&gt;
  &lt;/move&gt;
</pre>
<p><b>Append <code>&quot;.bak&quot;</code> to the names of all files
in a directory.</b></p>
<pre>
  &lt;move todir=&quot;my/src/dir&quot; includeemptydirs=&quot;false&quot;&gt;
    &lt;fileset dir=&quot;my/src/dir&quot;&gt;
      &lt;exclude name=&quot;**/*.bak&quot;/&gt;
    &lt;/fileset&gt;
    &lt;mapper type=&quot;glob&quot; from=&quot;*&quot; to=&quot;*.bak&quot;/&gt;
  &lt;/move&gt;
</pre>


</body>
</html>

