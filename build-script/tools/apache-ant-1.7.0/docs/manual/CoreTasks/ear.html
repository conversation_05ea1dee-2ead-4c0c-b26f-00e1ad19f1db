<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>EAR Task</title>
</head>

<body>

<h2><a name="ear">Ear</a></h2>
<h3>Description</h3>
<p>An extension of the <a href="jar.html">Jar</a> task with special
treatment for files that should end up in an Enterprise Application archive.</p>
<p>(The Ear task is a shortcut for specifying the particular layout of a EAR file. 
The same thing can be accomplished by using the <i>prefix</i> and <i>fullpath</i>
attributes of zipfilesets in a Zip or Jar task.)</p>
<p>The extended zipfileset element from the zip task (with attributes <i>prefix</i>, <i>fullpath</i>, and <i>src</i>) is available in the Ear task.</p>

<p><b>Please note that the zip format allows multiple files of the same
fully-qualified name to exist within a single archive.  This has been
documented as causing various problems for unsuspecting users.  If you wish
to avoid this behavior you must set the <code>duplicate</code> attribute
to a value other than its default, <code>&quot;add&quot;</code>.</b></p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">destfile</td>
    <td valign="top">the EAR file to create.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">appxml</td>
    <td valign="top">The deployment descriptor to use (META-INF/application.xml).</td>
    <td valign="top" align="center">Yes, unless update is set to true</td>
  </tr>
  <tr>
    <td valign="top">basedir</td>
    <td valign="top">the directory from which to jar the files.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">compress</td>
    <td valign="top">Not only store data but also compress them,
    defaults to true.  Unless you set the <em>keepcompression</em>
    attribute to false, this will apply to the entire archive, not
    only the files you've added while updating.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">keepcompression</td>
    <td valign="top">For entries coming from existing archives (like
    nested <em>zipfileset</em>s or while updating the archive), keep
    the compression as it has been originally instead of using the
    <em>compress</em> attribute.  Defaults false.  <em>Since Ant
    1.6</em></td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">encoding</td>
    <td valign="top">The character encoding to use for filenames
      inside the archive.  Defaults to UTF8. <strong>It is not
      recommended to change this value as the created archive will most
      likely be unreadable for Java otherwise.</strong></td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">filesonly</td>
    <td valign="top">Store only file entries, defaults to false</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">includes</td>
    <td valign="top">comma- or space-separated list of patterns of files that must be
      included. All files are included when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">includesfile</td>
    <td valign="top">the name of a file. Each line of this file is
      taken to be an include pattern</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">excludes</td>
    <td valign="top">comma- or space-separated list of patterns of files that must be
      excluded. No files (except default excludes) are excluded when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">excludesfile</td>
    <td valign="top">the name of a file. Each line of this file is
      taken to be an exclude pattern</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">defaultexcludes</td>
    <td valign="top">indicates whether default excludes should be used or not
      (&quot;yes&quot;/&quot;no&quot;). Default excludes are used when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">manifest</td>
    <td valign="top">the manifest file to use.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">update</td>
    <td valign="top">indicates whether to update or overwrite
      the destination file if it already exists.  Default is &quot;false&quot;.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">duplicate</td>
    <td valign="top">behavior when a duplicate file is found.  Valid values are &quot;add&quot;, &quot;preserve&quot;, and &quot;fail&quot;.  The default value is &quot;add&quot;.  </td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">roundup</td>
    <td valign="top">Whether the file modification times will be
    rounded up to the next even number of seconds.<br>
    Zip archives store file modification times with a granularity of
    two seconds, so the times will either be rounded up or down.  If
    you round down, the archive will always seem out-of-date when you
    rerun the task, so the default is to round up.  Rounding up may
    lead to a different type of problems like JSPs inside a web
    archive that seem to be slightly more recent than precompiled
    pages, rendering precompilation useless.<br>
    Defaults to true.  <em>Since Ant 1.6.2</em></td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">level</td>
    <td valign="top">Non-default level at which file compression should be
    performed. Valid values range from 0 (no compression/fastest) to 9
    (maximum compression/slowest). <em>Since Ant 1.7</em></td>
    <td valign="top" align="center">No</td>
  </tr>
</table>
<h3>Nested elements</h3>
<h4>metainf</h4>
<p>The nested <code>metainf</code> element specifies a <a
href="../CoreTypes/fileset.html">FileSet</a>. All files included in this fileset will
end up in the <code>META-INF</code> directory of the ear file. If this
fileset includes a file named <code>MANIFEST.MF</code>, the file is
ignored and you will get a warning.</p>
<h2>Example</h2>
<pre>
    &lt;ear destfile=&quot;build/myapp.ear&quot; appxml=&quot;src/metadata/application.xml&quot;&gt;
      &lt;fileset dir=&quot;build&quot; includes=&quot;*.jar,*.war&quot;/&gt;
    &lt;/ear&gt;
</pre>


</body>
</html>


