<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>GZip/BZip2 Tasks</title>
</head>

<body>

<h2><a name="pack">GZip/BZip2</a></h2>
<h3>Description</h3>
<p>Packs a resource using the GZip or BZip2 algorithm.
The output file is only generated if it doesn't exist or the source
resource is newer.</p>
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">src</td>
    <td valign="top">the file to gzip/bzip.</td>
    <td align="center" valign="top">Yes, or a nested resource collection.</td>
  </tr>
  <tr>
    <td valign="top">destfile</td>
    <td valign="top">the destination file to create.</td>
    <td align="center" valign="top" rowspan="2">Exactly one of the two.</td>
  </tr>
  <tr>
    <td valign="top">zipfile</td>
    <td valign="top">the <i>deprecated</i> old name of destfile.</td>
  </tr>
</table>
<h4>any <a href="../CoreTypes/resources.html">resource</a> or single element
resource collection</h4>

<p>The specified resource will be used as src. <em>Since Ant 1.7</em></p>

<h3>Examples</h3>
<blockquote><pre>
&lt;gzip src=&quot;test.tar&quot; destfile=&quot;test.tar.gz&quot;/&gt;
</pre></blockquote>
<blockquote><pre>
&lt;bzip2 src=&quot;test.tar&quot; destfile=&quot;test.tar.bz2&quot;/&gt;
</pre></blockquote>
<blockquote><pre>
&lt;gzip destfile=&quot;archive.tar.gz&quot;&gt;
  &lt;url url="http://example.org/archive.tar"/&gt;
&lt;/gzip&gt;
</pre></blockquote>
<p>downloads <i>http://example.org/archive.tar</i> and compresses it
to <i>archive.tar.gz</i> in the project's basedir on the fly.</p>


</body>
</html>
