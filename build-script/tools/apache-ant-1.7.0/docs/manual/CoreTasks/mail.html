<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Mail Task</title>
</head>

<body>

<h2><a name="mail">Mail</a></h2>
<h3>Description</h3>
  <p>
    A task to send SMTP email.
  </p>
  <p>
    This task can send mail using either plain
    text, UU encoding, or MIME format mail, depending on what is available.
  </p>
  <p>
    <PERSON>TP auth and SSL/TLS require JavaMail and are only available in MIME format.
  </p>
  <p>
    Attachments may be sent using nested
    <code>&lt;attachments&gt;</code> elements, which are <a
    href="../using.html#path">path-like structures</a>.  This means
    any filesystem based <a
    href="../CoreTypes/resources.html">resource</a> or resource
    collection can be used to point to attachments.  Prior to Ant 1.7
    only <code>&lt;fileset&gt;</code> has been supported as a nested
    element, you can still use this directly without an
    <code>&lt;attachments&gt;</code> container.
  </p>
  <p>
    <strong>Note:</strong> This task may depend on external libraries
    that are not included in the Ant distribution. 
    See <a href="../install.html#librarydependencies">Library Dependencies</a>
    for more information.
  </p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">from</td>
    <td valign="top">Email address of sender.</td>
    <td align="center" valign="top">Either a <code>from</code> attribute, or a <code>&lt;from&gt;</code>
    element.</td>
  </tr>
  <tr>
    <td valign="top">replyto</td>
    <td valign="top">Replyto email address.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">tolist</td>
    <td valign="top">Comma-separated list of recipients.</td>
    <td align="center" valign="middle" rowspan="3">At least one of these, or the
       equivalent elements.</td>
  </tr>
  <tr>
    <td valign="top">cclist</td>
    <td valign="top">Comma-separated list of recipients to carbon copy</td>
    </tr>
  <tr>
    <td valign="top">bcclist</td>
    <td valign="top">Comma-separated list of recipients to blind carbon copy
    </td>
  </tr>
  <tr>
    <td valign="top">message</td>
    <td valign="top">Message to send in the body of the email.</td>
    <td align="center" valign="middle" rowspan="2">One of these or a
    <code>&lt;message&gt;</code> element.</td>
  </tr>
  <tr>
    <td valign="top">messagefile</td>
    <td valign="top">File to send as the body of the email. Property
    values in the file will be expanded.</td>
  </tr>
      <td valign="top">messagemimetype</td>
    <td valign="top">The content type of the message.  The default is
    <code>text/plain</code>.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">files</td>
    <td valign="top">Files to send as attachments to the email.  Separate multiple
    file names using a comma or space.  You can also use <code>&lt;fileset&gt;</code>
    elements to specify files.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">failonerror</td>
    <td valign="top">flag to indicate whether to halt the build on
    any error.  The default value is <code>true</code>.</td>
    <td align="center" valign="top">No.</td>
  </tr>
  <tr>
    <td valign="top">includefilenames</td>
    <td valign="top">Include filename(s) before file contents.
    Valid only when the <code>plain</code> encoding is used.  The default
    value is <code>false</code>.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">mailhost</td>
    <td valign="top">Host name of the SMTP server.  The default value is
    <code>localhost</code>.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">mailport</td>
    <td valign="top">TCP port of the SMTP server.  The default value is 25.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">Employee</td>
    <td valign="top">Employee name for SMTP auth</td>
    <td valign="center">Yes, if SMTP auth is required on your SMTP server<br></br>
    the email message will be then sent using Mime and requires JavaMail</td>
  </tr>
  <tr>
    <td valign="top">password</td>
    <td valign="top">password for SMTP auth</td>
    <td valign="center">Yes, if SMTP auth is required on your SMTP server<br></br>
    the email message will be then sent using Mime and requires JavaMail</td>
  </tr>
  <tr>
    <td valign="top">ssl</td>
    <td valign="top">"true", "on" or "yes" accepted here<br></br>
    indicates whether you need TLS/SSL</td>
    <td valign="center">No</td>
  </tr>
  <tr>
    <td valign="top">encoding</td>
    <td valign="top">Specifies the encoding to use for the content of the email.
    Values are <code>mime</code>, <code>uu</code>, <code>plain</code>, or
      <code>auto</code>.  The default value is <code>auto</code>.
      <code>uu</code> or <code>plain</code> are not compatible with SMTP auth</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">charset</td>
    <td valign="top">Character set of the email.<br>
    You can also set the charset in the message nested element.<br>
    These options are mutually exclusive.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">subject</td>
    <td valign="top">Email subject line.</td>
    <td align="center" valign="top">No</td>
  </tr>
</table>

<h3>Note regarding the attributes containing email addresses</h3>
Since Ant 1.6, the attributes from, replyto, tolist, cclist, bcclist
can contain email addresses of the form :
<ul>
<li><EMAIL></li>
<li>name &lt;<EMAIL>&gt;</li>
<li>&lt;<EMAIL>&gt; name</li>
<li>(name) <EMAIL></li>
<li><EMAIL> (name)</li>
</ul>
<p>You need to enter the angle brackets as XML entities
<code>&amp;gt;</code> and <code>&amp;lt;</code>.</p>

<h3>Parameters specified as nested elements</h3>

<h4>to / cc / bcc / from/ replyto </h4>
<p>Adds an email address element.  It takes the following attributes:</p>

<table width="60%" border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">name</td>
    <td valign="top">The display name for the address.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">address</td>
    <td valign="top">The email address.</td>
    <td align="center" valign="top">Yes</td>
  </tr>
</table>

<h4>message</h4>

<p>Specifies the message to include in the email body.  It takes the following
attributes:</p>

<table width="60%" border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">src</td>
    <td valign="top">The file to use as the message.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">mimetype</td>
    <td valign="top">The content type to use for the message.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">charset</td>
    <td valign="top">Character set of the message<br>
    You can also set the charset as attribute of the enclosing mail task.<br>
    These options are mutually exclusive.</td>
    <td align="center" valign="top">No</td>
  </tr>
</table>

<p>If the <code>src</code> attribute is not specified, then text can be added
inside the <code>&lt;message&gt;</code> element. Property expansion will occur
in the message, whether it is specified as an external file or as text within
the <code>&lt;message&gt;</code> element.</p>

<h4>header</h4>
<p><strong>Since Ant 1.7</strong>, arbitrary mail headers can be added by
  specifying these attributes on one or more nested header elements:</p>

<table width="60%" border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">name</td>
    <td valign="top">The name associated with this mail header.</td>
    <td align="center" valign="top">Yes</td>
  </tr>
  <tr>
    <td valign="top">value</td>
    <td valign="top">The value to assign to this mail header.</td>
    <td align="center" valign="top">Yes</td>
  </tr>
</table>

<p>It is permissible to duplicate the name attribute amongst multiple headers.
</p>

<h3>Examples</h3>

<blockquote><pre>
&lt;mail from=&quot;me&quot;
      tolist=&quot;you&quot;
      subject=&quot;Results of nightly build&quot;
      files=&quot;build.log&quot;/&gt;
</pre></blockquote>

<p>Sends an email from <i>me</i> to <i>you</i> with a subject of
<i>Results of nightly build</i> and includes the contents of the file
<i>build.log</i> in the body of the message.</p>

<blockquote><pre>
&lt;mail mailhost=&quot;smtp.myisp.com&quot; mailport=&quot;1025&quot; subject=&quot;Test build&quot;&gt;
  &lt;from address=&quot;<EMAIL>&quot;/&gt;
  &lt;replyto address=&quot;<EMAIL>&quot;/&gt;
  &lt;to address=&quot;<EMAIL>&quot;/&gt;
  &lt;message&gt;The ${buildname} nightly build has completed&lt;/message&gt;
  &lt;attachments&gt;
    &lt;fileset dir=&quot;dist&quot;&gt;
      &lt;include name=&quot;**/*.zip&quot;/&gt;
    &lt;/fileset&gt;
  &lt;/attachments&gt;
&lt;/mail&gt;
</pre></blockquote>

<p>Sends an eMail from <i><EMAIL></i> to <i><EMAIL></i> with a subject of
<i>Test Build</i>. Replies to this email will go to <i><EMAIL></i>.
Any zip files from the dist directory are attached.&nbsp; The
task will attempt to use JavaMail and fall back to UU encoding or no encoding in
that order depending on what support classes are available. <code>${buildname}</code>
will be replaced with the <code>buildname</code> property's value.</p>

<blockquote><pre>
&lt;property name=&quot;line2&quot; value=&quot;some_international_message&quot;/&gt;
&lt;echo message=&quot;${line2}&quot;/&gt;

&lt;mail mailhost=&quot;<EMAIL>&quot; mailport=&quot;25&quot; subject=&quot;Test build&quot;  charset=&quot;utf-8&quot;&gt;
  &lt;from address=&quot;<EMAIL>&quot;/&gt;
  &lt;to address=&quot;<EMAIL>&quot;/&gt;
  &lt;message&gt;some international text:${line2}&lt;/message&gt;
&lt;/mail&gt;
</pre></blockquote>

<p>Sends an eMail from <i><EMAIL></i> to <i><EMAIL></i> with a subject of
<i>Test Build</i>, the message body being coded in UTF-8



</body>
</html>
