<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

    <head>
      <meta http-equiv="Content-Language" content="en-us">
      <link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Concat</title>
    </head>

    <body>

      <h2><a name="Concat">Concat</a></h2>

      <h3>Description</h3>

      <p>
        Concatenates one or more 
        <a href="../CoreTypes/resources.html">resource</a>s
        to a single file or to the console. The destination
        file will be created if it does not exist.
      </p>

      <p>
        <a href="../CoreTypes/resources.html#collection">
Resource Collection</a>s are used to
        select which resources are to be concatenated. There is no
        singular attribute to specify a single resource to cat.
      </p>

      <h3>Parameters</h3>

      <table border="1" cellpadding="2" cellspacing="0">

        <tr>
          <td valign="top"><b>Attribute</b></td>
          <td valign="top"><b>Description</b></td>
          <td align="center" valign="top"><b>Required</b></td>
        </tr>

        <tr>
          <td valign="top">destfile</td>
          <td valign="top">
            The destination file for the concatenated stream.
        If not specified the console will be used instead.
          </td>
          <td valign="top" align="center">
            No
          </td>
        </tr>

        <tr>
          <td valign="top">append</td>
          <td valign="top">
            Specifies whether or not the file specified by 'destfile'
            should be appended. Defaults to &quot;no&quot;.
          </td>
          <td valign="top" align="center">No</td>
        </tr>
        <tr>
          <td valign="top">force</td>
          <td valign="top">
            Specifies whether or not the file specified by 'destfile'
            should be written to even if it is newer than all source files.
            <em>since Ant 1.6</em>.
            Defaults to &quot;yes&quot;.
          </td>
          <td valign="top" align="center">No</td>
        </tr>

        <tr>
          <td valign="top">encoding</td>
          <td valign="top">
            Specifies the encoding for the input files. Please see <a
            href="http://java.sun.com/j2se/1.5.0/docs/guide/intl/encoding.doc.html">
            http://java.sun.com/j2se/1.5.0/docs/guide/intl/encoding.doc.html</a>
            for a list of possible values. Defaults to the platform's
            default character encoding.
          </td>
          <td valign="top" align="center">No</td>
        </tr>
        <tr>
          <td valign="top">outputencoding</td>
          <td valign="top">
            The encoding to use when writing the output file
            <em>since Ant 1.6</em>.
            Defaults to the value of the encoding attribute
            if given or the default JVM encoding otherwise.
          </td>
          <td valign="top" align="center">No</td>
        </tr>
        <tr>
          <td valign="top">fixlastline</td>
          <td valign="top">
                 Specifies whether or not to check if
            each file concatenated is terminated by
            a new line. If this attribute is &quot;yes&quot;
            a new line will be appended to the stream if
            the file did not end in a new line.
            <em>since Ant 1.6</em>.
            Defaults to &quot;no&quot;.
            This attribute does not apply to embedded text.
          </td>
          <td valign="top" align="center">No</td>
        </tr>
        <tr>
          <td valign="top">eol</td>
          <td valign="top">
            Specifies what the end of line character are
            for use by the fixlastline attribute.
            <em>since Ant 1.6</em>
            Valid values for this property are:
            <ul>
              <li>cr: a single CR</li>
              <li>lf: a single LF</li>
              <li>crlf: the pair CRLF</li>
              <li>mac: a single CR</li>
              <li>unix: a single LF</li>
              <li>dos: the pair CRLF</li>
            </ul>
            The default is platform dependent.
            For Unix platforms, the default is &quot;lf&quot;.
            For DOS based systems (including Windows),
            the default is &quot;crlf&quot;.
            For Mac OS, the default is &quot;cr&quot;.
          </td>
          <td valign="top" align="center">No</td>
        </tr>
        <tr>
          <td valign="top">binary</td>
          <td valign="top">
            <em>since Ant 1.6.2</em>
            If this attribute is set to true, the task concatenates the files
            in a byte by byte fashion. If this attribute is false, concat will
            not normally work for binary files due to character encoding
            issues.
            If this option is set to true, the destfile attribute must be
            set, and the task cannot used nested text.
            Also the attributes encoding, outputencoding, filelastline
            cannot be used.
            The default is false.
          </td>
          <td valign="top" align="center">No</td>
        </tr>

      </table>

      <h3>Parameters specified as nested elements</h3>
        <h4>Resource Collection</h4>
        <p><em>since Ant 1.7</em>.</p>

      <p>
        Any of the various <a href="../CoreTypes/resources.html#collection">
        Resource Collection</a> types can specify the resources to be
        concatenated.
      </p>

      <h4>filterchain</h4>
        <p><em>since Ant 1.6</em>.</p>
      <p>The concat task supports nested
        <a href="../CoreTypes/filterchain.html"> FilterChain</a>s.</p>

      <h4>header, footer</h4>
        <p><em>since Ant 1.6</em>.</p>
      <p>Used to prepend or postpend text into the concatenated stream.</p>
      <p>The text may be in-line or be in a file.</p>
      <table border="1" cellpadding="2" cellspacing="0">
        <tr>
          <td valign="top"><b>Attribute</b></td>
          <td valign="top"><b>Description</b></td>
          <td align="center" valign="top"><b>Required</b></td>
        </tr>
        <tr>
          <td valign="top">filtering</td>
          <td valign="top">
            Whether to filter the text provided by this sub element,
            default is "yes".
          <td valign="top" align = "center">No</td>
        </tr>
        <tr>
          <td valign="top">file</td>
          <td valign="top">A file to place at the head or tail of the
                           concatenated text.
          <td valign="top" align = "center">No</td>
        </tr>
        <tr>
          <td valign="top">trim</td>
          <td valign="top">Whether to trim the value, default is "no"</td>
          <td valign="top" align = "center">No</td>
        </tr>
        <tr>
          <td valign="top">trimleading</td>
          <td valign="top">
            Whether to trim leading white space on each line, default is "no"
          </td>
          <td valign="top" align = "center">No</td>
        </tr>
      </table>

      <h3>Examples</h3>

      <p><b>Concatenate a string to a file:</b></p>

      <pre>
  &lt;concat destfile=&quot;README&quot;&gt;Hello, World!&lt;/concat&gt;
      </pre>

      <p><b>Concatenate a series of files to the console:</b></p>

      <pre>
  &lt;concat&gt;
    &lt;fileset dir=&quot;messages&quot; includes=&quot;*important*&quot;/&gt;
  &lt;/concat&gt;
      </pre>

      <p><b>Concatenate a single file, appending if the destination file exists:</b></p>

      <pre>
  &lt;concat destfile=&quot;NOTES&quot; append=&quot;true&quot;&gt;
    &lt;filelist dir=&quot;notes&quot; files=&quot;note.txt&quot;/&gt;
  &lt;/concat&gt;
      </pre>

      <p><b>Concatenate a series of files, update the destination
      file only if is older that all the source files:</b></p>

      <pre>
  &lt;concat destfile=&quot;${docbook.dir}/all-sections.xml&quot;
          force=&quot;no&quot;&gt;
    &lt;filelist dir=&quot;${docbook.dir}/sections&quot;
         files=&quot;introduction.xml,overview.xml&quot;/&gt;
    &lt;fileset dir=&quot;${docbook.dir}&quot;
         includes=&quot;sections/*.xml&quot;
         excludes=&quot;introduction.xml,overview.xml&quot;/&gt;
  &lt;/concat&gt;
      </pre>

      <p><b>Concatenate a series of files, expanding ant properties</b></p>
        <pre>
   &lt;concat destfile="build/subs"&gt;
      &lt;path&gt;
        &lt;fileset dir="src" includes="*.xml"/&gt;
        &lt;pathelement location="build.xml"/&gt;
      &lt;/path&gt;
      &lt;filterchain&gt;
        &lt;expandproperties/&gt;
      &lt;/filterchain&gt;
   &lt;/concat&gt;
        </pre>

      <p><b>Filter the lines containing project from build.xml and output
           them to report.output, prepending with a header</b></p>
        <pre>
   &lt;concat destfile="build/report.output"&gt;
      &lt;header filtering="no" trimleading="yes"&gt;
          Lines that contain project
          ==========================
      &lt;/header&gt;
      &lt;path path="build.xml"/&gt;
      &lt;filterchain&gt;
         &lt;linecontains&gt;
           &lt;contains value="project"/&gt;
         &lt;/linecontains&gt;
      &lt;/filterchain&gt;
   &lt;/concat&gt;
        </pre>

      <p><b>Concatenate a number of binary files.</b></p>
        <pre>
   &lt;concat destfile="build/dist.bin" binary="yes"&gt;
     &lt;fileset file="src/scripts/dist.sh" /&gt;
     &lt;fileset file="build/dist.tar.bz2" /&gt;
   &lt;/concat&gt;
        </pre>

      

    </body>

  </html>
