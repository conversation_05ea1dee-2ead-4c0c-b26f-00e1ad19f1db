<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>WAR Task</title>
</head>

<body>

<h2><a name="war">War</a></h2>
<h3>Description</h3>
<p>An extension of the <a href="jar.html">Jar</a> task with special
treatment for files that should end up in the
<code>WEB-INF/lib</code>, <code>WEB-INF/classes</code> or
<code>WEB-INF</code> directories of the Web Application Archive.</p>
<p>(The War task is a shortcut for specifying the particular layout of a WAR file.
The same thing can be accomplished by using the <i>prefix</i> and <i>fullpath</i>
attributes of zipfilesets in a Zip or Jar task.)</p>
<p>The extended zipfileset element from the zip task
    (with attributes <i>prefix</i>, <i>fullpath</i>, and <i>src</i>)
    is available in the War task. The task is also resource-enabled
    and will add nested resources and resource collections to the archive.</p>

<p>
    Before Servlet API 2.5/Java EE 5, a WEB-INF/web.xml file was mandatory in a
    WAR file, so this task failed if the <code>webxml</code> attribute was missing.
    As the web.xml file is now optional, the <code>webxml</code> attribute may now
    be made optional. However, as most real web applications do need a web.xml file,
    it is not optional by default. The task will fail if the file is not
    included, unless the <code>needxmlfile</code> attribute
    is set to <code>true</code>. The task
    will warn if more than one web.xml file is added to the JAR  
    through the filesets.
</p>


<p><b>Please note that the Zip format allows multiple files of the same
fully-qualified name to exist within a single archive.  This has been
documented as causing various problems for unsuspecting users.  If you wish
to avoid this behavior you must set the <code>duplicate</code> attribute
to a value other than its default, <code>&quot;add&quot;</code>.</b></p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">destfile</td>
    <td valign="top">the WAR file to create.</td>
    <td align="center" valign="top" rowspan="2">Exactly one of the two.</td>
  </tr>
  <tr>
    <td valign="top">warfile</td>
    <td valign="top"><i>Deprecated</i> name of the file to create
    -use <tt>destfile</tt> instead.</td>
  </tr>
  <tr>
    <td valign="top">webxml</td>
    <td valign="top">The servlet configuration descriptor to use (WEB-INF/web.xml).</td>
    <td valign="top" align="center">Yes, unless <tt>needxmlfile</tt> is true,
    the file is pulled in via a nested fileset, or an existing WAR file is
    being updated.</td>
  </tr>
  <tr>
    <td valign="top">needxmlfile</td>
    <td valign="top">Flag to indicate whether or not the web.xml file is needed.
        I=it should be set to false when generating
        servlet 2.5+ WAR files without a web.xml file.
        <em>Since Ant 1.7</em></td>
    <td valign="top" align="center">No -default "true"</td>
  </tr>
  <tr>
    <td valign="top">basedir</td>
    <td valign="top">the directory from which to jar the files.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">compress</td>
    <td valign="top">Not only store data but also compress them,
    defaults to true.  Unless you set the <em>keepcompression</em>
    attribute to false, this will apply to the entire archive, not
    only the files you've added while updating.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">keepcompression</td>
    <td valign="top">For entries coming from existing archives (like
    nested <em>zipfileset</em>s or while updating the archive), keep
    the compression as it has been originally instead of using the
    <em>compress</em> attribute.  Defaults false.  <em>Since Ant
    1.6</em></td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">encoding</td>
    <td valign="top">The character encoding to use for filenames
      inside the archive.  Defaults to UTF8. <strong>It is not
      recommended to change this value as the created archive will most
      likely be unreadable for Java otherwise.</strong></td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">filesonly</td>
    <td valign="top">Store only file entries, defaults to false</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">includes</td>
    <td valign="top">comma- or space-separated list of patterns of files that must be
      included. All files are included when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">includesfile</td>
    <td valign="top">the name of a file. Each line of this file is
      taken to be an include pattern</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">excludes</td>
    <td valign="top">comma- or space-separated list of patterns of files that must be
      excluded. No files (except default excludes) are excluded when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">excludesfile</td>
    <td valign="top">the name of a file. Each line of this file is
      taken to be an exclude pattern</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">defaultexcludes</td>
    <td valign="top">indicates whether default excludes should be used or not
      (&quot;yes&quot;/&quot;no&quot;). Default excludes are used when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">manifest</td>
    <td valign="top">the manifest file to use.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">update</td>
    <td valign="top">indicates whether to update or overwrite
      the destination file if it already exists.  Default is &quot;false&quot;.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">duplicate</td>
    <td valign="top">behavior when a duplicate file is found.  Valid values are &quot;add&quot;, &quot;preserve&quot;, and &quot;fail&quot;.  The default value is &quot;add&quot;.  </td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">roundup</td>
    <td valign="top">Whether the file modification times will be
    rounded up to the next even number of seconds.<br>
    Zip archives store file modification times with a granularity of
    two seconds, so the times will either be rounded up or down.  If
    you round down, the archive will always seem out-of-date when you
    rerun the task, so the default is to round up.  Rounding up may
    lead to a different type of problems like JSPs inside a web
    archive that seem to be slightly more recent than precompiled
    pages, rendering precompilation useless.<br>
    Defaults to true.  <em>Since Ant 1.6.2</em></td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">level</td>
    <td valign="top">Non-default level at which file compression should be
    performed. Valid values range from 0 (no compression/fastest) to 9
    (maximum compression/slowest). <em>Since Ant 1.7</em></td>
    <td valign="top" align="center">No</td>
  </tr>
</table>
<h3>Nested elements</h3>
<h4>lib</h4>
<p>The nested <code>lib</code> element specifies a <a
href="../CoreTypes/fileset.html">FileSet</a>. All files included in this fileset will
end up in the <code>WEB-INF/lib</code> directory of the war file.</p>
<h4>classes</h4>
<p>The nested <code>classes</code> element specifies a <a
href="../CoreTypes/fileset.html">FileSet</a>. All files included in this fileset will
end up in the <code>WEB-INF/classes</code> directory of the war file.</p>
<h4>webinf</h4>
<p>The nested <code>webinf</code> element specifies a <a
href="../CoreTypes/fileset.html">FileSet</a>. All files included in this fileset will
end up in the <code>WEB-INF</code> directory of the war file. If this
fileset includes a file named <code>web.xml</code>, the file is
ignored and you will get a warning.</p>
<h4>metainf</h4>
<p>The nested <code>metainf</code> element specifies a <a
href="../CoreTypes/fileset.html">FileSet</a>. All files included in this fileset will
end up in the <code>META-INF</code> directory of the war file. If this
fileset includes a file named <code>MANIFEST.MF</code>, the file is
ignored and you will get a warning.</p>
<h3>Examples</h3>
<p>Assume the following structure in the project's base directory:</p>
<pre>
thirdparty/libs/jdbc1.jar
thirdparty/libs/jdbc2.jar
build/main/com/myco/myapp/Servlet.class
src/metadata/myapp.xml
src/html/myapp/index.html
src/jsp/myapp/front.jsp
src/graphics/images/gifs/small/logo.gif
src/graphics/images/gifs/large/logo.gif
</pre>
then the war file <code>myapp.war</code> created with
<pre>
&lt;war destfile=&quot;myapp.war&quot; webxml=&quot;src/metadata/myapp.xml&quot;&gt;
  &lt;fileset dir=&quot;src/html/myapp&quot;/&gt;
  &lt;fileset dir=&quot;src/jsp/myapp&quot;/&gt;
  &lt;lib dir=&quot;thirdparty/libs&quot;&gt;
    &lt;exclude name=&quot;jdbc1.jar&quot;/&gt;
  &lt;/lib&gt;
  &lt;classes dir=&quot;build/main&quot;/&gt;
  &lt;zipfileset dir=&quot;src/graphics/images/gifs&quot;
              prefix=&quot;images&quot;/&gt;
&lt;/war&gt;
</pre>
will consist of
<pre>
WEB-INF/web.xml
WEB-INF/lib/jdbc2.jar
WEB-INF/classes/com/myco/myapp/Servlet.class
META-INF/MANIFEST.MF
index.html
front.jsp
images/small/logo.gif
images/large/logo.gif
</pre>
using Ant's default manifest file. The content of
<code>WEB-INF/web.xml</code> is identical to
<code>src/metadata/myapp.xml</code>.

<p>We regulary receive bug reports that this task is creating the WEB-INF
directory, and thus it is our fault your webapp doesn't work. The cause
of these complaints lies in WinZip, which turns an all upper-case
directory into an all lower case one in a fit of helpfulness. Please check that
<code>jar xvf yourwebapp.war</code> shows the same behaviour before filing another
report.<br/>
Winzip has an option allowing all uppercase names (which is off by default!).  It can be enabled by:
Menu "Options" -> "Configuration",  "View" property/tab page, then "General" group box has an option called "Allow all uppercase file names".
</p>



</body>
</html>
