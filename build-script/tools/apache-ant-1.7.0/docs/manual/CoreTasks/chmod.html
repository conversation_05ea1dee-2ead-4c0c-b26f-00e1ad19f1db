<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Chmod Task</title>
</head>

<body>

<h2><a name="chmod">Chmod</a></h2>
<h3>Description</h3>
<p>Changes the permissions of a file or all files inside specified
directories. Right now it has effect only under Unix or NonStop Kernel (Tandem).
The permissions are also UNIX style, like the argument for the chmod command.</p>
<p>See the section on <a href="../dirtasks.html#directorybasedtasks">directory based
tasks</a>, on how the inclusion/exclusion of files works, and how to
write patterns.</p>

<p>This task holds an implicit <a
href="../CoreTypes/fileset.html">FileSet</a> and supports all of
FileSet's attributes and nested elements directly. More sets can be
specified using nested <code>&lt;fileset&gt;</code> or
<code>&lt;dirset&gt;</code> (<em>since Ant 1.6</em>) elements. </p>

<p>Starting with Ant 1.6, this task also supports nested <a
href="../CoreTypes/filelist.html">filelist</a>s.</p>

<p>Starting with Ant 1.7, this task supports arbitrary <a
href="../CoreTypes/resources.html#collection">Resource Collection</a>s
as nested elements.</p>

<p>By default this task will use a single invocation of the underlying
chmod command.  If you are working on a large number of files this may
result in a command line that is too long for your operating system.
If you encounter such problems, you should set the maxparallel
attribute of this task to a non-zero value.  The number to use highly
depends on the length of your file names (the depth of your directory
tree) and your operating system, so you'll have to experiment a
little.  POSIX recommends command line length limits of at least 4096
characters, this may give you an approximation for the number you
could use as initial value for these experiments.</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">the file or single directory of which the permissions 
      must be changed.</td>
    <td valign="top" valign="middle" rowspan="2">exactly one of the two or nested <code>&lt;fileset/list&gt;</code> elements.</td>
  </tr>
  <tr>
    <td valign="top">dir</td>
    <td valign="top">the directory which holds the files whose permissions 
      must be changed.</td>
  </tr>
  <tr>
    <td valign="top">perm</td>
    <td valign="top">the new permissions.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">includes</td>
    <td valign="top">comma- or space-separated list of patterns of files that must be
      included.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">excludes</td>
    <td valign="top">comma- or space-separated list of patterns of files that must be
      excluded. No files (except default excludes) are excluded when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">defaultexcludes</td>
    <td valign="top">indicates whether default excludes should be used or not
      (&quot;yes&quot;/&quot;no&quot;). Default excludes are used when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">parallel</td>
    <td valign="top">process all specified files using a single
      <code>chmod</code> command. Defaults to true.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">type</td>
    <td valign="top">One of <i>file</i>, <i>dir</i> or
      <i>both</i>. If set to <i>file</i>, only the permissions of
      plain files are going to be changed. If set to <i>dir</i>, only
      the directories are considered.<br>
      <strong>Note:</strong> The type attribute does not apply to
      nested <i>dirset</i>s - <i>dirset</i>s always implicitly
      assume type to be <i>dir</i>.</td>
    <td align="center" valign="top">No, default is <i>file</i></td>
  </tr>
  <tr>
    <td valign="top">maxparallel</td>
    <td valign="top">Limit the amount of parallelism by passing at
      most this many sourcefiles at once.  Set it to &lt;= 0 for
      unlimited.  Defaults to unlimited.  <em>Since Ant 1.6.</em></td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">verbose</td>
    <td valign="top">Whether to print a summary after execution or not.
      Defaults to <code>false</code>.  <em>Since Ant 1.6.</em></td>
    <td align="center" valign="top">No</td>
  </tr>
</table>
<h3>Examples</h3>
  <blockquote><pre>
&lt;chmod file=&quot;${dist}/start.sh&quot; perm=&quot;ugo+rx&quot;/&gt;
</pre></blockquote>
<p>makes the &quot;start.sh&quot; file readable and executable for anyone on a
UNIX system.</p>
<blockquote><pre>
&lt;chmod file=&quot;${dist}/start.sh&quot; perm=&quot;700&quot;/&gt;
</pre></blockquote>
<p>makes the &quot;start.sh&quot; file readable, writable and executable only for the owner on a
UNIX system.</p>
<blockquote>
<pre>
&lt;chmod dir=&quot;${dist}/bin&quot; perm=&quot;ugo+rx&quot; 
       includes=&quot;**/*.sh&quot;/&gt;
</pre>
</blockquote>
<p>makes all &quot;.sh&quot; files below <code>${dist}/bin</code>
readable and executable for anyone on a UNIX system.</p>
<blockquote>
<pre>
&lt;chmod perm=&quot;g+w&quot;&gt;
  &lt;fileset dir=&quot;shared/sources1&quot;&gt;
    &lt;exclude name=&quot;**/trial/**&quot;/&gt;
  &lt;/fileset&gt;
  &lt;fileset refid=&quot;other.shared.sources&quot;/&gt;
&lt;/chmod&gt;
</pre>
</blockquote>
<p>makes all files below <code>shared/sources1</code> (except those
below any directory named trial) writable for members of the same
group on a UNIX system. In addition all files belonging to a FileSet
with <code>id</code> <code>other.shared.sources</code> get the same
permissions.</p>

<blockquote>
<pre>
&lt;chmod perm=&quot;go-rwx&quot; type=&quot;file&quot;&gt;
  &lt;fileset dir=&quot;/web&quot;&gt;
    &lt;include name=&quot;**/*.cgi&quot;/&gt;
    &lt;include name=&quot;**/*.old&quot;/&gt;
  &lt;/fileset&gt;
  &lt;dirset dir=&quot;/web&quot;&gt;
    &lt;include name=&quot;**/private_*&quot;/&gt;
  &lt;/dirset&gt;
&lt;/chmod&gt;
</pre>
</blockquote>

<p>keeps non-owners from touching cgi scripts, files with a <code>.old</code>
extension or directories beginning with <code>private_</code>. A directory 
ending in <code>.old</code> or a file beginning with private_ would remain
unaffected.</p>



</body>
</html>

