<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Property Task</title>
</head>

<body>

<h2><a name="property">Property</a></h2>
<h3>Description</h3>
<p>Sets a <a href="../using.html#properties">property</a>
(by name and value), or set of properties (from file or
resource) in the project.  Properties are case sensitive.</p>
 Properties are immutable: whoever sets a property first freezes it for the
 rest of the build; they are most definitely not variables.
<p>There are six ways to set properties:</p>
<ul>
  <li>By supplying both the <i>name</i> and <i>value</i> attribute.</li>
  <li>By supplying both the <i>name</i> and <i>refid</i> attribute.</li>
  <li>By setting the <i>file</i> attribute with the filename of the property
    file to load. This property file has the format as defined by the file used
    in the class java.util.Properties, with the same rules about how
    non-ISO8859-1 characters must be escaped.</li>
  <li>By setting the <i>url</i> attribute with the url from which to load the
    properties. This url must be directed to a file that has the format as defined
    by the file used in the class java.util.Properties.</li>
  <li>By setting the <i>resource</i> attribute with the resource name of the
    property file to load. A resource is a property file on the current
    classpath, or on the specified classpath.</li>
  <li>By setting the <i>environment</i> attribute with a prefix to use.
    Properties will be defined for every environment variable by
    prefixing the supplied name and a period to the name of the variable.</li>
</ul>
<p>Although combinations of these ways are possible, only one should be used
at a time. Problems might occur with the order in which properties are set, for
instance.</p>
<p>The value part of the properties being set, might contain references to other
properties. These references are resolved at the time these properties are set.
This also holds for properties loaded from a property file.</p>
<p>A list of predefined properties can be found <a
href="../using.html#built-in-props">here</a>.</p>

<h4>OpenVMS Users</h4>
<p>With the <code>environment</code> attribute this task will load all defined
logicals on an OpenVMS system.  Logicals with multiple equivalence names get
mapped to a property whose value is a comma separated list of all equivalence
names.  If a logical is defined in multiple tables, only the most local
definition is available (the table priority order being PROCESS, JOB, GROUP,
SYSTEM).
</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">name</td>
    <td valign="top">the name of the property to set.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">value</td>
    <td valign="top">the value of the property.</td>
    <td valign="middle" align="center" rowspan="3">One of these, when using the
       name attribute</td>
  </tr>
  <tr>
    <td valign="top">location</td>
    <td valign="top">Sets the property to the absolute filename of the
      given file. If the value of this attribute is an absolute path, it
      is left unchanged (with / and \ characters converted to the
      current platforms conventions). Otherwise it is taken as a path
      relative to the project's basedir and expanded.</td>
  </tr>
  <tr>
    <td valign="top">refid</td>
    <td valign="top"><a href="../using.html#references">Reference</a> to an object
      defined elsewhere. Only yields reasonable results for references
      to <a href="../using.html#path">PATH like structures</a> or properties.</td>
  </tr>
  <tr>
    <td valign="top">resource</td>
    <td valign="top">the resource name of the property file.</td>
    <td valign="middle" align="center" rowspan="4">One of these, when
      <b>not</b> using the name attribute</td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">the filename of the property file .</td>
  </tr>
  <tr>
    <td valign="top">url</td>
    <td valign="top">the url from which to read properties.</td>
  </tr>
  <tr>
    <td valign="top">environment</td>
    <td valign="top">the prefix to use when retrieving environment variables. Thus
    if you specify environment=&quot;myenv&quot; you will be able to access OS-specific
    environment variables via property names &quot;myenv.PATH&quot; or
    &quot;myenv.TERM&quot;. Note that if you supply a property name with a final
    &quot;.&quot; it will not be doubled. ie environment=&quot;myenv.&quot; will still
    allow access of environment variables through &quot;myenv.PATH&quot; and
    &quot;myenv.TERM&quot;. This functionality is currently only implemented
    on select platforms. Feel free to send patches to increase the number of platforms
    this functionality is supported on ;).<br>
    Note also that properties are case sensitive, even if the
    environment variables on your operating system are not, e.g. it
    will be ${env.Path} not /Users/<USER>/opt/apache-tomcat-6.0.2/bin:/Users/<USER>/opt/maven-2.0.4/bin:/Users/<USER>/opt/ant/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin on Windows 2000.</td>

  </tr>
  <tr>
    <td valign="top">classpath</td>
    <td valign="top">the classpath to use when looking up a resource.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">classpathref</td>
    <td valign="top">the classpath to use when looking up a resource,
      given as <a href="../using.html#references">reference</a> to a <code>&lt;path&gt;</code> defined
      elsewhere..</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">prefix</td>
    <td valign="top">Prefix to apply to properties loaded using <code>file</code>
    or <code>resource</code>. A "." is appended to the prefix if not specified.</td>
    <td align="center" valign="top">No</td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>
<h4>classpath</h4>
<p><code>Property</code>'s <i>classpath</i> attribute is a <a
href="../using.html#path">PATH like structure</a> and can also be set via a nested
<i>classpath</i> element.</p>
<h3>Examples</h3>
<pre>  &lt;property name=&quot;foo.dist&quot; value=&quot;dist&quot;/&gt;</pre>
<p>sets the property <code>foo.dist</code> to the value &quot;dist&quot;.</p>
<pre>  &lt;property file=&quot;foo.properties&quot;/&gt;</pre>
<p>reads a set of properties from a file called &quot;foo.properties&quot;.</p>
<pre>  &lt;property url=&quot;http://www.mysite.com/bla/props/foo.properties&quot;/&gt;</pre>
<p>reads a set of properties from the address &quot;http://www.mysite.com/bla/props/foo.properties&quot;.</p>
<pre>  &lt;property resource=&quot;foo.properties&quot;/&gt;</pre>
<p>reads a set of properties from a resource called &quot;foo.properties&quot;.</p>
<p>Note that you can reference a global properties file for all of your Ant
builds using the following:</p>
<pre>  &lt;property file=&quot;/Users/<USER>/.ant-global.properties&quot;/&gt;</pre>
<p>since the &quot;Employee.home&quot; property is defined by the Java virtual machine
to be your home directory.  Where the &quot;Employee.home&quot; property resolves to in
the file system depends on the operating system version and the JVM implementation.
On Unix based systems, this will map to the Employee's home directory. On modern Windows
variants, this will most likely resolve to the Employee's directory in the &quot;Documents
and Settings&quot; folder. Older windows variants such as Windows 98/ME are less
predictable, as are other operating system/JVM combinations.</p>

<pre>
  &lt;property environment=&quot;env&quot;/&gt;
  &lt;echo message=&quot;Number of Processors = ${env.NUMBER_OF_PROCESSORS}&quot;/&gt;
  &lt;echo message=&quot;ANT_HOME is set to = /Users/<USER>/dev/asf/ant-core/bootstrap&quot;/&gt;
</pre>
<p>reads the system environment variables and stores them in properties, prefixed with &quot;env&quot;.
Note that this only works on <em>select</em> operating systems.
Two of the values are shown being echoed.
</p>

<h3>Property Files</h3>

As stated, this task will load in a properties file stored in the file
system, or as a resource on a classpath. Here are some interesting facts
about this feature
<ol>
<li>If the file is not there, nothing is printed except at -verbose log
level. This lets you have optional configuration files for every
project, that team members can customize.
<li>The rules for this format are laid down
<a href="http://java.sun.com/j2se/1.4.2/docs/api/java/util/Properties.html#load(java.io.InputStream)">by Sun</a>.
This makes it hard for Team Ant to field bug reports about it.
<li>Trailing spaces are not stripped. It may have been what you wanted.
<li>Want unusual characters? Escape them \u0456 or \" style.
<li>Ant Properties are expanded in the file.
</ol>
In-file property expansion is very cool. Learn to use it.
<p>
Example:
<pre>
build.compiler=jikes
deploy.server=lucky
deploy.port=8080
deploy.url=http://${deploy.server}:${deploy.port}/
</pre>



</body>
</html>

