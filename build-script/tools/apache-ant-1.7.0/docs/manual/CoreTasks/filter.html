<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Filter Task</title>
</head>

<body>

<h2><a name="filter">Filter</a></h2>
<h3>Description</h3>
<p>Sets a token filter for this project or read multiple token filter from
an input file and sets these as filters.
Token filters are used by all tasks that perform file copying operations
through the Project commodity methods. See the warning
<a href="../using.html#filters"><em>here</em></a> before using.</p>
<p>Note 1: the token string must not contain the separators chars (@).<br>
Note 2: Either token and value attributes must be provided, or only the
filtersfile attribute.</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">token</td>
    <td valign="top">the token string without @</td>
    <td align="center" valign="top">Yes*</td>
  </tr>
  <tr>
    <td valign="top">value</td>
    <td valign="top">the string that should be put to replace the token when the 
      file is copied</td>
    <td align="center" valign="top">Yes*</td>
  </tr>
  <tr>
    <td valign="top">filtersfile</td>
    <td valign="top">The file from which the filters must be read. This file must be a formatted as a property file. </td>
    <td align="center" valign="top">Yes*</td>
  </tr>
</table>
<p>* see notes 1 and 2 above parameters table.</p>
<h3>Examples</h3>
<pre>  &lt;filter token=&quot;year&quot; value=&quot;2000&quot;/&gt;
  &lt;copy todir=&quot;${dest.dir}&quot; filtering=&quot;true&quot;&gt;
    &lt;fileset dir=&quot;src&quot;/&gt;
  &lt;/copy&gt;</pre> 
<p>will copy recursively all the files from the <i>src.dir</i> directory into
the <i>dest.dir</i> directory replacing all the occurrences of the string <i>@year@</i>
with <i>2000.</i></p>
<pre>  &lt;filter filtersfile=&quot;deploy_env.properties&quot;/&gt;</pre>
will read all property entries from the <i>deploy_env.properties</i> file
and set these as filters.



</body>
</html>

