<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>ManifestClassPath Task</title>
</head>

<body>

<h2><a name="manifestclasspath">Manifestclasspath</a></h2>

<h3>Description</h3>
<p>Converts a <a href="../using.html#path">Path</a> into a property
whose value is appropriate for a <a href="manifest.html">Manifest</a>'s
<code>Class-Path</code> attribute.</p>

<p>This task is often used to work around command line limitations on Windows
when using very long class paths when launching an application. The long class
path normally specified on the command line is replaced by a single (possibly
empty) jar file which an in-manifest Class-Path attribute whose value lists
all the jar and zip files the class path should contain. The files referenced
from this attribute must be found relatively to the jar file itself, usually
in the same directory. The Java VM automically uses all file entries listed
in the Class-Path attributes of a jar to locate/load classes. Note though that
it silently ignores entries for which it cannot find any corresponding file.</p>

<p>Note that the property value created may be longer than a manifest's maximum
72 characters per line, but will be properly wrapped as per the Jar
specification by the <code>&lt;manifest&gt;</code> element, where the
defined property is re-referenced.</p>

<p><em>since Ant 1.7</em></p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">property</td>
    <td valign="top">the name of the property to set. This property must
                     not already be set.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">jarfile</td>
    <td valign="top">
      the filename for the Jar which will contain the manifest that will
      use the property this task will set. This file need not exist yet,
      but its parent directory must exist.
    </td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">maxParentLevels</td>
    <td valign="top">
      The maximum number of parent directories one is allowed to traverse
      to navigate from the jar file to the path entry. Put differently, the
      maximum number of .. which is allowed in the relative path from the
      jar file to a given class path enty. Specify 0 to enforce a path
      entry to be in the same directory (or one of its sub-directories)
      as the jar file itself.  Defaults to 2 levels.</td>
    <td valign="top" align="center">No</td>
  </tr>
</table>

<h3>Parameters specified as nested elements</h3>
<h4>classpath</h4>
<p>A <a href="../using#path.html">Path-like</a> element, which can be
defined in-place, or refer to a path defined elsewhere using the
<code>&lt;classpath refid="<em>pathid</em>" /&gt;</code> syntax.
This classpath must not be empty, and is required.</p>

<h3>Examples</h3>
<div id="example1">
  <blockquote><pre>
    &lt;manifestclasspath property="jar.classpath"
                       jarfile="build/acme.jar"&gt;
      &lt;classpath refid="classpath" /&gt;
    &lt;/manifestclasspath&gt;
  </pre></blockquote>
  <p>Assuming a path of id "classpath" was already defined, convert this
  path relatively to the build/ directory that will contain acme.jar, which
  can later be created with <code>&lt;jar&gt;</code> with a nested 
  <code>&lt;manifest&gt;</code> element that lists an
  <code>&lt;attribute name="Class-Path" value="${jar.classpath}" /&gt;</code>.
  </p>
</div>



</body>
</html>
