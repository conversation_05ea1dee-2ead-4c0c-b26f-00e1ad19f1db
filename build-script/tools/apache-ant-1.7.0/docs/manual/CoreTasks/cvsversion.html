<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>CVSVersion Task</title>
<link rel="stylesheet" type="text/css" href="../stylesheets/antmanual.css">
</head>

<body>

<h2><a name="cvs">CvsVersion</a></h2>
<h3>Description</h3>
<p>
This task allows to retrieve a CVS client and server version.
  <i>Since Ant 1.6.1.</i>
</p>
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td colspan="3">Attributes from parent Cvs task which are meaningful here</td>
  </tr>
  <tr>
    <td valign="top">cvsRoot</td>
    <td valign="top">the <code>CVSROOT</code> variable.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">cvsRsh</td>
    <td valign="top">the <code>CVS_RSH</code> variable.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">dest</td>
    <td valign="top">directory containing the checked out version of the project</td>
    <td align="center" valign="top">No, default is project's basedir.</td>
  </tr>
  <tr>
    <td valign="top">package</td>
    <td valign="top">the package/module to check out.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">port</td>
    <td valign="top">Port used by CVS to communicate with the server.</td>
    <td align="center" valign="top">No, default port 2401.</td>
  </tr>
  <tr>
    <td valign="top">passfile</td>
    <td valign="top">Password file to read passwords from.</td>
    <td align="center" valign="top">No, default file ~/.cvspass.</td>
  </tr>
  <tr>
    <td valign="top">failonerror</td>
    <td valign="top">Stop the build process if the command exits with a
      return code other than <code>0</code>. Defaults to false</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td colspan="3">Specific attributes</td>
  </tr>
  <tr>
    <td valign="top">clientversionproperty</td>
    <td valign="top">Name of a property where the cvsclient version
      should be stored</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">serverversionproperty</td>
    <td valign="top">Name of a property where the cvs server version
      should be stored</td>
    <td align="center" valign="top">No</td>
  </tr>
</table>
<h3>Examples</h3>
<pre>  &lt;cvsversion cvsRoot=&quot;:pserver:<EMAIL>:/home/<USER>
       passfile=&quot;/home/<USER>/.cvspass&quot;
       serverversionproperty=&quot;apachecvsversion&quot;
       clientversionproperty=&quot;localcvsversion&quot;
  /&gt;</pre>
<p>finds out the cvs client and server versions and stores the versions in the
properties called apachecvsversion and localcvsversion</p>



</body>
</html>

