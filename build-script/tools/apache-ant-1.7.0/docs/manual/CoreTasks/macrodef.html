<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

  <head>
    <meta http-equiv="Content-Language" content="en-us"></meta>
    <link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>MacroDef Task</title>
    <link rel="stylesheet" type="text/css" href="../stylesheets/antmanual.css">
    <style type="text/css">
      <!--
           .code { background: #EFEFEF; margin-top: }
           -->
    </style>
  </head>

  <body>

    <h2><a name="macrodef">MacroDef</a></h2>
    <h3>Description</h3>
    <p>
      This defines a new task using a <code>&lt;sequential&gt;</code>
      nested task as a template. Nested elements <code>&lt;attribute&gt;</code> and
      <code>&lt;element&gt;</code> are used to specify attributes and elements of
      the new task. These get substituted into the <code>&lt;sequential&gt;</code>
      task when the new task is run.
    </p>
    <h3>Note</h3>
    <p>
      You can also use <i>prior defined</i> attributes for default-values in
      other attributes. See the examples.
    </p>
    <p>
      <em>since Ant 1.6</em>
    </p>
    <h3>Parameters</h3>
    <table border="1" cellpadding="2" cellspacing="0">
      <tr>
        <td valign="top"><b>Attribute</b></td>
        <td valign="top"><b>Description</b></td>
        <td align="center" valign="top"><b>Required</b></td>
      </tr>
      <tr>
        <td valign="top">name</td>
        <td valign="top">The name of the new definition.</td>
        <td valign="top" align="center">Yes</td>
      </tr>
      <tr>
        <td valign="top">uri</td>
        <td valign="top">
          The uri that this definition should live in.
        </td>
        <td valign="top" align="center">No</td>
      </tr>
      <tr>
        <td valign="top">description</td>
        <td valign="top">A description of the macrodef
          (for documentation purposes only).
        </td>
        <td valign="top" align="center">No</td>
      </tr>
      <tr>
        <td valign="top">backtrace</td>
        <td valign="top">
          This controls the error traceback if they is an
          error detected when running the macro. If this is
          set to true, there will be an error trackback, if false
          there will not be one. <em>Since Ant 1.7</em>.
        </td>
        <td valign="top" align="center">No; default <em>true</em></td>
      </tr>
    </table>
      <h3>Parameters specified as nested elements</h3>
    <h4>attribute</h4>
    <p>
      This is used to specify attributes of the new task. The values
      of the attributes get substituted into the templated task.
      The attributes will be required attributes unless a default
      value has been set.
    </p>
    <p>
      This attribute is placed in the body of the templated
      task using a notation similar to the ant property notation
      - @{attribute name}. (May be remembered as "put the substitution
      AT this location").
    </p>
    <p>
      The escape sequence @@ is used to escape @. This allows @{x} to be
      placed in the text without substitution of x by using @@{x}.
      This corresponds to the $ escape sequence for properties.
    </p>
    <p>
      The case of the attribute is ignored, so @{myAttribute} is treated the
      same as @{MyAttribute}.
    </p>
    <h3>Parameters</h3>
    <table border="1" cellpadding="2" cellspacing="0">
      <tr>
        <td valign="top"><b>Attribute</b></td>
        <td valign="top"><b>Description</b></td>
        <td align="center" valign="top"><b>Required</b></td>
      </tr>
      <tr>
        <td valign="top">name</td>
        <td valign="top">The name of the new attribute</td>
        <td valign="top" align="center">Yes</td>
      </tr>
      <tr>
        <td valign="top">default</td>
        <td valign="top">
          The default value of the attribute.
        </td>
        <td valign="top" align="center">No</td>
      </tr>
      <tr>
        <td valign="top">description</td>
        <td valign="top">
          This contains a description of the attribute.
          <em>since ant 1.6.1</em>
        </td>
        <td valign="top" align="center">No</td>
      </tr>
    </table>
    <h4>element</h4>
    <p>
      This is used to specify nested elements of the new task.
      The contents of the nested elements of the task instance
      are placed in the templated task at the tag name.
    </p>
    <p>
      The case of the element name is ignored.
    </p>
    <h3>Parameters</h3>
    <table border="1" cellpadding="2" cellspacing="0">
      <tr>
        <td valign="top"><b>Attribute</b></td>
        <td valign="top"><b>Description</b></td>
        <td align="center" valign="top"><b>Required</b></td>
      </tr>
      <tr>
        <td valign="top">name</td>
        <td valign="top">The name of the element</td>
        <td valign="top" align="center">Yes</td>
      </tr>
      <tr>
        <td valign="top">optional</td>
        <td valign="top">
          If true this nested element is optional. Default is
          false - i.e the nested element is required in
          the new task.
        </td>
        <td valign="top" align="center">No</td>
      </tr>
      <tr>
        <td valign="top">implicit</td>
        <td valign="top">
          If true this nested element is implicit. This means that
          any nested elements of the macrodef instance will be placed
          in the element indicated by the name of this element.
          There can only be one element if an element is implicit.
          The default value is false. <em>since ant 1.6.2</em>
        </td>
        <td valign="top" align="center">No</td>
      </tr>
      <tr>
        <td valign="top">description</td>
        <td valign="top">
          This contains a description
          informing the Employee what the contents of the element are expected to be.
          <em>since ant 1.6.1</em>
        </td>
        <td valign="top" align="center">No</td>
      </tr>
    </table>
    <h4>text</h4>
    <p>
      This is used to specify the treatment of text contents of the macrodef.
      If this element is not present, then any nested text in the macro
      will be an error. If the text element is present, then the name
      becomes an attribute that gets set to the nested text of the macro.
      <em>Since ant 1.6.1.</em>
    </p>
    <p>
      The case of the text name is ignored.
    </p>
    <h3>Parameters</h3>
    <table border="1" cellpadding="2" cellspacing="0">
      <tr>
        <td valign="top"><b>Attribute</b></td>
        <td valign="top"><b>Description</b></td>
        <td align="center" valign="top"><b>Required</b></td>
      </tr>
      <tr>
        <td valign="top">name</td>
        <td valign="top">The name of the text attribute</td>
        <td valign="top" align="center">Yes</td>
      </tr>
      <tr>
        <td valign="top">optional</td>
        <td valign="top">
          If true nested text in the macro is optional, default is "false".
        </td>
        <td valign="top" align="center">No</td>
      </tr>
      <tr>
        <td valign="top">trim</td>
        <td valign="top">
          If true, the nested text is trimmed of white space,
          default is "false".
        </td>
        <td valign="top" align="center">No</td>
      </tr>
      <tr>
        <td valign="top">description</td>
        <td valign="top">
          This contains a description
          informing the Employee what the nested text of the macro is expected
          to be.
        </td>
        <td valign="top" align="center">No</td>
      </tr>
    </table>

    <h3>Examples</h3>
    <p>
      The following example defined a task called testing and
      runs it.
    </p>
    <blockquote>
<pre class=code>
&lt;macrodef name="testing"&gt;
   &lt;attribute name="v" default="NOT SET"/&gt;
   &lt;element name="some-tasks" optional="yes"/&gt;
   &lt;sequential&gt;
      &lt;echo&gt;v is @{v}&lt;/echo&gt;
      &lt;some-tasks/&gt;
   &lt;/sequential&gt;
&lt;/macrodef&gt;

&lt;testing v="This is v"&gt;
   &lt;some-tasks&gt;
      &lt;echo&gt;this is a test&lt;/echo&gt;
   &lt;/some-tasks&gt;
&lt;/testing&gt;
</pre>
    </blockquote>
    <p>
      The following fragment defines a task called <code>&lt;call-cc&gt;</code> which
      take the attributes "target", "link" and "target.dir" and the
      nested element "cc-elements". The body of the task
      uses the <code>&lt;cc&gt;</code> task from the
      <a href="http://ant-contrib.sourceforge.net/">ant-contrib</a> project.
    </p>
    <blockquote>
<pre class="code">
&lt;macrodef name="call-cc"&gt;
   &lt;attribute name="target"/&gt;
   &lt;attribute name="link"/&gt;
   &lt;attribute name="target.dir"/&gt;
   &lt;element name="cc-elements"/&gt;
   &lt;sequential&gt;
      &lt;mkdir dir="${obj.dir}/@{target}"/&gt;
      &lt;mkdir dir="@{target.dir}"/&gt;
         &lt;cc link="@{link}" objdir="${obj.dir}/@{target}"
             outfile="@{target.dir}/@{target}"&gt;
            &lt;compiler refid="compiler.options"/&gt;
            &lt;cc-elements/&gt;
         &lt;/cc&gt;
      &lt;/sequential&gt;
&lt;/macrodef&gt;
</pre>
    </blockquote>
    <p>
      This then can be used as follows:
    </p>
    <blockquote>
<pre class="code">
&lt;call-cc target="unittests" link="executable"
         target.dir="${build.bin.dir}"&gt;
   &lt;cc-elements&gt;
      &lt;includepath location="${gen.dir}"/&gt;
      &lt;includepath location="test"/&gt;
      &lt;fileset dir="test/unittest" includes = "**/*.cpp"/&gt;
      &lt;fileset dir="${gen.dir}" includes = "*.cpp"/&gt;
      &lt;linker refid="linker-libs"/&gt;
   &lt;/cc-elements&gt;
&lt;/call-cc&gt;
</pre>
    </blockquote>
    <p>
      The following fragment shows &lt;call-cc&gt;, but this time
      using an implicit element and with the link and target.dir arguments
      having default values.
    </p>
    <blockquote>
<pre class="code">
&lt;macrodef name="call-cc"&gt;
   &lt;attribute name="target"/&gt;
   &lt;attribute name="link" default="executable"/&gt;
   &lt;attribute name="target.dir" default="${build.bin.dir}"/&gt;
   &lt;element name="cc-elements" implicit="yes"/&gt;
   &lt;sequential&gt;
      &lt;mkdir dir="${obj.dir}/@{target}"/&gt;
      &lt;mkdir dir="@{target.dir}"/&gt;
         &lt;cc link="@{link}" objdir="${obj.dir}/@{target}"
             outfile="@{target.dir}/@{target}"&gt;
            &lt;compiler refid="compiler.options"/&gt;
            &lt;cc-elements/&gt;
         &lt;/cc&gt;
      &lt;/sequential&gt;
&lt;/macrodef&gt;
</pre>
    </blockquote>
    <p>
      This then can be used as follows, note that &lt;cc-elements&gt;
      is not specified.
    </p>
    <blockquote>
<pre class="code">
&lt;call-cc target="unittests"/&gt;
   &lt;includepath location="${gen.dir}"/&gt;
   &lt;includepath location="test"/&gt;
   &lt;fileset dir="test/unittest" includes = "**/*.cpp"/&gt;
   &lt;fileset dir="${gen.dir}" includes = "*.cpp"/&gt;
   &lt;linker refid="linker-libs"/&gt;
&lt;/call-cc&gt;
</pre>
    </blockquote>
    <p>
      The following shows the use of the <code>text</code> element.
    </p>
    <blockquote>
<pre class="code">
&lt;macrodef name="echotest"&gt;
   &lt;text name="text"/&gt;
   &lt;sequential&gt;
      &lt;echo&gt;@{text}&lt;/echo&gt;
   &lt;/sequential&gt;
&lt;/macrodef&gt;
&lt;echotest&gt;
   Hello world
&lt;/echotest&gt;
</pre>
    </blockquote>
    <p>
      The following uses a prior defined attribute for setting the
      default value of another. The output would be
      <tt>one=test two=test</tt>. If you change the order of lines
      *1 and *2 the output would be <tt>one=test two=@{one}</tt>,
      because while processing the <i>two</i>-line the value for
      <i>one</i> is not set.
    </p>
    <blockquote>
<pre class="code">
&lt;macrodef name="test"&gt;
   &lt;attribute name="one"/&gt;                     <b>*1</b>
   &lt;attribute name="two" default="@{one}"/&gt;    <b>*2</b>
   &lt;sequential&gt;
      &lt;echo&gt;one=@{one}   two=@{two}&lt;/echo&gt;
   &lt;/sequential&gt;
&lt;/macrodef&gt;
&lt;test one="test"/&gt;
</pre>
    </blockquote>


</body>
</html>
