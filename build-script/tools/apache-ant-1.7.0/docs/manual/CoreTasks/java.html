<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Java Task</title>
</head>

<body>

<h2><a name="java">Java</a></h2>
<h3>Description</h3>
<p>Executes a Java class within the running (Ant) VM or forks another VM if
specified.</p>
<p>
If odd things go wrong when you run this task, set fork="true" to use a new
JVM.

<p>As of Ant 1.6.3, you can interact with a forked VM, as well as
sending input to it via the <code>input</code> and <code>inputstring</code>
attributes.</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">classname</td>
    <td valign="top">the Java class to execute.</td>
    <td align="center" valign="top">Either <tt>jar</tt> or <tt>classname</tt></td>
  </tr>
  <tr>
    <td valign="top">jar</td>
    <td valign="top">the location of the jar file to execute (must have a
    Main-Class entry in the manifest). Fork must be set to true if this option is selected.
    See notes below for more details.
    </td>
    <td align="center" valign="top">Either <tt>jar</tt> or <tt>classname</tt></td>
  </tr>
  <tr>
    <td valign="top">args</td>
    <td valign="top">the arguments for the class that is
      executed. <b>deprecated, use nested <code>&lt;arg&gt;</code>
      elements instead.</b></td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">classpath</td>
    <td valign="top">the classpath to use.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">classpathref</td>
    <td valign="top">the classpath to use, given as <a
      href="../using.html#references">reference</a> to a PATH defined elsewhere.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">fork</td>
    <td valign="top">if enabled triggers the class execution in another VM
      (disabled by default)</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">spawn</td>
    <td valign="top">if enabled allows to start a process which will outlive ant.<br>
    Requires fork=true, and not compatible
    with timeout, input, output, error, result attributes.<br>
      (disabled by default)</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">jvm</td>
    <td valign="top">the command used to invoke the Java Virtual Machine,
      default is 'java'.  The command is resolved by java.lang.Runtime.exec().
      Ignored if fork is disabled.
    </td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">jvmargs</td>
    <td valign="top">the arguments to pass to the forked VM (ignored
      if fork is disabled). <b>deprecated, use nested
      <code>&lt;jvmarg&gt;</code> elements instead.</b></td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">maxmemory</td>
    <td valign="top">Max amount of memory to allocate to the forked VM
      (ignored if fork is disabled)</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">failonerror</td>
    <td valign="top">Stop the buildprocess if the command exits with a
      returncode other than 0. Default is "false" (see <a href="#failonerror">note</a>)</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">resultproperty</td>
    <td valign="top">The name of a property in which the return code of the
      command should be stored. Only of interest if failonerror=false
      and if fork=true.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">dir</td>
    <td valign="top">The directory to invoke the VM in.  (ignored if
      fork is disabled)</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">output</td>
    <td valign="top">Name of a file to which to write the output. If the error stream
      is not also redirected to a file or property, it will appear in this output.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">error</td>
    <td valign="top">The file to which the standard error of the command should be
      redirected. </td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">logError</td>
    <td valign="top">This attribute is used when you wish to see error output in Ant's
                     log and you are redirecting output to a file/property. The error
                     output will not be included in the output file/property. If you
                     redirect error with the &quot;error&quot; or &quot;errorProperty&quot;
                     attributes, this will have no effect.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">append</td>
    <td valign="top">Whether output and error files should be appended to or overwritten.
    Defaults to false.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">outputproperty</td>
    <td valign="top">The name of a property in which the output of the
      command should be stored. Unless the error stream is redirected to a separate
      file or stream, this property will include the error output.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">errorproperty</td>
    <td valign="top">The name of a property in which the standard error of the
      command should be stored.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">input</td>
    <td valign="top">A file from which the executed command's standard input
                     is taken. This attribute is mutually exclusive with the
                     inputstring attribute</td>
    <td align="center" valign="top">No; default is to take standard input from console
        (unless <code>spawn="true"</code>)</td>
  </tr>
  <tr>
    <td valign="top">inputstring</td>
    <td valign="top">A string which serves as the input stream for the
                     executed command. This attribute is mutually exclusive with the
                     input attribute.</td>
    <td align="center" valign="top">No; default is to take standard input from console
        (unless <code>spawn="true"</code>)</td>
  </tr>
  <tr>
    <td valign="top">newenvironment</td>
    <td valign="top">Do not propagate old environment when new
      environment variables are specified. Default is &quot;false&quot;
      (ignored if fork is disabled).</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">timeout</td>
    <td valign="top">Stop the command if it doesn't finish within the
    specified time (given in milliseconds).  <strong>It is highly
    recommended to use this feature only if fork is enabled.</strong></td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">clonevm</td>
    <td valign="top">If set to true true, then all system properties
      and the bootclasspath of the forked Java Virtual Machine will be
      the same as those of the Java VM running Ant.  Default is
      &quot;false&quot; (ignored if fork is disabled).
      <em>since Ant 1.7</em></td>
    <td align="center" valign="top">No</td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>
<h4>arg and jvmarg</h4>
<p>Use nested <code>&lt;arg&gt;</code> and <code>&lt;jvmarg&gt;</code>
elements to specify arguments for the Java class and the forked VM respectively.
See <a href="../using.html#arg">Command line arguments</a>.</p>
<h4>sysproperty</h4>
<p>Use nested <code>&lt;sysproperty&gt;</code>
elements to specify system properties required by the class.
These properties will be made available to the VM during the execution
of the class (either ANT's VM or the forked VM). The attributes
for this element are the same as for <a href="exec.html#env">environment
variables</a>.</p>

<h4>syspropertyset</h4>

<p>You can specify a set of properties to be used as system properties
with <a href="../CoreTypes/propertyset.html">syspropertyset</a>s.</p>

<p><em>since Ant 1.6</em>.</p>

<h4>classpath</h4>
<p><code>Java</code>'s <i>classpath</i> attribute is a <a
href="../using.html#path">PATH like structure</a> and can also be set via a nested
<i>classpath</i> element.</p>

<h4>bootclasspath</h4>

<p>The location of bootstrap class files can be specified using this
<a href="../using.html#path">PATH like structure</a> - will be ignored
if <i>fork</i> is not <code>true</code> or the target VM doesn't
support it (i.e. Java 1.1).</p>

<p><em>since Ant 1.6</em>.</p>

<h4>env</h4>
<p>It is possible to specify environment variables to pass to the
forked VM via nested <i>env</i> elements. See the description in the
section about <a href="exec.html#env">exec</a></p>
<p>Settings will be ignored if fork is disabled.</p>

<h4>permissions</h4>
<p>Security permissions can be revoked and granted during the execution of the
class via a nested <i>permissions</i> element. For more information please
see <a href="../CoreTypes/permissions.html">permissions</a></p>
<p>When the permission RuntimePermission exitVM has not been granted (or has
been revoked) the System.exit() call will be intercepted
and treated like indicated in <i>failonerror</i>.</p>
<p>Note:<br>
If you do not specify permissions,
a set of default permissions will be added to your Java invocation to make
sure that the ant run will continue or terminated as indicated by
<i>failonerror</i>. All permissions not granted per default will be
checked by whatever security manager was already in place. exitVM will be
disallowed.
</p>
<p>Settings will be ignored if fork is enabled.</p>

<p><em>since Ant 1.6</em>.</p>

<h4>assertions</h4>

<p>You can control enablement of Java 1.4 assertions with an
<a href="../CoreTypes/assertions.html"><tt>&lt;assertions&gt;</tt></a>
subelement.</p>

<p>Assertion statements are currently ignored in non-forked mode.</p>

<p><em>since Ant 1.6.</em></p>

<a name="redirector"><h4>redirector</h4></a>
<i><b>Since Ant 1.6.2</b></i>
<p>A nested <a href="../CoreTypes/redirector.html">I/O Redirector</a>
can be specified.  In general, the attributes of the redirector behave
as the corresponding attributes available at the task level.  The most
notable peculiarity stems from the retention of the <code>&lt;java&gt;</code>
attributes for backwards compatibility.  Any file mapping is done
using a <CODE>null</CODE> sourcefile; therefore not all
<a href="../CoreTypes/mapper.html">Mapper</a> types will return
results.  When no results are returned, redirection specifications
will fall back to the task level attributes.  In practice this means that
defaults can be specified for input, output, and error output files.
</p>
<h3>Errors and return codes</h3>
By default the return code of a <code>&lt;java&gt;</code> is ignored.
Alternatively, you can set <code>resultproperty</code> to the name
of a property and have it assigned to the result code (barring immutability,
of course).
When you set <code>failonerror="true"</code>, the only possible value for
<code>resultproperty</code> is 0. Any non-zero response is treated as an
error and would mean the build exits.
<p> Similarly, if <code>failonerror="false"</code> and <code>fork="false"</code>
, then <code>&lt;java&gt;</code> <b>must</b> return 0 otherwise the build will
exit, as the class was run by the build JVM.</p>

<h3>JAR file execution</h3>

<p>The parameter of the <tt>jar</tt> attribute is of type <tt>File</tt>;
that is, the parameter is resolved to an absolute file relative to the
base directory of the project, <i>not</i> the directory in which the Java
task is run. If you need to locate a JAR file relative to the directory
the task will be run in, you need to explicitly create the full path
to the JAR file.</p>
<p>When using the <tt>jar</tt> attribute, all classpath settings are 
ignored according to <a href="http://java.sun.com/j2se/1.5.0/docs/tooldocs/windows/java.html#-jar">Sun's
specification</a>. 


<h3>Examples</h3>
<pre>
       &lt;java classname=&quot;test.Main&quot;&gt;
         &lt;arg value=&quot;-h&quot;/&gt;
         &lt;classpath&gt;
           &lt;pathelement location=&quot;dist/test.jar&quot;/&gt;
           &lt;pathelement path=&quot;/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-launcher.jar:/System/Library/Frameworks/JavaVM.framework/Versions/1.5.0/Classes/.compatibility/14compatibility.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/activation.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/ant-antunit-1.0Beta2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/antlrall.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bcel.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bsf-2.4.0.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bsh-2.0b4.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bsh-core-2.0b4.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/commons-logging-api.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/commons-logging.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/commons-net-1.4.0.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jai_codec.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jai_core.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jakarta-oro-2.0.8.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jakarta-regexp-1.2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jdepend.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/js-1.6R3.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jsch-0.1.29.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/junit-3.8.2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jython.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/log4j-1.2.9.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/mail.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/NetRexxC.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/NetRexxR.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/resolver.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/starteam-sdk.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/stylebook-1.0-b2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/weblogic.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/weblogicaux.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/weblogicclasses.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xalan.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xalan1.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xercesSamples.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xmlParserAPIs.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xsltc.jar:/Users/<USER>/dev/asf/ant-core/lib/optional:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-antlr.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-bcel.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-bsf.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-log4j.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-oro.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-regexp.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-resolver.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-commons-logging.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-commons-net.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jai.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-javamail.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jdepend.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jmf.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jsch.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-junit.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-launcher.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-netrexx.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-nodeps.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-starteam.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-stylebook.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-swing.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-testutil.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-trax.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-weblogic.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/xercesImpl.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/xml-apis.jar&quot;/&gt;
         &lt;/classpath&gt;
       &lt;/java&gt;
</pre>
Run a class in this JVM with a new jar on the classpath

<pre>
       &lt;java jar=&quot;dist/test.jar&quot;
           fork="true"
           failonerror="true"
           maxmemory="128m"
           &gt;
         &lt;arg value=&quot;-h&quot;/&gt;
         &lt;classpath&gt;
           &lt;pathelement location=&quot;dist/test.jar&quot;/&gt;
           &lt;pathelement path=&quot;/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-launcher.jar:/System/Library/Frameworks/JavaVM.framework/Versions/1.5.0/Classes/.compatibility/14compatibility.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/activation.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/ant-antunit-1.0Beta2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/antlrall.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bcel.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bsf-2.4.0.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bsh-2.0b4.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bsh-core-2.0b4.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/commons-logging-api.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/commons-logging.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/commons-net-1.4.0.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jai_codec.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jai_core.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jakarta-oro-2.0.8.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jakarta-regexp-1.2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jdepend.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/js-1.6R3.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jsch-0.1.29.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/junit-3.8.2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jython.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/log4j-1.2.9.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/mail.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/NetRexxC.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/NetRexxR.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/resolver.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/starteam-sdk.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/stylebook-1.0-b2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/weblogic.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/weblogicaux.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/weblogicclasses.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xalan.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xalan1.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xercesSamples.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xmlParserAPIs.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xsltc.jar:/Users/<USER>/dev/asf/ant-core/lib/optional:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-antlr.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-bcel.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-bsf.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-log4j.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-oro.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-regexp.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-resolver.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-commons-logging.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-commons-net.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jai.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-javamail.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jdepend.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jmf.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jsch.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-junit.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-launcher.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-netrexx.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-nodeps.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-starteam.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-stylebook.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-swing.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-testutil.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-trax.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-weblogic.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/xercesImpl.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/xml-apis.jar&quot;/&gt;
         &lt;/classpath&gt;
       &lt;/java&gt;
</pre>
Run the JAR test.jar in this project's dist/lib directory.
using the manifest supplied entry point, forking (as required),
and with a maximum memory of 128MB. Any non zero return code breaks the build.

<pre>
       &lt;java
           dir="${exec.dir}"
           jar=&quot;${exec.dir}/dist/test.jar&quot;
           fork="true"
           failonerror="true"
           maxmemory="128m"
           &gt;
         &lt;arg value=&quot;-h&quot;/&gt;
         &lt;classpath&gt;
           &lt;pathelement location=&quot;dist/test.jar&quot;/&gt;
           &lt;pathelement path=&quot;/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-launcher.jar:/System/Library/Frameworks/JavaVM.framework/Versions/1.5.0/Classes/.compatibility/14compatibility.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/activation.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/ant-antunit-1.0Beta2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/antlrall.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bcel.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bsf-2.4.0.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bsh-2.0b4.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/bsh-core-2.0b4.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/commons-logging-api.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/commons-logging.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/commons-net-1.4.0.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jai_codec.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jai_core.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jakarta-oro-2.0.8.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jakarta-regexp-1.2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jdepend.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/js-1.6R3.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jsch-0.1.29.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/junit-3.8.2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/jython.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/log4j-1.2.9.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/mail.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/NetRexxC.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/NetRexxR.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/resolver.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/starteam-sdk.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/stylebook-1.0-b2.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/weblogic.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/weblogicaux.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/weblogicclasses.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xalan.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xalan1.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xercesSamples.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xmlParserAPIs.jar:/Users/<USER>/dev/asf/ant-core/lib/optional/xsltc.jar:/Users/<USER>/dev/asf/ant-core/lib/optional:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-antlr.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-bcel.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-bsf.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-log4j.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-oro.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-regexp.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-apache-resolver.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-commons-logging.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-commons-net.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jai.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-javamail.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jdepend.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jmf.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-jsch.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-junit.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-launcher.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-netrexx.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-nodeps.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-starteam.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-stylebook.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-swing.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-testutil.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-trax.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant-weblogic.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/ant.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/xercesImpl.jar:/Users/<USER>/dev/asf/ant-core/bootstrap/lib/xml-apis.jar&quot;/&gt;
         &lt;/classpath&gt;
       &lt;/java&gt;
</pre>
Run the JAR dist/test.jar relative to the directory
<tt>${exec.dir}</tt>, this being the same directory into which the JVM
is to start up.

<pre>  &lt;java classname=&quot;test.Main&quot;/&gt;</pre>
Runs a given class with the current classpath.

<pre>
  &lt;java classname=&quot;test.Main&quot;
        fork=&quot;yes&quot; &gt;
    &lt;sysproperty key=&quot;DEBUG&quot; value=&quot;true&quot;/&gt;
    &lt;arg value=&quot;-h&quot;/&gt;
    &lt;jvmarg value=&quot;-Xrunhprof:cpu=samples,file=log.txt,depth=3&quot;/&gt;
  &lt;/java&gt;
</pre>
Add system properties and JVM-properties to the JVM as in
<code>java ="-Xrunhprof:cpu=samples,file=log.txt,depth=3 -DDEBUG=true test.Main</code>

<pre>  &lt;java classname=&quot;ShowJavaVersion&quot; classpath=&quot;.&quot;
        jvm=&quot;path-to-java14-home/bin/java&quot; fork=&quot;true&quot;
        taskname=&quot;java1.4&quot; &gt;
</pre>
Use a given Java implementation (another the one Ant is currently using) to run the class.
For documentation in the log <code>taskname</code> is used to change the <code>[java]</code>
log-prefix to <code>[java1.4]</code>.


<p><strong>Note</strong>: you can not specify the (highly deprecated) MSJVM, "jview.exe" as the
JVM, as it takes different parameters for other JVMs,
That JVM can be started from <code>&lt;exec&gt;</code> if required.</p>


</body>
</html>
