<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Patch Task</title>
</head>

<body>

<h2><a name="patch">Patch</a></h2>
<h3>Description</h3>
<p>Applies a diff file to originals. ; requires "patch" to be
 on the execution path.  </p>
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">patchfile</td> 
    <td valign="top">the file that includes the diff output</td>
    <td align="center" valign="top">Yes</td>
  </tr>
  <tr>
    <td valign="top">originalfile</td> 
    <td valign="top">the file to patch</td>
    <td align="center" valign="top">No, tries to guess it from the diff 
      file</td>
  </tr>
  <tr>
    <td valign="top">destfile</td> 
    <td valign="top">the file to send the output to instead of
      patching the file(s) in place.  <em>since Ant 1.6</em></td>
    <td align="center" valign="top">No.</td>
  </tr>
  <tr>
    <td valign="top">backups</td> 
    <td valign="top">Keep backups of the unpatched files</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">quiet</td> 
    <td valign="top">Work silently unless an error occurs</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">reverse</td> 
    <td valign="top">Assume patch was created with old and new files 
      swapped.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">ignorewhitespace</td> 
    <td valign="top">Ignore whitespace differences.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">strip</td> 
    <td valign="top">Strip the smallest prefix containing <i>num</i> leading 
      slashes from filenames.</td>
    <td align="center" valign="top">No</td>
  </tr>
  <tr>
    <td valign="top">dir</td> 
    <td valign="top">The directory in which to run the patch command.</td>
    <td align="center" valign="top">No, default is the project's basedir.</td>
  </tr>
</table>
<h3>Examples</h3>
<pre>  &lt;patch patchfile=&quot;module.1.0-1.1.patch&quot;/&gt;</pre>
<p>applies the diff included in <i>module.1.0-1.1.patch</i> to the
files in base directory guessing the filename(s) from the diff output.</p>
<pre>  &lt;patch patchfile=&quot;module.1.0-1.1.patch&quot; strip=&quot;1&quot;/&gt;</pre>
<p>like above but one leading directory part will be removed. i.e. if
the diff output looked like</p>
<pre>
--- a/mod1.0/A	Mon Jun  5 17:28:41 2000
+++ a/mod1.1/A	Mon Jun  5 17:28:49 2000
</pre> 
the leading <i>a/</i> will be stripped.


</body>
</html>

