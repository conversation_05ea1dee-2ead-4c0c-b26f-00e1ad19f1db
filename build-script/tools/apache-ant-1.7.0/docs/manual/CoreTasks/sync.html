<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Sync Task</title>
</head>

<body>

<h2><a name="get">Sync</a></h2>
<p><em>Since Ant 1.6</em></p>
<h3>Description</h3>

<p>Synchronize a target directory from the files defined in one or
more <a href="../CoreTypes/resources.html#collection">Resource Collection</a>s.</p>

<p>Any file in the target directory that has not been matched by at
least one of the nested resource collections gets removed.  I.e. if you exclude a
file in your sources and a file of that name is present in the target
dir, it will get removed from the target.</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">todir</td>
    <td valign="top">the target directory to sync with the resource collections</td>
    <td align="center" valign="top">Yes</td>
  </tr>  
  <tr>
    <td valign="top">overwrite</td>
    <td valign="top">Overwrite existing files even if the destination
      files are newer.</td>
    <td valign="top" align="center">No; defaults to false.</td>
  </tr>
  <tr>
    <td valign="top">includeEmptyDirs</td>
     <td valign="top">Copy any empty directories included in the resource collection(s).
     </td>
     <td valign="top" align="center">No; defaults to true.</td>
  </tr>
  <tr>
    <td valign="top">failonerror</td>
     <td valign="top">If is set to false, log a warning message, but do not stop the build,
       when one of the nested filesets points to a directory that
       doesn't exist.
     </td>
     <td valign="top" align="center">No; defaults to true.</td>
  </tr>
  <tr>
    <td valign="top">verbose</td>
     <td valign="top">Log the files that are being copied.</td>
     <td valign="top" align="center">No; defaults to false.</td>
  </tr>
  <tr>
    <td valign="top">granularity</td>
    <td valign="top">The number of milliseconds leeway to give before
    deciding a file is out of date. This is needed because not every
    file system supports tracking the last modified time to the
    millisecond level. Default is 0 milliseconds, or 2 seconds on DOS
    systems.  This can also be useful if source and target files live
    on separate machines with clocks being out of sync.  <em>since Ant
    1.6.2</em>.</td>
     <td valign="top" align="center">No.</td>
  </tr>
</table>

<h3>Parameters specified as nested elements</h3>

<h4>fileset or any other resource collection</h4>
<p><a href="../CoreTypes/resources.html#collection">Resource
Collection</a>s are used to select groups of files to copy.  To use a
resource collection, the <code>todir</code> attribute must be set.</p>
<p>Prior to Ant 1.7 only <code>&lt;fileset&gt;</code> has been
supported as a nested element.</p>

<h4>preserveInTarget</h4>

<p>Specifies files or directories that should be kept in the target
directory even if they are not present in one of the source
directories.</p>

<p>This nested element is like a <a
href="../CoreTypes/fileset.html">FileSet</a> except that it doesn't
support the dir attribute and the usedefaultexcludes attribute
defaults to false.</p>

<h3>Examples</h3>

<blockquote><pre>
&lt;sync todir=&quot;site&quot;&gt;
  &lt;fileset dir=&quot;generated-site&quot;/&gt;
&lt;/sync&gt;
</pre></blockquote>
<p>overwrites all files in <em>site</em> with newer files from
<em>generated-site</em>, deletes files from <em>site</em> that are not
present in <em>generated-site</em>.</p>

<blockquote><pre>
&lt;sync todir=&quot;site&quot;&gt;
  &lt;fileset dir=&quot;generated-site&quot;/&gt;
  &lt;preserveintarget&gt;
    &lt;include name=&quot;**/CVS/**&quot;/&gt;
  &lt;/preserveintarget&gt;
&lt;/sync&gt;
</pre></blockquote>
<p>overwrites all files in <em>site</em> with newer files from
<em>generated-site</em>, deletes files from <em>site</em> that are not
present in <em>generated-site</em> but keeps all files in any
<em>CVS</em> sub-directory.</p>



</body>
</html>

