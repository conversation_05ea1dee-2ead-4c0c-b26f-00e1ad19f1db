<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>BuildNumber Task</title>
</head>

<body>

<h2><a name="buildnumber">BuildNumber</a></h2>
<h3>Description</h3>
<p>This is a basic task that can be used to track build numbers.</p>
<p>It will first attempt to read a build number from a file (by default,
<code>build.number</code> in the current directory), then
set the property <code>build.number</code> to the value that was read in
(or to <code>0</code>, if no such value). It will then increment the
number by one and write it back out to the file.
(See the
<a href="../OptionalTasks/propertyfile.html">PropertyFile</a> task
if you need finer control over things such as the property name or
the number format.)
</p>

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">The file to read and write the build number from/to.</td>
    <td align="center" valign="top">No; defaults to &quot;build.number&quot;</td>
  </tr>
</table>

<h3>Examples</h3>
<blockquote><pre>
&lt;buildnumber/&gt;
</pre></blockquote>

<p>Read, increment, and write a build number to the default file,
<code>build.number</code>.</p>

<blockquote><pre>
&lt;buildnumber file=&quot;mybuild.number&quot;/&gt;
</pre></blockquote>

<p>Read, increment, and write a build number to the file
<code>mybuild.number</code>.</p>



</body>
</html>

