<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Copydir Task</title>
</head>

<body>

<h2><a name="copydir">Copydir</a></h2>
<h3><i>Deprecated</i></h3>
<p><i>This task has been deprecated.  Use the Copy task instead.</i></p>
<h3>Description</h3>
<p>Copies a directory tree from the source to the destination.</p>
<p>It is possible to refine the set of files that are being copied. This can be
done with the <i>includes</i>, <i>includesfile</i>, <i>excludes</i>, <i>excludesfile</i> and <i>defaultexcludes</i>
attributes. With the <i>includes</i> or <i>includesfile</i> attribute you specify the files you want to
have included by using patterns. The <i>exclude</i> or <i>excludesfile</i> attribute is used to specify
the files you want to have excluded. This is also done with patterns. And
finally with the <i>defaultexcludes</i> attribute, you can specify whether you
want to use default exclusions or not. See the section on <a
href="../dirtasks.html#directorybasedtasks">directory based tasks</a>, on how the
inclusion/exclusion of files works, and how to write patterns.</p>
<p>This task forms an implicit <a href="../CoreTypes/fileset.html">FileSet</a> and
supports all attributes of <code>&lt;fileset&gt;</code>
(<code>dir</code> becomes <code>src</code>) as well as the nested
<code>&lt;include&gt;</code>, <code>&lt;exclude&gt;</code> and
<code>&lt;patternset&gt;</code> elements.</p>
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">src</td>
    <td valign="top">the directory to copy.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">dest</td>
    <td valign="top">the directory to copy to.</td>
    <td valign="top" align="center">Yes</td>
  </tr>
  <tr>
    <td valign="top">includes</td>
    <td valign="top">comma- or space-separated list of patterns of files that must be
      included. All files are included when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">includesfile</td>
    <td valign="top">the name of a file. Each line of this file is
      taken to be an include pattern</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">excludes</td>
    <td valign="top">comma- or space-separated list of patterns of files that must be
      excluded. No files (except default excludes) are excluded when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">excludesfile</td>
    <td valign="top">the name of a file. Each line of this file is
      taken to be an exclude pattern</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">defaultexcludes</td>
    <td valign="top">indicates whether default excludes should be used or not
      (&quot;yes&quot;/&quot;no&quot;). Default excludes are used when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">filtering</td>
    <td valign="top">indicates whether token filtering should take place during
      the copy</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">flatten</td>
    <td valign="top">ignore directory structure of source directory,
      copy all files into a single directory, specified by the <code>dest</code>
      attribute (default is <code>false</code>).</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">forceoverwrite</td>
    <td valign="top">overwrite existing files even if the destination
      files are newer (default is false).</td>
    <td valign="top" align="center">No</td>
  </tr>
</table>
<h3>Examples</h3>
<pre>  &lt;copydir src=&quot;${src}/resources&quot;
           dest=&quot;${dist}&quot;
  /&gt;</pre>
<p>copies the directory <code>${src}/resources</code> to <code>${dist}</code>.</p>
<pre>  &lt;copydir src=&quot;${src}/resources&quot;
           dest=&quot;${dist}&quot;
           includes=&quot;**/*.java&quot;
           excludes=&quot;**/Test.java&quot;
  /&gt;</pre>
<p>copies the directory <code>${src}/resources</code> to <code>${dist}</code>
recursively. All java files are copied, except for files with the name <code>Test.java</code>.</p>
<pre>  &lt;copydir src=&quot;${src}/resources&quot;
           dest=&quot;${dist}&quot;
           includes=&quot;**/*.java&quot;
           excludes=&quot;mypackage/test/**&quot;/&gt;</pre>
<p>copies the directory <code>${src}/resources</code> to <code>${dist}</code>
recursively. All java files are copied, except for the files under the <code>mypackage/test</code>
directory.</p>


</body>
</html>

