<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Length Task</title>
</head>

<body>

<h2>Length</h2>
<h3>Description</h3>
<p>Display or set a property containing length information for
   a string, a file, or one or more nested
   <a href="../CoreTypes/resources.html#collection">Resource Collection</a>s.
   Can also be used as a condition. <b>Since Ant 1.6.3</b></p>
<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">property</td>
    <td valign="top">The property to set.  If omitted
      the results are written to the log. Ignored when
      processing as a condition.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">Single file whose length to report.</td>
    <td valign="top" align="center" rowspan="2">One of these,
      or one or more nested filesets</td>
  </tr>
  <tr>
    <td valign="top">string</td>
    <td valign="top">The string whose length to report.</td>
  </tr>
  <tr>
    <td valign="top">mode</td>
    <td valign="top">File length mode; when &quot;all&quot; the resulting
      value is the sum of all included resources' lengths; when &quot;each&quot;
      the task outputs the absolute path and length of each included resource,
      one per line. Ignored when processing as a condition.</td>
    <td valign="top" align="center">No; default is &quot;all&quot;</td>
  </tr>
  <tr>
    <td valign="top">trim</td>
    <td valign="top">Whether to trim when operating on a string.</td>
    <td valign="top" align="center">No; only valid when string is set</td>
  </tr>
  <tr>
    <td valign="top">length</td>
    <td valign="top">Comparison length for processing as a condition.</td>
    <td valign="top" align="center">Yes, in condition mode</td>
  </tr>
  <tr>
    <td valign="top">when</td>
    <td valign="top">Comparison type: "equal", "eq", "greater", "gt", "less",
      "lt", "ge" (greater or equal), "ne" (not equal), "le" (less or equal)
      for use when operating as a condition.</td>
    <td valign="top" align="center">No; default is "equal"</td>
  </tr>
</table>
<h3>Parameters specified as nested elements</h3>
<h4>Resource Collections</h4>
<p>You can include resources via nested
  <a href="../CoreTypes/resources.html#collection">Resource Collection</a>s.</p>
<h3>Examples</h3>
<pre>&lt;length string=&quot;foo&quot; property=&quot;length.foo&quot; /&gt;
</pre>
<p>Stores the length of the string &quot;foo&quot; in the property named
<i>length.foo</i>.</p>
<pre>&lt;length file=&quot;bar&quot; property=&quot;length.bar&quot; /&gt;
</pre>
<p>Stores the length of file &quot;bar&quot; in the property named
<i>length.bar</i>.</p>

</body>
</html>

