<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>Replace Task</title>
</head>

<body>

<h2><a name="replace">Replace</a></h2>
<h3>Description</h3>
<p>Replace is a directory based task for replacing the occurrence of a given string with another string 
in selected file.</p>
<p>If you want to replace a text that crosses line boundaries, you
must use a nested <code>&lt;replacetoken&gt;</code> element.</p>

<p>The output file is only written if it differs from the existing
file.  This prevents spurious rebuilds based on unchanged files which
have been regenerated by this task.</p> 

<h3>Parameters</h3>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">file</td>
    <td valign="top">file for which the token should be replaced.</td>
    <td align="center" rowspan="2">Exactly one of the two.</td>
  </tr>
  <tr>
    <td valign="top">dir</td>
    <td valign="top">The base directory to use when replacing a token in 
      multiple files.</td>
  </tr>
  <tr>
    <td valign="top">encoding</td>
    <td valign="top">The encoding of the files upon which replace operates.</td>
    <td align="center">No - defaults to default JVM encoding</td>
  </tr>
  <tr>
    <td valign="top">token</td>
    <td valign="top">the token which must be replaced.</td>
    <td valign="top" align="center">Yes, unless a nested
    <code>replacetoken</code> element or the replacefilterfile
    attribute is used.</td>
  </tr>
  <tr>
    <td valign="top">value</td>
    <td valign="top">the new value for the token. When omitted, an empty string
      (&quot;&quot;) is used.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">summary</td>
    <td valign="top">Indicates whether a summary of the replace operation 
                     should be produced, detailing how many token occurrences 
                     and files were processed
                     </td>
    <td valign="top" align="center">No, by default no summary is produced</td>
  </tr>
  <tr>
    <td valign="top">propertyFile</td>
    <td valign="top">valid property file from which properties specified using nested <code>&lt;replacefilter&gt;</code> elements are drawn.</td>
    <td valign="top" align="center">Yes only if <i>property</i> attribute of <code>&lt;replacefilter&gt;</code> is used.</td>
  </tr>
  <tr>
    <td valign="top">replacefilterfile</td>
    <td valign="top">valid property file.  Each property will be
    treated as a replacefilter where <code>token</code> is the name of
    the property and <code>value</code> is the properties value.
    <td valign="top" align="center">No.</td>
  </tr>
  <tr>
    <td valign="top">includes</td>
    <td valign="top">comma- or space-separated list of patterns of files that must be
      included. All files are included when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">includesfile</td>
    <td valign="top">the name of a file. Each line of this file is
      taken to be an include pattern</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">excludes</td>
    <td valign="top">comma- or space-separated list of patterns of files that must be
      excluded. No files (except default excludes) are excluded when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">excludesfile</td>
    <td valign="top">the name of a file. Each line of this file is
      taken to be an exclude pattern</td>
    <td valign="top" align="center">No</td>
  </tr>
  <tr>
    <td valign="top">defaultexcludes</td>
    <td valign="top">indicates whether default excludes should be used or not
      (&quot;yes&quot;/&quot;no&quot;). Default excludes are used when omitted.</td>
    <td valign="top" align="center">No</td>
  </tr>
</table>
<h3>Examples</h3>
<pre>  &lt;replace file=&quot;${src}/index.html&quot; token=&quot;@@@&quot; value=&quot;wombat&quot;/&gt;</pre>
<p>replaces occurrences of the string &quot;@@@&quot; with the string
&quot;wombat&quot;, in the file <code>${src}/index.html</code>.</p>
<h3>Parameters specified as nested elements</h3>
<p>This task forms an implicit <a href="../CoreTypes/fileset.html">FileSet</a> and
supports all attributes of <code>&lt;fileset&gt;</code> as well as the
nested <code>&lt;include&gt;</code>, <code>&lt;exclude&gt;</code> and
<code>&lt;patternset&gt;</code> elements.</p>
<p>If either the text you want to replace or the replacement text
cross line boundaries, you can use nested elements to specify
them.</p>
<h3>Examples</h3>
<blockquote><pre>
&lt;replace dir=&quot;${src}&quot; value=&quot;wombat&quot;&gt;
  &lt;include name=&quot;**/*.html&quot;/&gt;
  &lt;replacetoken&gt;&lt;![CDATA[multi line
token]]&gt;&lt;/replacetoken&gt;
&lt;/replace&gt;
</pre></blockquote>
<p>replaces occurrences of the string &quot;multi
line<i>\n</i>token&quot; with the string &quot;wombat&quot;, in all
HTML files in the directory <code>${src}</code>.Where <i>\n</i> is
the platform specific line separator.</p>
<blockquote><pre>
&lt;replace file=&quot;${src}/index.html&quot;&gt;
  &lt;replacetoken&gt;&lt;![CDATA[two line
token]]&gt;&lt;/replacetoken&gt;
  &lt;replacevalue&gt;&lt;![CDATA[two line
token]]&gt;&lt;/replacevalue&gt;
&lt;/replace&gt;
</pre></blockquote>
<h4>replacefilter</h4>
<p>In addition to allowing for multiple replacements, optional nested <code>&lt;replacefilter&gt;</code> elements allow replacement values to be extracted from a property file. The name of this file is specified using the <code>&lt;replace&gt;</code> attribute <i>propertyFile</i>.
</p>
<table border="1" cellpadding="2" cellspacing="0">
  <tr>
    <td valign="top"><b>Attribute</b></td>
    <td valign="top"><b>Description</b></td>
    <td align="center" valign="top"><b>Required</b></td>
  </tr>
  <tr>
    <td valign="top">token</td>
    <td valign="top">The string to search for.</td>
    <td align="center" valign="top">Yes</td>
  </tr>
  <tr>
    <td valign="top">value</td>
    <td valign="top">The replacement string.</td>
    <td align="center" rowspan="2">Either may be specified, but not both. Both can be omitted, if desired.</td>
  </tr>
  <tr>
    <td valign="top">property</td>
    <td valign="top">Name of the property whose value is to serve as the replacement value.</td>
  </tr>
</table>
<p>If neither <i>value</i> nor <i>property</i> is used, the value provided using the <code>&lt;replace&gt;</code> attribute <i>value</i> and/or the <code>&lt;replacevalue&gt;</code> element is used. If no value was specified using either of these options, the token is replaced with an empty string.
</p>
<h3>Examples</h3>
<blockquote><pre>
&lt;replace 
    file=&quot;configure.sh&quot;
    value=&quot;defaultvalue&quot;
    propertyFile=&quot;source/name.properties&quot;&gt;
  &lt;replacefilter 
    token=&quot;@token1@&quot;/&gt;
  &lt;replacefilter 
    token=&quot;@token2@&quot; 
    value=&quot;value2&quot;/&gt;
  &lt;replacefilter 
    token=&quot;@token3@&quot; 
    property=&quot;property.key&quot;/&gt;
&lt;/replace&gt;
</pre></blockquote>
<p>In file <code>configure.sh</code>, replace all instances of &quot;@token1@&quot; with &quot;defaultvalue&quot;, all instances of &quot;@token2@&quot; with &quot;value2&quot;, and all instances of &quot;@token3@&quot; with the value of the property &quot;property.key&quot;, as it appears in property file <code>src/name.properties</code>.</p>
<p><b>Note:</b> It is possible to use either the <i>token</i>/<code>&lt;replacetoken&gt;</code> and <i>value</i>/<code>&lt;replacevalue&gt;</code> attributes/elements, the nested replacefilter elements, or both in the same operation.
</p>


</body>
</html>

