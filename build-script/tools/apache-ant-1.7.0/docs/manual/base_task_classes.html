<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>Tasks Designed for Extension</title>
</head>

<body>
<h1>Tasks Designed for Extension</h1>

<p>These classes are designed to be extended.  Always start here when writing your own task.</p>

<p><strong>The links will not work in the online version of this document.</strong></p>

<table border="1">
<thead>
<tr>
<th>
Class
</th>
<th>
Description
</th>
</tr>
</thead>
<tbody>
<tr>
<td>
<a href="api/org/apache/tools/ant/Task.html">Task</a>
</td>
<td>
Base class for all tasks.
</td>
</tr>

<tr>
<td>
<a href="api/org/apache/tools/ant/taskdefs/AbstractCvsTask.html">AbstractCvsTask</a>
</td>
<td>
Another task can extend this with some customized output processing
</td>
</tr>

<tr>
<td>
<a href="api/org/apache/tools/ant/taskdefs/JDBCTask.html">JDBCTask</a>
</td>
<td>
Handles JDBC configuration needed by SQL type tasks.
</td>
</tr>

<tr>
<td>
<a href="api/org/apache/tools/ant/taskdefs/MatchingTask.html">MatchingTask</a>
</td>
<td>
This is an abstract task that should be used by all those tasks that require to include or exclude files based on pattern matching.
</td>
</tr>

<tr>
<td>
<a href="api/org/apache/tools/ant/taskdefs/Pack.html">Pack</a>
</td>
<td>
Abstract Base class for pack tasks.
</td>
</tr>


<tr>
<td>
<a href="api/org/apache/tools/ant/taskdefs/Unpack.html">Unpack</a>
</td>
<td>
Abstract Base class for unpack tasks.
</td>
</tr>

<tr>
<td>
<a href="api/org/apache/tools/ant/dispatch/DispatchTask.html">DispatchTask</a>
</td>
<td>
Abstract Base class for tasks that may have multiple actions.
</td>
</tr>

</tbody>
</table>



</body>
</html>
