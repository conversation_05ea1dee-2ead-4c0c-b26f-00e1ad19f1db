<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:19 EST 2006 -->
<TITLE>
AntClassLoader (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.AntClassLoader class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="AntClassLoader (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV CLASS&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/AntClassLoader.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="AntClassLoader.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class AntClassLoader</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by ">java.lang.ClassLoader
      <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.AntClassLoader</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.util.EventListener, <A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A>, <A HREF="../../../../org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</A></DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../org/apache/tools/ant/loader/AntClassLoader2.html" title="class in org.apache.tools.ant.loader">AntClassLoader2</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>AntClassLoader</B><DT>extends java.lang.ClassLoader<DT>implements <A HREF="../../../../org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</A></DL>
</PRE>

<P>
Used to load classes within ant with a different classpath from
 that used to start ant. Note that it is possible to force a class
 into this loader even when that class is on the system classpath by
 using the forceLoadClass method. Any subsequent classes loaded by that
 class will then use this loader rather than the system class loader.

 <p>
 Note that this classloader has a feature to allow loading
 in reverse order and for "isolation".
 Due to the fact that a number of
 methods in java.lang.ClassLoader are final (at least
 in java 1.4 getResources) this means that the
 class has to fake the given parent.
 </p>
<P>

<P>
<HR>

<P>

<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#AntClassLoader()">AntClassLoader</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create an Ant Class Loader</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#AntClassLoader(java.lang.ClassLoader, boolean)">AntClassLoader</A></B>(java.lang.ClassLoader&nbsp;parent,
               boolean&nbsp;parentFirst)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates an empty class loader.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#AntClassLoader(java.lang.ClassLoader, org.apache.tools.ant.Project, org.apache.tools.ant.types.Path)">AntClassLoader</A></B>(java.lang.ClassLoader&nbsp;parent,
               <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
               <A HREF="../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create an Ant ClassLoader for a given project, with
 a parent classloader and an initial classpath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#AntClassLoader(java.lang.ClassLoader, org.apache.tools.ant.Project, org.apache.tools.ant.types.Path, boolean)">AntClassLoader</A></B>(java.lang.ClassLoader&nbsp;parent,
               <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
               <A HREF="../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath,
               boolean&nbsp;parentFirst)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a classloader for the given project using the classpath given.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#AntClassLoader(org.apache.tools.ant.Project, org.apache.tools.ant.types.Path)">AntClassLoader</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
               <A HREF="../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a classloader for the given project using the classpath given.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#AntClassLoader(org.apache.tools.ant.Project, org.apache.tools.ant.types.Path, boolean)">AntClassLoader</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
               <A HREF="../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath,
               boolean&nbsp;parentFirst)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a classloader for the given project using the classpath given.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#addJavaLibraries()">addJavaLibraries</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add any libraries that come with different java versions
 here</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#addLoaderPackageRoot(java.lang.String)">addLoaderPackageRoot</A></B>(java.lang.String&nbsp;packageRoot)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a package root to the list of packages which must be loaded using
 this loader.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#addPathElement(java.lang.String)">addPathElement</A></B>(java.lang.String&nbsp;pathElement)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds an element to the classpath to be searched.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#addPathFile(java.io.File)">addPathFile</A></B>(java.io.File&nbsp;pathComponent)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a file to the path.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#addSystemPackageRoot(java.lang.String)">addSystemPackageRoot</A></B>(java.lang.String&nbsp;packageRoot)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a package root to the list of packages which must be loaded on the
 parent loader.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#buildFinished(org.apache.tools.ant.BuildEvent)">buildFinished</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Cleans up any resources held by this classloader at the end
 of a build.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#buildStarted(org.apache.tools.ant.BuildEvent)">buildStarted</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Empty implementation to satisfy the BuildListener interface.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#cleanup()">cleanup</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Cleans up any resources held by this classloader.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.Class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#defineClassFromData(java.io.File, byte[], java.lang.String)">defineClassFromData</A></B>(java.io.File&nbsp;container,
                    byte[]&nbsp;classData,
                    java.lang.String&nbsp;classname)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Define a class given its bytes</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#definePackage(java.io.File, java.lang.String)">definePackage</A></B>(java.io.File&nbsp;container,
              java.lang.String&nbsp;className)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Define the package information associated with a class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#definePackage(java.io.File, java.lang.String, java.util.jar.Manifest)">definePackage</A></B>(java.io.File&nbsp;container,
              java.lang.String&nbsp;packageName,
              java.util.jar.Manifest&nbsp;manifest)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Define the package information when the class comes from a
 jar with a manifest</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#findClass(java.lang.String)">findClass</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Searches for and load a class on the classpath of this class loader.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Enumeration</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#findResources(java.lang.String)">findResources</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns an enumeration of URLs representing all the resources with the
 given name by searching the class loader's classpath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#forceLoadClass(java.lang.String)">forceLoadClass</A></B>(java.lang.String&nbsp;classname)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Loads a class through this class loader even if that class is available
 on the parent classpath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#forceLoadSystemClass(java.lang.String)">forceLoadSystemClass</A></B>(java.lang.String&nbsp;classname)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Loads a class through this class loader but defer to the parent class
 loader.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#getClasspath()">getClasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the classpath this classloader will consult.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.net.URL</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#getResource(java.lang.String)">getResource</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Finds the resource with the given name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.InputStream</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#getResourceAsStream(java.lang.String)">getResourceAsStream</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns a stream to read the requested resource name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.net.URL</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#getResourceURL(java.io.File, java.lang.String)">getResourceURL</A></B>(java.io.File&nbsp;file,
               java.lang.String&nbsp;resourceName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the URL of a given resource in the given file which may
 either be a directory or a zip file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#initializeClass(java.lang.Class)">initializeClass</A></B>(java.lang.Class&nbsp;theClass)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             Use Class.forName with initialize=true instead.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#isInPath(java.io.File)">isInPath</A></B>(java.io.File&nbsp;component)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicate if the given file is in this loader's path</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.Class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#loadClass(java.lang.String, boolean)">loadClass</A></B>(java.lang.String&nbsp;classname,
          boolean&nbsp;resolve)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Loads a class with this class loader.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#log(java.lang.String, int)">log</A></B>(java.lang.String&nbsp;message,
    int&nbsp;priority)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logs a message through the project object if one has been provided.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Empty implementation to satisfy the BuildListener interface.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#resetThreadContextLoader()">resetThreadContextLoader</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Resets the current thread's context loader to its original value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#setClassPath(org.apache.tools.ant.types.Path)">setClassPath</A></B>(<A HREF="../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the classpath to search for classes to load.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#setIsolated(boolean)">setIsolated</A></B>(boolean&nbsp;isolated)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets whether this classloader should run in isolated mode.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#setParent(java.lang.ClassLoader)">setParent</A></B>(java.lang.ClassLoader&nbsp;parent)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the parent for this class loader.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#setParentFirst(boolean)">setParentFirst</A></B>(boolean&nbsp;parentFirst)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Control whether class lookup is delegated to the parent loader first
 or after this loader.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#setProject(org.apache.tools.ant.Project)">setProject</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the project associated with this class loader</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#setThreadContextLoader()">setThreadContextLoader</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the current thread's context loader to this classloader, storing
 the current loader value for later resetting.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#subBuildFinished(org.apache.tools.ant.BuildEvent)">subBuildFinished</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Cleans up any resources held by this classloader at the end of
 a subbuild if it has been created for the subbuild's project
 instance.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#subBuildStarted(org.apache.tools.ant.BuildEvent)">subBuildStarted</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Empty implementation to satisfy the BuildListener interface.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Empty implementation to satisfy the BuildListener interface.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Empty implementation to satisfy the BuildListener interface.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#taskFinished(org.apache.tools.ant.BuildEvent)">taskFinished</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Empty implementation to satisfy the BuildListener interface.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#taskStarted(org.apache.tools.ant.BuildEvent)">taskStarted</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Empty implementation to satisfy the BuildListener interface.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html#toString()">toString</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns a <code>String</code> representing this loader.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.ClassLoader"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.ClassLoader</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clearAssertionStatus, defineClass, defineClass, defineClass, defineClass, definePackage, findLibrary, findLoadedClass, findResource, findSystemClass, getPackage, getPackages, getParent, getResources, getSystemClassLoader, getSystemResource, getSystemResourceAsStream, getSystemResources, loadClass, resolveClass, setClassAssertionStatus, setDefaultAssertionStatus, setPackageAssertionStatus, setSigners</CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="AntClassLoader(java.lang.ClassLoader, org.apache.tools.ant.Project, org.apache.tools.ant.types.Path)"><!-- --></A><H3>
AntClassLoader</H3>
<PRE>
public <B>AntClassLoader</B>(java.lang.ClassLoader&nbsp;parent,
                      <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                      <A HREF="../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath)</PRE>
<DL>
<DD>Create an Ant ClassLoader for a given project, with
 a parent classloader and an initial classpath.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>parent</CODE> - the parent for this classloader.<DD><CODE>project</CODE> - The project to which this classloader is to
                belong.<DD><CODE>classpath</CODE> - The classpath to use to load classes.<DT><B>Since:</B></DT>
  <DD>Ant 1.7.</DD>
</DL>
</DL>
<HR>

<A NAME="AntClassLoader()"><!-- --></A><H3>
AntClassLoader</H3>
<PRE>
public <B>AntClassLoader</B>()</PRE>
<DL>
<DD>Create an Ant Class Loader
<P>
</DL>
<HR>

<A NAME="AntClassLoader(org.apache.tools.ant.Project, org.apache.tools.ant.types.Path)"><!-- --></A><H3>
AntClassLoader</H3>
<PRE>
public <B>AntClassLoader</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                      <A HREF="../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath)</PRE>
<DL>
<DD>Creates a classloader for the given project using the classpath given.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - The project to which this classloader is to belong.
                Must not be <code>null</code>.<DD><CODE>classpath</CODE> - The classpath to use to load the classes.  This
                is combined with the system classpath in a manner
                determined by the value of ${build.sysclasspath}.
                May be <code>null</code>, in which case no path
                elements are set up to start with.</DL>
</DL>
<HR>

<A NAME="AntClassLoader(java.lang.ClassLoader, org.apache.tools.ant.Project, org.apache.tools.ant.types.Path, boolean)"><!-- --></A><H3>
AntClassLoader</H3>
<PRE>
public <B>AntClassLoader</B>(java.lang.ClassLoader&nbsp;parent,
                      <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                      <A HREF="../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath,
                      boolean&nbsp;parentFirst)</PRE>
<DL>
<DD>Creates a classloader for the given project using the classpath given.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>parent</CODE> - The parent classloader to which unsatisfied loading
               attempts are delegated. May be <code>null</code>,
               in which case the classloader which loaded this
               class is used as the parent.<DD><CODE>project</CODE> - The project to which this classloader is to belong.
                Must not be <code>null</code>.<DD><CODE>classpath</CODE> - the classpath to use to load the classes.
                  May be <code>null</code>, in which case no path
                  elements are set up to start with.<DD><CODE>parentFirst</CODE> - If <code>true</code>, indicates that the parent
                    classloader should be consulted  before trying to
                    load the a class through this loader.</DL>
</DL>
<HR>

<A NAME="AntClassLoader(org.apache.tools.ant.Project, org.apache.tools.ant.types.Path, boolean)"><!-- --></A><H3>
AntClassLoader</H3>
<PRE>
public <B>AntClassLoader</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                      <A HREF="../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath,
                      boolean&nbsp;parentFirst)</PRE>
<DL>
<DD>Creates a classloader for the given project using the classpath given.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - The project to which this classloader is to belong.
                Must not be <code>null</code>.<DD><CODE>classpath</CODE> - The classpath to use to load the classes. May be
                  <code>null</code>, in which case no path
                  elements are set up to start with.<DD><CODE>parentFirst</CODE> - If <code>true</code>, indicates that the parent
                    classloader should be consulted before trying to
                    load the a class through this loader.</DL>
</DL>
<HR>

<A NAME="AntClassLoader(java.lang.ClassLoader, boolean)"><!-- --></A><H3>
AntClassLoader</H3>
<PRE>
public <B>AntClassLoader</B>(java.lang.ClassLoader&nbsp;parent,
                      boolean&nbsp;parentFirst)</PRE>
<DL>
<DD>Creates an empty class loader. The classloader should be configured
 with path elements to specify where the loader is to look for
 classes.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>parent</CODE> - The parent classloader to which unsatisfied loading
               attempts are delegated. May be <code>null</code>,
               in which case the classloader which loaded this
               class is used as the parent.<DD><CODE>parentFirst</CODE> - If <code>true</code>, indicates that the parent
                    classloader should be consulted before trying to
                    load the a class through this loader.</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setProject(org.apache.tools.ant.Project)"><!-- --></A><H3>
setProject</H3>
<PRE>
public void <B>setProject</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Set the project associated with this class loader
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the project instance</DL>
</DD>
</DL>
<HR>

<A NAME="setClassPath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setClassPath</H3>
<PRE>
public void <B>setClassPath</B>(<A HREF="../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath)</PRE>
<DL>
<DD>Set the classpath to search for classes to load. This should not be
 changed once the classloader starts to server classes
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classpath</CODE> - the search classpath consisting of directories and
        jar/zip files.</DL>
</DD>
</DL>
<HR>

<A NAME="setParent(java.lang.ClassLoader)"><!-- --></A><H3>
setParent</H3>
<PRE>
public void <B>setParent</B>(java.lang.ClassLoader&nbsp;parent)</PRE>
<DL>
<DD>Set the parent for this class loader. This is the class loader to which
 this class loader will delegate to load classes
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>parent</CODE> - the parent class loader.</DL>
</DD>
</DL>
<HR>

<A NAME="setParentFirst(boolean)"><!-- --></A><H3>
setParentFirst</H3>
<PRE>
public void <B>setParentFirst</B>(boolean&nbsp;parentFirst)</PRE>
<DL>
<DD>Control whether class lookup is delegated to the parent loader first
 or after this loader. Use with extreme caution. Setting this to
 false violates the class loader hierarchy and can lead to Linkage errors
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>parentFirst</CODE> - if true, delegate initial class search to the parent
                    classloader.</DL>
</DD>
</DL>
<HR>

<A NAME="log(java.lang.String, int)"><!-- --></A><H3>
log</H3>
<PRE>
protected void <B>log</B>(java.lang.String&nbsp;message,
                   int&nbsp;priority)</PRE>
<DL>
<DD>Logs a message through the project object if one has been provided.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>message</CODE> - The message to log.
                Should not be <code>null</code>.<DD><CODE>priority</CODE> - The logging priority of the message.</DL>
</DD>
</DL>
<HR>

<A NAME="setThreadContextLoader()"><!-- --></A><H3>
setThreadContextLoader</H3>
<PRE>
public void <B>setThreadContextLoader</B>()</PRE>
<DL>
<DD>Sets the current thread's context loader to this classloader, storing
 the current loader value for later resetting.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="resetThreadContextLoader()"><!-- --></A><H3>
resetThreadContextLoader</H3>
<PRE>
public void <B>resetThreadContextLoader</B>()</PRE>
<DL>
<DD>Resets the current thread's context loader to its original value.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="addPathElement(java.lang.String)"><!-- --></A><H3>
addPathElement</H3>
<PRE>
public void <B>addPathElement</B>(java.lang.String&nbsp;pathElement)
                    throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Adds an element to the classpath to be searched.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pathElement</CODE> - The path element to add. Must not be
                    <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the given path element cannot be resolved
                           against the project.</DL>
</DD>
</DL>
<HR>

<A NAME="addPathFile(java.io.File)"><!-- --></A><H3>
addPathFile</H3>
<PRE>
protected void <B>addPathFile</B>(java.io.File&nbsp;pathComponent)
                    throws java.io.IOException</PRE>
<DL>
<DD>Add a file to the path.
 Reads the manifest, if available, and adds any additional class path jars
 specified in the manifest.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pathComponent</CODE> - the file which is to be added to the path for
                      this class loader
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if data needed from the file cannot be read.</DL>
</DD>
</DL>
<HR>

<A NAME="getClasspath()"><!-- --></A><H3>
getClasspath</H3>
<PRE>
public java.lang.String <B>getClasspath</B>()</PRE>
<DL>
<DD>Returns the classpath this classloader will consult.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the classpath used for this classloader, with elements
         separated by the path separator for the system.</DL>
</DD>
</DL>
<HR>

<A NAME="setIsolated(boolean)"><!-- --></A><H3>
setIsolated</H3>
<PRE>
public void <B>setIsolated</B>(boolean&nbsp;isolated)</PRE>
<DL>
<DD>Sets whether this classloader should run in isolated mode. In
 isolated mode, classes not found on the given classpath will
 not be referred to the parent class loader but will cause a
 ClassNotFoundException.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>isolated</CODE> - Whether or not this classloader should run in
                 isolated mode.</DL>
</DD>
</DL>
<HR>

<A NAME="initializeClass(java.lang.Class)"><!-- --></A><H3>
initializeClass</H3>
<PRE>
public static void <B>initializeClass</B>(java.lang.Class&nbsp;theClass)</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             Use Class.forName with initialize=true instead.</I>
<P>
<DD>Forces initialization of a class in a JDK 1.1 compatible, albeit hacky
 way.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>theClass</CODE> - The class to initialize.
                 Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="addSystemPackageRoot(java.lang.String)"><!-- --></A><H3>
addSystemPackageRoot</H3>
<PRE>
public void <B>addSystemPackageRoot</B>(java.lang.String&nbsp;packageRoot)</PRE>
<DL>
<DD>Adds a package root to the list of packages which must be loaded on the
 parent loader.

 All subpackages are also included.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>packageRoot</CODE> - The root of all packages to be included.
                    Should not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="addLoaderPackageRoot(java.lang.String)"><!-- --></A><H3>
addLoaderPackageRoot</H3>
<PRE>
public void <B>addLoaderPackageRoot</B>(java.lang.String&nbsp;packageRoot)</PRE>
<DL>
<DD>Adds a package root to the list of packages which must be loaded using
 this loader.

 All subpackages are also included.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>packageRoot</CODE> - The root of all packages to be included.
                    Should not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="forceLoadClass(java.lang.String)"><!-- --></A><H3>
forceLoadClass</H3>
<PRE>
public java.lang.Class <B>forceLoadClass</B>(java.lang.String&nbsp;classname)
                               throws java.lang.ClassNotFoundException</PRE>
<DL>
<DD>Loads a class through this class loader even if that class is available
 on the parent classpath.

 This ensures that any classes which are loaded by the returned class
 will use this classloader.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classname</CODE> - The name of the class to be loaded.
                  Must not be <code>null</code>.
<DT><B>Returns:</B><DD>the required Class object
<DT><B>Throws:</B>
<DD><CODE>java.lang.ClassNotFoundException</CODE> - if the requested class does not exist
                                   on this loader's classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="forceLoadSystemClass(java.lang.String)"><!-- --></A><H3>
forceLoadSystemClass</H3>
<PRE>
public java.lang.Class <B>forceLoadSystemClass</B>(java.lang.String&nbsp;classname)
                                     throws java.lang.ClassNotFoundException</PRE>
<DL>
<DD>Loads a class through this class loader but defer to the parent class
 loader.

 This ensures that instances of the returned class will be compatible
 with instances which have already been loaded on the parent
 loader.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classname</CODE> - The name of the class to be loaded.
                  Must not be <code>null</code>.
<DT><B>Returns:</B><DD>the required Class object
<DT><B>Throws:</B>
<DD><CODE>java.lang.ClassNotFoundException</CODE> - if the requested class does not exist
 on this loader's classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="getResourceAsStream(java.lang.String)"><!-- --></A><H3>
getResourceAsStream</H3>
<PRE>
public java.io.InputStream <B>getResourceAsStream</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Returns a stream to read the requested resource name.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>getResourceAsStream</CODE> in class <CODE>java.lang.ClassLoader</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - The name of the resource for which a stream is required.
             Must not be <code>null</code>.
<DT><B>Returns:</B><DD>a stream to the required resource or <code>null</code> if the
         resource cannot be found on the loader's classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="getResource(java.lang.String)"><!-- --></A><H3>
getResource</H3>
<PRE>
public java.net.URL <B>getResource</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Finds the resource with the given name. A resource is
 some data (images, audio, text, etc) that can be accessed by class
 code in a way that is independent of the location of the code.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>getResource</CODE> in class <CODE>java.lang.ClassLoader</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - The name of the resource for which a stream is required.
             Must not be <code>null</code>.
<DT><B>Returns:</B><DD>a URL for reading the resource, or <code>null</code> if the
         resource could not be found or the caller doesn't have
         adequate privileges to get the resource.</DL>
</DD>
</DL>
<HR>

<A NAME="findResources(java.lang.String)"><!-- --></A><H3>
findResources</H3>
<PRE>
protected java.util.Enumeration <B>findResources</B>(java.lang.String&nbsp;name)
                                       throws java.io.IOException</PRE>
<DL>
<DD>Returns an enumeration of URLs representing all the resources with the
 given name by searching the class loader's classpath.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>findResources</CODE> in class <CODE>java.lang.ClassLoader</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - The resource name to search for.
             Must not be <code>null</code>.
<DT><B>Returns:</B><DD>an enumeration of URLs for the resources
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if I/O errors occurs (can't happen)</DL>
</DD>
</DL>
<HR>

<A NAME="getResourceURL(java.io.File, java.lang.String)"><!-- --></A><H3>
getResourceURL</H3>
<PRE>
protected java.net.URL <B>getResourceURL</B>(java.io.File&nbsp;file,
                                      java.lang.String&nbsp;resourceName)</PRE>
<DL>
<DD>Returns the URL of a given resource in the given file which may
 either be a directory or a zip file.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>file</CODE> - The file (directory or jar) in which to search for
             the resource. Must not be <code>null</code>.<DD><CODE>resourceName</CODE> - The name of the resource for which a stream
                     is required. Must not be <code>null</code>.
<DT><B>Returns:</B><DD>a stream to the required resource or <code>null</code> if the
         resource cannot be found in the given file object.</DL>
</DD>
</DL>
<HR>

<A NAME="loadClass(java.lang.String, boolean)"><!-- --></A><H3>
loadClass</H3>
<PRE>
protected java.lang.Class <B>loadClass</B>(java.lang.String&nbsp;classname,
                                    boolean&nbsp;resolve)
                             throws java.lang.ClassNotFoundException</PRE>
<DL>
<DD>Loads a class with this class loader.

 This class attempts to load the class in an order determined by whether
 or not the class matches the system/loader package lists, with the
 loader package list taking priority. If the classloader is in isolated
 mode, failure to load the class in this loader will result in a
 ClassNotFoundException.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>loadClass</CODE> in class <CODE>java.lang.ClassLoader</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classname</CODE> - The name of the class to be loaded.
                  Must not be <code>null</code>.<DD><CODE>resolve</CODE> - <code>true</code> if all classes upon which this class
                depends are to be loaded.
<DT><B>Returns:</B><DD>the required Class object
<DT><B>Throws:</B>
<DD><CODE>java.lang.ClassNotFoundException</CODE> - if the requested class does not exist
 on the system classpath (when not in isolated mode) or this loader's
 classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="defineClassFromData(java.io.File, byte[], java.lang.String)"><!-- --></A><H3>
defineClassFromData</H3>
<PRE>
protected java.lang.Class <B>defineClassFromData</B>(java.io.File&nbsp;container,
                                              byte[]&nbsp;classData,
                                              java.lang.String&nbsp;classname)
                                       throws java.io.IOException</PRE>
<DL>
<DD>Define a class given its bytes
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>container</CODE> - the container from which the class data has been read
                  may be a directory or a jar/zip file.<DD><CODE>classData</CODE> - the bytecode data for the class<DD><CODE>classname</CODE> - the name of the class
<DT><B>Returns:</B><DD>the Class instance created from the given data
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the class data cannot be read.</DL>
</DD>
</DL>
<HR>

<A NAME="definePackage(java.io.File, java.lang.String)"><!-- --></A><H3>
definePackage</H3>
<PRE>
protected void <B>definePackage</B>(java.io.File&nbsp;container,
                             java.lang.String&nbsp;className)
                      throws java.io.IOException</PRE>
<DL>
<DD>Define the package information associated with a class.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>container</CODE> - the file containing the class definition.<DD><CODE>className</CODE> - the class name of for which the package information
        is to be determined.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the package information cannot be read from the
            container.</DL>
</DD>
</DL>
<HR>

<A NAME="definePackage(java.io.File, java.lang.String, java.util.jar.Manifest)"><!-- --></A><H3>
definePackage</H3>
<PRE>
protected void <B>definePackage</B>(java.io.File&nbsp;container,
                             java.lang.String&nbsp;packageName,
                             java.util.jar.Manifest&nbsp;manifest)</PRE>
<DL>
<DD>Define the package information when the class comes from a
 jar with a manifest
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>container</CODE> - the jar file containing the manifest<DD><CODE>packageName</CODE> - the name of the package being defined.<DD><CODE>manifest</CODE> - the jar's manifest</DL>
</DD>
</DL>
<HR>

<A NAME="findClass(java.lang.String)"><!-- --></A><H3>
findClass</H3>
<PRE>
public java.lang.Class <B>findClass</B>(java.lang.String&nbsp;name)
                          throws java.lang.ClassNotFoundException</PRE>
<DL>
<DD>Searches for and load a class on the classpath of this class loader.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>findClass</CODE> in class <CODE>java.lang.ClassLoader</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - The name of the class to be loaded. Must not be
             <code>null</code>.
<DT><B>Returns:</B><DD>the required Class object
<DT><B>Throws:</B>
<DD><CODE>java.lang.ClassNotFoundException</CODE> - if the requested class does not exist
                                   on this loader's classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="isInPath(java.io.File)"><!-- --></A><H3>
isInPath</H3>
<PRE>
protected boolean <B>isInPath</B>(java.io.File&nbsp;component)</PRE>
<DL>
<DD>Indicate if the given file is in this loader's path
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>component</CODE> - the file which is to be checked
<DT><B>Returns:</B><DD>true if the file is in the class path</DL>
</DD>
</DL>
<HR>

<A NAME="cleanup()"><!-- --></A><H3>
cleanup</H3>
<PRE>
public void <B>cleanup</B>()</PRE>
<DL>
<DD>Cleans up any resources held by this classloader. Any open archive
 files are closed.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="buildStarted(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
buildStarted</H3>
<PRE>
public void <B>buildStarted</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Empty implementation to satisfy the BuildListener interface.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#buildStarted(org.apache.tools.ant.BuildEvent)">buildStarted</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - the buildStarted event</DL>
</DD>
</DL>
<HR>

<A NAME="buildFinished(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
buildFinished</H3>
<PRE>
public void <B>buildFinished</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Cleans up any resources held by this classloader at the end
 of a build.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#buildFinished(org.apache.tools.ant.BuildEvent)">buildFinished</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - the buildFinished event<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getException()"><CODE>BuildEvent.getException()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="subBuildFinished(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
subBuildFinished</H3>
<PRE>
public void <B>subBuildFinished</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Cleans up any resources held by this classloader at the end of
 a subbuild if it has been created for the subbuild's project
 instance.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/SubBuildListener.html#subBuildFinished(org.apache.tools.ant.BuildEvent)">subBuildFinished</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - the buildFinished event<DT><B>Since:</B></DT>
  <DD>Ant 1.6.2</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getException()"><CODE>BuildEvent.getException()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="subBuildStarted(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
subBuildStarted</H3>
<PRE>
public void <B>subBuildStarted</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Empty implementation to satisfy the BuildListener interface.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/SubBuildListener.html#subBuildStarted(org.apache.tools.ant.BuildEvent)">subBuildStarted</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - the buildStarted event<DT><B>Since:</B></DT>
  <DD>Ant 1.6.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="targetStarted(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
targetStarted</H3>
<PRE>
public void <B>targetStarted</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Empty implementation to satisfy the BuildListener interface.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - the targetStarted event<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getTarget()"><CODE>BuildEvent.getTarget()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="targetFinished(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
targetFinished</H3>
<PRE>
public void <B>targetFinished</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Empty implementation to satisfy the BuildListener interface.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - the targetFinished event<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getException()"><CODE>BuildEvent.getException()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="taskStarted(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
taskStarted</H3>
<PRE>
public void <B>taskStarted</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Empty implementation to satisfy the BuildListener interface.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#taskStarted(org.apache.tools.ant.BuildEvent)">taskStarted</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - the taskStarted event<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getTask()"><CODE>BuildEvent.getTask()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="taskFinished(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
taskFinished</H3>
<PRE>
public void <B>taskFinished</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Empty implementation to satisfy the BuildListener interface.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#taskFinished(org.apache.tools.ant.BuildEvent)">taskFinished</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - the taskFinished event<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getException()"><CODE>BuildEvent.getException()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="messageLogged(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
messageLogged</H3>
<PRE>
public void <B>messageLogged</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Empty implementation to satisfy the BuildListener interface.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - the messageLogged event<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getMessage()"><CODE>BuildEvent.getMessage()</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getException()"><CODE>BuildEvent.getException()</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getPriority()"><CODE>BuildEvent.getPriority()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="addJavaLibraries()"><!-- --></A><H3>
addJavaLibraries</H3>
<PRE>
public void <B>addJavaLibraries</B>()</PRE>
<DL>
<DD>add any libraries that come with different java versions
 here
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="toString()"><!-- --></A><H3>
toString</H3>
<PRE>
public java.lang.String <B>toString</B>()</PRE>
<DL>
<DD>Returns a <code>String</code> representing this loader.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>toString</CODE> in class <CODE>java.lang.Object</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the path that this classloader has.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV CLASS&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/AntClassLoader.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="AntClassLoader.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
