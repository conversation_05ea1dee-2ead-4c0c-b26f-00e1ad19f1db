<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:24 EST 2006 -->
<TITLE>
SQLExec (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.SQLExec class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="SQLExec (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Sleep.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.DelimiterType.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/SQLExec.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="SQLExec.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.Task">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class SQLExec</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.JDBCTask</A>
              <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.SQLExec</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>SQLExec</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html" title="class in org.apache.tools.ant.taskdefs">JDBCTask</A></DL>
</PRE>

<P>
Executes a series of SQL statements on a database using JDBC.

 <p>Statements can
 either be read in from a text file using the <i>src</i> attribute or from
 between the enclosing SQL tags.</p>

 <p>Multiple statements can be provided, separated by semicolons (or the
 defined <i>delimiter</i>). Individual lines within the statements can be
 commented using either --, // or REM at the start of the line.</p>

 <p>The <i>autocommit</i> attribute specifies whether auto-commit should be
 turned on or off whilst executing the statements. If auto-commit is turned
 on each statement will be executed and committed. If it is turned off the
 statements will all be executed as one transaction.</p>

 <p>The <i>onerror</i> attribute specifies how to proceed when an error occurs
 during the execution of one of the statements.
 The possible values are: <b>continue</b> execution, only show the error;
 <b>stop</b> execution and commit transaction;
 and <b>abort</b> execution and transaction and fail task.</p>
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
</DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.DelimiterType.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.DelimiterType</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;delimiters we support, "normal" and "row"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.OnError.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.OnError</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The action a task should perform on an error,
 one of "continue", "stop" and "abort"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.Transaction.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.Transaction</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Contains the definition of a new transaction element.</TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#SQLExec()">SQLExec</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#add(org.apache.tools.ant.types.ResourceCollection)">add</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;rc)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a collection of resources (nested element).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;set)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a set of files (nested fileset attribute).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#addText(java.lang.String)">addText</A></B>(java.lang.String&nbsp;sql)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set an inline SQL command to execute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.Transaction.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.Transaction</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#createTransaction()">createTransaction</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a SQL transaction to execute</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#execSQL(java.lang.String, java.io.PrintStream)">execSQL</A></B>(java.lang.String&nbsp;sql,
        java.io.PrintStream&nbsp;out)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Exec the sql statement.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Load the sql file and then execute it</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#getExpandProperties()">getExpandProperties</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;is property expansion inside inline text enabled?</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#printResults(java.io.PrintStream)">printResults</A></B>(java.io.PrintStream&nbsp;out)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             Use <A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#printResults(java.sql.ResultSet, java.io.PrintStream)"><CODE>the two arg version</CODE></A> instead.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#printResults(java.sql.ResultSet, java.io.PrintStream)">printResults</A></B>(java.sql.ResultSet&nbsp;rs,
             java.io.PrintStream&nbsp;out)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;print any results in the result set.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#runStatements(java.io.Reader, java.io.PrintStream)">runStatements</A></B>(java.io.Reader&nbsp;reader,
              java.io.PrintStream&nbsp;out)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;read in lines and execute them</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#setAppend(boolean)">setAppend</A></B>(boolean&nbsp;append)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;whether output should be appended to or overwrite
 an existing file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#setDelimiter(java.lang.String)">setDelimiter</A></B>(java.lang.String&nbsp;delimiter)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the delimiter that separates SQL statements.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#setDelimiterType(org.apache.tools.ant.taskdefs.SQLExec.DelimiterType)">setDelimiterType</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.DelimiterType.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.DelimiterType</A>&nbsp;delimiterType)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the delimiter type: "normal" or "row" (default "normal").</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#setEncoding(java.lang.String)">setEncoding</A></B>(java.lang.String&nbsp;encoding)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the file encoding to use on the SQL files read in</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#setEscapeProcessing(boolean)">setEscapeProcessing</A></B>(boolean&nbsp;enable)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set escape processing for statements.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#setExpandProperties(boolean)">setExpandProperties</A></B>(boolean&nbsp;expandProperties)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Enable property expansion inside nested text</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#setKeepformat(boolean)">setKeepformat</A></B>(boolean&nbsp;keepformat)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;whether or not format should be preserved.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#setOnerror(org.apache.tools.ant.taskdefs.SQLExec.OnError)">setOnerror</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.OnError.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.OnError</A>&nbsp;action)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Action to perform when statement fails: continue, stop, or abort
 optional; default &quot;abort&quot;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#setOutput(java.io.File)">setOutput</A></B>(java.io.File&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the output file;
 optional, defaults to the Ant log.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#setPrint(boolean)">setPrint</A></B>(boolean&nbsp;print)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Print result sets from the statements;
 optional, default false</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#setShowheaders(boolean)">setShowheaders</A></B>(boolean&nbsp;showheaders)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Print headers for result sets from the
 statements; optional, default true.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#setShowtrailers(boolean)">setShowtrailers</A></B>(boolean&nbsp;showtrailers)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Print trailing info (rows affected) for the SQL
 Addresses Bug/Request #27446</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#setSrc(java.io.File)">setSrc</A></B>(java.io.File&nbsp;srcFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the name of the SQL file to be run.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.JDBCTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html" title="class in org.apache.tools.ant.taskdefs">JDBCTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#createClasspath()">createClasspath</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#getClasspath()">getClasspath</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#getConnection()">getConnection</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#getLoader()">getLoader</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#getLoaderMap()">getLoaderMap</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#getPassword()">getPassword</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#getRdbms()">getRdbms</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#getUrl()">getUrl</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#getUserId()">getUserId</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#getVersion()">getVersion</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#isAutocommit()">isAutocommit</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#isCaching(boolean)">isCaching</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#isValidRdbms(java.sql.Connection)">isValidRdbms</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#setAutocommit(boolean)">setAutocommit</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#setCaching(boolean)">setCaching</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#setClasspathRef(org.apache.tools.ant.types.Reference)">setClasspathRef</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#setDriver(java.lang.String)">setDriver</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#setPassword(java.lang.String)">setPassword</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#setRdbms(java.lang.String)">setRdbms</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#setUrl(java.lang.String)">setUrl</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#setUserid(java.lang.String)">setUserid</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html#setVersion(java.lang.String)">setVersion</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="SQLExec()"><!-- --></A><H3>
SQLExec</H3>
<PRE>
public <B>SQLExec</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setSrc(java.io.File)"><!-- --></A><H3>
setSrc</H3>
<PRE>
public void <B>setSrc</B>(java.io.File&nbsp;srcFile)</PRE>
<DL>
<DD>Set the name of the SQL file to be run.
 Required unless statements are enclosed in the build file
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>srcFile</CODE> - the file containing the SQL command.</DL>
</DD>
</DL>
<HR>

<A NAME="setExpandProperties(boolean)"><!-- --></A><H3>
setExpandProperties</H3>
<PRE>
public void <B>setExpandProperties</B>(boolean&nbsp;expandProperties)</PRE>
<DL>
<DD>Enable property expansion inside nested text
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>expandProperties</CODE> - if true expand properties.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getExpandProperties()"><!-- --></A><H3>
getExpandProperties</H3>
<PRE>
public boolean <B>getExpandProperties</B>()</PRE>
<DL>
<DD>is property expansion inside inline text enabled?
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true if properties are to be expanded.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addText(java.lang.String)"><!-- --></A><H3>
addText</H3>
<PRE>
public void <B>addText</B>(java.lang.String&nbsp;sql)</PRE>
<DL>
<DD>Set an inline SQL command to execute.
 NB: Properties are not expanded in this text unless <A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#expandProperties"><CODE>expandProperties</CODE></A>
 is set.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sql</CODE> - an inline string containing the SQL command.</DL>
</DD>
</DL>
<HR>

<A NAME="addFileset(org.apache.tools.ant.types.FileSet)"><!-- --></A><H3>
addFileset</H3>
<PRE>
public void <B>addFileset</B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;set)</PRE>
<DL>
<DD>Adds a set of files (nested fileset attribute).
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>set</CODE> - a set of files contains SQL commands, each File is run in
            a separate transaction.</DL>
</DD>
</DL>
<HR>

<A NAME="add(org.apache.tools.ant.types.ResourceCollection)"><!-- --></A><H3>
add</H3>
<PRE>
public void <B>add</B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;rc)</PRE>
<DL>
<DD>Adds a collection of resources (nested element).
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rc</CODE> - a collection of resources containing SQL commands,
 each resource is run in a separate transaction.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="createTransaction()"><!-- --></A><H3>
createTransaction</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.Transaction.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.Transaction</A> <B>createTransaction</B>()</PRE>
<DL>
<DD>Add a SQL transaction to execute
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a Transaction to be configured.</DL>
</DD>
</DL>
<HR>

<A NAME="setEncoding(java.lang.String)"><!-- --></A><H3>
setEncoding</H3>
<PRE>
public void <B>setEncoding</B>(java.lang.String&nbsp;encoding)</PRE>
<DL>
<DD>Set the file encoding to use on the SQL files read in
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>encoding</CODE> - the encoding to use on the files</DL>
</DD>
</DL>
<HR>

<A NAME="setDelimiter(java.lang.String)"><!-- --></A><H3>
setDelimiter</H3>
<PRE>
public void <B>setDelimiter</B>(java.lang.String&nbsp;delimiter)</PRE>
<DL>
<DD>Set the delimiter that separates SQL statements. Defaults to &quot;;&quot;;
 optional

 <p>For example, set this to "go" and delimitertype to "ROW" for
 Sybase ASE or MS SQL Server.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>delimiter</CODE> - the separator.</DL>
</DD>
</DL>
<HR>

<A NAME="setDelimiterType(org.apache.tools.ant.taskdefs.SQLExec.DelimiterType)"><!-- --></A><H3>
setDelimiterType</H3>
<PRE>
public void <B>setDelimiterType</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.DelimiterType.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.DelimiterType</A>&nbsp;delimiterType)</PRE>
<DL>
<DD>Set the delimiter type: "normal" or "row" (default "normal").

 <p>The delimiter type takes two values - normal and row. Normal
 means that any occurrence of the delimiter terminate the SQL
 command whereas with row, only a line containing just the
 delimiter is recognized as the end of the command.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>delimiterType</CODE> - the type of delimiter - "normal" or "row".</DL>
</DD>
</DL>
<HR>

<A NAME="setPrint(boolean)"><!-- --></A><H3>
setPrint</H3>
<PRE>
public void <B>setPrint</B>(boolean&nbsp;print)</PRE>
<DL>
<DD>Print result sets from the statements;
 optional, default false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>print</CODE> - if true print result sets.</DL>
</DD>
</DL>
<HR>

<A NAME="setShowheaders(boolean)"><!-- --></A><H3>
setShowheaders</H3>
<PRE>
public void <B>setShowheaders</B>(boolean&nbsp;showheaders)</PRE>
<DL>
<DD>Print headers for result sets from the
 statements; optional, default true.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>showheaders</CODE> - if true print headers of result sets.</DL>
</DD>
</DL>
<HR>

<A NAME="setShowtrailers(boolean)"><!-- --></A><H3>
setShowtrailers</H3>
<PRE>
public void <B>setShowtrailers</B>(boolean&nbsp;showtrailers)</PRE>
<DL>
<DD>Print trailing info (rows affected) for the SQL
 Addresses Bug/Request #27446
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>showtrailers</CODE> - if true prints the SQL rows affected<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setOutput(java.io.File)"><!-- --></A><H3>
setOutput</H3>
<PRE>
public void <B>setOutput</B>(java.io.File&nbsp;output)</PRE>
<DL>
<DD>Set the output file;
 optional, defaults to the Ant log.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - the output file to use for logging messages.</DL>
</DD>
</DL>
<HR>

<A NAME="setAppend(boolean)"><!-- --></A><H3>
setAppend</H3>
<PRE>
public void <B>setAppend</B>(boolean&nbsp;append)</PRE>
<DL>
<DD>whether output should be appended to or overwrite
 an existing file.  Defaults to false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>append</CODE> - if true append to an existing file.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setOnerror(org.apache.tools.ant.taskdefs.SQLExec.OnError)"><!-- --></A><H3>
setOnerror</H3>
<PRE>
public void <B>setOnerror</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.OnError.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.OnError</A>&nbsp;action)</PRE>
<DL>
<DD>Action to perform when statement fails: continue, stop, or abort
 optional; default &quot;abort&quot;
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>action</CODE> - the action to perform on statement failure.</DL>
</DD>
</DL>
<HR>

<A NAME="setKeepformat(boolean)"><!-- --></A><H3>
setKeepformat</H3>
<PRE>
public void <B>setKeepformat</B>(boolean&nbsp;keepformat)</PRE>
<DL>
<DD>whether or not format should be preserved.
 Defaults to false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>keepformat</CODE> - The keepformat to set</DL>
</DD>
</DL>
<HR>

<A NAME="setEscapeProcessing(boolean)"><!-- --></A><H3>
setEscapeProcessing</H3>
<PRE>
public void <B>setEscapeProcessing</B>(boolean&nbsp;enable)</PRE>
<DL>
<DD>Set escape processing for statements.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>enable</CODE> - if true enable escape processing, default is true.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Load the sql file and then execute it
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error.</DL>
</DD>
</DL>
<HR>

<A NAME="runStatements(java.io.Reader, java.io.PrintStream)"><!-- --></A><H3>
runStatements</H3>
<PRE>
protected void <B>runStatements</B>(java.io.Reader&nbsp;reader,
                             java.io.PrintStream&nbsp;out)
                      throws java.sql.SQLException,
                             java.io.IOException</PRE>
<DL>
<DD>read in lines and execute them
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>reader</CODE> - the reader contains sql lines.<DD><CODE>out</CODE> - the place to output results.
<DT><B>Throws:</B>
<DD><CODE>java.sql.SQLException</CODE> - on sql problems
<DD><CODE>java.io.IOException</CODE> - on io problems</DL>
</DD>
</DL>
<HR>

<A NAME="execSQL(java.lang.String, java.io.PrintStream)"><!-- --></A><H3>
execSQL</H3>
<PRE>
protected void <B>execSQL</B>(java.lang.String&nbsp;sql,
                       java.io.PrintStream&nbsp;out)
                throws java.sql.SQLException</PRE>
<DL>
<DD>Exec the sql statement.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sql</CODE> - the SQL statement to execute<DD><CODE>out</CODE> - the place to put output
<DT><B>Throws:</B>
<DD><CODE>java.sql.SQLException</CODE> - on SQL problems</DL>
</DD>
</DL>
<HR>

<A NAME="printResults(java.io.PrintStream)"><!-- --></A><H3>
printResults</H3>
<PRE>
protected void <B>printResults</B>(java.io.PrintStream&nbsp;out)
                     throws java.sql.SQLException</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             Use <A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html#printResults(java.sql.ResultSet, java.io.PrintStream)"><CODE>the two arg version</CODE></A> instead.</I>
<P>
<DD>print any results in the statement
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>out</CODE> - the place to print results
<DT><B>Throws:</B>
<DD><CODE>java.sql.SQLException</CODE> - on SQL problems.</DL>
</DD>
</DL>
<HR>

<A NAME="printResults(java.sql.ResultSet, java.io.PrintStream)"><!-- --></A><H3>
printResults</H3>
<PRE>
protected void <B>printResults</B>(java.sql.ResultSet&nbsp;rs,
                            java.io.PrintStream&nbsp;out)
                     throws java.sql.SQLException</PRE>
<DL>
<DD>print any results in the result set.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rs</CODE> - the resultset to print information about<DD><CODE>out</CODE> - the place to print results
<DT><B>Throws:</B>
<DD><CODE>java.sql.SQLException</CODE> - on SQL problems.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Sleep.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.DelimiterType.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/SQLExec.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="SQLExec.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.Task">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
