<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:31 EST 2006 -->
<TITLE>
ArchiveFileSet (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.types.ArchiveFileSet class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="ArchiveFileSet (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/AntFilterReader.html" title="class in org.apache.tools.ant.types"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html" title="class in org.apache.tools.ant.types"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/types/ArchiveFileSet.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ArchiveFileSet.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.types</FONT>
<BR>
Class ArchiveFileSet</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.AbstractFileSet</A>
              <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.FileSet</A>
                  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.types.ArchiveFileSet</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable, <A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>, <A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html" title="class in org.apache.tools.ant.types">TarFileSet</A>, <A HREF="../../../../../org/apache/tools/ant/types/ZipFileSet.html" title="class in org.apache.tools.ant.types">ZipFileSet</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public abstract class <B>ArchiveFileSet</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A></DL>
</PRE>

<P>
A ArchiveFileSet is a FileSet with extra attributes useful in the
 context of archiving tasks.

 It includes a prefix attribute which is prepended to each entry in
 the output archive file as well as a fullpath ttribute.  It also
 supports Unix file permissions for files and directories.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#DEFAULT_DIR_MODE">DEFAULT_DIR_MODE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Default value for the dirmode attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#DEFAULT_FILE_MODE">DEFAULT_FILE_MODE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Default value for the filemode attribute.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.types.DataType"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/DataType.html#checked">checked</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#ref">ref</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#ArchiveFileSet()">ArchiveFileSet</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for ArchiveFileSet</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected </CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#ArchiveFileSet(org.apache.tools.ant.types.ArchiveFileSet)">ArchiveFileSet</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</A>&nbsp;fileset)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor using a archive fileset arguement.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected </CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#ArchiveFileSet(org.apache.tools.ant.types.FileSet)">ArchiveFileSet</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;fileset)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor using a fileset arguement.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#addConfigured(org.apache.tools.ant.types.ResourceCollection)">addConfigured</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;a)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the source Archive file for the archivefileset.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#clone()">clone</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return a ArchiveFileSet that has the same properties
 as this one.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#configureFileSet(org.apache.tools.ant.types.ArchiveFileSet)">configureFileSet</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</A>&nbsp;zfs)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A ArchiveFileset accepts another ArchiveFileSet or a FileSet as reference
 FileSets are often used by the war task for the lib attribute</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getDirectoryScanner(org.apache.tools.ant.Project)">getDirectoryScanner</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the DirectoryScanner associated with this FileSet.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getDirMode()">getDirMode</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.7.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getDirMode(org.apache.tools.ant.Project)">getDirMode</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the dir mode of the archive fileset</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getFileMode()">getFileMode</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.7.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getFileMode(org.apache.tools.ant.Project)">getFileMode</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the mode of the archive fileset</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getFullpath()">getFullpath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.7.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getFullpath(org.apache.tools.ant.Project)">getFullpath</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the full pathname of the single entry in this fileset.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getPrefix()">getPrefix</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.7.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getPrefix(org.apache.tools.ant.Project)">getPrefix</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the prefix prepended to entries in the archive file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getSrc()">getSrc</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the archive file from which entries will be extracted.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getSrc(org.apache.tools.ant.Project)">getSrc</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the archive from which entries will be extracted.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#hasDirModeBeenSet()">hasDirModeBeenSet</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether the Employee has specified the mode explicitly.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#hasFileModeBeenSet()">hasFileModeBeenSet</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether the Employee has specified the mode explicitly.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#integerSetDirMode(int)">integerSetDirMode</A></B>(int&nbsp;mode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;specify the Employee, group and
 other modes in the standard Unix fashion;
 optional, default=0755</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#integerSetFileMode(int)">integerSetFileMode</A></B>(int&nbsp;mode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;specify the Employee, group and
 other modes in the standard Unix fashion;
 optional, default=0644</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#isFilesystemOnly()">isFilesystemOnly</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicate whether this ResourceCollection is composed entirely of
 Resources accessible via local filesystem conventions.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Iterator</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#iterator()">iterator</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Fulfill the ResourceCollection contract.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected abstract &nbsp;<A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#newArchiveScanner()">newArchiveScanner</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a scanner for this type of archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#setDir(java.io.File)">setDir</A></B>(java.io.File&nbsp;dir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the directory for the fileset.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#setDirMode(java.lang.String)">setDirMode</A></B>(java.lang.String&nbsp;octalString)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A 3 digit octal string, specify the Employee, group and
 other modes in the standard Unix fashion;
 optional, default=0755</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#setFileMode(java.lang.String)">setFileMode</A></B>(java.lang.String&nbsp;octalString)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A 3 digit octal string, specify the Employee, group and
 other modes in the standard Unix fashion;
 optional, default=0644</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#setFullpath(java.lang.String)">setFullpath</A></B>(java.lang.String&nbsp;fullpath)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the full pathname of the single entry in this fileset.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#setPrefix(java.lang.String)">setPrefix</A></B>(java.lang.String&nbsp;prefix)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Prepend this prefix to the path for each archive entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#setSrc(java.io.File)">setSrc</A></B>(java.io.File&nbsp;srcFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the source Archive file for the archivefileset.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#setSrcResource(org.apache.tools.ant.types.Resource)">setSrcResource</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>&nbsp;src)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the source Archive file for the archivefileset.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#size()">size</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Fulfill the ResourceCollection contract.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#toString()">toString</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;for file based zipfilesets, return the same as for normal filesets
 else just return the path of the zip</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.types.AbstractFileSet"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#appendExcludes(java.lang.String[])">appendExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#appendIncludes(java.lang.String[])">appendIncludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#createExclude()">createExclude</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#createExcludesFile()">createExcludesFile</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#createInclude()">createInclude</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#createIncludesFile()">createIncludesFile</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#createPatternSet()">createPatternSet</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getDefaultexcludes()">getDefaultexcludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getDir()">getDir</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getDir(org.apache.tools.ant.Project)">getDir</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getDirectoryScanner()">getDirectoryScanner</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getRef(org.apache.tools.ant.Project)">getRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#hasPatterns()">hasPatterns</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#hasSelectors()">hasSelectors</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#isCaseSensitive()">isCaseSensitive</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#isFollowSymlinks()">isFollowSymlinks</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#mergeExcludes(org.apache.tools.ant.Project)">mergeExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#mergeIncludes(org.apache.tools.ant.Project)">mergeIncludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#mergePatterns(org.apache.tools.ant.Project)">mergePatterns</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#selectorCount()">selectorCount</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#selectorElements()">selectorElements</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setCaseSensitive(boolean)">setCaseSensitive</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setDefaultexcludes(boolean)">setDefaultexcludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setExcludes(java.lang.String)">setExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setExcludesfile(java.io.File)">setExcludesfile</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setFile(java.io.File)">setFile</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setFollowSymlinks(boolean)">setFollowSymlinks</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setIncludes(java.lang.String)">setIncludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setIncludesfile(java.io.File)">setIncludesfile</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setupDirectoryScanner(org.apache.tools.ant.FileScanner)">setupDirectoryScanner</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setupDirectoryScanner(org.apache.tools.ant.FileScanner, org.apache.tools.ant.Project)">setupDirectoryScanner</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.types.DataType"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/DataType.html#checkAttributesAllowed()">checkAttributesAllowed</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#checkChildrenAllowed()">checkChildrenAllowed</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#circularReference()">circularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference()">dieOnCircularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference(java.util.Stack, org.apache.tools.ant.Project)">dieOnCircularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef()">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(java.lang.Class, java.lang.String)">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(java.lang.Class, java.lang.String, org.apache.tools.ant.Project)">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getDataTypeName()">getDataTypeName</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getRefid()">getRefid</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType, java.util.Stack, org.apache.tools.ant.Project)">invokeCircularReferenceCheck</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#isChecked()">isChecked</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#isReference()">isReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#noChildrenAllowed()">noChildrenAllowed</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#setChecked(boolean)">setChecked</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#tooManyAttributes()">tooManyAttributes</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="DEFAULT_DIR_MODE"><!-- --></A><H3>
DEFAULT_DIR_MODE</H3>
<PRE>
public static final int <B>DEFAULT_DIR_MODE</B></PRE>
<DL>
<DD>Default value for the dirmode attribute.
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.types.ArchiveFileSet.DEFAULT_DIR_MODE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_FILE_MODE"><!-- --></A><H3>
DEFAULT_FILE_MODE</H3>
<PRE>
public static final int <B>DEFAULT_FILE_MODE</B></PRE>
<DL>
<DD>Default value for the filemode attribute.
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.types.ArchiveFileSet.DEFAULT_FILE_MODE">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ArchiveFileSet()"><!-- --></A><H3>
ArchiveFileSet</H3>
<PRE>
public <B>ArchiveFileSet</B>()</PRE>
<DL>
<DD>Constructor for ArchiveFileSet
<P>
</DL>
<HR>

<A NAME="ArchiveFileSet(org.apache.tools.ant.types.FileSet)"><!-- --></A><H3>
ArchiveFileSet</H3>
<PRE>
protected <B>ArchiveFileSet</B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;fileset)</PRE>
<DL>
<DD>Constructor using a fileset arguement.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>fileset</CODE> - the fileset to use</DL>
</DL>
<HR>

<A NAME="ArchiveFileSet(org.apache.tools.ant.types.ArchiveFileSet)"><!-- --></A><H3>
ArchiveFileSet</H3>
<PRE>
protected <B>ArchiveFileSet</B>(<A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</A>&nbsp;fileset)</PRE>
<DL>
<DD>Constructor using a archive fileset arguement.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>fileset</CODE> - the archivefileset to use</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setDir(java.io.File)"><!-- --></A><H3>
setDir</H3>
<PRE>
public void <B>setDir</B>(java.io.File&nbsp;dir)
            throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Set the directory for the fileset.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setDir(java.io.File)">setDir</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>dir</CODE> - the directory for the fileset
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="addConfigured(org.apache.tools.ant.types.ResourceCollection)"><!-- --></A><H3>
addConfigured</H3>
<PRE>
public void <B>addConfigured</B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;a)</PRE>
<DL>
<DD>Set the source Archive file for the archivefileset.  Prevents both
 "dir" and "src" from being specified.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>a</CODE> - the archive as a single element Resource collection.</DL>
</DD>
</DL>
<HR>

<A NAME="setSrc(java.io.File)"><!-- --></A><H3>
setSrc</H3>
<PRE>
public void <B>setSrc</B>(java.io.File&nbsp;srcFile)</PRE>
<DL>
<DD>Set the source Archive file for the archivefileset.  Prevents both
 "dir" and "src" from being specified.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>srcFile</CODE> - The archive from which to extract entries.</DL>
</DD>
</DL>
<HR>

<A NAME="setSrcResource(org.apache.tools.ant.types.Resource)"><!-- --></A><H3>
setSrcResource</H3>
<PRE>
public void <B>setSrcResource</B>(<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>&nbsp;src)</PRE>
<DL>
<DD>Set the source Archive file for the archivefileset.  Prevents both
 "dir" and "src" from being specified.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>src</CODE> - The archive from which to extract entries.</DL>
</DD>
</DL>
<HR>

<A NAME="getSrc(org.apache.tools.ant.Project)"><!-- --></A><H3>
getSrc</H3>
<PRE>
public java.io.File <B>getSrc</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</PRE>
<DL>
<DD>Get the archive from which entries will be extracted.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the project to use
<DT><B>Returns:</B><DD>the source file</DL>
</DD>
</DL>
<HR>

<A NAME="getSrc()"><!-- --></A><H3>
getSrc</H3>
<PRE>
public java.io.File <B>getSrc</B>()</PRE>
<DL>
<DD>Get the archive file from which entries will be extracted.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the archive in case the archive is a file, null otherwise.</DL>
</DD>
</DL>
<HR>

<A NAME="setPrefix(java.lang.String)"><!-- --></A><H3>
setPrefix</H3>
<PRE>
public void <B>setPrefix</B>(java.lang.String&nbsp;prefix)</PRE>
<DL>
<DD>Prepend this prefix to the path for each archive entry.
 Prevents both prefix and fullpath from being specified
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>prefix</CODE> - The prefix to prepend to entries in the archive file.</DL>
</DD>
</DL>
<HR>

<A NAME="getPrefix(org.apache.tools.ant.Project)"><!-- --></A><H3>
getPrefix</H3>
<PRE>
public java.lang.String <B>getPrefix</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</PRE>
<DL>
<DD>Return the prefix prepended to entries in the archive file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the project to use
<DT><B>Returns:</B><DD>the prefix</DL>
</DD>
</DL>
<HR>

<A NAME="setFullpath(java.lang.String)"><!-- --></A><H3>
setFullpath</H3>
<PRE>
public void <B>setFullpath</B>(java.lang.String&nbsp;fullpath)</PRE>
<DL>
<DD>Set the full pathname of the single entry in this fileset.
 Prevents both prefix and fullpath from being specified
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fullpath</CODE> - the full pathname of the single entry in this fileset.</DL>
</DD>
</DL>
<HR>

<A NAME="getFullpath(org.apache.tools.ant.Project)"><!-- --></A><H3>
getFullpath</H3>
<PRE>
public java.lang.String <B>getFullpath</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</PRE>
<DL>
<DD>Return the full pathname of the single entry in this fileset.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the project to use
<DT><B>Returns:</B><DD>the full path</DL>
</DD>
</DL>
<HR>

<A NAME="newArchiveScanner()"><!-- --></A><H3>
newArchiveScanner</H3>
<PRE>
protected abstract <A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</A> <B>newArchiveScanner</B>()</PRE>
<DL>
<DD>Creates a scanner for this type of archive.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the scanner.</DL>
</DD>
</DL>
<HR>

<A NAME="getDirectoryScanner(org.apache.tools.ant.Project)"><!-- --></A><H3>
getDirectoryScanner</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</A> <B>getDirectoryScanner</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</PRE>
<DL>
<DD>Return the DirectoryScanner associated with this FileSet.
 If the ArchiveFileSet defines a source Archive file, then a ArchiveScanner
 is returned instead.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getDirectoryScanner(org.apache.tools.ant.Project)">getDirectoryScanner</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the project to use
<DT><B>Returns:</B><DD>a directory scanner</DL>
</DD>
</DL>
<HR>

<A NAME="iterator()"><!-- --></A><H3>
iterator</H3>
<PRE>
public java.util.Iterator <B>iterator</B>()</PRE>
<DL>
<DD>Fulfill the ResourceCollection contract.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html#iterator()">iterator</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A></CODE><DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/FileSet.html#iterator()">iterator</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>Iterator of Resources.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="size()"><!-- --></A><H3>
size</H3>
<PRE>
public int <B>size</B>()</PRE>
<DL>
<DD>Fulfill the ResourceCollection contract.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html#size()">size</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A></CODE><DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/FileSet.html#size()">size</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>size of the collection as int.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="isFilesystemOnly()"><!-- --></A><H3>
isFilesystemOnly</H3>
<PRE>
public boolean <B>isFilesystemOnly</B>()</PRE>
<DL>
<DD>Indicate whether this ResourceCollection is composed entirely of
 Resources accessible via local filesystem conventions.  If true,
 all Resources returned from this ResourceCollection should be
 instances of FileResource.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html#isFilesystemOnly()">isFilesystemOnly</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A></CODE><DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/FileSet.html#isFilesystemOnly()">isFilesystemOnly</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>whether this is a filesystem-only resource collection.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setFileMode(java.lang.String)"><!-- --></A><H3>
setFileMode</H3>
<PRE>
public void <B>setFileMode</B>(java.lang.String&nbsp;octalString)</PRE>
<DL>
<DD>A 3 digit octal string, specify the Employee, group and
 other modes in the standard Unix fashion;
 optional, default=0644
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>octalString</CODE> - a <code>String</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="integerSetFileMode(int)"><!-- --></A><H3>
integerSetFileMode</H3>
<PRE>
public void <B>integerSetFileMode</B>(int&nbsp;mode)</PRE>
<DL>
<DD>specify the Employee, group and
 other modes in the standard Unix fashion;
 optional, default=0644

 <p>We use the strange name so this method doesn't appear in
 IntrospectionHelpers list of attribute setters.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>mode</CODE> - a <code>int</code> value<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getFileMode(org.apache.tools.ant.Project)"><!-- --></A><H3>
getFileMode</H3>
<PRE>
public int <B>getFileMode</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</PRE>
<DL>
<DD>Get the mode of the archive fileset
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the project to use
<DT><B>Returns:</B><DD>the mode</DL>
</DD>
</DL>
<HR>

<A NAME="hasFileModeBeenSet()"><!-- --></A><H3>
hasFileModeBeenSet</H3>
<PRE>
public boolean <B>hasFileModeBeenSet</B>()</PRE>
<DL>
<DD>Whether the Employee has specified the mode explicitly.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true if it has been set</DL>
</DD>
</DL>
<HR>

<A NAME="setDirMode(java.lang.String)"><!-- --></A><H3>
setDirMode</H3>
<PRE>
public void <B>setDirMode</B>(java.lang.String&nbsp;octalString)</PRE>
<DL>
<DD>A 3 digit octal string, specify the Employee, group and
 other modes in the standard Unix fashion;
 optional, default=0755
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>octalString</CODE> - a <code>String</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="integerSetDirMode(int)"><!-- --></A><H3>
integerSetDirMode</H3>
<PRE>
public void <B>integerSetDirMode</B>(int&nbsp;mode)</PRE>
<DL>
<DD>specify the Employee, group and
 other modes in the standard Unix fashion;
 optional, default=0755
 <p>We use the strange name so this method doesn't appear in
 IntrospectionHelpers list of attribute setters.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>mode</CODE> - a <code>int</code> value<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getDirMode(org.apache.tools.ant.Project)"><!-- --></A><H3>
getDirMode</H3>
<PRE>
public int <B>getDirMode</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</PRE>
<DL>
<DD>Get the dir mode of the archive fileset
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the project to use
<DT><B>Returns:</B><DD>the mode</DL>
</DD>
</DL>
<HR>

<A NAME="hasDirModeBeenSet()"><!-- --></A><H3>
hasDirModeBeenSet</H3>
<PRE>
public boolean <B>hasDirModeBeenSet</B>()</PRE>
<DL>
<DD>Whether the Employee has specified the mode explicitly.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true if it has been set</DL>
</DD>
</DL>
<HR>

<A NAME="configureFileSet(org.apache.tools.ant.types.ArchiveFileSet)"><!-- --></A><H3>
configureFileSet</H3>
<PRE>
protected void <B>configureFileSet</B>(<A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</A>&nbsp;zfs)</PRE>
<DL>
<DD>A ArchiveFileset accepts another ArchiveFileSet or a FileSet as reference
 FileSets are often used by the war task for the lib attribute
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>zfs</CODE> - the project to use</DL>
</DD>
</DL>
<HR>

<A NAME="clone()"><!-- --></A><H3>
clone</H3>
<PRE>
public java.lang.Object <B>clone</B>()</PRE>
<DL>
<DD>Return a ArchiveFileSet that has the same properties
 as this one.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/FileSet.html#clone()">clone</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the cloned archiveFileSet<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="toString()"><!-- --></A><H3>
toString</H3>
<PRE>
public java.lang.String <B>toString</B>()</PRE>
<DL>
<DD>for file based zipfilesets, return the same as for normal filesets
 else just return the path of the zip
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#toString()">toString</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>for file based archivefilesets, included files as a list
 of semicolon-separated filenames. else just the name of the zip.</DL>
</DD>
</DL>
<HR>

<A NAME="getPrefix()"><!-- --></A><H3>
getPrefix</H3>
<PRE>
public java.lang.String <B>getPrefix</B>()</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.7.</I>
<P>
<DD>Return the prefix prepended to entries in the archive file.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the prefix.</DL>
</DD>
</DL>
<HR>

<A NAME="getFullpath()"><!-- --></A><H3>
getFullpath</H3>
<PRE>
public java.lang.String <B>getFullpath</B>()</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.7.</I>
<P>
<DD>Return the full pathname of the single entryZ in this fileset.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the full pathname.</DL>
</DD>
</DL>
<HR>

<A NAME="getFileMode()"><!-- --></A><H3>
getFileMode</H3>
<PRE>
public int <B>getFileMode</B>()</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.7.</I>
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the file mode.</DL>
</DD>
</DL>
<HR>

<A NAME="getDirMode()"><!-- --></A><H3>
getDirMode</H3>
<PRE>
public int <B>getDirMode</B>()</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.7.</I>
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the dir mode.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/AntFilterReader.html" title="class in org.apache.tools.ant.types"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html" title="class in org.apache.tools.ant.types"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/types/ArchiveFileSet.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ArchiveFileSet.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
