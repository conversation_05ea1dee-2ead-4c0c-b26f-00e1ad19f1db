<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:23 EST 2006 -->
<TITLE>
Javadoc (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.Javadoc class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Javadoc (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.AccessType.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Javadoc.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Javadoc.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.Task">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class Javadoc</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.Javadoc</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>Javadoc</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></DL>
</PRE>

<P>
Generates Javadoc documentation for a collection
 of source code.

 <p>Current known limitations are:

 <p><ul>
    <li>patterns must be of the form "xxx.*", every other pattern doesn't
        work.
    <li>there is no control on arguments sanity since they are left
        to the Javadoc implementation.
 </ul>

 <p>If no <code>doclet</code> is set, then the <code>version</code> and
 <code>author</code> are by default <code>"yes"</code>.

 <p>Note: This task is run on another VM because the Javadoc code calls
 <code>System.exit()</code> which would break Ant functionality.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.1</DD>
</DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.AccessType.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.AccessType</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;EnumeratedAttribute implementation supporting the Javadoc scoping
 values.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.DocletInfo.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.DocletInfo</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This class stores info about doclets.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.DocletParam.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.DocletParam</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Inner class used to manage doclet parameters.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.ExtensionInfo.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.ExtensionInfo</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A project aware class used for Javadoc extensions which take a name
 and a path such as doclet and taglet arguments.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.GroupArgument.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.GroupArgument</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A class corresponding to the group nested element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.Html.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.Html</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;An HTML element in the Javadoc.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.LinkArgument.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.LinkArgument</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Represents a link triplet (href, whether link is offline,
 location of the package list if off line)</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.PackageName.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.PackageName</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Used to track info about the packages to be javadoc'd</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.ResourceCollectionContainer.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.ResourceCollectionContainer</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Holds a collection of ResourceCollections.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.SourceFile.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.SourceFile</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This class is used to manage the source files to be processed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.TagArgument.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.TagArgument</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Class representing a -tag argument.</TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#Javadoc()">Javadoc</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#addBottom(org.apache.tools.ant.taskdefs.Javadoc.Html)">addBottom</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.Html.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.Html</A>&nbsp;text)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the text to be placed at the bottom of each output file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#addDoctitle(org.apache.tools.ant.taskdefs.Javadoc.Html)">addDoctitle</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.Html.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.Html</A>&nbsp;text)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a document title to use for the overview page.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#addExcludePackage(org.apache.tools.ant.taskdefs.Javadoc.PackageName)">addExcludePackage</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.PackageName.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.PackageName</A>&nbsp;pn)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a package to be excluded from the Javadoc run.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;fs)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a fileset.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#addFooter(org.apache.tools.ant.taskdefs.Javadoc.Html)">addFooter</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.Html.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.Html</A>&nbsp;text)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the footer text to be placed at the bottom of each output file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#addHeader(org.apache.tools.ant.taskdefs.Javadoc.Html)">addHeader</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.Html.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.Html</A>&nbsp;text)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the header text to be placed at the top of each output file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#addPackage(org.apache.tools.ant.taskdefs.Javadoc.PackageName)">addPackage</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.PackageName.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.PackageName</A>&nbsp;pn)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a single package to be processed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#addPackageset(org.apache.tools.ant.types.DirSet)">addPackageset</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/DirSet.html" title="class in org.apache.tools.ant.types">DirSet</A>&nbsp;packageSet)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a packageset.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#addSource(org.apache.tools.ant.taskdefs.Javadoc.SourceFile)">addSource</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.SourceFile.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.SourceFile</A>&nbsp;sf)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a single source file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#addTaglet(org.apache.tools.ant.taskdefs.Javadoc.ExtensionInfo)">addTaglet</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.ExtensionInfo.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.ExtensionInfo</A>&nbsp;tagletInfo)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a taglet</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#createArg()">createArg</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a command-line argument.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#createBootclasspath()">createBootclasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a Path to be configured with the boot classpath</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#createClasspath()">createClasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a Path to be configured with the classpath to use</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.DocletInfo.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.DocletInfo</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#createDoclet()">createDoclet</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a doclet to be used in the documentation generation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.GroupArgument.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.GroupArgument</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#createGroup()">createGroup</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Separates packages on the overview page into whatever
 groups you specify, one group per table.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.LinkArgument.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.LinkArgument</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#createLink()">createLink</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create link to Javadoc output at the given URL.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.ResourceCollectionContainer.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.ResourceCollectionContainer</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#createSourceFiles()">createSourceFiles</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a container for resource collections.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#createSourcepath()">createSourcepath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a path to be configured with the locations of the source
 files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.TagArgument.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.TagArgument</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#createTag()">createTag</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates and adds a -tag argument.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Execute the task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#expand(java.lang.String)">expand</A></B>(java.lang.String&nbsp;content)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to expand properties.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setAccess(org.apache.tools.ant.taskdefs.Javadoc.AccessType)">setAccess</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.AccessType.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.AccessType</A>&nbsp;at)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the scope to be processed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setAdditionalparam(java.lang.String)">setAdditionalparam</A></B>(java.lang.String&nbsp;add)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set an additional parameter on the command line</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setAuthor(boolean)">setAuthor</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Include the author tag in the generated documentation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setBootclasspath(org.apache.tools.ant.types.Path)">setBootclasspath</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;path)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the boot classpath to use.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setBootClasspathRef(org.apache.tools.ant.types.Reference)">setBootClasspathRef</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a reference to a CLASSPATH defined elsewhere.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setBottom(java.lang.String)">setBottom</A></B>(java.lang.String&nbsp;bottom)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the text to be placed at the bottom of each output file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setBreakiterator(boolean)">setBreakiterator</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Enables the -linksource switch, will be ignored if Javadoc is not
 the 1.4 version.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setCharset(java.lang.String)">setCharset</A></B>(java.lang.String&nbsp;src)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Charset for cross-platform viewing of generated documentation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;path)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the classpath to be used for this Javadoc run.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setClasspathRef(org.apache.tools.ant.types.Reference)">setClasspathRef</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a reference to a CLASSPATH defined elsewhere.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setDefaultexcludes(boolean)">setDefaultexcludes</A></B>(boolean&nbsp;useDefaultExcludes)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets whether default exclusions should be used or not.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setDestdir(java.io.File)">setDestdir</A></B>(java.io.File&nbsp;dir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the directory where the Javadoc output will be generated.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setDocencoding(java.lang.String)">setDocencoding</A></B>(java.lang.String&nbsp;enc)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Output file encoding name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setDoclet(java.lang.String)">setDoclet</A></B>(java.lang.String&nbsp;docletName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the class that starts the doclet used in generating the
 documentation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setDocletPath(org.apache.tools.ant.types.Path)">setDocletPath</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;docletPath)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the classpath used to find the doclet class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setDocletPathRef(org.apache.tools.ant.types.Reference)">setDocletPathRef</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the classpath used to find the doclet class by reference.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setDoctitle(java.lang.String)">setDoctitle</A></B>(java.lang.String&nbsp;doctitle)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the title of the generated overview page.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setEncoding(java.lang.String)">setEncoding</A></B>(java.lang.String&nbsp;enc)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the encoding name of the source files,</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setExcludePackageNames(java.lang.String)">setExcludePackageNames</A></B>(java.lang.String&nbsp;packages)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the list of packages to be excluded.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setExecutable(java.lang.String)">setExecutable</A></B>(java.lang.String&nbsp;executable)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the actual executable command to invoke, instead of the binary
 <code>javadoc</code> found in Ant's JDK.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setExtdirs(org.apache.tools.ant.types.Path)">setExtdirs</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;path)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the location of the extensions directories.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setExtdirs(java.lang.String)">setExtdirs</A></B>(java.lang.String&nbsp;path)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use the <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setExtdirs(org.apache.tools.ant.types.Path)"><CODE>setExtdirs(Path)</CODE></A> version.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setFailonerror(boolean)">setFailonerror</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Should the build process fail if Javadoc fails (as indicated by
 a non zero return code)?</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setFooter(java.lang.String)">setFooter</A></B>(java.lang.String&nbsp;footer)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the footer text to be placed at the bottom of each output file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setGroup(java.lang.String)">setGroup</A></B>(java.lang.String&nbsp;src)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Group specified packages together in overview page.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setHeader(java.lang.String)">setHeader</A></B>(java.lang.String&nbsp;header)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the header text to be placed at the top of each output file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setHelpfile(java.io.File)">setHelpfile</A></B>(java.io.File&nbsp;f)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specifies the HTML help file to use.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setIncludeNoSourcePackages(boolean)">setIncludeNoSourcePackages</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If set to true, Ant will also accept packages that only hold
 package.html files but no Java sources.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setLink(java.lang.String)">setLink</A></B>(java.lang.String&nbsp;src)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create links to Javadoc output at the given URL.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setLinkoffline(java.lang.String)">setLinkoffline</A></B>(java.lang.String&nbsp;src)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Link to docs at "url" using package list at "url2"
 - separate the URLs by using a space character.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setLinksource(boolean)">setLinksource</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Enables the -linksource switch, will be ignored if Javadoc is not
 the 1.4 version.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setLocale(java.lang.String)">setLocale</A></B>(java.lang.String&nbsp;locale)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the local to use in documentation generation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setMaxmemory(java.lang.String)">setMaxmemory</A></B>(java.lang.String&nbsp;max)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the maximum memory to be used by the javadoc process</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setNodeprecated(boolean)">setNodeprecated</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Control deprecation infromation</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setNodeprecatedlist(boolean)">setNodeprecatedlist</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Control deprecated list generation</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setNohelp(boolean)">setNohelp</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Control generation of help link.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setNoindex(boolean)">setNoindex</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Control generation of index.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setNonavbar(boolean)">setNonavbar</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Control generation of the navigation bar.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setNoqualifier(java.lang.String)">setNoqualifier</A></B>(java.lang.String&nbsp;noqualifier)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Enables the -noqualifier switch, will be ignored if Javadoc is not
 the 1.4 version.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setNotree(boolean)">setNotree</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Control class tree generation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setOld(boolean)">setOld</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicate whether Javadoc should produce old style (JDK 1.1)
 documentation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setOverview(java.io.File)">setOverview</A></B>(java.io.File&nbsp;f)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specify the file containing the overview to be included in the generated
 documentation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setPackage(boolean)">setPackage</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicate whether only package, protected and public classes and
 members are to be included in the scope processed</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setPackageList(java.lang.String)">setPackageList</A></B>(java.lang.String&nbsp;src)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The name of a file containing the packages to process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setPackagenames(java.lang.String)">setPackagenames</A></B>(java.lang.String&nbsp;packages)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the package names to be processed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setPrivate(boolean)">setPrivate</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicate whether all classes and
 members are to be included in the scope processed</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setProtected(boolean)">setProtected</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicate whether only protected and public classes and members are to
 be included in the scope processed</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setPublic(boolean)">setPublic</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicate whether only public classes and members are to be included in
 the scope processed</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setSerialwarn(boolean)">setSerialwarn</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Control warnings about serial tag.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setSource(java.lang.String)">setSource</A></B>(java.lang.String&nbsp;source)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Enables the -source switch, will be ignored if Javadoc is not
 the 1.4 version.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setSourcefiles(java.lang.String)">setSourcefiles</A></B>(java.lang.String&nbsp;src)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the list of source files to process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setSourcepath(org.apache.tools.ant.types.Path)">setSourcepath</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;src)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specify where to find source file</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setSourcepathRef(org.apache.tools.ant.types.Reference)">setSourcepathRef</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a reference to a CLASSPATH defined elsewhere.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setSplitindex(boolean)">setSplitindex</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Generate a split index</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setStylesheetfile(java.io.File)">setStylesheetfile</A></B>(java.io.File&nbsp;f)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specifies the CSS stylesheet file to use.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setUse(boolean)">setUse</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Generate the &quot;use&quot page for each package.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setUseExternalFile(boolean)">setUseExternalFile</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Work around command line length limit by using an external file
 for the sourcefiles.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setVerbose(boolean)">setVerbose</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Run javadoc in verbose mode</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setVersion(boolean)">setVersion</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Include the version tag in the generated documentation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setWindowtitle(java.lang.String)">setWindowtitle</A></B>(java.lang.String&nbsp;title)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the title to be placed in the HTML &lt;title&gt; tag of the
 generated documentation.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Javadoc()"><!-- --></A><H3>
Javadoc</H3>
<PRE>
public <B>Javadoc</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setUseExternalFile(boolean)"><!-- --></A><H3>
setUseExternalFile</H3>
<PRE>
public void <B>setUseExternalFile</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Work around command line length limit by using an external file
 for the sourcefiles.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - true if an external file is to be used.</DL>
</DD>
</DL>
<HR>

<A NAME="setDefaultexcludes(boolean)"><!-- --></A><H3>
setDefaultexcludes</H3>
<PRE>
public void <B>setDefaultexcludes</B>(boolean&nbsp;useDefaultExcludes)</PRE>
<DL>
<DD>Sets whether default exclusions should be used or not.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>useDefaultExcludes</CODE> - "true"|"on"|"yes" when default exclusions
                           should be used, "false"|"off"|"no" when they
                           shouldn't be used.</DL>
</DD>
</DL>
<HR>

<A NAME="setMaxmemory(java.lang.String)"><!-- --></A><H3>
setMaxmemory</H3>
<PRE>
public void <B>setMaxmemory</B>(java.lang.String&nbsp;max)</PRE>
<DL>
<DD>Set the maximum memory to be used by the javadoc process
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>max</CODE> - a string indicating the maximum memory according to the
        JVM conventions (e.g. 128m is 128 Megabytes)</DL>
</DD>
</DL>
<HR>

<A NAME="setAdditionalparam(java.lang.String)"><!-- --></A><H3>
setAdditionalparam</H3>
<PRE>
public void <B>setAdditionalparam</B>(java.lang.String&nbsp;add)</PRE>
<DL>
<DD>Set an additional parameter on the command line
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>add</CODE> - the additional command line parameter for the javadoc task.</DL>
</DD>
</DL>
<HR>

<A NAME="createArg()"><!-- --></A><H3>
createArg</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</A> <B>createArg</B>()</PRE>
<DL>
<DD>Adds a command-line argument.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a command-line argument to configure<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setSourcepath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setSourcepath</H3>
<PRE>
public void <B>setSourcepath</B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;src)</PRE>
<DL>
<DD>Specify where to find source file
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>src</CODE> - a Path instance containing the various source directories.</DL>
</DD>
</DL>
<HR>

<A NAME="createSourcepath()"><!-- --></A><H3>
createSourcepath</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createSourcepath</B>()</PRE>
<DL>
<DD>Create a path to be configured with the locations of the source
 files.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a new Path instance to be configured by the Ant core.</DL>
</DD>
</DL>
<HR>

<A NAME="setSourcepathRef(org.apache.tools.ant.types.Reference)"><!-- --></A><H3>
setSourcepathRef</H3>
<PRE>
public void <B>setSourcepathRef</B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</PRE>
<DL>
<DD>Adds a reference to a CLASSPATH defined elsewhere.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>r</CODE> - the reference containing the source path definition.</DL>
</DD>
</DL>
<HR>

<A NAME="setDestdir(java.io.File)"><!-- --></A><H3>
setDestdir</H3>
<PRE>
public void <B>setDestdir</B>(java.io.File&nbsp;dir)</PRE>
<DL>
<DD>Set the directory where the Javadoc output will be generated.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>dir</CODE> - the destination directory.</DL>
</DD>
</DL>
<HR>

<A NAME="setSourcefiles(java.lang.String)"><!-- --></A><H3>
setSourcefiles</H3>
<PRE>
public void <B>setSourcefiles</B>(java.lang.String&nbsp;src)</PRE>
<DL>
<DD>Set the list of source files to process.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>src</CODE> - a comma separated list of source files.</DL>
</DD>
</DL>
<HR>

<A NAME="addSource(org.apache.tools.ant.taskdefs.Javadoc.SourceFile)"><!-- --></A><H3>
addSource</H3>
<PRE>
public void <B>addSource</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.SourceFile.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.SourceFile</A>&nbsp;sf)</PRE>
<DL>
<DD>Add a single source file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sf</CODE> - the source file to be processed.</DL>
</DD>
</DL>
<HR>

<A NAME="setPackagenames(java.lang.String)"><!-- --></A><H3>
setPackagenames</H3>
<PRE>
public void <B>setPackagenames</B>(java.lang.String&nbsp;packages)</PRE>
<DL>
<DD>Set the package names to be processed.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>packages</CODE> - a comma separated list of packages specs
        (may be wildcarded).<DT><B>See Also:</B><DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#addPackage(org.apache.tools.ant.taskdefs.Javadoc.PackageName)"><CODE>for wildcard information.</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="addPackage(org.apache.tools.ant.taskdefs.Javadoc.PackageName)"><!-- --></A><H3>
addPackage</H3>
<PRE>
public void <B>addPackage</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.PackageName.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.PackageName</A>&nbsp;pn)</PRE>
<DL>
<DD>Add a single package to be processed.

 If the package name ends with &quot;.*&quot; the Javadoc task
 will find and process all subpackages.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pn</CODE> - the package name, possibly wildcarded.</DL>
</DD>
</DL>
<HR>

<A NAME="setExcludePackageNames(java.lang.String)"><!-- --></A><H3>
setExcludePackageNames</H3>
<PRE>
public void <B>setExcludePackageNames</B>(java.lang.String&nbsp;packages)</PRE>
<DL>
<DD>Set the list of packages to be excluded.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>packages</CODE> - a comma separated list of packages to be excluded.
        This may not include wildcards.</DL>
</DD>
</DL>
<HR>

<A NAME="addExcludePackage(org.apache.tools.ant.taskdefs.Javadoc.PackageName)"><!-- --></A><H3>
addExcludePackage</H3>
<PRE>
public void <B>addExcludePackage</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.PackageName.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.PackageName</A>&nbsp;pn)</PRE>
<DL>
<DD>Add a package to be excluded from the Javadoc run.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pn</CODE> - the name of the package (wildcards are not permitted).</DL>
</DD>
</DL>
<HR>

<A NAME="setOverview(java.io.File)"><!-- --></A><H3>
setOverview</H3>
<PRE>
public void <B>setOverview</B>(java.io.File&nbsp;f)</PRE>
<DL>
<DD>Specify the file containing the overview to be included in the generated
 documentation.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>f</CODE> - the file containing the overview.</DL>
</DD>
</DL>
<HR>

<A NAME="setPublic(boolean)"><!-- --></A><H3>
setPublic</H3>
<PRE>
public void <B>setPublic</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Indicate whether only public classes and members are to be included in
 the scope processed
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - true if scope is to be public.</DL>
</DD>
</DL>
<HR>

<A NAME="setProtected(boolean)"><!-- --></A><H3>
setProtected</H3>
<PRE>
public void <B>setProtected</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Indicate whether only protected and public classes and members are to
 be included in the scope processed
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - true if scope is to be protected.</DL>
</DD>
</DL>
<HR>

<A NAME="setPackage(boolean)"><!-- --></A><H3>
setPackage</H3>
<PRE>
public void <B>setPackage</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Indicate whether only package, protected and public classes and
 members are to be included in the scope processed
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - true if scope is to be package level.</DL>
</DD>
</DL>
<HR>

<A NAME="setPrivate(boolean)"><!-- --></A><H3>
setPrivate</H3>
<PRE>
public void <B>setPrivate</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Indicate whether all classes and
 members are to be included in the scope processed
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - true if scope is to be private level.</DL>
</DD>
</DL>
<HR>

<A NAME="setAccess(org.apache.tools.ant.taskdefs.Javadoc.AccessType)"><!-- --></A><H3>
setAccess</H3>
<PRE>
public void <B>setAccess</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.AccessType.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.AccessType</A>&nbsp;at)</PRE>
<DL>
<DD>Set the scope to be processed. This is an alternative to the
 use of the setPublic, setPrivate, etc methods. It gives better build
 file control over what scope is processed.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>at</CODE> - the scope to be processed.</DL>
</DD>
</DL>
<HR>

<A NAME="setDoclet(java.lang.String)"><!-- --></A><H3>
setDoclet</H3>
<PRE>
public void <B>setDoclet</B>(java.lang.String&nbsp;docletName)</PRE>
<DL>
<DD>Set the class that starts the doclet used in generating the
 documentation.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>docletName</CODE> - the name of the doclet class.</DL>
</DD>
</DL>
<HR>

<A NAME="setDocletPath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setDocletPath</H3>
<PRE>
public void <B>setDocletPath</B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;docletPath)</PRE>
<DL>
<DD>Set the classpath used to find the doclet class.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>docletPath</CODE> - the doclet classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="setDocletPathRef(org.apache.tools.ant.types.Reference)"><!-- --></A><H3>
setDocletPathRef</H3>
<PRE>
public void <B>setDocletPathRef</B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</PRE>
<DL>
<DD>Set the classpath used to find the doclet class by reference.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>r</CODE> - the reference to the Path instance to use as the doclet
        classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="createDoclet()"><!-- --></A><H3>
createDoclet</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.DocletInfo.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.DocletInfo</A> <B>createDoclet</B>()</PRE>
<DL>
<DD>Create a doclet to be used in the documentation generation.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a new DocletInfo instance to be configured.</DL>
</DD>
</DL>
<HR>

<A NAME="addTaglet(org.apache.tools.ant.taskdefs.Javadoc.ExtensionInfo)"><!-- --></A><H3>
addTaglet</H3>
<PRE>
public void <B>addTaglet</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.ExtensionInfo.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.ExtensionInfo</A>&nbsp;tagletInfo)</PRE>
<DL>
<DD>Add a taglet
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>tagletInfo</CODE> - information about the taglet.</DL>
</DD>
</DL>
<HR>

<A NAME="setOld(boolean)"><!-- --></A><H3>
setOld</H3>
<PRE>
public void <B>setOld</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Indicate whether Javadoc should produce old style (JDK 1.1)
 documentation.

 This is not supported by JDK 1.1 and has been phased out in JDK 1.4
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - if true attempt to generate old style documentation.</DL>
</DD>
</DL>
<HR>

<A NAME="setClasspath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setClasspath</H3>
<PRE>
public void <B>setClasspath</B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;path)</PRE>
<DL>
<DD>Set the classpath to be used for this Javadoc run.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>path</CODE> - an Ant Path object containing the compilation
        classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="createClasspath()"><!-- --></A><H3>
createClasspath</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createClasspath</B>()</PRE>
<DL>
<DD>Create a Path to be configured with the classpath to use
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a new Path instance to be configured with the classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="setClasspathRef(org.apache.tools.ant.types.Reference)"><!-- --></A><H3>
setClasspathRef</H3>
<PRE>
public void <B>setClasspathRef</B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</PRE>
<DL>
<DD>Adds a reference to a CLASSPATH defined elsewhere.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>r</CODE> - the reference to an instance defining the classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="setBootclasspath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setBootclasspath</H3>
<PRE>
public void <B>setBootclasspath</B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;path)</PRE>
<DL>
<DD>Set the boot classpath to use.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>path</CODE> - the boot classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="createBootclasspath()"><!-- --></A><H3>
createBootclasspath</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createBootclasspath</B>()</PRE>
<DL>
<DD>Create a Path to be configured with the boot classpath
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a new Path instance to be configured with the boot classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="setBootClasspathRef(org.apache.tools.ant.types.Reference)"><!-- --></A><H3>
setBootClasspathRef</H3>
<PRE>
public void <B>setBootClasspathRef</B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</PRE>
<DL>
<DD>Adds a reference to a CLASSPATH defined elsewhere.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>r</CODE> - the reference to an instance defining the bootclasspath.</DL>
</DD>
</DL>
<HR>

<A NAME="setExtdirs(java.lang.String)"><!-- --></A><H3>
setExtdirs</H3>
<PRE>
public void <B>setExtdirs</B>(java.lang.String&nbsp;path)</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use the <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html#setExtdirs(org.apache.tools.ant.types.Path)"><CODE>setExtdirs(Path)</CODE></A> version.</I>
<P>
<DD>Set the location of the extensions directories.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>path</CODE> - the string version of the path.</DL>
</DD>
</DL>
<HR>

<A NAME="setExtdirs(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setExtdirs</H3>
<PRE>
public void <B>setExtdirs</B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;path)</PRE>
<DL>
<DD>Set the location of the extensions directories.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>path</CODE> - a path containing the extension directories.</DL>
</DD>
</DL>
<HR>

<A NAME="setVerbose(boolean)"><!-- --></A><H3>
setVerbose</H3>
<PRE>
public void <B>setVerbose</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Run javadoc in verbose mode
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - true if operation is to be verbose.</DL>
</DD>
</DL>
<HR>

<A NAME="setLocale(java.lang.String)"><!-- --></A><H3>
setLocale</H3>
<PRE>
public void <B>setLocale</B>(java.lang.String&nbsp;locale)</PRE>
<DL>
<DD>Set the local to use in documentation generation.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>locale</CODE> - the locale to use.</DL>
</DD>
</DL>
<HR>

<A NAME="setEncoding(java.lang.String)"><!-- --></A><H3>
setEncoding</H3>
<PRE>
public void <B>setEncoding</B>(java.lang.String&nbsp;enc)</PRE>
<DL>
<DD>Set the encoding name of the source files,
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>enc</CODE> - the name of the encoding for the source files.</DL>
</DD>
</DL>
<HR>

<A NAME="setVersion(boolean)"><!-- --></A><H3>
setVersion</H3>
<PRE>
public void <B>setVersion</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Include the version tag in the generated documentation.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - true if the version tag should be included.</DL>
</DD>
</DL>
<HR>

<A NAME="setUse(boolean)"><!-- --></A><H3>
setUse</H3>
<PRE>
public void <B>setUse</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Generate the &quot;use&quot page for each package.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - true if the use page should be generated.</DL>
</DD>
</DL>
<HR>

<A NAME="setAuthor(boolean)"><!-- --></A><H3>
setAuthor</H3>
<PRE>
public void <B>setAuthor</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Include the author tag in the generated documentation.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - true if the author tag should be included.</DL>
</DD>
</DL>
<HR>

<A NAME="setSplitindex(boolean)"><!-- --></A><H3>
setSplitindex</H3>
<PRE>
public void <B>setSplitindex</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Generate a split index
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - true if the index should be split into a file per letter.</DL>
</DD>
</DL>
<HR>

<A NAME="setWindowtitle(java.lang.String)"><!-- --></A><H3>
setWindowtitle</H3>
<PRE>
public void <B>setWindowtitle</B>(java.lang.String&nbsp;title)</PRE>
<DL>
<DD>Set the title to be placed in the HTML &lt;title&gt; tag of the
 generated documentation.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>title</CODE> - the window title to use.</DL>
</DD>
</DL>
<HR>

<A NAME="setDoctitle(java.lang.String)"><!-- --></A><H3>
setDoctitle</H3>
<PRE>
public void <B>setDoctitle</B>(java.lang.String&nbsp;doctitle)</PRE>
<DL>
<DD>Set the title of the generated overview page.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>doctitle</CODE> - the Document title.</DL>
</DD>
</DL>
<HR>

<A NAME="addDoctitle(org.apache.tools.ant.taskdefs.Javadoc.Html)"><!-- --></A><H3>
addDoctitle</H3>
<PRE>
public void <B>addDoctitle</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.Html.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.Html</A>&nbsp;text)</PRE>
<DL>
<DD>Add a document title to use for the overview page.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>text</CODE> - the HTML element containing the document title.</DL>
</DD>
</DL>
<HR>

<A NAME="setHeader(java.lang.String)"><!-- --></A><H3>
setHeader</H3>
<PRE>
public void <B>setHeader</B>(java.lang.String&nbsp;header)</PRE>
<DL>
<DD>Set the header text to be placed at the top of each output file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>header</CODE> - the header text</DL>
</DD>
</DL>
<HR>

<A NAME="addHeader(org.apache.tools.ant.taskdefs.Javadoc.Html)"><!-- --></A><H3>
addHeader</H3>
<PRE>
public void <B>addHeader</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.Html.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.Html</A>&nbsp;text)</PRE>
<DL>
<DD>Set the header text to be placed at the top of each output file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>text</CODE> - the header text</DL>
</DD>
</DL>
<HR>

<A NAME="setFooter(java.lang.String)"><!-- --></A><H3>
setFooter</H3>
<PRE>
public void <B>setFooter</B>(java.lang.String&nbsp;footer)</PRE>
<DL>
<DD>Set the footer text to be placed at the bottom of each output file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>footer</CODE> - the footer text.</DL>
</DD>
</DL>
<HR>

<A NAME="addFooter(org.apache.tools.ant.taskdefs.Javadoc.Html)"><!-- --></A><H3>
addFooter</H3>
<PRE>
public void <B>addFooter</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.Html.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.Html</A>&nbsp;text)</PRE>
<DL>
<DD>Set the footer text to be placed at the bottom of each output file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>text</CODE> - the footer text.</DL>
</DD>
</DL>
<HR>

<A NAME="setBottom(java.lang.String)"><!-- --></A><H3>
setBottom</H3>
<PRE>
public void <B>setBottom</B>(java.lang.String&nbsp;bottom)</PRE>
<DL>
<DD>Set the text to be placed at the bottom of each output file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>bottom</CODE> - the bottom text.</DL>
</DD>
</DL>
<HR>

<A NAME="addBottom(org.apache.tools.ant.taskdefs.Javadoc.Html)"><!-- --></A><H3>
addBottom</H3>
<PRE>
public void <B>addBottom</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.Html.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.Html</A>&nbsp;text)</PRE>
<DL>
<DD>Set the text to be placed at the bottom of each output file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>text</CODE> - the bottom text.</DL>
</DD>
</DL>
<HR>

<A NAME="setLinkoffline(java.lang.String)"><!-- --></A><H3>
setLinkoffline</H3>
<PRE>
public void <B>setLinkoffline</B>(java.lang.String&nbsp;src)</PRE>
<DL>
<DD>Link to docs at "url" using package list at "url2"
 - separate the URLs by using a space character.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>src</CODE> - the offline link specification (url and package list)</DL>
</DD>
</DL>
<HR>

<A NAME="setGroup(java.lang.String)"><!-- --></A><H3>
setGroup</H3>
<PRE>
public void <B>setGroup</B>(java.lang.String&nbsp;src)</PRE>
<DL>
<DD>Group specified packages together in overview page.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>src</CODE> - the group packages - a command separated list of group specs,
        each one being a group name and package specification separated
        by a space.</DL>
</DD>
</DL>
<HR>

<A NAME="setLink(java.lang.String)"><!-- --></A><H3>
setLink</H3>
<PRE>
public void <B>setLink</B>(java.lang.String&nbsp;src)</PRE>
<DL>
<DD>Create links to Javadoc output at the given URL.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>src</CODE> - the URL to link to</DL>
</DD>
</DL>
<HR>

<A NAME="setNodeprecated(boolean)"><!-- --></A><H3>
setNodeprecated</H3>
<PRE>
public void <B>setNodeprecated</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Control deprecation infromation
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - If true, do not include deprecated information.</DL>
</DD>
</DL>
<HR>

<A NAME="setNodeprecatedlist(boolean)"><!-- --></A><H3>
setNodeprecatedlist</H3>
<PRE>
public void <B>setNodeprecatedlist</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Control deprecated list generation
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - if true, do not generate deprecated list.</DL>
</DD>
</DL>
<HR>

<A NAME="setNotree(boolean)"><!-- --></A><H3>
setNotree</H3>
<PRE>
public void <B>setNotree</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Control class tree generation.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - if true, do not generate class hierarchy.</DL>
</DD>
</DL>
<HR>

<A NAME="setNoindex(boolean)"><!-- --></A><H3>
setNoindex</H3>
<PRE>
public void <B>setNoindex</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Control generation of index.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - if true, do not generate index.</DL>
</DD>
</DL>
<HR>

<A NAME="setNohelp(boolean)"><!-- --></A><H3>
setNohelp</H3>
<PRE>
public void <B>setNohelp</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Control generation of help link.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - if true, do not generate help link</DL>
</DD>
</DL>
<HR>

<A NAME="setNonavbar(boolean)"><!-- --></A><H3>
setNonavbar</H3>
<PRE>
public void <B>setNonavbar</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Control generation of the navigation bar.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - if true, do not generate navigation bar.</DL>
</DD>
</DL>
<HR>

<A NAME="setSerialwarn(boolean)"><!-- --></A><H3>
setSerialwarn</H3>
<PRE>
public void <B>setSerialwarn</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Control warnings about serial tag.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - if true, generate warning about the serial tag.</DL>
</DD>
</DL>
<HR>

<A NAME="setStylesheetfile(java.io.File)"><!-- --></A><H3>
setStylesheetfile</H3>
<PRE>
public void <B>setStylesheetfile</B>(java.io.File&nbsp;f)</PRE>
<DL>
<DD>Specifies the CSS stylesheet file to use.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>f</CODE> - the file with the CSS to use.</DL>
</DD>
</DL>
<HR>

<A NAME="setHelpfile(java.io.File)"><!-- --></A><H3>
setHelpfile</H3>
<PRE>
public void <B>setHelpfile</B>(java.io.File&nbsp;f)</PRE>
<DL>
<DD>Specifies the HTML help file to use.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>f</CODE> - the file containing help content.</DL>
</DD>
</DL>
<HR>

<A NAME="setDocencoding(java.lang.String)"><!-- --></A><H3>
setDocencoding</H3>
<PRE>
public void <B>setDocencoding</B>(java.lang.String&nbsp;enc)</PRE>
<DL>
<DD>Output file encoding name.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>enc</CODE> - name of the encoding to use.</DL>
</DD>
</DL>
<HR>

<A NAME="setPackageList(java.lang.String)"><!-- --></A><H3>
setPackageList</H3>
<PRE>
public void <B>setPackageList</B>(java.lang.String&nbsp;src)</PRE>
<DL>
<DD>The name of a file containing the packages to process.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>src</CODE> - the file containing the package list.</DL>
</DD>
</DL>
<HR>

<A NAME="createLink()"><!-- --></A><H3>
createLink</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.LinkArgument.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.LinkArgument</A> <B>createLink</B>()</PRE>
<DL>
<DD>Create link to Javadoc output at the given URL.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>link argument to configure</DL>
</DD>
</DL>
<HR>

<A NAME="createTag()"><!-- --></A><H3>
createTag</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.TagArgument.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.TagArgument</A> <B>createTag</B>()</PRE>
<DL>
<DD>Creates and adds a -tag argument. This is used to specify
 custom tags. This argument is only available for Javadoc 1.4,
 and will generate a verbose message (and then be ignored)
 when run on Java versions below 1.4.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>tag argument to be configured</DL>
</DD>
</DL>
<HR>

<A NAME="createGroup()"><!-- --></A><H3>
createGroup</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.GroupArgument.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.GroupArgument</A> <B>createGroup</B>()</PRE>
<DL>
<DD>Separates packages on the overview page into whatever
 groups you specify, one group per table.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a group argument to be configured</DL>
</DD>
</DL>
<HR>

<A NAME="setCharset(java.lang.String)"><!-- --></A><H3>
setCharset</H3>
<PRE>
public void <B>setCharset</B>(java.lang.String&nbsp;src)</PRE>
<DL>
<DD>Charset for cross-platform viewing of generated documentation.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>src</CODE> - the name of the charset</DL>
</DD>
</DL>
<HR>

<A NAME="setFailonerror(boolean)"><!-- --></A><H3>
setFailonerror</H3>
<PRE>
public void <B>setFailonerror</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Should the build process fail if Javadoc fails (as indicated by
 a non zero return code)?

 <p>Default is false.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - a <code>boolean</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="setSource(java.lang.String)"><!-- --></A><H3>
setSource</H3>
<PRE>
public void <B>setSource</B>(java.lang.String&nbsp;source)</PRE>
<DL>
<DD>Enables the -source switch, will be ignored if Javadoc is not
 the 1.4 version.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>source</CODE> - a <code>String</code> value<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setExecutable(java.lang.String)"><!-- --></A><H3>
setExecutable</H3>
<PRE>
public void <B>setExecutable</B>(java.lang.String&nbsp;executable)</PRE>
<DL>
<DD>Sets the actual executable command to invoke, instead of the binary
 <code>javadoc</code> found in Ant's JDK.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>executable</CODE> - the command to invoke.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addPackageset(org.apache.tools.ant.types.DirSet)"><!-- --></A><H3>
addPackageset</H3>
<PRE>
public void <B>addPackageset</B>(<A HREF="../../../../../org/apache/tools/ant/types/DirSet.html" title="class in org.apache.tools.ant.types">DirSet</A>&nbsp;packageSet)</PRE>
<DL>
<DD>Adds a packageset.

 <p>All included directories will be translated into package
 names be converting the directory separator into dots.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>packageSet</CODE> - a directory set<DT><B>Since:</B></DT>
  <DD>1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addFileset(org.apache.tools.ant.types.FileSet)"><!-- --></A><H3>
addFileset</H3>
<PRE>
public void <B>addFileset</B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;fs)</PRE>
<DL>
<DD>Adds a fileset.

 <p>All included files will be added as sourcefiles.  The task
 will automatically add
 <code>includes=&quot;**&#47;*.java&quot;</code> to the
 fileset.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fs</CODE> - a file set<DT><B>Since:</B></DT>
  <DD>1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="createSourceFiles()"><!-- --></A><H3>
createSourceFiles</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.ResourceCollectionContainer.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.ResourceCollectionContainer</A> <B>createSourceFiles</B>()</PRE>
<DL>
<DD>Adds a container for resource collections.

 <p>All included files will be added as sourcefiles.</p>
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the source files to configure.<DT><B>Since:</B></DT>
  <DD>1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setLinksource(boolean)"><!-- --></A><H3>
setLinksource</H3>
<PRE>
public void <B>setLinksource</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Enables the -linksource switch, will be ignored if Javadoc is not
 the 1.4 version. Default is false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - a <code>String</code> value<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setBreakiterator(boolean)"><!-- --></A><H3>
setBreakiterator</H3>
<PRE>
public void <B>setBreakiterator</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Enables the -linksource switch, will be ignored if Javadoc is not
 the 1.4 version. Default is false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - a <code>String</code> value<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setNoqualifier(java.lang.String)"><!-- --></A><H3>
setNoqualifier</H3>
<PRE>
public void <B>setNoqualifier</B>(java.lang.String&nbsp;noqualifier)</PRE>
<DL>
<DD>Enables the -noqualifier switch, will be ignored if Javadoc is not
 the 1.4 version.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>noqualifier</CODE> - the parameter to the -noqualifier switch<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setIncludeNoSourcePackages(boolean)"><!-- --></A><H3>
setIncludeNoSourcePackages</H3>
<PRE>
public void <B>setIncludeNoSourcePackages</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>If set to true, Ant will also accept packages that only hold
 package.html files but no Java sources.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - a <code>boolean</code> value.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Execute the task.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="expand(java.lang.String)"><!-- --></A><H3>
expand</H3>
<PRE>
protected java.lang.String <B>expand</B>(java.lang.String&nbsp;content)</PRE>
<DL>
<DD>Convenience method to expand properties.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>content</CODE> - the string to expand
<DT><B>Returns:</B><DD>the converted string</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.AccessType.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Javadoc.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Javadoc.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.Task">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
