<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:26 EST 2006 -->
<TITLE>
XMLValidateTask (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.optional.XMLValidateTask class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="XMLValidateTask (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/TraXLiaison.html" title="class in org.apache.tools.ant.taskdefs.optional"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.Attribute.html" title="class in org.apache.tools.ant.taskdefs.optional"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="XMLValidateTask.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.optional</FONT>
<BR>
Class XMLValidateTask</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.optional.XMLValidateTask</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/SchemaValidate.html" title="class in org.apache.tools.ant.taskdefs.optional">SchemaValidate</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>XMLValidateTask</B><DT>extends <A HREF="../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></DL>
</PRE>

<P>
Checks XML files are valid (or only well formed). The
 task uses the SAX2 parser implementation provided by JAXP by default
 (probably the one that is used by Ant itself), but one can specify any
 SAX1/2 parser if needed.
<P>

<P>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.Attribute.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Attribute</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The class to create to set a feature of the parser.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.Property.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Property</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A Parser property.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.ValidatorErrorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.ValidatorErrorHandler</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ValidatorErrorHandler role :
 
  log SAX parse exceptions,
  remember if an error occurred
 </TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#classpath">classpath</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.ValidatorErrorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.ValidatorErrorHandler</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#errorHandler">errorHandler</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#failOnError">failOnError</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#file">file</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;file to be validated</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#filesets">filesets</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sets of file to be validated</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#INIT_FAILED_MSG">INIT_FAILED_MSG</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#lenient">lenient</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#MESSAGE_FILES_VALIDATED">MESSAGE_FILES_VALIDATED</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message for sucessfull validation</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#readerClassName">readerClassName</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#warn">warn</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;org.xml.sax.XMLReader</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#xmlReader">xmlReader</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;the parser is viewed as a SAX2 XMLReader.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#XMLValidateTask()">XMLValidateTask</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#addConfiguredXMLCatalog(org.apache.tools.ant.types.XMLCatalog)">addConfiguredXMLCatalog</A></B>(<A HREF="../../../../../../org/apache/tools/ant/types/XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</A>&nbsp;catalog)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add an XMLCatalog as a nested element; optional.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</A></B>(<A HREF="../../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;set)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;specify a set of file to be checked</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.Attribute.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Attribute</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#createAttribute()">createAttribute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add an attribute nested element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#createClasspath()">createClasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;org.xml.sax.XMLReader</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#createDefaultReader()">createDefaultReader</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;create a reader if the use of the class did not specify another one.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../org/apache/tools/ant/types/DTDLocation.html" title="class in org.apache.tools.ant.types">DTDLocation</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#createDTD()">createDTD</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a DTD location record; optional.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.Property.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Property</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#createProperty()">createProperty</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a property.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;org.xml.sax.XMLReader</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#createXmlReader()">createXmlReader</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;create the XML reader.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#doValidate(java.io.File)">doValidate</A></B>(java.io.File&nbsp;afile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;parse the file</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;execute the task</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;org.xml.sax.EntityResolver</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#getEntityResolver()">getEntityResolver</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;accessor to the xmlCatalog used in the task</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;org.xml.sax.XMLReader</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#getXmlReader()">getXmlReader</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;get the XML reader.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#init()">init</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Called by the project to let the task initialize properly.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#initValidator()">initValidator</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;init the parser :
 load the parser class, and set features if necessary
 It is only after this that the reader is valid</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#isSax1Parser()">isSax1Parser</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;test that returns true if we are using a SAX1 parser.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#onSuccessfulValidation(int)">onSuccessfulValidation</A></B>(int&nbsp;fileProcessed)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;handler called on successful file validation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#setClassName(java.lang.String)">setClassName</A></B>(java.lang.String&nbsp;className)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specify the class name of the SAX parser to be used.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</A></B>(<A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specify the classpath to be searched to load the parser (optional)</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#setClasspathRef(org.apache.tools.ant.types.Reference)">setClasspathRef</A></B>(<A HREF="../../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Where to find the parser class; optional.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#setFailOnError(boolean)">setFailOnError</A></B>(boolean&nbsp;fail)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specify how parser error are to be handled.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#setFeature(java.lang.String, boolean)">setFeature</A></B>(java.lang.String&nbsp;feature,
           boolean&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set a feature on the parser.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#setFile(java.io.File)">setFile</A></B>(java.io.File&nbsp;file)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;specify the file to be checked; optional.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#setLenient(boolean)">setLenient</A></B>(boolean&nbsp;bool)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specify whether the parser should be validating.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#setProperty(java.lang.String, java.lang.String)">setProperty</A></B>(java.lang.String&nbsp;name,
            java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets a property.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#setWarn(boolean)">setWarn</A></B>(boolean&nbsp;bool)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specify how parser error are to be handled.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="INIT_FAILED_MSG"><!-- --></A><H3>
INIT_FAILED_MSG</H3>
<PRE>
protected static final java.lang.String <B>INIT_FAILED_MSG</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.XMLValidateTask.INIT_FAILED_MSG">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="failOnError"><!-- --></A><H3>
failOnError</H3>
<PRE>
protected boolean <B>failOnError</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="warn"><!-- --></A><H3>
warn</H3>
<PRE>
protected boolean <B>warn</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="lenient"><!-- --></A><H3>
lenient</H3>
<PRE>
protected boolean <B>lenient</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="readerClassName"><!-- --></A><H3>
readerClassName</H3>
<PRE>
protected java.lang.String <B>readerClassName</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="file"><!-- --></A><H3>
file</H3>
<PRE>
protected java.io.File <B>file</B></PRE>
<DL>
<DD>file to be validated
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="filesets"><!-- --></A><H3>
filesets</H3>
<PRE>
protected java.util.Vector <B>filesets</B></PRE>
<DL>
<DD>sets of file to be validated
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="classpath"><!-- --></A><H3>
classpath</H3>
<PRE>
protected <A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>classpath</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="xmlReader"><!-- --></A><H3>
xmlReader</H3>
<PRE>
protected org.xml.sax.XMLReader <B>xmlReader</B></PRE>
<DL>
<DD>the parser is viewed as a SAX2 XMLReader. If a SAX1 parser is specified,
 it's wrapped in an adapter that make it behave as a XMLReader.
 a more 'standard' way of doing this would be to use the JAXP1.1 SAXParser
 interface.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="errorHandler"><!-- --></A><H3>
errorHandler</H3>
<PRE>
protected <A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.ValidatorErrorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.ValidatorErrorHandler</A> <B>errorHandler</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="MESSAGE_FILES_VALIDATED"><!-- --></A><H3>
MESSAGE_FILES_VALIDATED</H3>
<PRE>
public static final java.lang.String <B>MESSAGE_FILES_VALIDATED</B></PRE>
<DL>
<DD>Message for sucessfull validation
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.XMLValidateTask.MESSAGE_FILES_VALIDATED">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="XMLValidateTask()"><!-- --></A><H3>
XMLValidateTask</H3>
<PRE>
public <B>XMLValidateTask</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setFailOnError(boolean)"><!-- --></A><H3>
setFailOnError</H3>
<PRE>
public void <B>setFailOnError</B>(boolean&nbsp;fail)</PRE>
<DL>
<DD>Specify how parser error are to be handled.
 Optional, default is <code>true</code>.
 <p>
 If set to <code>true</code> (default), throw a buildException if the
 parser yields an error.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fail</CODE> - if set to <code>false</code> do not fail on error</DL>
</DD>
</DL>
<HR>

<A NAME="setWarn(boolean)"><!-- --></A><H3>
setWarn</H3>
<PRE>
public void <B>setWarn</B>(boolean&nbsp;bool)</PRE>
<DL>
<DD>Specify how parser error are to be handled.
 <p>
 If set to <code>true</true> (default), log a warn message for each SAX warn event.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>bool</CODE> - if set to <code>false</code> do not send warnings</DL>
</DD>
</DL>
<HR>

<A NAME="setLenient(boolean)"><!-- --></A><H3>
setLenient</H3>
<PRE>
public void <B>setLenient</B>(boolean&nbsp;bool)</PRE>
<DL>
<DD>Specify whether the parser should be validating. Default
 is <code>true</code>.
 <p>
 If set to false, the validation will fail only if the parsed document
 is not well formed XML.
 <p>
 this option is ignored if the specified class
 with <A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#setClassName(java.lang.String)"><CODE>setClassName(String)</CODE></A> is not a SAX2 XMLReader.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>bool</CODE> - if set to <code>false</code> only fail on malformed XML</DL>
</DD>
</DL>
<HR>

<A NAME="setClassName(java.lang.String)"><!-- --></A><H3>
setClassName</H3>
<PRE>
public void <B>setClassName</B>(java.lang.String&nbsp;className)</PRE>
<DL>
<DD>Specify the class name of the SAX parser to be used. (optional)
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>className</CODE> - should be an implementation of SAX2
 <code>org.xml.sax.XMLReader</code> or SAX2 <code>org.xml.sax.Parser</code>.
 <p> if className is an implementation of
 <code>org.xml.sax.Parser</code>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#setLenient(boolean)"><CODE>setLenient(boolean)</CODE></A>,
 will be ignored.
 <p> if not set, the default will be used.<DT><B>See Also:</B><DD><CODE>XMLReader</CODE>, 
<CODE>Parser</CODE></DL>
</DD>
</DL>
<HR>

<A NAME="setClasspath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setClasspath</H3>
<PRE>
public void <B>setClasspath</B>(<A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath)</PRE>
<DL>
<DD>Specify the classpath to be searched to load the parser (optional)
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classpath</CODE> - the classpath to load the parser</DL>
</DD>
</DL>
<HR>

<A NAME="createClasspath()"><!-- --></A><H3>
createClasspath</H3>
<PRE>
public <A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createClasspath</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the classpath created<DT><B>See Also:</B><DD><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#setClasspath(org.apache.tools.ant.types.Path)"><CODE>setClasspath(org.apache.tools.ant.types.Path)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setClasspathRef(org.apache.tools.ant.types.Reference)"><!-- --></A><H3>
setClasspathRef</H3>
<PRE>
public void <B>setClasspathRef</B>(<A HREF="../../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</PRE>
<DL>
<DD>Where to find the parser class; optional.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>r</CODE> - reference to a classpath defined elsewhere<DT><B>See Also:</B><DD><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#setClasspath(org.apache.tools.ant.types.Path)"><CODE>setClasspath(org.apache.tools.ant.types.Path)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setFile(java.io.File)"><!-- --></A><H3>
setFile</H3>
<PRE>
public void <B>setFile</B>(java.io.File&nbsp;file)</PRE>
<DL>
<DD>specify the file to be checked; optional.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>file</CODE> - the file to be checked</DL>
</DD>
</DL>
<HR>

<A NAME="addConfiguredXMLCatalog(org.apache.tools.ant.types.XMLCatalog)"><!-- --></A><H3>
addConfiguredXMLCatalog</H3>
<PRE>
public void <B>addConfiguredXMLCatalog</B>(<A HREF="../../../../../../org/apache/tools/ant/types/XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</A>&nbsp;catalog)</PRE>
<DL>
<DD>add an XMLCatalog as a nested element; optional.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>catalog</CODE> - XMLCatalog to use</DL>
</DD>
</DL>
<HR>

<A NAME="addFileset(org.apache.tools.ant.types.FileSet)"><!-- --></A><H3>
addFileset</H3>
<PRE>
public void <B>addFileset</B>(<A HREF="../../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;set)</PRE>
<DL>
<DD>specify a set of file to be checked
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>set</CODE> - the fileset to check</DL>
</DD>
</DL>
<HR>

<A NAME="createAttribute()"><!-- --></A><H3>
createAttribute</H3>
<PRE>
public <A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.Attribute.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Attribute</A> <B>createAttribute</B>()</PRE>
<DL>
<DD>Add an attribute nested element. This is used for setting arbitrary
 features of the SAX parser.
 Valid attributes
 <a href=
 "http://www.saxproject.org/apidoc/org/xml/sax/package-summary.html#package_description"
 >include</a>
<P>
<DD><DL>

<DT><B>Returns:</B><DD>attribute created<DT><B>Since:</B></DT>
  <DD>ant1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="createProperty()"><!-- --></A><H3>
createProperty</H3>
<PRE>
public <A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.Property.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask.Property</A> <B>createProperty</B>()</PRE>
<DL>
<DD>Creates a property.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a property.<DT><B>Since:</B></DT>
  <DD>ant 1.6.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="init()"><!-- --></A><H3>
init</H3>
<PRE>
public void <B>init</B>()
          throws <A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Called by the project to let the task initialize properly.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/Task.html#init()">init</A></CODE> in class <CODE><A HREF="../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if something goes wrong with the build</DL>
</DD>
</DL>
<HR>

<A NAME="createDTD()"><!-- --></A><H3>
createDTD</H3>
<PRE>
public <A HREF="../../../../../../org/apache/tools/ant/types/DTDLocation.html" title="class in org.apache.tools.ant.types">DTDLocation</A> <B>createDTD</B>()</PRE>
<DL>
<DD>Create a DTD location record; optional.
 This stores the location of a DTD. The DTD is identified
 by its public Id.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>created DTD location</DL>
</DD>
</DL>
<HR>

<A NAME="getEntityResolver()"><!-- --></A><H3>
getEntityResolver</H3>
<PRE>
protected org.xml.sax.EntityResolver <B>getEntityResolver</B>()</PRE>
<DL>
<DD>accessor to the xmlCatalog used in the task
<P>
<DD><DL>

<DT><B>Returns:</B><DD>xmlCatalog reference</DL>
</DD>
</DL>
<HR>

<A NAME="getXmlReader()"><!-- --></A><H3>
getXmlReader</H3>
<PRE>
protected org.xml.sax.XMLReader <B>getXmlReader</B>()</PRE>
<DL>
<DD>get the XML reader. Non-null only after <A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#initValidator()"><CODE>initValidator()</CODE></A>.
 If the reader is an instance of  <CODE>ParserAdapter</CODE> then
 the parser is a SAX1 parser, and you cannot call
 <A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#setFeature(java.lang.String, boolean)"><CODE>setFeature(String, boolean)</CODE></A> or <A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#setProperty(java.lang.String, java.lang.String)"><CODE>setProperty(String, String)</CODE></A>
 on it.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the XML reader or null.</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>execute the task
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if <code>failonerror</code> is true and an error happens</DL>
</DD>
</DL>
<HR>

<A NAME="onSuccessfulValidation(int)"><!-- --></A><H3>
onSuccessfulValidation</H3>
<PRE>
protected void <B>onSuccessfulValidation</B>(int&nbsp;fileProcessed)</PRE>
<DL>
<DD>handler called on successful file validation.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fileProcessed</CODE> - number of files processed.</DL>
</DD>
</DL>
<HR>

<A NAME="initValidator()"><!-- --></A><H3>
initValidator</H3>
<PRE>
protected void <B>initValidator</B>()</PRE>
<DL>
<DD>init the parser :
 load the parser class, and set features if necessary
 It is only after this that the reader is valid
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if something went wrong</DL>
</DD>
</DL>
<HR>

<A NAME="isSax1Parser()"><!-- --></A><H3>
isSax1Parser</H3>
<PRE>
protected boolean <B>isSax1Parser</B>()</PRE>
<DL>
<DD>test that returns true if we are using a SAX1 parser.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true when a SAX1 parser is in use</DL>
</DD>
</DL>
<HR>

<A NAME="createXmlReader()"><!-- --></A><H3>
createXmlReader</H3>
<PRE>
protected org.xml.sax.XMLReader <B>createXmlReader</B>()</PRE>
<DL>
<DD>create the XML reader.
 This is one by instantiating anything specified by <A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#readerClassName"><CODE>readerClassName</CODE></A>,
 falling back to a default reader if not.
 If the returned reader is an instance of <CODE>ParserAdapter</CODE> then
 we have created and wrapped a SAX1 parser.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the new XMLReader.</DL>
</DD>
</DL>
<HR>

<A NAME="createDefaultReader()"><!-- --></A><H3>
createDefaultReader</H3>
<PRE>
protected org.xml.sax.XMLReader <B>createDefaultReader</B>()</PRE>
<DL>
<DD>create a reader if the use of the class did not specify another one.
 If a BuildException is thrown, the caller may revert to an alternate
 reader.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a new reader.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if something went wrong</DL>
</DD>
</DL>
<HR>

<A NAME="setFeature(java.lang.String, boolean)"><!-- --></A><H3>
setFeature</H3>
<PRE>
protected void <B>setFeature</B>(java.lang.String&nbsp;feature,
                          boolean&nbsp;value)
                   throws <A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Set a feature on the parser.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>feature</CODE> - the name of the feature to set<DD><CODE>value</CODE> - the value of the feature
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the feature was not supported</DL>
</DD>
</DL>
<HR>

<A NAME="setProperty(java.lang.String, java.lang.String)"><!-- --></A><H3>
setProperty</H3>
<PRE>
protected void <B>setProperty</B>(java.lang.String&nbsp;name,
                           java.lang.String&nbsp;value)
                    throws <A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Sets a property.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - a property name<DD><CODE>value</CODE> - a property value.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if an error occurs.
<DD><CODE><A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the property was not supported</DL>
</DD>
</DL>
<HR>

<A NAME="doValidate(java.io.File)"><!-- --></A><H3>
doValidate</H3>
<PRE>
protected boolean <B>doValidate</B>(java.io.File&nbsp;afile)</PRE>
<DL>
<DD>parse the file
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>afile</CODE> - the file to validate.
<DT><B>Returns:</B><DD>true if the file validates.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/TraXLiaison.html" title="class in org.apache.tools.ant.taskdefs.optional"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.Attribute.html" title="class in org.apache.tools.ant.taskdefs.optional"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="XMLValidateTask.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
