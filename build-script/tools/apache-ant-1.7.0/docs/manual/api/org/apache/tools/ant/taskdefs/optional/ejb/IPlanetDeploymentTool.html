<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:28 EST 2006 -->
<TITLE>
IPlanetDeploymentTool (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.optional.ejb.IPlanetDeploymentTool class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="IPlanetDeploymentTool (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/InnerClassFilenameFilter.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/IPlanetEjbc.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/ejb/IPlanetDeploymentTool.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="IPlanetDeploymentTool.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.optional.ejb</FONT>
<BR>
Class IPlanetDeploymentTool</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool</A>
      <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.optional.ejb.IPlanetDeploymentTool</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>IPlanetDeploymentTool</B><DT>extends <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></DL>
</PRE>

<P>
This class is used to generate iPlanet Application Server (iAS) 6.0 stubs and
 skeletons and build an EJB Jar file.  It is designed to be used with the Ant
 <code>ejbjar</code> task.  If only stubs and skeletons need to be generated
 (in other words, if no JAR file needs to be created), refer to the
 <code>iplanet-ejbc</code> task and the <code>IPlanetEjbcTask</code> class.
 <p>
 The following attributes may be specified by the Employee:
   <ul>
     <li><i>destdir</i> -- The base directory into which the generated JAR
                           files will be written.  Each JAR file is written
                           in directories which correspond to their location
                           within the "descriptordir" namespace.  This is a
                           required attribute.
     <li><i>classpath</i> -- The classpath used when generating EJB stubs and
                             skeletons.  This is an optional attribute (if
                             omitted, the classpath specified in the "ejbjar"
                             parent task will be used).  If specified, the
                             classpath elements will be prepended to the
                             classpath specified in the parent "ejbjar" task.
                             Note that nested "classpath" elements may also be
                             used.
     <li><i>keepgenerated</i> -- Indicates whether or not the Java source
                                 files which are generated by ejbc will be
                                 saved or automatically deleted.  If "yes",
                                 the source files will be retained.  This is
                                 an optional attribute (if omitted, it
                                 defaults to "no").
     <li><i>debug</i> -- Indicates whether or not the ejbc utility should
                         log additional debugging statements to the standard
                         output.  If "yes", the additional debugging statements
                         will be generated (if omitted, it defaults to "no").
     <li><i>iashome</i> -- May be used to specify the "home" directory for
                           this iPlanet Application server installation.  This
                           is used to find the ejbc utility if it isn't
                           included in the Employee's system path.  This is an
                           optional attribute (if specified, it should refer
                           to the <code>[install-location]/iplanet/ias6/ias
                           </code> directory).  If omitted, the ejbc utility
                           must be on the Employee's system path.
     <li><i>suffix</i> -- String value appended to the JAR filename when
                          creating each JAR.  This attribute is not required
                          (if omitted, it defaults to ".jar").
   </ul>
 <p>
 For each EJB descriptor found in the "ejbjar" parent task, this deployment
 tool will locate the three classes that comprise the EJB.  If these class
 files cannot be located in the specified <code>srcdir</code> directory, the
 task will fail.  The task will also attempt to locate the EJB stubs and
 skeletons in this directory.  If found, the timestamps on the stubs and
 skeletons will be checked to ensure they are up to date.  Only if these files
 cannot be found or if they are out of date will ejbc be called.
<P>

<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/IPlanetEjbc.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb"><CODE>IPlanetEjbc</CODE></A></DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.optional.ejb.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#ANALYZER_CLASS_FULL">ANALYZER_CLASS_FULL</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#ANALYZER_CLASS_SUPER">ANALYZER_CLASS_SUPER</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#ANALYZER_FULL">ANALYZER_FULL</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#ANALYZER_NONE">ANALYZER_NONE</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#ANALYZER_SUPER">ANALYZER_SUPER</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#DEFAULT_ANALYZER">DEFAULT_ANALYZER</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#DEFAULT_BUFFER_SIZE">DEFAULT_BUFFER_SIZE</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#EJB_DD">EJB_DD</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#JAR_COMPRESS_LEVEL">JAR_COMPRESS_LEVEL</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#MANIFEST">MANIFEST</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#META_DIR">META_DIR</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/IPlanetDeploymentTool.html#IPlanetDeploymentTool()">IPlanetDeploymentTool</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/IPlanetDeploymentTool.html#addVendorFiles(java.util.Hashtable, java.lang.String)">addVendorFiles</A></B>(java.util.Hashtable&nbsp;ejbFiles,
               java.lang.String&nbsp;ddPrefix)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add the iAS-specific EJB descriptor to the list of files which will be
 written to the JAR file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/IPlanetDeploymentTool.html#checkConfiguration(java.lang.String, javax.xml.parsers.SAXParser)">checkConfiguration</A></B>(java.lang.String&nbsp;descriptorFileName,
                   javax.xml.parsers.SAXParser&nbsp;saxParser)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Verifies that the Employee selections are valid.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/IPlanetDeploymentTool.html#getPublicId()">getPublicId</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The iAS ejbc utility doesn't require the Public ID of the descriptor's
 DTD for it to process correctly--this method always returns <code>null
 </code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/IPlanetDeploymentTool.html#parseEjbFiles(java.lang.String, javax.xml.parsers.SAXParser)">parseEjbFiles</A></B>(java.lang.String&nbsp;descriptorFileName,
              javax.xml.parsers.SAXParser&nbsp;saxParser)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This method returns a list of EJB files found when the specified EJB
 descriptor is parsed and processed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/IPlanetDeploymentTool.html#processDescriptor(java.lang.String, javax.xml.parsers.SAXParser)">processDescriptor</A></B>(java.lang.String&nbsp;descriptorName,
                  javax.xml.parsers.SAXParser&nbsp;saxParser)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Process a deployment descriptor, generating the necessary vendor specific
 deployment files...</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/IPlanetDeploymentTool.html#setDebug(boolean)">setDebug</A></B>(boolean&nbsp;debug)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets whether or not debugging output will be generated when ejbc is
 executed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/IPlanetDeploymentTool.html#setGenericJarSuffix(java.lang.String)">setGenericJarSuffix</A></B>(java.lang.String&nbsp;inString)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Since iAS doesn't generate a "generic" JAR as part of its processing,
 this attribute is ignored and a warning message is displayed to the Employee.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/IPlanetDeploymentTool.html#setIashome(java.io.File)">setIashome</A></B>(java.io.File&nbsp;iashome)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Setter method used to store the "home" directory of the Employee's iAS
 installation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/IPlanetDeploymentTool.html#setKeepgenerated(boolean)">setKeepgenerated</A></B>(boolean&nbsp;keepgenerated)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Setter method used to specify whether the Java source files generated by
 the ejbc utility should be saved or automatically deleted.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/IPlanetDeploymentTool.html#setSuffix(java.lang.String)">setSuffix</A></B>(java.lang.String&nbsp;jarSuffix)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Setter method used to specify the filename suffix (for example, ".jar")
 for the JAR files to be created.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.optional.ejb.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#addFileToJar(java.util.jar.JarOutputStream, java.io.File, java.lang.String)">addFileToJar</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#addSupportClasses(java.util.Hashtable)">addSupportClasses</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#checkAndAddDependants(java.util.Hashtable)">checkAndAddDependants</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#configure(org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.Config)">configure</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#createClasspath()">createClasspath</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getClassLoaderForBuild()">getClassLoaderForBuild</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getCombinedClasspath()">getCombinedClasspath</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getConfig()">getConfig</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getDescriptorHandler(java.io.File)">getDescriptorHandler</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getDestDir()">getDestDir</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getJarBaseName(java.lang.String)">getJarBaseName</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getLocation()">getLocation</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getManifestFile(java.lang.String)">getManifestFile</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getTask()">getTask</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getVendorDDPrefix(java.lang.String, java.lang.String)">getVendorDDPrefix</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#needToRebuild(java.util.Hashtable, java.io.File)">needToRebuild</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#registerKnownDTDs(org.apache.tools.ant.taskdefs.optional.ejb.DescriptorHandler)">registerKnownDTDs</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#setDestdir(java.io.File)">setDestdir</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#setTask(org.apache.tools.ant.Task)">setTask</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#usingBaseJarName()">usingBaseJarName</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#validateConfigured()">validateConfigured</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#writeJar(java.lang.String, java.io.File, java.util.Hashtable, java.lang.String)">writeJar</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="IPlanetDeploymentTool()"><!-- --></A><H3>
IPlanetDeploymentTool</H3>
<PRE>
public <B>IPlanetDeploymentTool</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setIashome(java.io.File)"><!-- --></A><H3>
setIashome</H3>
<PRE>
public void <B>setIashome</B>(java.io.File&nbsp;iashome)</PRE>
<DL>
<DD>Setter method used to store the "home" directory of the Employee's iAS
 installation.  The directory specified should typically be
 <code>[install-location]/iplanet/ias6/ias</code>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>iashome</CODE> - The home directory for the Employee's iAS installation.</DL>
</DD>
</DL>
<HR>

<A NAME="setKeepgenerated(boolean)"><!-- --></A><H3>
setKeepgenerated</H3>
<PRE>
public void <B>setKeepgenerated</B>(boolean&nbsp;keepgenerated)</PRE>
<DL>
<DD>Setter method used to specify whether the Java source files generated by
 the ejbc utility should be saved or automatically deleted.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>keepgenerated</CODE> - boolean which, if <code>true</code>, indicates that
                      Java source files generated by ejbc for the stubs
                      and skeletons should be kept.</DL>
</DD>
</DL>
<HR>

<A NAME="setDebug(boolean)"><!-- --></A><H3>
setDebug</H3>
<PRE>
public void <B>setDebug</B>(boolean&nbsp;debug)</PRE>
<DL>
<DD>Sets whether or not debugging output will be generated when ejbc is
 executed.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>debug</CODE> - A boolean indicating if debugging output should be generated</DL>
</DD>
</DL>
<HR>

<A NAME="setSuffix(java.lang.String)"><!-- --></A><H3>
setSuffix</H3>
<PRE>
public void <B>setSuffix</B>(java.lang.String&nbsp;jarSuffix)</PRE>
<DL>
<DD>Setter method used to specify the filename suffix (for example, ".jar")
 for the JAR files to be created.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>jarSuffix</CODE> - The string to use as the JAR filename suffix.</DL>
</DD>
</DL>
<HR>

<A NAME="setGenericJarSuffix(java.lang.String)"><!-- --></A><H3>
setGenericJarSuffix</H3>
<PRE>
public void <B>setGenericJarSuffix</B>(java.lang.String&nbsp;inString)</PRE>
<DL>
<DD>Since iAS doesn't generate a "generic" JAR as part of its processing,
 this attribute is ignored and a warning message is displayed to the Employee.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#setGenericJarSuffix(java.lang.String)">setGenericJarSuffix</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>inString</CODE> - the string to use as the suffix.  This parameter is
                 ignored.</DL>
</DD>
</DL>
<HR>

<A NAME="processDescriptor(java.lang.String, javax.xml.parsers.SAXParser)"><!-- --></A><H3>
processDescriptor</H3>
<PRE>
public void <B>processDescriptor</B>(java.lang.String&nbsp;descriptorName,
                              javax.xml.parsers.SAXParser&nbsp;saxParser)</PRE>
<DL>
<DD>Process a deployment descriptor, generating the necessary vendor specific
 deployment files...
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/EJBDeploymentTool.html#processDescriptor(java.lang.String, javax.xml.parsers.SAXParser)">processDescriptor</A></CODE> in interface <CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</A></CODE><DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#processDescriptor(java.lang.String, javax.xml.parsers.SAXParser)">processDescriptor</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>descriptorName</CODE> - the name of the deployment descriptor<DD><CODE>saxParser</CODE> - a SAX parser which can be used to parse the deployment descriptor.</DL>
</DD>
</DL>
<HR>

<A NAME="checkConfiguration(java.lang.String, javax.xml.parsers.SAXParser)"><!-- --></A><H3>
checkConfiguration</H3>
<PRE>
protected void <B>checkConfiguration</B>(java.lang.String&nbsp;descriptorFileName,
                                  javax.xml.parsers.SAXParser&nbsp;saxParser)
                           throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Verifies that the Employee selections are valid.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#checkConfiguration(java.lang.String, javax.xml.parsers.SAXParser)">checkConfiguration</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>descriptorFileName</CODE> - String representing the file name of an EJB
                           descriptor to be processed<DD><CODE>saxParser</CODE> - SAXParser which may be used to parse the XML
                           descriptor
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - If the Employee selections are invalid.</DL>
</DD>
</DL>
<HR>

<A NAME="parseEjbFiles(java.lang.String, javax.xml.parsers.SAXParser)"><!-- --></A><H3>
parseEjbFiles</H3>
<PRE>
protected java.util.Hashtable <B>parseEjbFiles</B>(java.lang.String&nbsp;descriptorFileName,
                                            javax.xml.parsers.SAXParser&nbsp;saxParser)
                                     throws java.io.IOException,
                                            org.xml.sax.SAXException</PRE>
<DL>
<DD>This method returns a list of EJB files found when the specified EJB
 descriptor is parsed and processed.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#parseEjbFiles(java.lang.String, javax.xml.parsers.SAXParser)">parseEjbFiles</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>descriptorFileName</CODE> - String representing the file name of an EJB
                           descriptor to be processed<DD><CODE>saxParser</CODE> - SAXParser which may be used to parse the XML
                           descriptor
<DT><B>Returns:</B><DD>Hashtable of EJB class (and other) files to be
                           added to the completed JAR file
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - An IOException from the parser, possibly from
                           the byte stream or character stream
<DD><CODE>org.xml.sax.SAXException</CODE> - Any SAX exception, possibly wrapping another
                           exception</DL>
</DD>
</DL>
<HR>

<A NAME="addVendorFiles(java.util.Hashtable, java.lang.String)"><!-- --></A><H3>
addVendorFiles</H3>
<PRE>
protected void <B>addVendorFiles</B>(java.util.Hashtable&nbsp;ejbFiles,
                              java.lang.String&nbsp;ddPrefix)</PRE>
<DL>
<DD>Add the iAS-specific EJB descriptor to the list of files which will be
 written to the JAR file.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#addVendorFiles(java.util.Hashtable, java.lang.String)">addVendorFiles</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ejbFiles</CODE> - Hashtable of EJB class (and other) files to be added to
                 the completed JAR file.<DD><CODE>ddPrefix</CODE> - not used</DL>
</DD>
</DL>
<HR>

<A NAME="getPublicId()"><!-- --></A><H3>
getPublicId</H3>
<PRE>
protected java.lang.String <B>getPublicId</B>()</PRE>
<DL>
<DD>The iAS ejbc utility doesn't require the Public ID of the descriptor's
 DTD for it to process correctly--this method always returns <code>null
 </code>.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getPublicId()">getPublicId</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD><code>null</code>.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/InnerClassFilenameFilter.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/IPlanetEjbc.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/ejb/IPlanetDeploymentTool.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="IPlanetDeploymentTool.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
