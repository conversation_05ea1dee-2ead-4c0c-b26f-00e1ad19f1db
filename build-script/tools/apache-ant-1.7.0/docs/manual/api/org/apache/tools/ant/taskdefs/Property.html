<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:24 EST 2006 -->
<TITLE>
Property (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.Property class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Property (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Property.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Property.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class Property</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.Property</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>Property</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></DL>
</PRE>

<P>
Sets a property by name, or set of properties (from file or
 resource) in the project.  </p>
 Properties are immutable: whoever sets a property first freezes it for the
 rest of the build; they are most definitely not variable.
 <p>There are five ways to set properties:</p>
 <ul>
   <li>By supplying both the <i>name</i> and <i>value</i> attribute.</li>
   <li>By supplying both the <i>name</i> and <i>refid</i> attribute.</li>
   <li>By setting the <i>file</i> attribute with the filename of the property
     file to load. This property file has the format as defined by the file used
     in the class java.util.Properties.</li>
   <li>By setting the <i>resource</i> attribute with the resource name of the
     property file to load. This property file has the format as defined by the
     file used in the class java.util.Properties.</li>
   <li>By setting the <i>environment</i> attribute with a prefix to use.
     Properties will be defined for every environment variable by
     prefixing the supplied name and a period to the name of the variable.</li>
 </ul>
 <p>Although combinations of these ways are possible, only one should be used
 at a time. Problems might occur with the order in which properties are set, for
 instance.</p>
 <p>The value part of the properties being set, might contain references to other
 properties. These references are resolved at the time these properties are set.
 This also holds for properties loaded from a property file.</p>
 Properties are case sensitive.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.1</DD>
</DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#classpath">classpath</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#env">env</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#file">file</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#name">name</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#prefix">prefix</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#ref">ref</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#resource">resource</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.net.URL</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#url">url</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#userProperty">userProperty</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#value">value</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#Property()">Property</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for Property.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected </CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#Property(boolean)">Property</A></B>(boolean&nbsp;userProperty)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for Property.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected </CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#Property(boolean, org.apache.tools.ant.Project)">Property</A></B>(boolean&nbsp;userProperty,
         <A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;fallback)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for Property.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#addProperties(java.util.Properties)">addProperties</A></B>(java.util.Properties&nbsp;props)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iterate through a set of properties,
 resolve them then assign them</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#addProperty(java.lang.String, java.lang.String)">addProperty</A></B>(java.lang.String&nbsp;n,
            java.lang.String&nbsp;v)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a name value pair to the project property set</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#createClasspath()">createClasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The classpath to use when looking up a resource.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;set the property in the project to the value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#getClasspath()">getClasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the classpath used when looking up a resource.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#getEnvironment()">getEnvironment</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the environment attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#getFile()">getFile</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the file attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#getName()">getName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the property name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#getPrefix()">getPrefix</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the prefix attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#getRefid()">getRefid</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the refid attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#getResource()">getResource</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the resource attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.net.URL</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#getUrl()">getUrl</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the url attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#getValue()">getValue</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the property value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#loadEnvironment(java.lang.String)">loadEnvironment</A></B>(java.lang.String&nbsp;prefix)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;load the environment values</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#loadFile(java.io.File)">loadFile</A></B>(java.io.File&nbsp;file)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;load properties from a file</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#loadResource(java.lang.String)">loadResource</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;load properties from a resource in the current classpath</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#loadUrl(java.net.URL)">loadUrl</A></B>(java.net.URL&nbsp;url)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;load properties from a url</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The classpath to use when looking up a resource.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#setClasspathRef(org.apache.tools.ant.types.Reference)">setClasspathRef</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;the classpath to use when looking up a resource,
 given as reference to a &lt;path&gt; defined elsewhere</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#setEnvironment(java.lang.String)">setEnvironment</A></B>(java.lang.String&nbsp;env)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Prefix to use when retrieving environment variables.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#setFile(java.io.File)">setFile</A></B>(java.io.File&nbsp;file)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Filename of a property file to load.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#setLocation(java.io.File)">setLocation</A></B>(java.io.File&nbsp;location)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the property to the absolute filename of the
 given file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#setName(java.lang.String)">setName</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The name of the property to set.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#setPrefix(java.lang.String)">setPrefix</A></B>(java.lang.String&nbsp;prefix)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Prefix to apply to properties loaded using <code>file</code>
 or <code>resource</code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;ref)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets a reference to an Ant datatype
 declared elsewhere.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#setResource(java.lang.String)">setResource</A></B>(java.lang.String&nbsp;resource)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The resource name of a property file to load</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#setUrl(java.net.URL)">setUrl</A></B>(java.net.URL&nbsp;url)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The url from which to load properties.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#setUserProperty(boolean)">setUserProperty</A></B>(boolean&nbsp;userProperty)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             This was never a supported feature and has been
             deprecated without replacement.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#setValue(java.lang.String)">setValue</A></B>(java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The value of the property.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html#toString()">toString</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;get the value of this property</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="name"><!-- --></A><H3>
name</H3>
<PRE>
protected java.lang.String <B>name</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="value"><!-- --></A><H3>
value</H3>
<PRE>
protected java.lang.String <B>value</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="file"><!-- --></A><H3>
file</H3>
<PRE>
protected java.io.File <B>file</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="url"><!-- --></A><H3>
url</H3>
<PRE>
protected java.net.URL <B>url</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="resource"><!-- --></A><H3>
resource</H3>
<PRE>
protected java.lang.String <B>resource</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="classpath"><!-- --></A><H3>
classpath</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>classpath</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="env"><!-- --></A><H3>
env</H3>
<PRE>
protected java.lang.String <B>env</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="ref"><!-- --></A><H3>
ref</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A> <B>ref</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="prefix"><!-- --></A><H3>
prefix</H3>
<PRE>
protected java.lang.String <B>prefix</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="userProperty"><!-- --></A><H3>
userProperty</H3>
<PRE>
protected boolean <B>userProperty</B></PRE>
<DL>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Property()"><!-- --></A><H3>
Property</H3>
<PRE>
public <B>Property</B>()</PRE>
<DL>
<DD>Constructor for Property.
<P>
</DL>
<HR>

<A NAME="Property(boolean)"><!-- --></A><H3>
Property</H3>
<PRE>
protected <B>Property</B>(boolean&nbsp;userProperty)</PRE>
<DL>
<DD>Constructor for Property.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>userProperty</CODE> - if true this is a Employee property<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DL>
<HR>

<A NAME="Property(boolean, org.apache.tools.ant.Project)"><!-- --></A><H3>
Property</H3>
<PRE>
protected <B>Property</B>(boolean&nbsp;userProperty,
                   <A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;fallback)</PRE>
<DL>
<DD>Constructor for Property.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>userProperty</CODE> - if true this is a Employee property<DD><CODE>fallback</CODE> - a project to use to look for references if the reference is
                 not in the current project<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setName(java.lang.String)"><!-- --></A><H3>
setName</H3>
<PRE>
public void <B>setName</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>The name of the property to set.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - property name</DL>
</DD>
</DL>
<HR>

<A NAME="getName()"><!-- --></A><H3>
getName</H3>
<PRE>
public java.lang.String <B>getName</B>()</PRE>
<DL>
<DD>Get the property name.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the property name</DL>
</DD>
</DL>
<HR>

<A NAME="setLocation(java.io.File)"><!-- --></A><H3>
setLocation</H3>
<PRE>
public void <B>setLocation</B>(java.io.File&nbsp;location)</PRE>
<DL>
<DD>Sets the property to the absolute filename of the
 given file. If the value of this attribute is an absolute path, it
 is left unchanged (with / and \ characters converted to the
 current platforms conventions). Otherwise it is taken as a path
 relative to the project's basedir and expanded.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>location</CODE> - path to set</DL>
</DD>
</DL>
<HR>

<A NAME="setValue(java.lang.String)"><!-- --></A><H3>
setValue</H3>
<PRE>
public void <B>setValue</B>(java.lang.String&nbsp;value)</PRE>
<DL>
<DD>The value of the property.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - value to assign</DL>
</DD>
</DL>
<HR>

<A NAME="getValue()"><!-- --></A><H3>
getValue</H3>
<PRE>
public java.lang.String <B>getValue</B>()</PRE>
<DL>
<DD>Get the property value.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the property value</DL>
</DD>
</DL>
<HR>

<A NAME="setFile(java.io.File)"><!-- --></A><H3>
setFile</H3>
<PRE>
public void <B>setFile</B>(java.io.File&nbsp;file)</PRE>
<DL>
<DD>Filename of a property file to load.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>file</CODE> - filename</DL>
</DD>
</DL>
<HR>

<A NAME="getFile()"><!-- --></A><H3>
getFile</H3>
<PRE>
public java.io.File <B>getFile</B>()</PRE>
<DL>
<DD>Get the file attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the file attribute</DL>
</DD>
</DL>
<HR>

<A NAME="setUrl(java.net.URL)"><!-- --></A><H3>
setUrl</H3>
<PRE>
public void <B>setUrl</B>(java.net.URL&nbsp;url)</PRE>
<DL>
<DD>The url from which to load properties.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>url</CODE> - url string</DL>
</DD>
</DL>
<HR>

<A NAME="getUrl()"><!-- --></A><H3>
getUrl</H3>
<PRE>
public java.net.URL <B>getUrl</B>()</PRE>
<DL>
<DD>Get the url attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the url attribute</DL>
</DD>
</DL>
<HR>

<A NAME="setPrefix(java.lang.String)"><!-- --></A><H3>
setPrefix</H3>
<PRE>
public void <B>setPrefix</B>(java.lang.String&nbsp;prefix)</PRE>
<DL>
<DD>Prefix to apply to properties loaded using <code>file</code>
 or <code>resource</code>.
 A "." is appended to the prefix if not specified.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>prefix</CODE> - prefix string<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getPrefix()"><!-- --></A><H3>
getPrefix</H3>
<PRE>
public java.lang.String <B>getPrefix</B>()</PRE>
<DL>
<DD>Get the prefix attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the prefix attribute<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setRefid(org.apache.tools.ant.types.Reference)"><!-- --></A><H3>
setRefid</H3>
<PRE>
public void <B>setRefid</B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;ref)</PRE>
<DL>
<DD>Sets a reference to an Ant datatype
 declared elsewhere.
 Only yields reasonable results for references
 PATH like structures or properties.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ref</CODE> - reference</DL>
</DD>
</DL>
<HR>

<A NAME="getRefid()"><!-- --></A><H3>
getRefid</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A> <B>getRefid</B>()</PRE>
<DL>
<DD>Get the refid attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the refid attribute</DL>
</DD>
</DL>
<HR>

<A NAME="setResource(java.lang.String)"><!-- --></A><H3>
setResource</H3>
<PRE>
public void <B>setResource</B>(java.lang.String&nbsp;resource)</PRE>
<DL>
<DD>The resource name of a property file to load
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>resource</CODE> - resource on classpath</DL>
</DD>
</DL>
<HR>

<A NAME="getResource()"><!-- --></A><H3>
getResource</H3>
<PRE>
public java.lang.String <B>getResource</B>()</PRE>
<DL>
<DD>Get the resource attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the resource attribute</DL>
</DD>
</DL>
<HR>

<A NAME="setEnvironment(java.lang.String)"><!-- --></A><H3>
setEnvironment</H3>
<PRE>
public void <B>setEnvironment</B>(java.lang.String&nbsp;env)</PRE>
<DL>
<DD>Prefix to use when retrieving environment variables.
 Thus if you specify environment=&quot;myenv&quot;
 you will be able to access OS-specific
 environment variables via property names &quot;myenv.PATH&quot; or
 &quot;myenv.TERM&quot;.
 <p>
 Note that if you supply a property name with a final
 &quot;.&quot; it will not be doubled. ie environment=&quot;myenv.&quot; will still
 allow access of environment variables through &quot;myenv.PATH&quot; and
 &quot;myenv.TERM&quot;. This functionality is currently only implemented
 on select platforms. Feel free to send patches to increase the number of platforms
 this functionality is supported on ;).<br>
 Note also that properties are case sensitive, even if the
 environment variables on your operating system are not, e.g. it
 will be ${env.Path} not ${env.PATH} on Windows 2000.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>env</CODE> - prefix</DL>
</DD>
</DL>
<HR>

<A NAME="getEnvironment()"><!-- --></A><H3>
getEnvironment</H3>
<PRE>
public java.lang.String <B>getEnvironment</B>()</PRE>
<DL>
<DD>Get the environment attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the environment attribute<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setClasspath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setClasspath</H3>
<PRE>
public void <B>setClasspath</B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath)</PRE>
<DL>
<DD>The classpath to use when looking up a resource.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classpath</CODE> - to add to any existing classpath</DL>
</DD>
</DL>
<HR>

<A NAME="createClasspath()"><!-- --></A><H3>
createClasspath</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createClasspath</B>()</PRE>
<DL>
<DD>The classpath to use when looking up a resource.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a path to be configured</DL>
</DD>
</DL>
<HR>

<A NAME="setClasspathRef(org.apache.tools.ant.types.Reference)"><!-- --></A><H3>
setClasspathRef</H3>
<PRE>
public void <B>setClasspathRef</B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</PRE>
<DL>
<DD>the classpath to use when looking up a resource,
 given as reference to a &lt;path&gt; defined elsewhere
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>r</CODE> - a reference to a classpath</DL>
</DD>
</DL>
<HR>

<A NAME="getClasspath()"><!-- --></A><H3>
getClasspath</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>getClasspath</B>()</PRE>
<DL>
<DD>Get the classpath used when looking up a resource.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the classpath<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setUserProperty(boolean)"><!-- --></A><H3>
setUserProperty</H3>
<PRE>
public void <B>setUserProperty</B>(boolean&nbsp;userProperty)</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             This was never a supported feature and has been
             deprecated without replacement.</I>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>userProperty</CODE> - ignored</DL>
</DD>
</DL>
<HR>

<A NAME="toString()"><!-- --></A><H3>
toString</H3>
<PRE>
public java.lang.String <B>toString</B>()</PRE>
<DL>
<DD>get the value of this property
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>toString</CODE> in class <CODE>java.lang.Object</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the current value or the empty string</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>set the property in the project to the value.
 if the task was give a file, resource or env attribute
 here is where it is loaded
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="loadUrl(java.net.URL)"><!-- --></A><H3>
loadUrl</H3>
<PRE>
protected void <B>loadUrl</B>(java.net.URL&nbsp;url)
                throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>load properties from a url
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>url</CODE> - url to load from
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="loadFile(java.io.File)"><!-- --></A><H3>
loadFile</H3>
<PRE>
protected void <B>loadFile</B>(java.io.File&nbsp;file)
                 throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>load properties from a file
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>file</CODE> - file to load
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="loadResource(java.lang.String)"><!-- --></A><H3>
loadResource</H3>
<PRE>
protected void <B>loadResource</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>load properties from a resource in the current classpath
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - name of resource to load</DL>
</DD>
</DL>
<HR>

<A NAME="loadEnvironment(java.lang.String)"><!-- --></A><H3>
loadEnvironment</H3>
<PRE>
protected void <B>loadEnvironment</B>(java.lang.String&nbsp;prefix)</PRE>
<DL>
<DD>load the environment values
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>prefix</CODE> - prefix to place before them</DL>
</DD>
</DL>
<HR>

<A NAME="addProperties(java.util.Properties)"><!-- --></A><H3>
addProperties</H3>
<PRE>
protected void <B>addProperties</B>(java.util.Properties&nbsp;props)</PRE>
<DL>
<DD>iterate through a set of properties,
 resolve them then assign them
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>props</CODE> - the properties to iterate over</DL>
</DD>
</DL>
<HR>

<A NAME="addProperty(java.lang.String, java.lang.String)"><!-- --></A><H3>
addProperty</H3>
<PRE>
protected void <B>addProperty</B>(java.lang.String&nbsp;n,
                           java.lang.String&nbsp;v)</PRE>
<DL>
<DD>add a name value pair to the project property set
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>n</CODE> - name of property<DD><CODE>v</CODE> - value to set</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Property.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Property.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
