<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:27 EST 2006 -->
<TITLE>
MethodRefCPInfo (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.optional.depend.constantpool.MethodRefCPInfo class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="MethodRefCPInfo (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/LongCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/NameAndTypeCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodRefCPInfo.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="MethodRefCPInfo.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.optional.depend.constantpool</FONT>
<BR>
Class MethodRefCPInfo</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry</A>
      <IMG SRC="../../../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.optional.depend.constantpool.MethodRefCPInfo</B>
</PRE>
<HR>
<DL>
<DT><PRE>public class <B>MethodRefCPInfo</B><DT>extends <A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</A></DL>
</PRE>

<P>
A MethodRef CP Info
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.optional.depend.constantpool.<A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_CLASS">CONSTANT_CLASS</A>, <A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_DOUBLE">CONSTANT_DOUBLE</A>, <A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_FIELDREF">CONSTANT_FIELDREF</A>, <A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_FLOAT">CONSTANT_FLOAT</A>, <A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_INTEGER">CONSTANT_INTEGER</A>, <A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_INTERFACEMETHODREF">CONSTANT_INTERFACEMETHODREF</A>, <A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_LONG">CONSTANT_LONG</A>, <A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_METHODREF">CONSTANT_METHODREF</A>, <A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_NAMEANDTYPE">CONSTANT_NAMEANDTYPE</A>, <A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_STRING">CONSTANT_STRING</A>, <A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_UTF8">CONSTANT_UTF8</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodRefCPInfo.html#MethodRefCPInfo()">MethodRefCPInfo</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodRefCPInfo.html#getMethodClassName()">getMethodClassName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the name of the class defining the method</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodRefCPInfo.html#getMethodName()">getMethodName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the name of the method.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodRefCPInfo.html#getMethodType()">getMethodType</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the type signature of the method.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodRefCPInfo.html#read(java.io.DataInputStream)">read</A></B>(java.io.DataInputStream&nbsp;cpStream)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;read a constant pool entry from a class stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodRefCPInfo.html#resolve(org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPool)">resolve</A></B>(<A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPool</A>&nbsp;constantPool)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Resolve this constant pool entry with respect to its dependents in
 the constant pool.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodRefCPInfo.html#toString()">toString</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Print a readable version of the constant pool entry.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.optional.depend.constantpool.<A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#getNumEntries()">getNumEntries</A>, <A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#getTag()">getTag</A>, <A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#isResolved()">isResolved</A>, <A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#readEntry(java.io.DataInputStream)">readEntry</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="MethodRefCPInfo()"><!-- --></A><H3>
MethodRefCPInfo</H3>
<PRE>
public <B>MethodRefCPInfo</B>()</PRE>
<DL>
<DD>Constructor.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="read(java.io.DataInputStream)"><!-- --></A><H3>
read</H3>
<PRE>
public void <B>read</B>(java.io.DataInputStream&nbsp;cpStream)
          throws java.io.IOException</PRE>
<DL>
<DD>read a constant pool entry from a class stream.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#read(java.io.DataInputStream)">read</A></CODE> in class <CODE><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>cpStream</CODE> - the DataInputStream which contains the constant pool
      entry to be read.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there is a problem reading the entry from
      the stream.</DL>
</DD>
</DL>
<HR>

<A NAME="toString()"><!-- --></A><H3>
toString</H3>
<PRE>
public java.lang.String <B>toString</B>()</PRE>
<DL>
<DD>Print a readable version of the constant pool entry.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>toString</CODE> in class <CODE>java.lang.Object</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the string representation of this constant pool entry.</DL>
</DD>
</DL>
<HR>

<A NAME="resolve(org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPool)"><!-- --></A><H3>
resolve</H3>
<PRE>
public void <B>resolve</B>(<A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPool</A>&nbsp;constantPool)</PRE>
<DL>
<DD>Resolve this constant pool entry with respect to its dependents in
 the constant pool.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#resolve(org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPool)">resolve</A></CODE> in class <CODE><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>constantPool</CODE> - the constant pool of which this entry is a member
      and against which this entry is to be resolved.</DL>
</DD>
</DL>
<HR>

<A NAME="getMethodClassName()"><!-- --></A><H3>
getMethodClassName</H3>
<PRE>
public java.lang.String <B>getMethodClassName</B>()</PRE>
<DL>
<DD>Get the name of the class defining the method
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the name of the class defining this method</DL>
</DD>
</DL>
<HR>

<A NAME="getMethodName()"><!-- --></A><H3>
getMethodName</H3>
<PRE>
public java.lang.String <B>getMethodName</B>()</PRE>
<DL>
<DD>Get the name of the method.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the name of the method.</DL>
</DD>
</DL>
<HR>

<A NAME="getMethodType()"><!-- --></A><H3>
getMethodType</H3>
<PRE>
public java.lang.String <B>getMethodType</B>()</PRE>
<DL>
<DD>Get the type signature of the method.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the type signature of the method.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/LongCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/NameAndTypeCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/depend/constantpool/MethodRefCPInfo.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="MethodRefCPInfo.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
