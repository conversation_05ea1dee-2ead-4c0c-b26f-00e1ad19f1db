<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:25 EST 2006 -->
<TITLE>
XmlProperty (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.XmlProperty class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="XmlProperty (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/WhichResource.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/XmlProperty.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="XmlProperty.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.Task">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class XmlProperty</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.XmlProperty</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>XmlProperty</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></DL>
</PRE>

<P>
Loads property values from a valid XML file, generating the
 property names from the file's element and attribute names.

 <p>Example:</p>
 <pre>
   &lt;root-tag myattr="true"&gt;
     &lt;inner-tag someattr="val"&gt;Text&lt;/inner-tag&gt;
     &lt;a2&gt;&lt;a3&gt;&lt;a4&gt;false&lt;/a4&gt;&lt;/a3&gt;&lt;/a2&gt;
     &lt;x&gt;x1&lt;/x&gt;
     &lt;x&gt;x2&lt;/x&gt;
   &lt;/root-tag&gt;
</pre>

 <p>this generates the following properties:</p>

 <pre>
  root-tag(myattr)=true
  root-tag.inner-tag=Text
  root-tag.inner-tag(someattr)=val
  root-tag.a2.a3.a4=false
  root-tag.x=x1,x2
 </pre>

 <p>The <i>collapseAttributes</i> property of this task can be set
 to true (the default is false) which will instead result in the
 following properties (note the difference in names of properties
 corresponding to XML attributes):</p>

 <pre>
  root-tag.myattr=true
  root-tag.inner-tag=Text
  root-tag.inner-tag.someattr=val
  root-tag.a2.a3.a4=false
  root-tag.x=x1,x2
 </pre>

 <p>Optionally, to more closely mirror the abilities of the Property
 task, a selected set of attributes can be treated specially.  To
 enable this behavior, the "semanticAttributes" property of this task
 must be set to true (it defaults to false).  If this attribute is
 specified, the following attributes take on special meaning
 (setting this to true implicitly sets collapseAttributes to true as
 well):</p>

 <ul>
  <li><b>value</b>: Identifies a text value for a property.</li>
  <li><b>location</b>: Identifies a file location for a property.</li>
  <li><b>id</b>: Sets an id for a property</li>
  <li><b>refid</b>: Sets a property to the value of another property
       based upon the provided id</li>
  <li><b>pathid</b>: Defines a path rather than a property with
       the given id.</li>
 </ul>

 <p>For example, with keepRoot = false, the following properties file:</p>

 <pre>
 &lt;root-tag&gt;
   &lt;build&gt;
   &lt;build folder="build"&gt;
     &lt;classes id="build.classes" location="${build.folder}/classes"/&gt;
     &lt;reference refid="build.classes"/&gt;
   &lt;/build&gt;
   &lt;compile&gt;
     &lt;classpath pathid="compile.classpath"&gt;
       &lt;pathelement location="${build.classes}"/&gt;
     &lt;/classpath&gt;
   &lt;/compile&gt;
   &lt;run-time&gt;
     &lt;jars&gt;*.jar&lt;/jars&gt;
     &lt;classpath pathid="run-time.classpath"&gt;
       &lt;path refid="compile.classpath"/&gt;
       &lt;pathelement path="${run-time.jars}"/&gt;
     &lt;/classpath&gt;
   &lt;/run-time&gt;
 &lt;/root-tag&gt;
 </pre>

 <p>is equivalent to the following entries in a build file:</p>

 <pre>
 &lt;property name="build" location="build"/&gt;
 &lt;property name="build.classes" location="${build.location}/classes"/&gt;
 &lt;property name="build.reference" refid="build.classes"/&gt;

 &lt;property name="run-time.jars" value="*.jar/&gt;

 &lt;classpath id="compile.classpath"&gt;
   &lt;pathelement location="${build.classes}"/&gt;
 &lt;/classpath&gt;

 &lt;classpath id="run-time.classpath"&gt;
   &lt;path refid="compile.classpath"/&gt;
   &lt;pathelement path="${run-time.jars}"/&gt;
 &lt;/classpath&gt;
 </pre>

 <p> This task <i>requires</i> the following attributes:</p>

 <ul>
 <li><b>file</b>: The name of the file to load.</li>
 </ul>

 <p>This task supports the following attributes:</p>

 <ul>
 <li><b>prefix</b>: Optionally specify a prefix applied to
     all properties loaded.  Defaults to an empty string.</li>
 <li><b>keepRoot</b>: Indicate whether the root xml element
     is kept as part of property name.  Defaults to true.</li>
 <li><b>validate</b>: Indicate whether the xml file is validated.
     Defaults to false.</li>
 <li><b>collapseAttributes</b>: Indicate whether attributes are
     stored in property names with parens or with period
     delimiters.  Defaults to false, meaning properties
     are stored with parens (i.e., foo(attr)).</li>
 <li><b>semanticAttributes</b>: Indicate whether attributes
     named "location", "value", "refid" and "path"
     are interpreted as ant properties.  Defaults
     to false.</li>
 <li><b>rootDirectory</b>: Indicate the directory to use
     as the root directory for resolving location
     properties.  Defaults to the directory
     of the project using the task.</li>
 <li><b>includeSemanticAttribute</b>: Indicate whether to include
     the semantic attribute ("location" or "value") as
     part of the property name.  Defaults to false.</li>
 </ul>
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#XmlProperty()">XmlProperty</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#addConfigured(org.apache.tools.ant.types.ResourceCollection)">addConfigured</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;a)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the source resource.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#addConfiguredXMLCatalog(org.apache.tools.ant.types.XMLCatalog)">addConfiguredXMLCatalog</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</A>&nbsp;catalog)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add an XMLCatalog as a nested element; optional.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Run the task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#getCollapseAttributes()">getCollapseAttributes</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;org.xml.sax.EntityResolver</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#getEntityResolver()">getEntityResolver</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#getFile()">getFile</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#getIncludeSementicAttribute()">getIncludeSementicAttribute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#getKeeproot()">getKeeproot</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#getPrefix()">getPrefix</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#getResource()">getResource</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#getRootDirectory()">getRootDirectory</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#getSemanticAttributes()">getSemanticAttributes</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#getValidate()">getValidate</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#init()">init</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Initializes the task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#processNode(org.w3c.dom.Node, java.lang.String, java.lang.Object)">processNode</A></B>(org.w3c.dom.Node&nbsp;node,
            java.lang.String&nbsp;prefix,
            java.lang.Object&nbsp;container)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Process the given node, adding any required attributes from
 this child node alone -- but <em>not</em> processing any
 children.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#setCollapseAttributes(boolean)">setCollapseAttributes</A></B>(boolean&nbsp;collapseAttributes)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;flag to treat attributes as nested elements;
 optional, default false</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#setFile(java.io.File)">setFile</A></B>(java.io.File&nbsp;src)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The XML file to parse; required.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#setIncludeSemanticAttribute(boolean)">setIncludeSemanticAttribute</A></B>(boolean&nbsp;includeSemanticAttribute)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Include the semantic attribute name as part of the property name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#setKeeproot(boolean)">setKeeproot</A></B>(boolean&nbsp;keepRoot)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;flag to include the xml root tag as a
 first value in the property name; optional,
 default is true</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#setPrefix(java.lang.String)">setPrefix</A></B>(java.lang.String&nbsp;prefix)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;the prefix to prepend to each property</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#setRootDirectory(java.io.File)">setRootDirectory</A></B>(java.io.File&nbsp;rootDirectory)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The directory to use for resolving file references.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#setSemanticAttributes(boolean)">setSemanticAttributes</A></B>(boolean&nbsp;semanticAttributes)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Attribute to enable special handling of attributes - see ant manual.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#setSrcResource(org.apache.tools.ant.types.Resource)">setSrcResource</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>&nbsp;src)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The resource to pack; required.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#setValidate(boolean)">setValidate</A></B>(boolean&nbsp;validate)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;flag to validate the XML file; optional, default false</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html#supportsNonFileResources()">supportsNonFileResources</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether this task can deal with non-file resources.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="XmlProperty()"><!-- --></A><H3>
XmlProperty</H3>
<PRE>
public <B>XmlProperty</B>()</PRE>
<DL>
<DD>Constructor.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="init()"><!-- --></A><H3>
init</H3>
<PRE>
public void <B>init</B>()</PRE>
<DL>
<DD>Initializes the task.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="getEntityResolver()"><!-- --></A><H3>
getEntityResolver</H3>
<PRE>
protected org.xml.sax.EntityResolver <B>getEntityResolver</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the xmlCatalog as the entityresolver.</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Run the task.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - The exception raised during task execution.<DT><B>To do:</B></DT>
  <DD>validate the source file is valid before opening, print a better error message, add a verbose level log message listing the name of the file being loaded</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="processNode(org.w3c.dom.Node, java.lang.String, java.lang.Object)"><!-- --></A><H3>
processNode</H3>
<PRE>
public java.lang.Object <B>processNode</B>(org.w3c.dom.Node&nbsp;node,
                                    java.lang.String&nbsp;prefix,
                                    java.lang.Object&nbsp;container)</PRE>
<DL>
<DD>Process the given node, adding any required attributes from
 this child node alone -- but <em>not</em> processing any
 children.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>node</CODE> - the XML Node to parse<DD><CODE>prefix</CODE> - A string to prepend to any properties that get
 added by this node.<DD><CODE>container</CODE> - Optionally, an object that a parent node
 generated that this node might belong to.  For example, this
 node could be within a node that generated a Path.
<DT><B>Returns:</B><DD>the Object created by this node.  Generally, this is
 either a String if this node resulted in setting an attribute,
 or a Path.</DL>
</DD>
</DL>
<HR>

<A NAME="setFile(java.io.File)"><!-- --></A><H3>
setFile</H3>
<PRE>
public void <B>setFile</B>(java.io.File&nbsp;src)</PRE>
<DL>
<DD>The XML file to parse; required.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>src</CODE> - the file to parse</DL>
</DD>
</DL>
<HR>

<A NAME="setSrcResource(org.apache.tools.ant.types.Resource)"><!-- --></A><H3>
setSrcResource</H3>
<PRE>
public void <B>setSrcResource</B>(<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>&nbsp;src)</PRE>
<DL>
<DD>The resource to pack; required.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>src</CODE> - resource to expand</DL>
</DD>
</DL>
<HR>

<A NAME="addConfigured(org.apache.tools.ant.types.ResourceCollection)"><!-- --></A><H3>
addConfigured</H3>
<PRE>
public void <B>addConfigured</B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;a)</PRE>
<DL>
<DD>Set the source resource.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>a</CODE> - the resource to pack as a single element Resource collection.</DL>
</DD>
</DL>
<HR>

<A NAME="setPrefix(java.lang.String)"><!-- --></A><H3>
setPrefix</H3>
<PRE>
public void <B>setPrefix</B>(java.lang.String&nbsp;prefix)</PRE>
<DL>
<DD>the prefix to prepend to each property
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>prefix</CODE> - the prefix to prepend to each property</DL>
</DD>
</DL>
<HR>

<A NAME="setKeeproot(boolean)"><!-- --></A><H3>
setKeeproot</H3>
<PRE>
public void <B>setKeeproot</B>(boolean&nbsp;keepRoot)</PRE>
<DL>
<DD>flag to include the xml root tag as a
 first value in the property name; optional,
 default is true
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>keepRoot</CODE> - if true (default), include the xml root tag</DL>
</DD>
</DL>
<HR>

<A NAME="setValidate(boolean)"><!-- --></A><H3>
setValidate</H3>
<PRE>
public void <B>setValidate</B>(boolean&nbsp;validate)</PRE>
<DL>
<DD>flag to validate the XML file; optional, default false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>validate</CODE> - if true validate the XML file, default false</DL>
</DD>
</DL>
<HR>

<A NAME="setCollapseAttributes(boolean)"><!-- --></A><H3>
setCollapseAttributes</H3>
<PRE>
public void <B>setCollapseAttributes</B>(boolean&nbsp;collapseAttributes)</PRE>
<DL>
<DD>flag to treat attributes as nested elements;
 optional, default false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>collapseAttributes</CODE> - if true treat attributes as nested elements</DL>
</DD>
</DL>
<HR>

<A NAME="setSemanticAttributes(boolean)"><!-- --></A><H3>
setSemanticAttributes</H3>
<PRE>
public void <B>setSemanticAttributes</B>(boolean&nbsp;semanticAttributes)</PRE>
<DL>
<DD>Attribute to enable special handling of attributes - see ant manual.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>semanticAttributes</CODE> - if true enable the special handling.</DL>
</DD>
</DL>
<HR>

<A NAME="setRootDirectory(java.io.File)"><!-- --></A><H3>
setRootDirectory</H3>
<PRE>
public void <B>setRootDirectory</B>(java.io.File&nbsp;rootDirectory)</PRE>
<DL>
<DD>The directory to use for resolving file references.
 Ignored if semanticAttributes is not set to true.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rootDirectory</CODE> - the directory.</DL>
</DD>
</DL>
<HR>

<A NAME="setIncludeSemanticAttribute(boolean)"><!-- --></A><H3>
setIncludeSemanticAttribute</H3>
<PRE>
public void <B>setIncludeSemanticAttribute</B>(boolean&nbsp;includeSemanticAttribute)</PRE>
<DL>
<DD>Include the semantic attribute name as part of the property name.
 Ignored if semanticAttributes is not set to true.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>includeSemanticAttribute</CODE> - if true include the sematic attribute
                                 name.</DL>
</DD>
</DL>
<HR>

<A NAME="addConfiguredXMLCatalog(org.apache.tools.ant.types.XMLCatalog)"><!-- --></A><H3>
addConfiguredXMLCatalog</H3>
<PRE>
public void <B>addConfiguredXMLCatalog</B>(<A HREF="../../../../../org/apache/tools/ant/types/XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</A>&nbsp;catalog)</PRE>
<DL>
<DD>add an XMLCatalog as a nested element; optional.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>catalog</CODE> - the XMLCatalog to use</DL>
</DD>
</DL>
<HR>

<A NAME="getFile()"><!-- --></A><H3>
getFile</H3>
<PRE>
protected java.io.File <B>getFile</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the file attribute.</DL>
</DD>
</DL>
<HR>

<A NAME="getResource()"><!-- --></A><H3>
getResource</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A> <B>getResource</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the resource.</DL>
</DD>
</DL>
<HR>

<A NAME="getPrefix()"><!-- --></A><H3>
getPrefix</H3>
<PRE>
protected java.lang.String <B>getPrefix</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the prefix attribute.</DL>
</DD>
</DL>
<HR>

<A NAME="getKeeproot()"><!-- --></A><H3>
getKeeproot</H3>
<PRE>
protected boolean <B>getKeeproot</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the keeproot attribute.</DL>
</DD>
</DL>
<HR>

<A NAME="getValidate()"><!-- --></A><H3>
getValidate</H3>
<PRE>
protected boolean <B>getValidate</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the validate attribute.</DL>
</DD>
</DL>
<HR>

<A NAME="getCollapseAttributes()"><!-- --></A><H3>
getCollapseAttributes</H3>
<PRE>
protected boolean <B>getCollapseAttributes</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the collapse attributes attribute.</DL>
</DD>
</DL>
<HR>

<A NAME="getSemanticAttributes()"><!-- --></A><H3>
getSemanticAttributes</H3>
<PRE>
protected boolean <B>getSemanticAttributes</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the semantic attributes attribute.</DL>
</DD>
</DL>
<HR>

<A NAME="getRootDirectory()"><!-- --></A><H3>
getRootDirectory</H3>
<PRE>
protected java.io.File <B>getRootDirectory</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the root directory attribute.</DL>
</DD>
</DL>
<HR>

<A NAME="getIncludeSementicAttribute()"><!-- --></A><H3>
getIncludeSementicAttribute</H3>
<PRE>
protected boolean <B>getIncludeSementicAttribute</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the include semantic attribute.</DL>
</DD>
</DL>
<HR>

<A NAME="supportsNonFileResources()"><!-- --></A><H3>
supportsNonFileResources</H3>
<PRE>
protected boolean <B>supportsNonFileResources</B>()</PRE>
<DL>
<DD>Whether this task can deal with non-file resources.

 <p>This implementation returns true only if this task is
 &lt;xmlproperty&gt;.  Any subclass of this class that also wants to
 support non-file resources needs to override this method.  We
 need to do so for backwards compatibility reasons since we
 can't expect subclasses to support resources.</p>
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true for this task.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/WhichResource.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/XmlProperty.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="XmlProperty.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.Task">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
