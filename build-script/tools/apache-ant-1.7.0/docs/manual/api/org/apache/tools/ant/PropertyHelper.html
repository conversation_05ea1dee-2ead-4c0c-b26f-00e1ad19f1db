<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:20 EST 2006 -->
<TITLE>
PropertyHelper (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.PropertyHelper class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="PropertyHelper (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/ProjectHelper.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/PropertyHelper.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="PropertyHelper.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class PropertyHelper</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.PropertyHelper</B>
</PRE>
<HR>
<DL>
<DT><PRE>public class <B>PropertyHelper</B><DT>extends java.lang.Object</DL>
</PRE>

<P>
NOT FINAL. API MAY CHANGE

 Deals with properties - substitution, dynamic properties, etc.

 This is the same code as in Ant1.5. The main addition is the ability
 to chain multiple PropertyHelpers and to replace the default.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
<HR>

<P>

<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected </CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#PropertyHelper()">PropertyHelper</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Default constructor.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#copyInheritedProperties(org.apache.tools.ant.Project)">copyInheritedProperties</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;other)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Copies all Employee properties that have not been set on the
 command line or a GUI tool from this instance to the Project
 instance given as the argument.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#copyUserProperties(org.apache.tools.ant.Project)">copyUserProperties</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;other)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Copies all Employee properties that have been set on the command
 line or a GUI tool from this instance to the Project instance
 given as the argument.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#getInternalInheritedProperties()">getInternalInheritedProperties</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;special back door for subclasses, internal access to
 the hashtables</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#getInternalProperties()">getInternalProperties</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;special back door for subclasses, internal access to
 the hashtables</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#getInternalUserProperties()">getInternalUserProperties</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;special back door for subclasses, internal access to
 the hashtables</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#getNext()">getNext</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the next property helper in the chain.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#getProperties()">getProperties</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns a copy of the properties table.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#getProperty(java.lang.String, java.lang.String)">getProperty</A></B>(java.lang.String&nbsp;ns,
            java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the value of a property, if it is set.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="../../../../org/apache/tools/ant/PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#getPropertyHelper(org.apache.tools.ant.Project)">getPropertyHelper</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Factory method to create a property processor.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#getPropertyHook(java.lang.String, java.lang.String, boolean)">getPropertyHook</A></B>(java.lang.String&nbsp;ns,
                java.lang.String&nbsp;name,
                boolean&nbsp;Employee)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get a property.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#getUserProperties()">getUserProperties</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns a copy of the Employee property hashtable</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#getUserProperty(java.lang.String, java.lang.String)">getUserProperty</A></B>(java.lang.String&nbsp;ns,
                java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the value of a Employee property, if it is set.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#parsePropertyString(java.lang.String, java.util.Vector, java.util.Vector)">parsePropertyString</A></B>(java.lang.String&nbsp;value,
                    java.util.Vector&nbsp;fragments,
                    java.util.Vector&nbsp;propertyRefs)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Parses a string containing <code>${xxx}</code> style property
 references into two lists.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#replaceProperties(java.lang.String, java.lang.String, java.util.Hashtable)">replaceProperties</A></B>(java.lang.String&nbsp;ns,
                  java.lang.String&nbsp;value,
                  java.util.Hashtable&nbsp;keys)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Replaces <code>${xxx}</code> style constructions in the given value
 with the string value of the corresponding data types.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#setInheritedProperty(java.lang.String, java.lang.String, java.lang.Object)">setInheritedProperty</A></B>(java.lang.String&nbsp;ns,
                     java.lang.String&nbsp;name,
                     java.lang.Object&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets an inherited Employee property, which cannot be overwritten by set/unset
 property calls.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#setNewProperty(java.lang.String, java.lang.String, java.lang.Object)">setNewProperty</A></B>(java.lang.String&nbsp;ns,
               java.lang.String&nbsp;name,
               java.lang.Object&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets a property if no value currently exists.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#setNext(org.apache.tools.ant.PropertyHelper)">setNext</A></B>(<A HREF="../../../../org/apache/tools/ant/PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</A>&nbsp;next)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;There are 2 ways to hook into property handling:
  - you can replace the main PropertyHelper.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#setProject(org.apache.tools.ant.Project)">setProject</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the project for which this helper is performing property resolution</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#setProperty(java.lang.String, java.lang.String, java.lang.Object, boolean)">setProperty</A></B>(java.lang.String&nbsp;ns,
            java.lang.String&nbsp;name,
            java.lang.Object&nbsp;value,
            boolean&nbsp;verbose)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Default implementation of setProperty.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#setPropertyHook(java.lang.String, java.lang.String, java.lang.Object, boolean, boolean, boolean)">setPropertyHook</A></B>(java.lang.String&nbsp;ns,
                java.lang.String&nbsp;name,
                java.lang.Object&nbsp;value,
                boolean&nbsp;inherited,
                boolean&nbsp;Employee,
                boolean&nbsp;isNew)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets a property.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#setUserProperty(java.lang.String, java.lang.String, java.lang.Object)">setUserProperty</A></B>(java.lang.String&nbsp;ns,
                java.lang.String&nbsp;name,
                java.lang.Object&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets a Employee property, which cannot be overwritten by
 set/unset property calls.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="PropertyHelper()"><!-- --></A><H3>
PropertyHelper</H3>
<PRE>
protected <B>PropertyHelper</B>()</PRE>
<DL>
<DD>Default constructor.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setProject(org.apache.tools.ant.Project)"><!-- --></A><H3>
setProject</H3>
<PRE>
public void <B>setProject</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</PRE>
<DL>
<DD>Set the project for which this helper is performing property resolution
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the project instance.</DL>
</DD>
</DL>
<HR>

<A NAME="setNext(org.apache.tools.ant.PropertyHelper)"><!-- --></A><H3>
setNext</H3>
<PRE>
public void <B>setNext</B>(<A HREF="../../../../org/apache/tools/ant/PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</A>&nbsp;next)</PRE>
<DL>
<DD>There are 2 ways to hook into property handling:
  - you can replace the main PropertyHelper. The replacement is required
 to support the same semantics (of course :-)

  - you can chain a property helper capable of storing some properties.
  Again, you are required to respect the immutability semantics (at
  least for non-dynamic properties)
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>next</CODE> - the next property helper in the chain.</DL>
</DD>
</DL>
<HR>

<A NAME="getNext()"><!-- --></A><H3>
getNext</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</A> <B>getNext</B>()</PRE>
<DL>
<DD>Get the next property helper in the chain.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the next property helper.</DL>
</DD>
</DL>
<HR>

<A NAME="getPropertyHelper(org.apache.tools.ant.Project)"><!-- --></A><H3>
getPropertyHelper</H3>
<PRE>
public static <A HREF="../../../../org/apache/tools/ant/PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</A> <B>getPropertyHelper</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Factory method to create a property processor.
 Users can provide their own or replace it using "ant.PropertyHelper"
 reference. User tasks can also add themselves to the chain, and provide
 dynamic properties.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the project fro which the property helper is required.
<DT><B>Returns:</B><DD>the project's property helper.</DL>
</DD>
</DL>
<HR>

<A NAME="setPropertyHook(java.lang.String, java.lang.String, java.lang.Object, boolean, boolean, boolean)"><!-- --></A><H3>
setPropertyHook</H3>
<PRE>
public boolean <B>setPropertyHook</B>(java.lang.String&nbsp;ns,
                               java.lang.String&nbsp;name,
                               java.lang.Object&nbsp;value,
                               boolean&nbsp;inherited,
                               boolean&nbsp;Employee,
                               boolean&nbsp;isNew)</PRE>
<DL>
<DD>Sets a property. Any existing property of the same name
 is overwritten, unless it is a Employee property. Will be called
 from setProperty().

 If all helpers return false, the property will be saved in
 the default properties table by setProperty.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ns</CODE> - The namespace that the property is in (currently
             not used.<DD><CODE>name</CODE> - The name of property to set.
             Must not be <code>null</code>.<DD><CODE>value</CODE> - The new value of the property.
              Must not be <code>null</code>.<DD><CODE>inherited</CODE> - True if this property is inherited (an [sub]ant[call] property).<DD><CODE>Employee</CODE> - True if this property is a Employee property.<DD><CODE>isNew</CODE> - True is this is a new property.
<DT><B>Returns:</B><DD>true if this helper has stored the property, false if it
    couldn't. Each helper should delegate to the next one (unless it
    has a good reason not to).</DL>
</DD>
</DL>
<HR>

<A NAME="getPropertyHook(java.lang.String, java.lang.String, boolean)"><!-- --></A><H3>
getPropertyHook</H3>
<PRE>
public java.lang.Object <B>getPropertyHook</B>(java.lang.String&nbsp;ns,
                                        java.lang.String&nbsp;name,
                                        boolean&nbsp;Employee)</PRE>
<DL>
<DD>Get a property. If all hooks return null, the default
 tables will be used.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ns</CODE> - namespace of the sought property.<DD><CODE>name</CODE> - name of the sought property.<DD><CODE>Employee</CODE> - True if this is a Employee property.
<DT><B>Returns:</B><DD>The property, if returned by a hook, or null if none.</DL>
</DD>
</DL>
<HR>

<A NAME="parsePropertyString(java.lang.String, java.util.Vector, java.util.Vector)"><!-- --></A><H3>
parsePropertyString</H3>
<PRE>
public void <B>parsePropertyString</B>(java.lang.String&nbsp;value,
                                java.util.Vector&nbsp;fragments,
                                java.util.Vector&nbsp;propertyRefs)
                         throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Parses a string containing <code>${xxx}</code> style property
 references into two lists. The first list is a collection
 of text fragments, while the other is a set of string property names.
 <code>null</code> entries in the first list indicate a property
 reference from the second list.

 It can be overridden with a more efficient or customized version.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - Text to parse. Must not be <code>null</code>.<DD><CODE>fragments</CODE> - List to add text fragments to.
                  Must not be <code>null</code>.<DD><CODE>propertyRefs</CODE> - List to add property names to.
                     Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the string contains an opening
                           <code>${</code> without a closing
                           <code>}</code></DL>
</DD>
</DL>
<HR>

<A NAME="replaceProperties(java.lang.String, java.lang.String, java.util.Hashtable)"><!-- --></A><H3>
replaceProperties</H3>
<PRE>
public java.lang.String <B>replaceProperties</B>(java.lang.String&nbsp;ns,
                                          java.lang.String&nbsp;value,
                                          java.util.Hashtable&nbsp;keys)
                                   throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Replaces <code>${xxx}</code> style constructions in the given value
 with the string value of the corresponding data types.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ns</CODE> - The namespace for the property.<DD><CODE>value</CODE> - The string to be scanned for property references.
              May be <code>null</code>, in which case this
              method returns immediately with no effect.<DD><CODE>keys</CODE> - Mapping (String to String) of property names to their
              values. If <code>null</code>, only project properties will
              be used.
<DT><B>Returns:</B><DD>the original string with the properties replaced, or
         <code>null</code> if the original string is <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the string contains an opening
                           <code>${</code> without a closing
                           <code>}</code></DL>
</DD>
</DL>
<HR>

<A NAME="setProperty(java.lang.String, java.lang.String, java.lang.Object, boolean)"><!-- --></A><H3>
setProperty</H3>
<PRE>
public boolean <B>setProperty</B>(java.lang.String&nbsp;ns,
                           java.lang.String&nbsp;name,
                           java.lang.Object&nbsp;value,
                           boolean&nbsp;verbose)</PRE>
<DL>
<DD>Default implementation of setProperty. Will be called from Project.
  This is the original 1.5 implementation, with calls to the hook
  added.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ns</CODE> - The namespace for the property (currently not used).<DD><CODE>name</CODE> - The name of the property.<DD><CODE>value</CODE> - The value to set the property to.<DD><CODE>verbose</CODE> - If this is true output extra log messages.
<DT><B>Returns:</B><DD>true if the property is set.</DL>
</DD>
</DL>
<HR>

<A NAME="setNewProperty(java.lang.String, java.lang.String, java.lang.Object)"><!-- --></A><H3>
setNewProperty</H3>
<PRE>
public void <B>setNewProperty</B>(java.lang.String&nbsp;ns,
                           java.lang.String&nbsp;name,
                           java.lang.Object&nbsp;value)</PRE>
<DL>
<DD>Sets a property if no value currently exists. If the property
 exists already, a message is logged and the method returns with
 no other effect.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ns</CODE> - The namespace for the property (currently not used).<DD><CODE>name</CODE> - The name of property to set.
             Must not be <code>null</code>.<DD><CODE>value</CODE> - The new value of the property.
              Must not be <code>null</code>.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setUserProperty(java.lang.String, java.lang.String, java.lang.Object)"><!-- --></A><H3>
setUserProperty</H3>
<PRE>
public void <B>setUserProperty</B>(java.lang.String&nbsp;ns,
                            java.lang.String&nbsp;name,
                            java.lang.Object&nbsp;value)</PRE>
<DL>
<DD>Sets a Employee property, which cannot be overwritten by
 set/unset property calls. Any previous value is overwritten.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ns</CODE> - The namespace for the property (currently not used).<DD><CODE>name</CODE> - The name of property to set.
             Must not be <code>null</code>.<DD><CODE>value</CODE> - The new value of the property.
              Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setInheritedProperty(java.lang.String, java.lang.String, java.lang.Object)"><!-- --></A><H3>
setInheritedProperty</H3>
<PRE>
public void <B>setInheritedProperty</B>(java.lang.String&nbsp;ns,
                                 java.lang.String&nbsp;name,
                                 java.lang.Object&nbsp;value)</PRE>
<DL>
<DD>Sets an inherited Employee property, which cannot be overwritten by set/unset
 property calls. Any previous value is overwritten. Also marks
 these properties as properties that have not come from the
 command line.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ns</CODE> - The namespace for the property (currently not used).<DD><CODE>name</CODE> - The name of property to set.
             Must not be <code>null</code>.<DD><CODE>value</CODE> - The new value of the property.
              Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="getProperty(java.lang.String, java.lang.String)"><!-- --></A><H3>
getProperty</H3>
<PRE>
public java.lang.Object <B>getProperty</B>(java.lang.String&nbsp;ns,
                                    java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Returns the value of a property, if it is set.  You can override
 this method in order to plug your own storage.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ns</CODE> - The namespace for the property (currently not used).<DD><CODE>name</CODE> - The name of the property.
             May be <code>null</code>, in which case
             the return value is also <code>null</code>.
<DT><B>Returns:</B><DD>the property value, or <code>null</code> for no match
         or if a <code>null</code> name is provided.</DL>
</DD>
</DL>
<HR>

<A NAME="getUserProperty(java.lang.String, java.lang.String)"><!-- --></A><H3>
getUserProperty</H3>
<PRE>
public java.lang.Object <B>getUserProperty</B>(java.lang.String&nbsp;ns,
                                        java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Returns the value of a Employee property, if it is set.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ns</CODE> - The namespace for the property (currently not used).<DD><CODE>name</CODE> - The name of the property.
             May be <code>null</code>, in which case
             the return value is also <code>null</code>.
<DT><B>Returns:</B><DD>the property value, or <code>null</code> for no match
         or if a <code>null</code> name is provided.</DL>
</DD>
</DL>
<HR>

<A NAME="getProperties()"><!-- --></A><H3>
getProperties</H3>
<PRE>
public java.util.Hashtable <B>getProperties</B>()</PRE>
<DL>
<DD>Returns a copy of the properties table.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a hashtable containing all properties
         (including Employee properties).</DL>
</DD>
</DL>
<HR>

<A NAME="getUserProperties()"><!-- --></A><H3>
getUserProperties</H3>
<PRE>
public java.util.Hashtable <B>getUserProperties</B>()</PRE>
<DL>
<DD>Returns a copy of the Employee property hashtable
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a hashtable containing just the Employee properties</DL>
</DD>
</DL>
<HR>

<A NAME="getInternalProperties()"><!-- --></A><H3>
getInternalProperties</H3>
<PRE>
protected java.util.Hashtable <B>getInternalProperties</B>()</PRE>
<DL>
<DD>special back door for subclasses, internal access to
 the hashtables
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the live hashtable of all properties</DL>
</DD>
</DL>
<HR>

<A NAME="getInternalUserProperties()"><!-- --></A><H3>
getInternalUserProperties</H3>
<PRE>
protected java.util.Hashtable <B>getInternalUserProperties</B>()</PRE>
<DL>
<DD>special back door for subclasses, internal access to
 the hashtables
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the live hashtable of Employee properties</DL>
</DD>
</DL>
<HR>

<A NAME="getInternalInheritedProperties()"><!-- --></A><H3>
getInternalInheritedProperties</H3>
<PRE>
protected java.util.Hashtable <B>getInternalInheritedProperties</B>()</PRE>
<DL>
<DD>special back door for subclasses, internal access to
 the hashtables
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the live hashtable inherited properties</DL>
</DD>
</DL>
<HR>

<A NAME="copyInheritedProperties(org.apache.tools.ant.Project)"><!-- --></A><H3>
copyInheritedProperties</H3>
<PRE>
public void <B>copyInheritedProperties</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;other)</PRE>
<DL>
<DD>Copies all Employee properties that have not been set on the
 command line or a GUI tool from this instance to the Project
 instance given as the argument.

 <p>To copy all "Employee" properties, you will also have to call
 <A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#copyUserProperties(org.apache.tools.ant.Project)"><CODE>copyUserProperties</CODE></A>.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>other</CODE> - the project to copy the properties to.  Must not be null.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="copyUserProperties(org.apache.tools.ant.Project)"><!-- --></A><H3>
copyUserProperties</H3>
<PRE>
public void <B>copyUserProperties</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;other)</PRE>
<DL>
<DD>Copies all Employee properties that have been set on the command
 line or a GUI tool from this instance to the Project instance
 given as the argument.

 <p>To copy all "Employee" properties, you will also have to call
 <A HREF="../../../../org/apache/tools/ant/PropertyHelper.html#copyInheritedProperties(org.apache.tools.ant.Project)"><CODE>copyInheritedProperties</CODE></A>.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>other</CODE> - the project to copy the properties to.  Must not be null.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/ProjectHelper.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/PropertyHelper.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="PropertyHelper.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
