<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:35 EST 2006 -->
<TITLE>
org.apache.tools.ant.taskdefs (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs package">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">


</HEAD>

<BODY BGCOLOR="white">
<FONT size="+1" CLASS="FrameTitleFont">
<A HREF="../../../../../org/apache/tools/ant/taskdefs/package-summary.html" target="classFrame">org.apache.tools.ant.taskdefs</A></FONT>
<TABLE BORDER="0" WIDTH="100%" SUMMARY="">
<TR>
<TD NOWRAP><FONT size="+1" CLASS="FrameHeadingFont">
Interfaces</FONT>&nbsp;
<FONT CLASS="FrameItemFont">
<BR>
<A HREF="AntStructure.StructurePrinter.html" title="interface in org.apache.tools.ant.taskdefs" target="classFrame"><I>AntStructure.StructurePrinter</I></A>
<BR>
<A HREF="ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs" target="classFrame"><I>ExecuteStreamHandler</I></A>
<BR>
<A HREF="Get.DownloadProgress.html" title="interface in org.apache.tools.ant.taskdefs" target="classFrame"><I>Get.DownloadProgress</I></A>
<BR>
<A HREF="XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs" target="classFrame"><I>XSLTLiaison</I></A>
<BR>
<A HREF="XSLTLiaison2.html" title="interface in org.apache.tools.ant.taskdefs" target="classFrame"><I>XSLTLiaison2</I></A>
<BR>
<A HREF="XSLTLiaison3.html" title="interface in org.apache.tools.ant.taskdefs" target="classFrame"><I>XSLTLiaison3</I></A>
<BR>
<A HREF="XSLTLogger.html" title="interface in org.apache.tools.ant.taskdefs" target="classFrame"><I>XSLTLogger</I></A>
<BR>
<A HREF="XSLTLoggerAware.html" title="interface in org.apache.tools.ant.taskdefs" target="classFrame"><I>XSLTLoggerAware</I></A></FONT></TD>
</TR>
</TABLE>


<TABLE BORDER="0" WIDTH="100%" SUMMARY="">
<TR>
<TD NOWRAP><FONT size="+1" CLASS="FrameHeadingFont">
Classes</FONT>&nbsp;
<FONT CLASS="FrameItemFont">
<BR>
<A HREF="AbstractCvsTask.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">AbstractCvsTask</A>
<BR>
<A HREF="AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">AbstractJarSignerTask</A>
<BR>
<A HREF="Ant.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Ant</A>
<BR>
<A HREF="Ant.Reference.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Ant.Reference</A>
<BR>
<A HREF="Ant.TargetElement.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Ant.TargetElement</A>
<BR>
<A HREF="Antlib.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Antlib</A>
<BR>
<A HREF="AntlibDefinition.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">AntlibDefinition</A>
<BR>
<A HREF="AntStructure.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">AntStructure</A>
<BR>
<A HREF="Apt.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Apt</A>
<BR>
<A HREF="Apt.Option.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Apt.Option</A>
<BR>
<A HREF="Available.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Available</A>
<BR>
<A HREF="Available.FileDir.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Available.FileDir</A>
<BR>
<A HREF="Basename.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Basename</A>
<BR>
<A HREF="BuildNumber.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">BuildNumber</A>
<BR>
<A HREF="BUnzip2.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">BUnzip2</A>
<BR>
<A HREF="BZip2.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">BZip2</A>
<BR>
<A HREF="CallTarget.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">CallTarget</A>
<BR>
<A HREF="Checksum.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Checksum</A>
<BR>
<A HREF="Checksum.FormatElement.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Checksum.FormatElement</A>
<BR>
<A HREF="Chmod.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Chmod</A>
<BR>
<A HREF="Classloader.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Classloader</A>
<BR>
<A HREF="Concat.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Concat</A>
<BR>
<A HREF="Concat.TextElement.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Concat.TextElement</A>
<BR>
<A HREF="ConditionTask.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">ConditionTask</A>
<BR>
<A HREF="Copy.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Copy</A>
<BR>
<A HREF="Copydir.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Copydir</A>
<BR>
<A HREF="Copyfile.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Copyfile</A>
<BR>
<A HREF="CopyPath.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">CopyPath</A>
<BR>
<A HREF="Cvs.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Cvs</A>
<BR>
<A HREF="CVSPass.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">CVSPass</A>
<BR>
<A HREF="DefaultExcludes.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">DefaultExcludes</A>
<BR>
<A HREF="DefBase.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">DefBase</A>
<BR>
<A HREF="Definer.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Definer</A>
<BR>
<A HREF="Definer.Format.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Definer.Format</A>
<BR>
<A HREF="Definer.OnError.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Definer.OnError</A>
<BR>
<A HREF="Delete.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Delete</A>
<BR>
<A HREF="Deltree.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Deltree</A>
<BR>
<A HREF="DependSet.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">DependSet</A>
<BR>
<A HREF="DiagnosticsTask.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">DiagnosticsTask</A>
<BR>
<A HREF="Dirname.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Dirname</A>
<BR>
<A HREF="Ear.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Ear</A>
<BR>
<A HREF="Echo.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Echo</A>
<BR>
<A HREF="Echo.EchoLevel.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Echo.EchoLevel</A>
<BR>
<A HREF="EchoXML.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">EchoXML</A>
<BR>
<A HREF="Exec.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Exec</A>
<BR>
<A HREF="ExecTask.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">ExecTask</A>
<BR>
<A HREF="Execute.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Execute</A>
<BR>
<A HREF="ExecuteJava.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">ExecuteJava</A>
<BR>
<A HREF="ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">ExecuteOn</A>
<BR>
<A HREF="ExecuteOn.FileDirBoth.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">ExecuteOn.FileDirBoth</A>
<BR>
<A HREF="ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">ExecuteWatchdog</A>
<BR>
<A HREF="Exit.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Exit</A>
<BR>
<A HREF="Expand.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Expand</A>
<BR>
<A HREF="Filter.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Filter</A>
<BR>
<A HREF="FixCRLF.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">FixCRLF</A>
<BR>
<A HREF="FixCRLF.AddAsisRemove.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">FixCRLF.AddAsisRemove</A>
<BR>
<A HREF="FixCRLF.CrLf.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">FixCRLF.CrLf</A>
<BR>
<A HREF="GenerateKey.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">GenerateKey</A>
<BR>
<A HREF="GenerateKey.DistinguishedName.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">GenerateKey.DistinguishedName</A>
<BR>
<A HREF="GenerateKey.DnameParam.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">GenerateKey.DnameParam</A>
<BR>
<A HREF="Get.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Get</A>
<BR>
<A HREF="Get.Base64Converter.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Get.Base64Converter</A>
<BR>
<A HREF="Get.NullProgress.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Get.NullProgress</A>
<BR>
<A HREF="Get.VerboseProgress.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Get.VerboseProgress</A>
<BR>
<A HREF="GUnzip.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">GUnzip</A>
<BR>
<A HREF="GZip.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">GZip</A>
<BR>
<A HREF="ImportTask.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">ImportTask</A>
<BR>
<A HREF="Input.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Input</A>
<BR>
<A HREF="Input.HandlerType.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Input.HandlerType</A>
<BR>
<A HREF="Jar.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Jar</A>
<BR>
<A HREF="Jar.FilesetManifestConfig.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Jar.FilesetManifestConfig</A>
<BR>
<A HREF="Java.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Java</A>
<BR>
<A HREF="Javac.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Javac</A>
<BR>
<A HREF="Javadoc.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Javadoc</A>
<BR>
<A HREF="Javadoc.AccessType.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Javadoc.AccessType</A>
<BR>
<A HREF="Javadoc.ExtensionInfo.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Javadoc.ExtensionInfo</A>
<BR>
<A HREF="Javadoc.Html.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Javadoc.Html</A>
<BR>
<A HREF="Javadoc.PackageName.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Javadoc.PackageName</A>
<BR>
<A HREF="Javadoc.SourceFile.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Javadoc.SourceFile</A>
<BR>
<A HREF="JDBCTask.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">JDBCTask</A>
<BR>
<A HREF="Jikes.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Jikes</A>
<BR>
<A HREF="JikesOutputParser.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">JikesOutputParser</A>
<BR>
<A HREF="KeySubst.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">KeySubst</A>
<BR>
<A HREF="Length.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Length</A>
<BR>
<A HREF="Length.FileMode.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Length.FileMode</A>
<BR>
<A HREF="Length.When.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Length.When</A>
<BR>
<A HREF="LoadFile.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">LoadFile</A>
<BR>
<A HREF="LoadProperties.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">LoadProperties</A>
<BR>
<A HREF="LoadResource.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">LoadResource</A>
<BR>
<A HREF="LogOutputStream.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">LogOutputStream</A>
<BR>
<A HREF="LogStreamHandler.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">LogStreamHandler</A>
<BR>
<A HREF="MacroDef.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">MacroDef</A>
<BR>
<A HREF="MacroDef.Attribute.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">MacroDef.Attribute</A>
<BR>
<A HREF="MacroDef.NestedSequential.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">MacroDef.NestedSequential</A>
<BR>
<A HREF="MacroDef.TemplateElement.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">MacroDef.TemplateElement</A>
<BR>
<A HREF="MacroDef.Text.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">MacroDef.Text</A>
<BR>
<A HREF="MacroInstance.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">MacroInstance</A>
<BR>
<A HREF="MacroInstance.Element.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">MacroInstance.Element</A>
<BR>
<A HREF="MakeUrl.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">MakeUrl</A>
<BR>
<A HREF="Manifest.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Manifest</A>
<BR>
<A HREF="Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Manifest.Attribute</A>
<BR>
<A HREF="Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Manifest.Section</A>
<BR>
<A HREF="ManifestClassPath.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">ManifestClassPath</A>
<BR>
<A HREF="ManifestTask.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">ManifestTask</A>
<BR>
<A HREF="ManifestTask.Mode.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">ManifestTask.Mode</A>
<BR>
<A HREF="MatchingTask.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">MatchingTask</A>
<BR>
<A HREF="Mkdir.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Mkdir</A>
<BR>
<A HREF="Move.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Move</A>
<BR>
<A HREF="Nice.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Nice</A>
<BR>
<A HREF="Pack.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Pack</A>
<BR>
<A HREF="Parallel.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Parallel</A>
<BR>
<A HREF="Parallel.TaskList.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Parallel.TaskList</A>
<BR>
<A HREF="Patch.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Patch</A>
<BR>
<A HREF="PathConvert.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">PathConvert</A>
<BR>
<A HREF="PathConvert.TargetOs.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">PathConvert.TargetOs</A>
<BR>
<A HREF="PreSetDef.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">PreSetDef</A>
<BR>
<A HREF="PreSetDef.PreSetDefinition.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">PreSetDef.PreSetDefinition</A>
<BR>
<A HREF="Property.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Property</A>
<BR>
<A HREF="PumpStreamHandler.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">PumpStreamHandler</A>
<BR>
<A HREF="Recorder.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Recorder</A>
<BR>
<A HREF="Recorder.ActionChoices.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Recorder.ActionChoices</A>
<BR>
<A HREF="Recorder.VerbosityLevelChoices.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Recorder.VerbosityLevelChoices</A>
<BR>
<A HREF="RecorderEntry.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">RecorderEntry</A>
<BR>
<A HREF="Redirector.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Redirector</A>
<BR>
<A HREF="Rename.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Rename</A>
<BR>
<A HREF="Replace.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Replace</A>
<BR>
<A HREF="ResourceCount.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">ResourceCount</A>
<BR>
<A HREF="Rmic.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Rmic</A>
<BR>
<A HREF="SendEmail.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">SendEmail</A>
<BR>
<A HREF="Sequential.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Sequential</A>
<BR>
<A HREF="SignJar.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">SignJar</A>
<BR>
<A HREF="Sleep.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Sleep</A>
<BR>
<A HREF="SQLExec.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">SQLExec</A>
<BR>
<A HREF="SQLExec.DelimiterType.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">SQLExec.DelimiterType</A>
<BR>
<A HREF="SQLExec.OnError.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">SQLExec.OnError</A>
<BR>
<A HREF="StreamPumper.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">StreamPumper</A>
<BR>
<A HREF="SubAnt.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">SubAnt</A>
<BR>
<A HREF="Sync.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Sync</A>
<BR>
<A HREF="Sync.MyCopy.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Sync.MyCopy</A>
<BR>
<A HREF="Sync.SyncTarget.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Sync.SyncTarget</A>
<BR>
<A HREF="Tar.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Tar</A>
<BR>
<A HREF="Tar.TarCompressionMethod.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Tar.TarCompressionMethod</A>
<BR>
<A HREF="Tar.TarFileSet.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Tar.TarFileSet</A>
<BR>
<A HREF="Tar.TarLongFileMode.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Tar.TarLongFileMode</A>
<BR>
<A HREF="Taskdef.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Taskdef</A>
<BR>
<A HREF="TaskOutputStream.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">TaskOutputStream</A>
<BR>
<A HREF="TempFile.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">TempFile</A>
<BR>
<A HREF="Touch.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Touch</A>
<BR>
<A HREF="Transform.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Transform</A>
<BR>
<A HREF="Tstamp.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Tstamp</A>
<BR>
<A HREF="Tstamp.Unit.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Tstamp.Unit</A>
<BR>
<A HREF="Typedef.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Typedef</A>
<BR>
<A HREF="Unpack.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Unpack</A>
<BR>
<A HREF="Untar.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Untar</A>
<BR>
<A HREF="Untar.UntarCompressionMethod.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Untar.UntarCompressionMethod</A>
<BR>
<A HREF="UpToDate.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">UpToDate</A>
<BR>
<A HREF="VerifyJar.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">VerifyJar</A>
<BR>
<A HREF="WaitFor.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">WaitFor</A>
<BR>
<A HREF="WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">WaitFor.Unit</A>
<BR>
<A HREF="War.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">War</A>
<BR>
<A HREF="WhichResource.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">WhichResource</A>
<BR>
<A HREF="XmlProperty.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">XmlProperty</A>
<BR>
<A HREF="XSLTProcess.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">XSLTProcess</A>
<BR>
<A HREF="XSLTProcess.Factory.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">XSLTProcess.Factory</A>
<BR>
<A HREF="XSLTProcess.Factory.Attribute.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">XSLTProcess.Factory.Attribute</A>
<BR>
<A HREF="XSLTProcess.OutputProperty.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">XSLTProcess.OutputProperty</A>
<BR>
<A HREF="XSLTProcess.Param.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">XSLTProcess.Param</A>
<BR>
<A HREF="Zip.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Zip</A>
<BR>
<A HREF="Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Zip.ArchiveState</A>
<BR>
<A HREF="Zip.Duplicate.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Zip.Duplicate</A>
<BR>
<A HREF="Zip.WhenEmpty.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">Zip.WhenEmpty</A></FONT></TD>
</TR>
</TABLE>


<TABLE BORDER="0" WIDTH="100%" SUMMARY="">
<TR>
<TD NOWRAP><FONT size="+1" CLASS="FrameHeadingFont">
Exceptions</FONT>&nbsp;
<FONT CLASS="FrameItemFont">
<BR>
<A HREF="ManifestException.html" title="class in org.apache.tools.ant.taskdefs" target="classFrame">ManifestException</A></FONT></TD>
</TR>
</TABLE>


</BODY>
</HTML>
