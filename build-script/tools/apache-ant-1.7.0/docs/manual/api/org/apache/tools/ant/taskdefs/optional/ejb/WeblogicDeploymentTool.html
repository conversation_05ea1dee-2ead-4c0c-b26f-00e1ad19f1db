<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:28 EST 2006 -->
<TITLE>
WeblogicDeploymentTool (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="WeblogicDeploymentTool (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicTOPLinkDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="WeblogicDeploymentTool.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.optional.ejb</FONT>
<BR>
Class WeblogicDeploymentTool</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool</A>
      <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</A></DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicTOPLinkDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WeblogicTOPLinkDeploymentTool</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>WeblogicDeploymentTool</B><DT>extends <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></DL>
</PRE>

<P>
The weblogic element is used to control the weblogic.ejbc compiler for
    generating weblogic EJB jars. Prior to Ant 1.3, the method of locating CMP
    descriptors was to use the ejbjar naming convention. So if your ejb-jar was
    called, Customer-ejb-jar.xml, your weblogic descriptor was called Customer-
    weblogic-ejb-jar.xml and your CMP descriptor had to be Customer-weblogic-cmp-
    rdbms-jar.xml. In addition, the &lt;type-storage&gt; element in the weblogic
    descriptor had to be set to the standard name META-INF/weblogic-cmp-rdbms-
    jar.xml, as that is where the CMP descriptor was mapped to in the generated
    jar.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#COMPILER_EJB11">COMPILER_EJB11</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#COMPILER_EJB20">COMPILER_EJB20</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#DEFAULT_COMPILER">DEFAULT_COMPILER</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#DEFAULT_WL51_DTD_LOCATION">DEFAULT_WL51_DTD_LOCATION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#DEFAULT_WL51_EJB11_DTD_LOCATION">DEFAULT_WL51_EJB11_DTD_LOCATION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Weblogic 5.1 dtd location</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#DEFAULT_WL60_51_DTD_LOCATION">DEFAULT_WL60_51_DTD_LOCATION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#DEFAULT_WL60_DTD_LOCATION">DEFAULT_WL60_DTD_LOCATION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#DEFAULT_WL60_EJB11_DTD_LOCATION">DEFAULT_WL60_EJB11_DTD_LOCATION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Weblogic 6.0 ejb 1.1 dtd location</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#DEFAULT_WL60_EJB20_DTD_LOCATION">DEFAULT_WL60_EJB20_DTD_LOCATION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Weblogic 6.0 ejb 2.0 dtd location</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#DEFAULT_WL70_DTD_LOCATION">DEFAULT_WL70_DTD_LOCATION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#PUBLICID_EJB11">PUBLICID_EJB11</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;EJB11 id</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#PUBLICID_EJB20">PUBLICID_EJB20</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;EJB20 id</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#PUBLICID_WEBLOGIC_EJB510">PUBLICID_WEBLOGIC_EJB510</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Weblogic 5.1.0 id</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#PUBLICID_WEBLOGIC_EJB600">PUBLICID_WEBLOGIC_EJB600</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Weblogic 6.0.0 id</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#PUBLICID_WEBLOGIC_EJB700">PUBLICID_WEBLOGIC_EJB700</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Weblogic 7.0.0 id</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#WL_CMP_DD">WL_CMP_DD</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#WL_DD">WL_DD</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.optional.ejb.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#ANALYZER_CLASS_FULL">ANALYZER_CLASS_FULL</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#ANALYZER_CLASS_SUPER">ANALYZER_CLASS_SUPER</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#ANALYZER_FULL">ANALYZER_FULL</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#ANALYZER_NONE">ANALYZER_NONE</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#ANALYZER_SUPER">ANALYZER_SUPER</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#DEFAULT_ANALYZER">DEFAULT_ANALYZER</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#DEFAULT_BUFFER_SIZE">DEFAULT_BUFFER_SIZE</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#EJB_DD">EJB_DD</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#JAR_COMPRESS_LEVEL">JAR_COMPRESS_LEVEL</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#MANIFEST">MANIFEST</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#META_DIR">META_DIR</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#WeblogicDeploymentTool()">WeblogicDeploymentTool</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#addSysproperty(org.apache.tools.ant.types.Environment.Variable)">addSysproperty</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</A>&nbsp;sysp)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a nested sysproperty element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#addVendorFiles(java.util.Hashtable, java.lang.String)">addVendorFiles</A></B>(java.util.Hashtable&nbsp;ejbFiles,
               java.lang.String&nbsp;ddPrefix)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add any vendor specific files which should be included in the EJB Jar.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#createWLClasspath()">createWLClasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the classpath to the weblogic classpaths.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.ClassLoader</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#getClassLoaderFromJar(java.io.File)">getClassLoaderFromJar</A></B>(java.io.File&nbsp;classjar)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Helper method invoked by isRebuildRequired to get a ClassLoader for a
 Jar File passed to it.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#getEjbcClass()">getEjbcClass</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the ejbc compiler class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Integer</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#getJvmDebugLevel()">getJvmDebugLevel</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the debug level.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#getWeblogicDescriptorHandler(java.io.File)">getWeblogicDescriptorHandler</A></B>(java.io.File&nbsp;srcDir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the weblogic descriptor handler.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#isRebuildRequired(java.io.File, java.io.File)">isRebuildRequired</A></B>(java.io.File&nbsp;genericJarFile,
                  java.io.File&nbsp;weblogicJarFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Helper method to check to see if a weblogic EBJ1.1 jar needs to be
 rebuilt using ejbc.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#registerKnownDTDs(org.apache.tools.ant.taskdefs.optional.ejb.DescriptorHandler)">registerKnownDTDs</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</A>&nbsp;handler)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Register the DTDs.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setArgs(java.lang.String)">setArgs</A></B>(java.lang.String&nbsp;args)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Any optional extra arguments pass to the weblogic.ejbc
 tool.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setCompiler(java.lang.String)">setCompiler</A></B>(java.lang.String&nbsp;compiler)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The compiler (switch <code>-compiler</code>) to use; optional.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setEjbcClass(java.lang.String)">setEjbcClass</A></B>(java.lang.String&nbsp;ejbcClass)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the classname of the ejbc compiler;  optional
 Normally ejbjar determines
 the appropriate class based on the DTD used for the EJB.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setEJBdtd(java.lang.String)">setEJBdtd</A></B>(java.lang.String&nbsp;inString)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>Deprecated</b>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setJvmargs(java.lang.String)">setJvmargs</A></B>(java.lang.String&nbsp;args)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set any additional arguments to pass to the weblogic JVM; optional.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setJvmDebugLevel(java.lang.Integer)">setJvmDebugLevel</A></B>(java.lang.Integer&nbsp;jvmDebugLevel)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the weblogic.StdoutSeverityLevel to use when running the JVM that
 executes ejbc; optional.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setKeepgenerated(java.lang.String)">setKeepgenerated</A></B>(java.lang.String&nbsp;inValue)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Controls whether weblogic will keep the generated Java
 files used to build the class files added to the
 jar.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setKeepgeneric(boolean)">setKeepgeneric</A></B>(boolean&nbsp;inValue)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;controls whether the generic file used as input to
 ejbc is retained; defaults to false</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setNewCMP(boolean)">setNewCMP</A></B>(boolean&nbsp;newCMP)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If this is set to true, the new method for locating
 CMP descriptors will be used; optional, default false.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setNoEJBC(boolean)">setNoEJBC</A></B>(boolean&nbsp;noEJBC)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Do not EJBC the jar after it has been put together;
 optional, default false</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setOldCMP(boolean)">setOldCMP</A></B>(boolean&nbsp;oldCMP)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the value of the oldCMP scheme.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setOutputDir(java.io.File)">setOutputDir</A></B>(java.io.File&nbsp;outputDir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If set ejbc will use this directory as the output
 destination rather than a jar file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setRebuild(boolean)">setRebuild</A></B>(boolean&nbsp;rebuild)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the rebuild flag to false to only update changes in the jar rather
 than rerunning ejbc; optional, default true.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setSuffix(java.lang.String)">setSuffix</A></B>(java.lang.String&nbsp;inString)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Setter used to store the suffix for the generated weblogic jar file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setWeblogicdtd(java.lang.String)">setWeblogicdtd</A></B>(java.lang.String&nbsp;inString)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>Deprecated</b>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setWLClasspath(org.apache.tools.ant.types.Path)">setWLClasspath</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;wlClasspath)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Optional classpath to WL6.0.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#setWLdtd(java.lang.String)">setWLdtd</A></B>(java.lang.String&nbsp;inString)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>Deprecated</b>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#validateConfigured()">validateConfigured</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Called to validate that the tool parameters have been configured.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#writeJar(java.lang.String, java.io.File, java.util.Hashtable, java.lang.String)">writeJar</A></B>(java.lang.String&nbsp;baseName,
         java.io.File&nbsp;jarFile,
         java.util.Hashtable&nbsp;files,
         java.lang.String&nbsp;publicId)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Method used to encapsulate the writing of the JAR file.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.optional.ejb.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#addFileToJar(java.util.jar.JarOutputStream, java.io.File, java.lang.String)">addFileToJar</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#addSupportClasses(java.util.Hashtable)">addSupportClasses</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#checkAndAddDependants(java.util.Hashtable)">checkAndAddDependants</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#checkConfiguration(java.lang.String, javax.xml.parsers.SAXParser)">checkConfiguration</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#configure(org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.Config)">configure</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#createClasspath()">createClasspath</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getClassLoaderForBuild()">getClassLoaderForBuild</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getCombinedClasspath()">getCombinedClasspath</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getConfig()">getConfig</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getDescriptorHandler(java.io.File)">getDescriptorHandler</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getDestDir()">getDestDir</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getJarBaseName(java.lang.String)">getJarBaseName</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getLocation()">getLocation</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getManifestFile(java.lang.String)">getManifestFile</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getPublicId()">getPublicId</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getTask()">getTask</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#getVendorDDPrefix(java.lang.String, java.lang.String)">getVendorDDPrefix</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#needToRebuild(java.util.Hashtable, java.io.File)">needToRebuild</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#parseEjbFiles(java.lang.String, javax.xml.parsers.SAXParser)">parseEjbFiles</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#processDescriptor(java.lang.String, javax.xml.parsers.SAXParser)">processDescriptor</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#setDestdir(java.io.File)">setDestdir</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#setGenericJarSuffix(java.lang.String)">setGenericJarSuffix</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#setTask(org.apache.tools.ant.Task)">setTask</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#usingBaseJarName()">usingBaseJarName</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="PUBLICID_EJB11"><!-- --></A><H3>
PUBLICID_EJB11</H3>
<PRE>
public static final java.lang.String <B>PUBLICID_EJB11</B></PRE>
<DL>
<DD>EJB11 id
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.PUBLICID_EJB11">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="PUBLICID_EJB20"><!-- --></A><H3>
PUBLICID_EJB20</H3>
<PRE>
public static final java.lang.String <B>PUBLICID_EJB20</B></PRE>
<DL>
<DD>EJB20 id
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.PUBLICID_EJB20">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="PUBLICID_WEBLOGIC_EJB510"><!-- --></A><H3>
PUBLICID_WEBLOGIC_EJB510</H3>
<PRE>
public static final java.lang.String <B>PUBLICID_WEBLOGIC_EJB510</B></PRE>
<DL>
<DD>Weblogic 5.1.0 id
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.PUBLICID_WEBLOGIC_EJB510">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="PUBLICID_WEBLOGIC_EJB600"><!-- --></A><H3>
PUBLICID_WEBLOGIC_EJB600</H3>
<PRE>
public static final java.lang.String <B>PUBLICID_WEBLOGIC_EJB600</B></PRE>
<DL>
<DD>Weblogic 6.0.0 id
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.PUBLICID_WEBLOGIC_EJB600">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="PUBLICID_WEBLOGIC_EJB700"><!-- --></A><H3>
PUBLICID_WEBLOGIC_EJB700</H3>
<PRE>
public static final java.lang.String <B>PUBLICID_WEBLOGIC_EJB700</B></PRE>
<DL>
<DD>Weblogic 7.0.0 id
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.PUBLICID_WEBLOGIC_EJB700">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_WL51_EJB11_DTD_LOCATION"><!-- --></A><H3>
DEFAULT_WL51_EJB11_DTD_LOCATION</H3>
<PRE>
protected static final java.lang.String <B>DEFAULT_WL51_EJB11_DTD_LOCATION</B></PRE>
<DL>
<DD>Weblogic 5.1 dtd location
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL51_EJB11_DTD_LOCATION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_WL60_EJB11_DTD_LOCATION"><!-- --></A><H3>
DEFAULT_WL60_EJB11_DTD_LOCATION</H3>
<PRE>
protected static final java.lang.String <B>DEFAULT_WL60_EJB11_DTD_LOCATION</B></PRE>
<DL>
<DD>Weblogic 6.0 ejb 1.1 dtd location
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL60_EJB11_DTD_LOCATION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_WL60_EJB20_DTD_LOCATION"><!-- --></A><H3>
DEFAULT_WL60_EJB20_DTD_LOCATION</H3>
<PRE>
protected static final java.lang.String <B>DEFAULT_WL60_EJB20_DTD_LOCATION</B></PRE>
<DL>
<DD>Weblogic 6.0 ejb 2.0 dtd location
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL60_EJB20_DTD_LOCATION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_WL51_DTD_LOCATION"><!-- --></A><H3>
DEFAULT_WL51_DTD_LOCATION</H3>
<PRE>
protected static final java.lang.String <B>DEFAULT_WL51_DTD_LOCATION</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL51_DTD_LOCATION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_WL60_51_DTD_LOCATION"><!-- --></A><H3>
DEFAULT_WL60_51_DTD_LOCATION</H3>
<PRE>
protected static final java.lang.String <B>DEFAULT_WL60_51_DTD_LOCATION</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL60_51_DTD_LOCATION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_WL60_DTD_LOCATION"><!-- --></A><H3>
DEFAULT_WL60_DTD_LOCATION</H3>
<PRE>
protected static final java.lang.String <B>DEFAULT_WL60_DTD_LOCATION</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL60_DTD_LOCATION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_WL70_DTD_LOCATION"><!-- --></A><H3>
DEFAULT_WL70_DTD_LOCATION</H3>
<PRE>
protected static final java.lang.String <B>DEFAULT_WL70_DTD_LOCATION</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL70_DTD_LOCATION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_COMPILER"><!-- --></A><H3>
DEFAULT_COMPILER</H3>
<PRE>
protected static final java.lang.String <B>DEFAULT_COMPILER</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_COMPILER">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="WL_DD"><!-- --></A><H3>
WL_DD</H3>
<PRE>
protected static final java.lang.String <B>WL_DD</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.WL_DD">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="WL_CMP_DD"><!-- --></A><H3>
WL_CMP_DD</H3>
<PRE>
protected static final java.lang.String <B>WL_CMP_DD</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.WL_CMP_DD">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="COMPILER_EJB11"><!-- --></A><H3>
COMPILER_EJB11</H3>
<PRE>
protected static final java.lang.String <B>COMPILER_EJB11</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.COMPILER_EJB11">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="COMPILER_EJB20"><!-- --></A><H3>
COMPILER_EJB20</H3>
<PRE>
protected static final java.lang.String <B>COMPILER_EJB20</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.COMPILER_EJB20">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="WeblogicDeploymentTool()"><!-- --></A><H3>
WeblogicDeploymentTool</H3>
<PRE>
public <B>WeblogicDeploymentTool</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="addSysproperty(org.apache.tools.ant.types.Environment.Variable)"><!-- --></A><H3>
addSysproperty</H3>
<PRE>
public void <B>addSysproperty</B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</A>&nbsp;sysp)</PRE>
<DL>
<DD>Add a nested sysproperty element.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sysp</CODE> - the element to add.</DL>
</DD>
</DL>
<HR>

<A NAME="createWLClasspath()"><!-- --></A><H3>
createWLClasspath</H3>
<PRE>
public <A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createWLClasspath</B>()</PRE>
<DL>
<DD>Get the classpath to the weblogic classpaths.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the classpath to configure.</DL>
</DD>
</DL>
<HR>

<A NAME="setOutputDir(java.io.File)"><!-- --></A><H3>
setOutputDir</H3>
<PRE>
public void <B>setOutputDir</B>(java.io.File&nbsp;outputDir)</PRE>
<DL>
<DD>If set ejbc will use this directory as the output
 destination rather than a jar file. This allows for the
 generation of &quot;exploded&quot; jars.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>outputDir</CODE> - the directory to be used.</DL>
</DD>
</DL>
<HR>

<A NAME="setWLClasspath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setWLClasspath</H3>
<PRE>
public void <B>setWLClasspath</B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;wlClasspath)</PRE>
<DL>
<DD>Optional classpath to WL6.0.
 Weblogic 6.0 will give a warning if the home and remote interfaces
 of a bean are on the system classpath used to run weblogic.ejbc.
 In that case, the standard weblogic classes should be set with
 this attribute (or equivalent nested element) and the
 home and remote interfaces located with the standard classpath
 attribute.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>wlClasspath</CODE> - the path to be used.</DL>
</DD>
</DL>
<HR>

<A NAME="setCompiler(java.lang.String)"><!-- --></A><H3>
setCompiler</H3>
<PRE>
public void <B>setCompiler</B>(java.lang.String&nbsp;compiler)</PRE>
<DL>
<DD>The compiler (switch <code>-compiler</code>) to use; optional.
 This allows for the selection of a different compiler
 to be used for the compilation of the generated Java
 files. This could be set, for example, to Jikes to
 compile with the Jikes compiler. If this is not set
 and the <code>build.compiler</code> property is set
 to jikes, the Jikes compiler will be used. If this
 is not desired, the value &quot;<code>default</code>&quot;
 may be given to use the default compiler.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>compiler</CODE> - the compiler to be used.</DL>
</DD>
</DL>
<HR>

<A NAME="setRebuild(boolean)"><!-- --></A><H3>
setRebuild</H3>
<PRE>
public void <B>setRebuild</B>(boolean&nbsp;rebuild)</PRE>
<DL>
<DD>Set the rebuild flag to false to only update changes in the jar rather
 than rerunning ejbc; optional, default true.
 This flag controls whether weblogic.ejbc is always
 invoked to build the jar file. In certain circumstances,
 such as when only a bean class has been changed, the jar
 can be generated by merely replacing the changed classes
 and not rerunning ejbc. Setting this to false will reduce
 the time to run ejbjar.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rebuild</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setJvmDebugLevel(java.lang.Integer)"><!-- --></A><H3>
setJvmDebugLevel</H3>
<PRE>
public void <B>setJvmDebugLevel</B>(java.lang.Integer&nbsp;jvmDebugLevel)</PRE>
<DL>
<DD>Sets the weblogic.StdoutSeverityLevel to use when running the JVM that
 executes ejbc; optional. Set to 16 to avoid the warnings about EJB Home and
 Remotes being in the classpath
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>jvmDebugLevel</CODE> - the value to use.</DL>
</DD>
</DL>
<HR>

<A NAME="getJvmDebugLevel()"><!-- --></A><H3>
getJvmDebugLevel</H3>
<PRE>
public java.lang.Integer <B>getJvmDebugLevel</B>()</PRE>
<DL>
<DD>Get the debug level.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the jvm debug level (may be null).</DL>
</DD>
</DL>
<HR>

<A NAME="setSuffix(java.lang.String)"><!-- --></A><H3>
setSuffix</H3>
<PRE>
public void <B>setSuffix</B>(java.lang.String&nbsp;inString)</PRE>
<DL>
<DD>Setter used to store the suffix for the generated weblogic jar file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>inString</CODE> - the string to use as the suffix.</DL>
</DD>
</DL>
<HR>

<A NAME="setKeepgeneric(boolean)"><!-- --></A><H3>
setKeepgeneric</H3>
<PRE>
public void <B>setKeepgeneric</B>(boolean&nbsp;inValue)</PRE>
<DL>
<DD>controls whether the generic file used as input to
 ejbc is retained; defaults to false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>inValue</CODE> - true for keep generic</DL>
</DD>
</DL>
<HR>

<A NAME="setKeepgenerated(java.lang.String)"><!-- --></A><H3>
setKeepgenerated</H3>
<PRE>
public void <B>setKeepgenerated</B>(java.lang.String&nbsp;inValue)</PRE>
<DL>
<DD>Controls whether weblogic will keep the generated Java
 files used to build the class files added to the
 jar. This can be useful when debugging; default is false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>inValue</CODE> - either 'true' or 'false'</DL>
</DD>
</DL>
<HR>

<A NAME="setArgs(java.lang.String)"><!-- --></A><H3>
setArgs</H3>
<PRE>
public void <B>setArgs</B>(java.lang.String&nbsp;args)</PRE>
<DL>
<DD>Any optional extra arguments pass to the weblogic.ejbc
 tool.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>args</CODE> - extra arguments to pass to the ejbc tool.</DL>
</DD>
</DL>
<HR>

<A NAME="setJvmargs(java.lang.String)"><!-- --></A><H3>
setJvmargs</H3>
<PRE>
public void <B>setJvmargs</B>(java.lang.String&nbsp;args)</PRE>
<DL>
<DD>Set any additional arguments to pass to the weblogic JVM; optional.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>args</CODE> - the arguments to be passed to the JVM</DL>
</DD>
</DL>
<HR>

<A NAME="setEjbcClass(java.lang.String)"><!-- --></A><H3>
setEjbcClass</H3>
<PRE>
public void <B>setEjbcClass</B>(java.lang.String&nbsp;ejbcClass)</PRE>
<DL>
<DD>Set the classname of the ejbc compiler;  optional
 Normally ejbjar determines
 the appropriate class based on the DTD used for the EJB. The EJB 2.0 compiler
 featured in weblogic 6 has, however, been deprecated in version 7. When
 using with version 7 this attribute should be set to
 &quot;weblogic.ejbc&quot; to avoid the deprecation warning.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ejbcClass</CODE> - the name of the class to use.</DL>
</DD>
</DL>
<HR>

<A NAME="getEjbcClass()"><!-- --></A><H3>
getEjbcClass</H3>
<PRE>
public java.lang.String <B>getEjbcClass</B>()</PRE>
<DL>
<DD>Get the ejbc compiler class.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the name of the ejbc compiler class.</DL>
</DD>
</DL>
<HR>

<A NAME="setWeblogicdtd(java.lang.String)"><!-- --></A><H3>
setWeblogicdtd</H3>
<PRE>
public void <B>setWeblogicdtd</B>(java.lang.String&nbsp;inString)</PRE>
<DL>
<DD><b>Deprecated</b>. Defines the location of the ejb-jar DTD in
  the weblogic class hierarchy. Should not be needed, and the
 nested &lt;dtd&gt; element is recommended when it is.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>inString</CODE> - the string to use as the DTD location.</DL>
</DD>
</DL>
<HR>

<A NAME="setWLdtd(java.lang.String)"><!-- --></A><H3>
setWLdtd</H3>
<PRE>
public void <B>setWLdtd</B>(java.lang.String&nbsp;inString)</PRE>
<DL>
<DD><b>Deprecated</b>. Defines the location of weblogic DTD in
  the weblogic class hierarchy. Should not be needed, and the
 nested &lt;dtd&gt; element is recommended when it is.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>inString</CODE> - the string to use as the DTD location.</DL>
</DD>
</DL>
<HR>

<A NAME="setEJBdtd(java.lang.String)"><!-- --></A><H3>
setEJBdtd</H3>
<PRE>
public void <B>setEJBdtd</B>(java.lang.String&nbsp;inString)</PRE>
<DL>
<DD><b>Deprecated</b>. Defines the location of Sun's EJB DTD in
  the weblogic class hierarchy. Should not be needed, and the
 nested &lt;dtd&gt; element is recommended when it is.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>inString</CODE> - the string to use as the DTD location.</DL>
</DD>
</DL>
<HR>

<A NAME="setOldCMP(boolean)"><!-- --></A><H3>
setOldCMP</H3>
<PRE>
public void <B>setOldCMP</B>(boolean&nbsp;oldCMP)</PRE>
<DL>
<DD>Set the value of the oldCMP scheme. This is an antonym for newCMP
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>oldCMP</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setNewCMP(boolean)"><!-- --></A><H3>
setNewCMP</H3>
<PRE>
public void <B>setNewCMP</B>(boolean&nbsp;newCMP)</PRE>
<DL>
<DD>If this is set to true, the new method for locating
 CMP descriptors will be used; optional, default false.
 <P>
 The old CMP scheme locates the
 weblogic CMP descriptor based on the naming convention where the
 weblogic CMP file is expected to be named with the bean name as the
 prefix. Under this scheme the name of the CMP descriptor does not match
 the name actually used in the main weblogic EJB descriptor. Also,
 descriptors which contain multiple CMP references could not be used.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>newCMP</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setNoEJBC(boolean)"><!-- --></A><H3>
setNoEJBC</H3>
<PRE>
public void <B>setNoEJBC</B>(boolean&nbsp;noEJBC)</PRE>
<DL>
<DD>Do not EJBC the jar after it has been put together;
 optional, default false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>noEJBC</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="registerKnownDTDs(org.apache.tools.ant.taskdefs.optional.ejb.DescriptorHandler)"><!-- --></A><H3>
registerKnownDTDs</H3>
<PRE>
protected void <B>registerKnownDTDs</B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</A>&nbsp;handler)</PRE>
<DL>
<DD>Register the DTDs.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#registerKnownDTDs(org.apache.tools.ant.taskdefs.optional.ejb.DescriptorHandler)">registerKnownDTDs</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>handler</CODE> - the handler to use.</DL>
</DD>
</DL>
<HR>

<A NAME="getWeblogicDescriptorHandler(java.io.File)"><!-- --></A><H3>
getWeblogicDescriptorHandler</H3>
<PRE>
protected <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/DescriptorHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DescriptorHandler</A> <B>getWeblogicDescriptorHandler</B>(java.io.File&nbsp;srcDir)</PRE>
<DL>
<DD>Get the weblogic descriptor handler.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>srcDir</CODE> - the source directory.
<DT><B>Returns:</B><DD>the descriptor.</DL>
</DD>
</DL>
<HR>

<A NAME="addVendorFiles(java.util.Hashtable, java.lang.String)"><!-- --></A><H3>
addVendorFiles</H3>
<PRE>
protected void <B>addVendorFiles</B>(java.util.Hashtable&nbsp;ejbFiles,
                              java.lang.String&nbsp;ddPrefix)</PRE>
<DL>
<DD>Add any vendor specific files which should be included in the EJB Jar.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#addVendorFiles(java.util.Hashtable, java.lang.String)">addVendorFiles</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ejbFiles</CODE> - the hash table to be populated.<DD><CODE>ddPrefix</CODE> - the prefix to use.</DL>
</DD>
</DL>
<HR>

<A NAME="writeJar(java.lang.String, java.io.File, java.util.Hashtable, java.lang.String)"><!-- --></A><H3>
writeJar</H3>
<PRE>
protected void <B>writeJar</B>(java.lang.String&nbsp;baseName,
                        java.io.File&nbsp;jarFile,
                        java.util.Hashtable&nbsp;files,
                        java.lang.String&nbsp;publicId)
                 throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Method used to encapsulate the writing of the JAR file. Iterates over
 the filenames/java.io.Files in the Hashtable stored on the instance
 variable ejbFiles.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#writeJar(java.lang.String, java.io.File, java.util.Hashtable, java.lang.String)">writeJar</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>baseName</CODE> - the base name.<DD><CODE>jarFile</CODE> - the jar file to populate.<DD><CODE>files</CODE> - the hash table of files to write.<DD><CODE>publicId</CODE> - the id to use.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if there is a problem.</DL>
</DD>
</DL>
<HR>

<A NAME="validateConfigured()"><!-- --></A><H3>
validateConfigured</H3>
<PRE>
public void <B>validateConfigured</B>()
                        throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Called to validate that the tool parameters have been configured.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/EJBDeploymentTool.html#validateConfigured()">validateConfigured</A></CODE> in interface <CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/EJBDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.ejb">EJBDeploymentTool</A></CODE><DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#validateConfigured()">validateConfigured</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if there is an error.</DL>
</DD>
</DL>
<HR>

<A NAME="isRebuildRequired(java.io.File, java.io.File)"><!-- --></A><H3>
isRebuildRequired</H3>
<PRE>
protected boolean <B>isRebuildRequired</B>(java.io.File&nbsp;genericJarFile,
                                    java.io.File&nbsp;weblogicJarFile)</PRE>
<DL>
<DD>Helper method to check to see if a weblogic EBJ1.1 jar needs to be
 rebuilt using ejbc. Called from writeJar it sees if the "Bean" classes
 are the only thing that needs to be updated and either updates the Jar
 with the Bean classfile or returns true, saying that the whole weblogic
 jar needs to be regened with ejbc. This allows faster build times for
 working developers. <p>

 The way weblogic ejbc works is it creates wrappers for the publicly
 defined methods as they are exposed in the remote interface. If the
 actual bean changes without changing the the method signatures then
 only the bean classfile needs to be updated and the rest of the
 weblogic jar file can remain the same. If the Interfaces, ie. the
 method signatures change or if the xml deployment descriptors changed,
 the whole jar needs to be rebuilt with ejbc. This is not strictly true
 for the xml files. If the JNDI name changes then the jar doesnt have to
 be rebuild, but if the resources references change then it does. At
 this point the weblogic jar gets rebuilt if the xml files change at
 all.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>genericJarFile</CODE> - java.io.File The generic jar file.<DD><CODE>weblogicJarFile</CODE> - java.io.File The weblogic jar file to check to
      see if it needs to be rebuilt.
<DT><B>Returns:</B><DD>true if the jar needs to be rebuilt.</DL>
</DD>
</DL>
<HR>

<A NAME="getClassLoaderFromJar(java.io.File)"><!-- --></A><H3>
getClassLoaderFromJar</H3>
<PRE>
protected java.lang.ClassLoader <B>getClassLoaderFromJar</B>(java.io.File&nbsp;classjar)
                                               throws java.io.IOException</PRE>
<DL>
<DD>Helper method invoked by isRebuildRequired to get a ClassLoader for a
 Jar File passed to it.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classjar</CODE> - java.io.File representing jar file to get classes from.
<DT><B>Returns:</B><DD>the classloader for the jarfile.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there is a problem.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/ejb/WeblogicTOPLinkDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="WeblogicDeploymentTool.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
