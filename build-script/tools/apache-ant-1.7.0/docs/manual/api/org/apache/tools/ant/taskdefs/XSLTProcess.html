<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:25 EST 2006 -->
<TITLE>
XSLTProcess (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.XSLTProcess class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="XSLTProcess (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLoggerAware.html" title="interface in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Factory.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/XSLTProcess.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="XSLTProcess.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class XSLTProcess</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.MatchingTask</A>
              <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.XSLTProcess</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable, <A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLogger.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLogger</A>, <A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>XSLTProcess</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A><DT>implements <A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLogger.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLogger</A></DL>
</PRE>

<P>
Processes a set of XML documents via XSLT. This is
 useful for building views of XML based documentation.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.1</DD>
</DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Factory.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The factory element to configure a transformer factory</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.OutputProperty.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.OutputProperty</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specify how the result tree should be output as specified
 in the <a href="http://www.w3.org/TR/xslt#output">
 specification</a>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Param.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Param</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The Param inner class used to store XSL parameters</TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#PROCESSOR_TRAX">PROCESSOR_TRAX</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The default processor is trax</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.MatchingTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#fileset">fileset</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#XSLTProcess()">XSLTProcess</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new XSLTProcess Task.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#add(org.apache.tools.ant.util.FileNameMapper)">add</A></B>(<A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A>&nbsp;fileNameMapper)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a nested filenamemapper.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#add(org.apache.tools.ant.types.ResourceCollection)">add</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;rc)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a collection of resources to style in addition to the
 given file or the implicit fileset.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#addConfiguredStyle(org.apache.tools.ant.types.resources.Resources)">addConfiguredStyle</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/resources/Resources.html" title="class in org.apache.tools.ant.types.resources">Resources</A>&nbsp;rc)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a nested &lt;style&gt; element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#addConfiguredXMLCatalog(org.apache.tools.ant.types.XMLCatalog)">addConfiguredXMLCatalog</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</A>&nbsp;xmlCatalog)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add the catalog to our internal catalog</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#addMapper(org.apache.tools.ant.types.Mapper)">addMapper</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Mapper.html" title="class in org.apache.tools.ant.types">Mapper</A>&nbsp;mapper)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Defines the mapper to map source to destination files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#configureLiaison(java.io.File)">configureLiaison</A></B>(java.io.File&nbsp;stylesheet)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since Ant 1.7</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#configureLiaison(org.apache.tools.ant.types.Resource)">configureLiaison</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>&nbsp;stylesheet)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Loads the stylesheet and set xsl:param parameters.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#createClasspath()">createClasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the optional classpath to the XSL processor</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Factory.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#createFactory()">createFactory</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create the factory element to configure a trax liaison.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.OutputProperty.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.OutputProperty</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#createOutputProperty()">createOutputProperty</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create an instance of an output property to be configured.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Param.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Param</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#createParam()">createParam</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create an instance of an XSL parameter for configuration by Ant.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Executes the task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Factory.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#getFactory()">getFactory</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the factory instance configured for this processor</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#getLiaison()">getLiaison</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the Liason implementation to use in processing.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Enumeration</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#getOutputProperties()">getOutputProperties</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get an enumeration on the outputproperties.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#getXMLCatalog()">getXMLCatalog</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the XML catalog containing entity definitions</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#init()">init</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Initialize internal instance of XMLCatalog</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#setBasedir(java.io.File)">setBasedir</A></B>(java.io.File&nbsp;dir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the base directory;
 optional, default is the project's basedir.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the optional classpath to the XSL processor</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#setClasspathRef(org.apache.tools.ant.types.Reference)">setClasspathRef</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the reference to an optional classpath to the XSL processor</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#setDestdir(java.io.File)">setDestdir</A></B>(java.io.File&nbsp;dir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the destination directory into which the XSL result
 files should be copied to;
 required, unless <tt>in</tt> and <tt>out</tt> are
 specified.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#setExtension(java.lang.String)">setExtension</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the desired file extension to be used for the target;
 optional, default is html.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#setFileDirParameter(java.lang.String)">setFileDirParameter</A></B>(java.lang.String&nbsp;fileDirParameter)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Pass the directory name of the current processed file as a xsl parameter
 to the transformation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#setFileNameParameter(java.lang.String)">setFileNameParameter</A></B>(java.lang.String&nbsp;fileNameParameter)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Pass the filename of the current processed file as a xsl parameter
 to the transformation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#setForce(boolean)">setForce</A></B>(boolean&nbsp;force)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether to check dependencies, or always generate;
 optional, default is false.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#setIn(java.io.File)">setIn</A></B>(java.io.File&nbsp;inFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;specifies a single XML document to be styled.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#setOut(java.io.File)">setOut</A></B>(java.io.File&nbsp;outFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specifies the output name for the styled result from the
 <tt>in</tt> attribute; required if <tt>in</tt> is set</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#setProcessor(java.lang.String)">setProcessor</A></B>(java.lang.String&nbsp;processor)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the name of the XSL processor to use; optional, default trax.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#setReloadStylesheet(boolean)">setReloadStylesheet</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Controls whether the stylesheet is reloaded for every transform.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#setScanIncludedDirectories(boolean)">setScanIncludedDirectories</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether to style all files in the included directories as well;
 optional, default is true.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#setStyle(java.lang.String)">setStyle</A></B>(java.lang.String&nbsp;xslFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Name of the stylesheet to use - given either relative
 to the project's basedir or as an absolute path; required.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#setUseImplicitFileset(boolean)">setUseImplicitFileset</A></B>(boolean&nbsp;useimplicitfileset)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether to use the implicit fileset.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html#setXslResource(org.apache.tools.ant.types.Resource)">setXslResource</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>&nbsp;xslResource)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;API method to set the XSL Resource.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.MatchingTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createExclude()">createExclude</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createExcludesFile()">createExcludesFile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createInclude()">createInclude</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createIncludesFile()">createIncludesFile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createPatternSet()">createPatternSet</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#hasSelectors()">hasSelectors</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#selectorCount()">selectorCount</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#selectorElements()">selectorElements</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setExcludes(java.lang.String)">setExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setIncludes(java.lang.String)">setIncludes</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#XsetItems(java.lang.String)">XsetItems</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.XSLTLogger"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from interface org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLogger.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLogger</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLogger.html#log(java.lang.String)">log</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="PROCESSOR_TRAX"><!-- --></A><H3>
PROCESSOR_TRAX</H3>
<PRE>
public static final java.lang.String <B>PROCESSOR_TRAX</B></PRE>
<DL>
<DD>The default processor is trax
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.XSLTProcess.PROCESSOR_TRAX">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="XSLTProcess()"><!-- --></A><H3>
XSLTProcess</H3>
<PRE>
public <B>XSLTProcess</B>()</PRE>
<DL>
<DD>Creates a new XSLTProcess Task.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setScanIncludedDirectories(boolean)"><!-- --></A><H3>
setScanIncludedDirectories</H3>
<PRE>
public void <B>setScanIncludedDirectories</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Whether to style all files in the included directories as well;
 optional, default is true.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - true if files in included directories are processed.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setReloadStylesheet(boolean)"><!-- --></A><H3>
setReloadStylesheet</H3>
<PRE>
public void <B>setReloadStylesheet</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Controls whether the stylesheet is reloaded for every transform.

 <p>Setting this to true may get around a bug in certain
 Xalan-J versions, default is false.</p>
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - a <code>boolean</code> value<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addMapper(org.apache.tools.ant.types.Mapper)"><!-- --></A><H3>
addMapper</H3>
<PRE>
public void <B>addMapper</B>(<A HREF="../../../../../org/apache/tools/ant/types/Mapper.html" title="class in org.apache.tools.ant.types">Mapper</A>&nbsp;mapper)</PRE>
<DL>
<DD>Defines the mapper to map source to destination files.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>mapper</CODE> - the mapper to use
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if more than one mapper is defined<DT><B>Since:</B></DT>
  <DD>Ant 1.6.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="add(org.apache.tools.ant.types.ResourceCollection)"><!-- --></A><H3>
add</H3>
<PRE>
public void <B>add</B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;rc)</PRE>
<DL>
<DD>Adds a collection of resources to style in addition to the
 given file or the implicit fileset.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rc</CODE> - the collection of resources to style<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addConfiguredStyle(org.apache.tools.ant.types.resources.Resources)"><!-- --></A><H3>
addConfiguredStyle</H3>
<PRE>
public void <B>addConfiguredStyle</B>(<A HREF="../../../../../org/apache/tools/ant/types/resources/Resources.html" title="class in org.apache.tools.ant.types.resources">Resources</A>&nbsp;rc)</PRE>
<DL>
<DD>Add a nested &lt;style&gt; element.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rc</CODE> - the configured Resources object represented as &lt;style&gt;.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setXslResource(org.apache.tools.ant.types.Resource)"><!-- --></A><H3>
setXslResource</H3>
<PRE>
public void <B>setXslResource</B>(<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>&nbsp;xslResource)</PRE>
<DL>
<DD>API method to set the XSL Resource.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>xslResource</CODE> - Resource to set as the stylesheet.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="add(org.apache.tools.ant.util.FileNameMapper)"><!-- --></A><H3>
add</H3>
<PRE>
public void <B>add</B>(<A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A>&nbsp;fileNameMapper)
         throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Adds a nested filenamemapper.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fileNameMapper</CODE> - the mapper to add
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if more than one mapper is defined<DT><B>Since:</B></DT>
  <DD>Ant 1.7.0</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Executes the task.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if there is an execution problem.<DT><B>To do:</B></DT>
  <DD>validate that if either in or our is defined, then both are</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setForce(boolean)"><!-- --></A><H3>
setForce</H3>
<PRE>
public void <B>setForce</B>(boolean&nbsp;force)</PRE>
<DL>
<DD>Set whether to check dependencies, or always generate;
 optional, default is false.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>force</CODE> - true if always generate.</DL>
</DD>
</DL>
<HR>

<A NAME="setBasedir(java.io.File)"><!-- --></A><H3>
setBasedir</H3>
<PRE>
public void <B>setBasedir</B>(java.io.File&nbsp;dir)</PRE>
<DL>
<DD>Set the base directory;
 optional, default is the project's basedir.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>dir</CODE> - the base directory</DL>
</DD>
</DL>
<HR>

<A NAME="setDestdir(java.io.File)"><!-- --></A><H3>
setDestdir</H3>
<PRE>
public void <B>setDestdir</B>(java.io.File&nbsp;dir)</PRE>
<DL>
<DD>Set the destination directory into which the XSL result
 files should be copied to;
 required, unless <tt>in</tt> and <tt>out</tt> are
 specified.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>dir</CODE> - the name of the destination directory</DL>
</DD>
</DL>
<HR>

<A NAME="setExtension(java.lang.String)"><!-- --></A><H3>
setExtension</H3>
<PRE>
public void <B>setExtension</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Set the desired file extension to be used for the target;
 optional, default is html.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the extension to use</DL>
</DD>
</DL>
<HR>

<A NAME="setStyle(java.lang.String)"><!-- --></A><H3>
setStyle</H3>
<PRE>
public void <B>setStyle</B>(java.lang.String&nbsp;xslFile)</PRE>
<DL>
<DD>Name of the stylesheet to use - given either relative
 to the project's basedir or as an absolute path; required.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>xslFile</CODE> - the stylesheet to use</DL>
</DD>
</DL>
<HR>

<A NAME="setClasspath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setClasspath</H3>
<PRE>
public void <B>setClasspath</B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath)</PRE>
<DL>
<DD>Set the optional classpath to the XSL processor
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classpath</CODE> - the classpath to use when loading the XSL processor</DL>
</DD>
</DL>
<HR>

<A NAME="createClasspath()"><!-- --></A><H3>
createClasspath</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createClasspath</B>()</PRE>
<DL>
<DD>Set the optional classpath to the XSL processor
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a path instance to be configured by the Ant core.</DL>
</DD>
</DL>
<HR>

<A NAME="setClasspathRef(org.apache.tools.ant.types.Reference)"><!-- --></A><H3>
setClasspathRef</H3>
<PRE>
public void <B>setClasspathRef</B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</PRE>
<DL>
<DD>Set the reference to an optional classpath to the XSL processor
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>r</CODE> - the id of the Ant path instance to act as the classpath
          for loading the XSL processor</DL>
</DD>
</DL>
<HR>

<A NAME="setProcessor(java.lang.String)"><!-- --></A><H3>
setProcessor</H3>
<PRE>
public void <B>setProcessor</B>(java.lang.String&nbsp;processor)</PRE>
<DL>
<DD>Set the name of the XSL processor to use; optional, default trax.
 Other values are "xalan" for Xalan1
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>processor</CODE> - the name of the XSL processor</DL>
</DD>
</DL>
<HR>

<A NAME="setUseImplicitFileset(boolean)"><!-- --></A><H3>
setUseImplicitFileset</H3>
<PRE>
public void <B>setUseImplicitFileset</B>(boolean&nbsp;useimplicitfileset)</PRE>
<DL>
<DD>Whether to use the implicit fileset.

 <p>Set this to false if you want explicit control with nested
 resource collections.</p>
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>useimplicitfileset</CODE> - set to true if you want to use implicit fileset<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addConfiguredXMLCatalog(org.apache.tools.ant.types.XMLCatalog)"><!-- --></A><H3>
addConfiguredXMLCatalog</H3>
<PRE>
public void <B>addConfiguredXMLCatalog</B>(<A HREF="../../../../../org/apache/tools/ant/types/XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</A>&nbsp;xmlCatalog)</PRE>
<DL>
<DD>Add the catalog to our internal catalog
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>xmlCatalog</CODE> - the XMLCatalog instance to use to look up DTDs</DL>
</DD>
</DL>
<HR>

<A NAME="setFileNameParameter(java.lang.String)"><!-- --></A><H3>
setFileNameParameter</H3>
<PRE>
public void <B>setFileNameParameter</B>(java.lang.String&nbsp;fileNameParameter)</PRE>
<DL>
<DD>Pass the filename of the current processed file as a xsl parameter
 to the transformation. This value sets the name of that xsl parameter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fileNameParameter</CODE> - name of the xsl parameter retrieving the
                          current file name</DL>
</DD>
</DL>
<HR>

<A NAME="setFileDirParameter(java.lang.String)"><!-- --></A><H3>
setFileDirParameter</H3>
<PRE>
public void <B>setFileDirParameter</B>(java.lang.String&nbsp;fileDirParameter)</PRE>
<DL>
<DD>Pass the directory name of the current processed file as a xsl parameter
 to the transformation. This value sets the name of that xsl parameter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fileDirParameter</CODE> - name of the xsl parameter retrieving the
                         current file directory</DL>
</DD>
</DL>
<HR>

<A NAME="setOut(java.io.File)"><!-- --></A><H3>
setOut</H3>
<PRE>
public void <B>setOut</B>(java.io.File&nbsp;outFile)</PRE>
<DL>
<DD>Specifies the output name for the styled result from the
 <tt>in</tt> attribute; required if <tt>in</tt> is set
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>outFile</CODE> - the output File instance.</DL>
</DD>
</DL>
<HR>

<A NAME="setIn(java.io.File)"><!-- --></A><H3>
setIn</H3>
<PRE>
public void <B>setIn</B>(java.io.File&nbsp;inFile)</PRE>
<DL>
<DD>specifies a single XML document to be styled. Should be used
 with the <tt>out</tt> attribute; ; required if <tt>out</tt> is set
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>inFile</CODE> - the input file</DL>
</DD>
</DL>
<HR>

<A NAME="getFactory()"><!-- --></A><H3>
getFactory</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Factory.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory</A> <B>getFactory</B>()</PRE>
<DL>
<DD>Get the factory instance configured for this processor
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the factory instance in use</DL>
</DD>
</DL>
<HR>

<A NAME="getXMLCatalog()"><!-- --></A><H3>
getXMLCatalog</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</A> <B>getXMLCatalog</B>()</PRE>
<DL>
<DD>Get the XML catalog containing entity definitions
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the XML catalog for the task.</DL>
</DD>
</DL>
<HR>

<A NAME="getOutputProperties()"><!-- --></A><H3>
getOutputProperties</H3>
<PRE>
public java.util.Enumeration <B>getOutputProperties</B>()</PRE>
<DL>
<DD>Get an enumeration on the outputproperties.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the outputproperties</DL>
</DD>
</DL>
<HR>

<A NAME="getLiaison()"><!-- --></A><H3>
getLiaison</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</A> <B>getLiaison</B>()</PRE>
<DL>
<DD>Get the Liason implementation to use in processing.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>an instance of the XSLTLiason interface.</DL>
</DD>
</DL>
<HR>

<A NAME="createParam()"><!-- --></A><H3>
createParam</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Param.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Param</A> <B>createParam</B>()</PRE>
<DL>
<DD>Create an instance of an XSL parameter for configuration by Ant.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>an instance of the Param class to be configured.</DL>
</DD>
</DL>
<HR>

<A NAME="createOutputProperty()"><!-- --></A><H3>
createOutputProperty</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.OutputProperty.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.OutputProperty</A> <B>createOutputProperty</B>()</PRE>
<DL>
<DD>Create an instance of an output property to be configured.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the newly created output property.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="init()"><!-- --></A><H3>
init</H3>
<PRE>
public void <B>init</B>()
          throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Initialize internal instance of XMLCatalog
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="configureLiaison(java.io.File)"><!-- --></A><H3>
configureLiaison</H3>
<PRE>
protected void <B>configureLiaison</B>(java.io.File&nbsp;stylesheet)
                         throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since Ant 1.7</I>
<P>
<DD>Loads the stylesheet and set xsl:param parameters.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>stylesheet</CODE> - the file from which to load the stylesheet.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the stylesheet cannot be loaded.</DL>
</DD>
</DL>
<HR>

<A NAME="configureLiaison(org.apache.tools.ant.types.Resource)"><!-- --></A><H3>
configureLiaison</H3>
<PRE>
protected void <B>configureLiaison</B>(<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>&nbsp;stylesheet)
                         throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Loads the stylesheet and set xsl:param parameters.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>stylesheet</CODE> - the resource from which to load the stylesheet.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the stylesheet cannot be loaded.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="createFactory()"><!-- --></A><H3>
createFactory</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Factory.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory</A> <B>createFactory</B>()
                                  throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Create the factory element to configure a trax liaison.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the newly created factory element.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the element is created more than one time.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLoggerAware.html" title="interface in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Factory.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/XSLTProcess.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="XSLTProcess.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
