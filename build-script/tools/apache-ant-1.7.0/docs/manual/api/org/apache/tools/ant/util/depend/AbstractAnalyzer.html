<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:34 EST 2006 -->
<TITLE>
AbstractAnalyzer (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.util.depend.AbstractAnalyzer class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="AbstractAnalyzer (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV CLASS&nbsp;
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html" title="interface in org.apache.tools.ant.util.depend"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../index.html?org/apache/tools/ant/util/depend/AbstractAnalyzer.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="AbstractAnalyzer.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.util.depend</FONT>
<BR>
Class AbstractAnalyzer</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.util.depend.AbstractAnalyzer</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html" title="interface in org.apache.tools.ant.util.depend">DependencyAnalyzer</A></DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../../../org/apache/tools/ant/util/depend/bcel/AncestorAnalyzer.html" title="class in org.apache.tools.ant.util.depend.bcel">AncestorAnalyzer</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/depend/AntAnalyzer.html" title="class in org.apache.tools.ant.taskdefs.optional.depend">AntAnalyzer</A>, <A HREF="../../../../../../org/apache/tools/ant/util/depend/bcel/FullAnalyzer.html" title="class in org.apache.tools.ant.util.depend.bcel">FullAnalyzer</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public abstract class <B>AbstractAnalyzer</B><DT>extends java.lang.Object<DT>implements <A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html" title="interface in org.apache.tools.ant.util.depend">DependencyAnalyzer</A></DL>
</PRE>

<P>
An abstract implementation of the analyzer interface providing support
 for the bulk of interface methods.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/util/depend/AbstractAnalyzer.html#MAX_LOOPS">MAX_LOOPS</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Maximum number of loops for looking for indirect dependencies.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected </CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/util/depend/AbstractAnalyzer.html#AbstractAnalyzer()">AbstractAnalyzer</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Setup the analyzer</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/util/depend/AbstractAnalyzer.html#addClassPath(org.apache.tools.ant.types.Path)">addClassPath</A></B>(<A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classPath)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a classpath to the classpath being used by the analyzer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/util/depend/AbstractAnalyzer.html#addRootClass(java.lang.String)">addRootClass</A></B>(java.lang.String&nbsp;className)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a root class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/util/depend/AbstractAnalyzer.html#addSourcePath(org.apache.tools.ant.types.Path)">addSourcePath</A></B>(<A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;sourcePath)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a source path to the source path used by this analyzer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/util/depend/AbstractAnalyzer.html#config(java.lang.String, java.lang.Object)">config</A></B>(java.lang.String&nbsp;name,
       java.lang.Object&nbsp;info)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Configure an aspect of the analyzer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected abstract &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/util/depend/AbstractAnalyzer.html#determineDependencies(java.util.Vector, java.util.Vector)">determineDependencies</A></B>(java.util.Vector&nbsp;files,
                      java.util.Vector&nbsp;classes)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Determine the dependencies of the current set of root classes</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/util/depend/AbstractAnalyzer.html#getClassContainer(java.lang.String)">getClassContainer</A></B>(java.lang.String&nbsp;classname)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the file that contains the class definition</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Enumeration</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/util/depend/AbstractAnalyzer.html#getClassDependencies()">getClassDependencies</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the list of classes upon which root classes depend.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Enumeration</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/util/depend/AbstractAnalyzer.html#getFileDependencies()">getFileDependencies</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the list of files in the file system upon which the root classes
 depend.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Enumeration</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/util/depend/AbstractAnalyzer.html#getRootClasses()">getRootClasses</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get an enumeration of the root classes</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/util/depend/AbstractAnalyzer.html#getSourceContainer(java.lang.String)">getSourceContainer</A></B>(java.lang.String&nbsp;classname)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the file that contains the class source.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/util/depend/AbstractAnalyzer.html#isClosureRequired()">isClosureRequired</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicate if the analyzer is required to follow
 indirect class relationships.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/util/depend/AbstractAnalyzer.html#reset()">reset</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Reset the dependency list.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/util/depend/AbstractAnalyzer.html#setClosure(boolean)">setClosure</A></B>(boolean&nbsp;closure)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the closure flag.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected abstract &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/util/depend/AbstractAnalyzer.html#supportsFileDependencies()">supportsFileDependencies</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicate if the particular subclass supports file dependency
 information.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="MAX_LOOPS"><!-- --></A><H3>
MAX_LOOPS</H3>
<PRE>
public static final int <B>MAX_LOOPS</B></PRE>
<DL>
<DD>Maximum number of loops for looking for indirect dependencies.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../constant-values.html#org.apache.tools.ant.util.depend.AbstractAnalyzer.MAX_LOOPS">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="AbstractAnalyzer()"><!-- --></A><H3>
AbstractAnalyzer</H3>
<PRE>
protected <B>AbstractAnalyzer</B>()</PRE>
<DL>
<DD>Setup the analyzer
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setClosure(boolean)"><!-- --></A><H3>
setClosure</H3>
<PRE>
public void <B>setClosure</B>(boolean&nbsp;closure)</PRE>
<DL>
<DD>Set the closure flag. If this flag is true the analyzer will traverse
 all class relationships until it has collected the entire set of
 direct and indirect dependencies
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html#setClosure(boolean)">setClosure</A></CODE> in interface <CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html" title="interface in org.apache.tools.ant.util.depend">DependencyAnalyzer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>closure</CODE> - true if dependencies should be traversed to determine
      indirect dependencies.</DL>
</DD>
</DL>
<HR>

<A NAME="getFileDependencies()"><!-- --></A><H3>
getFileDependencies</H3>
<PRE>
public java.util.Enumeration <B>getFileDependencies</B>()</PRE>
<DL>
<DD>Get the list of files in the file system upon which the root classes
 depend. The files will be either the classfiles or jar files upon
 which the root classes depend.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html#getFileDependencies()">getFileDependencies</A></CODE> in interface <CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html" title="interface in org.apache.tools.ant.util.depend">DependencyAnalyzer</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>an enumeration of File instances.</DL>
</DD>
</DL>
<HR>

<A NAME="getClassDependencies()"><!-- --></A><H3>
getClassDependencies</H3>
<PRE>
public java.util.Enumeration <B>getClassDependencies</B>()</PRE>
<DL>
<DD>Get the list of classes upon which root classes depend. This is a
 list of Java classnames in dot notation.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html#getClassDependencies()">getClassDependencies</A></CODE> in interface <CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html" title="interface in org.apache.tools.ant.util.depend">DependencyAnalyzer</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>an enumeration of Strings, each being the name of a Java
      class in dot notation.</DL>
</DD>
</DL>
<HR>

<A NAME="getClassContainer(java.lang.String)"><!-- --></A><H3>
getClassContainer</H3>
<PRE>
public java.io.File <B>getClassContainer</B>(java.lang.String&nbsp;classname)
                               throws java.io.IOException</PRE>
<DL>
<DD>Get the file that contains the class definition
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html#getClassContainer(java.lang.String)">getClassContainer</A></CODE> in interface <CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html" title="interface in org.apache.tools.ant.util.depend">DependencyAnalyzer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classname</CODE> - the name of the required class
<DT><B>Returns:</B><DD>the file instance, zip or class, containing the
         class or null if the class could not be found.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the files in the classpath cannot be read.</DL>
</DD>
</DL>
<HR>

<A NAME="getSourceContainer(java.lang.String)"><!-- --></A><H3>
getSourceContainer</H3>
<PRE>
public java.io.File <B>getSourceContainer</B>(java.lang.String&nbsp;classname)
                                throws java.io.IOException</PRE>
<DL>
<DD>Get the file that contains the class source.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html#getSourceContainer(java.lang.String)">getSourceContainer</A></CODE> in interface <CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html" title="interface in org.apache.tools.ant.util.depend">DependencyAnalyzer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classname</CODE> - the name of the required class
<DT><B>Returns:</B><DD>the file instance, zip or java, containing the
         source or null if the source for the class could not be found.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the files in the sourcepath cannot be read.</DL>
</DD>
</DL>
<HR>

<A NAME="addSourcePath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
addSourcePath</H3>
<PRE>
public void <B>addSourcePath</B>(<A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;sourcePath)</PRE>
<DL>
<DD>Add a source path to the source path used by this analyzer. The
 elements in the given path contain the source files for the classes
 being analyzed. Not all analyzers will use this information.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html#addSourcePath(org.apache.tools.ant.types.Path)">addSourcePath</A></CODE> in interface <CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html" title="interface in org.apache.tools.ant.util.depend">DependencyAnalyzer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourcePath</CODE> - The Path instance specifying the source path
      elements.</DL>
</DD>
</DL>
<HR>

<A NAME="addClassPath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
addClassPath</H3>
<PRE>
public void <B>addClassPath</B>(<A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classPath)</PRE>
<DL>
<DD>Add a classpath to the classpath being used by the analyzer. The
 classpath contains the binary classfiles for the classes being
 analyzed The elements may either be the directories or jar files.Not
 all analyzers will use this information.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html#addClassPath(org.apache.tools.ant.types.Path)">addClassPath</A></CODE> in interface <CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html" title="interface in org.apache.tools.ant.util.depend">DependencyAnalyzer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classPath</CODE> - the Path instance specifying the classpath elements</DL>
</DD>
</DL>
<HR>

<A NAME="addRootClass(java.lang.String)"><!-- --></A><H3>
addRootClass</H3>
<PRE>
public void <B>addRootClass</B>(java.lang.String&nbsp;className)</PRE>
<DL>
<DD>Add a root class. The root classes are used to drive the
 determination of dependency information. The analyzer will start at
 the root classes and add dependencies from there.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html#addRootClass(java.lang.String)">addRootClass</A></CODE> in interface <CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html" title="interface in org.apache.tools.ant.util.depend">DependencyAnalyzer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>className</CODE> - the name of the class in Java dot notation.</DL>
</DD>
</DL>
<HR>

<A NAME="config(java.lang.String, java.lang.Object)"><!-- --></A><H3>
config</H3>
<PRE>
public void <B>config</B>(java.lang.String&nbsp;name,
                   java.lang.Object&nbsp;info)</PRE>
<DL>
<DD>Configure an aspect of the analyzer. The set of aspects that are
 supported is specific to each analyzer instance.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html#config(java.lang.String, java.lang.Object)">config</A></CODE> in interface <CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html" title="interface in org.apache.tools.ant.util.depend">DependencyAnalyzer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the name of the aspect being configured<DD><CODE>info</CODE> - the configuration info.</DL>
</DD>
</DL>
<HR>

<A NAME="reset()"><!-- --></A><H3>
reset</H3>
<PRE>
public void <B>reset</B>()</PRE>
<DL>
<DD>Reset the dependency list. This will reset the determined
 dependencies and the also list of root classes.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html#reset()">reset</A></CODE> in interface <CODE><A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html" title="interface in org.apache.tools.ant.util.depend">DependencyAnalyzer</A></CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="getRootClasses()"><!-- --></A><H3>
getRootClasses</H3>
<PRE>
protected java.util.Enumeration <B>getRootClasses</B>()</PRE>
<DL>
<DD>Get an enumeration of the root classes
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>an enumeration of Strings, each of which is a class name
         for a root class.</DL>
</DD>
</DL>
<HR>

<A NAME="isClosureRequired()"><!-- --></A><H3>
isClosureRequired</H3>
<PRE>
protected boolean <B>isClosureRequired</B>()</PRE>
<DL>
<DD>Indicate if the analyzer is required to follow
 indirect class relationships.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>true if indirect relationships should be followed.</DL>
</DD>
</DL>
<HR>

<A NAME="determineDependencies(java.util.Vector, java.util.Vector)"><!-- --></A><H3>
determineDependencies</H3>
<PRE>
protected abstract void <B>determineDependencies</B>(java.util.Vector&nbsp;files,
                                              java.util.Vector&nbsp;classes)</PRE>
<DL>
<DD>Determine the dependencies of the current set of root classes
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>files</CODE> - a vector into which Files upon which the root classes
      depend should be placed.<DD><CODE>classes</CODE> - a vector of Strings into which the names of classes
      upon which the root classes depend should be placed.</DL>
</DD>
</DL>
<HR>

<A NAME="supportsFileDependencies()"><!-- --></A><H3>
supportsFileDependencies</H3>
<PRE>
protected abstract boolean <B>supportsFileDependencies</B>()</PRE>
<DL>
<DD>Indicate if the particular subclass supports file dependency
 information.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>true if file dependencies are supported.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV CLASS&nbsp;
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/util/depend/DependencyAnalyzer.html" title="interface in org.apache.tools.ant.util.depend"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../index.html?org/apache/tools/ant/util/depend/AbstractAnalyzer.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="AbstractAnalyzer.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
