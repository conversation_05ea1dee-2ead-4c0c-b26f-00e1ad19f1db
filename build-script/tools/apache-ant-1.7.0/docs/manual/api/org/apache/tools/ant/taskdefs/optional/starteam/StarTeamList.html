<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:30 EST 2006 -->
<TITLE>
StarTeamList (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.optional.starteam.StarTeamList class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="StarTeamList (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamLabel.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/starteam/StarTeamList.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="StarTeamList.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_classes_inherited_from_class_org.apache.tools.ant.taskdefs.optional.starteam.TreeBasedTask">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.starteam.TreeBasedTask">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.optional.starteam</FONT>
<BR>
Class StarTeamList</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam">org.apache.tools.ant.taskdefs.optional.starteam.StarTeamTask</A>
              <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam">org.apache.tools.ant.taskdefs.optional.starteam.TreeBasedTask</A>
                  <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.optional.starteam.StarTeamList</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>StarTeamList</B><DT>extends <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam">TreeBasedTask</A></DL>
</PRE>

<P>
Produces a listing of the contents of the StarTeam repository
 at the specified view and StarTeamFolder.

 Created: Tue Dec 25 06:51:14 2001
<P>

<P>
<DL>
<DT><B>Version:</B></DT>
  <DD>1.0</DD>
</DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="nested_classes_inherited_from_class_org.apache.tools.ant.taskdefs.optional.starteam.TreeBasedTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Nested classes/interfaces inherited from class org.apache.tools.ant.taskdefs.optional.starteam.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam">TreeBasedTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.UnmatchedFileMap.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam">TreeBasedTask.UnmatchedFileMap</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.starteam.TreeBasedTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.optional.starteam.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam">TreeBasedTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#DEFAULT_EXCLUDESETTING">DEFAULT_EXCLUDESETTING</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#DEFAULT_INCLUDESETTING">DEFAULT_INCLUDESETTING</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamList.html#StarTeamList()">StarTeamList</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;com.starbase.starteam.View</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamList.html#createSnapshotView(com.starbase.starteam.View)">createSnapshotView</A></B>(com.starbase.starteam.View&nbsp;raw)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Override of base-class abstract function creates an
 appropriately configured view for checkoutlists - either
 the current view or a view from this.label.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamList.html#list(com.starbase.starteam.File, java.io.File)">list</A></B>(com.starbase.starteam.File&nbsp;reposFile,
     java.io.File&nbsp;localFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Log a repositary file and it's corresponding local file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamList.html#logOperationDescription(com.starbase.starteam.Folder, java.io.File)">logOperationDescription</A></B>(com.starbase.starteam.Folder&nbsp;starteamrootFolder,
                        java.io.File&nbsp;targetrootFolder)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;extenders should emit to the log an entry describing the parameters
 that will be used by this operation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamList.html#pad(java.lang.String, int)">pad</A></B>(java.lang.String&nbsp;s,
    int&nbsp;padlen)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return a padded string.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamList.html#rpad(java.lang.String, int)">rpad</A></B>(java.lang.String&nbsp;s,
     int&nbsp;padlen)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return a right padded string.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamList.html#setAsOfDate(java.lang.String)">setAsOfDate</A></B>(java.lang.String&nbsp;asOfDateParam)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;List files, dates, and statuses as of this date; optional.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamList.html#setAsOfDateFormat(java.lang.String)">setAsOfDateFormat</A></B>(java.lang.String&nbsp;asOfDateFormat)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Date Format with which asOfDate parameter to be parsed; optional.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamList.html#setLabel(java.lang.String)">setLabel</A></B>(java.lang.String&nbsp;label)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;List files, dates, and statuses as of this label; optional.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamList.html#testPreconditions()">testPreconditions</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Required base-class abstract function implementation checks for
 incompatible parameters.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamList.html#visit(com.starbase.starteam.Folder, java.io.File)">visit</A></B>(com.starbase.starteam.Folder&nbsp;starteamFolder,
      java.io.File&nbsp;targetFolder)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Implements base-class abstract function to perform the checkout
 operation on the files in each folder of the tree.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.optional.starteam.TreeBasedTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.optional.starteam.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam">TreeBasedTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#_setAsOfDate(java.lang.String)">_setAsOfDate</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#_setAsOfDateFormat(java.lang.String)">_setAsOfDateFormat</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#_setLabel(java.lang.String)">_setLabel</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#execute()">execute</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#getAsOfDate()">getAsOfDate</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#getExcludes()">getExcludes</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#getFullRepositoryPath(com.starbase.starteam.File)">getFullRepositoryPath</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#getIDofLabelInUse()">getIDofLabelInUse</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#getIncludes()">getIncludes</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#getLabel()">getLabel</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#getLabelID(com.starbase.starteam.View)">getLabelID</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#getLabelInUse()">getLabelInUse</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#getRootLocalFolder()">getRootLocalFolder</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#getRootStarteamFolder()">getRootStarteamFolder</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#getViewConfiguredByDate(com.starbase.starteam.View)">getViewConfiguredByDate</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#isForced()">isForced</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#isPreloadFileInformation()">isPreloadFileInformation</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#isRecursive()">isRecursive</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#isUsingRevisionLabel()">isUsingRevisionLabel</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#isUsingViewLabel()">isUsingViewLabel</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#logAsOfDate()">logAsOfDate</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#logExcludes()">logExcludes</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#logIncludes()">logIncludes</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#logLabel()">logLabel</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#matchPatterns(java.lang.String, java.lang.String)">matchPatterns</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#setExcludes(java.lang.String)">setExcludes</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#setForced(boolean)">setForced</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#setIncludes(java.lang.String)">setIncludes</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#setPreloadFileInformation(boolean)">setPreloadFileInformation</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#setRecursive(boolean)">setRecursive</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#setRootLocalFolder(java.lang.String)">setRootLocalFolder</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#setRootStarteamFolder(java.lang.String)">setRootStarteamFolder</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#shouldProcess(java.lang.String)">shouldProcess</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.optional.starteam.StarTeamTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.optional.starteam.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam">StarTeamTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#disconnectFromServer()">disconnectFromServer</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#getPassword()">getPassword</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#getProjectname()">getProjectname</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#getServer()">getServer</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#getServername()">getServername</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#getServerport()">getServerport</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#getTypeNames()">getTypeNames</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#getURL()">getURL</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#getUserName()">getUserName</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#getUserName(int)">getUserName</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#getViewname()">getViewname</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#getViewURL()">getViewURL</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#openView()">openView</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#setPassword(java.lang.String)">setPassword</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#setProjectname(java.lang.String)">setProjectname</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#setServername(java.lang.String)">setServername</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#setServerport(java.lang.String)">setServerport</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#setURL(java.lang.String)">setURL</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#setUserName(java.lang.String)">setUserName</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#setViewname(java.lang.String)">setViewname</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="StarTeamList()"><!-- --></A><H3>
StarTeamList</H3>
<PRE>
public <B>StarTeamList</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setLabel(java.lang.String)"><!-- --></A><H3>
setLabel</H3>
<PRE>
public void <B>setLabel</B>(java.lang.String&nbsp;label)</PRE>
<DL>
<DD>List files, dates, and statuses as of this label; optional.
 The label must exist in starteam or an exception will be thrown.
 If not specified, the most recent version of each file will be listed.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>label</CODE> - the label to be listed</DL>
</DD>
</DL>
<HR>

<A NAME="setAsOfDate(java.lang.String)"><!-- --></A><H3>
setAsOfDate</H3>
<PRE>
public void <B>setAsOfDate</B>(java.lang.String&nbsp;asOfDateParam)</PRE>
<DL>
<DD>List files, dates, and statuses as of this date; optional.
 If not specified, the most recent version of each file will be listed.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>asOfDateParam</CODE> - the date as of which the listing to be made<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setAsOfDateFormat(java.lang.String)"><!-- --></A><H3>
setAsOfDateFormat</H3>
<PRE>
public void <B>setAsOfDateFormat</B>(java.lang.String&nbsp;asOfDateFormat)</PRE>
<DL>
<DD>Date Format with which asOfDate parameter to be parsed; optional.
 Must be a SimpleDateFormat compatible string.
 If not specified, and asOfDateParam is specified, parse will use ISO8601
 datetime and date formats.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>asOfDateFormat</CODE> - the SimpleDateFormat-compatible format string<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="createSnapshotView(com.starbase.starteam.View)"><!-- --></A><H3>
createSnapshotView</H3>
<PRE>
protected com.starbase.starteam.View <B>createSnapshotView</B>(com.starbase.starteam.View&nbsp;raw)</PRE>
<DL>
<DD>Override of base-class abstract function creates an
 appropriately configured view for checkoutlists - either
 the current view or a view from this.label.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html#createSnapshotView(com.starbase.starteam.View)">createSnapshotView</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam">StarTeamTask</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>raw</CODE> - the unconfigured <code>View</code>
<DT><B>Returns:</B><DD>the snapshot <code>View</code> appropriately configured.</DL>
</DD>
</DL>
<HR>

<A NAME="testPreconditions()"><!-- --></A><H3>
testPreconditions</H3>
<PRE>
protected void <B>testPreconditions</B>()
                          throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Required base-class abstract function implementation checks for
 incompatible parameters.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#testPreconditions()">testPreconditions</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam">TreeBasedTask</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - thrown on incompatible params specified<DT><B>See Also:</B><DD><code>execute()</code></DL>
</DD>
</DL>
<HR>

<A NAME="logOperationDescription(com.starbase.starteam.Folder, java.io.File)"><!-- --></A><H3>
logOperationDescription</H3>
<PRE>
protected void <B>logOperationDescription</B>(com.starbase.starteam.Folder&nbsp;starteamrootFolder,
                                       java.io.File&nbsp;targetrootFolder)</PRE>
<DL>
<DD>extenders should emit to the log an entry describing the parameters
 that will be used by this operation.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#logOperationDescription(com.starbase.starteam.Folder, java.io.File)">logOperationDescription</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam">TreeBasedTask</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>starteamrootFolder</CODE> - root folder in StarTeam for the operation<DD><CODE>targetrootFolder</CODE> - root local folder for the operation (whether specified by the Employee or not.</DL>
</DD>
</DL>
<HR>

<A NAME="visit(com.starbase.starteam.Folder, java.io.File)"><!-- --></A><H3>
visit</H3>
<PRE>
protected void <B>visit</B>(com.starbase.starteam.Folder&nbsp;starteamFolder,
                     java.io.File&nbsp;targetFolder)
              throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Implements base-class abstract function to perform the checkout
 operation on the files in each folder of the tree.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#visit(com.starbase.starteam.Folder, java.io.File)">visit</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam">TreeBasedTask</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>starteamFolder</CODE> - the StarTeam folder from which files to be
                       checked out<DD><CODE>targetFolder</CODE> - the local mapping of rootStarteamFolder
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="list(com.starbase.starteam.File, java.io.File)"><!-- --></A><H3>
list</H3>
<PRE>
protected void <B>list</B>(com.starbase.starteam.File&nbsp;reposFile,
                    java.io.File&nbsp;localFile)
             throws java.io.IOException</PRE>
<DL>
<DD>Log a repositary file and it's corresponding local file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>reposFile</CODE> - the repositary file to log<DD><CODE>localFile</CODE> - the corresponding local file
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error getting information from files</DL>
</DD>
</DL>
<HR>

<A NAME="pad(java.lang.String, int)"><!-- --></A><H3>
pad</H3>
<PRE>
protected static java.lang.String <B>pad</B>(java.lang.String&nbsp;s,
                                      int&nbsp;padlen)</PRE>
<DL>
<DD>Return a padded string.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>s</CODE> - the string to pad<DD><CODE>padlen</CODE> - the size of the padded string
<DT><B>Returns:</B><DD>the padded string</DL>
</DD>
</DL>
<HR>

<A NAME="rpad(java.lang.String, int)"><!-- --></A><H3>
rpad</H3>
<PRE>
protected static java.lang.String <B>rpad</B>(java.lang.String&nbsp;s,
                                       int&nbsp;padlen)</PRE>
<DL>
<DD>Return a right padded string.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>s</CODE> - the string to pad<DD><CODE>padlen</CODE> - the size of the padded string
<DT><B>Returns:</B><DD>the padded string</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamLabel.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/starteam/StarTeamList.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="StarTeamList.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_classes_inherited_from_class_org.apache.tools.ant.taskdefs.optional.starteam.TreeBasedTask">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.starteam.TreeBasedTask">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
