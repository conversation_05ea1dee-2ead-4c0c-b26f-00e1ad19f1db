<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:22 EST 2006 -->
<TITLE>
Execute (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.Execute class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Execute (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Execute.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Execute.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class Execute</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.Execute</B>
</PRE>
<HR>
<DL>
<DT><PRE>public class <B>Execute</B><DT>extends java.lang.Object</DL>
</PRE>

<P>
Runs an external program.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
</DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#INVALID">INVALID</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Invalid exit code.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#Execute()">Execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new execute object using <code>PumpStreamHandler</code> for
 stream handling.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#Execute(org.apache.tools.ant.taskdefs.ExecuteStreamHandler)">Execute</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A>&nbsp;streamHandler)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new execute object.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#Execute(org.apache.tools.ant.taskdefs.ExecuteStreamHandler, org.apache.tools.ant.taskdefs.ExecuteWatchdog)">Execute</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A>&nbsp;streamHandler,
        <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs">ExecuteWatchdog</A>&nbsp;watchdog)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new execute object.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#closeStreams(java.lang.Process)">closeStreams</A></B>(java.lang.Process&nbsp;process)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Close the streams belonging to the given Process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Runs a process defined by the command line and returns its exit status.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#getCommandline()">getCommandline</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the commandline used to create a subprocess.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#getEnvironment()">getEnvironment</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the environment used to create a subprocess.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#getExitValue()">getExitValue</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Query the exit value of the process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#getProcEnvironment()">getProcEnvironment</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Find the list of environment variables for this process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#getWorkingDirectory()">getWorkingDirectory</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the working directory.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#isFailure()">isFailure</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Did this execute return in a failure.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#isFailure(int)">isFailure</A></B>(int&nbsp;exitValue)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Checks whether <code>exitValue</code> signals a failure on the current
 system (OS specific).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#killedProcess()">killedProcess</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Test for an untimely death of the process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.Process</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#launch(org.apache.tools.ant.Project, java.lang.String[], java.lang.String[], java.io.File, boolean)">launch</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
       java.lang.String[]&nbsp;command,
       java.lang.String[]&nbsp;env,
       java.io.File&nbsp;dir,
       boolean&nbsp;useVM)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a process that runs a command.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#runCommand(org.apache.tools.ant.Task, java.lang.String[])">runCommand</A></B>(<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task,
           java.lang.String[]&nbsp;cmdline)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A utility method that runs an external command.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#setAntRun(org.apache.tools.ant.Project)">setAntRun</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the name of the antRun script using the project's value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#setCommandline(java.lang.String[])">setCommandline</A></B>(java.lang.String[]&nbsp;commandline)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the commandline of the subprocess to launch.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#setEnvironment(java.lang.String[])">setEnvironment</A></B>(java.lang.String[]&nbsp;env)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the environment variables for the subprocess to launch.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#setExitValue(int)">setExitValue</A></B>(int&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the exit value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#setNewenvironment(boolean)">setNewenvironment</A></B>(boolean&nbsp;newenv)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether to propagate the default environment or not.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#setSpawn(boolean)">setSpawn</A></B>(boolean&nbsp;spawn)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether or not you want the process to be spawned.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#setStreamHandler(org.apache.tools.ant.taskdefs.ExecuteStreamHandler)">setStreamHandler</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A>&nbsp;streamHandler)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the stream handler to use.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#setVMLauncher(boolean)">setVMLauncher</A></B>(boolean&nbsp;useVMLauncher)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Launch this execution through the VM, where possible, rather than through
 the OS's shell.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#setWorkingDirectory(java.io.File)">setWorkingDirectory</A></B>(java.io.File&nbsp;wd)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the working directory of the process to execute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#spawn()">spawn</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Starts a process defined by the command line.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#toString(java.io.ByteArrayOutputStream)">toString</A></B>(java.io.ByteArrayOutputStream&nbsp;bos)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ByteArrayOutputStream#toString doesn't seem to work reliably on
 OS/390, at least not the way we use it in the execution
 context.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#waitFor(java.lang.Process)">waitFor</A></B>(java.lang.Process&nbsp;process)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Wait for a given process.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="INVALID"><!-- --></A><H3>
INVALID</H3>
<PRE>
public static final int <B>INVALID</B></PRE>
<DL>
<DD>Invalid exit code.
 set to <CODE>Integer.MAX_VALUE</CODE>
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Execute.INVALID">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Execute()"><!-- --></A><H3>
Execute</H3>
<PRE>
public <B>Execute</B>()</PRE>
<DL>
<DD>Creates a new execute object using <code>PumpStreamHandler</code> for
 stream handling.
<P>
</DL>
<HR>

<A NAME="Execute(org.apache.tools.ant.taskdefs.ExecuteStreamHandler)"><!-- --></A><H3>
Execute</H3>
<PRE>
public <B>Execute</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A>&nbsp;streamHandler)</PRE>
<DL>
<DD>Creates a new execute object.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>streamHandler</CODE> - the stream handler used to handle the input and
        output streams of the subprocess.</DL>
</DL>
<HR>

<A NAME="Execute(org.apache.tools.ant.taskdefs.ExecuteStreamHandler, org.apache.tools.ant.taskdefs.ExecuteWatchdog)"><!-- --></A><H3>
Execute</H3>
<PRE>
public <B>Execute</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A>&nbsp;streamHandler,
               <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs">ExecuteWatchdog</A>&nbsp;watchdog)</PRE>
<DL>
<DD>Creates a new execute object.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>streamHandler</CODE> - the stream handler used to handle the input and
        output streams of the subprocess.<DD><CODE>watchdog</CODE> - a watchdog for the subprocess or <code>null</code> to
        to disable a timeout for the subprocess.</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setSpawn(boolean)"><!-- --></A><H3>
setSpawn</H3>
<PRE>
public void <B>setSpawn</B>(boolean&nbsp;spawn)</PRE>
<DL>
<DD>Set whether or not you want the process to be spawned.
 Default is not spawned.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>spawn</CODE> - if true you do not want Ant
              to wait for the end of the process.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getProcEnvironment()"><!-- --></A><H3>
getProcEnvironment</H3>
<PRE>
public static java.util.Vector <B>getProcEnvironment</B>()</PRE>
<DL>
<DD>Find the list of environment variables for this process.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a vector containing the environment variables.
 The vector elements are strings formatted like variable = value.</DL>
</DD>
</DL>
<HR>

<A NAME="toString(java.io.ByteArrayOutputStream)"><!-- --></A><H3>
toString</H3>
<PRE>
public static java.lang.String <B>toString</B>(java.io.ByteArrayOutputStream&nbsp;bos)</PRE>
<DL>
<DD>ByteArrayOutputStream#toString doesn't seem to work reliably on
 OS/390, at least not the way we use it in the execution
 context.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>bos</CODE> - the output stream that one wants to read.
<DT><B>Returns:</B><DD>the output stream as a string, read with
 special encodings in the case of z/os and os/400.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setStreamHandler(org.apache.tools.ant.taskdefs.ExecuteStreamHandler)"><!-- --></A><H3>
setStreamHandler</H3>
<PRE>
public void <B>setStreamHandler</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A>&nbsp;streamHandler)</PRE>
<DL>
<DD>Set the stream handler to use.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>streamHandler</CODE> - ExecuteStreamHandler.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getCommandline()"><!-- --></A><H3>
getCommandline</H3>
<PRE>
public java.lang.String[] <B>getCommandline</B>()</PRE>
<DL>
<DD>Returns the commandline used to create a subprocess.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the commandline used to create a subprocess.</DL>
</DD>
</DL>
<HR>

<A NAME="setCommandline(java.lang.String[])"><!-- --></A><H3>
setCommandline</H3>
<PRE>
public void <B>setCommandline</B>(java.lang.String[]&nbsp;commandline)</PRE>
<DL>
<DD>Sets the commandline of the subprocess to launch.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>commandline</CODE> - the commandline of the subprocess to launch.</DL>
</DD>
</DL>
<HR>

<A NAME="setNewenvironment(boolean)"><!-- --></A><H3>
setNewenvironment</H3>
<PRE>
public void <B>setNewenvironment</B>(boolean&nbsp;newenv)</PRE>
<DL>
<DD>Set whether to propagate the default environment or not.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>newenv</CODE> - whether to propagate the process environment.</DL>
</DD>
</DL>
<HR>

<A NAME="getEnvironment()"><!-- --></A><H3>
getEnvironment</H3>
<PRE>
public java.lang.String[] <B>getEnvironment</B>()</PRE>
<DL>
<DD>Returns the environment used to create a subprocess.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the environment used to create a subprocess.</DL>
</DD>
</DL>
<HR>

<A NAME="setEnvironment(java.lang.String[])"><!-- --></A><H3>
setEnvironment</H3>
<PRE>
public void <B>setEnvironment</B>(java.lang.String[]&nbsp;env)</PRE>
<DL>
<DD>Sets the environment variables for the subprocess to launch.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>env</CODE> - array of Strings, each element of which has
 an environment variable settings in format <em>key=value</em>.</DL>
</DD>
</DL>
<HR>

<A NAME="setWorkingDirectory(java.io.File)"><!-- --></A><H3>
setWorkingDirectory</H3>
<PRE>
public void <B>setWorkingDirectory</B>(java.io.File&nbsp;wd)</PRE>
<DL>
<DD>Sets the working directory of the process to execute.

 <p>This is emulated using the antRun scripts unless the OS is
 Windows NT in which case a cmd.exe is spawned,
 or MRJ and setting Employee.dir works, or JDK 1.3 and there is
 official support in java.lang.Runtime.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>wd</CODE> - the working directory of the process.</DL>
</DD>
</DL>
<HR>

<A NAME="getWorkingDirectory()"><!-- --></A><H3>
getWorkingDirectory</H3>
<PRE>
public java.io.File <B>getWorkingDirectory</B>()</PRE>
<DL>
<DD>Return the working directory.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the directory as a File.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setAntRun(org.apache.tools.ant.Project)"><!-- --></A><H3>
setAntRun</H3>
<PRE>
public void <B>setAntRun</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)
               throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Set the name of the antRun script using the project's value.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the current project.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - not clear when it is going to throw an exception, but
 it is the method's signature.</DL>
</DD>
</DL>
<HR>

<A NAME="setVMLauncher(boolean)"><!-- --></A><H3>
setVMLauncher</H3>
<PRE>
public void <B>setVMLauncher</B>(boolean&nbsp;useVMLauncher)</PRE>
<DL>
<DD>Launch this execution through the VM, where possible, rather than through
 the OS's shell. In some cases and operating systems using the shell will
 allow the shell to perform additional processing such as associating an
 executable with a script, etc.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>useVMLauncher</CODE> - true if exec should launch through the VM,
                   false if the shell should be used to launch the
                   command.</DL>
</DD>
</DL>
<HR>

<A NAME="launch(org.apache.tools.ant.Project, java.lang.String[], java.lang.String[], java.io.File, boolean)"><!-- --></A><H3>
launch</H3>
<PRE>
public static java.lang.Process <B>launch</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                                       java.lang.String[]&nbsp;command,
                                       java.lang.String[]&nbsp;env,
                                       java.io.File&nbsp;dir,
                                       boolean&nbsp;useVM)
                                throws java.io.IOException</PRE>
<DL>
<DD>Creates a process that runs a command.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the Project, only used for logging purposes, may be null.<DD><CODE>command</CODE> - the command to run.<DD><CODE>env</CODE> - the environment for the command.<DD><CODE>dir</CODE> - the working directory for the command.<DD><CODE>useVM</CODE> - use the built-in exec command for JDK 1.3 if available.
<DT><B>Returns:</B><DD>the process started.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - forwarded from the particular launcher used.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public int <B>execute</B>()
            throws java.io.IOException</PRE>
<DL>
<DD>Runs a process defined by the command line and returns its exit status.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the exit status of the subprocess or <code>INVALID</code>.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - The exception is thrown, if launching
            of the subprocess failed.</DL>
</DD>
</DL>
<HR>

<A NAME="spawn()"><!-- --></A><H3>
spawn</H3>
<PRE>
public void <B>spawn</B>()
           throws java.io.IOException</PRE>
<DL>
<DD>Starts a process defined by the command line.
 Ant will not wait for this process, nor log its output.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - The exception is thrown, if launching
            of the subprocess failed.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="waitFor(java.lang.Process)"><!-- --></A><H3>
waitFor</H3>
<PRE>
protected void <B>waitFor</B>(java.lang.Process&nbsp;process)</PRE>
<DL>
<DD>Wait for a given process.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>process</CODE> - the process one wants to wait for.</DL>
</DD>
</DL>
<HR>

<A NAME="setExitValue(int)"><!-- --></A><H3>
setExitValue</H3>
<PRE>
protected void <B>setExitValue</B>(int&nbsp;value)</PRE>
<DL>
<DD>Set the exit value.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - exit value of the process.</DL>
</DD>
</DL>
<HR>

<A NAME="getExitValue()"><!-- --></A><H3>
getExitValue</H3>
<PRE>
public int <B>getExitValue</B>()</PRE>
<DL>
<DD>Query the exit value of the process.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the exit value or Execute.INVALID if no exit value has
 been received.</DL>
</DD>
</DL>
<HR>

<A NAME="isFailure(int)"><!-- --></A><H3>
isFailure</H3>
<PRE>
public static boolean <B>isFailure</B>(int&nbsp;exitValue)</PRE>
<DL>
<DD>Checks whether <code>exitValue</code> signals a failure on the current
 system (OS specific).

 <p><b>Note</b> that this method relies on the conventions of
 the OS, it will return false results if the application you are
 running doesn't follow these conventions.  One notable
 exception is the Java VM provided by HP for OpenVMS - it will
 return 0 if successful (like on any other platform), but this
 signals a failure on OpenVMS.  So if you execute a new Java VM
 on OpenVMS, you cannot trust this method.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>exitValue</CODE> - the exit value (return code) to be checked.
<DT><B>Returns:</B><DD><code>true</code> if <code>exitValue</code> signals a failure.</DL>
</DD>
</DL>
<HR>

<A NAME="isFailure()"><!-- --></A><H3>
isFailure</H3>
<PRE>
public boolean <B>isFailure</B>()</PRE>
<DL>
<DD>Did this execute return in a failure.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true if and only if the exit code is interpreted as a failure<DT><B>Since:</B></DT>
  <DD>Ant1.7</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html#isFailure(int)"><CODE>isFailure(int)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="killedProcess()"><!-- --></A><H3>
killedProcess</H3>
<PRE>
public boolean <B>killedProcess</B>()</PRE>
<DL>
<DD>Test for an untimely death of the process.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true if a watchdog had to kill the process.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="runCommand(org.apache.tools.ant.Task, java.lang.String[])"><!-- --></A><H3>
runCommand</H3>
<PRE>
public static void <B>runCommand</B>(<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task,
                              java.lang.String[]&nbsp;cmdline)
                       throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>A utility method that runs an external command.  Writes the output and
 error streams of the command to the project log.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>task</CODE> - The task that the command is part of.  Used for logging<DD><CODE>cmdline</CODE> - The command to execute.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the command does not exit successfully.</DL>
</DD>
</DL>
<HR>

<A NAME="closeStreams(java.lang.Process)"><!-- --></A><H3>
closeStreams</H3>
<PRE>
public static void <B>closeStreams</B>(java.lang.Process&nbsp;process)</PRE>
<DL>
<DD>Close the streams belonging to the given Process.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>process</CODE> - the <code>Process</code>.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Execute.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Execute.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
