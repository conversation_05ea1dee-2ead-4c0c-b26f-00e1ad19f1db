<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:34 EST 2006 -->
<TITLE>
TarUtils (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.tar.TarUtils class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="TarUtils (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarOutputStream.html" title="class in org.apache.tools.tar"><B>PREV CLASS</B></A>&nbsp;
&nbsp;NEXT CLASS</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/tar/TarUtils.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="TarUtils.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.tar</FONT>
<BR>
Class TarUtils</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.tar.TarUtils</B>
</PRE>
<HR>
<DL>
<DT><PRE>public class <B>TarUtils</B><DT>extends java.lang.Object</DL>
</PRE>

<P>
This class provides static utility methods to work with byte streams.
<P>

<P>
<HR>

<P>

<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarUtils.html#TarUtils()">TarUtils</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarUtils.html#computeCheckSum(byte[])">computeCheckSum</A></B>(byte[]&nbsp;buf)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Compute the checksum of a tar entry header.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarUtils.html#getCheckSumOctalBytes(long, byte[], int, int)">getCheckSumOctalBytes</A></B>(long&nbsp;value,
                      byte[]&nbsp;buf,
                      int&nbsp;offset,
                      int&nbsp;length)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Parse the checksum octal integer from a header buffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarUtils.html#getLongOctalBytes(long, byte[], int, int)">getLongOctalBytes</A></B>(long&nbsp;value,
                  byte[]&nbsp;buf,
                  int&nbsp;offset,
                  int&nbsp;length)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Parse an octal long integer from a header buffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarUtils.html#getNameBytes(java.lang.StringBuffer, byte[], int, int)">getNameBytes</A></B>(java.lang.StringBuffer&nbsp;name,
             byte[]&nbsp;buf,
             int&nbsp;offset,
             int&nbsp;length)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Determine the number of bytes in an entry name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarUtils.html#getOctalBytes(long, byte[], int, int)">getOctalBytes</A></B>(long&nbsp;value,
              byte[]&nbsp;buf,
              int&nbsp;offset,
              int&nbsp;length)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Parse an octal integer from a header buffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.StringBuffer</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarUtils.html#parseName(byte[], int, int)">parseName</A></B>(byte[]&nbsp;header,
          int&nbsp;offset,
          int&nbsp;length)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Parse an entry name from a header buffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarUtils.html#parseOctal(byte[], int, int)">parseOctal</A></B>(byte[]&nbsp;header,
           int&nbsp;offset,
           int&nbsp;length)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Parse an octal string from a header buffer.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="TarUtils()"><!-- --></A><H3>
TarUtils</H3>
<PRE>
public <B>TarUtils</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="parseOctal(byte[], int, int)"><!-- --></A><H3>
parseOctal</H3>
<PRE>
public static long <B>parseOctal</B>(byte[]&nbsp;header,
                              int&nbsp;offset,
                              int&nbsp;length)</PRE>
<DL>
<DD>Parse an octal string from a header buffer. This is used for the
 file permission mode value.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>header</CODE> - The header buffer from which to parse.<DD><CODE>offset</CODE> - The offset into the buffer from which to parse.<DD><CODE>length</CODE> - The number of header bytes to parse.
<DT><B>Returns:</B><DD>The long value of the octal string.</DL>
</DD>
</DL>
<HR>

<A NAME="parseName(byte[], int, int)"><!-- --></A><H3>
parseName</H3>
<PRE>
public static java.lang.StringBuffer <B>parseName</B>(byte[]&nbsp;header,
                                               int&nbsp;offset,
                                               int&nbsp;length)</PRE>
<DL>
<DD>Parse an entry name from a header buffer.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>header</CODE> - The header buffer from which to parse.<DD><CODE>offset</CODE> - The offset into the buffer from which to parse.<DD><CODE>length</CODE> - The number of header bytes to parse.
<DT><B>Returns:</B><DD>The header's entry name.</DL>
</DD>
</DL>
<HR>

<A NAME="getNameBytes(java.lang.StringBuffer, byte[], int, int)"><!-- --></A><H3>
getNameBytes</H3>
<PRE>
public static int <B>getNameBytes</B>(java.lang.StringBuffer&nbsp;name,
                               byte[]&nbsp;buf,
                               int&nbsp;offset,
                               int&nbsp;length)</PRE>
<DL>
<DD>Determine the number of bytes in an entry name.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - The header name from which to parse.<DD><CODE>buf</CODE> - The buffer from which to parse.<DD><CODE>offset</CODE> - The offset into the buffer from which to parse.<DD><CODE>length</CODE> - The number of header bytes to parse.
<DT><B>Returns:</B><DD>The number of bytes in a header's entry name.</DL>
</DD>
</DL>
<HR>

<A NAME="getOctalBytes(long, byte[], int, int)"><!-- --></A><H3>
getOctalBytes</H3>
<PRE>
public static int <B>getOctalBytes</B>(long&nbsp;value,
                                byte[]&nbsp;buf,
                                int&nbsp;offset,
                                int&nbsp;length)</PRE>
<DL>
<DD>Parse an octal integer from a header buffer.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - The header value<DD><CODE>buf</CODE> - The buffer from which to parse.<DD><CODE>offset</CODE> - The offset into the buffer from which to parse.<DD><CODE>length</CODE> - The number of header bytes to parse.
<DT><B>Returns:</B><DD>The integer value of the octal bytes.</DL>
</DD>
</DL>
<HR>

<A NAME="getLongOctalBytes(long, byte[], int, int)"><!-- --></A><H3>
getLongOctalBytes</H3>
<PRE>
public static int <B>getLongOctalBytes</B>(long&nbsp;value,
                                    byte[]&nbsp;buf,
                                    int&nbsp;offset,
                                    int&nbsp;length)</PRE>
<DL>
<DD>Parse an octal long integer from a header buffer.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - The header value<DD><CODE>buf</CODE> - The buffer from which to parse.<DD><CODE>offset</CODE> - The offset into the buffer from which to parse.<DD><CODE>length</CODE> - The number of header bytes to parse.
<DT><B>Returns:</B><DD>The long value of the octal bytes.</DL>
</DD>
</DL>
<HR>

<A NAME="getCheckSumOctalBytes(long, byte[], int, int)"><!-- --></A><H3>
getCheckSumOctalBytes</H3>
<PRE>
public static int <B>getCheckSumOctalBytes</B>(long&nbsp;value,
                                        byte[]&nbsp;buf,
                                        int&nbsp;offset,
                                        int&nbsp;length)</PRE>
<DL>
<DD>Parse the checksum octal integer from a header buffer.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - The header value<DD><CODE>buf</CODE> - The buffer from which to parse.<DD><CODE>offset</CODE> - The offset into the buffer from which to parse.<DD><CODE>length</CODE> - The number of header bytes to parse.
<DT><B>Returns:</B><DD>The integer value of the entry's checksum.</DL>
</DD>
</DL>
<HR>

<A NAME="computeCheckSum(byte[])"><!-- --></A><H3>
computeCheckSum</H3>
<PRE>
public static long <B>computeCheckSum</B>(byte[]&nbsp;buf)</PRE>
<DL>
<DD>Compute the checksum of a tar entry header.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>buf</CODE> - The tar entry's header buffer.
<DT><B>Returns:</B><DD>The computed checksum.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarOutputStream.html" title="class in org.apache.tools.tar"><B>PREV CLASS</B></A>&nbsp;
&nbsp;NEXT CLASS</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/tar/TarUtils.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="TarUtils.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
