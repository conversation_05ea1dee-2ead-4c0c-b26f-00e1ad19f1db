<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:20 EST 2006 -->
<TITLE>
FileScanner (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.FileScanner interface">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="FileScanner (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/ExitStatusException.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/FileScanner.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="FileScanner.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Interface FileScanner</H2>
<DL>
<DT><B>All Known Implementing Classes:</B> <DD><A HREF="../../../../org/apache/tools/ant/types/ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</A>, <A HREF="../../../../org/apache/tools/ant/types/optional/depend/DependScanner.html" title="class in org.apache.tools.ant.types.optional.depend">DependScanner</A>, <A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.FTPDirectoryScanner.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPDirectoryScanner</A>, <A HREF="../../../../org/apache/tools/ant/types/TarScanner.html" title="class in org.apache.tools.ant.types">TarScanner</A>, <A HREF="../../../../org/apache/tools/ant/types/ZipScanner.html" title="class in org.apache.tools.ant.types">ZipScanner</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public interface <B>FileScanner</B></DL>
</PRE>

<P>
An interface used to describe the actions required of any type of
 directory scanner.
<P>

<P>
<HR>

<P>

<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/FileScanner.html#addDefaultExcludes()">addDefaultExcludes</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds default exclusions to the current exclusions set.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/FileScanner.html#getBasedir()">getBasedir</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the base directory to be scanned.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/FileScanner.html#getExcludedDirectories()">getExcludedDirectories</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the names of the directories which matched at least one of the
 include patterns and at least one of the exclude patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/FileScanner.html#getExcludedFiles()">getExcludedFiles</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the names of the files which matched at least one of the
 include patterns and at least one of the exclude patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/FileScanner.html#getIncludedDirectories()">getIncludedDirectories</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the names of the directories which matched at least one of the
 include patterns and none of the exclude patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/FileScanner.html#getIncludedFiles()">getIncludedFiles</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the names of the files which matched at least one of the
 include patterns and none of the exclude patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/FileScanner.html#getNotIncludedDirectories()">getNotIncludedDirectories</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the names of the directories which matched none of the include
 patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/FileScanner.html#getNotIncludedFiles()">getNotIncludedFiles</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the names of the files which matched none of the include
 patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/FileScanner.html#scan()">scan</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Scans the base directory for files which match at least one include
 pattern and don't match any exclude patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/FileScanner.html#setBasedir(java.io.File)">setBasedir</A></B>(java.io.File&nbsp;basedir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the base directory to be scanned.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/FileScanner.html#setBasedir(java.lang.String)">setBasedir</A></B>(java.lang.String&nbsp;basedir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the base directory to be scanned.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/FileScanner.html#setCaseSensitive(boolean)">setCaseSensitive</A></B>(boolean&nbsp;isCaseSensitive)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets whether or not the file system should be regarded as case sensitive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/FileScanner.html#setExcludes(java.lang.String[])">setExcludes</A></B>(java.lang.String[]&nbsp;excludes)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the list of exclude patterns to use.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/FileScanner.html#setIncludes(java.lang.String[])">setIncludes</A></B>(java.lang.String[]&nbsp;includes)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the list of include patterns to use.</TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="addDefaultExcludes()"><!-- --></A><H3>
addDefaultExcludes</H3>
<PRE>
void <B>addDefaultExcludes</B>()</PRE>
<DL>
<DD>Adds default exclusions to the current exclusions set.
<P>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="getBasedir()"><!-- --></A><H3>
getBasedir</H3>
<PRE>
java.io.File <B>getBasedir</B>()</PRE>
<DL>
<DD>Returns the base directory to be scanned.
 This is the directory which is scanned recursively.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the base directory to be scanned</DL>
</DD>
</DL>
<HR>

<A NAME="getExcludedDirectories()"><!-- --></A><H3>
getExcludedDirectories</H3>
<PRE>
java.lang.String[] <B>getExcludedDirectories</B>()</PRE>
<DL>
<DD>Returns the names of the directories which matched at least one of the
 include patterns and at least one of the exclude patterns.
 The names are relative to the base directory.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the names of the directories which matched at least one of the
 include patterns and at least one of the exclude patterns.</DL>
</DD>
</DL>
<HR>

<A NAME="getExcludedFiles()"><!-- --></A><H3>
getExcludedFiles</H3>
<PRE>
java.lang.String[] <B>getExcludedFiles</B>()</PRE>
<DL>
<DD>Returns the names of the files which matched at least one of the
 include patterns and at least one of the exclude patterns.
 The names are relative to the base directory.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the names of the files which matched at least one of the
         include patterns and at least one of the exclude patterns.</DL>
</DD>
</DL>
<HR>

<A NAME="getIncludedDirectories()"><!-- --></A><H3>
getIncludedDirectories</H3>
<PRE>
java.lang.String[] <B>getIncludedDirectories</B>()</PRE>
<DL>
<DD>Returns the names of the directories which matched at least one of the
 include patterns and none of the exclude patterns.
 The names are relative to the base directory.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the names of the directories which matched at least one of the
 include patterns and none of the exclude patterns.</DL>
</DD>
</DL>
<HR>

<A NAME="getIncludedFiles()"><!-- --></A><H3>
getIncludedFiles</H3>
<PRE>
java.lang.String[] <B>getIncludedFiles</B>()</PRE>
<DL>
<DD>Returns the names of the files which matched at least one of the
 include patterns and none of the exclude patterns.
 The names are relative to the base directory.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the names of the files which matched at least one of the
         include patterns and none of the exclude patterns.</DL>
</DD>
</DL>
<HR>

<A NAME="getNotIncludedDirectories()"><!-- --></A><H3>
getNotIncludedDirectories</H3>
<PRE>
java.lang.String[] <B>getNotIncludedDirectories</B>()</PRE>
<DL>
<DD>Returns the names of the directories which matched none of the include
 patterns. The names are relative to the base directory.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the names of the directories which matched none of the include
 patterns.</DL>
</DD>
</DL>
<HR>

<A NAME="getNotIncludedFiles()"><!-- --></A><H3>
getNotIncludedFiles</H3>
<PRE>
java.lang.String[] <B>getNotIncludedFiles</B>()</PRE>
<DL>
<DD>Returns the names of the files which matched none of the include
 patterns. The names are relative to the base directory.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the names of the files which matched none of the include
         patterns.</DL>
</DD>
</DL>
<HR>

<A NAME="scan()"><!-- --></A><H3>
scan</H3>
<PRE>
void <B>scan</B>()
          throws java.lang.IllegalStateException</PRE>
<DL>
<DD>Scans the base directory for files which match at least one include
 pattern and don't match any exclude patterns.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.lang.IllegalStateException</CODE> - if the base directory was set
            incorrectly (i.e. if it is <code>null</code>, doesn't exist,
            or isn't a directory).</DL>
</DD>
</DL>
<HR>

<A NAME="setBasedir(java.lang.String)"><!-- --></A><H3>
setBasedir</H3>
<PRE>
void <B>setBasedir</B>(java.lang.String&nbsp;basedir)</PRE>
<DL>
<DD>Sets the base directory to be scanned. This is the directory which is
 scanned recursively. All '/' and '\' characters should be replaced by
 <code>File.separatorChar</code>, so the separator used need not match
 <code>File.separatorChar</code>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>basedir</CODE> - The base directory to scan.
                Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setBasedir(java.io.File)"><!-- --></A><H3>
setBasedir</H3>
<PRE>
void <B>setBasedir</B>(java.io.File&nbsp;basedir)</PRE>
<DL>
<DD>Sets the base directory to be scanned. This is the directory which is
 scanned recursively.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>basedir</CODE> - The base directory for scanning.
                Should not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setExcludes(java.lang.String[])"><!-- --></A><H3>
setExcludes</H3>
<PRE>
void <B>setExcludes</B>(java.lang.String[]&nbsp;excludes)</PRE>
<DL>
<DD>Sets the list of exclude patterns to use.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>excludes</CODE> - A list of exclude patterns.
                 May be <code>null</code>, indicating that no files
                 should be excluded. If a non-<code>null</code> list is
                 given, all elements must be non-<code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setIncludes(java.lang.String[])"><!-- --></A><H3>
setIncludes</H3>
<PRE>
void <B>setIncludes</B>(java.lang.String[]&nbsp;includes)</PRE>
<DL>
<DD>Sets the list of include patterns to use.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>includes</CODE> - A list of include patterns.
                 May be <code>null</code>, indicating that all files
                 should be included. If a non-<code>null</code>
                 list is given, all elements must be
 non-<code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setCaseSensitive(boolean)"><!-- --></A><H3>
setCaseSensitive</H3>
<PRE>
void <B>setCaseSensitive</B>(boolean&nbsp;isCaseSensitive)</PRE>
<DL>
<DD>Sets whether or not the file system should be regarded as case sensitive.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>isCaseSensitive</CODE> - whether or not the file system should be
                        regarded as a case sensitive one</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/ExitStatusException.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/FileScanner.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="FileScanner.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
