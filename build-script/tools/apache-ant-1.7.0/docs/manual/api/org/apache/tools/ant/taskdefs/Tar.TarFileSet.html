<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:24 EST 2006 -->
<TITLE>
Tar.TarFileSet (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.Tar.TarFileSet class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Tar.TarFileSet (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarCompressionMethod.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarLongFileMode.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Tar.TarFileSet.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Tar.TarFileSet.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.types.ArchiveFileSet">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class Tar.TarFileSet</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.AbstractFileSet</A>
              <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.FileSet</A>
                  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.ArchiveFileSet</A>
                      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.TarFileSet</A>
                          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.Tar.TarFileSet</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable, <A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>, <A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></DD>
</DL>
<DL>
<DT><B>Enclosing class:</B><DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.html" title="class in org.apache.tools.ant.taskdefs">Tar</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public static class <B>Tar.TarFileSet</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html" title="class in org.apache.tools.ant.types">TarFileSet</A></DL>
</PRE>

<P>
This is a FileSet with the option to specify permissions
 and other attributes.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.types.ArchiveFileSet"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#DEFAULT_DIR_MODE">DEFAULT_DIR_MODE</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#DEFAULT_FILE_MODE">DEFAULT_FILE_MODE</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.types.DataType"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/DataType.html#checked">checked</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#ref">ref</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarFileSet.html#Tar.TarFileSet()">Tar.TarFileSet</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new <code>TarFileSet</code> instance.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarFileSet.html#Tar.TarFileSet(org.apache.tools.ant.types.FileSet)">Tar.TarFileSet</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;fileset)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new <code>TarFileSet</code> instance.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarFileSet.html#getFiles(org.apache.tools.ant.Project)">getFiles</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get a list of files and directories specified in the fileset.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarFileSet.html#getMode()">getMode</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarFileSet.html#getPreserveLeadingSlashes()">getPreserveLeadingSlashes</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarFileSet.html#setMode(java.lang.String)">setMode</A></B>(java.lang.String&nbsp;octalString)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A 3 digit octal string, specify the Employee, group and
 other modes in the standard Unix fashion;
 optional, default=0644</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarFileSet.html#setPreserveLeadingSlashes(boolean)">setPreserveLeadingSlashes</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Flag to indicates whether leading `/'s should
 be preserved in the file names.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.types.TarFileSet"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html" title="class in org.apache.tools.ant.types">TarFileSet</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#configureFileSet(org.apache.tools.ant.types.ArchiveFileSet)">configureFileSet</A>, <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#getGid()">getGid</A>, <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#getGroup()">getGroup</A>, <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#getRef(org.apache.tools.ant.Project)">getRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#getUid()">getUid</A>, <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#getUserName()">getUserName</A>, <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#hasGroupBeenSet()">hasGroupBeenSet</A>, <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#hasGroupIdBeenSet()">hasGroupIdBeenSet</A>, <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#hasUserIdBeenSet()">hasUserIdBeenSet</A>, <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#hasUserNameBeenSet()">hasUserNameBeenSet</A>, <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#newArchiveScanner()">newArchiveScanner</A>, <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#setGid(int)">setGid</A>, <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#setGroup(java.lang.String)">setGroup</A>, <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</A>, <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#setUid(int)">setUid</A>, <A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html#setUserName(java.lang.String)">setUserName</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.types.ArchiveFileSet"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#addConfigured(org.apache.tools.ant.types.ResourceCollection)">addConfigured</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getDirectoryScanner(org.apache.tools.ant.Project)">getDirectoryScanner</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getDirMode()">getDirMode</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getDirMode(org.apache.tools.ant.Project)">getDirMode</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getFileMode()">getFileMode</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getFileMode(org.apache.tools.ant.Project)">getFileMode</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getFullpath()">getFullpath</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getFullpath(org.apache.tools.ant.Project)">getFullpath</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getPrefix()">getPrefix</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getPrefix(org.apache.tools.ant.Project)">getPrefix</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getSrc()">getSrc</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#getSrc(org.apache.tools.ant.Project)">getSrc</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#hasDirModeBeenSet()">hasDirModeBeenSet</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#hasFileModeBeenSet()">hasFileModeBeenSet</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#integerSetDirMode(int)">integerSetDirMode</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#integerSetFileMode(int)">integerSetFileMode</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#isFilesystemOnly()">isFilesystemOnly</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#iterator()">iterator</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#setDir(java.io.File)">setDir</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#setDirMode(java.lang.String)">setDirMode</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#setFileMode(java.lang.String)">setFileMode</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#setFullpath(java.lang.String)">setFullpath</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#setPrefix(java.lang.String)">setPrefix</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#setSrc(java.io.File)">setSrc</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#setSrcResource(org.apache.tools.ant.types.Resource)">setSrcResource</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#size()">size</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html#toString()">toString</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.types.AbstractFileSet"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#appendExcludes(java.lang.String[])">appendExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#appendIncludes(java.lang.String[])">appendIncludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#createExclude()">createExclude</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#createExcludesFile()">createExcludesFile</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#createInclude()">createInclude</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#createIncludesFile()">createIncludesFile</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#createPatternSet()">createPatternSet</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getDefaultexcludes()">getDefaultexcludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getDir()">getDir</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getDir(org.apache.tools.ant.Project)">getDir</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getDirectoryScanner()">getDirectoryScanner</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#hasPatterns()">hasPatterns</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#hasSelectors()">hasSelectors</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#isCaseSensitive()">isCaseSensitive</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#isFollowSymlinks()">isFollowSymlinks</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#mergeExcludes(org.apache.tools.ant.Project)">mergeExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#mergeIncludes(org.apache.tools.ant.Project)">mergeIncludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#mergePatterns(org.apache.tools.ant.Project)">mergePatterns</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#selectorCount()">selectorCount</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#selectorElements()">selectorElements</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setCaseSensitive(boolean)">setCaseSensitive</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setDefaultexcludes(boolean)">setDefaultexcludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setExcludes(java.lang.String)">setExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setExcludesfile(java.io.File)">setExcludesfile</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setFile(java.io.File)">setFile</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setFollowSymlinks(boolean)">setFollowSymlinks</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setIncludes(java.lang.String)">setIncludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setIncludesfile(java.io.File)">setIncludesfile</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setupDirectoryScanner(org.apache.tools.ant.FileScanner)">setupDirectoryScanner</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setupDirectoryScanner(org.apache.tools.ant.FileScanner, org.apache.tools.ant.Project)">setupDirectoryScanner</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.types.DataType"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/DataType.html#checkAttributesAllowed()">checkAttributesAllowed</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#checkChildrenAllowed()">checkChildrenAllowed</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#circularReference()">circularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference()">dieOnCircularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference(java.util.Stack, org.apache.tools.ant.Project)">dieOnCircularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef()">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(java.lang.Class, java.lang.String)">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(java.lang.Class, java.lang.String, org.apache.tools.ant.Project)">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getDataTypeName()">getDataTypeName</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getRefid()">getRefid</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType, java.util.Stack, org.apache.tools.ant.Project)">invokeCircularReferenceCheck</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#isChecked()">isChecked</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#isReference()">isReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#noChildrenAllowed()">noChildrenAllowed</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#setChecked(boolean)">setChecked</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#tooManyAttributes()">tooManyAttributes</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Tar.TarFileSet(org.apache.tools.ant.types.FileSet)"><!-- --></A><H3>
Tar.TarFileSet</H3>
<PRE>
public <B>Tar.TarFileSet</B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;fileset)</PRE>
<DL>
<DD>Creates a new <code>TarFileSet</code> instance.
 Using a fileset as a constructor argument.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>fileset</CODE> - a <code>FileSet</code> value</DL>
</DL>
<HR>

<A NAME="Tar.TarFileSet()"><!-- --></A><H3>
Tar.TarFileSet</H3>
<PRE>
public <B>Tar.TarFileSet</B>()</PRE>
<DL>
<DD>Creates a new <code>TarFileSet</code> instance.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="getFiles(org.apache.tools.ant.Project)"><!-- --></A><H3>
getFiles</H3>
<PRE>
public java.lang.String[] <B>getFiles</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</PRE>
<DL>
<DD>Get a list of files and directories specified in the fileset.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the current project.
<DT><B>Returns:</B><DD>a list of file and directory names, relative to
    the baseDir for the project.</DL>
</DD>
</DL>
<HR>

<A NAME="setMode(java.lang.String)"><!-- --></A><H3>
setMode</H3>
<PRE>
public void <B>setMode</B>(java.lang.String&nbsp;octalString)</PRE>
<DL>
<DD>A 3 digit octal string, specify the Employee, group and
 other modes in the standard Unix fashion;
 optional, default=0644
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>octalString</CODE> - a 3 digit octal string.</DL>
</DD>
</DL>
<HR>

<A NAME="getMode()"><!-- --></A><H3>
getMode</H3>
<PRE>
public int <B>getMode</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the current mode.</DL>
</DD>
</DL>
<HR>

<A NAME="setPreserveLeadingSlashes(boolean)"><!-- --></A><H3>
setPreserveLeadingSlashes</H3>
<PRE>
public void <B>setPreserveLeadingSlashes</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Flag to indicates whether leading `/'s should
 be preserved in the file names.
 Optional, default is <code>false</code>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - the leading slashes flag.</DL>
</DD>
</DL>
<HR>

<A NAME="getPreserveLeadingSlashes()"><!-- --></A><H3>
getPreserveLeadingSlashes</H3>
<PRE>
public boolean <B>getPreserveLeadingSlashes</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the leading slashes flag.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarCompressionMethod.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarLongFileMode.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Tar.TarFileSet.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Tar.TarFileSet.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.types.ArchiveFileSet">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
