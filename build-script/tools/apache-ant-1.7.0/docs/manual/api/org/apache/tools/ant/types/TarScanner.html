<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:31 EST 2006 -->
<TITLE>
TarScanner (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.types.TarScanner class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="TarScanner (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html" title="class in org.apache.tools.ant.types"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/TimeComparison.html" title="class in org.apache.tools.ant.types"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/types/TarScanner.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="TarScanner.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.types.ArchiveScanner">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.types</FONT>
<BR>
Class TarScanner</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html" title="class in org.apache.tools.ant">org.apache.tools.ant.DirectoryScanner</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.ArchiveScanner</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.types.TarScanner</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD><A HREF="../../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A>, <A HREF="../../../../../org/apache/tools/ant/types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</A>, <A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>TarScanner</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</A></DL>
</PRE>

<P>
Scans tar archives for resources.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.types.ArchiveScanner"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html#srcFile">srcFile</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.DirectoryScanner"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#basedir">basedir</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#DEFAULTEXCLUDES">DEFAULTEXCLUDES</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#dirsDeselected">dirsDeselected</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#dirsExcluded">dirsExcluded</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#dirsIncluded">dirsIncluded</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#dirsNotIncluded">dirsNotIncluded</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#everythingIncluded">everythingIncluded</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#excludes">excludes</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#filesDeselected">filesDeselected</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#filesExcluded">filesExcluded</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#filesIncluded">filesIncluded</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#filesNotIncluded">filesNotIncluded</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#haveSlowResults">haveSlowResults</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#includes">includes</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#isCaseSensitive">isCaseSensitive</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#selectors">selectors</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/TarScanner.html#TarScanner()">TarScanner</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/TarScanner.html#fillMapsFromArchive(org.apache.tools.ant.types.Resource, java.lang.String, java.util.Map, java.util.Map, java.util.Map, java.util.Map)">fillMapsFromArchive</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>&nbsp;src,
                    java.lang.String&nbsp;encoding,
                    java.util.Map&nbsp;fileEntries,
                    java.util.Map&nbsp;matchFileEntries,
                    java.util.Map&nbsp;dirEntries,
                    java.util.Map&nbsp;matchDirEntries)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Fills the file and directory maps with resources read from the
 archive.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.types.ArchiveScanner"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html#getIncludedDirectories()">getIncludedDirectories</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html#getIncludedDirsCount()">getIncludedDirsCount</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html#getIncludedFiles()">getIncludedFiles</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html#getIncludedFilesCount()">getIncludedFilesCount</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html#getResource(java.lang.String)">getResource</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html#init()">init</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html#match(java.lang.String)">match</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html#scan()">scan</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html#setEncoding(java.lang.String)">setEncoding</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html#setSrc(java.io.File)">setSrc</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html#setSrc(org.apache.tools.ant.types.Resource)">setSrc</A>, <A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html#trimSeparator(java.lang.String)">trimSeparator</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.DirectoryScanner"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#addDefaultExclude(java.lang.String)">addDefaultExclude</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#addDefaultExcludes()">addDefaultExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#addExcludes(java.lang.String[])">addExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#clearResults()">clearResults</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#couldHoldIncluded(java.lang.String)">couldHoldIncluded</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#getBasedir()">getBasedir</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#getDefaultExcludes()">getDefaultExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#getDeselectedDirectories()">getDeselectedDirectories</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#getDeselectedFiles()">getDeselectedFiles</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#getExcludedDirectories()">getExcludedDirectories</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#getExcludedFiles()">getExcludedFiles</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#getNotIncludedDirectories()">getNotIncludedDirectories</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#getNotIncludedFiles()">getNotIncludedFiles</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#isCaseSensitive()">isCaseSensitive</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#isEverythingIncluded()">isEverythingIncluded</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#isExcluded(java.lang.String)">isExcluded</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#isFollowSymlinks()">isFollowSymlinks</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#isIncluded(java.lang.String)">isIncluded</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#isSelected(java.lang.String, java.io.File)">isSelected</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#match(java.lang.String, java.lang.String)">match</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#match(java.lang.String, java.lang.String, boolean)">match</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#matchPath(java.lang.String, java.lang.String)">matchPath</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#matchPath(java.lang.String, java.lang.String, boolean)">matchPath</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#matchPatternStart(java.lang.String, java.lang.String)">matchPatternStart</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#matchPatternStart(java.lang.String, java.lang.String, boolean)">matchPatternStart</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#removeDefaultExclude(java.lang.String)">removeDefaultExclude</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#resetDefaultExcludes()">resetDefaultExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#scandir(java.io.File, java.lang.String, boolean)">scandir</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#setBasedir(java.io.File)">setBasedir</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#setBasedir(java.lang.String)">setBasedir</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#setCaseSensitive(boolean)">setCaseSensitive</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#setExcludes(java.lang.String[])">setExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#setFollowSymlinks(boolean)">setFollowSymlinks</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#setIncludes(java.lang.String[])">setIncludes</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#setSelectors(org.apache.tools.ant.types.selectors.FileSelector[])">setSelectors</A>, <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html#slowScan()">slowScan</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="TarScanner()"><!-- --></A><H3>
TarScanner</H3>
<PRE>
public <B>TarScanner</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="fillMapsFromArchive(org.apache.tools.ant.types.Resource, java.lang.String, java.util.Map, java.util.Map, java.util.Map, java.util.Map)"><!-- --></A><H3>
fillMapsFromArchive</H3>
<PRE>
protected void <B>fillMapsFromArchive</B>(<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>&nbsp;src,
                                   java.lang.String&nbsp;encoding,
                                   java.util.Map&nbsp;fileEntries,
                                   java.util.Map&nbsp;matchFileEntries,
                                   java.util.Map&nbsp;dirEntries,
                                   java.util.Map&nbsp;matchDirEntries)</PRE>
<DL>
<DD>Fills the file and directory maps with resources read from the
 archive.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html#fillMapsFromArchive(org.apache.tools.ant.types.Resource, java.lang.String, java.util.Map, java.util.Map, java.util.Map, java.util.Map)">fillMapsFromArchive</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/types/ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>src</CODE> - the archive to scan.<DD><CODE>encoding</CODE> - encoding used to encode file names inside the archive.<DD><CODE>fileEntries</CODE> - Map (name to resource) of non-directory
 resources found inside the archive.<DD><CODE>matchFileEntries</CODE> - Map (name to resource) of non-directory
 resources found inside the archive that matched all include
 patterns and didn't match any exclude patterns.<DD><CODE>dirEntries</CODE> - Map (name to resource) of directory
 resources found inside the archive.<DD><CODE>matchDirEntries</CODE> - Map (name to resource) of directory
 resources found inside the archive that matched all include
 patterns and didn't match any exclude patterns.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html" title="class in org.apache.tools.ant.types"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/TimeComparison.html" title="class in org.apache.tools.ant.types"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/types/TarScanner.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="TarScanner.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.types.ArchiveScanner">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
