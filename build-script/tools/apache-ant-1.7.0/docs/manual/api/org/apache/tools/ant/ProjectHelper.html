<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:20 EST 2006 -->
<TITLE>
ProjectHelper (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.ProjectHelper class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="ProjectHelper (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/PropertyHelper.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/ProjectHelper.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ProjectHelper.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class ProjectHelper</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.ProjectHelper</B>
</PRE>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../org/apache/tools/ant/helper/ProjectHelper2.html" title="class in org.apache.tools.ant.helper">ProjectHelper2</A>, <A HREF="../../../../org/apache/tools/ant/helper/ProjectHelperImpl.html" title="class in org.apache.tools.ant.helper">ProjectHelperImpl</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>ProjectHelper</B><DT>extends java.lang.Object</DL>
</PRE>

<P>
Configures a Project (complete with Targets and Tasks) based on
 a XML build file. It'll rely on a plugin to do the actual processing
 of the xml file.

 This class also provide static wrappers for common introspection.

 All helper plugins must provide backward compatibility with the
 original ant patterns, unless a different behavior is explicitly
 specified. For example, if namespace is used on the &lt;project&gt; tag
 the helper can expect the entire build file to be namespace-enabled.
 Namespaces or helper-specific tags can provide meta-information to
 the helper, allowing it to use new ( or different policies ).

 However, if no namespace is used the behavior should be exactly
 identical with the default helper.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#ANT_CORE_URI">ANT_CORE_URI</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The URI for ant name space</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#ANT_CURRENT_URI">ANT_CURRENT_URI</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The URI for antlib current definitions</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#ANT_TYPE">ANT_TYPE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Polymorphic attribute</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#ANTLIB_URI">ANTLIB_URI</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The URI for defined types/tasks - the format is antlib:<package></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#HELPER_PROPERTY">HELPER_PROPERTY</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Name of JVM system property which provides the name of the
 ProjectHelper class to use.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#PROJECTHELPER_REFERENCE">PROJECTHELPER_REFERENCE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;name of project helper reference that we add to a project</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#SERVICE_ID">SERVICE_ID</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The service identifier in jars which provide Project Helper
 implementations.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#ProjectHelper()">ProjectHelper</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Default constructor</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#addLocationToBuildException(org.apache.tools.ant.BuildException, org.apache.tools.ant.Location)">addLocationToBuildException</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A>&nbsp;ex,
                            <A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A>&nbsp;newLocation)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add location to build exception.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#addText(org.apache.tools.ant.Project, java.lang.Object, char[], int, int)">addText</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
        java.lang.Object&nbsp;target,
        char[]&nbsp;buf,
        int&nbsp;start,
        int&nbsp;count)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds the content of #PCDATA sections to an element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#addText(org.apache.tools.ant.Project, java.lang.Object, java.lang.String)">addText</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
        java.lang.Object&nbsp;target,
        java.lang.String&nbsp;text)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds the content of #PCDATA sections to an element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#configure(java.lang.Object, org.xml.sax.AttributeList, org.apache.tools.ant.Project)">configure</A></B>(java.lang.Object&nbsp;target,
          org.xml.sax.AttributeList&nbsp;attrs,
          <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             Use IntrospectionHelper for each property.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#configureProject(org.apache.tools.ant.Project, java.io.File)">configureProject</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                 java.io.File&nbsp;buildFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Configures the project with the contents of the specified XML file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#extractNameFromComponentName(java.lang.String)">extractNameFromComponentName</A></B>(java.lang.String&nbsp;componentName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;extract the element name from a component name</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#extractUriFromComponentName(java.lang.String)">extractUriFromComponentName</A></B>(java.lang.String&nbsp;componentName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;extract a uri from a component name</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#genComponentName(java.lang.String, java.lang.String)">genComponentName</A></B>(java.lang.String&nbsp;uri,
                 java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Map a namespaced {uri,name} to an internal string format.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.ClassLoader</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#getContextClassLoader()">getContextClassLoader</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             Use LoaderUtils.getContextClassLoader()</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#getImportStack()">getImportStack</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;EXPERIMENTAL WILL_CHANGE
  Import stack.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="../../../../org/apache/tools/ant/ProjectHelper.html" title="class in org.apache.tools.ant">ProjectHelper</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#getProjectHelper()">getProjectHelper</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Discovers a project helper instance.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#parse(org.apache.tools.ant.Project, java.lang.Object)">parse</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
      java.lang.Object&nbsp;source)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Parses the project file, configuring the project as it goes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#parsePropertyString(java.lang.String, java.util.Vector, java.util.Vector)">parsePropertyString</A></B>(java.lang.String&nbsp;value,
                    java.util.Vector&nbsp;fragments,
                    java.util.Vector&nbsp;propertyRefs)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             Use PropertyHelper.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#replaceProperties(org.apache.tools.ant.Project, java.lang.String)">replaceProperties</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                  java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             Use project.replaceProperties().</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#replaceProperties(org.apache.tools.ant.Project, java.lang.String, java.util.Hashtable)">replaceProperties</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                  java.lang.String&nbsp;value,
                  java.util.Hashtable&nbsp;keys)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             Use PropertyHelper.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html#storeChild(org.apache.tools.ant.Project, java.lang.Object, java.lang.Object, java.lang.String)">storeChild</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
           java.lang.Object&nbsp;parent,
           java.lang.Object&nbsp;child,
           java.lang.String&nbsp;tag)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Stores a configured child element within its parent object.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ANT_CORE_URI"><!-- --></A><H3>
ANT_CORE_URI</H3>
<PRE>
public static final java.lang.String <B>ANT_CORE_URI</B></PRE>
<DL>
<DD>The URI for ant name space
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.ProjectHelper.ANT_CORE_URI">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ANT_CURRENT_URI"><!-- --></A><H3>
ANT_CURRENT_URI</H3>
<PRE>
public static final java.lang.String <B>ANT_CURRENT_URI</B></PRE>
<DL>
<DD>The URI for antlib current definitions
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.ProjectHelper.ANT_CURRENT_URI">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ANTLIB_URI"><!-- --></A><H3>
ANTLIB_URI</H3>
<PRE>
public static final java.lang.String <B>ANTLIB_URI</B></PRE>
<DL>
<DD>The URI for defined types/tasks - the format is antlib:<package>
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.ProjectHelper.ANTLIB_URI">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ANT_TYPE"><!-- --></A><H3>
ANT_TYPE</H3>
<PRE>
public static final java.lang.String <B>ANT_TYPE</B></PRE>
<DL>
<DD>Polymorphic attribute
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.ProjectHelper.ANT_TYPE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="HELPER_PROPERTY"><!-- --></A><H3>
HELPER_PROPERTY</H3>
<PRE>
public static final java.lang.String <B>HELPER_PROPERTY</B></PRE>
<DL>
<DD>Name of JVM system property which provides the name of the
 ProjectHelper class to use.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.ProjectHelper.HELPER_PROPERTY">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="SERVICE_ID"><!-- --></A><H3>
SERVICE_ID</H3>
<PRE>
public static final java.lang.String <B>SERVICE_ID</B></PRE>
<DL>
<DD>The service identifier in jars which provide Project Helper
 implementations.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.ProjectHelper.SERVICE_ID">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="PROJECTHELPER_REFERENCE"><!-- --></A><H3>
PROJECTHELPER_REFERENCE</H3>
<PRE>
public static final java.lang.String <B>PROJECTHELPER_REFERENCE</B></PRE>
<DL>
<DD>name of project helper reference that we add to a project
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.ProjectHelper.PROJECTHELPER_REFERENCE">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ProjectHelper()"><!-- --></A><H3>
ProjectHelper</H3>
<PRE>
public <B>ProjectHelper</B>()</PRE>
<DL>
<DD>Default constructor
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="configureProject(org.apache.tools.ant.Project, java.io.File)"><!-- --></A><H3>
configureProject</H3>
<PRE>
public static void <B>configureProject</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                                    java.io.File&nbsp;buildFile)
                             throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Configures the project with the contents of the specified XML file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - The project to configure. Must not be <code>null</code>.<DD><CODE>buildFile</CODE> - An XML file giving the project's configuration.
                  Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the configuration is invalid or cannot
                           be read</DL>
</DD>
</DL>
<HR>

<A NAME="getImportStack()"><!-- --></A><H3>
getImportStack</H3>
<PRE>
public java.util.Vector <B>getImportStack</B>()</PRE>
<DL>
<DD>EXPERIMENTAL WILL_CHANGE
  Import stack.
  Used to keep track of imported files. Error reporting should
  display the import path.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the stack of import source objects.</DL>
</DD>
</DL>
<HR>

<A NAME="parse(org.apache.tools.ant.Project, java.lang.Object)"><!-- --></A><H3>
parse</H3>
<PRE>
public void <B>parse</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                  java.lang.Object&nbsp;source)
           throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Parses the project file, configuring the project as it goes.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - The project for the resulting ProjectHelper to configure.
                Must not be <code>null</code>.<DD><CODE>source</CODE> - The source for XML configuration. A helper must support
               at least File, for backward compatibility. Helpers may
               support URL, InputStream, etc or specialized types.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the configuration is invalid or cannot
                           be read<DT><B>Since:</B></DT>
  <DD>Ant1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getProjectHelper()"><!-- --></A><H3>
getProjectHelper</H3>
<PRE>
public static <A HREF="../../../../org/apache/tools/ant/ProjectHelper.html" title="class in org.apache.tools.ant">ProjectHelper</A> <B>getProjectHelper</B>()
                                      throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Discovers a project helper instance. Uses the same patterns
 as JAXP, commons-logging, etc: a system property, a JDK1.3
 service discovery, default.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a ProjectHelper, either a custom implementation
 if one is available and configured, or the default implementation
 otherwise.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if a specified helper class cannot
 be loaded/instantiated.</DL>
</DD>
</DL>
<HR>

<A NAME="getContextClassLoader()"><!-- --></A><H3>
getContextClassLoader</H3>
<PRE>
public static java.lang.ClassLoader <B>getContextClassLoader</B>()</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             Use LoaderUtils.getContextClassLoader()</I>
<P>
<DD>JDK1.1 compatible access to the context class loader.
 Cut&paste from JAXP.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the current context class loader, or <code>null</code>
 if the context class loader is unavailable.</DL>
</DD>
</DL>
<HR>

<A NAME="configure(java.lang.Object, org.xml.sax.AttributeList, org.apache.tools.ant.Project)"><!-- --></A><H3>
configure</H3>
<PRE>
public static void <B>configure</B>(java.lang.Object&nbsp;target,
                             org.xml.sax.AttributeList&nbsp;attrs,
                             <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)
                      throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             Use IntrospectionHelper for each property.</I>
<P>
<DD>Configures an object using an introspection handler.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>target</CODE> - The target object to be configured.
               Must not be <code>null</code>.<DD><CODE>attrs</CODE> - A list of attributes to configure within the target.
               Must not be <code>null</code>.<DD><CODE>project</CODE> - The project containing the target.
                Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if any of the attributes can't be handled by
                           the target</DL>
</DD>
</DL>
<HR>

<A NAME="addText(org.apache.tools.ant.Project, java.lang.Object, char[], int, int)"><!-- --></A><H3>
addText</H3>
<PRE>
public static void <B>addText</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                           java.lang.Object&nbsp;target,
                           char[]&nbsp;buf,
                           int&nbsp;start,
                           int&nbsp;count)
                    throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Adds the content of #PCDATA sections to an element.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - The project containing the target.
                Must not be <code>null</code>.<DD><CODE>target</CODE> - The target object to be configured.
                Must not be <code>null</code>.<DD><CODE>buf</CODE> - A character array of the text within the element.
            Will not be <code>null</code>.<DD><CODE>start</CODE> - The start element in the array.<DD><CODE>count</CODE> - The number of characters to read from the array.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the target object doesn't accept text</DL>
</DD>
</DL>
<HR>

<A NAME="addText(org.apache.tools.ant.Project, java.lang.Object, java.lang.String)"><!-- --></A><H3>
addText</H3>
<PRE>
public static void <B>addText</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                           java.lang.Object&nbsp;target,
                           java.lang.String&nbsp;text)
                    throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Adds the content of #PCDATA sections to an element.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - The project containing the target.
                Must not be <code>null</code>.<DD><CODE>target</CODE> - The target object to be configured.
                Must not be <code>null</code>.<DD><CODE>text</CODE> - Text to add to the target.
                May be <code>null</code>, in which case this
                method call is a no-op.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the target object doesn't accept text</DL>
</DD>
</DL>
<HR>

<A NAME="storeChild(org.apache.tools.ant.Project, java.lang.Object, java.lang.Object, java.lang.String)"><!-- --></A><H3>
storeChild</H3>
<PRE>
public static void <B>storeChild</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                              java.lang.Object&nbsp;parent,
                              java.lang.Object&nbsp;child,
                              java.lang.String&nbsp;tag)</PRE>
<DL>
<DD>Stores a configured child element within its parent object.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - Project containing the objects.
                May be <code>null</code>.<DD><CODE>parent</CODE> - Parent object to add child to.
                Must not be <code>null</code>.<DD><CODE>child</CODE> - Child object to store in parent.
                Should not be <code>null</code>.<DD><CODE>tag</CODE> - Name of element which generated the child.
                May be <code>null</code>, in which case
                the child is not stored.</DL>
</DD>
</DL>
<HR>

<A NAME="replaceProperties(org.apache.tools.ant.Project, java.lang.String)"><!-- --></A><H3>
replaceProperties</H3>
<PRE>
public static java.lang.String <B>replaceProperties</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                                                 java.lang.String&nbsp;value)
                                          throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             Use project.replaceProperties().</I>
<P>
<DD>Replaces <code>${xxx}</code> style constructions in the given value with
 the string value of the corresponding properties.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - The project containing the properties to replace.
                Must not be <code>null</code>.<DD><CODE>value</CODE> - The string to be scanned for property references.
              May be <code>null</code>.
<DT><B>Returns:</B><DD>the original string with the properties replaced, or
         <code>null</code> if the original string is <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the string contains an opening
                           <code>${</code> without a closing
                           <code>}</code><DT><B>Since:</B></DT>
  <DD>1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="replaceProperties(org.apache.tools.ant.Project, java.lang.String, java.util.Hashtable)"><!-- --></A><H3>
replaceProperties</H3>
<PRE>
public static java.lang.String <B>replaceProperties</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                                                 java.lang.String&nbsp;value,
                                                 java.util.Hashtable&nbsp;keys)
                                          throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             Use PropertyHelper.</I>
<P>
<DD>Replaces <code>${xxx}</code> style constructions in the given value
 with the string value of the corresponding data types.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - The container project. This is used solely for
                logging purposes. Must not be <code>null</code>.<DD><CODE>value</CODE> - The string to be scanned for property references.
              May be <code>null</code>, in which case this
              method returns immediately with no effect.<DD><CODE>keys</CODE> - Mapping (String to String) of property names to their
              values. Must not be <code>null</code>.
<DT><B>Returns:</B><DD>the original string with the properties replaced, or
         <code>null</code> if the original string is <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the string contains an opening
                           <code>${</code> without a closing
                           <code>}</code></DL>
</DD>
</DL>
<HR>

<A NAME="parsePropertyString(java.lang.String, java.util.Vector, java.util.Vector)"><!-- --></A><H3>
parsePropertyString</H3>
<PRE>
public static void <B>parsePropertyString</B>(java.lang.String&nbsp;value,
                                       java.util.Vector&nbsp;fragments,
                                       java.util.Vector&nbsp;propertyRefs)
                                throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             Use PropertyHelper.</I>
<P>
<DD>Parses a string containing <code>${xxx}</code> style property
 references into two lists. The first list is a collection
 of text fragments, while the other is a set of string property names.
 <code>null</code> entries in the first list indicate a property
 reference from the second list.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - Text to parse. Must not be <code>null</code>.<DD><CODE>fragments</CODE> - List to add text fragments to.
                  Must not be <code>null</code>.<DD><CODE>propertyRefs</CODE> - List to add property names to.
                     Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the string contains an opening
                           <code>${</code> without a closing
                           <code>}</code></DL>
</DD>
</DL>
<HR>

<A NAME="genComponentName(java.lang.String, java.lang.String)"><!-- --></A><H3>
genComponentName</H3>
<PRE>
public static java.lang.String <B>genComponentName</B>(java.lang.String&nbsp;uri,
                                                java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Map a namespaced {uri,name} to an internal string format.
 For BC purposes the names from the ant core uri will be
 mapped to "name", other names will be mapped to
 uri + ":" + name.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>uri</CODE> - The namepace URI<DD><CODE>name</CODE> - The localname
<DT><B>Returns:</B><DD>The stringified form of the ns name</DL>
</DD>
</DL>
<HR>

<A NAME="extractUriFromComponentName(java.lang.String)"><!-- --></A><H3>
extractUriFromComponentName</H3>
<PRE>
public static java.lang.String <B>extractUriFromComponentName</B>(java.lang.String&nbsp;componentName)</PRE>
<DL>
<DD>extract a uri from a component name
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>componentName</CODE> - The stringified form for {uri, name}
<DT><B>Returns:</B><DD>The uri or "" if not present</DL>
</DD>
</DL>
<HR>

<A NAME="extractNameFromComponentName(java.lang.String)"><!-- --></A><H3>
extractNameFromComponentName</H3>
<PRE>
public static java.lang.String <B>extractNameFromComponentName</B>(java.lang.String&nbsp;componentName)</PRE>
<DL>
<DD>extract the element name from a component name
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>componentName</CODE> - The stringified form for {uri, name}
<DT><B>Returns:</B><DD>The element name of the component</DL>
</DD>
</DL>
<HR>

<A NAME="addLocationToBuildException(org.apache.tools.ant.BuildException, org.apache.tools.ant.Location)"><!-- --></A><H3>
addLocationToBuildException</H3>
<PRE>
public static <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A> <B>addLocationToBuildException</B>(<A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A>&nbsp;ex,
                                                         <A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A>&nbsp;newLocation)</PRE>
<DL>
<DD>Add location to build exception.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ex</CODE> - the build exception, if the build exception
           does not include<DD><CODE>newLocation</CODE> - the location of the calling task (may be null)
<DT><B>Returns:</B><DD>a new build exception based in the build exception with
         location set to newLocation. If the original exception
         did not have a location, just return the build exception</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/PropertyHelper.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/ProjectHelper.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ProjectHelper.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
