<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:27 EST 2006 -->
<TITLE>
ConstantPool (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPool class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="ConstantPool (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ConstantPool.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.optional.depend.constantpool</FONT>
<BR>
Class ConstantPool</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPool</B>
</PRE>
<HR>
<DL>
<DT><PRE>public class <B>ConstantPool</B><DT>extends java.lang.Object</DL>
</PRE>

<P>
The constant pool of a Java class. The constant pool is a collection of
 constants used in a Java class file. It stores strings, constant values,
 class names, method names, field names etc.
<P>

<P>
<DL>
<DT><B>See Also:</B><DD><a href="http://java.sun.com/docs/books/vmspec/">The Java Virtual
      Machine Specification</a></DL>
<HR>

<P>

<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html#ConstantPool()">ConstantPool</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Initialise the constant pool.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html#addEntry(org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry)">addEntry</A></B>(<A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</A>&nbsp;entry)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add an entry to the constant pool.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html#getClassEntry(java.lang.String)">getClassEntry</A></B>(java.lang.String&nbsp;className)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the index of a given CONSTANT_CLASS entry in the constant pool.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html#getConstantEntry(java.lang.Object)">getConstantEntry</A></B>(java.lang.Object&nbsp;constantValue)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the index of a given constant value entry in the constant pool.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html#getEntry(int)">getEntry</A></B>(int&nbsp;index)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get an constant pool entry at a particular index.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html#getFieldRefEntry(java.lang.String, java.lang.String, java.lang.String)">getFieldRefEntry</A></B>(java.lang.String&nbsp;fieldClassName,
                 java.lang.String&nbsp;fieldName,
                 java.lang.String&nbsp;fieldType)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the index of a given CONSTANT_FIELDREF entry in the constant
 pool.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html#getInterfaceMethodRefEntry(java.lang.String, java.lang.String, java.lang.String)">getInterfaceMethodRefEntry</A></B>(java.lang.String&nbsp;interfaceMethodClassName,
                           java.lang.String&nbsp;interfaceMethodName,
                           java.lang.String&nbsp;interfaceMethodType)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the index of a given CONSTANT_INTERFACEMETHODREF entry in the
 constant pool.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html#getMethodRefEntry(java.lang.String, java.lang.String, java.lang.String)">getMethodRefEntry</A></B>(java.lang.String&nbsp;methodClassName,
                  java.lang.String&nbsp;methodName,
                  java.lang.String&nbsp;methodType)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the index of a given CONSTANT_METHODREF entry in the constant
 pool.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html#getNameAndTypeEntry(java.lang.String, java.lang.String)">getNameAndTypeEntry</A></B>(java.lang.String&nbsp;name,
                    java.lang.String&nbsp;type)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the index of a given CONSTANT_NAMEANDTYPE entry in the constant
 pool.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html#getUTF8Entry(java.lang.String)">getUTF8Entry</A></B>(java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the index of a given UTF8 constant pool entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html#read(java.io.DataInputStream)">read</A></B>(java.io.DataInputStream&nbsp;classStream)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Read the constant pool from a class input stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html#resolve()">resolve</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Resolve the entries in the constant pool.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html#size()">size</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the size of the constant pool.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html#toString()">toString</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Dump the constant pool to a string.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ConstantPool()"><!-- --></A><H3>
ConstantPool</H3>
<PRE>
public <B>ConstantPool</B>()</PRE>
<DL>
<DD>Initialise the constant pool.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="read(java.io.DataInputStream)"><!-- --></A><H3>
read</H3>
<PRE>
public void <B>read</B>(java.io.DataInputStream&nbsp;classStream)
          throws java.io.IOException</PRE>
<DL>
<DD>Read the constant pool from a class input stream.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classStream</CODE> - the DataInputStream of a class file.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there is a problem reading the constant pool
      from the stream</DL>
</DD>
</DL>
<HR>

<A NAME="size()"><!-- --></A><H3>
size</H3>
<PRE>
public int <B>size</B>()</PRE>
<DL>
<DD>Get the size of the constant pool.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the size of the constant pool</DL>
</DD>
</DL>
<HR>

<A NAME="addEntry(org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry)"><!-- --></A><H3>
addEntry</H3>
<PRE>
public int <B>addEntry</B>(<A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</A>&nbsp;entry)</PRE>
<DL>
<DD>Add an entry to the constant pool.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>entry</CODE> - the new entry to be added to the constant pool.
<DT><B>Returns:</B><DD>the index into the constant pool at which the entry is
      stored.</DL>
</DD>
</DL>
<HR>

<A NAME="resolve()"><!-- --></A><H3>
resolve</H3>
<PRE>
public void <B>resolve</B>()</PRE>
<DL>
<DD>Resolve the entries in the constant pool. Resolution of the constant
 pool involves transforming indexes to other constant pool entries
 into the actual data for that entry.
<P>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="getEntry(int)"><!-- --></A><H3>
getEntry</H3>
<PRE>
public <A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</A> <B>getEntry</B>(int&nbsp;index)</PRE>
<DL>
<DD>Get an constant pool entry at a particular index.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>index</CODE> - the index into the constant pool.
<DT><B>Returns:</B><DD>the constant pool entry at that index.</DL>
</DD>
</DL>
<HR>

<A NAME="getUTF8Entry(java.lang.String)"><!-- --></A><H3>
getUTF8Entry</H3>
<PRE>
public int <B>getUTF8Entry</B>(java.lang.String&nbsp;value)</PRE>
<DL>
<DD>Get the index of a given UTF8 constant pool entry.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - the string value of the UTF8 entry.
<DT><B>Returns:</B><DD>the index at which the given string occurs in the constant
      pool or -1 if the value does not occur.</DL>
</DD>
</DL>
<HR>

<A NAME="getClassEntry(java.lang.String)"><!-- --></A><H3>
getClassEntry</H3>
<PRE>
public int <B>getClassEntry</B>(java.lang.String&nbsp;className)</PRE>
<DL>
<DD>Get the index of a given CONSTANT_CLASS entry in the constant pool.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>className</CODE> - the name of the class for which the class entry
      index is required.
<DT><B>Returns:</B><DD>the index at which the given class entry occurs in the
      constant pool or -1 if the value does not occur.</DL>
</DD>
</DL>
<HR>

<A NAME="getConstantEntry(java.lang.Object)"><!-- --></A><H3>
getConstantEntry</H3>
<PRE>
public int <B>getConstantEntry</B>(java.lang.Object&nbsp;constantValue)</PRE>
<DL>
<DD>Get the index of a given constant value entry in the constant pool.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>constantValue</CODE> - the constant value for which the index is
      required.
<DT><B>Returns:</B><DD>the index at which the given value entry occurs in the
      constant pool or -1 if the value does not occur.</DL>
</DD>
</DL>
<HR>

<A NAME="getMethodRefEntry(java.lang.String, java.lang.String, java.lang.String)"><!-- --></A><H3>
getMethodRefEntry</H3>
<PRE>
public int <B>getMethodRefEntry</B>(java.lang.String&nbsp;methodClassName,
                             java.lang.String&nbsp;methodName,
                             java.lang.String&nbsp;methodType)</PRE>
<DL>
<DD>Get the index of a given CONSTANT_METHODREF entry in the constant
 pool.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>methodClassName</CODE> - the name of the class which contains the
      method being referenced.<DD><CODE>methodName</CODE> - the name of the method being referenced.<DD><CODE>methodType</CODE> - the type descriptor of the method being referenced.
<DT><B>Returns:</B><DD>the index at which the given method ref entry occurs in the
      constant pool or -1 if the value does not occur.</DL>
</DD>
</DL>
<HR>

<A NAME="getInterfaceMethodRefEntry(java.lang.String, java.lang.String, java.lang.String)"><!-- --></A><H3>
getInterfaceMethodRefEntry</H3>
<PRE>
public int <B>getInterfaceMethodRefEntry</B>(java.lang.String&nbsp;interfaceMethodClassName,
                                      java.lang.String&nbsp;interfaceMethodName,
                                      java.lang.String&nbsp;interfaceMethodType)</PRE>
<DL>
<DD>Get the index of a given CONSTANT_INTERFACEMETHODREF entry in the
 constant pool.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>interfaceMethodClassName</CODE> - the name of the interface which
      contains the method being referenced.<DD><CODE>interfaceMethodName</CODE> - the name of the method being referenced.<DD><CODE>interfaceMethodType</CODE> - the type descriptor of the method being
      referenced.
<DT><B>Returns:</B><DD>the index at which the given method ref entry occurs in the
      constant pool or -1 if the value does not occur.</DL>
</DD>
</DL>
<HR>

<A NAME="getFieldRefEntry(java.lang.String, java.lang.String, java.lang.String)"><!-- --></A><H3>
getFieldRefEntry</H3>
<PRE>
public int <B>getFieldRefEntry</B>(java.lang.String&nbsp;fieldClassName,
                            java.lang.String&nbsp;fieldName,
                            java.lang.String&nbsp;fieldType)</PRE>
<DL>
<DD>Get the index of a given CONSTANT_FIELDREF entry in the constant
 pool.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fieldClassName</CODE> - the name of the class which contains the field
      being referenced.<DD><CODE>fieldName</CODE> - the name of the field being referenced.<DD><CODE>fieldType</CODE> - the type descriptor of the field being referenced.
<DT><B>Returns:</B><DD>the index at which the given field ref entry occurs in the
      constant pool or -1 if the value does not occur.</DL>
</DD>
</DL>
<HR>

<A NAME="getNameAndTypeEntry(java.lang.String, java.lang.String)"><!-- --></A><H3>
getNameAndTypeEntry</H3>
<PRE>
public int <B>getNameAndTypeEntry</B>(java.lang.String&nbsp;name,
                               java.lang.String&nbsp;type)</PRE>
<DL>
<DD>Get the index of a given CONSTANT_NAMEANDTYPE entry in the constant
 pool.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the name<DD><CODE>type</CODE> - the type
<DT><B>Returns:</B><DD>the index at which the given NameAndType entry occurs in the
      constant pool or -1 if the value does not occur.</DL>
</DD>
</DL>
<HR>

<A NAME="toString()"><!-- --></A><H3>
toString</H3>
<PRE>
public java.lang.String <B>toString</B>()</PRE>
<DL>
<DD>Dump the constant pool to a string.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>toString</CODE> in class <CODE>java.lang.Object</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the constant pool entries as strings</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantCPInfo.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../../org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPool.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ConstantPool.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
