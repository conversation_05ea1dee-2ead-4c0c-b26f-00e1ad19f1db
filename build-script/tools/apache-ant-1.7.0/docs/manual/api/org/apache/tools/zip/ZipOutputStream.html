<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:34 EST 2006 -->
<TITLE>
ZipOutputStream (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.zip.ZipOutputStream class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="ZipOutputStream (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipLong.html" title="class in org.apache.tools.zip"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipShort.html" title="class in org.apache.tools.zip"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/zip/ZipOutputStream.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ZipOutputStream.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.zip</FONT>
<BR>
Class ZipOutputStream</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by ">java.io.OutputStream
      <IMG SRC="../../../../resources/inherit.gif" ALT="extended by ">java.io.FilterOutputStream
          <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.zip.ZipOutputStream</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.io.Closeable, java.io.Flushable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>ZipOutputStream</B><DT>extends java.io.FilterOutputStream</DL>
</PRE>

<P>
Reimplementation of <CODE>java.util.zip.ZipOutputStream</CODE> that does handle the extended
 functionality of this package, especially internal/external file
 attributes and extra fields with different layouts for local file
 data and central directory entries.

 <p>This class will try to use <CODE>RandomAccessFile</CODE> when you know that the output is going to go to a
 file.</p>

 <p>If RandomAccessFile cannot be used, this implementation will use
 a Data Descriptor to store size and CRC information for <A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#DEFLATED"><CODE>DEFLATED</CODE></A> entries, this means, you don't need to
 calculate them yourself.  Unfortunately this is not possible for
 the <A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#STORED"><CODE>STORED</CODE></A> method, here setting the CRC and
 uncompressed size information is required before <A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#putNextEntry(org.apache.tools.zip.ZipEntry)"><CODE>putNextEntry</CODE></A> can be called.</p>
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#buf">buf</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This buffer servers as a Deflater.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#CFH_SIG">CFH_SIG</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;central file header signature</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#DD_SIG">DD_SIG</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;data descriptor signature</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.zip.Deflater</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#def">def</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This Deflater object is used for output.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#DEFAULT_COMPRESSION">DEFAULT_COMPRESSION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Default compression level for deflated entries.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#DEFLATED">DEFLATED</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Compression method for deflated entries.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#EOCD_SIG">EOCD_SIG</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;end of central dir signature</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#LFH_SIG">LFH_SIG</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;local file header signature</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#STORED">STORED</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Compression method for stored entries.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_java.io.FilterOutputStream"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class java.io.FilterOutputStream</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>out</CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#ZipOutputStream(java.io.File)">ZipOutputStream</A></B>(java.io.File&nbsp;file)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new ZIP OutputStream writing to a File.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#ZipOutputStream(java.io.OutputStream)">ZipOutputStream</A></B>(java.io.OutputStream&nbsp;out)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new ZIP OutputStream filtering the underlying stream.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#adjustToLong(int)">adjustToLong</A></B>(int&nbsp;i)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Assumes a negative integer really is a positive integer that
 has wrapped around and re-creates the original value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#close()">close</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Closes this output stream and releases any system resources
 associated with the stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#closeEntry()">closeEntry</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Writes all necessary data for this entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#deflate()">deflate</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Writes next block of compressed data to the output stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#finish()">finish</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Finishs writing the contents and closes this as well as the
 underlying stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#flush()">flush</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Flushes this output stream and forces any buffered output bytes
 to be written out to the stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#getBytes(java.lang.String)">getBytes</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Retrieve the bytes for the given String in the encoding set for
 this Stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#getEncoding()">getEncoding</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The encoding to use for filenames and the file comment.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#isSeekable()">isSeekable</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This method indicates whether this archive is writing to a seekable stream (i.e., to a random
 access file).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#putNextEntry(org.apache.tools.zip.ZipEntry)">putNextEntry</A></B>(<A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</A>&nbsp;ze)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Begin writing next entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#setComment(java.lang.String)">setComment</A></B>(java.lang.String&nbsp;comment)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the file comment.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#setEncoding(java.lang.String)">setEncoding</A></B>(java.lang.String&nbsp;encoding)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The encoding to use for filenames and the file comment.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#setLevel(int)">setLevel</A></B>(int&nbsp;level)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the compression level for subsequent entries.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#setMethod(int)">setMethod</A></B>(int&nbsp;method)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the default compression method for subsequent entries.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipLong.html" title="class in org.apache.tools.zip">ZipLong</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#toDosTime(java.util.Date)">toDosTime</A></B>(java.util.Date&nbsp;time)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convert a Date object to a DOS date/time field.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#toDosTime(long)">toDosTime</A></B>(long&nbsp;t)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convert a Date object to a DOS date/time field.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#write(byte[], int, int)">write</A></B>(byte[]&nbsp;b,
      int&nbsp;offset,
      int&nbsp;length)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Writes bytes to ZIP entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#write(int)">write</A></B>(int&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Writes a single byte to ZIP entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#writeCentralDirectoryEnd()">writeCentralDirectoryEnd</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Writes the &quot;End of central dir record&quot;.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#writeCentralFileHeader(org.apache.tools.zip.ZipEntry)">writeCentralFileHeader</A></B>(<A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</A>&nbsp;ze)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Writes the central file header entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#writeDataDescriptor(org.apache.tools.zip.ZipEntry)">writeDataDescriptor</A></B>(<A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</A>&nbsp;ze)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Writes the data descriptor entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#writeLocalFileHeader(org.apache.tools.zip.ZipEntry)">writeLocalFileHeader</A></B>(<A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</A>&nbsp;ze)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Writes the local file header entry</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#writeOut(byte[])">writeOut</A></B>(byte[]&nbsp;data)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Write bytes to output or random access file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#writeOut(byte[], int, int)">writeOut</A></B>(byte[]&nbsp;data,
         int&nbsp;offset,
         int&nbsp;length)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Write bytes to output or random access file.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.io.FilterOutputStream"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.io.FilterOutputStream</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>write</CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="DEFLATED"><!-- --></A><H3>
DEFLATED</H3>
<PRE>
public static final int <B>DEFLATED</B></PRE>
<DL>
<DD>Compression method for deflated entries.
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>1.1</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipOutputStream.DEFLATED">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_COMPRESSION"><!-- --></A><H3>
DEFAULT_COMPRESSION</H3>
<PRE>
public static final int <B>DEFAULT_COMPRESSION</B></PRE>
<DL>
<DD>Default compression level for deflated entries.
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipOutputStream.DEFAULT_COMPRESSION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="STORED"><!-- --></A><H3>
STORED</H3>
<PRE>
public static final int <B>STORED</B></PRE>
<DL>
<DD>Compression method for stored entries.
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>1.1</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipOutputStream.STORED">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="def"><!-- --></A><H3>
def</H3>
<PRE>
protected java.util.zip.Deflater <B>def</B></PRE>
<DL>
<DD>This Deflater object is used for output.

 <p>This attribute is only protected to provide a level of API
 backwards compatibility.  This class used to extend <CODE>DeflaterOutputStream</CODE> up to
 Revision 1.13.</p>
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>1.14</DD>
</DL>
</DL>
<HR>

<A NAME="buf"><!-- --></A><H3>
buf</H3>
<PRE>
protected byte[] <B>buf</B></PRE>
<DL>
<DD>This buffer servers as a Deflater.

 <p>This attribute is only protected to provide a level of API
 backwards compatibility.  This class used to extend <CODE>DeflaterOutputStream</CODE> up to
 Revision 1.13.</p>
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>1.14</DD>
</DL>
</DL>
<HR>

<A NAME="LFH_SIG"><!-- --></A><H3>
LFH_SIG</H3>
<PRE>
protected static final byte[] <B>LFH_SIG</B></PRE>
<DL>
<DD>local file header signature
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DL>
<HR>

<A NAME="DD_SIG"><!-- --></A><H3>
DD_SIG</H3>
<PRE>
protected static final byte[] <B>DD_SIG</B></PRE>
<DL>
<DD>data descriptor signature
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DL>
<HR>

<A NAME="CFH_SIG"><!-- --></A><H3>
CFH_SIG</H3>
<PRE>
protected static final byte[] <B>CFH_SIG</B></PRE>
<DL>
<DD>central file header signature
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DL>
<HR>

<A NAME="EOCD_SIG"><!-- --></A><H3>
EOCD_SIG</H3>
<PRE>
protected static final byte[] <B>EOCD_SIG</B></PRE>
<DL>
<DD>end of central dir signature
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ZipOutputStream(java.io.OutputStream)"><!-- --></A><H3>
ZipOutputStream</H3>
<PRE>
public <B>ZipOutputStream</B>(java.io.OutputStream&nbsp;out)</PRE>
<DL>
<DD>Creates a new ZIP OutputStream filtering the underlying stream.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>out</CODE> - the outputstream to zip<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DL>
<HR>

<A NAME="ZipOutputStream(java.io.File)"><!-- --></A><H3>
ZipOutputStream</H3>
<PRE>
public <B>ZipOutputStream</B>(java.io.File&nbsp;file)
                throws java.io.IOException</PRE>
<DL>
<DD>Creates a new ZIP OutputStream writing to a File.  Will use
 random access if possible.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>file</CODE> - the file to zip to
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.14</DD>
</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="isSeekable()"><!-- --></A><H3>
isSeekable</H3>
<PRE>
public boolean <B>isSeekable</B>()</PRE>
<DL>
<DD>This method indicates whether this archive is writing to a seekable stream (i.e., to a random
 access file).

 <p>For seekable streams, you don't need to calculate the CRC or
 uncompressed size for <A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#STORED"><CODE>STORED</CODE></A> entries before
 invoking <A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html#putNextEntry(org.apache.tools.zip.ZipEntry)"><CODE>putNextEntry(org.apache.tools.zip.ZipEntry)</CODE></A>.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true if seekable<DT><B>Since:</B></DT>
  <DD>1.17</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setEncoding(java.lang.String)"><!-- --></A><H3>
setEncoding</H3>
<PRE>
public void <B>setEncoding</B>(java.lang.String&nbsp;encoding)</PRE>
<DL>
<DD>The encoding to use for filenames and the file comment.

 <p>For a list of possible values see <a
 href="http://java.sun.com/j2se/1.5.0/docs/guide/intl/encoding.doc.html">http://java.sun.com/j2se/1.5.0/docs/guide/intl/encoding.doc.html</a>.
 Defaults to the platform's default character encoding.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>encoding</CODE> - the encoding value<DT><B>Since:</B></DT>
  <DD>1.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getEncoding()"><!-- --></A><H3>
getEncoding</H3>
<PRE>
public java.lang.String <B>getEncoding</B>()</PRE>
<DL>
<DD>The encoding to use for filenames and the file comment.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>null if using the platform's default character encoding.<DT><B>Since:</B></DT>
  <DD>1.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="finish()"><!-- --></A><H3>
finish</H3>
<PRE>
public void <B>finish</B>()
            throws java.io.IOException</PRE>
<DL>
<DD>Finishs writing the contents and closes this as well as the
 underlying stream.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="closeEntry()"><!-- --></A><H3>
closeEntry</H3>
<PRE>
public void <B>closeEntry</B>()
                throws java.io.IOException</PRE>
<DL>
<DD>Writes all necessary data for this entry.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="putNextEntry(org.apache.tools.zip.ZipEntry)"><!-- --></A><H3>
putNextEntry</H3>
<PRE>
public void <B>putNextEntry</B>(<A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</A>&nbsp;ze)
                  throws java.io.IOException</PRE>
<DL>
<DD>Begin writing next entry.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ze</CODE> - the entry to write
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setComment(java.lang.String)"><!-- --></A><H3>
setComment</H3>
<PRE>
public void <B>setComment</B>(java.lang.String&nbsp;comment)</PRE>
<DL>
<DD>Set the file comment.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>comment</CODE> - the comment<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setLevel(int)"><!-- --></A><H3>
setLevel</H3>
<PRE>
public void <B>setLevel</B>(int&nbsp;level)</PRE>
<DL>
<DD>Sets the compression level for subsequent entries.

 <p>Default is Deflater.DEFAULT_COMPRESSION.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>level</CODE> - the compression level.
<DT><B>Throws:</B>
<DD><CODE>java.lang.IllegalArgumentException</CODE> - if an invalid compression level is specified.<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setMethod(int)"><!-- --></A><H3>
setMethod</H3>
<PRE>
public void <B>setMethod</B>(int&nbsp;method)</PRE>
<DL>
<DD>Sets the default compression method for subsequent entries.

 <p>Default is DEFLATED.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>method</CODE> - an <code>int</code> from java.util.zip.ZipEntry<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="write(byte[], int, int)"><!-- --></A><H3>
write</H3>
<PRE>
public void <B>write</B>(byte[]&nbsp;b,
                  int&nbsp;offset,
                  int&nbsp;length)
           throws java.io.IOException</PRE>
<DL>
<DD>Writes bytes to ZIP entry.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>write</CODE> in class <CODE>java.io.FilterOutputStream</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - the byte array to write<DD><CODE>offset</CODE> - the start position to write from<DD><CODE>length</CODE> - the number of bytes to write
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="write(int)"><!-- --></A><H3>
write</H3>
<PRE>
public void <B>write</B>(int&nbsp;b)
           throws java.io.IOException</PRE>
<DL>
<DD>Writes a single byte to ZIP entry.

 <p>Delegates to the three arg method.</p>
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>write</CODE> in class <CODE>java.io.FilterOutputStream</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - the byte to write
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.14</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="close()"><!-- --></A><H3>
close</H3>
<PRE>
public void <B>close</B>()
           throws java.io.IOException</PRE>
<DL>
<DD>Closes this output stream and releases any system resources
 associated with the stream.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE>close</CODE> in interface <CODE>java.io.Closeable</CODE><DT><B>Overrides:</B><DD><CODE>close</CODE> in class <CODE>java.io.FilterOutputStream</CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if an I/O error occurs.<DT><B>Since:</B></DT>
  <DD>1.14</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="flush()"><!-- --></A><H3>
flush</H3>
<PRE>
public void <B>flush</B>()
           throws java.io.IOException</PRE>
<DL>
<DD>Flushes this output stream and forces any buffered output bytes
 to be written out to the stream.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE>flush</CODE> in interface <CODE>java.io.Flushable</CODE><DT><B>Overrides:</B><DD><CODE>flush</CODE> in class <CODE>java.io.FilterOutputStream</CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if an I/O error occurs.<DT><B>Since:</B></DT>
  <DD>1.14</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="deflate()"><!-- --></A><H3>
deflate</H3>
<PRE>
protected final void <B>deflate</B>()
                      throws java.io.IOException</PRE>
<DL>
<DD>Writes next block of compressed data to the output stream.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.14</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="writeLocalFileHeader(org.apache.tools.zip.ZipEntry)"><!-- --></A><H3>
writeLocalFileHeader</H3>
<PRE>
protected void <B>writeLocalFileHeader</B>(<A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</A>&nbsp;ze)
                             throws java.io.IOException</PRE>
<DL>
<DD>Writes the local file header entry
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ze</CODE> - the entry to write
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="writeDataDescriptor(org.apache.tools.zip.ZipEntry)"><!-- --></A><H3>
writeDataDescriptor</H3>
<PRE>
protected void <B>writeDataDescriptor</B>(<A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</A>&nbsp;ze)
                            throws java.io.IOException</PRE>
<DL>
<DD>Writes the data descriptor entry.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ze</CODE> - the entry to write
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="writeCentralFileHeader(org.apache.tools.zip.ZipEntry)"><!-- --></A><H3>
writeCentralFileHeader</H3>
<PRE>
protected void <B>writeCentralFileHeader</B>(<A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</A>&nbsp;ze)
                               throws java.io.IOException</PRE>
<DL>
<DD>Writes the central file header entry.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ze</CODE> - the entry to write
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="writeCentralDirectoryEnd()"><!-- --></A><H3>
writeCentralDirectoryEnd</H3>
<PRE>
protected void <B>writeCentralDirectoryEnd</B>()
                                 throws java.io.IOException</PRE>
<DL>
<DD>Writes the &quot;End of central dir record&quot;.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="toDosTime(java.util.Date)"><!-- --></A><H3>
toDosTime</H3>
<PRE>
protected static <A HREF="../../../../org/apache/tools/zip/ZipLong.html" title="class in org.apache.tools.zip">ZipLong</A> <B>toDosTime</B>(java.util.Date&nbsp;time)</PRE>
<DL>
<DD>Convert a Date object to a DOS date/time field.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>time</CODE> - the <code>Date</code> to convert
<DT><B>Returns:</B><DD>the date as a <code>ZipLong</code><DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="toDosTime(long)"><!-- --></A><H3>
toDosTime</H3>
<PRE>
protected static byte[] <B>toDosTime</B>(long&nbsp;t)</PRE>
<DL>
<DD>Convert a Date object to a DOS date/time field.

 <p>Stolen from InfoZip's <code>fileio.c</code></p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>t</CODE> - number of milliseconds since the epoch
<DT><B>Returns:</B><DD>the date as a byte array<DT><B>Since:</B></DT>
  <DD>1.26</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getBytes(java.lang.String)"><!-- --></A><H3>
getBytes</H3>
<PRE>
protected byte[] <B>getBytes</B>(java.lang.String&nbsp;name)
                   throws java.util.zip.ZipException</PRE>
<DL>
<DD>Retrieve the bytes for the given String in the encoding set for
 this Stream.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the string to get bytes from
<DT><B>Returns:</B><DD>the bytes as a byte array
<DT><B>Throws:</B>
<DD><CODE>java.util.zip.ZipException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="writeOut(byte[])"><!-- --></A><H3>
writeOut</H3>
<PRE>
protected final void <B>writeOut</B>(byte[]&nbsp;data)
                       throws java.io.IOException</PRE>
<DL>
<DD>Write bytes to output or random access file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>data</CODE> - the byte array to write
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.14</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="writeOut(byte[], int, int)"><!-- --></A><H3>
writeOut</H3>
<PRE>
protected final void <B>writeOut</B>(byte[]&nbsp;data,
                              int&nbsp;offset,
                              int&nbsp;length)
                       throws java.io.IOException</PRE>
<DL>
<DD>Write bytes to output or random access file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>data</CODE> - the byte array to write<DD><CODE>offset</CODE> - the start position to write from<DD><CODE>length</CODE> - the number of bytes to write
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.14</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="adjustToLong(int)"><!-- --></A><H3>
adjustToLong</H3>
<PRE>
protected static long <B>adjustToLong</B>(int&nbsp;i)</PRE>
<DL>
<DD>Assumes a negative integer really is a positive integer that
 has wrapped around and re-creates the original value.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>i</CODE> - the value to treat as unsigned int.
<DT><B>Returns:</B><DD>the unsigned int as a long.<DT><B>Since:</B></DT>
  <DD>1.34</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipLong.html" title="class in org.apache.tools.zip"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipShort.html" title="class in org.apache.tools.zip"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/zip/ZipOutputStream.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ZipOutputStream.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
