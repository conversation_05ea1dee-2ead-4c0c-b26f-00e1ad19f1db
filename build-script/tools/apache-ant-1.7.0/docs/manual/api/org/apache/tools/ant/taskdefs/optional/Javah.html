<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:26 EST 2006 -->
<TITLE>
Javah (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.optional.Javah class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Javah (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/EchoProperties.FormatAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.ClassArgument.html" title="class in org.apache.tools.ant.taskdefs.optional"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/Javah.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Javah.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.Task">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.optional</FONT>
<BR>
Class Javah</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.optional.Javah</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>Javah</B><DT>extends <A HREF="../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></DL>
</PRE>

<P>
Generates JNI header files using javah.

 This task can take the following arguments:
 <ul>
 <li>classname - the fully-qualified name of a class</li>
 <li>outputFile - Concatenates the resulting header or source files for all
     the classes listed into this file</li>
 <li>destdir - Sets the directory where javah saves the header files or the
     stub files</li>
 <li>classpath</li>
 <li>bootclasspath</li>
 <li>force - Specifies that output files should always be written
       (JDK1.2 only)</li>
 <li>old - Specifies that old JDK1.0-style header files should be generated
     (otherwise output file contain JNI-style native method
      function prototypes) (JDK1.2 only)</li>
 <li>stubs - generate C declarations from the Java object file (used with old)</li>
 <li>verbose - causes javah to print a message to stdout concerning the status
     of the generated files</li>
 <li>extdirs - Override location of installed extensions</li>
 </ul>
 Of these arguments, either <b>outputFile</b> or <b>destdir</b> is required,
 but not both. More than one classname may be specified, using a comma-separated
 list or by using <code>&lt;class name="xxx"&gt;</code> elements within the task.
 <p>
 When this task executes, it will generate C header and source files that
 are needed to implement native methods.
<P>

<P>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.ClassArgument.html" title="class in org.apache.tools.ant.taskdefs.optional">Javah.ClassArgument</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A class corresponding the the nested "class" element.</TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#Javah()">Javah</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;No arg constructor.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../org/apache/tools/ant/util/facade/ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.util.facade">ImplementationSpecificArgument</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#createArg()">createArg</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds an implementation specific command-line argument.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#createBootclasspath()">createBootclasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds path to bootstrap class files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.ClassArgument.html" title="class in org.apache.tools.ant.taskdefs.optional">Javah.ClassArgument</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#createClass()">createClass</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds class to process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#createClasspath()">createClasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Path to use for classpath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Execute the task</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#getBootclasspath()">getBootclasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The bootclasspath to use.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#getClasses()">getClasses</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Names of the classes to process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#getClasspath()">getClasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The classpath to use.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#getCurrentArgs()">getCurrentArgs</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the (implementation specific) settings given as nested
 arg elements.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#getDestdir()">getDestdir</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The destination directory, if any.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#getForce()">getForce</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether output files should always be written.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#getOld()">getOld</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether old JDK1.0-style header files should be generated.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#getOutputfile()">getOutputfile</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The destination file, if any.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#getStubs()">getStubs</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether C declarations from the Java object file should be generated.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#getVerbose()">getVerbose</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether verbose output should get generated.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#logAndAddFiles(org.apache.tools.ant.types.Commandline)">logAndAddFiles</A></B>(<A HREF="../../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;cmd)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logs the compilation parameters, adds the files to compile and logs the
 &quot;niceSourceList&quot;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#logAndAddFilesToCompile(org.apache.tools.ant.types.Commandline)">logAndAddFilesToCompile</A></B>(<A HREF="../../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;cmd)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logs the compilation parameters, adds the files to compile and logs the
 &quot;niceSourceList&quot;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#setBootclasspath(org.apache.tools.ant.types.Path)">setBootclasspath</A></B>(<A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;src)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;location of bootstrap class files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#setBootClasspathRef(org.apache.tools.ant.types.Reference)">setBootClasspathRef</A></B>(<A HREF="../../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;To the bootstrap path, this adds a reference to a classpath defined elsewhere.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#setClass(java.lang.String)">setClass</A></B>(java.lang.String&nbsp;cls)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;the fully-qualified name of the class (or classes, separated by commas).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</A></B>(<A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;src)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;the classpath to use.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#setClasspathRef(org.apache.tools.ant.types.Reference)">setClasspathRef</A></B>(<A HREF="../../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a reference to a classpath defined elsewhere.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#setDestdir(java.io.File)">setDestdir</A></B>(java.io.File&nbsp;destDir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the destination directory into which the Java source
 files should be compiled.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#setForce(boolean)">setForce</A></B>(boolean&nbsp;force)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, output files should always be written (JDK1.2 only).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#setImplementation(java.lang.String)">setImplementation</A></B>(java.lang.String&nbsp;impl)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Choose the implementation for this particular task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#setOld(boolean)">setOld</A></B>(boolean&nbsp;old)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, specifies that old JDK1.0-style header files should be
 generated.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#setOutputFile(java.io.File)">setOutputFile</A></B>(java.io.File&nbsp;outputFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Concatenates the resulting header or source files for all
 the classes listed into this file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#setStubs(boolean)">setStubs</A></B>(boolean&nbsp;stubs)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, generate C declarations from the Java object file (used with old).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.html#setVerbose(boolean)">setVerbose</A></B>(boolean&nbsp;verbose)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, causes Javah to print a message concerning
 the status of the generated files.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Javah()"><!-- --></A><H3>
Javah</H3>
<PRE>
public <B>Javah</B>()</PRE>
<DL>
<DD>No arg constructor.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setClass(java.lang.String)"><!-- --></A><H3>
setClass</H3>
<PRE>
public void <B>setClass</B>(java.lang.String&nbsp;cls)</PRE>
<DL>
<DD>the fully-qualified name of the class (or classes, separated by commas).
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>cls</CODE> - the classname (or classnames).</DL>
</DD>
</DL>
<HR>

<A NAME="createClass()"><!-- --></A><H3>
createClass</H3>
<PRE>
public <A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.ClassArgument.html" title="class in org.apache.tools.ant.taskdefs.optional">Javah.ClassArgument</A> <B>createClass</B>()</PRE>
<DL>
<DD>Adds class to process.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a <code>ClassArgument</code> to be configured.</DL>
</DD>
</DL>
<HR>

<A NAME="getClasses()"><!-- --></A><H3>
getClasses</H3>
<PRE>
public java.lang.String[] <B>getClasses</B>()</PRE>
<DL>
<DD>Names of the classes to process.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the array of classes.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setDestdir(java.io.File)"><!-- --></A><H3>
setDestdir</H3>
<PRE>
public void <B>setDestdir</B>(java.io.File&nbsp;destDir)</PRE>
<DL>
<DD>Set the destination directory into which the Java source
 files should be compiled.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>destDir</CODE> - the destination directory.</DL>
</DD>
</DL>
<HR>

<A NAME="getDestdir()"><!-- --></A><H3>
getDestdir</H3>
<PRE>
public java.io.File <B>getDestdir</B>()</PRE>
<DL>
<DD>The destination directory, if any.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the destination directory.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setClasspath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setClasspath</H3>
<PRE>
public void <B>setClasspath</B>(<A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;src)</PRE>
<DL>
<DD>the classpath to use.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>src</CODE> - the classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="createClasspath()"><!-- --></A><H3>
createClasspath</H3>
<PRE>
public <A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createClasspath</B>()</PRE>
<DL>
<DD>Path to use for classpath.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a path to be configured.</DL>
</DD>
</DL>
<HR>

<A NAME="setClasspathRef(org.apache.tools.ant.types.Reference)"><!-- --></A><H3>
setClasspathRef</H3>
<PRE>
public void <B>setClasspathRef</B>(<A HREF="../../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</PRE>
<DL>
<DD>Adds a reference to a classpath defined elsewhere.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>r</CODE> - a reference to a classpath.<DT><B>To do:</B></DT>
  <DD>this needs to be documented in the HTML docs.</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getClasspath()"><!-- --></A><H3>
getClasspath</H3>
<PRE>
public <A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>getClasspath</B>()</PRE>
<DL>
<DD>The classpath to use.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the classpath.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setBootclasspath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setBootclasspath</H3>
<PRE>
public void <B>setBootclasspath</B>(<A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;src)</PRE>
<DL>
<DD>location of bootstrap class files.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>src</CODE> - the bootstrap classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="createBootclasspath()"><!-- --></A><H3>
createBootclasspath</H3>
<PRE>
public <A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createBootclasspath</B>()</PRE>
<DL>
<DD>Adds path to bootstrap class files.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a path to be configured.</DL>
</DD>
</DL>
<HR>

<A NAME="setBootClasspathRef(org.apache.tools.ant.types.Reference)"><!-- --></A><H3>
setBootClasspathRef</H3>
<PRE>
public void <B>setBootClasspathRef</B>(<A HREF="../../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</PRE>
<DL>
<DD>To the bootstrap path, this adds a reference to a classpath defined elsewhere.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>r</CODE> - a reference to a classpath<DT><B>To do:</B></DT>
  <DD>this needs to be documented in the HTML.</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getBootclasspath()"><!-- --></A><H3>
getBootclasspath</H3>
<PRE>
public <A HREF="../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>getBootclasspath</B>()</PRE>
<DL>
<DD>The bootclasspath to use.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the bootclass path.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setOutputFile(java.io.File)"><!-- --></A><H3>
setOutputFile</H3>
<PRE>
public void <B>setOutputFile</B>(java.io.File&nbsp;outputFile)</PRE>
<DL>
<DD>Concatenates the resulting header or source files for all
 the classes listed into this file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>outputFile</CODE> - the output file.</DL>
</DD>
</DL>
<HR>

<A NAME="getOutputfile()"><!-- --></A><H3>
getOutputfile</H3>
<PRE>
public java.io.File <B>getOutputfile</B>()</PRE>
<DL>
<DD>The destination file, if any.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the destination file.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setForce(boolean)"><!-- --></A><H3>
setForce</H3>
<PRE>
public void <B>setForce</B>(boolean&nbsp;force)</PRE>
<DL>
<DD>If true, output files should always be written (JDK1.2 only).
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>force</CODE> - the value to use.</DL>
</DD>
</DL>
<HR>

<A NAME="getForce()"><!-- --></A><H3>
getForce</H3>
<PRE>
public boolean <B>getForce</B>()</PRE>
<DL>
<DD>Whether output files should always be written.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the force attribute.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setOld(boolean)"><!-- --></A><H3>
setOld</H3>
<PRE>
public void <B>setOld</B>(boolean&nbsp;old)</PRE>
<DL>
<DD>If true, specifies that old JDK1.0-style header files should be
 generated.
 (otherwise output file contain JNI-style native method function
  prototypes) (JDK1.2 only).
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>old</CODE> - if true use old 1.0 style header files.</DL>
</DD>
</DL>
<HR>

<A NAME="getOld()"><!-- --></A><H3>
getOld</H3>
<PRE>
public boolean <B>getOld</B>()</PRE>
<DL>
<DD>Whether old JDK1.0-style header files should be generated.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the old attribute.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setStubs(boolean)"><!-- --></A><H3>
setStubs</H3>
<PRE>
public void <B>setStubs</B>(boolean&nbsp;stubs)</PRE>
<DL>
<DD>If true, generate C declarations from the Java object file (used with old).
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>stubs</CODE> - if true, generated C declarations.</DL>
</DD>
</DL>
<HR>

<A NAME="getStubs()"><!-- --></A><H3>
getStubs</H3>
<PRE>
public boolean <B>getStubs</B>()</PRE>
<DL>
<DD>Whether C declarations from the Java object file should be generated.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the stubs attribute.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setVerbose(boolean)"><!-- --></A><H3>
setVerbose</H3>
<PRE>
public void <B>setVerbose</B>(boolean&nbsp;verbose)</PRE>
<DL>
<DD>If true, causes Javah to print a message concerning
 the status of the generated files.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>verbose</CODE> - if true, do verbose printing.</DL>
</DD>
</DL>
<HR>

<A NAME="getVerbose()"><!-- --></A><H3>
getVerbose</H3>
<PRE>
public boolean <B>getVerbose</B>()</PRE>
<DL>
<DD>Whether verbose output should get generated.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the verbose attribute.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setImplementation(java.lang.String)"><!-- --></A><H3>
setImplementation</H3>
<PRE>
public void <B>setImplementation</B>(java.lang.String&nbsp;impl)</PRE>
<DL>
<DD>Choose the implementation for this particular task.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>impl</CODE> - the name of the implemenation.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="createArg()"><!-- --></A><H3>
createArg</H3>
<PRE>
public <A HREF="../../../../../../org/apache/tools/ant/util/facade/ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.util.facade">ImplementationSpecificArgument</A> <B>createArg</B>()</PRE>
<DL>
<DD>Adds an implementation specific command-line argument.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a ImplementationSpecificArgument to be configured.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getCurrentArgs()"><!-- --></A><H3>
getCurrentArgs</H3>
<PRE>
public java.lang.String[] <B>getCurrentArgs</B>()</PRE>
<DL>
<DD>Returns the (implementation specific) settings given as nested
 arg elements.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the arguments.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Execute the task
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - is there is a problem in the task execution.</DL>
</DD>
</DL>
<HR>

<A NAME="logAndAddFiles(org.apache.tools.ant.types.Commandline)"><!-- --></A><H3>
logAndAddFiles</H3>
<PRE>
public void <B>logAndAddFiles</B>(<A HREF="../../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;cmd)</PRE>
<DL>
<DD>Logs the compilation parameters, adds the files to compile and logs the
 &quot;niceSourceList&quot;
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>cmd</CODE> - the command line.</DL>
</DD>
</DL>
<HR>

<A NAME="logAndAddFilesToCompile(org.apache.tools.ant.types.Commandline)"><!-- --></A><H3>
logAndAddFilesToCompile</H3>
<PRE>
protected void <B>logAndAddFilesToCompile</B>(<A HREF="../../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;cmd)</PRE>
<DL>
<DD>Logs the compilation parameters, adds the files to compile and logs the
 &quot;niceSourceList&quot;
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>cmd</CODE> - the command line to add parameters to.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/EchoProperties.FormatAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Javah.ClassArgument.html" title="class in org.apache.tools.ant.taskdefs.optional"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/Javah.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Javah.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.Task">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
