<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:29 EST 2006 -->
<TITLE>
JUnitTask (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.optional.junit.JUnitTask class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="JUnitTask (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.ForkMode.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="JUnitTask.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.optional.junit</FONT>
<BR>
Class JUnitTask</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.optional.junit.JUnitTask</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>JUnitTask</B><DT>extends <A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></DL>
</PRE>

<P>
Runs JUnit tests.

 <p> JUnit is a framework to create unit tests. It has been initially
 created by Erich Gamma and Kent Beck.  JUnit can be found at <a
 href="http://www.junit.org">http://www.junit.org</a>.

 <p> <code>JUnitTask</code> can run a single specific
 <code>JUnitTest</code> using the <code>test</code> element.</p>
 For example, the following target <code><pre>
   &lt;target name="test-int-chars" depends="jar-test"&gt;
       &lt;echo message="testing international characters"/&gt;
       &lt;junit printsummary="no" haltonfailure="yes" fork="false"&gt;
           &lt;classpath refid="classpath"/&gt;
           &lt;formatter type="plain" usefile="false" /&gt;
           &lt;test name="org.apache.ecs.InternationalCharTest" /&gt;
       &lt;/junit&gt;
   &lt;/target&gt;
 </pre></code>
 <p>runs a single junit test
 (<code>org.apache.ecs.InternationalCharTest</code>) in the current
 VM using the path with id <code>classpath</code> as classpath and
 presents the results formatted using the standard
 <code>plain</code> formatter on the command line.</p>

 <p> This task can also run batches of tests.  The
 <code>batchtest</code> element creates a <code>BatchTest</code>
 based on a fileset.  This allows, for example, all classes found in
 directory to be run as testcases.</p>

 <p>For example,</p><code><pre>
 &lt;target name="run-tests" depends="dump-info,compile-tests" if="junit.present"&gt;
   &lt;junit printsummary="no" haltonfailure="yes" fork="${junit.fork}"&gt;
     &lt;jvmarg value="-classic"/&gt;
     &lt;classpath refid="tests-classpath"/&gt;
     &lt;sysproperty key="build.tests" value="${build.tests}"/&gt;
     &lt;formatter type="brief" usefile="false" /&gt;
     &lt;batchtest&gt;
       &lt;fileset dir="${tests.dir}"&gt;
         &lt;include name="**&#047;*Test*" /&gt;
       &lt;/fileset&gt;
     &lt;/batchtest&gt;
   &lt;/junit&gt;
 &lt;/target&gt;
 </pre></code>
 <p>this target finds any classes with a <code>test</code> directory
 anywhere in their path (under the top <code>${tests.dir}</code>, of
 course) and creates <code>JUnitTest</code>'s for each one.</p>

 <p> Of course, <code>&lt;junit&gt;</code> and
 <code>&lt;batch&gt;</code> elements can be combined for more
 complex tests. For an example, see the ant <code>build.xml</code>
 target <code>run-tests</code> (the second example is an edited
 version).</p>

 <p> To spawn a new Java VM to prevent interferences between
 different testcases, you need to enable <code>fork</code>.  A
 number of attributes and elements allow you to set up how this JVM
 runs.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><CODE>JUnitTest</CODE></A>, 
<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BatchTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><CODE>BatchTest</CODE></A></DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.ForkMode.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.ForkMode</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;These are the different forking options</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.JUnitLogOutputStream.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.JUnitLogOutputStream</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A stream handler for handling the junit task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.JUnitLogStreamHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.JUnitLogStreamHandler</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A log stream handler for junit.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.SummaryAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.SummaryAttribute</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Print summary enumeration values.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.TestResultHolder.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.TestResultHolder</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A value class that contains thee result of a test.</TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#TESTLISTENER_PREFIX">TESTLISTENER_PREFIX</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#JUnitTask()">JUnitTask</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new JUnitRunner and enables fork of a new Java VM.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#actOnTestResult(int, boolean, org.apache.tools.ant.taskdefs.optional.junit.JUnitTest, java.lang.String)">actOnTestResult</A></B>(int&nbsp;exitValue,
                boolean&nbsp;wasKilled,
                <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</A>&nbsp;test,
                java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logs information about failed tests, potentially stops
 processing (by throwing a BuildException) if a failure/error
 occurred or sets a property.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#actOnTestResult(org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.TestResultHolder, org.apache.tools.ant.taskdefs.optional.junit.JUnitTest, java.lang.String)">actOnTestResult</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.TestResultHolder.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.TestResultHolder</A>&nbsp;result,
                <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</A>&nbsp;test,
                java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logs information about failed tests, potentially stops
 processing (by throwing a BuildException) if a failure/error
 occurred or sets a property.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#addAssertions(org.apache.tools.ant.types.Assertions)">addAssertions</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Assertions.html" title="class in org.apache.tools.ant.types">Assertions</A>&nbsp;asserts)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Assertions to enable in this program (if fork=true)</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#addClasspathEntry(java.lang.String)">addClasspathEntry</A></B>(java.lang.String&nbsp;resource)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Search for the given resource and add the directory or archive
 that contains it to the classpath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#addConfiguredSysproperty(org.apache.tools.ant.types.Environment.Variable)">addConfiguredSysproperty</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</A>&nbsp;sysp)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a system property that tests can access.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#addEnv(org.apache.tools.ant.types.Environment.Variable)">addEnv</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</A>&nbsp;var)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds an environment variable; used when forking.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#addFormatter(org.apache.tools.ant.taskdefs.optional.junit.FormatterElement)">addFormatter</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/FormatterElement.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</A>&nbsp;fe)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a new formatter to all tests of this task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#addSysproperty(org.apache.tools.ant.types.Environment.Variable)">addSysproperty</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</A>&nbsp;sysp)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since ant 1.6</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#addSyspropertyset(org.apache.tools.ant.types.PropertySet)">addSyspropertyset</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/types/PropertySet.html" title="class in org.apache.tools.ant.types">PropertySet</A>&nbsp;sysp)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a set of properties that will be used as system properties
 that tests can access.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#addTest(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)">addTest</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</A>&nbsp;test)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a new single testcase.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Enumeration</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#allTests()">allTests</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return an enumeration listing each test, then each batchtest</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BatchTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BatchTest</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#createBatchTest()">createBatchTest</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a set of tests based on pattern matching.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#createBootclasspath()">createBootclasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a path to the bootclasspath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#createClasspath()">createClasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds path to classpath used for tests.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#createJvmarg()">createJvmarg</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a JVM argument; ignored if not forking.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/types/Permissions.html" title="class in org.apache.tools.ant.types">Permissions</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#createPermissions()">createPermissions</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the permissions for the application run inside the same JVM.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs">ExecuteWatchdog</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#createWatchdog()">createWatchdog</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Runs the testcase.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#execute(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)">execute</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</A>&nbsp;arg)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Run the tests.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#execute(java.util.List)">execute</A></B>(java.util.List&nbsp;tests)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Execute a list of tests in a single forked Java VM.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Collection</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#executeOrQueue(java.util.Enumeration, boolean)">executeOrQueue</A></B>(java.util.Enumeration&nbsp;testList,
               boolean&nbsp;runIndividual)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Executes all tests that don't need to be forked (or all tests
 if the runIndividual argument is true.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../../../org/apache/tools/ant/types/CommandlineJava.html" title="class in org.apache.tools.ant.types">CommandlineJava</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#getCommandline()">getCommandline</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the command line used to run the tests.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.OutputStream</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#getDefaultOutput()">getDefaultOutput</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the default output for a formatter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Enumeration</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#getIndividualTests()">getIndividualTests</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Merge all individual tests from the batchtest with all individual tests
 and return an enumeration over all <tt>JUnitTest</tt>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#getOutput(org.apache.tools.ant.taskdefs.optional.junit.FormatterElement, org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)">getOutput</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/FormatterElement.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</A>&nbsp;fe,
          <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</A>&nbsp;test)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If the formatter sends output to a file, return that file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A></B>(java.lang.String&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Pass output sent to System.err to the TestRunner so it can
 collect it for the formatters.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A></B>(java.lang.String&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Pass output sent to System.err to the TestRunner so it can
 collect it for the formatters.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#handleFlush(java.lang.String)">handleFlush</A></B>(java.lang.String&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Pass output sent to System.out to the TestRunner so it can
 collect ot for the formatters.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#handleInput(byte[], int, int)">handleInput</A></B>(byte[]&nbsp;buffer,
            int&nbsp;offset,
            int&nbsp;length)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Handle an input request by this task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#handleOutput(java.lang.String)">handleOutput</A></B>(java.lang.String&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Pass output sent to System.out to the TestRunner so it can
 collect ot for the formatters.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#init()">init</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds the jars or directories containing Ant, this task and
 JUnit to the classpath - this should make the forked JVM work
 without having to specify them directly.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setCloneVm(boolean)">setCloneVm</A></B>(boolean&nbsp;cloneVm)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If set, system properties will be copied to the cloned VM - as
 well as the bootclasspath unless you have explicitly specified
 a bootclaspath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setDir(java.io.File)">setDir</A></B>(java.io.File&nbsp;dir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The directory to invoke the VM in.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setErrorProperty(java.lang.String)">setErrorProperty</A></B>(java.lang.String&nbsp;propertyName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Property to set to "true" if there is a error in a test.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setFailureProperty(java.lang.String)">setFailureProperty</A></B>(java.lang.String&nbsp;propertyName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Property to set to "true" if there is a failure in a test.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setFiltertrace(boolean)">setFiltertrace</A></B>(boolean&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, smartly filter the stack frames of
 JUnit errors and failures before reporting them.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setFork(boolean)">setFork</A></B>(boolean&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, JVM should be forked for each test.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setForkMode(org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.ForkMode)">setForkMode</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.ForkMode.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.ForkMode</A>&nbsp;mode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the behavior when <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setFork(boolean)"><CODE>fork</CODE></A> fork has been enabled.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setHaltonerror(boolean)">setHaltonerror</A></B>(boolean&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, stop the build process when there is an error in a test.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setHaltonfailure(boolean)">setHaltonfailure</A></B>(boolean&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, stop the build process if a test fails
 (errors are considered failures as well).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setIncludeantruntime(boolean)">setIncludeantruntime</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, include ant.jar, optional.jar and junit.jar in the forked VM.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setJvm(java.lang.String)">setJvm</A></B>(java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The command used to invoke the Java Virtual Machine,
 default is 'java'.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setMaxmemory(java.lang.String)">setMaxmemory</A></B>(java.lang.String&nbsp;max)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the maximum memory to be used by all forked JVMs.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setNewenvironment(boolean)">setNewenvironment</A></B>(boolean&nbsp;newenv)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, use a new environment when forked.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setOutputToFormatters(boolean)">setOutputToFormatters</A></B>(boolean&nbsp;outputToFormatters)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, send any output generated by tests to the formatters.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setPrintsummary(org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.SummaryAttribute)">setPrintsummary</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.SummaryAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.SummaryAttribute</A>&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, print one-line statistics for each test, or "withOutAndErr"
 to also show standard output and error.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setReloading(boolean)">setReloading</A></B>(boolean&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, force ant to re-classload all classes for each JUnit TestCase</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setShowOutput(boolean)">setShowOutput</A></B>(boolean&nbsp;showOutput)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, send any output generated by tests to Ant's logging system
 as well as to the formatters.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setTempdir(java.io.File)">setTempdir</A></B>(java.io.File&nbsp;tmpDir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Where Ant should place temporary files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setTimeout(java.lang.Integer)">setTimeout</A></B>(java.lang.Integer&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the timeout value (in milliseconds).</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="TESTLISTENER_PREFIX"><!-- --></A><H3>
TESTLISTENER_PREFIX</H3>
<PRE>
public static final java.lang.String <B>TESTLISTENER_PREFIX</B></PRE>
<DL>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.TESTLISTENER_PREFIX">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="JUnitTask()"><!-- --></A><H3>
JUnitTask</H3>
<PRE>
public <B>JUnitTask</B>()
          throws java.lang.Exception</PRE>
<DL>
<DD>Creates a new JUnitRunner and enables fork of a new Java VM.
<P>
<DL>

<DT><B>Throws:</B>
<DD><CODE>java.lang.Exception</CODE> - under ??? circumstances<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setReloading(boolean)"><!-- --></A><H3>
setReloading</H3>
<PRE>
public void <B>setReloading</B>(boolean&nbsp;value)</PRE>
<DL>
<DD>If true, force ant to re-classload all classes for each JUnit TestCase
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - force class reloading for each test case</DL>
</DD>
</DL>
<HR>

<A NAME="setFiltertrace(boolean)"><!-- --></A><H3>
setFiltertrace</H3>
<PRE>
public void <B>setFiltertrace</B>(boolean&nbsp;value)</PRE>
<DL>
<DD>If true, smartly filter the stack frames of
 JUnit errors and failures before reporting them.

 <p>This property is applied on all BatchTest (batchtest) and
 JUnitTest (test) however it can possibly be overridden by their
 own properties.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - <tt>false</tt> if it should not filter, otherwise
 <tt>true<tt><DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setHaltonerror(boolean)"><!-- --></A><H3>
setHaltonerror</H3>
<PRE>
public void <B>setHaltonerror</B>(boolean&nbsp;value)</PRE>
<DL>
<DD>If true, stop the build process when there is an error in a test.
 This property is applied on all BatchTest (batchtest) and JUnitTest
 (test) however it can possibly be overridden by their own
 properties.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - <tt>true</tt> if it should halt, otherwise
 <tt>false</tt><DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setErrorProperty(java.lang.String)"><!-- --></A><H3>
setErrorProperty</H3>
<PRE>
public void <B>setErrorProperty</B>(java.lang.String&nbsp;propertyName)</PRE>
<DL>
<DD>Property to set to "true" if there is a error in a test.

 <p>This property is applied on all BatchTest (batchtest) and
 JUnitTest (test), however, it can possibly be overriden by
 their own properties.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>propertyName</CODE> - the name of the property to set in the
 event of an error.<DT><B>Since:</B></DT>
  <DD>Ant 1.4</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setHaltonfailure(boolean)"><!-- --></A><H3>
setHaltonfailure</H3>
<PRE>
public void <B>setHaltonfailure</B>(boolean&nbsp;value)</PRE>
<DL>
<DD>If true, stop the build process if a test fails
 (errors are considered failures as well).
 This property is applied on all BatchTest (batchtest) and
 JUnitTest (test) however it can possibly be overridden by their
 own properties.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - <tt>true</tt> if it should halt, otherwise
 <tt>false</tt><DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setFailureProperty(java.lang.String)"><!-- --></A><H3>
setFailureProperty</H3>
<PRE>
public void <B>setFailureProperty</B>(java.lang.String&nbsp;propertyName)</PRE>
<DL>
<DD>Property to set to "true" if there is a failure in a test.

 <p>This property is applied on all BatchTest (batchtest) and
 JUnitTest (test), however, it can possibly be overriden by
 their own properties.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>propertyName</CODE> - the name of the property to set in the
 event of an failure.<DT><B>Since:</B></DT>
  <DD>Ant 1.4</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setFork(boolean)"><!-- --></A><H3>
setFork</H3>
<PRE>
public void <B>setFork</B>(boolean&nbsp;value)</PRE>
<DL>
<DD>If true, JVM should be forked for each test.

 <p>It avoids interference between testcases and possibly avoids
 hanging the build.  this property is applied on all BatchTest
 (batchtest) and JUnitTest (test) however it can possibly be
 overridden by their own properties.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - <tt>true</tt> if a JVM should be forked, otherwise
 <tt>false</tt><DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setTimeout(java.lang.Integer)"><CODE>setTimeout(java.lang.Integer)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setForkMode(org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.ForkMode)"><!-- --></A><H3>
setForkMode</H3>
<PRE>
public void <B>setForkMode</B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.ForkMode.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.ForkMode</A>&nbsp;mode)</PRE>
<DL>
<DD>Set the behavior when <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setFork(boolean)"><CODE>fork</CODE></A> fork has been enabled.

 <p>Possible values are "once", "perTest" and "perBatch".  If
 set to "once", only a single Java VM will be forked for all
 tests, with "perTest" (the default) each test will run in a
 fresh Java VM and "perBatch" will run all tests from the same
 &lt;batchtest&gt; in the same Java VM.</p>

 <p>This attribute will be ignored if tests run in the same VM
 as Ant.</p>

 <p>Only tests with the same configuration of haltonerror,
 haltonfailure, errorproperty, failureproperty and filtertrace
 can share a forked Java VM, so even if you set the value to
 "once", Ant may need to fork mutliple VMs.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>mode</CODE> - the mode to use.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setPrintsummary(org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.SummaryAttribute)"><!-- --></A><H3>
setPrintsummary</H3>
<PRE>
public void <B>setPrintsummary</B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.SummaryAttribute.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.SummaryAttribute</A>&nbsp;value)</PRE>
<DL>
<DD>If true, print one-line statistics for each test, or "withOutAndErr"
 to also show standard output and error.

 Can take the values on, off, and withOutAndErr.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - <tt>true</tt> to print a summary,
 <tt>withOutAndErr</tt> to include the test&apos;s output as
 well, <tt>false</tt> otherwise.<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/SummaryJUnitResultFormatter.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><CODE>SummaryJUnitResultFormatter</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setTimeout(java.lang.Integer)"><!-- --></A><H3>
setTimeout</H3>
<PRE>
public void <B>setTimeout</B>(java.lang.Integer&nbsp;value)</PRE>
<DL>
<DD>Set the timeout value (in milliseconds).

 <p>If the test is running for more than this value, the test
 will be canceled. (works only when in 'fork' mode).</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - the maximum time (in milliseconds) allowed before
 declaring the test as 'timed-out'<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setFork(boolean)"><CODE>setFork(boolean)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setMaxmemory(java.lang.String)"><!-- --></A><H3>
setMaxmemory</H3>
<PRE>
public void <B>setMaxmemory</B>(java.lang.String&nbsp;max)</PRE>
<DL>
<DD>Set the maximum memory to be used by all forked JVMs.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>max</CODE> - the value as defined by <tt>-mx</tt> or <tt>-Xmx</tt>
                  in the java command line options.<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setJvm(java.lang.String)"><!-- --></A><H3>
setJvm</H3>
<PRE>
public void <B>setJvm</B>(java.lang.String&nbsp;value)</PRE>
<DL>
<DD>The command used to invoke the Java Virtual Machine,
 default is 'java'. The command is resolved by
 java.lang.Runtime.exec(). Ignored if fork is disabled.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - the new VM to use instead of <tt>java</tt><DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setFork(boolean)"><CODE>setFork(boolean)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="createJvmarg()"><!-- --></A><H3>
createJvmarg</H3>
<PRE>
public <A HREF="../../../../../../../org/apache/tools/ant/types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</A> <B>createJvmarg</B>()</PRE>
<DL>
<DD>Adds a JVM argument; ignored if not forking.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>create a new JVM argument so that any argument can be
 passed to the JVM.<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setFork(boolean)"><CODE>setFork(boolean)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setDir(java.io.File)"><!-- --></A><H3>
setDir</H3>
<PRE>
public void <B>setDir</B>(java.io.File&nbsp;dir)</PRE>
<DL>
<DD>The directory to invoke the VM in. Ignored if no JVM is forked.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>dir</CODE> - the directory to invoke the JVM from.<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#setFork(boolean)"><CODE>setFork(boolean)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="addSysproperty(org.apache.tools.ant.types.Environment.Variable)"><!-- --></A><H3>
addSysproperty</H3>
<PRE>
public void <B>addSysproperty</B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</A>&nbsp;sysp)</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since ant 1.6</I>
<P>
<DD>Adds a system property that tests can access.
 This might be useful to tranfer Ant properties to the
 testcases when JVM forking is not enabled.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sysp</CODE> - environment variable to add<DT><B>Since:</B></DT>
  <DD>Ant 1.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addConfiguredSysproperty(org.apache.tools.ant.types.Environment.Variable)"><!-- --></A><H3>
addConfiguredSysproperty</H3>
<PRE>
public void <B>addConfiguredSysproperty</B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</A>&nbsp;sysp)</PRE>
<DL>
<DD>Adds a system property that tests can access.
 This might be useful to tranfer Ant properties to the
 testcases when JVM forking is not enabled.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sysp</CODE> - new environment variable to add<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addSyspropertyset(org.apache.tools.ant.types.PropertySet)"><!-- --></A><H3>
addSyspropertyset</H3>
<PRE>
public void <B>addSyspropertyset</B>(<A HREF="../../../../../../../org/apache/tools/ant/types/PropertySet.html" title="class in org.apache.tools.ant.types">PropertySet</A>&nbsp;sysp)</PRE>
<DL>
<DD>Adds a set of properties that will be used as system properties
 that tests can access.

 This might be useful to tranfer Ant properties to the
 testcases when JVM forking is not enabled.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sysp</CODE> - set of properties to be added<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="createClasspath()"><!-- --></A><H3>
createClasspath</H3>
<PRE>
public <A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createClasspath</B>()</PRE>
<DL>
<DD>Adds path to classpath used for tests.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>reference to the classpath in the embedded java command line<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="createBootclasspath()"><!-- --></A><H3>
createBootclasspath</H3>
<PRE>
public <A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createBootclasspath</B>()</PRE>
<DL>
<DD>Adds a path to the bootclasspath.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>reference to the bootclasspath in the embedded java command line<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addEnv(org.apache.tools.ant.types.Environment.Variable)"><!-- --></A><H3>
addEnv</H3>
<PRE>
public void <B>addEnv</B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</A>&nbsp;var)</PRE>
<DL>
<DD>Adds an environment variable; used when forking.

 <p>Will be ignored if we are not forking a new VM.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>var</CODE> - environment variable to be added<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setNewenvironment(boolean)"><!-- --></A><H3>
setNewenvironment</H3>
<PRE>
public void <B>setNewenvironment</B>(boolean&nbsp;newenv)</PRE>
<DL>
<DD>If true, use a new environment when forked.

 <p>Will be ignored if we are not forking a new VM.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>newenv</CODE> - boolean indicating if setting a new environment is wished<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addTest(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)"><!-- --></A><H3>
addTest</H3>
<PRE>
public void <B>addTest</B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</A>&nbsp;test)</PRE>
<DL>
<DD>Add a new single testcase.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>test</CODE> - a new single testcase<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><CODE>JUnitTest</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="createBatchTest()"><!-- --></A><H3>
createBatchTest</H3>
<PRE>
public <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BatchTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BatchTest</A> <B>createBatchTest</B>()</PRE>
<DL>
<DD>Adds a set of tests based on pattern matching.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a new instance of a batch test.<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BatchTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><CODE>BatchTest</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="addFormatter(org.apache.tools.ant.taskdefs.optional.junit.FormatterElement)"><!-- --></A><H3>
addFormatter</H3>
<PRE>
public void <B>addFormatter</B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/FormatterElement.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</A>&nbsp;fe)</PRE>
<DL>
<DD>Add a new formatter to all tests of this task.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fe</CODE> - formatter element<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setIncludeantruntime(boolean)"><!-- --></A><H3>
setIncludeantruntime</H3>
<PRE>
public void <B>setIncludeantruntime</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>If true, include ant.jar, optional.jar and junit.jar in the forked VM.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - include ant run time yes or no<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setShowOutput(boolean)"><!-- --></A><H3>
setShowOutput</H3>
<PRE>
public void <B>setShowOutput</B>(boolean&nbsp;showOutput)</PRE>
<DL>
<DD>If true, send any output generated by tests to Ant's logging system
 as well as to the formatters.
 By default only the formatters receive the output.

 <p>Output will always be passed to the formatters and not by
 shown by default.  This option should for example be set for
 tests that are interactive and prompt the Employee to do
 something.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>showOutput</CODE> - if true, send output to Ant's logging system too<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setOutputToFormatters(boolean)"><!-- --></A><H3>
setOutputToFormatters</H3>
<PRE>
public void <B>setOutputToFormatters</B>(boolean&nbsp;outputToFormatters)</PRE>
<DL>
<DD>If true, send any output generated by tests to the formatters.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>outputToFormatters</CODE> - if true, send output to formatters (Default
                           is true).<DT><B>Since:</B></DT>
  <DD>Ant 1.7.0</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addAssertions(org.apache.tools.ant.types.Assertions)"><!-- --></A><H3>
addAssertions</H3>
<PRE>
public void <B>addAssertions</B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Assertions.html" title="class in org.apache.tools.ant.types">Assertions</A>&nbsp;asserts)</PRE>
<DL>
<DD>Assertions to enable in this program (if fork=true)
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>asserts</CODE> - assertion set<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="createPermissions()"><!-- --></A><H3>
createPermissions</H3>
<PRE>
public <A HREF="../../../../../../../org/apache/tools/ant/types/Permissions.html" title="class in org.apache.tools.ant.types">Permissions</A> <B>createPermissions</B>()</PRE>
<DL>
<DD>Sets the permissions for the application run inside the same JVM.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setCloneVm(boolean)"><!-- --></A><H3>
setCloneVm</H3>
<PRE>
public void <B>setCloneVm</B>(boolean&nbsp;cloneVm)</PRE>
<DL>
<DD>If set, system properties will be copied to the cloned VM - as
 well as the bootclasspath unless you have explicitly specified
 a bootclaspath.

 <p>Doesn't have any effect unless fork is true.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>cloneVm</CODE> - a <code>boolean</code> value.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setTempdir(java.io.File)"><!-- --></A><H3>
setTempdir</H3>
<PRE>
public void <B>setTempdir</B>(java.io.File&nbsp;tmpDir)</PRE>
<DL>
<DD>Where Ant should place temporary files.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>tmpDir</CODE> - location where temporary files should go to<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="init()"><!-- --></A><H3>
init</H3>
<PRE>
public void <B>init</B>()</PRE>
<DL>
<DD>Adds the jars or directories containing Ant, this task and
 JUnit to the classpath - this should make the forked JVM work
 without having to specify them directly.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#init()">init</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.4</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Runs the testcase.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - in case of test failures or errors<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="execute(org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)"><!-- --></A><H3>
execute</H3>
<PRE>
protected void <B>execute</B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</A>&nbsp;arg)
                throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Run the tests.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>arg</CODE> - one JunitTest
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - in case of test failures or errors</DL>
</DD>
</DL>
<HR>

<A NAME="execute(java.util.List)"><!-- --></A><H3>
execute</H3>
<PRE>
protected void <B>execute</B>(java.util.List&nbsp;tests)
                throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Execute a list of tests in a single forked Java VM.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>tests</CODE> - the list of tests to execute.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error.</DL>
</DD>
</DL>
<HR>

<A NAME="handleOutput(java.lang.String)"><!-- --></A><H3>
handleOutput</H3>
<PRE>
protected void <B>handleOutput</B>(java.lang.String&nbsp;output)</PRE>
<DL>
<DD>Pass output sent to System.out to the TestRunner so it can
 collect ot for the formatters.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - output coming from System.out<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="handleInput(byte[], int, int)"><!-- --></A><H3>
handleInput</H3>
<PRE>
protected int <B>handleInput</B>(byte[]&nbsp;buffer,
                          int&nbsp;offset,
                          int&nbsp;length)
                   throws java.io.IOException</PRE>
<DL>
<DD>Handle an input request by this task.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>buffer</CODE> - the buffer into which data is to be read.<DD><CODE>offset</CODE> - the offset into the buffer at which data is stored.<DD><CODE>length</CODE> - the amount of data to read.
<DT><B>Returns:</B><DD>the number of bytes read.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the data cannot be read.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)"><CODE>This implementation delegates to a runner if it
 present.</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="handleFlush(java.lang.String)"><!-- --></A><H3>
handleFlush</H3>
<PRE>
protected void <B>handleFlush</B>(java.lang.String&nbsp;output)</PRE>
<DL>
<DD>Pass output sent to System.out to the TestRunner so it can
 collect ot for the formatters.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - output coming from System.out<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="handleErrorOutput(java.lang.String)"><!-- --></A><H3>
handleErrorOutput</H3>
<PRE>
public void <B>handleErrorOutput</B>(java.lang.String&nbsp;output)</PRE>
<DL>
<DD>Pass output sent to System.err to the TestRunner so it can
 collect it for the formatters.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - output coming from System.err<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="handleErrorFlush(java.lang.String)"><!-- --></A><H3>
handleErrorFlush</H3>
<PRE>
public void <B>handleErrorFlush</B>(java.lang.String&nbsp;output)</PRE>
<DL>
<DD>Pass output sent to System.err to the TestRunner so it can
 collect it for the formatters.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - coming from System.err<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="createWatchdog()"><!-- --></A><H3>
createWatchdog</H3>
<PRE>
protected <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs">ExecuteWatchdog</A> <B>createWatchdog</B>()
                                  throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD><tt>null</tt> if there is a timeout value, otherwise the
 watchdog instance.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - under unspecified circumstances<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getDefaultOutput()"><!-- --></A><H3>
getDefaultOutput</H3>
<PRE>
protected java.io.OutputStream <B>getDefaultOutput</B>()</PRE>
<DL>
<DD>Get the default output for a formatter.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>default output stream for a formatter<DT><B>Since:</B></DT>
  <DD>Ant 1.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getIndividualTests()"><!-- --></A><H3>
getIndividualTests</H3>
<PRE>
protected java.util.Enumeration <B>getIndividualTests</B>()</PRE>
<DL>
<DD>Merge all individual tests from the batchtest with all individual tests
 and return an enumeration over all <tt>JUnitTest</tt>.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>enumeration over individual tests<DT><B>Since:</B></DT>
  <DD>Ant 1.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="allTests()"><!-- --></A><H3>
allTests</H3>
<PRE>
protected java.util.Enumeration <B>allTests</B>()</PRE>
<DL>
<DD>return an enumeration listing each test, then each batchtest
<P>
<DD><DL>

<DT><B>Returns:</B><DD>enumeration<DT><B>Since:</B></DT>
  <DD>Ant 1.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getOutput(org.apache.tools.ant.taskdefs.optional.junit.FormatterElement, org.apache.tools.ant.taskdefs.optional.junit.JUnitTest)"><!-- --></A><H3>
getOutput</H3>
<PRE>
protected java.io.File <B>getOutput</B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/FormatterElement.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</A>&nbsp;fe,
                                 <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</A>&nbsp;test)</PRE>
<DL>
<DD>If the formatter sends output to a file, return that file.
 null otherwise.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fe</CODE> - formatter element<DD><CODE>test</CODE> - one JUnit test
<DT><B>Returns:</B><DD>file reference<DT><B>Since:</B></DT>
  <DD>Ant 1.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addClasspathEntry(java.lang.String)"><!-- --></A><H3>
addClasspathEntry</H3>
<PRE>
protected boolean <B>addClasspathEntry</B>(java.lang.String&nbsp;resource)</PRE>
<DL>
<DD>Search for the given resource and add the directory or archive
 that contains it to the classpath.

 <p>Doesn't work for archives in JDK 1.1 as the URL returned by
 getResource doesn't contain the name of the archive.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>resource</CODE> - resource that one wants to lookup
<DT><B>Returns:</B><DD>true if something was in fact added<DT><B>Since:</B></DT>
  <DD>Ant 1.4</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getCommandline()"><!-- --></A><H3>
getCommandline</H3>
<PRE>
protected <A HREF="../../../../../../../org/apache/tools/ant/types/CommandlineJava.html" title="class in org.apache.tools.ant.types">CommandlineJava</A> <B>getCommandline</B>()</PRE>
<DL>
<DD>Get the command line used to run the tests.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the command line.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="executeOrQueue(java.util.Enumeration, boolean)"><!-- --></A><H3>
executeOrQueue</H3>
<PRE>
protected java.util.Collection <B>executeOrQueue</B>(java.util.Enumeration&nbsp;testList,
                                              boolean&nbsp;runIndividual)</PRE>
<DL>
<DD>Executes all tests that don't need to be forked (or all tests
 if the runIndividual argument is true.  Returns a collection of
 lists of tests that share the same VM configuration and haven't
 been executed yet.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>testList</CODE> - the list of tests to be executed or queued.<DD><CODE>runIndividual</CODE> - if true execute each test individually.
<DT><B>Returns:</B><DD>a list of tasks to be executed.<DT><B>Since:</B></DT>
  <DD>1.6.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="actOnTestResult(int, boolean, org.apache.tools.ant.taskdefs.optional.junit.JUnitTest, java.lang.String)"><!-- --></A><H3>
actOnTestResult</H3>
<PRE>
protected void <B>actOnTestResult</B>(int&nbsp;exitValue,
                               boolean&nbsp;wasKilled,
                               <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</A>&nbsp;test,
                               java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Logs information about failed tests, potentially stops
 processing (by throwing a BuildException) if a failure/error
 occurred or sets a property.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>exitValue</CODE> - the exitValue of the test.<DD><CODE>wasKilled</CODE> - if true, the test had been killed.<DD><CODE>test</CODE> - the test in question.<DD><CODE>name</CODE> - the name of the test.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="actOnTestResult(org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.TestResultHolder, org.apache.tools.ant.taskdefs.optional.junit.JUnitTest, java.lang.String)"><!-- --></A><H3>
actOnTestResult</H3>
<PRE>
protected void <B>actOnTestResult</B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.TestResultHolder.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.TestResultHolder</A>&nbsp;result,
                               <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTest</A>&nbsp;test,
                               java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Logs information about failed tests, potentially stops
 processing (by throwing a BuildException) if a failure/error
 occurred or sets a property.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>result</CODE> - the result of the test.<DD><CODE>test</CODE> - the test in question.<DD><CODE>name</CODE> - the name of the test.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitResultFormatter.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.ForkMode.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="JUnitTask.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
