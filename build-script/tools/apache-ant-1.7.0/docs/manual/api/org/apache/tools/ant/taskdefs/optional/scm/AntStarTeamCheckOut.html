<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:29 EST 2006 -->
<TITLE>
AntStarTeamCheckOut (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.optional.scm.AntStarTeamCheckOut class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="AntStarTeamCheckOut (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV CLASS&nbsp;
&nbsp;NEXT CLASS</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="AntStarTeamCheckOut.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.optional.scm</FONT>
<BR>
Class AntStarTeamCheckOut</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.optional.scm.AntStarTeamCheckOut</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>AntStarTeamCheckOut</B><DT>extends <A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></DL>
</PRE>

<P>
Checks out files from a specific StarTeam server, project, view, and
 folder.
 <BR><BR>
 This program logs in to a StarTeam server and opens up the specified
 project and view.  Then, it searches through that view for the given
 folder (or, if you prefer, it uses the root folder).  Beginning with
 that folder and optionally continuing recursivesly, AntStarTeamCheckOut
 compares each file with your include and exclude filters and checks it
 out only if appropriate.
 <BR><BR>
 Checked out files go to a directory you specify under the subfolder
 named for the default StarTeam path to the view.  That is, if you
 entered /home/<USER>/work as the target folder, your project was named
 "OurProject," the given view was named "TestView," and that view is
 stored by default at "C:\projects\Test," your files would be checked
 out to /home/<USER>/work/Test."  I avoided using the project name in
 the path because you may want to keep several versions of the same
 project on your computer, and I didn't want to use the view name, as
 there may be many "Test" or "Version 1.0" views, for example.  This
 system's success, of course, depends on what you set the default path
 to in StarTeam.
 <BR><BR>
 You can set AntStarTeamCheckOut to verbose or quiet mode.  Also, it has
 a safeguard against overwriting the files on your computer:  If the
 target directory you specify already exists, the program will throw a
 BuildException.  To override the exception, set <CODE>force</CODE> to
 true.
 <BR><BR>
 <B>This program makes use of functions from the StarTeam API.  As a result
 AntStarTeamCheckOut is available only to licensed users of StarTeam and
 requires the StarTeam SDK to function.  You must have
 <CODE>starteam-sdk.jar</CODE> in your classpath to run this program.
 For more information about the StarTeam API and how to license it, see
 the link below.</B>
<P>

<P>
<DL>
<DT><B>Version:</B></DT>
  <DD>1.0</DD>
<DT><B>See Also:</B><DD><A HREF="http://www.starbase.com/">StarBase Web Site</A></DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#DEFAULT_EXCLUDESETTING">DEFAULT_EXCLUDESETTING</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This disables the exclude filter by default.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#DEFAULT_FOLDERSETTING">DEFAULT_FOLDERSETTING</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The default folder to search; the root folder.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#DEFAULT_INCLUDESETTING">DEFAULT_INCLUDESETTING</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This constant sets the filter to include all files.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#AntStarTeamCheckOut()">AntStarTeamCheckOut</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#checkParameters()">checkParameters</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Check if the attributes/elements are correct.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Do the execution.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#formatForDisplay(com.starbase.starteam.Property, java.lang.Object)">formatForDisplay</A></B>(com.starbase.starteam.Property&nbsp;p,
                 java.lang.Object&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Formats a property value for display to the Employee.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getExcludes()">getExcludes</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the patterns from the exclude filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getFolderName()">getFolderName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the <CODE>folderName</CODE> attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getForce()">getForce</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the <CODE>force</CODE> attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getIncludes()">getIncludes</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the patterns from the include filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getPassword()">getPassword</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the <CODE>password</CODE> attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;com.starbase.starteam.Property</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getPrimaryDescriptor(com.starbase.starteam.Type)">getPrimaryDescriptor</A></B>(com.starbase.starteam.Type&nbsp;t)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the primary descriptor of the given item type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getProjectName()">getProjectName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the <CODE>projectName</CODE> attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getRecursion()">getRecursion</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the <CODE>recursion</CODE> attribute, which tells
 AntStarTeamCheckOut whether to search subfolders when checking out
 files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;com.starbase.starteam.Property</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getSecondaryDescriptor(com.starbase.starteam.Type)">getSecondaryDescriptor</A></B>(com.starbase.starteam.Type&nbsp;t)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the secondary descriptor of the given item type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;com.starbase.starteam.Server</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getServer()">getServer</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates and logs in to a StarTeam server.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getServerName()">getServerName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the <CODE>serverName</CODE> attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getServerPort()">getServerPort</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the <CODE>serverPort</CODE> attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getTargetFolder()">getTargetFolder</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the <CODE>targetFolder</CODE> attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getTargetFolderAbsolute()">getTargetFolderAbsolute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;returns whether the StarTeam default path is factored into calculated
 target path locations (false) or whether targetFolder is an absolute
 mapping to the root folder named by folderName</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getUsername()">getUsername</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the <CODE>username</CODE> attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getVerbose()">getVerbose</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the <CODE>verbose</CODE> attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getViewName()">getViewName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the <CODE>viewName</CODE> attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#matchPatterns(java.lang.String, java.lang.String)">matchPatterns</A></B>(java.lang.String&nbsp;patterns,
              java.lang.String&nbsp;pName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenient method to see if a string match a one pattern in given set
 of space-separated patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#runFolder(com.starbase.starteam.Server, com.starbase.starteam.Project, com.starbase.starteam.View, com.starbase.starteam.Type, com.starbase.starteam.Folder, java.io.File)">runFolder</A></B>(com.starbase.starteam.Server&nbsp;s,
          com.starbase.starteam.Project&nbsp;p,
          com.starbase.starteam.View&nbsp;v,
          com.starbase.starteam.Type&nbsp;t,
          com.starbase.starteam.Folder&nbsp;f,
          java.io.File&nbsp;tgt)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Searches for files in the given folder.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#runItem(com.starbase.starteam.Server, com.starbase.starteam.Project, com.starbase.starteam.View, com.starbase.starteam.Type, com.starbase.starteam.Folder, com.starbase.starteam.Item, java.io.File)">runItem</A></B>(com.starbase.starteam.Server&nbsp;s,
        com.starbase.starteam.Project&nbsp;p,
        com.starbase.starteam.View&nbsp;v,
        com.starbase.starteam.Type&nbsp;t,
        com.starbase.starteam.Folder&nbsp;f,
        com.starbase.starteam.Item&nbsp;item,
        java.io.File&nbsp;tgt)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Check out one file if it matches the include filter but not the exclude
 filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#runProject(com.starbase.starteam.Server, com.starbase.starteam.Project)">runProject</A></B>(com.starbase.starteam.Server&nbsp;s,
           com.starbase.starteam.Project&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Searches for the given view in the project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#runServer(com.starbase.starteam.Server)">runServer</A></B>(com.starbase.starteam.Server&nbsp;s)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Searches for the specified project on the server.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#runType(com.starbase.starteam.Server, com.starbase.starteam.Project, com.starbase.starteam.View, com.starbase.starteam.Type)">runType</A></B>(com.starbase.starteam.Server&nbsp;s,
        com.starbase.starteam.Project&nbsp;p,
        com.starbase.starteam.View&nbsp;v,
        com.starbase.starteam.Type&nbsp;t)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Searches for folders in the given view.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setExcludes(java.lang.String)">setExcludes</A></B>(java.lang.String&nbsp;excludes)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the exclude filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setFolderName(java.lang.String)">setFolderName</A></B>(java.lang.String&nbsp;folderName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the <CODE>folderName</CODE> attribute to the given value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setForce(boolean)">setForce</A></B>(boolean&nbsp;force)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the <CODE>force</CODE> attribute to the given value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setIncludes(java.lang.String)">setIncludes</A></B>(java.lang.String&nbsp;includes)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the include filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setPassword(java.lang.String)">setPassword</A></B>(java.lang.String&nbsp;password)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the <CODE>password</CODE> attribute to the given value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setProjectName(java.lang.String)">setProjectName</A></B>(java.lang.String&nbsp;projectName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the <CODE>projectName</CODE> attribute to the given value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setRecursion(boolean)">setRecursion</A></B>(boolean&nbsp;recursion)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Turns recursion on or off.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setServerName(java.lang.String)">setServerName</A></B>(java.lang.String&nbsp;serverName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the <CODE>serverName</CODE> attribute to the given value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setServerPort(int)">setServerPort</A></B>(int&nbsp;serverPort)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the <CODE>serverPort</CODE> attribute to the given value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setTargetFolder(java.lang.String)">setTargetFolder</A></B>(java.lang.String&nbsp;targetFolder)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the <CODE>targetFolder</CODE> attribute to the given value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setTargetFolderAbsolute(boolean)">setTargetFolderAbsolute</A></B>(boolean&nbsp;targetFolderAbsolute)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sets the property that indicates whether or not the Star Team "default
 folder" is to be used when calculation paths for items on the target
 (false) or if targetFolder is an absolute mapping to the root folder
 named by foldername.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setUsername(java.lang.String)">setUsername</A></B>(java.lang.String&nbsp;username)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the <CODE>username</CODE> attribute to the given value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setVerbose(boolean)">setVerbose</A></B>(boolean&nbsp;verbose)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the <CODE>verbose</CODE> attribute to the given value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setViewName(java.lang.String)">setViewName</A></B>(java.lang.String&nbsp;viewName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the <CODE>viewName</CODE> attribute to the given value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#shouldCheckout(java.lang.String)">shouldCheckout</A></B>(java.lang.String&nbsp;pName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Look if the file should be checked out.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="DEFAULT_INCLUDESETTING"><!-- --></A><H3>
DEFAULT_INCLUDESETTING</H3>
<PRE>
public static final java.lang.String <B>DEFAULT_INCLUDESETTING</B></PRE>
<DL>
<DD>This constant sets the filter to include all files. This default has
 the same result as <CODE>setIncludes("*")</CODE>.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getIncludes()"><CODE>getIncludes()</CODE></A>, 
<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setIncludes(java.lang.String)"><CODE>setIncludes(String includes)</CODE></A>, 
<A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.scm.AntStarTeamCheckOut.DEFAULT_INCLUDESETTING">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_EXCLUDESETTING"><!-- --></A><H3>
DEFAULT_EXCLUDESETTING</H3>
<PRE>
public static final java.lang.String <B>DEFAULT_EXCLUDESETTING</B></PRE>
<DL>
<DD>This disables the exclude filter by default. In other words, no files
 are excluded. This setting is equivalent to <CODE>setExcludes(null)</CODE>
 .
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getExcludes()"><CODE>getExcludes()</CODE></A>, 
<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setExcludes(java.lang.String)"><CODE>setExcludes(String excludes)</CODE></A></DL>
</DL>
<HR>

<A NAME="DEFAULT_FOLDERSETTING"><!-- --></A><H3>
DEFAULT_FOLDERSETTING</H3>
<PRE>
public static final java.lang.String <B>DEFAULT_FOLDERSETTING</B></PRE>
<DL>
<DD>The default folder to search; the root folder. Since
 AntStarTeamCheckOut searches subfolders, by default it processes an
 entire view.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getFolderName()"><CODE>getFolderName()</CODE></A>, 
<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setFolderName(java.lang.String)"><CODE>setFolderName(String folderName)</CODE></A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="AntStarTeamCheckOut()"><!-- --></A><H3>
AntStarTeamCheckOut</H3>
<PRE>
public <B>AntStarTeamCheckOut</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="checkParameters()"><!-- --></A><H3>
checkParameters</H3>
<PRE>
protected void <B>checkParameters</B>()
                        throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Check if the attributes/elements are correct.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if there was a problem.</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Do the execution.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if there was a problem.</DL>
</DD>
</DL>
<HR>

<A NAME="getServer()"><!-- --></A><H3>
getServer</H3>
<PRE>
protected com.starbase.starteam.Server <B>getServer</B>()</PRE>
<DL>
<DD>Creates and logs in to a StarTeam server.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>A StarTeam server.</DL>
</DD>
</DL>
<HR>

<A NAME="runServer(com.starbase.starteam.Server)"><!-- --></A><H3>
runServer</H3>
<PRE>
protected void <B>runServer</B>(com.starbase.starteam.Server&nbsp;s)</PRE>
<DL>
<DD>Searches for the specified project on the server.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>s</CODE> - A StarTeam server.</DL>
</DD>
</DL>
<HR>

<A NAME="runProject(com.starbase.starteam.Server, com.starbase.starteam.Project)"><!-- --></A><H3>
runProject</H3>
<PRE>
protected void <B>runProject</B>(com.starbase.starteam.Server&nbsp;s,
                          com.starbase.starteam.Project&nbsp;p)</PRE>
<DL>
<DD>Searches for the given view in the project.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>s</CODE> - A StarTeam server.<DD><CODE>p</CODE> - A valid project on the given server.</DL>
</DD>
</DL>
<HR>

<A NAME="runType(com.starbase.starteam.Server, com.starbase.starteam.Project, com.starbase.starteam.View, com.starbase.starteam.Type)"><!-- --></A><H3>
runType</H3>
<PRE>
protected void <B>runType</B>(com.starbase.starteam.Server&nbsp;s,
                       com.starbase.starteam.Project&nbsp;p,
                       com.starbase.starteam.View&nbsp;v,
                       com.starbase.starteam.Type&nbsp;t)</PRE>
<DL>
<DD>Searches for folders in the given view.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>s</CODE> - A StarTeam server.<DD><CODE>p</CODE> - A valid project on the server.<DD><CODE>v</CODE> - A view name from the specified project.<DD><CODE>t</CODE> - An item type which is currently always "file".</DL>
</DD>
</DL>
<HR>

<A NAME="runFolder(com.starbase.starteam.Server, com.starbase.starteam.Project, com.starbase.starteam.View, com.starbase.starteam.Type, com.starbase.starteam.Folder, java.io.File)"><!-- --></A><H3>
runFolder</H3>
<PRE>
protected void <B>runFolder</B>(com.starbase.starteam.Server&nbsp;s,
                         com.starbase.starteam.Project&nbsp;p,
                         com.starbase.starteam.View&nbsp;v,
                         com.starbase.starteam.Type&nbsp;t,
                         com.starbase.starteam.Folder&nbsp;f,
                         java.io.File&nbsp;tgt)</PRE>
<DL>
<DD>Searches for files in the given folder. This method is recursive and
 thus searches all subfolders.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>s</CODE> - A StarTeam server.<DD><CODE>p</CODE> - A valid project on the server.<DD><CODE>v</CODE> - A view name from the specified project.<DD><CODE>t</CODE> - An item type which is currently always "file".<DD><CODE>f</CODE> - The folder to search.<DD><CODE>tgt</CODE> - Target folder on local machine</DL>
</DD>
</DL>
<HR>

<A NAME="runItem(com.starbase.starteam.Server, com.starbase.starteam.Project, com.starbase.starteam.View, com.starbase.starteam.Type, com.starbase.starteam.Folder, com.starbase.starteam.Item, java.io.File)"><!-- --></A><H3>
runItem</H3>
<PRE>
protected void <B>runItem</B>(com.starbase.starteam.Server&nbsp;s,
                       com.starbase.starteam.Project&nbsp;p,
                       com.starbase.starteam.View&nbsp;v,
                       com.starbase.starteam.Type&nbsp;t,
                       com.starbase.starteam.Folder&nbsp;f,
                       com.starbase.starteam.Item&nbsp;item,
                       java.io.File&nbsp;tgt)</PRE>
<DL>
<DD>Check out one file if it matches the include filter but not the exclude
 filter.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>s</CODE> - A StarTeam server.<DD><CODE>p</CODE> - A valid project on the server.<DD><CODE>v</CODE> - A view name from the specified project.<DD><CODE>t</CODE> - An item type which is currently always "file".<DD><CODE>f</CODE> - The folder the file is localed in.<DD><CODE>item</CODE> - The file to check out.<DD><CODE>tgt</CODE> - target folder on local machine</DL>
</DD>
</DL>
<HR>

<A NAME="shouldCheckout(java.lang.String)"><!-- --></A><H3>
shouldCheckout</H3>
<PRE>
protected boolean <B>shouldCheckout</B>(java.lang.String&nbsp;pName)</PRE>
<DL>
<DD>Look if the file should be checked out. Don't check it out if It fits
 no include filters and It fits an exclude filter.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pName</CODE> - the item name to look for being included.
<DT><B>Returns:</B><DD>whether the file should be checked out or not.</DL>
</DD>
</DL>
<HR>

<A NAME="matchPatterns(java.lang.String, java.lang.String)"><!-- --></A><H3>
matchPatterns</H3>
<PRE>
protected boolean <B>matchPatterns</B>(java.lang.String&nbsp;patterns,
                                java.lang.String&nbsp;pName)</PRE>
<DL>
<DD>Convenient method to see if a string match a one pattern in given set
 of space-separated patterns.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>patterns</CODE> - the space-separated list of patterns.<DD><CODE>pName</CODE> - the name to look for matching.
<DT><B>Returns:</B><DD>whether the name match at least one pattern.</DL>
</DD>
</DL>
<HR>

<A NAME="getPrimaryDescriptor(com.starbase.starteam.Type)"><!-- --></A><H3>
getPrimaryDescriptor</H3>
<PRE>
protected com.starbase.starteam.Property <B>getPrimaryDescriptor</B>(com.starbase.starteam.Type&nbsp;t)</PRE>
<DL>
<DD>Get the primary descriptor of the given item type. Returns null if
 there isn't one. In practice, all item types have a primary descriptor.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>t</CODE> - An item type. At this point it will always be "file".
<DT><B>Returns:</B><DD>The specified item's primary descriptor.</DL>
</DD>
</DL>
<HR>

<A NAME="getSecondaryDescriptor(com.starbase.starteam.Type)"><!-- --></A><H3>
getSecondaryDescriptor</H3>
<PRE>
protected com.starbase.starteam.Property <B>getSecondaryDescriptor</B>(com.starbase.starteam.Type&nbsp;t)</PRE>
<DL>
<DD>Get the secondary descriptor of the given item type. Returns null if
 there isn't one.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>t</CODE> - An item type. At this point it will always be "file".
<DT><B>Returns:</B><DD>The specified item's secondary descriptor. There may not be one
      for every file.</DL>
</DD>
</DL>
<HR>

<A NAME="formatForDisplay(com.starbase.starteam.Property, java.lang.Object)"><!-- --></A><H3>
formatForDisplay</H3>
<PRE>
protected java.lang.String <B>formatForDisplay</B>(com.starbase.starteam.Property&nbsp;p,
                                            java.lang.Object&nbsp;value)</PRE>
<DL>
<DD>Formats a property value for display to the Employee.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - An item property to format.<DD><CODE>value</CODE> - the object to format.
<DT><B>Returns:</B><DD>A string containing the property, which is truncated to 35
      characters for display.</DL>
</DD>
</DL>
<HR>

<A NAME="setServerName(java.lang.String)"><!-- --></A><H3>
setServerName</H3>
<PRE>
public void <B>setServerName</B>(java.lang.String&nbsp;serverName)</PRE>
<DL>
<DD>Sets the <CODE>serverName</CODE> attribute to the given value.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>serverName</CODE> - The name of the server you wish to connect to.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getServerName()"><CODE>getServerName()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getServerName()"><!-- --></A><H3>
getServerName</H3>
<PRE>
public java.lang.String <B>getServerName</B>()</PRE>
<DL>
<DD>Gets the <CODE>serverName</CODE> attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>The StarTeam server to log in to.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setServerName(java.lang.String)"><CODE>setServerName(String serverName)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setServerPort(int)"><!-- --></A><H3>
setServerPort</H3>
<PRE>
public void <B>setServerPort</B>(int&nbsp;serverPort)</PRE>
<DL>
<DD>Sets the <CODE>serverPort</CODE> attribute to the given value. The
 given value must be a valid integer, but it must be a string object.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>serverPort</CODE> - A string containing the port on the StarTeam server
      to use.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getServerPort()"><CODE>getServerPort()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getServerPort()"><!-- --></A><H3>
getServerPort</H3>
<PRE>
public int <B>getServerPort</B>()</PRE>
<DL>
<DD>Gets the <CODE>serverPort</CODE> attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>A string containing the port on the StarTeam server to use.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setServerPort(int)"><CODE>setServerPort(int)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setProjectName(java.lang.String)"><!-- --></A><H3>
setProjectName</H3>
<PRE>
public void <B>setProjectName</B>(java.lang.String&nbsp;projectName)</PRE>
<DL>
<DD>Sets the <CODE>projectName</CODE> attribute to the given value.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>projectName</CODE> - The StarTeam project to search.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getProjectName()"><CODE>getProjectName()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getProjectName()"><!-- --></A><H3>
getProjectName</H3>
<PRE>
public java.lang.String <B>getProjectName</B>()</PRE>
<DL>
<DD>Gets the <CODE>projectName</CODE> attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>The StarTeam project to search.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setProjectName(java.lang.String)"><CODE>setProjectName(String projectName)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setViewName(java.lang.String)"><!-- --></A><H3>
setViewName</H3>
<PRE>
public void <B>setViewName</B>(java.lang.String&nbsp;viewName)</PRE>
<DL>
<DD>Sets the <CODE>viewName</CODE> attribute to the given value.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>viewName</CODE> - The view to find the specified folder in.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getViewName()"><CODE>getViewName()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getViewName()"><!-- --></A><H3>
getViewName</H3>
<PRE>
public java.lang.String <B>getViewName</B>()</PRE>
<DL>
<DD>Gets the <CODE>viewName</CODE> attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>The view to find the specified folder in.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setViewName(java.lang.String)"><CODE>setViewName(String viewName)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setFolderName(java.lang.String)"><!-- --></A><H3>
setFolderName</H3>
<PRE>
public void <B>setFolderName</B>(java.lang.String&nbsp;folderName)</PRE>
<DL>
<DD>Sets the <CODE>folderName</CODE> attribute to the given value. To
 search the root folder, use a slash or backslash, or simply don't set a
 folder at all.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>folderName</CODE> - The subfolder from which to check out files.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getFolderName()"><CODE>getFolderName()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getFolderName()"><!-- --></A><H3>
getFolderName</H3>
<PRE>
public java.lang.String <B>getFolderName</B>()</PRE>
<DL>
<DD>Gets the <CODE>folderName</CODE> attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>The subfolder from which to check out files. All subfolders
      will be searched, as well.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setFolderName(java.lang.String)"><CODE>setFolderName(String folderName)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setUsername(java.lang.String)"><!-- --></A><H3>
setUsername</H3>
<PRE>
public void <B>setUsername</B>(java.lang.String&nbsp;username)</PRE>
<DL>
<DD>Sets the <CODE>username</CODE> attribute to the given value.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>username</CODE> - Your username for the specified StarTeam server.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getUsername()"><CODE>getUsername()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getUsername()"><!-- --></A><H3>
getUsername</H3>
<PRE>
public java.lang.String <B>getUsername</B>()</PRE>
<DL>
<DD>Gets the <CODE>username</CODE> attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>The username given by the Employee.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setUsername(java.lang.String)"><CODE>setUsername(String username)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setPassword(java.lang.String)"><!-- --></A><H3>
setPassword</H3>
<PRE>
public void <B>setPassword</B>(java.lang.String&nbsp;password)</PRE>
<DL>
<DD>Sets the <CODE>password</CODE> attribute to the given value.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>password</CODE> - Your password for the specified StarTeam server.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getPassword()"><CODE>getPassword()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getPassword()"><!-- --></A><H3>
getPassword</H3>
<PRE>
public java.lang.String <B>getPassword</B>()</PRE>
<DL>
<DD>Gets the <CODE>password</CODE> attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>The password given by the Employee.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setPassword(java.lang.String)"><CODE>setPassword(String password)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setTargetFolder(java.lang.String)"><!-- --></A><H3>
setTargetFolder</H3>
<PRE>
public void <B>setTargetFolder</B>(java.lang.String&nbsp;targetFolder)</PRE>
<DL>
<DD>Sets the <CODE>targetFolder</CODE> attribute to the given value.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>targetFolder</CODE> - The target path on the local machine to check out to.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getTargetFolder()"><CODE>getTargetFolder()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getTargetFolder()"><!-- --></A><H3>
getTargetFolder</H3>
<PRE>
public java.lang.String <B>getTargetFolder</B>()</PRE>
<DL>
<DD>Gets the <CODE>targetFolder</CODE> attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>The target path on the local machine to check out to.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setTargetFolder(java.lang.String)"><CODE>setTargetFolder(String targetFolder)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setForce(boolean)"><!-- --></A><H3>
setForce</H3>
<PRE>
public void <B>setForce</B>(boolean&nbsp;force)</PRE>
<DL>
<DD>Sets the <CODE>force</CODE> attribute to the given value.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>force</CODE> - if true, it overwrites files in the target directory. By
      default it set to false as a safeguard. Note that if the target
      directory does not exist, this setting has no effect.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getForce()"><CODE>getForce()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getForce()"><!-- --></A><H3>
getForce</H3>
<PRE>
public boolean <B>getForce</B>()</PRE>
<DL>
<DD>Gets the <CODE>force</CODE> attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>whether to continue if the target directory exists.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setForce(boolean)"><CODE>setForce(boolean)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setRecursion(boolean)"><!-- --></A><H3>
setRecursion</H3>
<PRE>
public void <B>setRecursion</B>(boolean&nbsp;recursion)</PRE>
<DL>
<DD>Turns recursion on or off.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>recursion</CODE> - if it is true, the default, subfolders are searched
      recursively for files to check out. Otherwise, only files
      specified by <CODE>folderName</CODE> are scanned.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getRecursion()"><CODE>getRecursion()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getRecursion()"><!-- --></A><H3>
getRecursion</H3>
<PRE>
public boolean <B>getRecursion</B>()</PRE>
<DL>
<DD>Gets the <CODE>recursion</CODE> attribute, which tells
 AntStarTeamCheckOut whether to search subfolders when checking out
 files.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>whether to search subfolders when checking out files.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setRecursion(boolean)"><CODE>setRecursion(boolean)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setVerbose(boolean)"><!-- --></A><H3>
setVerbose</H3>
<PRE>
public void <B>setVerbose</B>(boolean&nbsp;verbose)</PRE>
<DL>
<DD>Sets the <CODE>verbose</CODE> attribute to the given value.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>verbose</CODE> - whether to display all files as it checks them out. By
      default it is false, so the program only displays the total number
      of files unless you override this default.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getVerbose()"><CODE>getVerbose()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getVerbose()"><!-- --></A><H3>
getVerbose</H3>
<PRE>
public boolean <B>getVerbose</B>()</PRE>
<DL>
<DD>Gets the <CODE>verbose</CODE> attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>whether to display all files as it checks them out.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setVerbose(boolean)"><CODE>setVerbose(boolean verbose)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setIncludes(java.lang.String)"><!-- --></A><H3>
setIncludes</H3>
<PRE>
public void <B>setIncludes</B>(java.lang.String&nbsp;includes)</PRE>
<DL>
<DD>Sets the include filter. When filtering files, AntStarTeamCheckOut uses
 an unmodified version of <CODE>DirectoryScanner</CODE>'s <CODE>match</CODE>
 method, so here are the patterns straight from the Ant source code:
 <BR>
 <BR>
 Matches a string against a pattern. The pattern contains two special
 characters: <BR>
 '*' which means zero or more characters, <BR>
 '?' which means one and only one character. <BR>
 <BR>
 Separate multiple inlcude filters by <I>spaces</I> , not commas as Ant
 uses. For example, if you want to check out all .java and .class\
 files, you would put the following line in your program:
 <CODE>setIncludes("*.java *.class");</CODE>
 Finally, note that filters have no effect on the <B>directories</B>
 that are scanned; you could not check out files from directories with
 names beginning only with "build," for instance. Of course, you could
 limit AntStarTeamCheckOut to a particular folder and its subfolders
 with the <CODE>setFolderName(String folderName)</CODE> command. <BR>
 <BR>
 Treatment of overlapping inlcudes and excludes: To give a simplistic
 example suppose that you set your include filter to "*.htm *.html" and
 your exclude filter to "index.*". What happens to index.html?
 AntStarTeamCheckOut will not check out index.html, as it matches an
 exclude filter ("index.*"), even though it matches the include filter,
 as well. <BR>
 <BR>
 Please also read the following sections before using filters:
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>includes</CODE> - A string of filter patterns to include. Separate the
      patterns by spaces.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getIncludes()"><CODE>getIncludes()</CODE></A>, 
<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setExcludes(java.lang.String)"><CODE>setExcludes(String excludes)</CODE></A>, 
<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getExcludes()"><CODE>getExcludes()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getIncludes()"><!-- --></A><H3>
getIncludes</H3>
<PRE>
public java.lang.String <B>getIncludes</B>()</PRE>
<DL>
<DD>Gets the patterns from the include filter. Rather that duplicate the
 details of AntStarTeanCheckOut's filtering here, refer to these links:
<P>
<DD><DL>

<DT><B>Returns:</B><DD>A string of filter patterns separated by spaces.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setIncludes(java.lang.String)"><CODE>setIncludes(String includes)</CODE></A>, 
<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setExcludes(java.lang.String)"><CODE>setExcludes(String excludes)</CODE></A>, 
<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getExcludes()"><CODE>getExcludes()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setExcludes(java.lang.String)"><!-- --></A><H3>
setExcludes</H3>
<PRE>
public void <B>setExcludes</B>(java.lang.String&nbsp;excludes)</PRE>
<DL>
<DD>Sets the exclude filter. When filtering files, AntStarTeamCheckOut uses
 an unmodified version of <CODE>DirectoryScanner</CODE>'s <CODE>match</CODE>
 method, so here are the patterns straight from the Ant source code:
 <BR>
 <BR>
 Matches a string against a pattern. The pattern contains two special
 characters: <BR>
 '*' which means zero or more characters, <BR>
 '?' which means one and only one character. <BR>
 <BR>
 Separate multiple exlcude filters by <I>spaces</I> , not commas as Ant
 uses. For example, if you want to check out all files except .XML and
 .HTML files, you would put the following line in your program:
 <CODE>setExcludes("*.XML *.HTML");</CODE>
 Finally, note that filters have no effect on the <B>directories</B>
 that are scanned; you could not skip over all files in directories
 whose names begin with "project," for instance. <BR>
 <BR>
 Treatment of overlapping inlcudes and excludes: To give a simplistic
 example suppose that you set your include filter to "*.htm *.html" and
 your exclude filter to "index.*". What happens to index.html?
 AntStarTeamCheckOut will not check out index.html, as it matches an
 exclude filter ("index.*"), even though it matches the include filter,
 as well. <BR>
 <BR>
 Please also read the following sections before using filters:
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>excludes</CODE> - A string of filter patterns to exclude. Separate the
      patterns by spaces.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setIncludes(java.lang.String)"><CODE>setIncludes(String includes)</CODE></A>, 
<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getIncludes()"><CODE>getIncludes()</CODE></A>, 
<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getExcludes()"><CODE>getExcludes()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getExcludes()"><!-- --></A><H3>
getExcludes</H3>
<PRE>
public java.lang.String <B>getExcludes</B>()</PRE>
<DL>
<DD>Gets the patterns from the exclude filter. Rather that duplicate the
 details of AntStarTeanCheckOut's filtering here, refer to these links:
<P>
<DD><DL>

<DT><B>Returns:</B><DD>A string of filter patterns separated by spaces.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setExcludes(java.lang.String)"><CODE>setExcludes(String excludes)</CODE></A>, 
<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setIncludes(java.lang.String)"><CODE>setIncludes(String includes)</CODE></A>, 
<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getIncludes()"><CODE>getIncludes()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getTargetFolderAbsolute()"><!-- --></A><H3>
getTargetFolderAbsolute</H3>
<PRE>
public boolean <B>getTargetFolderAbsolute</B>()</PRE>
<DL>
<DD>returns whether the StarTeam default path is factored into calculated
 target path locations (false) or whether targetFolder is an absolute
 mapping to the root folder named by folderName
<P>
<DD><DL>

<DT><B>Returns:</B><DD>returns true if absolute mapping is used, false if it is not
      used.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#setTargetFolderAbsolute(boolean)"><CODE>setTargetFolderAbsolute(boolean)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setTargetFolderAbsolute(boolean)"><!-- --></A><H3>
setTargetFolderAbsolute</H3>
<PRE>
public void <B>setTargetFolderAbsolute</B>(boolean&nbsp;targetFolderAbsolute)</PRE>
<DL>
<DD>sets the property that indicates whether or not the Star Team "default
 folder" is to be used when calculation paths for items on the target
 (false) or if targetFolder is an absolute mapping to the root folder
 named by foldername.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>targetFolderAbsolute</CODE> - <tt>true</tt> if the absolute mapping is to
      be used. <tt>false</tt> (the default) if the "default folder" is
      to be factored in.<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#getTargetFolderAbsolute()"><CODE>getTargetFolderAbsolute()</CODE></A></DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV CLASS&nbsp;
&nbsp;NEXT CLASS</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="AntStarTeamCheckOut.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
