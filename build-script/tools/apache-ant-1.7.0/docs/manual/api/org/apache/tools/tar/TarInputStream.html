<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:34 EST 2006 -->
<TITLE>
TarInputStream (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.tar.TarInputStream class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="TarInputStream (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarOutputStream.html" title="class in org.apache.tools.tar"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/tar/TarInputStream.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="TarInputStream.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.tar</FONT>
<BR>
Class TarInputStream</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by ">java.io.InputStream
      <IMG SRC="../../../../resources/inherit.gif" ALT="extended by ">java.io.FilterInputStream
          <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.tar.TarInputStream</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.io.Closeable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>TarInputStream</B><DT>extends java.io.FilterInputStream</DL>
</PRE>

<P>
The TarInputStream reads a UNIX tar archive as an InputStream.
 methods are provided to position at each successive entry in
 the archive, and the read each entry as a normal input stream
 using read().
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../org/apache/tools/tar/TarBuffer.html" title="class in org.apache.tools.tar">TarBuffer</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#buffer">buffer</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#currEntry">currEntry</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#debug">debug</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#entryOffset">entryOffset</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#entrySize">entrySize</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#hasHitEOF">hasHitEOF</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#oneBuf">oneBuf</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This contents of this array is not used at all in this class,
 it is only here to avoid repreated object creation during calls
 to the no-arg read method.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#readBuf">readBuf</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_java.io.FilterInputStream"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class java.io.FilterInputStream</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>in</CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#TarInputStream(java.io.InputStream)">TarInputStream</A></B>(java.io.InputStream&nbsp;is)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for TarInputStream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#TarInputStream(java.io.InputStream, int)">TarInputStream</A></B>(java.io.InputStream&nbsp;is,
               int&nbsp;blockSize)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for TarInputStream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#TarInputStream(java.io.InputStream, int, int)">TarInputStream</A></B>(java.io.InputStream&nbsp;is,
               int&nbsp;blockSize,
               int&nbsp;recordSize)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for TarInputStream.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#available()">available</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the available data that can be read from the current
 entry in the archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#close()">close</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Closes this stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#copyEntryContents(java.io.OutputStream)">copyEntryContents</A></B>(java.io.OutputStream&nbsp;out)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Copies the contents of the current tar archive entry directly into
 an output stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#getNextEntry()">getNextEntry</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the next entry in this tar archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#getRecordSize()">getRecordSize</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the record size being used by this stream's TarBuffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#mark(int)">mark</A></B>(int&nbsp;markLimit)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Since we do not support marking just yet, we do nothing.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#markSupported()">markSupported</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Since we do not support marking just yet, we return false.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#read()">read</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Reads a byte from the current tar archive entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#read(byte[], int, int)">read</A></B>(byte[]&nbsp;buf,
     int&nbsp;offset,
     int&nbsp;numToRead)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Reads bytes from the current tar archive entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#reset()">reset</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Since we do not support marking just yet, we do nothing.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#setDebug(boolean)">setDebug</A></B>(boolean&nbsp;debug)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the debugging flag.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarInputStream.html#skip(long)">skip</A></B>(long&nbsp;numToSkip)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Skip bytes in the input buffer.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.io.FilterInputStream"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.io.FilterInputStream</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>read</CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="debug"><!-- --></A><H3>
debug</H3>
<PRE>
protected boolean <B>debug</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="hasHitEOF"><!-- --></A><H3>
hasHitEOF</H3>
<PRE>
protected boolean <B>hasHitEOF</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="entrySize"><!-- --></A><H3>
entrySize</H3>
<PRE>
protected long <B>entrySize</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="entryOffset"><!-- --></A><H3>
entryOffset</H3>
<PRE>
protected long <B>entryOffset</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="readBuf"><!-- --></A><H3>
readBuf</H3>
<PRE>
protected byte[] <B>readBuf</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="buffer"><!-- --></A><H3>
buffer</H3>
<PRE>
protected <A HREF="../../../../org/apache/tools/tar/TarBuffer.html" title="class in org.apache.tools.tar">TarBuffer</A> <B>buffer</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="currEntry"><!-- --></A><H3>
currEntry</H3>
<PRE>
protected <A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</A> <B>currEntry</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="oneBuf"><!-- --></A><H3>
oneBuf</H3>
<PRE>
protected byte[] <B>oneBuf</B></PRE>
<DL>
<DD>This contents of this array is not used at all in this class,
 it is only here to avoid repreated object creation during calls
 to the no-arg read method.
<P>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="TarInputStream(java.io.InputStream)"><!-- --></A><H3>
TarInputStream</H3>
<PRE>
public <B>TarInputStream</B>(java.io.InputStream&nbsp;is)</PRE>
<DL>
<DD>Constructor for TarInputStream.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>is</CODE> - the input stream to use</DL>
</DL>
<HR>

<A NAME="TarInputStream(java.io.InputStream, int)"><!-- --></A><H3>
TarInputStream</H3>
<PRE>
public <B>TarInputStream</B>(java.io.InputStream&nbsp;is,
                      int&nbsp;blockSize)</PRE>
<DL>
<DD>Constructor for TarInputStream.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>is</CODE> - the input stream to use<DD><CODE>blockSize</CODE> - the block size to use</DL>
</DL>
<HR>

<A NAME="TarInputStream(java.io.InputStream, int, int)"><!-- --></A><H3>
TarInputStream</H3>
<PRE>
public <B>TarInputStream</B>(java.io.InputStream&nbsp;is,
                      int&nbsp;blockSize,
                      int&nbsp;recordSize)</PRE>
<DL>
<DD>Constructor for TarInputStream.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>is</CODE> - the input stream to use<DD><CODE>blockSize</CODE> - the block size to use<DD><CODE>recordSize</CODE> - the record size to use</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setDebug(boolean)"><!-- --></A><H3>
setDebug</H3>
<PRE>
public void <B>setDebug</B>(boolean&nbsp;debug)</PRE>
<DL>
<DD>Sets the debugging flag.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>debug</CODE> - True to turn on debugging.</DL>
</DD>
</DL>
<HR>

<A NAME="close()"><!-- --></A><H3>
close</H3>
<PRE>
public void <B>close</B>()
           throws java.io.IOException</PRE>
<DL>
<DD>Closes this stream. Calls the TarBuffer's close() method.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE>close</CODE> in interface <CODE>java.io.Closeable</CODE><DT><B>Overrides:</B><DD><CODE>close</CODE> in class <CODE>java.io.FilterInputStream</CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="getRecordSize()"><!-- --></A><H3>
getRecordSize</H3>
<PRE>
public int <B>getRecordSize</B>()</PRE>
<DL>
<DD>Get the record size being used by this stream's TarBuffer.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>The TarBuffer record size.</DL>
</DD>
</DL>
<HR>

<A NAME="available()"><!-- --></A><H3>
available</H3>
<PRE>
public int <B>available</B>()
              throws java.io.IOException</PRE>
<DL>
<DD>Get the available data that can be read from the current
 entry in the archive. This does not indicate how much data
 is left in the entire archive, only in the current entry.
 This value is determined from the entry's size header field
 and the amount of data already read from the current entry.
 Integer.MAX_VALUE is returen in case more than Integer.MAX_VALUE
 bytes are left in the current entry in the archive.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>available</CODE> in class <CODE>java.io.FilterInputStream</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>The number of available bytes for the current entry.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - for signature</DL>
</DD>
</DL>
<HR>

<A NAME="skip(long)"><!-- --></A><H3>
skip</H3>
<PRE>
public long <B>skip</B>(long&nbsp;numToSkip)
          throws java.io.IOException</PRE>
<DL>
<DD>Skip bytes in the input buffer. This skips bytes in the
 current entry's data, not the entire archive, and will
 stop at the end of the current entry's data if the number
 to skip extends beyond that point.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>skip</CODE> in class <CODE>java.io.FilterInputStream</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>numToSkip</CODE> - The number of bytes to skip.
<DT><B>Returns:</B><DD>the number actually skipped
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="markSupported()"><!-- --></A><H3>
markSupported</H3>
<PRE>
public boolean <B>markSupported</B>()</PRE>
<DL>
<DD>Since we do not support marking just yet, we return false.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>markSupported</CODE> in class <CODE>java.io.FilterInputStream</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>False.</DL>
</DD>
</DL>
<HR>

<A NAME="mark(int)"><!-- --></A><H3>
mark</H3>
<PRE>
public void <B>mark</B>(int&nbsp;markLimit)</PRE>
<DL>
<DD>Since we do not support marking just yet, we do nothing.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>mark</CODE> in class <CODE>java.io.FilterInputStream</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>markLimit</CODE> - The limit to mark.</DL>
</DD>
</DL>
<HR>

<A NAME="reset()"><!-- --></A><H3>
reset</H3>
<PRE>
public void <B>reset</B>()</PRE>
<DL>
<DD>Since we do not support marking just yet, we do nothing.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>reset</CODE> in class <CODE>java.io.FilterInputStream</CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="getNextEntry()"><!-- --></A><H3>
getNextEntry</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</A> <B>getNextEntry</B>()
                      throws java.io.IOException</PRE>
<DL>
<DD>Get the next entry in this tar archive. This will skip
 over any remaining data in the current entry, if there
 is one, and place the input stream at the header of the
 next entry, and read the header and instantiate a new
 TarEntry from the header bytes and return that entry.
 If there are no more entries in the archive, null will
 be returned to indicate that the end of the archive has
 been reached.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>The next TarEntry in the archive, or null.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="read()"><!-- --></A><H3>
read</H3>
<PRE>
public int <B>read</B>()
         throws java.io.IOException</PRE>
<DL>
<DD>Reads a byte from the current tar archive entry.

 This method simply calls read( byte[], int, int ).
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>read</CODE> in class <CODE>java.io.FilterInputStream</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>The byte read, or -1 at EOF.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="read(byte[], int, int)"><!-- --></A><H3>
read</H3>
<PRE>
public int <B>read</B>(byte[]&nbsp;buf,
                int&nbsp;offset,
                int&nbsp;numToRead)
         throws java.io.IOException</PRE>
<DL>
<DD>Reads bytes from the current tar archive entry.

 This method is aware of the boundaries of the current
 entry in the archive and will deal with them as if they
 were this stream's start and EOF.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>read</CODE> in class <CODE>java.io.FilterInputStream</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>buf</CODE> - The buffer into which to place bytes read.<DD><CODE>offset</CODE> - The offset at which to place bytes read.<DD><CODE>numToRead</CODE> - The number of bytes to read.
<DT><B>Returns:</B><DD>The number of bytes read, or -1 at EOF.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="copyEntryContents(java.io.OutputStream)"><!-- --></A><H3>
copyEntryContents</H3>
<PRE>
public void <B>copyEntryContents</B>(java.io.OutputStream&nbsp;out)
                       throws java.io.IOException</PRE>
<DL>
<DD>Copies the contents of the current tar archive entry directly into
 an output stream.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>out</CODE> - The OutputStream into which to write the entry's data.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarOutputStream.html" title="class in org.apache.tools.tar"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/tar/TarInputStream.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="TarInputStream.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
