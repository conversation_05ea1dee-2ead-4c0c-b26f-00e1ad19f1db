<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:22 EST 2006 -->
<TITLE>
ExecuteOn (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.ExecuteOn class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="ExecuteOn (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.FileDirBoth.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/ExecuteOn.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ExecuteOn.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class ExecuteOn</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.ExecTask</A>
              <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.ExecuteOn</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/unix/AbstractAccessTask.html" title="class in org.apache.tools.ant.taskdefs.optional.unix">AbstractAccessTask</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/windows/Attrib.html" title="class in org.apache.tools.ant.taskdefs.optional.windows">Attrib</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Chmod.html" title="class in org.apache.tools.ant.taskdefs">Chmod</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Transform.html" title="class in org.apache.tools.ant.taskdefs">Transform</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>ExecuteOn</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A></DL>
</PRE>

<P>
Executes a given command, supplying a set of files as arguments.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
</DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.FileDirBoth.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn.FileDirBoth</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Enumerated attribute with the values "file", "dir" and "both"
 for the type attribute.</TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#destDir">destDir</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#filesets">filesets</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#mapper">mapper</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Mapper.html" title="class in org.apache.tools.ant.types">Mapper</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#mapperElement">mapperElement</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Commandline.Marker.html" title="class in org.apache.tools.ant.types">Commandline.Marker</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#srcFilePos">srcFilePos</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#srcIsFirst">srcIsFirst</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Has &lt;srcfile&gt; been specified before &lt;targetfile&gt;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Commandline.Marker.html" title="class in org.apache.tools.ant.types">Commandline.Marker</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#targetFilePos">targetFilePos</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#type">type</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.ExecTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#cmdl">cmdl</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#failOnError">failOnError</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#newEnvironment">newEnvironment</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#redirector">redirector</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#redirectorElement">redirectorElement</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#ExecuteOn()">ExecuteOn</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#add(org.apache.tools.ant.util.FileNameMapper)">add</A></B>(<A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A>&nbsp;fileNameMapper)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a nested FileNameMapper.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#add(org.apache.tools.ant.types.ResourceCollection)">add</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;rc)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a collection of resources upon which to operate.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#addDirset(org.apache.tools.ant.types.DirSet)">addDirset</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/DirSet.html" title="class in org.apache.tools.ant.types">DirSet</A>&nbsp;set)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a set of directories upon which to operate.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#addFilelist(org.apache.tools.ant.types.FileList)">addFilelist</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/FileList.html" title="class in org.apache.tools.ant.types">FileList</A>&nbsp;list)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a list of source files upon which to operate.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;set)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a set of files upon which to operate.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#checkConfiguration()">checkConfiguration</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Check the configuration of this ExecuteOn instance.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#createHandler()">createHandler</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create the ExecuteStreamHandler instance that will be used
 during execution.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Mapper.html" title="class in org.apache.tools.ant.types">Mapper</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#createMapper()">createMapper</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a nested Mapper element to use for mapping
 source files to target files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Commandline.Marker.html" title="class in org.apache.tools.ant.types">Commandline.Marker</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#createSrcfile()">createSrcfile</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a placeholder indicating where on the command line
 the name of the source file should be inserted.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Commandline.Marker.html" title="class in org.apache.tools.ant.types">Commandline.Marker</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#createTargetfile()">createTargetfile</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a placeholder indicating where on the command line
 the name of the target file should be inserted.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#getCommandline(java.lang.String[], java.io.File[])">getCommandline</A></B>(java.lang.String[]&nbsp;srcFiles,
               java.io.File[]&nbsp;baseDirs)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Construct the command line for parallel execution.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#getCommandline(java.lang.String, java.io.File)">getCommandline</A></B>(java.lang.String&nbsp;srcFile,
               java.io.File&nbsp;baseDir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Construct the command line for serial execution.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#getDirs(java.io.File, org.apache.tools.ant.DirectoryScanner)">getDirs</A></B>(java.io.File&nbsp;baseDir,
        <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</A>&nbsp;ds)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the list of Directories from this DirectoryScanner that
 should be included on the command line.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#getFiles(java.io.File, org.apache.tools.ant.DirectoryScanner)">getFiles</A></B>(java.io.File&nbsp;baseDir,
         <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</A>&nbsp;ds)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the list of files from this DirectoryScanner that should
 be included on the command line.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#getFilesAndDirs(org.apache.tools.ant.types.FileList)">getFilesAndDirs</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/FileList.html" title="class in org.apache.tools.ant.types">FileList</A>&nbsp;list)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the list of files or directories from this FileList that
 should be included on the command line.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#runExec(org.apache.tools.ant.taskdefs.Execute)">runExec</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</A>&nbsp;exe)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Run the specified Execute object.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#runParallel(org.apache.tools.ant.taskdefs.Execute, java.util.Vector, java.util.Vector)">runParallel</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</A>&nbsp;exe,
            java.util.Vector&nbsp;fileNames,
            java.util.Vector&nbsp;baseDirs)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Run the command in "parallel" mode, making sure that at most
 maxParallel sourcefiles get passed on the command line.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setAddsourcefile(boolean)">setAddsourcefile</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether to send the source file name on the command line.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setDest(java.io.File)">setDest</A></B>(java.io.File&nbsp;destDir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specify the directory where target files are to be placed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setForce(boolean)">setForce</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether to bypass timestamp comparisons for target files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setForwardslash(boolean)">setForwardslash</A></B>(boolean&nbsp;forwardSlash)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether the source and target file names on Windows and OS/2
 must use the forward slash as file separator.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setIgnoremissing(boolean)">setIgnoremissing</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether to ignore nonexistent files from filelists.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setMaxParallel(int)">setMaxParallel</A></B>(int&nbsp;max)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Limit the command line length by passing at maximum this many
 sourcefiles at once to the command.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setParallel(boolean)">setParallel</A></B>(boolean&nbsp;parallel)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether to execute in parallel mode.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setRelative(boolean)">setRelative</A></B>(boolean&nbsp;relative)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether the filenames should be passed on the command line as
 absolute or relative pathnames.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setSkipEmptyFilesets(boolean)">setSkipEmptyFilesets</A></B>(boolean&nbsp;skip)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether empty filesets will be skipped.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setType(org.apache.tools.ant.taskdefs.ExecuteOn.FileDirBoth)">setType</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.FileDirBoth.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn.FileDirBoth</A>&nbsp;type)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether the command works only on files, directories or both.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setupRedirector()">setupRedirector</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set up the I/O Redirector.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setVerbose(boolean)">setVerbose</A></B>(boolean&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether to operate in verbose mode.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.ExecTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#addConfiguredRedirector(org.apache.tools.ant.types.RedirectorElement)">addConfiguredRedirector</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#addEnv(org.apache.tools.ant.types.Environment.Variable)">addEnv</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#createArg()">createArg</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#createWatchdog()">createWatchdog</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#execute()">execute</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#getResolveExecutable()">getResolveExecutable</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#isValidOs()">isValidOs</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#logFlush()">logFlush</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#maybeSetResultPropertyValue(int)">maybeSetResultPropertyValue</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#prepareExec()">prepareExec</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#resolveExecutable(java.lang.String, boolean)">resolveExecutable</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#runExecute(org.apache.tools.ant.taskdefs.Execute)">runExecute</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setAppend(boolean)">setAppend</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setCommand(org.apache.tools.ant.types.Commandline)">setCommand</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setDir(java.io.File)">setDir</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setError(java.io.File)">setError</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setErrorProperty(java.lang.String)">setErrorProperty</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setExecutable(java.lang.String)">setExecutable</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setFailIfExecutionFails(boolean)">setFailIfExecutionFails</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setFailonerror(boolean)">setFailonerror</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setInput(java.io.File)">setInput</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setInputString(java.lang.String)">setInputString</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setLogError(boolean)">setLogError</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setNewenvironment(boolean)">setNewenvironment</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setOs(java.lang.String)">setOs</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setOsFamily(java.lang.String)">setOsFamily</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setOutput(java.io.File)">setOutput</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setOutputproperty(java.lang.String)">setOutputproperty</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setResolveExecutable(boolean)">setResolveExecutable</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setResultProperty(java.lang.String)">setResultProperty</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setSearchPath(boolean)">setSearchPath</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setSpawn(boolean)">setSpawn</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setTimeout(java.lang.Integer)">setTimeout</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setTimeout(java.lang.Long)">setTimeout</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setVMLauncher(boolean)">setVMLauncher</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="filesets"><!-- --></A><H3>
filesets</H3>
<PRE>
protected java.util.Vector <B>filesets</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="type"><!-- --></A><H3>
type</H3>
<PRE>
protected java.lang.String <B>type</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="srcFilePos"><!-- --></A><H3>
srcFilePos</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/types/Commandline.Marker.html" title="class in org.apache.tools.ant.types">Commandline.Marker</A> <B>srcFilePos</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="targetFilePos"><!-- --></A><H3>
targetFilePos</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/types/Commandline.Marker.html" title="class in org.apache.tools.ant.types">Commandline.Marker</A> <B>targetFilePos</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="mapperElement"><!-- --></A><H3>
mapperElement</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/types/Mapper.html" title="class in org.apache.tools.ant.types">Mapper</A> <B>mapperElement</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="mapper"><!-- --></A><H3>
mapper</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A> <B>mapper</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="destDir"><!-- --></A><H3>
destDir</H3>
<PRE>
protected java.io.File <B>destDir</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="srcIsFirst"><!-- --></A><H3>
srcIsFirst</H3>
<PRE>
protected boolean <B>srcIsFirst</B></PRE>
<DL>
<DD>Has &lt;srcfile&gt; been specified before &lt;targetfile&gt;
<P>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ExecuteOn()"><!-- --></A><H3>
ExecuteOn</H3>
<PRE>
public <B>ExecuteOn</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="addFileset(org.apache.tools.ant.types.FileSet)"><!-- --></A><H3>
addFileset</H3>
<PRE>
public void <B>addFileset</B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;set)</PRE>
<DL>
<DD>Add a set of files upon which to operate.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>set</CODE> - the FileSet to add.</DL>
</DD>
</DL>
<HR>

<A NAME="addDirset(org.apache.tools.ant.types.DirSet)"><!-- --></A><H3>
addDirset</H3>
<PRE>
public void <B>addDirset</B>(<A HREF="../../../../../org/apache/tools/ant/types/DirSet.html" title="class in org.apache.tools.ant.types">DirSet</A>&nbsp;set)</PRE>
<DL>
<DD>Add a set of directories upon which to operate.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>set</CODE> - the DirSet to add.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addFilelist(org.apache.tools.ant.types.FileList)"><!-- --></A><H3>
addFilelist</H3>
<PRE>
public void <B>addFilelist</B>(<A HREF="../../../../../org/apache/tools/ant/types/FileList.html" title="class in org.apache.tools.ant.types">FileList</A>&nbsp;list)</PRE>
<DL>
<DD>Add a list of source files upon which to operate.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>list</CODE> - the FileList to add.</DL>
</DD>
</DL>
<HR>

<A NAME="add(org.apache.tools.ant.types.ResourceCollection)"><!-- --></A><H3>
add</H3>
<PRE>
public void <B>add</B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;rc)</PRE>
<DL>
<DD>Add a collection of resources upon which to operate.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rc</CODE> - resource collection to add.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setRelative(boolean)"><!-- --></A><H3>
setRelative</H3>
<PRE>
public void <B>setRelative</B>(boolean&nbsp;relative)</PRE>
<DL>
<DD>Set whether the filenames should be passed on the command line as
 absolute or relative pathnames. Paths are relative to the base
 directory of the corresponding fileset for source files or the
 dest attribute for target files.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>relative</CODE> - whether to pass relative pathnames.</DL>
</DD>
</DL>
<HR>

<A NAME="setParallel(boolean)"><!-- --></A><H3>
setParallel</H3>
<PRE>
public void <B>setParallel</B>(boolean&nbsp;parallel)</PRE>
<DL>
<DD>Set whether to execute in parallel mode.
 If true, run the command only once, appending all files as arguments.
 If false, command will be executed once for every file. Defaults to false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>parallel</CODE> - whether to run in parallel.</DL>
</DD>
</DL>
<HR>

<A NAME="setType(org.apache.tools.ant.taskdefs.ExecuteOn.FileDirBoth)"><!-- --></A><H3>
setType</H3>
<PRE>
public void <B>setType</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.FileDirBoth.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn.FileDirBoth</A>&nbsp;type)</PRE>
<DL>
<DD>Set whether the command works only on files, directories or both.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>type</CODE> - a FileDirBoth EnumeratedAttribute.</DL>
</DD>
</DL>
<HR>

<A NAME="setSkipEmptyFilesets(boolean)"><!-- --></A><H3>
setSkipEmptyFilesets</H3>
<PRE>
public void <B>setSkipEmptyFilesets</B>(boolean&nbsp;skip)</PRE>
<DL>
<DD>Set whether empty filesets will be skipped.  If true and
 no source files have been found or are newer than their
 corresponding target files, the command will not be run.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>skip</CODE> - whether to skip empty filesets.</DL>
</DD>
</DL>
<HR>

<A NAME="setDest(java.io.File)"><!-- --></A><H3>
setDest</H3>
<PRE>
public void <B>setDest</B>(java.io.File&nbsp;destDir)</PRE>
<DL>
<DD>Specify the directory where target files are to be placed.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>destDir</CODE> - the File object representing the destination directory.</DL>
</DD>
</DL>
<HR>

<A NAME="setForwardslash(boolean)"><!-- --></A><H3>
setForwardslash</H3>
<PRE>
public void <B>setForwardslash</B>(boolean&nbsp;forwardSlash)</PRE>
<DL>
<DD>Set whether the source and target file names on Windows and OS/2
 must use the forward slash as file separator.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>forwardSlash</CODE> - whether the forward slash will be forced.</DL>
</DD>
</DL>
<HR>

<A NAME="setMaxParallel(int)"><!-- --></A><H3>
setMaxParallel</H3>
<PRE>
public void <B>setMaxParallel</B>(int&nbsp;max)</PRE>
<DL>
<DD>Limit the command line length by passing at maximum this many
 sourcefiles at once to the command.

 <p>Set to &lt;= 0 for unlimited - this is the default.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>max</CODE> - <code>int</code> maximum number of sourcefiles
            passed to the executable.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setAddsourcefile(boolean)"><!-- --></A><H3>
setAddsourcefile</H3>
<PRE>
public void <B>setAddsourcefile</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Set whether to send the source file name on the command line.

 <p>Defaults to <code>true</code>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - whether to add the source file to the command line.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setVerbose(boolean)"><!-- --></A><H3>
setVerbose</H3>
<PRE>
public void <B>setVerbose</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Set whether to operate in verbose mode.
 If true, a verbose summary will be printed after execution.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - whether to operate in verbose mode.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setIgnoremissing(boolean)"><!-- --></A><H3>
setIgnoremissing</H3>
<PRE>
public void <B>setIgnoremissing</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Set whether to ignore nonexistent files from filelists.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - whether to ignore missing files.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setForce(boolean)"><!-- --></A><H3>
setForce</H3>
<PRE>
public void <B>setForce</B>(boolean&nbsp;b)</PRE>
<DL>
<DD>Set whether to bypass timestamp comparisons for target files.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - whether to bypass timestamp comparisons.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="createSrcfile()"><!-- --></A><H3>
createSrcfile</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Commandline.Marker.html" title="class in org.apache.tools.ant.types">Commandline.Marker</A> <B>createSrcfile</B>()</PRE>
<DL>
<DD>Create a placeholder indicating where on the command line
 the name of the source file should be inserted.
<P>
<DD><DL>

<DT><B>Returns:</B><DD><code>Commandline.Marker</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="createTargetfile()"><!-- --></A><H3>
createTargetfile</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Commandline.Marker.html" title="class in org.apache.tools.ant.types">Commandline.Marker</A> <B>createTargetfile</B>()</PRE>
<DL>
<DD>Create a placeholder indicating where on the command line
 the name of the target file should be inserted.
<P>
<DD><DL>

<DT><B>Returns:</B><DD><code>Commandline.Marker</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="createMapper()"><!-- --></A><H3>
createMapper</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Mapper.html" title="class in org.apache.tools.ant.types">Mapper</A> <B>createMapper</B>()
                    throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Create a nested Mapper element to use for mapping
 source files to target files.
<P>
<DD><DL>

<DT><B>Returns:</B><DD><code>Mapper</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if more than one mapper is defined.</DL>
</DD>
</DL>
<HR>

<A NAME="add(org.apache.tools.ant.util.FileNameMapper)"><!-- --></A><H3>
add</H3>
<PRE>
public void <B>add</B>(<A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A>&nbsp;fileNameMapper)</PRE>
<DL>
<DD>Add a nested FileNameMapper.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fileNameMapper</CODE> - the mapper to add.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="checkConfiguration()"><!-- --></A><H3>
checkConfiguration</H3>
<PRE>
protected void <B>checkConfiguration</B>()</PRE>
<DL>
<DD>Check the configuration of this ExecuteOn instance.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#checkConfiguration()">checkConfiguration</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A></CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="createHandler()"><!-- --></A><H3>
createHandler</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A> <B>createHandler</B>()
                                      throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Create the ExecuteStreamHandler instance that will be used
 during execution.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#createHandler()">createHandler</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD><code>ExecuteStreamHandler</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error.</DL>
</DD>
</DL>
<HR>

<A NAME="setupRedirector()"><!-- --></A><H3>
setupRedirector</H3>
<PRE>
protected void <B>setupRedirector</B>()</PRE>
<DL>
<DD>Set up the I/O Redirector.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setupRedirector()">setupRedirector</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A></CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="runExec(org.apache.tools.ant.taskdefs.Execute)"><!-- --></A><H3>
runExec</H3>
<PRE>
protected void <B>runExec</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</A>&nbsp;exe)
                throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Run the specified Execute object.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#runExec(org.apache.tools.ant.taskdefs.Execute)">runExec</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>exe</CODE> - the Execute instance representing the external process.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="getCommandline(java.lang.String[], java.io.File[])"><!-- --></A><H3>
getCommandline</H3>
<PRE>
protected java.lang.String[] <B>getCommandline</B>(java.lang.String[]&nbsp;srcFiles,
                                            java.io.File[]&nbsp;baseDirs)</PRE>
<DL>
<DD>Construct the command line for parallel execution.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>srcFiles</CODE> - The filenames to add to the commandline.<DD><CODE>baseDirs</CODE> - filenames are relative to this dir.
<DT><B>Returns:</B><DD>the command line in the form of a String[].</DL>
</DD>
</DL>
<HR>

<A NAME="getCommandline(java.lang.String, java.io.File)"><!-- --></A><H3>
getCommandline</H3>
<PRE>
protected java.lang.String[] <B>getCommandline</B>(java.lang.String&nbsp;srcFile,
                                            java.io.File&nbsp;baseDir)</PRE>
<DL>
<DD>Construct the command line for serial execution.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>srcFile</CODE> - The filename to add to the commandline.<DD><CODE>baseDir</CODE> - filename is relative to this dir.
<DT><B>Returns:</B><DD>the command line in the form of a String[].</DL>
</DD>
</DL>
<HR>

<A NAME="getFiles(java.io.File, org.apache.tools.ant.DirectoryScanner)"><!-- --></A><H3>
getFiles</H3>
<PRE>
protected java.lang.String[] <B>getFiles</B>(java.io.File&nbsp;baseDir,
                                      <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</A>&nbsp;ds)</PRE>
<DL>
<DD>Return the list of files from this DirectoryScanner that should
 be included on the command line.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>baseDir</CODE> - the File base directory.<DD><CODE>ds</CODE> - the DirectoryScanner to use for file scanning.
<DT><B>Returns:</B><DD>a String[] containing the filenames.</DL>
</DD>
</DL>
<HR>

<A NAME="getDirs(java.io.File, org.apache.tools.ant.DirectoryScanner)"><!-- --></A><H3>
getDirs</H3>
<PRE>
protected java.lang.String[] <B>getDirs</B>(java.io.File&nbsp;baseDir,
                                     <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</A>&nbsp;ds)</PRE>
<DL>
<DD>Return the list of Directories from this DirectoryScanner that
 should be included on the command line.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>baseDir</CODE> - the File base directory.<DD><CODE>ds</CODE> - the DirectoryScanner to use for file scanning.
<DT><B>Returns:</B><DD>a String[] containing the directory names.</DL>
</DD>
</DL>
<HR>

<A NAME="getFilesAndDirs(org.apache.tools.ant.types.FileList)"><!-- --></A><H3>
getFilesAndDirs</H3>
<PRE>
protected java.lang.String[] <B>getFilesAndDirs</B>(<A HREF="../../../../../org/apache/tools/ant/types/FileList.html" title="class in org.apache.tools.ant.types">FileList</A>&nbsp;list)</PRE>
<DL>
<DD>Return the list of files or directories from this FileList that
 should be included on the command line.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>list</CODE> - the FileList to check.
<DT><B>Returns:</B><DD>a String[] containing the directory names.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="runParallel(org.apache.tools.ant.taskdefs.Execute, java.util.Vector, java.util.Vector)"><!-- --></A><H3>
runParallel</H3>
<PRE>
protected void <B>runParallel</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</A>&nbsp;exe,
                           java.util.Vector&nbsp;fileNames,
                           java.util.Vector&nbsp;baseDirs)
                    throws java.io.IOException,
                           <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Run the command in "parallel" mode, making sure that at most
 maxParallel sourcefiles get passed on the command line.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>exe</CODE> - the Executable to use.<DD><CODE>fileNames</CODE> - the Vector of filenames.<DD><CODE>baseDirs</CODE> - the Vector of base directories corresponding to fileNames.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on I/O errors.
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on other errors.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.FileDirBoth.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/ExecuteOn.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ExecuteOn.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
