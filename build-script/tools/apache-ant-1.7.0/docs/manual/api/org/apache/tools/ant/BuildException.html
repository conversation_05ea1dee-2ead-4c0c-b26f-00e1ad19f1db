<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:19 EST 2006 -->
<TITLE>
BuildException (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.BuildException class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="BuildException (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/BuildException.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="BuildException.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class BuildException</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by ">java.lang.Throwable
      <IMG SRC="../../../../resources/inherit.gif" ALT="extended by ">java.lang.Exception
          <IMG SRC="../../../../resources/inherit.gif" ALT="extended by ">java.lang.RuntimeException
              <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.BuildException</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.io.Serializable</DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../org/apache/tools/ant/ExitStatusException.html" title="class in org.apache.tools.ant">ExitStatusException</A>, <A HREF="../../../../org/apache/tools/ant/UnsupportedAttributeException.html" title="class in org.apache.tools.ant">UnsupportedAttributeException</A>, <A HREF="../../../../org/apache/tools/ant/UnsupportedElementException.html" title="class in org.apache.tools.ant">UnsupportedElementException</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>BuildException</B><DT>extends java.lang.RuntimeException</DL>
</PRE>

<P>
Signals an error condition during a build
<P>

<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../serialized-form.html#org.apache.tools.ant.BuildException">Serialized Form</A></DL>
<HR>

<P>

<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildException.html#BuildException()">BuildException</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructs a build exception with no descriptive information.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildException.html#BuildException(java.lang.String)">BuildException</A></B>(java.lang.String&nbsp;message)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructs an exception with the given descriptive message.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildException.html#BuildException(java.lang.String, org.apache.tools.ant.Location)">BuildException</A></B>(java.lang.String&nbsp;message,
               <A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A>&nbsp;location)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructs an exception with the given descriptive message and a
 location in a file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildException.html#BuildException(java.lang.String, java.lang.Throwable)">BuildException</A></B>(java.lang.String&nbsp;message,
               java.lang.Throwable&nbsp;cause)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructs an exception with the given message and exception as
 a root cause.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildException.html#BuildException(java.lang.String, java.lang.Throwable, org.apache.tools.ant.Location)">BuildException</A></B>(java.lang.String&nbsp;msg,
               java.lang.Throwable&nbsp;cause,
               <A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A>&nbsp;location)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructs an exception with the given message and exception as
 a root cause and a location in a file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildException.html#BuildException(java.lang.Throwable)">BuildException</A></B>(java.lang.Throwable&nbsp;cause)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructs an exception with the given exception as a root cause.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildException.html#BuildException(java.lang.Throwable, org.apache.tools.ant.Location)">BuildException</A></B>(java.lang.Throwable&nbsp;cause,
               <A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A>&nbsp;location)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructs an exception with the given exception as
 a root cause and a location in a file.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Throwable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildException.html#getCause()">getCause</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the nested exception, if any.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Throwable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildException.html#getException()">getException</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the nested exception, if any.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildException.html#getLocation()">getLocation</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the file location where the error occurred.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildException.html#printStackTrace()">printStackTrace</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Prints the stack trace for this exception and any
 nested exception to <code>System.err</code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildException.html#printStackTrace(java.io.PrintStream)">printStackTrace</A></B>(java.io.PrintStream&nbsp;ps)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Prints the stack trace of this exception and any nested
 exception to the specified PrintStream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildException.html#printStackTrace(java.io.PrintWriter)">printStackTrace</A></B>(java.io.PrintWriter&nbsp;pw)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Prints the stack trace of this exception and any nested
 exception to the specified PrintWriter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildException.html#setLocation(org.apache.tools.ant.Location)">setLocation</A></B>(<A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A>&nbsp;location)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the file location where the error occurred.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildException.html#toString()">toString</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the location of the error and the error message.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Throwable"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Throwable</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>fillInStackTrace, getLocalizedMessage, getMessage, getStackTrace, initCause, setStackTrace</CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="BuildException()"><!-- --></A><H3>
BuildException</H3>
<PRE>
public <B>BuildException</B>()</PRE>
<DL>
<DD>Constructs a build exception with no descriptive information.
<P>
</DL>
<HR>

<A NAME="BuildException(java.lang.String)"><!-- --></A><H3>
BuildException</H3>
<PRE>
public <B>BuildException</B>(java.lang.String&nbsp;message)</PRE>
<DL>
<DD>Constructs an exception with the given descriptive message.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>message</CODE> - A description of or information about the exception.
            Should not be <code>null</code>.</DL>
</DL>
<HR>

<A NAME="BuildException(java.lang.String, java.lang.Throwable)"><!-- --></A><H3>
BuildException</H3>
<PRE>
public <B>BuildException</B>(java.lang.String&nbsp;message,
                      java.lang.Throwable&nbsp;cause)</PRE>
<DL>
<DD>Constructs an exception with the given message and exception as
 a root cause.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>message</CODE> - A description of or information about the exception.
            Should not be <code>null</code> unless a cause is specified.<DD><CODE>cause</CODE> - The exception that might have caused this one.
              May be <code>null</code>.</DL>
</DL>
<HR>

<A NAME="BuildException(java.lang.String, java.lang.Throwable, org.apache.tools.ant.Location)"><!-- --></A><H3>
BuildException</H3>
<PRE>
public <B>BuildException</B>(java.lang.String&nbsp;msg,
                      java.lang.Throwable&nbsp;cause,
                      <A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A>&nbsp;location)</PRE>
<DL>
<DD>Constructs an exception with the given message and exception as
 a root cause and a location in a file.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>msg</CODE> - A description of or information about the exception.
            Should not be <code>null</code> unless a cause is specified.<DD><CODE>cause</CODE> - The exception that might have caused this one.
              May be <code>null</code>.<DD><CODE>location</CODE> - The location in the project file where the error
                 occurred. Must not be <code>null</code>.</DL>
</DL>
<HR>

<A NAME="BuildException(java.lang.Throwable)"><!-- --></A><H3>
BuildException</H3>
<PRE>
public <B>BuildException</B>(java.lang.Throwable&nbsp;cause)</PRE>
<DL>
<DD>Constructs an exception with the given exception as a root cause.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>cause</CODE> - The exception that might have caused this one.
              Should not be <code>null</code>.</DL>
</DL>
<HR>

<A NAME="BuildException(java.lang.String, org.apache.tools.ant.Location)"><!-- --></A><H3>
BuildException</H3>
<PRE>
public <B>BuildException</B>(java.lang.String&nbsp;message,
                      <A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A>&nbsp;location)</PRE>
<DL>
<DD>Constructs an exception with the given descriptive message and a
 location in a file.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>message</CODE> - A description of or information about the exception.
            Should not be <code>null</code>.<DD><CODE>location</CODE> - The location in the project file where the error
                 occurred. Must not be <code>null</code>.</DL>
</DL>
<HR>

<A NAME="BuildException(java.lang.Throwable, org.apache.tools.ant.Location)"><!-- --></A><H3>
BuildException</H3>
<PRE>
public <B>BuildException</B>(java.lang.Throwable&nbsp;cause,
                      <A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A>&nbsp;location)</PRE>
<DL>
<DD>Constructs an exception with the given exception as
 a root cause and a location in a file.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>cause</CODE> - The exception that might have caused this one.
              Should not be <code>null</code>.<DD><CODE>location</CODE> - The location in the project file where the error
                 occurred. Must not be <code>null</code>.</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="getException()"><!-- --></A><H3>
getException</H3>
<PRE>
public java.lang.Throwable <B>getException</B>()</PRE>
<DL>
<DD>Returns the nested exception, if any.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the nested exception, or <code>null</code> if no
         exception is associated with this one</DL>
</DD>
</DL>
<HR>

<A NAME="getCause()"><!-- --></A><H3>
getCause</H3>
<PRE>
public java.lang.Throwable <B>getCause</B>()</PRE>
<DL>
<DD>Returns the nested exception, if any.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>getCause</CODE> in class <CODE>java.lang.Throwable</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the nested exception, or <code>null</code> if no
         exception is associated with this one</DL>
</DD>
</DL>
<HR>

<A NAME="toString()"><!-- --></A><H3>
toString</H3>
<PRE>
public java.lang.String <B>toString</B>()</PRE>
<DL>
<DD>Returns the location of the error and the error message.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>toString</CODE> in class <CODE>java.lang.Throwable</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the location of the error and the error message</DL>
</DD>
</DL>
<HR>

<A NAME="setLocation(org.apache.tools.ant.Location)"><!-- --></A><H3>
setLocation</H3>
<PRE>
public void <B>setLocation</B>(<A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A>&nbsp;location)</PRE>
<DL>
<DD>Sets the file location where the error occurred.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>location</CODE> - The file location where the error occurred.
                 Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="getLocation()"><!-- --></A><H3>
getLocation</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A> <B>getLocation</B>()</PRE>
<DL>
<DD>Returns the file location where the error occurred.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the file location where the error occurred.</DL>
</DD>
</DL>
<HR>

<A NAME="printStackTrace()"><!-- --></A><H3>
printStackTrace</H3>
<PRE>
public void <B>printStackTrace</B>()</PRE>
<DL>
<DD>Prints the stack trace for this exception and any
 nested exception to <code>System.err</code>.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>printStackTrace</CODE> in class <CODE>java.lang.Throwable</CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="printStackTrace(java.io.PrintStream)"><!-- --></A><H3>
printStackTrace</H3>
<PRE>
public void <B>printStackTrace</B>(java.io.PrintStream&nbsp;ps)</PRE>
<DL>
<DD>Prints the stack trace of this exception and any nested
 exception to the specified PrintStream.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>printStackTrace</CODE> in class <CODE>java.lang.Throwable</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ps</CODE> - The PrintStream to print the stack trace to.
           Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="printStackTrace(java.io.PrintWriter)"><!-- --></A><H3>
printStackTrace</H3>
<PRE>
public void <B>printStackTrace</B>(java.io.PrintWriter&nbsp;pw)</PRE>
<DL>
<DD>Prints the stack trace of this exception and any nested
 exception to the specified PrintWriter.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>printStackTrace</CODE> in class <CODE>java.lang.Throwable</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pw</CODE> - The PrintWriter to print the stack trace to.
           Must not be <code>null</code>.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/BuildException.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="BuildException.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
