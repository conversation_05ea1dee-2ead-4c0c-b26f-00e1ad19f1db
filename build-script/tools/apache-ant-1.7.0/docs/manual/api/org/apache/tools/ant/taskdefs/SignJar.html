<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:24 EST 2006 -->
<TITLE>
SignJar (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.SignJar class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="SignJar (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Sequential.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Sleep.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/SignJar.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="SignJar.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class SignJar</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.AbstractJarSignerTask</A>
              <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.SignJar</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>SignJar</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</A></DL>
</PRE>

<P>
Signs JAR or ZIP files with the javasign command line tool. The tool detailed
 dependency checking: files are only signed if they are not signed. The
 <tt>signjar</tt> attribute can point to the file to generate; if this file
 exists then its modification date is used as a cue as to whether to resign
 any JAR file.

 Timestamp driven signing is based on the unstable and inadequately documented
 information in the Java1.5 docs
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.1</DD>
<DT><B>See Also:</B><DD><a href="http://java.sun.com/j2se/1.5.0/docs/guide/security/time-of-signing-beta1.html">
 beta documentation</a></DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#destDir">destDir</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;the output directory when using paths.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#ERROR_BAD_MAP">ERROR_BAD_MAP</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;error string for unit test verification: "Cannot map source file to anything sensible: "</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#ERROR_MAPPER_WITHOUT_DEST">ERROR_MAPPER_WITHOUT_DEST</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;error string for unit test verification: "The destDir attribute is required if a mapper is set"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#ERROR_NO_ALIAS">ERROR_NO_ALIAS</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;error string for unit test verification: "alias attribute must be set"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#ERROR_NO_STOREPASS">ERROR_NO_STOREPASS</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;error string for unit test verification: "storepass attribute must be set"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#ERROR_SIGNEDJAR_AND_PATHS">ERROR_SIGNEDJAR_AND_PATHS</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;error string for unit test verification "You cannot specify the signed JAR when using paths or filesets"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#ERROR_TODIR_AND_SIGNEDJAR">ERROR_TODIR_AND_SIGNEDJAR</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;error string for unit test verification: "\'destdir\' and \'signedjar\' cannot both be set"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#ERROR_TOO_MANY_MAPPERS">ERROR_TOO_MANY_MAPPERS</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;error string for unit test verification: "Too many mappers"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#internalsf">internalsf</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;flag for internal sf signing</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#lazy">lazy</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether to assume a jar which has an appropriate .SF file in is already
 signed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#sectionsonly">sectionsonly</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sign sections only?</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#sigfile">sigfile</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;name to a signature file</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#signedjar">signedjar</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;name of a single jar</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#tsacert">tsacert</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;alias for the TSA in the keystore</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#tsaurl">tsaurl</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;URL for a tsa; null implies no tsa support</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.AbstractJarSignerTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#alias">alias</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#ERROR_NO_SOURCE">ERROR_NO_SOURCE</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#filesets">filesets</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#jar">jar</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#JARSIGNER_COMMAND">JARSIGNER_COMMAND</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#keypass">keypass</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#keystore">keystore</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#maxMemory">maxMemory</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#storepass">storepass</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#storetype">storetype</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#verbose">verbose</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#SignJar()">SignJar</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#add(org.apache.tools.ant.util.FileNameMapper)">add</A></B>(<A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A>&nbsp;newMapper)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a mapper to determine file naming policy.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sign the jar(s)</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#getMapper()">getMapper</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;get the active mapper; may be null</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#getTsacert()">getTsacert</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;get the -tsacert option</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#getTsaurl()">getTsaurl</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;get the -tsaurl url</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#isSigned(java.io.File)">isSigned</A></B>(java.io.File&nbsp;file)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;test for a file being signed, by looking for a signature in the META-INF
 directory with our alias.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#isUpToDate(java.io.File, java.io.File)">isUpToDate</A></B>(java.io.File&nbsp;jarFile,
           java.io.File&nbsp;signedjarFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Compare a jar file with its corresponding signed jar.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#setDestDir(java.io.File)">setDestDir</A></B>(java.io.File&nbsp;destDir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Optionally sets the output directory to be used.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#setInternalsf(boolean)">setInternalsf</A></B>(boolean&nbsp;internalsf)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Flag to include the .SF file inside the signature; optional; default
 false</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#setLazy(boolean)">setLazy</A></B>(boolean&nbsp;lazy)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;flag to control whether the presence of a signature file means a JAR is
 signed; optional, default false</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#setPreserveLastModified(boolean)">setPreserveLastModified</A></B>(boolean&nbsp;preserveLastModified)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;true to indicate that the signed jar modification date remains the same
 as the original.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#setSectionsonly(boolean)">setSectionsonly</A></B>(boolean&nbsp;sectionsonly)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;flag to compute hash of entire manifest; optional, default false</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#setSigfile(java.lang.String)">setSigfile</A></B>(java.lang.String&nbsp;sigfile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;name of .SF/.DSA file; optional</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#setSignedjar(java.io.File)">setSignedjar</A></B>(java.io.File&nbsp;signedjar)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;name of signed JAR file; optional</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#setTsacert(java.lang.String)">setTsacert</A></B>(java.lang.String&nbsp;tsacert)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;set the alias in the keystore of the TSA to use;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#setTsaurl(java.lang.String)">setTsaurl</A></B>(java.lang.String&nbsp;tsaurl)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.AbstractJarSignerTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#addSysproperty(org.apache.tools.ant.types.Environment.Variable)">addSysproperty</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#addValue(org.apache.tools.ant.taskdefs.ExecTask, java.lang.String)">addValue</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#beginExecution()">beginExecution</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#bindToKeystore(org.apache.tools.ant.taskdefs.ExecTask)">bindToKeystore</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#createJarSigner()">createJarSigner</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#createPath()">createPath</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#createUnifiedSourcePath()">createUnifiedSourcePath</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#createUnifiedSources()">createUnifiedSources</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#declareSysProperty(org.apache.tools.ant.taskdefs.ExecTask, org.apache.tools.ant.types.Environment.Variable)">declareSysProperty</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#endExecution()">endExecution</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#getRedirector()">getRedirector</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#hasResources()">hasResources</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setAlias(java.lang.String)">setAlias</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setCommonOptions(org.apache.tools.ant.taskdefs.ExecTask)">setCommonOptions</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setJar(java.io.File)">setJar</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setKeypass(java.lang.String)">setKeypass</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setKeystore(java.lang.String)">setKeystore</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setMaxmemory(java.lang.String)">setMaxmemory</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setStorepass(java.lang.String)">setStorepass</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setStoretype(java.lang.String)">setStoretype</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setVerbose(boolean)">setVerbose</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="sigfile"><!-- --></A><H3>
sigfile</H3>
<PRE>
protected java.lang.String <B>sigfile</B></PRE>
<DL>
<DD>name to a signature file
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="signedjar"><!-- --></A><H3>
signedjar</H3>
<PRE>
protected java.io.File <B>signedjar</B></PRE>
<DL>
<DD>name of a single jar
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="internalsf"><!-- --></A><H3>
internalsf</H3>
<PRE>
protected boolean <B>internalsf</B></PRE>
<DL>
<DD>flag for internal sf signing
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="sectionsonly"><!-- --></A><H3>
sectionsonly</H3>
<PRE>
protected boolean <B>sectionsonly</B></PRE>
<DL>
<DD>sign sections only?
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="lazy"><!-- --></A><H3>
lazy</H3>
<PRE>
protected boolean <B>lazy</B></PRE>
<DL>
<DD>Whether to assume a jar which has an appropriate .SF file in is already
 signed.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="destDir"><!-- --></A><H3>
destDir</H3>
<PRE>
protected java.io.File <B>destDir</B></PRE>
<DL>
<DD>the output directory when using paths.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="tsaurl"><!-- --></A><H3>
tsaurl</H3>
<PRE>
protected java.lang.String <B>tsaurl</B></PRE>
<DL>
<DD>URL for a tsa; null implies no tsa support
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="tsacert"><!-- --></A><H3>
tsacert</H3>
<PRE>
protected java.lang.String <B>tsacert</B></PRE>
<DL>
<DD>alias for the TSA in the keystore
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="ERROR_TODIR_AND_SIGNEDJAR"><!-- --></A><H3>
ERROR_TODIR_AND_SIGNEDJAR</H3>
<PRE>
public static final java.lang.String <B>ERROR_TODIR_AND_SIGNEDJAR</B></PRE>
<DL>
<DD>error string for unit test verification: "\'destdir\' and \'signedjar\' cannot both be set"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.SignJar.ERROR_TODIR_AND_SIGNEDJAR">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_TOO_MANY_MAPPERS"><!-- --></A><H3>
ERROR_TOO_MANY_MAPPERS</H3>
<PRE>
public static final java.lang.String <B>ERROR_TOO_MANY_MAPPERS</B></PRE>
<DL>
<DD>error string for unit test verification: "Too many mappers"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.SignJar.ERROR_TOO_MANY_MAPPERS">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_SIGNEDJAR_AND_PATHS"><!-- --></A><H3>
ERROR_SIGNEDJAR_AND_PATHS</H3>
<PRE>
public static final java.lang.String <B>ERROR_SIGNEDJAR_AND_PATHS</B></PRE>
<DL>
<DD>error string for unit test verification "You cannot specify the signed JAR when using paths or filesets"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.SignJar.ERROR_SIGNEDJAR_AND_PATHS">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_BAD_MAP"><!-- --></A><H3>
ERROR_BAD_MAP</H3>
<PRE>
public static final java.lang.String <B>ERROR_BAD_MAP</B></PRE>
<DL>
<DD>error string for unit test verification: "Cannot map source file to anything sensible: "
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.SignJar.ERROR_BAD_MAP">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_MAPPER_WITHOUT_DEST"><!-- --></A><H3>
ERROR_MAPPER_WITHOUT_DEST</H3>
<PRE>
public static final java.lang.String <B>ERROR_MAPPER_WITHOUT_DEST</B></PRE>
<DL>
<DD>error string for unit test verification: "The destDir attribute is required if a mapper is set"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.SignJar.ERROR_MAPPER_WITHOUT_DEST">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_NO_ALIAS"><!-- --></A><H3>
ERROR_NO_ALIAS</H3>
<PRE>
public static final java.lang.String <B>ERROR_NO_ALIAS</B></PRE>
<DL>
<DD>error string for unit test verification: "alias attribute must be set"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.SignJar.ERROR_NO_ALIAS">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_NO_STOREPASS"><!-- --></A><H3>
ERROR_NO_STOREPASS</H3>
<PRE>
public static final java.lang.String <B>ERROR_NO_STOREPASS</B></PRE>
<DL>
<DD>error string for unit test verification: "storepass attribute must be set"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.SignJar.ERROR_NO_STOREPASS">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="SignJar()"><!-- --></A><H3>
SignJar</H3>
<PRE>
public <B>SignJar</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setSigfile(java.lang.String)"><!-- --></A><H3>
setSigfile</H3>
<PRE>
public void <B>setSigfile</B>(java.lang.String&nbsp;sigfile)</PRE>
<DL>
<DD>name of .SF/.DSA file; optional
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sigfile</CODE> - the name of the .SF/.DSA file</DL>
</DD>
</DL>
<HR>

<A NAME="setSignedjar(java.io.File)"><!-- --></A><H3>
setSignedjar</H3>
<PRE>
public void <B>setSignedjar</B>(java.io.File&nbsp;signedjar)</PRE>
<DL>
<DD>name of signed JAR file; optional
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>signedjar</CODE> - the name of the signed jar file</DL>
</DD>
</DL>
<HR>

<A NAME="setInternalsf(boolean)"><!-- --></A><H3>
setInternalsf</H3>
<PRE>
public void <B>setInternalsf</B>(boolean&nbsp;internalsf)</PRE>
<DL>
<DD>Flag to include the .SF file inside the signature; optional; default
 false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>internalsf</CODE> - if true include the .SF file inside the signature</DL>
</DD>
</DL>
<HR>

<A NAME="setSectionsonly(boolean)"><!-- --></A><H3>
setSectionsonly</H3>
<PRE>
public void <B>setSectionsonly</B>(boolean&nbsp;sectionsonly)</PRE>
<DL>
<DD>flag to compute hash of entire manifest; optional, default false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sectionsonly</CODE> - flag to compute hash of entire manifest</DL>
</DD>
</DL>
<HR>

<A NAME="setLazy(boolean)"><!-- --></A><H3>
setLazy</H3>
<PRE>
public void <B>setLazy</B>(boolean&nbsp;lazy)</PRE>
<DL>
<DD>flag to control whether the presence of a signature file means a JAR is
 signed; optional, default false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>lazy</CODE> - flag to control whether the presence of a signature</DL>
</DD>
</DL>
<HR>

<A NAME="setDestDir(java.io.File)"><!-- --></A><H3>
setDestDir</H3>
<PRE>
public void <B>setDestDir</B>(java.io.File&nbsp;destDir)</PRE>
<DL>
<DD>Optionally sets the output directory to be used.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>destDir</CODE> - the directory in which to place signed jars<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="add(org.apache.tools.ant.util.FileNameMapper)"><!-- --></A><H3>
add</H3>
<PRE>
public void <B>add</B>(<A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A>&nbsp;newMapper)</PRE>
<DL>
<DD>add a mapper to determine file naming policy. Only used with toDir
 processing.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>newMapper</CODE> - the mapper to add.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getMapper()"><!-- --></A><H3>
getMapper</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A> <B>getMapper</B>()</PRE>
<DL>
<DD>get the active mapper; may be null
<P>
<DD><DL>

<DT><B>Returns:</B><DD>mapper or null<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getTsaurl()"><!-- --></A><H3>
getTsaurl</H3>
<PRE>
public java.lang.String <B>getTsaurl</B>()</PRE>
<DL>
<DD>get the -tsaurl url
<P>
<DD><DL>

<DT><B>Returns:</B><DD>url or null<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setTsaurl(java.lang.String)"><!-- --></A><H3>
setTsaurl</H3>
<PRE>
public void <B>setTsaurl</B>(java.lang.String&nbsp;tsaurl)</PRE>
<DL>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>tsaurl</CODE> - the tsa url.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getTsacert()"><!-- --></A><H3>
getTsacert</H3>
<PRE>
public java.lang.String <B>getTsacert</B>()</PRE>
<DL>
<DD>get the -tsacert option
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a certificate alias or null<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setTsacert(java.lang.String)"><!-- --></A><H3>
setTsacert</H3>
<PRE>
public void <B>setTsacert</B>(java.lang.String&nbsp;tsacert)</PRE>
<DL>
<DD>set the alias in the keystore of the TSA to use;
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>tsacert</CODE> - the cert alias.</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>sign the jar(s)
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on errors</DL>
</DD>
</DL>
<HR>

<A NAME="isUpToDate(java.io.File, java.io.File)"><!-- --></A><H3>
isUpToDate</H3>
<PRE>
protected boolean <B>isUpToDate</B>(java.io.File&nbsp;jarFile,
                             java.io.File&nbsp;signedjarFile)</PRE>
<DL>
<DD>Compare a jar file with its corresponding signed jar. The logic for this
 is complex, and best explained in the source itself. Essentially if
 either file doesnt exist, or the destfile has an out of date timestamp,
 then the return value is false.
 <p/>
 If we are signing ourself, the check <A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html#isSigned(java.io.File)"><CODE>isSigned(File)</CODE></A> is used to
 trigger the process.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>jarFile</CODE> - the unsigned jar file<DD><CODE>signedjarFile</CODE> - the result signed jar file
<DT><B>Returns:</B><DD>true if the signedjarFile is considered up to date</DL>
</DD>
</DL>
<HR>

<A NAME="isSigned(java.io.File)"><!-- --></A><H3>
isSigned</H3>
<PRE>
protected boolean <B>isSigned</B>(java.io.File&nbsp;file)</PRE>
<DL>
<DD>test for a file being signed, by looking for a signature in the META-INF
 directory with our alias.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>file</CODE> - the file to be checked
<DT><B>Returns:</B><DD>true if the file is signed<DT><B>See Also:</B><DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/IsSigned.html#isSigned(java.io.File, java.lang.String)"><CODE>IsSigned.isSigned(File, String)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setPreserveLastModified(boolean)"><!-- --></A><H3>
setPreserveLastModified</H3>
<PRE>
public void <B>setPreserveLastModified</B>(boolean&nbsp;preserveLastModified)</PRE>
<DL>
<DD>true to indicate that the signed jar modification date remains the same
 as the original. Defaults to false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>preserveLastModified</CODE> - if true preserve the last modified time</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Sequential.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Sleep.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/SignJar.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="SignJar.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
