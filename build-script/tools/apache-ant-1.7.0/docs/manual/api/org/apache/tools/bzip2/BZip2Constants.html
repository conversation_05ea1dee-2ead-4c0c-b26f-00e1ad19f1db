<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:34 EST 2006 -->
<TITLE>
BZip2Constants (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.bzip2.BZip2Constants interface">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="BZip2Constants (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV CLASS&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/bzip2/CBZip2InputStream.html" title="class in org.apache.tools.bzip2"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/bzip2/BZip2Constants.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="BZip2Constants.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;METHOD</FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;METHOD</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.bzip2</FONT>
<BR>
Interface BZip2Constants</H2>
<DL>
<DT><B>All Known Implementing Classes:</B> <DD><A HREF="../../../../org/apache/tools/bzip2/CBZip2InputStream.html" title="class in org.apache.tools.bzip2">CBZip2InputStream</A>, <A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html" title="class in org.apache.tools.bzip2">CBZip2OutputStream</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public interface <B>BZip2Constants</B></DL>
</PRE>

<P>
Base class for both the compress and decompress classes.
 Holds common arrays, and static data.
 <p>
 This interface is public for historical purposes.
 You should have no need to use it.
 </p>
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#baseBlockSize">baseBlockSize</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#G_SIZE">G_SIZE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#MAX_ALPHA_SIZE">MAX_ALPHA_SIZE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#MAX_CODE_LEN">MAX_CODE_LEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#MAX_SELECTORS">MAX_SELECTORS</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#N_GROUPS">N_GROUPS</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#N_ITERS">N_ITERS</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#NUM_OVERSHOOT_BYTES">NUM_OVERSHOOT_BYTES</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#rNums">rNums</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This array really shouldn't be here.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#RUNA">RUNA</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#RUNB">RUNB</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="baseBlockSize"><!-- --></A><H3>
baseBlockSize</H3>
<PRE>
static final int <B>baseBlockSize</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.BZip2Constants.baseBlockSize">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="MAX_ALPHA_SIZE"><!-- --></A><H3>
MAX_ALPHA_SIZE</H3>
<PRE>
static final int <B>MAX_ALPHA_SIZE</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.BZip2Constants.MAX_ALPHA_SIZE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="MAX_CODE_LEN"><!-- --></A><H3>
MAX_CODE_LEN</H3>
<PRE>
static final int <B>MAX_CODE_LEN</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.BZip2Constants.MAX_CODE_LEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="RUNA"><!-- --></A><H3>
RUNA</H3>
<PRE>
static final int <B>RUNA</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.BZip2Constants.RUNA">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="RUNB"><!-- --></A><H3>
RUNB</H3>
<PRE>
static final int <B>RUNB</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.BZip2Constants.RUNB">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="N_GROUPS"><!-- --></A><H3>
N_GROUPS</H3>
<PRE>
static final int <B>N_GROUPS</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.BZip2Constants.N_GROUPS">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="G_SIZE"><!-- --></A><H3>
G_SIZE</H3>
<PRE>
static final int <B>G_SIZE</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.BZip2Constants.G_SIZE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="N_ITERS"><!-- --></A><H3>
N_ITERS</H3>
<PRE>
static final int <B>N_ITERS</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.BZip2Constants.N_ITERS">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="MAX_SELECTORS"><!-- --></A><H3>
MAX_SELECTORS</H3>
<PRE>
static final int <B>MAX_SELECTORS</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.BZip2Constants.MAX_SELECTORS">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="NUM_OVERSHOOT_BYTES"><!-- --></A><H3>
NUM_OVERSHOOT_BYTES</H3>
<PRE>
static final int <B>NUM_OVERSHOOT_BYTES</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.BZip2Constants.NUM_OVERSHOOT_BYTES">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="rNums"><!-- --></A><H3>
rNums</H3>
<PRE>
static final int[] <B>rNums</B></PRE>
<DL>
<DD>This array really shouldn't be here.
 Again, for historical purposes it is.

 <p>FIXME: This array should be in a private or package private
 location, since it could be modified by malicious code.</p>
<P>
<DL>
</DL>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV CLASS&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/bzip2/CBZip2InputStream.html" title="class in org.apache.tools.bzip2"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/bzip2/BZip2Constants.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="BZip2Constants.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;METHOD</FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;METHOD</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
