<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:26 EST 2006 -->
<TITLE>
NetRexxC (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.optional.NetRexxC class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="NetRexxC (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Native2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.TraceAttr.html" title="class in org.apache.tools.ant.taskdefs.optional"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/NetRexxC.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="NetRexxC.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.MatchingTask">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.optional</FONT>
<BR>
Class NetRexxC</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.MatchingTask</A>
              <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.optional.NetRexxC</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable, <A HREF="../../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>NetRexxC</B><DT>extends <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></DL>
</PRE>

<P>
Compiles NetRexx source files.
 This task can take the following
 arguments:
 <ul>
 <li>binary</li>
 <li>classpath</li>
 <li>comments</li>
 <li>compile</li>
 <li>console</li>
 <li>crossref</li>
 <li>decimal</li>
 <li>destdir</li>
 <li>diag</li>
 <li>explicit</li>
 <li>format</li>
 <li>keep</li>
 <li>logo</li>
 <li>replace</li>
 <li>savelog</li>
 <li>srcdir</li>
 <li>sourcedir</li>
 <li>strictargs</li>
 <li>strictassign</li>
 <li>strictcase</li>
 <li>strictimport</li>
 <li>symbols</li>
 <li>time</li>
 <li>trace</li>
 <li>utf8</li>
 <li>verbose</li>
 <li>suppressMethodArgumentNotUsed</li>
 <li>suppressPrivatePropertyNotUsed</li>
 <li>suppressVariableNotUsed</li>
 <li>suppressExceptionNotSignalled</li>
 <li>suppressDeprecation</li>
 </ul>
 Of these arguments, the <b>srcdir</b> argument is required.

 <p>When this task executes, it will recursively scan the srcdir
 looking for NetRexx source files to compile. This task makes its
 compile decision based on timestamp.
 <p>Before files are compiled they and any other file in the
 srcdir will be copied to the destdir allowing support files to be
 located properly in the classpath. The reason for copying the source files
 before the compile is that NetRexxC has only two destinations for classfiles:
 <ol>
 <li>The current directory, and,</li>
 <li>The directory the source is in (see sourcedir option)
 </ol>
<P>

<P>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.TraceAttr.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.TraceAttr</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Enumerated class corresponding to the trace attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.VerboseAttr.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.VerboseAttr</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Enumerated class corresponding to the verbose attribute.</TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.MatchingTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#fileset">fileset</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#NetRexxC()">NetRexxC</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Executes the task - performs the actual compiler call.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#init()">init</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;init-Method sets defaults from Properties.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setBinary(boolean)">setBinary</A></B>(boolean&nbsp;binary)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether literals are treated as binary, rather than NetRexx types.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setClasspath(java.lang.String)">setClasspath</A></B>(java.lang.String&nbsp;classpath)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the classpath used for NetRexx compilation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setComments(boolean)">setComments</A></B>(boolean&nbsp;comments)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether comments are passed through to the generated java source.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setCompact(boolean)">setCompact</A></B>(boolean&nbsp;compact)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether error messages come out in compact or verbose format.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setCompile(boolean)">setCompile</A></B>(boolean&nbsp;compile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether the NetRexx compiler should compile the generated java code
 Valid true values are "on" or "true".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setConsole(boolean)">setConsole</A></B>(boolean&nbsp;console)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether or not messages should be displayed on the 'console' Valid
 true values are "on" or "true".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setCrossref(boolean)">setCrossref</A></B>(boolean&nbsp;crossref)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether variable cross references are generated.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setDecimal(boolean)">setDecimal</A></B>(boolean&nbsp;decimal)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether decimal arithmetic should be used for the netrexx code.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setDestDir(java.io.File)">setDestDir</A></B>(java.io.File&nbsp;destDirName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the destination directory into which the NetRexx source files
 should be copied and then compiled.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setDiag(boolean)">setDiag</A></B>(boolean&nbsp;diag)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether diagnostic information about the compile is generated</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setExplicit(boolean)">setExplicit</A></B>(boolean&nbsp;explicit)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets whether variables must be declared explicitly before use.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setFormat(boolean)">setFormat</A></B>(boolean&nbsp;format)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether the generated java code is formatted nicely or left to match
 NetRexx line numbers for call stack debugging.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setJava(boolean)">setJava</A></B>(boolean&nbsp;java)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether the generated java code is produced Valid true values are "on"
 or "true".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setKeep(boolean)">setKeep</A></B>(boolean&nbsp;keep)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets whether the generated java source file should be kept after
 compilation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setLogo(boolean)">setLogo</A></B>(boolean&nbsp;logo)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether the compiler text logo is displayed when compiling.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setReplace(boolean)">setReplace</A></B>(boolean&nbsp;replace)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether the generated .java file should be replaced when compiling
 Valid true values are "on" or "true".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setSavelog(boolean)">setSavelog</A></B>(boolean&nbsp;savelog)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets whether the compiler messages will be written to NetRexxC.log as
 well as to the console Valid true values are "on" or "true".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setSourcedir(boolean)">setSourcedir</A></B>(boolean&nbsp;sourcedir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tells the NetRexx compiler to store the class files in the same
 directory as the source files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setSrcDir(java.io.File)">setSrcDir</A></B>(java.io.File&nbsp;srcDirName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the source dir to find the source Java files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setStrictargs(boolean)">setStrictargs</A></B>(boolean&nbsp;strictargs)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tells the NetRexx compiler that method calls always need parentheses,
 even if no arguments are needed, e.g.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setStrictassign(boolean)">setStrictassign</A></B>(boolean&nbsp;strictassign)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tells the NetRexx compile that assignments must match exactly on type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setStrictcase(boolean)">setStrictcase</A></B>(boolean&nbsp;strictcase)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specifies whether the NetRexx compiler should be case sensitive or not.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setStrictimport(boolean)">setStrictimport</A></B>(boolean&nbsp;strictimport)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets whether classes need to be imported explicitly using an <code>import</code>
 statement.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setStrictprops(boolean)">setStrictprops</A></B>(boolean&nbsp;strictprops)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets whether local properties need to be qualified explicitly using
 <code>this</code> Valid true values are "on" or "true".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setStrictsignal(boolean)">setStrictsignal</A></B>(boolean&nbsp;strictsignal)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether the compiler should force catching of exceptions by explicitly
 named types.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setSuppressDeprecation(boolean)">setSuppressDeprecation</A></B>(boolean&nbsp;suppressDeprecation)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tells whether we should filter out any deprecation-messages
 of the compiler out.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setSuppressExceptionNotSignalled(boolean)">setSuppressExceptionNotSignalled</A></B>(boolean&nbsp;suppressExceptionNotSignalled)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether the task should suppress the "FooException is in SIGNALS list
 but is not signalled within the method", which is sometimes rather
 useless.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setSuppressMethodArgumentNotUsed(boolean)">setSuppressMethodArgumentNotUsed</A></B>(boolean&nbsp;suppressMethodArgumentNotUsed)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether the task should suppress the "Method argument is not used" in
 strictargs-Mode, which can not be suppressed by the compiler itself.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setSuppressPrivatePropertyNotUsed(boolean)">setSuppressPrivatePropertyNotUsed</A></B>(boolean&nbsp;suppressPrivatePropertyNotUsed)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether the task should suppress the "Private property is defined but
 not used" in strictargs-Mode, which can be quite annoying while
 developing.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setSuppressVariableNotUsed(boolean)">setSuppressVariableNotUsed</A></B>(boolean&nbsp;suppressVariableNotUsed)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether the task should suppress the "Variable is set but not used" in
 strictargs-Mode.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setSymbols(boolean)">setSymbols</A></B>(boolean&nbsp;symbols)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets whether debug symbols should be generated into the class file
 Valid true values are "on" or "true".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setTime(boolean)">setTime</A></B>(boolean&nbsp;time)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Asks the NetRexx compiler to print compilation times to the console
 Valid true values are "on" or "true".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setTrace(org.apache.tools.ant.taskdefs.optional.NetRexxC.TraceAttr)">setTrace</A></B>(<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.TraceAttr.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.TraceAttr</A>&nbsp;trace)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Turns on or off tracing and directs the resultant trace output Valid
 values are: "trace", "trace1", "trace2" and "notrace".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setTrace(java.lang.String)">setTrace</A></B>(java.lang.String&nbsp;trace)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Turns on or off tracing and directs the resultant trace output Valid
 values are: "trace", "trace1", "trace2" and "notrace".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setUtf8(boolean)">setUtf8</A></B>(boolean&nbsp;utf8)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tells the NetRexx compiler that the source is in UTF8 Valid true values
 are "on" or "true".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setVerbose(org.apache.tools.ant.taskdefs.optional.NetRexxC.VerboseAttr)">setVerbose</A></B>(<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.VerboseAttr.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.VerboseAttr</A>&nbsp;verbose)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether lots of warnings and error messages should be generated</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html#setVerbose(java.lang.String)">setVerbose</A></B>(java.lang.String&nbsp;verbose)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether lots of warnings and error messages should be generated</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.MatchingTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createExclude()">createExclude</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createExcludesFile()">createExcludesFile</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createInclude()">createInclude</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createIncludesFile()">createIncludesFile</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createPatternSet()">createPatternSet</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#hasSelectors()">hasSelectors</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#selectorCount()">selectorCount</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#selectorElements()">selectorElements</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setExcludes(java.lang.String)">setExcludes</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setIncludes(java.lang.String)">setIncludes</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#XsetItems(java.lang.String)">XsetItems</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="NetRexxC()"><!-- --></A><H3>
NetRexxC</H3>
<PRE>
public <B>NetRexxC</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setBinary(boolean)"><!-- --></A><H3>
setBinary</H3>
<PRE>
public void <B>setBinary</B>(boolean&nbsp;binary)</PRE>
<DL>
<DD>Set whether literals are treated as binary, rather than NetRexx types.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>binary</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setClasspath(java.lang.String)"><!-- --></A><H3>
setClasspath</H3>
<PRE>
public void <B>setClasspath</B>(java.lang.String&nbsp;classpath)</PRE>
<DL>
<DD>Set the classpath used for NetRexx compilation.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classpath</CODE> - the classpath to use.</DL>
</DD>
</DL>
<HR>

<A NAME="setComments(boolean)"><!-- --></A><H3>
setComments</H3>
<PRE>
public void <B>setComments</B>(boolean&nbsp;comments)</PRE>
<DL>
<DD>Set whether comments are passed through to the generated java source.
 Valid true values are "on" or "true". Anything else sets the flag to
 false. The default value is false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>comments</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setCompact(boolean)"><!-- --></A><H3>
setCompact</H3>
<PRE>
public void <B>setCompact</B>(boolean&nbsp;compact)</PRE>
<DL>
<DD>Set whether error messages come out in compact or verbose format. Valid
 true values are "on" or "true". Anything else sets the flag to false.
 The default value is false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>compact</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setCompile(boolean)"><!-- --></A><H3>
setCompile</H3>
<PRE>
public void <B>setCompile</B>(boolean&nbsp;compile)</PRE>
<DL>
<DD>Set whether the NetRexx compiler should compile the generated java code
 Valid true values are "on" or "true". Anything else sets the flag to
 false. The default value is true. Setting this flag to false, will
 automatically set the keep flag to true.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>compile</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setConsole(boolean)"><!-- --></A><H3>
setConsole</H3>
<PRE>
public void <B>setConsole</B>(boolean&nbsp;console)</PRE>
<DL>
<DD>Set whether or not messages should be displayed on the 'console' Valid
 true values are "on" or "true". Anything else sets the flag to false.
 The default value is true.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>console</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setCrossref(boolean)"><!-- --></A><H3>
setCrossref</H3>
<PRE>
public void <B>setCrossref</B>(boolean&nbsp;crossref)</PRE>
<DL>
<DD>Whether variable cross references are generated.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>crossref</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setDecimal(boolean)"><!-- --></A><H3>
setDecimal</H3>
<PRE>
public void <B>setDecimal</B>(boolean&nbsp;decimal)</PRE>
<DL>
<DD>Set whether decimal arithmetic should be used for the netrexx code.
 Binary arithmetic is used when this flag is turned off. Valid true
 values are "on" or "true". Anything else sets the flag to false. The
 default value is true.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>decimal</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setDestDir(java.io.File)"><!-- --></A><H3>
setDestDir</H3>
<PRE>
public void <B>setDestDir</B>(java.io.File&nbsp;destDirName)</PRE>
<DL>
<DD>Set the destination directory into which the NetRexx source files
 should be copied and then compiled.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>destDirName</CODE> - the destination directory.</DL>
</DD>
</DL>
<HR>

<A NAME="setDiag(boolean)"><!-- --></A><H3>
setDiag</H3>
<PRE>
public void <B>setDiag</B>(boolean&nbsp;diag)</PRE>
<DL>
<DD>Whether diagnostic information about the compile is generated
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>diag</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setExplicit(boolean)"><!-- --></A><H3>
setExplicit</H3>
<PRE>
public void <B>setExplicit</B>(boolean&nbsp;explicit)</PRE>
<DL>
<DD>Sets whether variables must be declared explicitly before use. Valid
 true values are "on" or "true". Anything else sets the flag to false.
 The default value is false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>explicit</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setFormat(boolean)"><!-- --></A><H3>
setFormat</H3>
<PRE>
public void <B>setFormat</B>(boolean&nbsp;format)</PRE>
<DL>
<DD>Whether the generated java code is formatted nicely or left to match
 NetRexx line numbers for call stack debugging.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>format</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setJava(boolean)"><!-- --></A><H3>
setJava</H3>
<PRE>
public void <B>setJava</B>(boolean&nbsp;java)</PRE>
<DL>
<DD>Whether the generated java code is produced Valid true values are "on"
 or "true". Anything else sets the flag to false. The default value is
 false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>java</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setKeep(boolean)"><!-- --></A><H3>
setKeep</H3>
<PRE>
public void <B>setKeep</B>(boolean&nbsp;keep)</PRE>
<DL>
<DD>Sets whether the generated java source file should be kept after
 compilation. The generated files will have an extension of .java.keep,
 <b>not</b> .java Valid true values are "on" or "true". Anything else
 sets the flag to false. The default value is false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>keep</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setLogo(boolean)"><!-- --></A><H3>
setLogo</H3>
<PRE>
public void <B>setLogo</B>(boolean&nbsp;logo)</PRE>
<DL>
<DD>Whether the compiler text logo is displayed when compiling.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>logo</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setReplace(boolean)"><!-- --></A><H3>
setReplace</H3>
<PRE>
public void <B>setReplace</B>(boolean&nbsp;replace)</PRE>
<DL>
<DD>Whether the generated .java file should be replaced when compiling
 Valid true values are "on" or "true". Anything else sets the flag to
 false. The default value is false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>replace</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setSavelog(boolean)"><!-- --></A><H3>
setSavelog</H3>
<PRE>
public void <B>setSavelog</B>(boolean&nbsp;savelog)</PRE>
<DL>
<DD>Sets whether the compiler messages will be written to NetRexxC.log as
 well as to the console Valid true values are "on" or "true". Anything
 else sets the flag to false. The default value is false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>savelog</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setSourcedir(boolean)"><!-- --></A><H3>
setSourcedir</H3>
<PRE>
public void <B>setSourcedir</B>(boolean&nbsp;sourcedir)</PRE>
<DL>
<DD>Tells the NetRexx compiler to store the class files in the same
 directory as the source files. The alternative is the working directory
 Valid true values are "on" or "true". Anything else sets the flag to
 false. The default value is true.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourcedir</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setSrcDir(java.io.File)"><!-- --></A><H3>
setSrcDir</H3>
<PRE>
public void <B>setSrcDir</B>(java.io.File&nbsp;srcDirName)</PRE>
<DL>
<DD>Set the source dir to find the source Java files.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>srcDirName</CODE> - the source directory.</DL>
</DD>
</DL>
<HR>

<A NAME="setStrictargs(boolean)"><!-- --></A><H3>
setStrictargs</H3>
<PRE>
public void <B>setStrictargs</B>(boolean&nbsp;strictargs)</PRE>
<DL>
<DD>Tells the NetRexx compiler that method calls always need parentheses,
 even if no arguments are needed, e.g. <code>aStringVar.getBytes</code>
 vs. <code>aStringVar.getBytes()</code> Valid true values are "on" or
 "true". Anything else sets the flag to false. The default value is
 false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>strictargs</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setStrictassign(boolean)"><!-- --></A><H3>
setStrictassign</H3>
<PRE>
public void <B>setStrictassign</B>(boolean&nbsp;strictassign)</PRE>
<DL>
<DD>Tells the NetRexx compile that assignments must match exactly on type.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>strictassign</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setStrictcase(boolean)"><!-- --></A><H3>
setStrictcase</H3>
<PRE>
public void <B>setStrictcase</B>(boolean&nbsp;strictcase)</PRE>
<DL>
<DD>Specifies whether the NetRexx compiler should be case sensitive or not.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>strictcase</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setStrictimport(boolean)"><!-- --></A><H3>
setStrictimport</H3>
<PRE>
public void <B>setStrictimport</B>(boolean&nbsp;strictimport)</PRE>
<DL>
<DD>Sets whether classes need to be imported explicitly using an <code>import</code>
 statement. By default the NetRexx compiler will import certain packages
 automatically Valid true values are "on" or "true". Anything else sets
 the flag to false. The default value is false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>strictimport</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setStrictprops(boolean)"><!-- --></A><H3>
setStrictprops</H3>
<PRE>
public void <B>setStrictprops</B>(boolean&nbsp;strictprops)</PRE>
<DL>
<DD>Sets whether local properties need to be qualified explicitly using
 <code>this</code> Valid true values are "on" or "true". Anything else
 sets the flag to false. The default value is false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>strictprops</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setStrictsignal(boolean)"><!-- --></A><H3>
setStrictsignal</H3>
<PRE>
public void <B>setStrictsignal</B>(boolean&nbsp;strictsignal)</PRE>
<DL>
<DD>Whether the compiler should force catching of exceptions by explicitly
 named types.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>strictsignal</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setSymbols(boolean)"><!-- --></A><H3>
setSymbols</H3>
<PRE>
public void <B>setSymbols</B>(boolean&nbsp;symbols)</PRE>
<DL>
<DD>Sets whether debug symbols should be generated into the class file
 Valid true values are "on" or "true". Anything else sets the flag to
 false. The default value is false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>symbols</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setTime(boolean)"><!-- --></A><H3>
setTime</H3>
<PRE>
public void <B>setTime</B>(boolean&nbsp;time)</PRE>
<DL>
<DD>Asks the NetRexx compiler to print compilation times to the console
 Valid true values are "on" or "true". Anything else sets the flag to
 false. The default value is false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>time</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setTrace(org.apache.tools.ant.taskdefs.optional.NetRexxC.TraceAttr)"><!-- --></A><H3>
setTrace</H3>
<PRE>
public void <B>setTrace</B>(<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.TraceAttr.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.TraceAttr</A>&nbsp;trace)</PRE>
<DL>
<DD>Turns on or off tracing and directs the resultant trace output Valid
 values are: "trace", "trace1", "trace2" and "notrace". "trace" and
 "trace2".
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>trace</CODE> - the value to set.</DL>
</DD>
</DL>
<HR>

<A NAME="setTrace(java.lang.String)"><!-- --></A><H3>
setTrace</H3>
<PRE>
public void <B>setTrace</B>(java.lang.String&nbsp;trace)</PRE>
<DL>
<DD>Turns on or off tracing and directs the resultant trace output Valid
 values are: "trace", "trace1", "trace2" and "notrace". "trace" and
 "trace2".
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>trace</CODE> - the value to set.</DL>
</DD>
</DL>
<HR>

<A NAME="setUtf8(boolean)"><!-- --></A><H3>
setUtf8</H3>
<PRE>
public void <B>setUtf8</B>(boolean&nbsp;utf8)</PRE>
<DL>
<DD>Tells the NetRexx compiler that the source is in UTF8 Valid true values
 are "on" or "true". Anything else sets the flag to false. The default
 value is false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>utf8</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setVerbose(org.apache.tools.ant.taskdefs.optional.NetRexxC.VerboseAttr)"><!-- --></A><H3>
setVerbose</H3>
<PRE>
public void <B>setVerbose</B>(<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.VerboseAttr.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC.VerboseAttr</A>&nbsp;verbose)</PRE>
<DL>
<DD>Whether lots of warnings and error messages should be generated
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>verbose</CODE> - the value to set - verbose&lt;level&gt; or noverbose.</DL>
</DD>
</DL>
<HR>

<A NAME="setVerbose(java.lang.String)"><!-- --></A><H3>
setVerbose</H3>
<PRE>
public void <B>setVerbose</B>(java.lang.String&nbsp;verbose)</PRE>
<DL>
<DD>Whether lots of warnings and error messages should be generated
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>verbose</CODE> - the value to set - verbose&lt;level&gt; or noverbose.</DL>
</DD>
</DL>
<HR>

<A NAME="setSuppressMethodArgumentNotUsed(boolean)"><!-- --></A><H3>
setSuppressMethodArgumentNotUsed</H3>
<PRE>
public void <B>setSuppressMethodArgumentNotUsed</B>(boolean&nbsp;suppressMethodArgumentNotUsed)</PRE>
<DL>
<DD>Whether the task should suppress the "Method argument is not used" in
 strictargs-Mode, which can not be suppressed by the compiler itself.
 The warning is logged as verbose message, though.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>suppressMethodArgumentNotUsed</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setSuppressPrivatePropertyNotUsed(boolean)"><!-- --></A><H3>
setSuppressPrivatePropertyNotUsed</H3>
<PRE>
public void <B>setSuppressPrivatePropertyNotUsed</B>(boolean&nbsp;suppressPrivatePropertyNotUsed)</PRE>
<DL>
<DD>Whether the task should suppress the "Private property is defined but
 not used" in strictargs-Mode, which can be quite annoying while
 developing. The warning is logged as verbose message, though.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>suppressPrivatePropertyNotUsed</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setSuppressVariableNotUsed(boolean)"><!-- --></A><H3>
setSuppressVariableNotUsed</H3>
<PRE>
public void <B>setSuppressVariableNotUsed</B>(boolean&nbsp;suppressVariableNotUsed)</PRE>
<DL>
<DD>Whether the task should suppress the "Variable is set but not used" in
 strictargs-Mode. Be careful with this one! The warning is logged as
 verbose message, though.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>suppressVariableNotUsed</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setSuppressExceptionNotSignalled(boolean)"><!-- --></A><H3>
setSuppressExceptionNotSignalled</H3>
<PRE>
public void <B>setSuppressExceptionNotSignalled</B>(boolean&nbsp;suppressExceptionNotSignalled)</PRE>
<DL>
<DD>Whether the task should suppress the "FooException is in SIGNALS list
 but is not signalled within the method", which is sometimes rather
 useless. The warning is logged as verbose message, though.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>suppressExceptionNotSignalled</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setSuppressDeprecation(boolean)"><!-- --></A><H3>
setSuppressDeprecation</H3>
<PRE>
public void <B>setSuppressDeprecation</B>(boolean&nbsp;suppressDeprecation)</PRE>
<DL>
<DD>Tells whether we should filter out any deprecation-messages
 of the compiler out.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>suppressDeprecation</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="init()"><!-- --></A><H3>
init</H3>
<PRE>
public void <B>init</B>()</PRE>
<DL>
<DD>init-Method sets defaults from Properties. That way, when ant is called
 with arguments like -Dant.netrexxc.verbose=verbose5 one can easily take
 control of all netrexxc-tasks.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/Task.html#init()">init</A></CODE> in class <CODE><A HREF="../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Executes the task - performs the actual compiler call.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/Native2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.TraceAttr.html" title="class in org.apache.tools.ant.taskdefs.optional"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/NetRexxC.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="NetRexxC.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.MatchingTask">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
