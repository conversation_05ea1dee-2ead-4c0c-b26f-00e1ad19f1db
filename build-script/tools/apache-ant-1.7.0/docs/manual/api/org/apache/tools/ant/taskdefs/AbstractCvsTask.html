<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:21 EST 2006 -->
<TITLE>
AbstractCvsTask (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.AbstractCvsTask class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="AbstractCvsTask (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV CLASS&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/AbstractCvsTask.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="AbstractCvsTask.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class AbstractCvsTask</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.AbstractCvsTask</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/cvslib/ChangeLogTask.html" title="class in org.apache.tools.ant.taskdefs.cvslib">ChangeLogTask</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Cvs.html" title="class in org.apache.tools.ant.taskdefs">Cvs</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/cvslib/CvsTagDiff.html" title="class in org.apache.tools.ant.taskdefs.cvslib">CvsTagDiff</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/cvslib/CvsVersion.html" title="class in org.apache.tools.ant.taskdefs.cvslib">CvsVersion</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public abstract class <B>AbstractCvsTask</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></DL>
</PRE>

<P>
original Cvs.java 1.20

  NOTE: This implementation has been moved here from Cvs.java with
  the addition of some accessors for extensibility.  Another task
  can extend this with some customized output processing.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#DEFAULT_COMPRESSION_LEVEL">DEFAULT_COMPRESSION_LEVEL</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Default compression level to use, if compression is enabled via
 setCompression( true ).</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#AbstractCvsTask()">AbstractCvsTask</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;empty no-arg constructor</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#addCommandArgument(org.apache.tools.ant.types.Commandline, java.lang.String)">addCommandArgument</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;c,
                   java.lang.String&nbsp;arg)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This method adds a command line argument to an external command.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#addCommandArgument(java.lang.String)">addCommandArgument</A></B>(java.lang.String&nbsp;arg)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This needs to be public to allow configuration
      of commands externally.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#addConfiguredCommandline(org.apache.tools.ant.types.Commandline)">addConfiguredCommandline</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;c)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds direct command-line to execute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#addConfiguredCommandline(org.apache.tools.ant.types.Commandline, boolean)">addConfiguredCommandline</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;c,
                         boolean&nbsp;insertAtStart)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Configures and adds the given Commandline.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#configureCommandline(org.apache.tools.ant.types.Commandline)">configureCommandline</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;c)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Configure a commandline element for things like cvsRoot, quiet, etc.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;do the work</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#getCommand()">getCommand</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;accessor to a command line as string

 This should be deprecated
 AntoineLL July 23d 2003</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#getCvsRoot()">getCvsRoot</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;access the CVSROOT variable</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#getCvsRsh()">getCvsRsh</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;access the CVS_RSH variable</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#getDest()">getDest</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;get the file where the checked out files should be placed</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.OutputStream</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#getErrorStream()">getErrorStream</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;access the stream to which the stderr from cvs should go
 if this stream has already been set, it will be returned
 if the stream has not yet been set, if the attribute error
 has been set, the output stream will go to the file denoted by the error attribute
 otherwise the stderr output will go to ant's logging system</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#getExecuteStreamHandler()">getExecuteStreamHandler</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;find the handler and instantiate it if it does not exist yet</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.OutputStream</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#getOutputStream()">getOutputStream</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;access the stream to which the stdout from cvs should go
 if this stream has already been set, it will be returned
 if the stream has not yet been set, if the attribute output
 has been set, the output stream will go to the output file
 otherwise the output will go to ant's logging system</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#getPackage()">getPackage</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;access the package or module to operate upon</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#getPassFile()">getPassFile</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;find the password file</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#getPort()">getPort</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;access the port of CVS</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#getTag()">getTag</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tag or branch</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#removeCommandline(org.apache.tools.ant.types.Commandline)">removeCommandline</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;c)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;remove a particular command from a vector of command lines</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#runCommand(org.apache.tools.ant.types.Commandline)">runCommand</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;toExecute)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets up the environment for toExecute and then runs it.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setAppend(boolean)">setAppend</A></B>(boolean&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether to append output/error when redirecting to a file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setCommand(java.lang.String)">setCommand</A></B>(java.lang.String&nbsp;c)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The CVS command to execute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setCompression(boolean)">setCompression</A></B>(boolean&nbsp;usecomp)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, this is the same as compressionlevel="3".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setCompressionLevel(int)">setCompressionLevel</A></B>(int&nbsp;level)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If set to a value 1-9 it adds -zN to the cvs command line, else
 it disables compression.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setCvsRoot(java.lang.String)">setCvsRoot</A></B>(java.lang.String&nbsp;root)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The CVSROOT variable.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setCvsRsh(java.lang.String)">setCvsRsh</A></B>(java.lang.String&nbsp;rsh)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The CVS_RSH variable.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setDate(java.lang.String)">setDate</A></B>(java.lang.String&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Use the most recent revision no later than the given date.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setDest(java.io.File)">setDest</A></B>(java.io.File&nbsp;dest)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The directory where the checked out files should be placed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setError(java.io.File)">setError</A></B>(java.io.File&nbsp;error)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The file to direct standard error from the command.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setErrorStream(java.io.OutputStream)">setErrorStream</A></B>(java.io.OutputStream&nbsp;errorStream)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sets a stream to which the stderr from the cvs exe should go</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setExecuteStreamHandler(org.apache.tools.ant.taskdefs.ExecuteStreamHandler)">setExecuteStreamHandler</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A>&nbsp;handler)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sets the handler</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setFailOnError(boolean)">setFailOnError</A></B>(boolean&nbsp;failOnError)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Stop the build process if the command exits with
 a return code other than 0.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setNoexec(boolean)">setNoexec</A></B>(boolean&nbsp;ne)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, report only and don't change any files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setOutput(java.io.File)">setOutput</A></B>(java.io.File&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The file to direct standard output from the command.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setOutputStream(java.io.OutputStream)">setOutputStream</A></B>(java.io.OutputStream&nbsp;outputStream)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sets a stream to which the output from the cvs executable should be sent</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setPackage(java.lang.String)">setPackage</A></B>(java.lang.String&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The package/module to operate upon.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setPassfile(java.io.File)">setPassfile</A></B>(java.io.File&nbsp;passFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Password file to read passwords from.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setPort(int)">setPort</A></B>(int&nbsp;port)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Port used by CVS to communicate with the server.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setQuiet(boolean)">setQuiet</A></B>(boolean&nbsp;q)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, suppress informational messages.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setReallyquiet(boolean)">setReallyquiet</A></B>(boolean&nbsp;q)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, suppress all messages.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html#setTag(java.lang.String)">setTag</A></B>(java.lang.String&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The tag of the package/module to operate upon.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="DEFAULT_COMPRESSION_LEVEL"><!-- --></A><H3>
DEFAULT_COMPRESSION_LEVEL</H3>
<PRE>
public static final int <B>DEFAULT_COMPRESSION_LEVEL</B></PRE>
<DL>
<DD>Default compression level to use, if compression is enabled via
 setCompression( true ).
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.AbstractCvsTask.DEFAULT_COMPRESSION_LEVEL">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="AbstractCvsTask()"><!-- --></A><H3>
AbstractCvsTask</H3>
<PRE>
public <B>AbstractCvsTask</B>()</PRE>
<DL>
<DD>empty no-arg constructor
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setExecuteStreamHandler(org.apache.tools.ant.taskdefs.ExecuteStreamHandler)"><!-- --></A><H3>
setExecuteStreamHandler</H3>
<PRE>
public void <B>setExecuteStreamHandler</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A>&nbsp;handler)</PRE>
<DL>
<DD>sets the handler
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>handler</CODE> - a handler able of processing the output and error streams from the cvs exe</DL>
</DD>
</DL>
<HR>

<A NAME="getExecuteStreamHandler()"><!-- --></A><H3>
getExecuteStreamHandler</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A> <B>getExecuteStreamHandler</B>()</PRE>
<DL>
<DD>find the handler and instantiate it if it does not exist yet
<P>
<DD><DL>

<DT><B>Returns:</B><DD>handler for output and error streams</DL>
</DD>
</DL>
<HR>

<A NAME="setOutputStream(java.io.OutputStream)"><!-- --></A><H3>
setOutputStream</H3>
<PRE>
protected void <B>setOutputStream</B>(java.io.OutputStream&nbsp;outputStream)</PRE>
<DL>
<DD>sets a stream to which the output from the cvs executable should be sent
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>outputStream</CODE> - stream to which the stdout from cvs should go</DL>
</DD>
</DL>
<HR>

<A NAME="getOutputStream()"><!-- --></A><H3>
getOutputStream</H3>
<PRE>
protected java.io.OutputStream <B>getOutputStream</B>()</PRE>
<DL>
<DD>access the stream to which the stdout from cvs should go
 if this stream has already been set, it will be returned
 if the stream has not yet been set, if the attribute output
 has been set, the output stream will go to the output file
 otherwise the output will go to ant's logging system
<P>
<DD><DL>

<DT><B>Returns:</B><DD>output stream to which cvs' stdout should go to</DL>
</DD>
</DL>
<HR>

<A NAME="setErrorStream(java.io.OutputStream)"><!-- --></A><H3>
setErrorStream</H3>
<PRE>
protected void <B>setErrorStream</B>(java.io.OutputStream&nbsp;errorStream)</PRE>
<DL>
<DD>sets a stream to which the stderr from the cvs exe should go
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>errorStream</CODE> - an output stream willing to process stderr</DL>
</DD>
</DL>
<HR>

<A NAME="getErrorStream()"><!-- --></A><H3>
getErrorStream</H3>
<PRE>
protected java.io.OutputStream <B>getErrorStream</B>()</PRE>
<DL>
<DD>access the stream to which the stderr from cvs should go
 if this stream has already been set, it will be returned
 if the stream has not yet been set, if the attribute error
 has been set, the output stream will go to the file denoted by the error attribute
 otherwise the stderr output will go to ant's logging system
<P>
<DD><DL>

<DT><B>Returns:</B><DD>output stream to which cvs' stderr should go to</DL>
</DD>
</DL>
<HR>

<A NAME="runCommand(org.apache.tools.ant.types.Commandline)"><!-- --></A><H3>
runCommand</H3>
<PRE>
protected void <B>runCommand</B>(<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;toExecute)
                   throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Sets up the environment for toExecute and then runs it.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>toExecute</CODE> - the command line to execute
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if failonError is set to true and the cvs command fails</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>do the work
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if failonerror is set to true and the
 cvs command fails.</DL>
</DD>
</DL>
<HR>

<A NAME="setCvsRoot(java.lang.String)"><!-- --></A><H3>
setCvsRoot</H3>
<PRE>
public void <B>setCvsRoot</B>(java.lang.String&nbsp;root)</PRE>
<DL>
<DD>The CVSROOT variable.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>root</CODE> - the CVSROOT variable</DL>
</DD>
</DL>
<HR>

<A NAME="getCvsRoot()"><!-- --></A><H3>
getCvsRoot</H3>
<PRE>
public java.lang.String <B>getCvsRoot</B>()</PRE>
<DL>
<DD>access the CVSROOT variable
<P>
<DD><DL>

<DT><B>Returns:</B><DD>CVSROOT</DL>
</DD>
</DL>
<HR>

<A NAME="setCvsRsh(java.lang.String)"><!-- --></A><H3>
setCvsRsh</H3>
<PRE>
public void <B>setCvsRsh</B>(java.lang.String&nbsp;rsh)</PRE>
<DL>
<DD>The CVS_RSH variable.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rsh</CODE> - the CVS_RSH variable</DL>
</DD>
</DL>
<HR>

<A NAME="getCvsRsh()"><!-- --></A><H3>
getCvsRsh</H3>
<PRE>
public java.lang.String <B>getCvsRsh</B>()</PRE>
<DL>
<DD>access the CVS_RSH variable
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the CVS_RSH variable</DL>
</DD>
</DL>
<HR>

<A NAME="setPort(int)"><!-- --></A><H3>
setPort</H3>
<PRE>
public void <B>setPort</B>(int&nbsp;port)</PRE>
<DL>
<DD>Port used by CVS to communicate with the server.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>port</CODE> - port of CVS</DL>
</DD>
</DL>
<HR>

<A NAME="getPort()"><!-- --></A><H3>
getPort</H3>
<PRE>
public int <B>getPort</B>()</PRE>
<DL>
<DD>access the port of CVS
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the port of CVS</DL>
</DD>
</DL>
<HR>

<A NAME="setPassfile(java.io.File)"><!-- --></A><H3>
setPassfile</H3>
<PRE>
public void <B>setPassfile</B>(java.io.File&nbsp;passFile)</PRE>
<DL>
<DD>Password file to read passwords from.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>passFile</CODE> - password file to read passwords from</DL>
</DD>
</DL>
<HR>

<A NAME="getPassFile()"><!-- --></A><H3>
getPassFile</H3>
<PRE>
public java.io.File <B>getPassFile</B>()</PRE>
<DL>
<DD>find the password file
<P>
<DD><DL>

<DT><B>Returns:</B><DD>password file</DL>
</DD>
</DL>
<HR>

<A NAME="setDest(java.io.File)"><!-- --></A><H3>
setDest</H3>
<PRE>
public void <B>setDest</B>(java.io.File&nbsp;dest)</PRE>
<DL>
<DD>The directory where the checked out files should be placed.

 <p>Note that this is different from CVS's -d command line
 switch as Ant will never shorten pathnames to avoid empty
 directories.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>dest</CODE> - directory where the checked out files should be placed</DL>
</DD>
</DL>
<HR>

<A NAME="getDest()"><!-- --></A><H3>
getDest</H3>
<PRE>
public java.io.File <B>getDest</B>()</PRE>
<DL>
<DD>get the file where the checked out files should be placed
<P>
<DD><DL>

<DT><B>Returns:</B><DD>directory where the checked out files should be placed</DL>
</DD>
</DL>
<HR>

<A NAME="setPackage(java.lang.String)"><!-- --></A><H3>
setPackage</H3>
<PRE>
public void <B>setPackage</B>(java.lang.String&nbsp;p)</PRE>
<DL>
<DD>The package/module to operate upon.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - package or module to operate upon</DL>
</DD>
</DL>
<HR>

<A NAME="getPackage()"><!-- --></A><H3>
getPackage</H3>
<PRE>
public java.lang.String <B>getPackage</B>()</PRE>
<DL>
<DD>access the package or module to operate upon
<P>
<DD><DL>

<DT><B>Returns:</B><DD>package/module</DL>
</DD>
</DL>
<HR>

<A NAME="getTag()"><!-- --></A><H3>
getTag</H3>
<PRE>
public java.lang.String <B>getTag</B>()</PRE>
<DL>
<DD>tag or branch
<P>
<DD><DL>

<DT><B>Returns:</B><DD>tag or branch<DT><B>Since:</B></DT>
  <DD>ant 1.6.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setTag(java.lang.String)"><!-- --></A><H3>
setTag</H3>
<PRE>
public void <B>setTag</B>(java.lang.String&nbsp;p)</PRE>
<DL>
<DD>The tag of the package/module to operate upon.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - tag</DL>
</DD>
</DL>
<HR>

<A NAME="addCommandArgument(java.lang.String)"><!-- --></A><H3>
addCommandArgument</H3>
<PRE>
public void <B>addCommandArgument</B>(java.lang.String&nbsp;arg)</PRE>
<DL>
<DD>This needs to be public to allow configuration
      of commands externally.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>arg</CODE> - command argument</DL>
</DD>
</DL>
<HR>

<A NAME="addCommandArgument(org.apache.tools.ant.types.Commandline, java.lang.String)"><!-- --></A><H3>
addCommandArgument</H3>
<PRE>
public void <B>addCommandArgument</B>(<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;c,
                               java.lang.String&nbsp;arg)</PRE>
<DL>
<DD>This method adds a command line argument to an external command.

 I do not understand what this method does in this class ???
 particularly not why it is public ????
 AntoineLL July 23d 2003
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>c</CODE> - command line to which one argument should be added<DD><CODE>arg</CODE> - argument to add</DL>
</DD>
</DL>
<HR>

<A NAME="setDate(java.lang.String)"><!-- --></A><H3>
setDate</H3>
<PRE>
public void <B>setDate</B>(java.lang.String&nbsp;p)</PRE>
<DL>
<DD>Use the most recent revision no later than the given date.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - a date as string in a format that the CVS executable
 can understand see man cvs</DL>
</DD>
</DL>
<HR>

<A NAME="setCommand(java.lang.String)"><!-- --></A><H3>
setCommand</H3>
<PRE>
public void <B>setCommand</B>(java.lang.String&nbsp;c)</PRE>
<DL>
<DD>The CVS command to execute.

 This should be deprecated, it is better to use the Commandline class ?
 AntoineLL July 23d 2003
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>c</CODE> - a command as string</DL>
</DD>
</DL>
<HR>

<A NAME="getCommand()"><!-- --></A><H3>
getCommand</H3>
<PRE>
public java.lang.String <B>getCommand</B>()</PRE>
<DL>
<DD>accessor to a command line as string

 This should be deprecated
 AntoineLL July 23d 2003
<P>
<DD><DL>

<DT><B>Returns:</B><DD>command line as string</DL>
</DD>
</DL>
<HR>

<A NAME="setQuiet(boolean)"><!-- --></A><H3>
setQuiet</H3>
<PRE>
public void <B>setQuiet</B>(boolean&nbsp;q)</PRE>
<DL>
<DD>If true, suppress informational messages.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>q</CODE> - if true, suppress informational messages</DL>
</DD>
</DL>
<HR>

<A NAME="setReallyquiet(boolean)"><!-- --></A><H3>
setReallyquiet</H3>
<PRE>
public void <B>setReallyquiet</B>(boolean&nbsp;q)</PRE>
<DL>
<DD>If true, suppress all messages.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>q</CODE> - if true, suppress all messages<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setNoexec(boolean)"><!-- --></A><H3>
setNoexec</H3>
<PRE>
public void <B>setNoexec</B>(boolean&nbsp;ne)</PRE>
<DL>
<DD>If true, report only and don't change any files.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ne</CODE> - if true, report only and do not change any files.</DL>
</DD>
</DL>
<HR>

<A NAME="setOutput(java.io.File)"><!-- --></A><H3>
setOutput</H3>
<PRE>
public void <B>setOutput</B>(java.io.File&nbsp;output)</PRE>
<DL>
<DD>The file to direct standard output from the command.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - a file to which stdout should go</DL>
</DD>
</DL>
<HR>

<A NAME="setError(java.io.File)"><!-- --></A><H3>
setError</H3>
<PRE>
public void <B>setError</B>(java.io.File&nbsp;error)</PRE>
<DL>
<DD>The file to direct standard error from the command.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>error</CODE> - a file to which stderr should go</DL>
</DD>
</DL>
<HR>

<A NAME="setAppend(boolean)"><!-- --></A><H3>
setAppend</H3>
<PRE>
public void <B>setAppend</B>(boolean&nbsp;value)</PRE>
<DL>
<DD>Whether to append output/error when redirecting to a file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - true indicated you want to append</DL>
</DD>
</DL>
<HR>

<A NAME="setFailOnError(boolean)"><!-- --></A><H3>
setFailOnError</H3>
<PRE>
public void <B>setFailOnError</B>(boolean&nbsp;failOnError)</PRE>
<DL>
<DD>Stop the build process if the command exits with
 a return code other than 0.
 Defaults to false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>failOnError</CODE> - stop the build process if the command exits with
 a return code other than 0</DL>
</DD>
</DL>
<HR>

<A NAME="configureCommandline(org.apache.tools.ant.types.Commandline)"><!-- --></A><H3>
configureCommandline</H3>
<PRE>
protected void <B>configureCommandline</B>(<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;c)</PRE>
<DL>
<DD>Configure a commandline element for things like cvsRoot, quiet, etc.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>c</CODE> - the command line which will be configured
 if the commandline is initially null, the function is a noop
 otherwise the function append to the commandline arguments concerning
 <ul>
 <li>
 cvs package
 </li>
 <li>
 compression
 </li>
 <li>
 quiet or reallyquiet
 </li>
 <li>cvsroot</li>
 <li>noexec</li>
 </ul></DL>
</DD>
</DL>
<HR>

<A NAME="removeCommandline(org.apache.tools.ant.types.Commandline)"><!-- --></A><H3>
removeCommandline</H3>
<PRE>
protected void <B>removeCommandline</B>(<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;c)</PRE>
<DL>
<DD>remove a particular command from a vector of command lines
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>c</CODE> - command line which should be removed</DL>
</DD>
</DL>
<HR>

<A NAME="addConfiguredCommandline(org.apache.tools.ant.types.Commandline)"><!-- --></A><H3>
addConfiguredCommandline</H3>
<PRE>
public void <B>addConfiguredCommandline</B>(<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;c)</PRE>
<DL>
<DD>Adds direct command-line to execute.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>c</CODE> - command line to execute</DL>
</DD>
</DL>
<HR>

<A NAME="addConfiguredCommandline(org.apache.tools.ant.types.Commandline, boolean)"><!-- --></A><H3>
addConfiguredCommandline</H3>
<PRE>
public void <B>addConfiguredCommandline</B>(<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;c,
                                     boolean&nbsp;insertAtStart)</PRE>
<DL>
<DD>Configures and adds the given Commandline.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>c</CODE> - commandline to insert<DD><CODE>insertAtStart</CODE> - If true, c is
 inserted at the beginning of the vector of command lines</DL>
</DD>
</DL>
<HR>

<A NAME="setCompressionLevel(int)"><!-- --></A><H3>
setCompressionLevel</H3>
<PRE>
public void <B>setCompressionLevel</B>(int&nbsp;level)</PRE>
<DL>
<DD>If set to a value 1-9 it adds -zN to the cvs command line, else
 it disables compression.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>level</CODE> - compression level 1 to 9</DL>
</DD>
</DL>
<HR>

<A NAME="setCompression(boolean)"><!-- --></A><H3>
setCompression</H3>
<PRE>
public void <B>setCompression</B>(boolean&nbsp;usecomp)</PRE>
<DL>
<DD>If true, this is the same as compressionlevel="3".
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>usecomp</CODE> - If true, turns on compression using default
 level, AbstractCvsTask.DEFAULT_COMPRESSION_LEVEL.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV CLASS&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/AbstractCvsTask.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="AbstractCvsTask.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
