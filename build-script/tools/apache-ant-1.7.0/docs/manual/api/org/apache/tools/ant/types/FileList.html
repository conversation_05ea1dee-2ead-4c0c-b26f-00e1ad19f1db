<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:31 EST 2006 -->
<TITLE>
FileList (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.types.FileList class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="FileList (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/FileList.FileName.html" title="class in org.apache.tools.ant.types"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/types/FileList.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="FileList.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.types.DataType">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.types</FONT>
<BR>
Class FileList</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.types.FileList</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable, <A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>FileList</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A><DT>implements <A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A></DL>
</PRE>

<P>
FileList represents an explicitly named list of files.  FileLists
 are useful when you want to capture a list of files regardless of
 whether they currently exist.  By contrast, FileSet operates as a
 filter, only returning the name of a matched file if it currently
 exists in the file system.
<P>

<P>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FileList.FileName.html" title="class in org.apache.tools.ant.types">FileList.FileName</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Inner class corresponding to the &lt;file&gt; nested element.</TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.types.DataType"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/DataType.html#checked">checked</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#ref">ref</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FileList.html#FileList()">FileList</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The default constructor.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected </CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FileList.html#FileList(org.apache.tools.ant.types.FileList)">FileList</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/FileList.html" title="class in org.apache.tools.ant.types">FileList</A>&nbsp;filelist)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A copy constructor.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FileList.html#addConfiguredFile(org.apache.tools.ant.types.FileList.FileName)">addConfiguredFile</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/FileList.FileName.html" title="class in org.apache.tools.ant.types">FileList.FileName</A>&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a nested &lt;file&gt; nested element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FileList.html#getDir(org.apache.tools.ant.Project)">getDir</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FileList.html#getFiles(org.apache.tools.ant.Project)">getFiles</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the list of files represented by this FileList.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/types/FileList.html" title="class in org.apache.tools.ant.types">FileList</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FileList.html#getRef(org.apache.tools.ant.Project)">getRef</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Performs the check for circular references and returns the
 referenced FileList.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FileList.html#isFilesystemOnly()">isFilesystemOnly</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Always returns true.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Iterator</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FileList.html#iterator()">iterator</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Fulfill the ResourceCollection contract.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FileList.html#setDir(java.io.File)">setDir</A></B>(java.io.File&nbsp;dir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the dir attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FileList.html#setFiles(java.lang.String)">setFiles</A></B>(java.lang.String&nbsp;filenames)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the filenames attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FileList.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Makes this instance in effect a reference to another FileList
 instance.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FileList.html#size()">size</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Fulfill the ResourceCollection contract.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.types.DataType"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/DataType.html#checkAttributesAllowed()">checkAttributesAllowed</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#checkChildrenAllowed()">checkChildrenAllowed</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#circularReference()">circularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference()">dieOnCircularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference(java.util.Stack, org.apache.tools.ant.Project)">dieOnCircularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef()">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(java.lang.Class, java.lang.String)">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(java.lang.Class, java.lang.String, org.apache.tools.ant.Project)">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getDataTypeName()">getDataTypeName</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getRefid()">getRefid</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType, java.util.Stack, org.apache.tools.ant.Project)">invokeCircularReferenceCheck</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#isChecked()">isChecked</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#isReference()">isReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#noChildrenAllowed()">noChildrenAllowed</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#setChecked(boolean)">setChecked</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#tooManyAttributes()">tooManyAttributes</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#toString()">toString</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="FileList()"><!-- --></A><H3>
FileList</H3>
<PRE>
public <B>FileList</B>()</PRE>
<DL>
<DD>The default constructor.
<P>
</DL>
<HR>

<A NAME="FileList(org.apache.tools.ant.types.FileList)"><!-- --></A><H3>
FileList</H3>
<PRE>
protected <B>FileList</B>(<A HREF="../../../../../org/apache/tools/ant/types/FileList.html" title="class in org.apache.tools.ant.types">FileList</A>&nbsp;filelist)</PRE>
<DL>
<DD>A copy constructor.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>filelist</CODE> - a <code>FileList</code> value</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setRefid(org.apache.tools.ant.types.Reference)"><!-- --></A><H3>
setRefid</H3>
<PRE>
public void <B>setRefid</B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)
              throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Makes this instance in effect a reference to another FileList
 instance.

 <p>You must not set another attribute or nest elements inside
 this element if you make it a reference.</p>
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/DataType.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>r</CODE> - the reference to another filelist.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if an error occurs.</DL>
</DD>
</DL>
<HR>

<A NAME="setDir(java.io.File)"><!-- --></A><H3>
setDir</H3>
<PRE>
public void <B>setDir</B>(java.io.File&nbsp;dir)
            throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Set the dir attribute.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>dir</CODE> - the directory this filelist is relative to.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if an error occurs</DL>
</DD>
</DL>
<HR>

<A NAME="getDir(org.apache.tools.ant.Project)"><!-- --></A><H3>
getDir</H3>
<PRE>
public java.io.File <B>getDir</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</PRE>
<DL>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the current project
<DT><B>Returns:</B><DD>the directory attribute</DL>
</DD>
</DL>
<HR>

<A NAME="setFiles(java.lang.String)"><!-- --></A><H3>
setFiles</H3>
<PRE>
public void <B>setFiles</B>(java.lang.String&nbsp;filenames)</PRE>
<DL>
<DD>Set the filenames attribute.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filenames</CODE> - a string contains filenames, separated by , or
        by whitespace.</DL>
</DD>
</DL>
<HR>

<A NAME="getFiles(org.apache.tools.ant.Project)"><!-- --></A><H3>
getFiles</H3>
<PRE>
public java.lang.String[] <B>getFiles</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</PRE>
<DL>
<DD>Returns the list of files represented by this FileList.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the current project
<DT><B>Returns:</B><DD>the list of files represented by this FileList.</DL>
</DD>
</DL>
<HR>

<A NAME="getRef(org.apache.tools.ant.Project)"><!-- --></A><H3>
getRef</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/types/FileList.html" title="class in org.apache.tools.ant.types">FileList</A> <B>getRef</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</PRE>
<DL>
<DD>Performs the check for circular references and returns the
 referenced FileList.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the current project
<DT><B>Returns:</B><DD>the FileList represented by a referenced filelist.</DL>
</DD>
</DL>
<HR>

<A NAME="addConfiguredFile(org.apache.tools.ant.types.FileList.FileName)"><!-- --></A><H3>
addConfiguredFile</H3>
<PRE>
public void <B>addConfiguredFile</B>(<A HREF="../../../../../org/apache/tools/ant/types/FileList.FileName.html" title="class in org.apache.tools.ant.types">FileList.FileName</A>&nbsp;name)</PRE>
<DL>
<DD>Add a nested &lt;file&gt; nested element.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - a configured file element with a name.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="iterator()"><!-- --></A><H3>
iterator</H3>
<PRE>
public java.util.Iterator <B>iterator</B>()</PRE>
<DL>
<DD>Fulfill the ResourceCollection contract.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html#iterator()">iterator</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>an Iterator of Resources.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="size()"><!-- --></A><H3>
size</H3>
<PRE>
public int <B>size</B>()</PRE>
<DL>
<DD>Fulfill the ResourceCollection contract.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html#size()">size</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>number of elements as int.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="isFilesystemOnly()"><!-- --></A><H3>
isFilesystemOnly</H3>
<PRE>
public boolean <B>isFilesystemOnly</B>()</PRE>
<DL>
<DD>Always returns true.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html#isFilesystemOnly()">isFilesystemOnly</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>true indicating that all elements will be FileResources.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/FileList.FileName.html" title="class in org.apache.tools.ant.types"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/types/FileList.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="FileList.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.types.DataType">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
