<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:25 EST 2006 -->
<TITLE>
Zip (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.Zip class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Zip (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Param.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Zip.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Zip.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class Zip</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.MatchingTask</A>
              <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.Zip</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable, <A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/Jar.html" title="class in org.apache.tools.ant.taskdefs">Jar</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>Zip</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></DL>
</PRE>

<P>
Create a Zip file.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.1</DD>
</DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Holds the up-to-date status and the out-of-date resources of
 the original archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.Duplicate.html" title="class in org.apache.tools.ant.taskdefs">Zip.Duplicate</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Possible behaviors when a duplicate file is added:
 "add", "preserve" or "fail"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.WhenEmpty.html" title="class in org.apache.tools.ant.taskdefs">Zip.WhenEmpty</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Possible behaviors when there are no matching files for the task:
 "fail", "skip", or "create".</TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#addedDirs">addedDirs</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#archiveType">archiveType</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#doubleFilePass">doubleFilePass</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#duplicate">duplicate</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#emptyBehavior">emptyBehavior</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#entries">entries</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#skipWriting">skipWriting</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#zipFile">zipFile</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.MatchingTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#fileset">fileset</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#Zip()">Zip</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#add(org.apache.tools.ant.types.ResourceCollection)">add</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;a)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a collection of resources to be archived.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;set)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a set of files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#addParentDirs(java.io.File, java.lang.String, org.apache.tools.zip.ZipOutputStream, java.lang.String, int)">addParentDirs</A></B>(java.io.File&nbsp;baseDir,
              java.lang.String&nbsp;entry,
              <A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut,
              java.lang.String&nbsp;prefix,
              int&nbsp;dirMode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Ensure all parent dirs of a given entry have been added.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#addResources(org.apache.tools.ant.types.FileSet, org.apache.tools.ant.types.Resource[], org.apache.tools.zip.ZipOutputStream)">addResources</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;fileset,
             <A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>[]&nbsp;resources,
             <A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add the given resources.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#addResources(org.apache.tools.ant.types.ResourceCollection, org.apache.tools.ant.types.Resource[], org.apache.tools.zip.ZipOutputStream)">addResources</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;rc,
             <A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>[]&nbsp;resources,
             <A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add the given resources.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#addZipfileset(org.apache.tools.ant.types.ZipFileSet)">addZipfileset</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/ZipFileSet.html" title="class in org.apache.tools.ant.types">ZipFileSet</A>&nbsp;set)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a set of files that can be
 read from an archive and be given a prefix/fullpath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#addZipGroupFileset(org.apache.tools.ant.types.FileSet)">addZipGroupFileset</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;set)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a group of zip files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#cleanUp()">cleanUp</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Do any clean up necessary to allow this instance to be used again.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#createEmptyZip(java.io.File)">createEmptyZip</A></B>(java.io.File&nbsp;zipFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create an empty zip file</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;validate and build</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#executeMain()">executeMain</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Build the zip file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#finalizeZipOutputStream(org.apache.tools.zip.ZipOutputStream)">finalizeZipOutputStream</A></B>(<A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;method for subclasses to override</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#getComment()">getComment</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Comment of the archive</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#getDestFile()">getDestFile</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The file to create.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#getEncoding()">getEncoding</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Encoding to use for filenames.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#getLevel()">getLevel</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the compression level.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#getNonFileSetResourcesToAdd(org.apache.tools.ant.types.ResourceCollection[], java.io.File, boolean)">getNonFileSetResourcesToAdd</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>[]&nbsp;rcs,
                            java.io.File&nbsp;zipFile,
                            boolean&nbsp;needsUpdate)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Collect the resources that are newer than the corresponding
 entries (or missing) in the original archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#getResourcesToAdd(org.apache.tools.ant.types.FileSet[], java.io.File, boolean)">getResourcesToAdd</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>[]&nbsp;filesets,
                  java.io.File&nbsp;zipFile,
                  boolean&nbsp;needsUpdate)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Collect the resources that are newer than the corresponding
 entries (or missing) in the original archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#getResourcesToAdd(org.apache.tools.ant.types.ResourceCollection[], java.io.File, boolean)">getResourcesToAdd</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>[]&nbsp;rcs,
                  java.io.File&nbsp;zipFile,
                  boolean&nbsp;needsUpdate)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Collect the resources that are newer than the corresponding
 entries (or missing) in the original archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>[][]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#grabNonFileSetResources(org.apache.tools.ant.types.ResourceCollection[])">grabNonFileSetResources</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>[]&nbsp;rcs)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Fetch all included and not excluded resources from the collections.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>[][]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#grabResources(org.apache.tools.ant.types.FileSet[])">grabResources</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>[]&nbsp;filesets)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Fetch all included and not excluded resources from the sets.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#initZipOutputStream(org.apache.tools.zip.ZipOutputStream)">initZipOutputStream</A></B>(<A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;method for subclasses to override</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#isAddingNewFiles()">isAddingNewFiles</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicates if the task is adding new files into the archive as opposed to
 copying back unchanged files from the backup copy</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#isCompress()">isCompress</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether we want to compress the files or only store them;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#isEmpty(org.apache.tools.ant.types.Resource[][])">isEmpty</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>[][]&nbsp;r)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Check is the resource arrays are empty.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#isInUpdateMode()">isInUpdateMode</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Are we updating an existing archive?</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#reset()">reset</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Makes this instance reset all attributes to their default
 values and forget all children.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#selectFileResources(org.apache.tools.ant.types.Resource[])">selectFileResources</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>[]&nbsp;orig)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Drops all non-file resources from the given array.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#setBasedir(java.io.File)">setBasedir</A></B>(java.io.File&nbsp;baseDir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Directory from which to archive files; optional.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#setComment(java.lang.String)">setComment</A></B>(java.lang.String&nbsp;comment)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Comment to use for archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#setCompress(boolean)">setCompress</A></B>(boolean&nbsp;c)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether we want to compress the files or only store them;
 optional, default=true;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#setDestFile(java.io.File)">setDestFile</A></B>(java.io.File&nbsp;destFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The file to create; required.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#setDuplicate(org.apache.tools.ant.taskdefs.Zip.Duplicate)">setDuplicate</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.Duplicate.html" title="class in org.apache.tools.ant.taskdefs">Zip.Duplicate</A>&nbsp;df)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets behavior for when a duplicate file is about to be added -
 one of <code>add</code>, <code>preserve</code> or <code>fail</code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#setEncoding(java.lang.String)">setEncoding</A></B>(java.lang.String&nbsp;encoding)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Encoding to use for filenames, defaults to the platform's
 default encoding.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#setFile(java.io.File)">setFile</A></B>(java.io.File&nbsp;file)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use setDestFile(File) instead.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#setFilesonly(boolean)">setFilesonly</A></B>(boolean&nbsp;f)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, emulate Sun's jar utility by not adding parent directories;
 optional, defaults to false.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#setKeepCompression(boolean)">setKeepCompression</A></B>(boolean&nbsp;keep)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether the original compression of entries coming from a ZIP
 archive should be kept (for example when updating an archive).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#setLevel(int)">setLevel</A></B>(int&nbsp;level)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the compression level to use.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#setRoundUp(boolean)">setRoundUp</A></B>(boolean&nbsp;r)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether the file modification times will be rounded up to the
 next even number of seconds.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#setUpdate(boolean)">setUpdate</A></B>(boolean&nbsp;c)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, updates an existing file, otherwise overwrite
 any existing one; optional defaults to false.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#setWhenempty(org.apache.tools.ant.taskdefs.Zip.WhenEmpty)">setWhenempty</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.WhenEmpty.html" title="class in org.apache.tools.ant.taskdefs">Zip.WhenEmpty</A>&nbsp;we)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets behavior of the task when no files match.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#setZipfile(java.io.File)">setZipfile</A></B>(java.io.File&nbsp;zipFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use setDestFile(File) instead.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#zipDir(java.io.File, org.apache.tools.zip.ZipOutputStream, java.lang.String, int)">zipDir</A></B>(java.io.File&nbsp;dir,
       <A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut,
       java.lang.String&nbsp;vPath,
       int&nbsp;mode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a directory to the zip stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#zipDir(java.io.File, org.apache.tools.zip.ZipOutputStream, java.lang.String, int, org.apache.tools.zip.ZipExtraField[])">zipDir</A></B>(java.io.File&nbsp;dir,
       <A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut,
       java.lang.String&nbsp;vPath,
       int&nbsp;mode,
       <A HREF="../../../../../org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</A>[]&nbsp;extra)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a directory to the zip stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#zipFile(java.io.File, org.apache.tools.zip.ZipOutputStream, java.lang.String, int)">zipFile</A></B>(java.io.File&nbsp;file,
        <A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut,
        java.lang.String&nbsp;vPath,
        int&nbsp;mode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Method that gets called when adding from <code>java.io.File</code> instances.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#zipFile(java.io.InputStream, org.apache.tools.zip.ZipOutputStream, java.lang.String, long, java.io.File, int)">zipFile</A></B>(java.io.InputStream&nbsp;in,
        <A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut,
        java.lang.String&nbsp;vPath,
        long&nbsp;lastModified,
        java.io.File&nbsp;fromArchive,
        int&nbsp;mode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a new entry to the archive, takes care of duplicates as well.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.MatchingTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createExclude()">createExclude</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createExcludesFile()">createExcludesFile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createInclude()">createInclude</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createIncludesFile()">createIncludesFile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createPatternSet()">createPatternSet</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#hasSelectors()">hasSelectors</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#selectorCount()">selectorCount</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#selectorElements()">selectorElements</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setExcludes(java.lang.String)">setExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setIncludes(java.lang.String)">setIncludes</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#XsetItems(java.lang.String)">XsetItems</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="zipFile"><!-- --></A><H3>
zipFile</H3>
<PRE>
protected java.io.File <B>zipFile</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="entries"><!-- --></A><H3>
entries</H3>
<PRE>
protected java.util.Hashtable <B>entries</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="duplicate"><!-- --></A><H3>
duplicate</H3>
<PRE>
protected java.lang.String <B>duplicate</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="archiveType"><!-- --></A><H3>
archiveType</H3>
<PRE>
protected java.lang.String <B>archiveType</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="emptyBehavior"><!-- --></A><H3>
emptyBehavior</H3>
<PRE>
protected java.lang.String <B>emptyBehavior</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="addedDirs"><!-- --></A><H3>
addedDirs</H3>
<PRE>
protected java.util.Hashtable <B>addedDirs</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="doubleFilePass"><!-- --></A><H3>
doubleFilePass</H3>
<PRE>
protected boolean <B>doubleFilePass</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="skipWriting"><!-- --></A><H3>
skipWriting</H3>
<PRE>
protected boolean <B>skipWriting</B></PRE>
<DL>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Zip()"><!-- --></A><H3>
Zip</H3>
<PRE>
public <B>Zip</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setZipfile(java.io.File)"><!-- --></A><H3>
setZipfile</H3>
<PRE>
public void <B>setZipfile</B>(java.io.File&nbsp;zipFile)</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use setDestFile(File) instead.</I>
<P>
<DD>This is the name/location of where to
 create the .zip file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>zipFile</CODE> - the path of the zipFile</DL>
</DD>
</DL>
<HR>

<A NAME="setFile(java.io.File)"><!-- --></A><H3>
setFile</H3>
<PRE>
public void <B>setFile</B>(java.io.File&nbsp;file)</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use setDestFile(File) instead.</I>
<P>
<DD>This is the name/location of where to
 create the file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>file</CODE> - the path of the zipFile<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setDestFile(java.io.File)"><!-- --></A><H3>
setDestFile</H3>
<PRE>
public void <B>setDestFile</B>(java.io.File&nbsp;destFile)</PRE>
<DL>
<DD>The file to create; required.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>destFile</CODE> - The new destination File<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getDestFile()"><!-- --></A><H3>
getDestFile</H3>
<PRE>
public java.io.File <B>getDestFile</B>()</PRE>
<DL>
<DD>The file to create.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the destination file<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setBasedir(java.io.File)"><!-- --></A><H3>
setBasedir</H3>
<PRE>
public void <B>setBasedir</B>(java.io.File&nbsp;baseDir)</PRE>
<DL>
<DD>Directory from which to archive files; optional.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>baseDir</CODE> - the base directory</DL>
</DD>
</DL>
<HR>

<A NAME="setCompress(boolean)"><!-- --></A><H3>
setCompress</H3>
<PRE>
public void <B>setCompress</B>(boolean&nbsp;c)</PRE>
<DL>
<DD>Whether we want to compress the files or only store them;
 optional, default=true;
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>c</CODE> - if true, compress the files</DL>
</DD>
</DL>
<HR>

<A NAME="isCompress()"><!-- --></A><H3>
isCompress</H3>
<PRE>
public boolean <B>isCompress</B>()</PRE>
<DL>
<DD>Whether we want to compress the files or only store them;
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true if the files are to be compressed<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setFilesonly(boolean)"><!-- --></A><H3>
setFilesonly</H3>
<PRE>
public void <B>setFilesonly</B>(boolean&nbsp;f)</PRE>
<DL>
<DD>If true, emulate Sun's jar utility by not adding parent directories;
 optional, defaults to false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>f</CODE> - if true, emulate sun's jar by not adding parent directories</DL>
</DD>
</DL>
<HR>

<A NAME="setUpdate(boolean)"><!-- --></A><H3>
setUpdate</H3>
<PRE>
public void <B>setUpdate</B>(boolean&nbsp;c)</PRE>
<DL>
<DD>If true, updates an existing file, otherwise overwrite
 any existing one; optional defaults to false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>c</CODE> - if true, updates an existing zip file</DL>
</DD>
</DL>
<HR>

<A NAME="isInUpdateMode()"><!-- --></A><H3>
isInUpdateMode</H3>
<PRE>
public boolean <B>isInUpdateMode</B>()</PRE>
<DL>
<DD>Are we updating an existing archive?
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true if updating an existing archive</DL>
</DD>
</DL>
<HR>

<A NAME="addFileset(org.apache.tools.ant.types.FileSet)"><!-- --></A><H3>
addFileset</H3>
<PRE>
public void <B>addFileset</B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;set)</PRE>
<DL>
<DD>Adds a set of files.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>set</CODE> - the fileset to add</DL>
</DD>
</DL>
<HR>

<A NAME="addZipfileset(org.apache.tools.ant.types.ZipFileSet)"><!-- --></A><H3>
addZipfileset</H3>
<PRE>
public void <B>addZipfileset</B>(<A HREF="../../../../../org/apache/tools/ant/types/ZipFileSet.html" title="class in org.apache.tools.ant.types">ZipFileSet</A>&nbsp;set)</PRE>
<DL>
<DD>Adds a set of files that can be
 read from an archive and be given a prefix/fullpath.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>set</CODE> - the zipfileset to add</DL>
</DD>
</DL>
<HR>

<A NAME="add(org.apache.tools.ant.types.ResourceCollection)"><!-- --></A><H3>
add</H3>
<PRE>
public void <B>add</B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;a)</PRE>
<DL>
<DD>Add a collection of resources to be archived.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>a</CODE> - the resources to archive<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addZipGroupFileset(org.apache.tools.ant.types.FileSet)"><!-- --></A><H3>
addZipGroupFileset</H3>
<PRE>
public void <B>addZipGroupFileset</B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;set)</PRE>
<DL>
<DD>Adds a group of zip files.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>set</CODE> - the group (a fileset) to add</DL>
</DD>
</DL>
<HR>

<A NAME="setDuplicate(org.apache.tools.ant.taskdefs.Zip.Duplicate)"><!-- --></A><H3>
setDuplicate</H3>
<PRE>
public void <B>setDuplicate</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.Duplicate.html" title="class in org.apache.tools.ant.taskdefs">Zip.Duplicate</A>&nbsp;df)</PRE>
<DL>
<DD>Sets behavior for when a duplicate file is about to be added -
 one of <code>add</code>, <code>preserve</code> or <code>fail</code>.
 Possible values are: <code>add</code> (keep both
 of the files); <code>preserve</code> (keep the first version
 of the file found); <code>fail</code> halt a problem
 Default for zip tasks is <code>add</code>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>df</CODE> - a <code>Duplicate</code> enumerated value</DL>
</DD>
</DL>
<HR>

<A NAME="setWhenempty(org.apache.tools.ant.taskdefs.Zip.WhenEmpty)"><!-- --></A><H3>
setWhenempty</H3>
<PRE>
public void <B>setWhenempty</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.WhenEmpty.html" title="class in org.apache.tools.ant.taskdefs">Zip.WhenEmpty</A>&nbsp;we)</PRE>
<DL>
<DD>Sets behavior of the task when no files match.
 Possible values are: <code>fail</code> (throw an exception
 and halt the build); <code>skip</code> (do not create
 any archive, but issue a warning); <code>create</code>
 (make an archive with no entries).
 Default for zip tasks is <code>skip</code>;
 for jar tasks, <code>create</code>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>we</CODE> - a <code>WhenEmpty</code> enumerated value</DL>
</DD>
</DL>
<HR>

<A NAME="setEncoding(java.lang.String)"><!-- --></A><H3>
setEncoding</H3>
<PRE>
public void <B>setEncoding</B>(java.lang.String&nbsp;encoding)</PRE>
<DL>
<DD>Encoding to use for filenames, defaults to the platform's
 default encoding.

 <p>For a list of possible values see <a
 href="http://java.sun.com/j2se/1.5.0/docs/guide/intl/encoding.doc.html">http://java.sun.com/j2se/1.5.0/docs/guide/intl/encoding.doc.html</a>.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>encoding</CODE> - the encoding name</DL>
</DD>
</DL>
<HR>

<A NAME="getEncoding()"><!-- --></A><H3>
getEncoding</H3>
<PRE>
public java.lang.String <B>getEncoding</B>()</PRE>
<DL>
<DD>Encoding to use for filenames.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the name of the encoding to use<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setKeepCompression(boolean)"><!-- --></A><H3>
setKeepCompression</H3>
<PRE>
public void <B>setKeepCompression</B>(boolean&nbsp;keep)</PRE>
<DL>
<DD>Whether the original compression of entries coming from a ZIP
 archive should be kept (for example when updating an archive).
 Default is false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>keep</CODE> - if true, keep the original compression<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setComment(java.lang.String)"><!-- --></A><H3>
setComment</H3>
<PRE>
public void <B>setComment</B>(java.lang.String&nbsp;comment)</PRE>
<DL>
<DD>Comment to use for archive.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>comment</CODE> - The content of the comment.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getComment()"><!-- --></A><H3>
getComment</H3>
<PRE>
public java.lang.String <B>getComment</B>()</PRE>
<DL>
<DD>Comment of the archive
<P>
<DD><DL>

<DT><B>Returns:</B><DD>Comment of the archive.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setLevel(int)"><!-- --></A><H3>
setLevel</H3>
<PRE>
public void <B>setLevel</B>(int&nbsp;level)</PRE>
<DL>
<DD>Set the compression level to use.  Default is
 ZipOutputStream.DEFAULT_COMPRESSION.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>level</CODE> - compression level.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getLevel()"><!-- --></A><H3>
getLevel</H3>
<PRE>
public int <B>getLevel</B>()</PRE>
<DL>
<DD>Get the compression level.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>compression level.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setRoundUp(boolean)"><!-- --></A><H3>
setRoundUp</H3>
<PRE>
public void <B>setRoundUp</B>(boolean&nbsp;r)</PRE>
<DL>
<DD>Whether the file modification times will be rounded up to the
 next even number of seconds.

 <p>Zip archives store file modification times with a
 granularity of two seconds, so the times will either be rounded
 up or down.  If you round down, the archive will always seem
 out-of-date when you rerun the task, so the default is to round
 up.  Rounding up may lead to a different type of problems like
 JSPs inside a web archive that seem to be slightly more recent
 than precompiled pages, rendering precompilation useless.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>r</CODE> - a <code>boolean</code> value<DT><B>Since:</B></DT>
  <DD>Ant 1.6.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>validate and build
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="executeMain()"><!-- --></A><H3>
executeMain</H3>
<PRE>
public void <B>executeMain</B>()
                 throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Build the zip file.
 This is called twice if doubleFilePass is true.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="isAddingNewFiles()"><!-- --></A><H3>
isAddingNewFiles</H3>
<PRE>
protected final boolean <B>isAddingNewFiles</B>()</PRE>
<DL>
<DD>Indicates if the task is adding new files into the archive as opposed to
 copying back unchanged files from the backup copy
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true if adding new files</DL>
</DD>
</DL>
<HR>

<A NAME="addResources(org.apache.tools.ant.types.FileSet, org.apache.tools.ant.types.Resource[], org.apache.tools.zip.ZipOutputStream)"><!-- --></A><H3>
addResources</H3>
<PRE>
protected final void <B>addResources</B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;fileset,
                                  <A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>[]&nbsp;resources,
                                  <A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut)
                           throws java.io.IOException</PRE>
<DL>
<DD>Add the given resources.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fileset</CODE> - may give additional information like fullpath or
 permissions.<DD><CODE>resources</CODE> - the resources to add<DD><CODE>zOut</CODE> - the stream to write to
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addResources(org.apache.tools.ant.types.ResourceCollection, org.apache.tools.ant.types.Resource[], org.apache.tools.zip.ZipOutputStream)"><!-- --></A><H3>
addResources</H3>
<PRE>
protected final void <B>addResources</B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;rc,
                                  <A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>[]&nbsp;resources,
                                  <A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut)
                           throws java.io.IOException</PRE>
<DL>
<DD>Add the given resources.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rc</CODE> - may give additional information like fullpath or
 permissions.<DD><CODE>resources</CODE> - the resources to add<DD><CODE>zOut</CODE> - the stream to write to
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="initZipOutputStream(org.apache.tools.zip.ZipOutputStream)"><!-- --></A><H3>
initZipOutputStream</H3>
<PRE>
protected void <B>initZipOutputStream</B>(<A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut)
                            throws java.io.IOException,
                                   <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>method for subclasses to override
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>zOut</CODE> - the zip output stream
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on output error
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on other errors</DL>
</DD>
</DL>
<HR>

<A NAME="finalizeZipOutputStream(org.apache.tools.zip.ZipOutputStream)"><!-- --></A><H3>
finalizeZipOutputStream</H3>
<PRE>
protected void <B>finalizeZipOutputStream</B>(<A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut)
                                throws java.io.IOException,
                                       <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>method for subclasses to override
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>zOut</CODE> - the zip output stream
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on output error
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on other errors</DL>
</DD>
</DL>
<HR>

<A NAME="createEmptyZip(java.io.File)"><!-- --></A><H3>
createEmptyZip</H3>
<PRE>
protected boolean <B>createEmptyZip</B>(java.io.File&nbsp;zipFile)
                          throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Create an empty zip file
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>zipFile</CODE> - the zip file
<DT><B>Returns:</B><DD>true for historic reasons
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="getResourcesToAdd(org.apache.tools.ant.types.ResourceCollection[], java.io.File, boolean)"><!-- --></A><H3>
getResourcesToAdd</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</A> <B>getResourcesToAdd</B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>[]&nbsp;rcs,
                                             java.io.File&nbsp;zipFile,
                                             boolean&nbsp;needsUpdate)
                                      throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Collect the resources that are newer than the corresponding
 entries (or missing) in the original archive.

 <p>If we are going to recreate the archive instead of updating
 it, all resources should be considered as new, if a single one
 is.  Because of this, subclasses overriding this method must
 call <code>super.getResourcesToAdd</code> and indicate with the
 third arg if they already know that the archive is
 out-of-date.</p>

 <p>This method first delegates to getNonFileSetResourceToAdd
 and then invokes the FileSet-arg version.  All this to keep
 backwards compatibility for subclasses that don't know how to
 deal with non-FileSet ResourceCollections.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rcs</CODE> - The resource collections to grab resources from<DD><CODE>zipFile</CODE> - intended archive file (may or may not exist)<DD><CODE>needsUpdate</CODE> - whether we already know that the archive is
 out-of-date.  Subclasses overriding this method are supposed to
 set this value correctly in their call to
 <code>super.getResourcesToAdd</code>.
<DT><B>Returns:</B><DD>an array of resources to add for each fileset passed in as well
         as a flag that indicates whether the archive is uptodate.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if it likes<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getResourcesToAdd(org.apache.tools.ant.types.FileSet[], java.io.File, boolean)"><!-- --></A><H3>
getResourcesToAdd</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</A> <B>getResourcesToAdd</B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>[]&nbsp;filesets,
                                             java.io.File&nbsp;zipFile,
                                             boolean&nbsp;needsUpdate)
                                      throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Collect the resources that are newer than the corresponding
 entries (or missing) in the original archive.

 <p>If we are going to recreate the archive instead of updating
 it, all resources should be considered as new, if a single one
 is.  Because of this, subclasses overriding this method must
 call <code>super.getResourcesToAdd</code> and indicate with the
 third arg if they already know that the archive is
 out-of-date.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filesets</CODE> - The filesets to grab resources from<DD><CODE>zipFile</CODE> - intended archive file (may or may not exist)<DD><CODE>needsUpdate</CODE> - whether we already know that the archive is
 out-of-date.  Subclasses overriding this method are supposed to
 set this value correctly in their call to
 <code>super.getResourcesToAdd</code>.
<DT><B>Returns:</B><DD>an array of resources to add for each fileset passed in as well
         as a flag that indicates whether the archive is uptodate.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if it likes</DL>
</DD>
</DL>
<HR>

<A NAME="getNonFileSetResourcesToAdd(org.apache.tools.ant.types.ResourceCollection[], java.io.File, boolean)"><!-- --></A><H3>
getNonFileSetResourcesToAdd</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</A> <B>getNonFileSetResourcesToAdd</B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>[]&nbsp;rcs,
                                                       java.io.File&nbsp;zipFile,
                                                       boolean&nbsp;needsUpdate)
                                                throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Collect the resources that are newer than the corresponding
 entries (or missing) in the original archive.

 <p>If we are going to recreate the archive instead of updating
 it, all resources should be considered as new, if a single one
 is.  Because of this, subclasses overriding this method must
 call <code>super.getResourcesToAdd</code> and indicate with the
 third arg if they already know that the archive is
 out-of-date.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rcs</CODE> - The filesets to grab resources from<DD><CODE>zipFile</CODE> - intended archive file (may or may not exist)<DD><CODE>needsUpdate</CODE> - whether we already know that the archive is
 out-of-date.  Subclasses overriding this method are supposed to
 set this value correctly in their call to
 <code>super.getResourcesToAdd</code>.
<DT><B>Returns:</B><DD>an array of resources to add for each fileset passed in as well
         as a flag that indicates whether the archive is uptodate.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if it likes</DL>
</DD>
</DL>
<HR>

<A NAME="grabResources(org.apache.tools.ant.types.FileSet[])"><!-- --></A><H3>
grabResources</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>[][] <B>grabResources</B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>[]&nbsp;filesets)</PRE>
<DL>
<DD>Fetch all included and not excluded resources from the sets.

 <p>Included directories will precede included files.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filesets</CODE> - an array of filesets
<DT><B>Returns:</B><DD>the resources included<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="grabNonFileSetResources(org.apache.tools.ant.types.ResourceCollection[])"><!-- --></A><H3>
grabNonFileSetResources</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>[][] <B>grabNonFileSetResources</B>(<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>[]&nbsp;rcs)</PRE>
<DL>
<DD>Fetch all included and not excluded resources from the collections.

 <p>Included directories will precede included files.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rcs</CODE> - an array of resource collections
<DT><B>Returns:</B><DD>the resources included<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="zipDir(java.io.File, org.apache.tools.zip.ZipOutputStream, java.lang.String, int)"><!-- --></A><H3>
zipDir</H3>
<PRE>
protected void <B>zipDir</B>(java.io.File&nbsp;dir,
                      <A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut,
                      java.lang.String&nbsp;vPath,
                      int&nbsp;mode)
               throws java.io.IOException</PRE>
<DL>
<DD>Add a directory to the zip stream.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>dir</CODE> - the directort to add to the archive<DD><CODE>zOut</CODE> - the stream to write to<DD><CODE>vPath</CODE> - the name this entry shall have in the archive<DD><CODE>mode</CODE> - the Unix permissions to set.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="zipDir(java.io.File, org.apache.tools.zip.ZipOutputStream, java.lang.String, int, org.apache.tools.zip.ZipExtraField[])"><!-- --></A><H3>
zipDir</H3>
<PRE>
protected void <B>zipDir</B>(java.io.File&nbsp;dir,
                      <A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut,
                      java.lang.String&nbsp;vPath,
                      int&nbsp;mode,
                      <A HREF="../../../../../org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</A>[]&nbsp;extra)
               throws java.io.IOException</PRE>
<DL>
<DD>Add a directory to the zip stream.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>dir</CODE> - the directort to add to the archive<DD><CODE>zOut</CODE> - the stream to write to<DD><CODE>vPath</CODE> - the name this entry shall have in the archive<DD><CODE>mode</CODE> - the Unix permissions to set.<DD><CODE>extra</CODE> - ZipExtraFields to add
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="zipFile(java.io.InputStream, org.apache.tools.zip.ZipOutputStream, java.lang.String, long, java.io.File, int)"><!-- --></A><H3>
zipFile</H3>
<PRE>
protected void <B>zipFile</B>(java.io.InputStream&nbsp;in,
                       <A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut,
                       java.lang.String&nbsp;vPath,
                       long&nbsp;lastModified,
                       java.io.File&nbsp;fromArchive,
                       int&nbsp;mode)
                throws java.io.IOException</PRE>
<DL>
<DD>Adds a new entry to the archive, takes care of duplicates as well.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>in</CODE> - the stream to read data for the entry from.<DD><CODE>zOut</CODE> - the stream to write to.<DD><CODE>vPath</CODE> - the name this entry shall have in the archive.<DD><CODE>lastModified</CODE> - last modification time for the entry.<DD><CODE>fromArchive</CODE> - the original archive we are copying this
 entry from, will be null if we are not copying from an archive.<DD><CODE>mode</CODE> - the Unix permissions to set.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="zipFile(java.io.File, org.apache.tools.zip.ZipOutputStream, java.lang.String, int)"><!-- --></A><H3>
zipFile</H3>
<PRE>
protected void <B>zipFile</B>(java.io.File&nbsp;file,
                       <A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut,
                       java.lang.String&nbsp;vPath,
                       int&nbsp;mode)
                throws java.io.IOException</PRE>
<DL>
<DD>Method that gets called when adding from <code>java.io.File</code> instances.

 <p>This implementation delegates to the six-arg version.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>file</CODE> - the file to add to the archive<DD><CODE>zOut</CODE> - the stream to write to<DD><CODE>vPath</CODE> - the name this entry shall have in the archive<DD><CODE>mode</CODE> - the Unix permissions to set.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addParentDirs(java.io.File, java.lang.String, org.apache.tools.zip.ZipOutputStream, java.lang.String, int)"><!-- --></A><H3>
addParentDirs</H3>
<PRE>
protected final void <B>addParentDirs</B>(java.io.File&nbsp;baseDir,
                                   java.lang.String&nbsp;entry,
                                   <A HREF="../../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A>&nbsp;zOut,
                                   java.lang.String&nbsp;prefix,
                                   int&nbsp;dirMode)
                            throws java.io.IOException</PRE>
<DL>
<DD>Ensure all parent dirs of a given entry have been added.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>baseDir</CODE> - the base directory to use (may be null)<DD><CODE>entry</CODE> - the entry name to create directories from<DD><CODE>zOut</CODE> - the stream to write to<DD><CODE>prefix</CODE> - a prefix to place on the created entries<DD><CODE>dirMode</CODE> - the directory mode
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="cleanUp()"><!-- --></A><H3>
cleanUp</H3>
<PRE>
protected void <B>cleanUp</B>()</PRE>
<DL>
<DD>Do any clean up necessary to allow this instance to be used again.

 <p>When we get here, the Zip file has been closed and all we
 need to do is to reset some globals.</p>

 <p>This method will only reset globals that have been changed
 during execute(), it will not alter the attributes or nested
 child elements.  If you want to reset the instance so that you
 can later zip a completely different set of files, you must use
 the reset method.</p>
<P>
<DD><DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#reset()"><CODE>reset()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="reset()"><!-- --></A><H3>
reset</H3>
<PRE>
public void <B>reset</B>()</PRE>
<DL>
<DD>Makes this instance reset all attributes to their default
 values and forget all children.
<P>
<DD><DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html#cleanUp()"><CODE>cleanUp()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="isEmpty(org.apache.tools.ant.types.Resource[][])"><!-- --></A><H3>
isEmpty</H3>
<PRE>
protected static final boolean <B>isEmpty</B>(<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>[][]&nbsp;r)</PRE>
<DL>
<DD>Check is the resource arrays are empty.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>r</CODE> - the arrays to check
<DT><B>Returns:</B><DD>true if all individual arrays are empty<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="selectFileResources(org.apache.tools.ant.types.Resource[])"><!-- --></A><H3>
selectFileResources</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>[] <B>selectFileResources</B>(<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>[]&nbsp;orig)</PRE>
<DL>
<DD>Drops all non-file resources from the given array.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>orig</CODE> - the resources to filter
<DT><B>Returns:</B><DD>the filters resources<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Param.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Zip.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Zip.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
