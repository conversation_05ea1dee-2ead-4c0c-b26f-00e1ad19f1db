<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:20 EST 2006 -->
<TITLE>
MagicNames (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.MagicNames class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="MagicNames (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/Main.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/MagicNames.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="MagicNames.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#methods_inherited_from_class_java.lang.Object">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;METHOD</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class MagicNames</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.MagicNames</B>
</PRE>
<HR>
<DL>
<DT><PRE>public final class <B>MagicNames</B><DT>extends java.lang.Object</DL>
</PRE>

<P>
Magic names used within Ant.

 Not all magic names are here yet.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#ANT_EXECUTOR_CLASSNAME">ANT_EXECUTOR_CLASSNAME</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Property defining the classname of an executor.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#ANT_EXECUTOR_REFERENCE">ANT_EXECUTOR_REFERENCE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Reference to the current Ant executor
 Value: "ant.executor"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#ANT_FILE">ANT_FILE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;property for ant file name
 Value: "ant.file"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#ANT_HOME">ANT_HOME</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Property used to store the location of ant.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#ANT_JAVA_VERSION">ANT_JAVA_VERSION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Property used to store the java version ant is running in.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#ANT_LIB">ANT_LIB</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Property used to store the location of the ant library (typically the ant.jar file.)</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#ANT_VERSION">ANT_VERSION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Ant version property.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#ANTLIB_PREFIX">ANTLIB_PREFIX</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;prefix for antlib URIs:
 "antlib:"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#BUILD_JAVAC_SOURCE">BUILD_JAVAC_SOURCE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;property that provides the default value for javac's and
 javadoc's source attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#BUILD_JAVAC_TARGET">BUILD_JAVAC_TARGET</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;property that provides the default value for javac's target
 attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#BUILD_SYSCLASSPATH">BUILD_SYSCLASSPATH</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;System classpath policy.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#PROJECT_BASEDIR">PROJECT_BASEDIR</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;property name for basedir of the project
 Value: "basedir"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#REFID_CLASSPATH_LOADER_PREFIX">REFID_CLASSPATH_LOADER_PREFIX</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Prefix used to store classloader references.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#REFID_CLASSPATH_REUSE_LOADER">REFID_CLASSPATH_REUSE_LOADER</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Name of the magic property that controls classloader reuse</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#REFID_PROPERTY_HELPER">REFID_PROPERTY_HELPER</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Reference used to store the property helper
 Value: "ant.PropertyHelper"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#REGEXP_IMPL">REGEXP_IMPL</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;property for regular expression implementation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#REPOSITORY_DIR_PROPERTY">REPOSITORY_DIR_PROPERTY</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Name of the property which can provide an override of the repository dir
 for the libraries task
 Value "ant.maven.repository.dir"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#REPOSITORY_URL_PROPERTY">REPOSITORY_URL_PROPERTY</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Name of the property which can provide an override of the repository URL
 for the libraries task
 Value "ant.maven.repository.url"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#SCRIPT_REPOSITORY">SCRIPT_REPOSITORY</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The name of the script repository used by the script repo task
 Value "org.apache.ant.scriptrepo"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#SYSTEM_LOADER_REF">SYSTEM_LOADER_REF</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The name of the reference to the System Class Loader
 Value "ant.coreLoader"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#TASKDEF_PROPERTIES_RESOURCE">TASKDEF_PROPERTIES_RESOURCE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;name of the resource that taskdefs are stored under
 Value: "/org/apache/tools/ant/taskdefs/defaults.properties"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html#TYPEDEFS_PROPERTIES_RESOURCE">TYPEDEFS_PROPERTIES_RESOURCE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;name of the resource that typedefs are stored under
 Value: "/org/apache/tools/ant/types/defaults.properties"</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ANTLIB_PREFIX"><!-- --></A><H3>
ANTLIB_PREFIX</H3>
<PRE>
public static final java.lang.String <B>ANTLIB_PREFIX</B></PRE>
<DL>
<DD>prefix for antlib URIs:
 "antlib:"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANTLIB_PREFIX">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ANT_VERSION"><!-- --></A><H3>
ANT_VERSION</H3>
<PRE>
public static final java.lang.String <B>ANT_VERSION</B></PRE>
<DL>
<DD>Ant version property. "ant.version"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_VERSION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="BUILD_SYSCLASSPATH"><!-- --></A><H3>
BUILD_SYSCLASSPATH</H3>
<PRE>
public static final java.lang.String <B>BUILD_SYSCLASSPATH</B></PRE>
<DL>
<DD>System classpath policy. "build.sysclasspath"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.BUILD_SYSCLASSPATH">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="SCRIPT_REPOSITORY"><!-- --></A><H3>
SCRIPT_REPOSITORY</H3>
<PRE>
public static final java.lang.String <B>SCRIPT_REPOSITORY</B></PRE>
<DL>
<DD>The name of the script repository used by the script repo task
 Value "org.apache.ant.scriptrepo"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.SCRIPT_REPOSITORY">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="SYSTEM_LOADER_REF"><!-- --></A><H3>
SYSTEM_LOADER_REF</H3>
<PRE>
public static final java.lang.String <B>SYSTEM_LOADER_REF</B></PRE>
<DL>
<DD>The name of the reference to the System Class Loader
 Value "ant.coreLoader"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.SYSTEM_LOADER_REF">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="REPOSITORY_DIR_PROPERTY"><!-- --></A><H3>
REPOSITORY_DIR_PROPERTY</H3>
<PRE>
public static final java.lang.String <B>REPOSITORY_DIR_PROPERTY</B></PRE>
<DL>
<DD>Name of the property which can provide an override of the repository dir
 for the libraries task
 Value "ant.maven.repository.dir"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.REPOSITORY_DIR_PROPERTY">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="REPOSITORY_URL_PROPERTY"><!-- --></A><H3>
REPOSITORY_URL_PROPERTY</H3>
<PRE>
public static final java.lang.String <B>REPOSITORY_URL_PROPERTY</B></PRE>
<DL>
<DD>Name of the property which can provide an override of the repository URL
 for the libraries task
 Value "ant.maven.repository.url"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.REPOSITORY_URL_PROPERTY">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="TASKDEF_PROPERTIES_RESOURCE"><!-- --></A><H3>
TASKDEF_PROPERTIES_RESOURCE</H3>
<PRE>
public static final java.lang.String <B>TASKDEF_PROPERTIES_RESOURCE</B></PRE>
<DL>
<DD>name of the resource that taskdefs are stored under
 Value: "/org/apache/tools/ant/taskdefs/defaults.properties"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.TASKDEF_PROPERTIES_RESOURCE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="TYPEDEFS_PROPERTIES_RESOURCE"><!-- --></A><H3>
TYPEDEFS_PROPERTIES_RESOURCE</H3>
<PRE>
public static final java.lang.String <B>TYPEDEFS_PROPERTIES_RESOURCE</B></PRE>
<DL>
<DD>name of the resource that typedefs are stored under
 Value: "/org/apache/tools/ant/types/defaults.properties"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.TYPEDEFS_PROPERTIES_RESOURCE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ANT_EXECUTOR_REFERENCE"><!-- --></A><H3>
ANT_EXECUTOR_REFERENCE</H3>
<PRE>
public static final java.lang.String <B>ANT_EXECUTOR_REFERENCE</B></PRE>
<DL>
<DD>Reference to the current Ant executor
 Value: "ant.executor"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_EXECUTOR_REFERENCE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ANT_EXECUTOR_CLASSNAME"><!-- --></A><H3>
ANT_EXECUTOR_CLASSNAME</H3>
<PRE>
public static final java.lang.String <B>ANT_EXECUTOR_CLASSNAME</B></PRE>
<DL>
<DD>Property defining the classname of an executor.
 Value: "ant.executor.class"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_EXECUTOR_CLASSNAME">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="PROJECT_BASEDIR"><!-- --></A><H3>
PROJECT_BASEDIR</H3>
<PRE>
public static final java.lang.String <B>PROJECT_BASEDIR</B></PRE>
<DL>
<DD>property name for basedir of the project
 Value: "basedir"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.PROJECT_BASEDIR">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ANT_FILE"><!-- --></A><H3>
ANT_FILE</H3>
<PRE>
public static final java.lang.String <B>ANT_FILE</B></PRE>
<DL>
<DD>property for ant file name
 Value: "ant.file"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_FILE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ANT_JAVA_VERSION"><!-- --></A><H3>
ANT_JAVA_VERSION</H3>
<PRE>
public static final java.lang.String <B>ANT_JAVA_VERSION</B></PRE>
<DL>
<DD>Property used to store the java version ant is running in.
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_JAVA_VERSION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ANT_HOME"><!-- --></A><H3>
ANT_HOME</H3>
<PRE>
public static final java.lang.String <B>ANT_HOME</B></PRE>
<DL>
<DD>Property used to store the location of ant.
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_HOME">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ANT_LIB"><!-- --></A><H3>
ANT_LIB</H3>
<PRE>
public static final java.lang.String <B>ANT_LIB</B></PRE>
<DL>
<DD>Property used to store the location of the ant library (typically the ant.jar file.)
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.ANT_LIB">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="REGEXP_IMPL"><!-- --></A><H3>
REGEXP_IMPL</H3>
<PRE>
public static final java.lang.String <B>REGEXP_IMPL</B></PRE>
<DL>
<DD>property for regular expression implementation.
 Value: "ant.regexp.regexpimpl"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.REGEXP_IMPL">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="BUILD_JAVAC_SOURCE"><!-- --></A><H3>
BUILD_JAVAC_SOURCE</H3>
<PRE>
public static final java.lang.String <B>BUILD_JAVAC_SOURCE</B></PRE>
<DL>
<DD>property that provides the default value for javac's and
 javadoc's source attribute.
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.7
 Value: "ant.build.javac.source"</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.BUILD_JAVAC_SOURCE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="BUILD_JAVAC_TARGET"><!-- --></A><H3>
BUILD_JAVAC_TARGET</H3>
<PRE>
public static final java.lang.String <B>BUILD_JAVAC_TARGET</B></PRE>
<DL>
<DD>property that provides the default value for javac's target
 attribute.
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.7
 Value: "ant.build.javac.target"</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.BUILD_JAVAC_TARGET">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="REFID_CLASSPATH_REUSE_LOADER"><!-- --></A><H3>
REFID_CLASSPATH_REUSE_LOADER</H3>
<PRE>
public static final java.lang.String <B>REFID_CLASSPATH_REUSE_LOADER</B></PRE>
<DL>
<DD>Name of the magic property that controls classloader reuse
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.4.
 Value: "ant.reuse.loader"</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.REFID_CLASSPATH_REUSE_LOADER">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="REFID_CLASSPATH_LOADER_PREFIX"><!-- --></A><H3>
REFID_CLASSPATH_LOADER_PREFIX</H3>
<PRE>
public static final java.lang.String <B>REFID_CLASSPATH_LOADER_PREFIX</B></PRE>
<DL>
<DD>Prefix used to store classloader references.
 Value: "ant.loader."
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.REFID_CLASSPATH_LOADER_PREFIX">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="REFID_PROPERTY_HELPER"><!-- --></A><H3>
REFID_PROPERTY_HELPER</H3>
<PRE>
public static final java.lang.String <B>REFID_PROPERTY_HELPER</B></PRE>
<DL>
<DD>Reference used to store the property helper
 Value: "ant.PropertyHelper"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.MagicNames.REFID_PROPERTY_HELPER">Constant Field Values</A></DL>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/Main.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/MagicNames.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="MagicNames.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#methods_inherited_from_class_java.lang.Object">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;METHOD</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
