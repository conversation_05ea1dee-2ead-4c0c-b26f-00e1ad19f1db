<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:32 EST 2006 -->
<TITLE>
BZip2Resource (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.types.resources.BZip2Resource class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="BZip2Resource (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/types/resources/BCFileSet.html" title="class in org.apache.tools.ant.types.resources"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html" title="class in org.apache.tools.ant.types.resources"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../index.html?org/apache/tools/ant/types/resources/BZip2Resource.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="BZip2Resource.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.types.Resource">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.types.resources</FONT>
<BR>
Class BZip2Resource</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</A>
          <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.Resource</A>
              <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html" title="class in org.apache.tools.ant.types.resources">org.apache.tools.ant.types.resources.CompressedResource</A>
                  <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.types.resources.BZip2Resource</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable, java.lang.Comparable, <A HREF="../../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>BZip2Resource</B><DT>extends <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html" title="class in org.apache.tools.ant.types.resources">CompressedResource</A></DL>
</PRE>

<P>
A Bzip2 compressed resource.

 <p>Wraps around another resource, delegates all quries to that
 other resource but uncompresses/compresses streams on the fly.</p>
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.types.Resource"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.types.<A HREF="../../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/types/Resource.html#UNKNOWN_DATETIME">UNKNOWN_DATETIME</A>, <A HREF="../../../../../../org/apache/tools/ant/types/Resource.html#UNKNOWN_SIZE">UNKNOWN_SIZE</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.types.DataType"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.types.<A HREF="../../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#checked">checked</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#ref">ref</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/types/resources/BZip2Resource.html#BZip2Resource()">BZip2Resource</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A no-arg constructor</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/types/resources/BZip2Resource.html#BZip2Resource(org.apache.tools.ant.types.ResourceCollection)">BZip2Resource</A></B>(<A HREF="../../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;other)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor with another resource to wrap.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/types/resources/BZip2Resource.html#getCompressionName()">getCompressionName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the name of the compression method.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.InputStream</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/types/resources/BZip2Resource.html#wrapStream(java.io.InputStream)">wrapStream</A></B>(java.io.InputStream&nbsp;in)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Decompress on the fly using <A HREF="../../../../../../org/apache/tools/bzip2/CBZip2InputStream.html" title="class in org.apache.tools.bzip2"><CODE>CBZip2InputStream</CODE></A>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.OutputStream</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/types/resources/BZip2Resource.html#wrapStream(java.io.OutputStream)">wrapStream</A></B>(java.io.OutputStream&nbsp;out)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Compress on the fly using <A HREF="../../../../../../org/apache/tools/bzip2/CBZip2OutputStream.html" title="class in org.apache.tools.bzip2"><CODE>CBZip2OutputStream</CODE></A>.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.types.resources.CompressedResource"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.types.resources.<A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html" title="class in org.apache.tools.ant.types.resources">CompressedResource</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#addConfigured(org.apache.tools.ant.types.ResourceCollection)">addConfigured</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#compareTo(java.lang.Object)">compareTo</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#getInputStream()">getInputStream</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#getLastModified()">getLastModified</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#getName()">getName</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#getOutputStream()">getOutputStream</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#getSize()">getSize</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#hashCode()">hashCode</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#isDirectory()">isDirectory</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#isExists()">isExists</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#isFilesystemOnly()">isFilesystemOnly</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#setDirectory(boolean)">setDirectory</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#setExists(boolean)">setExists</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#setLastModified(long)">setLastModified</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#setName(java.lang.String)">setName</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#setSize(long)">setSize</A>, <A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#toString()">toString</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.types.Resource"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.types.<A HREF="../../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/types/Resource.html#clone()">clone</A>, <A HREF="../../../../../../org/apache/tools/ant/types/Resource.html#equals(java.lang.Object)">equals</A>, <A HREF="../../../../../../org/apache/tools/ant/types/Resource.html#getMagicNumber(byte[])">getMagicNumber</A>, <A HREF="../../../../../../org/apache/tools/ant/types/Resource.html#iterator()">iterator</A>, <A HREF="../../../../../../org/apache/tools/ant/types/Resource.html#size()">size</A>, <A HREF="../../../../../../org/apache/tools/ant/types/Resource.html#toLongString()">toLongString</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.types.DataType"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.types.<A HREF="../../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#checkAttributesAllowed()">checkAttributesAllowed</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#checkChildrenAllowed()">checkChildrenAllowed</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#circularReference()">circularReference</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference()">dieOnCircularReference</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference(java.util.Stack, org.apache.tools.ant.Project)">dieOnCircularReference</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef()">getCheckedRef</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(java.lang.Class, java.lang.String)">getCheckedRef</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(java.lang.Class, java.lang.String, org.apache.tools.ant.Project)">getCheckedRef</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#getDataTypeName()">getDataTypeName</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#getRefid()">getRefid</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType, java.util.Stack, org.apache.tools.ant.Project)">invokeCircularReferenceCheck</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#isChecked()">isChecked</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#isReference()">isReference</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#noChildrenAllowed()">noChildrenAllowed</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#setChecked(boolean)">setChecked</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#tooManyAttributes()">tooManyAttributes</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String)">log</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>finalize, getClass, notify, notifyAll, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="BZip2Resource()"><!-- --></A><H3>
BZip2Resource</H3>
<PRE>
public <B>BZip2Resource</B>()</PRE>
<DL>
<DD>A no-arg constructor
<P>
</DL>
<HR>

<A NAME="BZip2Resource(org.apache.tools.ant.types.ResourceCollection)"><!-- --></A><H3>
BZip2Resource</H3>
<PRE>
public <B>BZip2Resource</B>(<A HREF="../../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;other)</PRE>
<DL>
<DD>Constructor with another resource to wrap.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>other</CODE> - the resource to wrap.</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="wrapStream(java.io.InputStream)"><!-- --></A><H3>
wrapStream</H3>
<PRE>
protected java.io.InputStream <B>wrapStream</B>(java.io.InputStream&nbsp;in)
                                  throws java.io.IOException</PRE>
<DL>
<DD>Decompress on the fly using <A HREF="../../../../../../org/apache/tools/bzip2/CBZip2InputStream.html" title="class in org.apache.tools.bzip2"><CODE>CBZip2InputStream</CODE></A>.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#wrapStream(java.io.InputStream)">wrapStream</A></CODE> in class <CODE><A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html" title="class in org.apache.tools.ant.types.resources">CompressedResource</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>in</CODE> - the stream to wrap.
<DT><B>Returns:</B><DD>the wrapped stream.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there is a problem.</DL>
</DD>
</DL>
<HR>

<A NAME="wrapStream(java.io.OutputStream)"><!-- --></A><H3>
wrapStream</H3>
<PRE>
protected java.io.OutputStream <B>wrapStream</B>(java.io.OutputStream&nbsp;out)
                                   throws java.io.IOException</PRE>
<DL>
<DD>Compress on the fly using <A HREF="../../../../../../org/apache/tools/bzip2/CBZip2OutputStream.html" title="class in org.apache.tools.bzip2"><CODE>CBZip2OutputStream</CODE></A>.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#wrapStream(java.io.OutputStream)">wrapStream</A></CODE> in class <CODE><A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html" title="class in org.apache.tools.ant.types.resources">CompressedResource</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>out</CODE> - the stream to wrap.
<DT><B>Returns:</B><DD>the wrapped stream.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there is a problem.</DL>
</DD>
</DL>
<HR>

<A NAME="getCompressionName()"><!-- --></A><H3>
getCompressionName</H3>
<PRE>
protected java.lang.String <B>getCompressionName</B>()</PRE>
<DL>
<DD>Get the name of the compression method.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html#getCompressionName()">getCompressionName</A></CODE> in class <CODE><A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html" title="class in org.apache.tools.ant.types.resources">CompressedResource</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the string "Bzip2".</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/types/resources/BCFileSet.html" title="class in org.apache.tools.ant.types.resources"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/types/resources/CompressedResource.html" title="class in org.apache.tools.ant.types.resources"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../index.html?org/apache/tools/ant/types/resources/BZip2Resource.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="BZip2Resource.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.types.Resource">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
