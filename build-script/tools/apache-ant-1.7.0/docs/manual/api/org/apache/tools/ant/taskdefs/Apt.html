<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:21 EST 2006 -->
<TITLE>
Apt (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.Apt class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Apt (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/AntStructure.StructurePrinter.html" title="interface in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.Option.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Apt.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Apt.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class Apt</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.MatchingTask</A>
              <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Javac</A>
                  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.Apt</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable, <A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>Apt</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</A></DL>
</PRE>

<P>
Apt Task for running the Annotation processing tool for JDK 1.5.  It derives
 from the existing Javac task, and forces the compiler based on whether we're
 executing internally, or externally.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.Option.html" title="class in org.apache.tools.ant.taskdefs">Apt.Option</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The nested option element.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="nested_classes_inherited_from_class_org.apache.tools.ant.taskdefs.Javac"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Nested classes/interfaces inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.taskdefs">Javac.ImplementationSpecificArgument</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#ERROR_IGNORING_COMPILER_OPTION">ERROR_IGNORING_COMPILER_OPTION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;An warning message when ignoring compiler attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#ERROR_WRONG_JAVA_VERSION">ERROR_WRONG_JAVA_VERSION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A warning message if used with java < 1.5.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#EXECUTABLE_NAME">EXECUTABLE_NAME</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The name of the apt tool.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#WARNING_IGNORING_FORK">WARNING_IGNORING_FORK</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;exposed for debug messages</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.Javac"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#compileList">compileList</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#failOnError">failOnError</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#listFiles">listFiles</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.MatchingTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#fileset">fileset</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#Apt()">Apt</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Construtor for Apt task.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#createFactoryPath()">createFactoryPath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a path to the factoryPath attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.Option.html" title="class in org.apache.tools.ant.taskdefs">Apt.Option</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#createOption()">createOption</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a nested option.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Do the compilation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#getAptExecutable()">getAptExecutable</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the name of the apt executable.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#getCompiler()">getCompiler</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the compiler class name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#getFactory()">getFactory</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the factory option for the apt compiler.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#getFactoryPath()">getFactoryPath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the factory path attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#getOptions()">getOptions</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the options to the compiler.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#getPreprocessDir()">getPreprocessDir</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the preprocessdir attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#isCompile()">isCompile</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the compile option for the apt compiler.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#setCompile(boolean)">setCompile</A></B>(boolean&nbsp;compile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the compile option for the apt compiler.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#setCompiler(java.lang.String)">setCompiler</A></B>(java.lang.String&nbsp;compiler)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the compiler.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#setFactory(java.lang.String)">setFactory</A></B>(java.lang.String&nbsp;factory)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the factory option for the apt compiler.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#setFactoryPathRef(org.apache.tools.ant.types.Reference)">setFactoryPathRef</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;ref)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a reference to a path to the factoryPath attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#setFork(boolean)">setFork</A></B>(boolean&nbsp;fork)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the fork attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html#setPreprocessDir(java.io.File)">setPreprocessDir</A></B>(java.io.File&nbsp;preprocessDir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the preprocessdir attribute.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.Javac"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#checkParameters()">checkParameters</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#compile()">compile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#createBootclasspath()">createBootclasspath</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#createClasspath()">createClasspath</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#createCompilerArg()">createCompilerArg</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#createExtdirs()">createExtdirs</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#createSourcepath()">createSourcepath</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#createSrc()">createSrc</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getBootclasspath()">getBootclasspath</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getClasspath()">getClasspath</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getCompilerVersion()">getCompilerVersion</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getCurrentCompilerArgs()">getCurrentCompilerArgs</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getDebug()">getDebug</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getDebugLevel()">getDebugLevel</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getDepend()">getDepend</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getDeprecation()">getDeprecation</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getDestdir()">getDestdir</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getEncoding()">getEncoding</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getExecutable()">getExecutable</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getExtdirs()">getExtdirs</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getFailonerror()">getFailonerror</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getFileList()">getFileList</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getIncludeantruntime()">getIncludeantruntime</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getIncludejavaruntime()">getIncludejavaruntime</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getJavacExecutable()">getJavacExecutable</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getListfiles()">getListfiles</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getMemoryInitialSize()">getMemoryInitialSize</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getMemoryMaximumSize()">getMemoryMaximumSize</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getNowarn()">getNowarn</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getOptimize()">getOptimize</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getSource()">getSource</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getSourcepath()">getSourcepath</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getSrcdir()">getSrcdir</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getSystemJavac()">getSystemJavac</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getTarget()">getTarget</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getTempdir()">getTempdir</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getVerbose()">getVerbose</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#isForkedJavac()">isForkedJavac</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#isJdkCompiler(java.lang.String)">isJdkCompiler</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#recreateSrc()">recreateSrc</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#resetFileLists()">resetFileLists</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#scanDir(java.io.File, java.io.File, java.lang.String[])">scanDir</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setBootclasspath(org.apache.tools.ant.types.Path)">setBootclasspath</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setBootClasspathRef(org.apache.tools.ant.types.Reference)">setBootClasspathRef</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setClasspathRef(org.apache.tools.ant.types.Reference)">setClasspathRef</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setDebug(boolean)">setDebug</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setDebugLevel(java.lang.String)">setDebugLevel</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setDepend(boolean)">setDepend</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setDeprecation(boolean)">setDeprecation</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setDestdir(java.io.File)">setDestdir</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setEncoding(java.lang.String)">setEncoding</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setExecutable(java.lang.String)">setExecutable</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setExtdirs(org.apache.tools.ant.types.Path)">setExtdirs</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setFailonerror(boolean)">setFailonerror</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setIncludeantruntime(boolean)">setIncludeantruntime</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setIncludejavaruntime(boolean)">setIncludejavaruntime</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setListfiles(boolean)">setListfiles</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setMemoryInitialSize(java.lang.String)">setMemoryInitialSize</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setMemoryMaximumSize(java.lang.String)">setMemoryMaximumSize</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setNowarn(boolean)">setNowarn</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setOptimize(boolean)">setOptimize</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setProceed(boolean)">setProceed</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setSource(java.lang.String)">setSource</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setSourcepath(org.apache.tools.ant.types.Path)">setSourcepath</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setSourcepathRef(org.apache.tools.ant.types.Reference)">setSourcepathRef</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setSrcdir(org.apache.tools.ant.types.Path)">setSrcdir</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setTarget(java.lang.String)">setTarget</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setTempdir(java.io.File)">setTempdir</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setVerbose(boolean)">setVerbose</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.MatchingTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createExclude()">createExclude</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createExcludesFile()">createExcludesFile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createInclude()">createInclude</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createIncludesFile()">createIncludesFile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createPatternSet()">createPatternSet</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#hasSelectors()">hasSelectors</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#selectorCount()">selectorCount</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#selectorElements()">selectorElements</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setExcludes(java.lang.String)">setExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setIncludes(java.lang.String)">setIncludes</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#XsetItems(java.lang.String)">XsetItems</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="EXECUTABLE_NAME"><!-- --></A><H3>
EXECUTABLE_NAME</H3>
<PRE>
public static final java.lang.String <B>EXECUTABLE_NAME</B></PRE>
<DL>
<DD>The name of the apt tool.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Apt.EXECUTABLE_NAME">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_IGNORING_COMPILER_OPTION"><!-- --></A><H3>
ERROR_IGNORING_COMPILER_OPTION</H3>
<PRE>
public static final java.lang.String <B>ERROR_IGNORING_COMPILER_OPTION</B></PRE>
<DL>
<DD>An warning message when ignoring compiler attribute.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Apt.ERROR_IGNORING_COMPILER_OPTION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_WRONG_JAVA_VERSION"><!-- --></A><H3>
ERROR_WRONG_JAVA_VERSION</H3>
<PRE>
public static final java.lang.String <B>ERROR_WRONG_JAVA_VERSION</B></PRE>
<DL>
<DD>A warning message if used with java < 1.5.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Apt.ERROR_WRONG_JAVA_VERSION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="WARNING_IGNORING_FORK"><!-- --></A><H3>
WARNING_IGNORING_FORK</H3>
<PRE>
public static final java.lang.String <B>WARNING_IGNORING_FORK</B></PRE>
<DL>
<DD>exposed for debug messages
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Apt.WARNING_IGNORING_FORK">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Apt()"><!-- --></A><H3>
Apt</H3>
<PRE>
public <B>Apt</B>()</PRE>
<DL>
<DD>Construtor for Apt task.
 This sets the apt compiler adapter as the compiler in the super class.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="getAptExecutable()"><!-- --></A><H3>
getAptExecutable</H3>
<PRE>
public java.lang.String <B>getAptExecutable</B>()</PRE>
<DL>
<DD>Get the name of the apt executable.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the name of the executable.</DL>
</DD>
</DL>
<HR>

<A NAME="setCompiler(java.lang.String)"><!-- --></A><H3>
setCompiler</H3>
<PRE>
public void <B>setCompiler</B>(java.lang.String&nbsp;compiler)</PRE>
<DL>
<DD>Set the compiler.
 This is not allowed and a warning log message is made.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setCompiler(java.lang.String)">setCompiler</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>compiler</CODE> - not used.</DL>
</DD>
</DL>
<HR>

<A NAME="setFork(boolean)"><!-- --></A><H3>
setFork</H3>
<PRE>
public void <B>setFork</B>(boolean&nbsp;fork)</PRE>
<DL>
<DD>Set the fork attribute.
 Non-forking APT is highly classpath dependent and appears to be too
 brittle to work. The sole reason this attribute is retained
 is the superclass does it
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#setFork(boolean)">setFork</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fork</CODE> - if false; warn the option is ignored.</DL>
</DD>
</DL>
<HR>

<A NAME="getCompiler()"><!-- --></A><H3>
getCompiler</H3>
<PRE>
public java.lang.String <B>getCompiler</B>()</PRE>
<DL>
<DD>Get the compiler class name.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getCompiler()">getCompiler</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the compiler class name.<DT><B>See Also:</B><DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#getCompilerVersion()"><CODE>Javac.getCompilerVersion()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="isCompile()"><!-- --></A><H3>
isCompile</H3>
<PRE>
public boolean <B>isCompile</B>()</PRE>
<DL>
<DD>Get the compile option for the apt compiler.
 If this is false the "-nocompile" argument will be used.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the value of the compile option.</DL>
</DD>
</DL>
<HR>

<A NAME="setCompile(boolean)"><!-- --></A><H3>
setCompile</H3>
<PRE>
public void <B>setCompile</B>(boolean&nbsp;compile)</PRE>
<DL>
<DD>Set the compile option for the apt compiler.
 Default value is true.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>compile</CODE> - if true set the compile option.</DL>
</DD>
</DL>
<HR>

<A NAME="getFactory()"><!-- --></A><H3>
getFactory</H3>
<PRE>
public java.lang.String <B>getFactory</B>()</PRE>
<DL>
<DD>Get the factory option for the apt compiler.
 If this is non-null the "-factory" argument will be used.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the value of the factory option.</DL>
</DD>
</DL>
<HR>

<A NAME="setFactory(java.lang.String)"><!-- --></A><H3>
setFactory</H3>
<PRE>
public void <B>setFactory</B>(java.lang.String&nbsp;factory)</PRE>
<DL>
<DD>Set the factory option for the apt compiler.
 Default value is null.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>factory</CODE> - the classname of the factory.</DL>
</DD>
</DL>
<HR>

<A NAME="setFactoryPathRef(org.apache.tools.ant.types.Reference)"><!-- --></A><H3>
setFactoryPathRef</H3>
<PRE>
public void <B>setFactoryPathRef</B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;ref)</PRE>
<DL>
<DD>Add a reference to a path to the factoryPath attribute.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ref</CODE> - a reference to a path.</DL>
</DD>
</DL>
<HR>

<A NAME="createFactoryPath()"><!-- --></A><H3>
createFactoryPath</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createFactoryPath</B>()</PRE>
<DL>
<DD>Add a path to the factoryPath attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a path to be configured.</DL>
</DD>
</DL>
<HR>

<A NAME="getFactoryPath()"><!-- --></A><H3>
getFactoryPath</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>getFactoryPath</B>()</PRE>
<DL>
<DD>Get the factory path attribute.
 If this is not null, the "-factorypath" argument will be used.
 The default value is null.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the factory path attribute.</DL>
</DD>
</DL>
<HR>

<A NAME="createOption()"><!-- --></A><H3>
createOption</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.Option.html" title="class in org.apache.tools.ant.taskdefs">Apt.Option</A> <B>createOption</B>()</PRE>
<DL>
<DD>Create a nested option.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>an option to be configured.</DL>
</DD>
</DL>
<HR>

<A NAME="getOptions()"><!-- --></A><H3>
getOptions</H3>
<PRE>
public java.util.Vector <B>getOptions</B>()</PRE>
<DL>
<DD>Get the options to the compiler.
 Each option will use '"-E" name ["=" value]' argument.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the options.</DL>
</DD>
</DL>
<HR>

<A NAME="getPreprocessDir()"><!-- --></A><H3>
getPreprocessDir</H3>
<PRE>
public java.io.File <B>getPreprocessDir</B>()</PRE>
<DL>
<DD>Get the preprocessdir attribute.
 This corresponds to the "-s" argument.
 The default value is null.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the preprocessdir attribute.</DL>
</DD>
</DL>
<HR>

<A NAME="setPreprocessDir(java.io.File)"><!-- --></A><H3>
setPreprocessDir</H3>
<PRE>
public void <B>setPreprocessDir</B>(java.io.File&nbsp;preprocessDir)</PRE>
<DL>
<DD>Set the preprocessdir attribute.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>preprocessDir</CODE> - where to place processor generated source files.</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Do the compilation.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/AntStructure.StructurePrinter.html" title="interface in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.Option.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Apt.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Apt.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
