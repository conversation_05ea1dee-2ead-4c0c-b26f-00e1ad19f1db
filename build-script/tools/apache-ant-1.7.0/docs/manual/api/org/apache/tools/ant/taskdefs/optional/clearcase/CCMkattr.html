<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:26 EST 2006 -->
<TITLE>
CCMkattr (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkattr class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="CCMkattr (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCLock.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkbl.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="CCMkattr.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.optional.clearcase</FONT>
<BR>
Class CCMkattr</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase</A>
              <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.optional.clearcase.CCMkattr</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>CCMkattr</B><DT>extends <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">ClearCase</A></DL>
</PRE>

<P>
Task to perform mkattr command to ClearCase.
 <p>
 The following attributes are interpreted:
 <table border="1">
   <tr>
     <th>Attribute</th>
     <th>Values</th>
     <th>Required</th>
   </tr>
   <tr>
      <td>viewpath</td>
      <td>Path to the ClearCase view file or directory that the command will operate on</td>
      <td>Yes</td>
   <tr>
   <tr>
      <td>replace</td>
      <td>Replace the value of the attribute if it already exists</td>
      <td>No</td>
   <tr>
   <tr>
      <td>recurse</td>
      <td>Process each subdirectory under viewpath</td>
      <td>No</td>
   <tr>
   <tr>
      <td>version</td>
      <td>Identify a specific version to attach the attribute to</td>
      <td>No</td>
   <tr>
   <tr>
      <td>typename</td>
      <td>Name of the attribute type</td>
      <td>Yes</td>
   <tr>
   <tr>
      <td>typevalue</td>
      <td>Value to attach to the attribute type</td>
      <td>Yes</td>
   <tr>
   <tr>
      <td>comment</td>
      <td>Specify a comment. Only one of comment or cfile may be used.</td>
      <td>No</td>
   <tr>
   <tr>
      <td>commentfile</td>
      <td>Specify a file containing a comment. Only one of comment or cfile may be used.</td>
      <td>No</td>
   <tr>
   <tr>
      <td>failonerr</td>
      <td>Throw an exception if the command fails. Default is true</td>
      <td>No</td>
   <tr>
 </table>
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#FLAG_COMMENT">FLAG_COMMENT</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-c flag -- comment to attach to the element</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#FLAG_COMMENTFILE">FLAG_COMMENTFILE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-cfile flag -- file containing a comment to attach to the file</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#FLAG_NOCOMMENT">FLAG_NOCOMMENT</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-nc flag -- no comment is specified</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#FLAG_RECURSE">FLAG_RECURSE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-recurse flag -- process all subdirectories</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#FLAG_REPLACE">FLAG_REPLACE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-replace flag -- replace the existing value of the attribute</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#FLAG_VERSION">FLAG_VERSION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-version flag -- attach attribute to specified version</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.optional.clearcase.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">ClearCase</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_CHECKIN">COMMAND_CHECKIN</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_CHECKOUT">COMMAND_CHECKOUT</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_LOCK">COMMAND_LOCK</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_LSCO">COMMAND_LSCO</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_MKATTR">COMMAND_MKATTR</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_MKBL">COMMAND_MKBL</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_MKDIR">COMMAND_MKDIR</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_MKELEM">COMMAND_MKELEM</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_MKLABEL">COMMAND_MKLABEL</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_MKLBTYPE">COMMAND_MKLBTYPE</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_RMTYPE">COMMAND_RMTYPE</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_UNCHECKOUT">COMMAND_UNCHECKOUT</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_UNLOCK">COMMAND_UNLOCK</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_UPDATE">COMMAND_UPDATE</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#CCMkattr()">CCMkattr</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Executes the task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#getComment()">getComment</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get comment string</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#getCommentFile()">getCommentFile</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get comment file</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#getRecurse()">getRecurse</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get recurse flag status</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#getReplace()">getReplace</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get replace flag status</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#getTypeName()">getTypeName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get attribute type-name</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#getTypeValue()">getTypeValue</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the attribute type-value</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#getVersion()">getVersion</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get version flag status</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#setComment(java.lang.String)">setComment</A></B>(java.lang.String&nbsp;comment)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set comment string</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#setCommentFile(java.lang.String)">setCommentFile</A></B>(java.lang.String&nbsp;cfile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set comment file</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#setRecurse(boolean)">setRecurse</A></B>(boolean&nbsp;recurse)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set recurse flag</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#setReplace(boolean)">setReplace</A></B>(boolean&nbsp;replace)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the replace flag</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#setTypeName(java.lang.String)">setTypeName</A></B>(java.lang.String&nbsp;tn)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the attribute type-name</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#setTypeValue(java.lang.String)">setTypeValue</A></B>(java.lang.String&nbsp;tv)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the attribute type-value</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#setVersion(java.lang.String)">setVersion</A></B>(java.lang.String&nbsp;version)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the version flag</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.optional.clearcase.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">ClearCase</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#getClearToolCommand()">getClearToolCommand</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#getFailOnErr()">getFailOnErr</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#getObjSelect()">getObjSelect</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#getViewPath()">getViewPath</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#getViewPathBasename()">getViewPathBasename</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#run(org.apache.tools.ant.types.Commandline)">run</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#runS(org.apache.tools.ant.types.Commandline)">runS</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#setClearToolDir(java.lang.String)">setClearToolDir</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#setFailOnErr(boolean)">setFailOnErr</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#setObjSelect(java.lang.String)">setObjSelect</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#setViewPath(java.lang.String)">setViewPath</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="FLAG_REPLACE"><!-- --></A><H3>
FLAG_REPLACE</H3>
<PRE>
public static final java.lang.String <B>FLAG_REPLACE</B></PRE>
<DL>
<DD>-replace flag -- replace the existing value of the attribute
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.clearcase.CCMkattr.FLAG_REPLACE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="FLAG_RECURSE"><!-- --></A><H3>
FLAG_RECURSE</H3>
<PRE>
public static final java.lang.String <B>FLAG_RECURSE</B></PRE>
<DL>
<DD>-recurse flag -- process all subdirectories
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.clearcase.CCMkattr.FLAG_RECURSE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="FLAG_VERSION"><!-- --></A><H3>
FLAG_VERSION</H3>
<PRE>
public static final java.lang.String <B>FLAG_VERSION</B></PRE>
<DL>
<DD>-version flag -- attach attribute to specified version
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.clearcase.CCMkattr.FLAG_VERSION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="FLAG_COMMENT"><!-- --></A><H3>
FLAG_COMMENT</H3>
<PRE>
public static final java.lang.String <B>FLAG_COMMENT</B></PRE>
<DL>
<DD>-c flag -- comment to attach to the element
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.clearcase.CCMkattr.FLAG_COMMENT">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="FLAG_COMMENTFILE"><!-- --></A><H3>
FLAG_COMMENTFILE</H3>
<PRE>
public static final java.lang.String <B>FLAG_COMMENTFILE</B></PRE>
<DL>
<DD>-cfile flag -- file containing a comment to attach to the file
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.clearcase.CCMkattr.FLAG_COMMENTFILE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="FLAG_NOCOMMENT"><!-- --></A><H3>
FLAG_NOCOMMENT</H3>
<PRE>
public static final java.lang.String <B>FLAG_NOCOMMENT</B></PRE>
<DL>
<DD>-nc flag -- no comment is specified
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.clearcase.CCMkattr.FLAG_NOCOMMENT">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="CCMkattr()"><!-- --></A><H3>
CCMkattr</H3>
<PRE>
public <B>CCMkattr</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Executes the task.
 <p>
 Builds a command line to execute cleartool and then calls Exec's run method
 to execute the command line.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the command fails and failonerr is set to true</DL>
</DD>
</DL>
<HR>

<A NAME="setReplace(boolean)"><!-- --></A><H3>
setReplace</H3>
<PRE>
public void <B>setReplace</B>(boolean&nbsp;replace)</PRE>
<DL>
<DD>Set the replace flag
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>replace</CODE> - the status to set the flag to</DL>
</DD>
</DL>
<HR>

<A NAME="getReplace()"><!-- --></A><H3>
getReplace</H3>
<PRE>
public boolean <B>getReplace</B>()</PRE>
<DL>
<DD>Get replace flag status
<P>
<DD><DL>

<DT><B>Returns:</B><DD>boolean containing status of replace flag</DL>
</DD>
</DL>
<HR>

<A NAME="setRecurse(boolean)"><!-- --></A><H3>
setRecurse</H3>
<PRE>
public void <B>setRecurse</B>(boolean&nbsp;recurse)</PRE>
<DL>
<DD>Set recurse flag
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>recurse</CODE> - the status to set the flag to</DL>
</DD>
</DL>
<HR>

<A NAME="getRecurse()"><!-- --></A><H3>
getRecurse</H3>
<PRE>
public boolean <B>getRecurse</B>()</PRE>
<DL>
<DD>Get recurse flag status
<P>
<DD><DL>

<DT><B>Returns:</B><DD>boolean containing status of recurse flag</DL>
</DD>
</DL>
<HR>

<A NAME="setVersion(java.lang.String)"><!-- --></A><H3>
setVersion</H3>
<PRE>
public void <B>setVersion</B>(java.lang.String&nbsp;version)</PRE>
<DL>
<DD>Set the version flag
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>version</CODE> - the status to set the flag to</DL>
</DD>
</DL>
<HR>

<A NAME="getVersion()"><!-- --></A><H3>
getVersion</H3>
<PRE>
public java.lang.String <B>getVersion</B>()</PRE>
<DL>
<DD>Get version flag status
<P>
<DD><DL>

<DT><B>Returns:</B><DD>boolean containing status of version flag</DL>
</DD>
</DL>
<HR>

<A NAME="setComment(java.lang.String)"><!-- --></A><H3>
setComment</H3>
<PRE>
public void <B>setComment</B>(java.lang.String&nbsp;comment)</PRE>
<DL>
<DD>Set comment string
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>comment</CODE> - the comment string</DL>
</DD>
</DL>
<HR>

<A NAME="getComment()"><!-- --></A><H3>
getComment</H3>
<PRE>
public java.lang.String <B>getComment</B>()</PRE>
<DL>
<DD>Get comment string
<P>
<DD><DL>

<DT><B>Returns:</B><DD>String containing the comment</DL>
</DD>
</DL>
<HR>

<A NAME="setCommentFile(java.lang.String)"><!-- --></A><H3>
setCommentFile</H3>
<PRE>
public void <B>setCommentFile</B>(java.lang.String&nbsp;cfile)</PRE>
<DL>
<DD>Set comment file
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>cfile</CODE> - the path to the comment file</DL>
</DD>
</DL>
<HR>

<A NAME="getCommentFile()"><!-- --></A><H3>
getCommentFile</H3>
<PRE>
public java.lang.String <B>getCommentFile</B>()</PRE>
<DL>
<DD>Get comment file
<P>
<DD><DL>

<DT><B>Returns:</B><DD>String containing the path to the comment file</DL>
</DD>
</DL>
<HR>

<A NAME="setTypeName(java.lang.String)"><!-- --></A><H3>
setTypeName</H3>
<PRE>
public void <B>setTypeName</B>(java.lang.String&nbsp;tn)</PRE>
<DL>
<DD>Set the attribute type-name
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>tn</CODE> - the type name</DL>
</DD>
</DL>
<HR>

<A NAME="getTypeName()"><!-- --></A><H3>
getTypeName</H3>
<PRE>
public java.lang.String <B>getTypeName</B>()</PRE>
<DL>
<DD>Get attribute type-name
<P>
<DD><DL>

<DT><B>Returns:</B><DD>String containing type name</DL>
</DD>
</DL>
<HR>

<A NAME="setTypeValue(java.lang.String)"><!-- --></A><H3>
setTypeValue</H3>
<PRE>
public void <B>setTypeValue</B>(java.lang.String&nbsp;tv)</PRE>
<DL>
<DD>Set the attribute type-value
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>tv</CODE> - the type value</DL>
</DD>
</DL>
<HR>

<A NAME="getTypeValue()"><!-- --></A><H3>
getTypeValue</H3>
<PRE>
public java.lang.String <B>getTypeValue</B>()</PRE>
<DL>
<DD>Get the attribute type-value
<P>
<DD><DL>

<DT><B>Returns:</B><DD>String containing type value</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCLock.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/clearcase/CCMkbl.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="CCMkattr.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
