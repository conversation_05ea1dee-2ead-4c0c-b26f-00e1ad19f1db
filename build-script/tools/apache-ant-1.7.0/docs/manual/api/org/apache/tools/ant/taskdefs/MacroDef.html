<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:23 EST 2006 -->
<TITLE>
MacroDef (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.MacroDef class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="MacroDef (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/LogStreamHandler.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.Attribute.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/MacroDef.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="MacroDef.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.Task">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class MacroDef</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/taskdefs/AntlibDefinition.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.AntlibDefinition</A>
              <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.MacroDef</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>MacroDef</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/taskdefs/AntlibDefinition.html" title="class in org.apache.tools.ant.taskdefs">AntlibDefinition</A></DL>
</PRE>

<P>
Describe class <code>MacroDef</code> here.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.Attribute.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.Attribute</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;An attribute for the MacroDef task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.NestedSequential.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.NestedSequential</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The class corresponding to the sequential nested element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.TemplateElement.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.TemplateElement</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A nested element for the MacroDef task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.Text.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.Text</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A nested text element for the MacroDef task.</TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html#MacroDef()">MacroDef</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html#addConfiguredAttribute(org.apache.tools.ant.taskdefs.MacroDef.Attribute)">addConfiguredAttribute</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.Attribute.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.Attribute</A>&nbsp;attribute)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add an attribute element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html#addConfiguredElement(org.apache.tools.ant.taskdefs.MacroDef.TemplateElement)">addConfiguredElement</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.TemplateElement.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.TemplateElement</A>&nbsp;element)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add an element element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html#addConfiguredText(org.apache.tools.ant.taskdefs.MacroDef.Text)">addConfiguredText</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.Text.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.Text</A>&nbsp;text)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add the text element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.NestedSequential.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.NestedSequential</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html#createSequential()">createSequential</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This is the sequential nested element of the macrodef.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a new ant type based on the embedded tasks and types.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.List</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html#getAttributes()">getAttributes</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets this macro's attribute (and define?) list.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html#getBackTrace()">getBackTrace</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Map</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html#getElements()">getElements</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets this macro's elements.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html#getNestedTask()">getNestedTask</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convert the nested sequential to an unknown element</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.Text.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.Text</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html#getText()">getText</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html#isValidNameCharacter(char)">isValidNameCharacter</A></B>(char&nbsp;c)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Check if a character is a valid character for an element or
 attribute name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html#sameDefinition(java.lang.Object)">sameDefinition</A></B>(java.lang.Object&nbsp;obj)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Equality method for this definition</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html#setBackTrace(boolean)">setBackTrace</A></B>(boolean&nbsp;backTrace)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the backTrace attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html#setName(java.lang.String)">setName</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Name of the definition</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html#similar(java.lang.Object)">similar</A></B>(java.lang.Object&nbsp;obj)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Similar method for this definition</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.AntlibDefinition"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/AntlibDefinition.html" title="class in org.apache.tools.ant.taskdefs">AntlibDefinition</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/AntlibDefinition.html#getAntlibClassLoader()">getAntlibClassLoader</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AntlibDefinition.html#getURI()">getURI</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AntlibDefinition.html#setAntlibClassLoader(java.lang.ClassLoader)">setAntlibClassLoader</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/AntlibDefinition.html#setURI(java.lang.String)">setURI</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="MacroDef()"><!-- --></A><H3>
MacroDef</H3>
<PRE>
public <B>MacroDef</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setName(java.lang.String)"><!-- --></A><H3>
setName</H3>
<PRE>
public void <B>setName</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Name of the definition
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the name of the definition</DL>
</DD>
</DL>
<HR>

<A NAME="addConfiguredText(org.apache.tools.ant.taskdefs.MacroDef.Text)"><!-- --></A><H3>
addConfiguredText</H3>
<PRE>
public void <B>addConfiguredText</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.Text.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.Text</A>&nbsp;text)</PRE>
<DL>
<DD>Add the text element.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>text</CODE> - the nested text element to add<DT><B>Since:</B></DT>
  <DD>ant 1.6.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getText()"><!-- --></A><H3>
getText</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.Text.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.Text</A> <B>getText</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the nested text element<DT><B>Since:</B></DT>
  <DD>ant 1.6.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setBackTrace(boolean)"><!-- --></A><H3>
setBackTrace</H3>
<PRE>
public void <B>setBackTrace</B>(boolean&nbsp;backTrace)</PRE>
<DL>
<DD>Set the backTrace attribute.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>backTrace</CODE> - if true and the macro instance generates
                  an error, a backtrace of the location within
                  the macro and call to the macro will be output.
                  if false, only the location of the call to the
                  macro will be shown. Default is true.<DT><B>Since:</B></DT>
  <DD>ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getBackTrace()"><!-- --></A><H3>
getBackTrace</H3>
<PRE>
public boolean <B>getBackTrace</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the backTrace attribute.<DT><B>Since:</B></DT>
  <DD>ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="createSequential()"><!-- --></A><H3>
createSequential</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.NestedSequential.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.NestedSequential</A> <B>createSequential</B>()</PRE>
<DL>
<DD>This is the sequential nested element of the macrodef.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a sequential element to be configured.</DL>
</DD>
</DL>
<HR>

<A NAME="getNestedTask()"><!-- --></A><H3>
getNestedTask</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A> <B>getNestedTask</B>()</PRE>
<DL>
<DD>Convert the nested sequential to an unknown element
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the nested sequential as an unknown element.</DL>
</DD>
</DL>
<HR>

<A NAME="getAttributes()"><!-- --></A><H3>
getAttributes</H3>
<PRE>
public java.util.List <B>getAttributes</B>()</PRE>
<DL>
<DD>Gets this macro's attribute (and define?) list.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the nested Attributes</DL>
</DD>
</DL>
<HR>

<A NAME="getElements()"><!-- --></A><H3>
getElements</H3>
<PRE>
public java.util.Map <B>getElements</B>()</PRE>
<DL>
<DD>Gets this macro's elements.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the map nested elements, keyed by element name, with
         <A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.TemplateElement.html" title="class in org.apache.tools.ant.taskdefs"><CODE>MacroDef.TemplateElement</CODE></A> values.</DL>
</DD>
</DL>
<HR>

<A NAME="isValidNameCharacter(char)"><!-- --></A><H3>
isValidNameCharacter</H3>
<PRE>
public static boolean <B>isValidNameCharacter</B>(char&nbsp;c)</PRE>
<DL>
<DD>Check if a character is a valid character for an element or
 attribute name.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>c</CODE> - the character to check
<DT><B>Returns:</B><DD>true if the character is a letter or digit or '.' or '-'
         attribute name</DL>
</DD>
</DL>
<HR>

<A NAME="addConfiguredAttribute(org.apache.tools.ant.taskdefs.MacroDef.Attribute)"><!-- --></A><H3>
addConfiguredAttribute</H3>
<PRE>
public void <B>addConfiguredAttribute</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.Attribute.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.Attribute</A>&nbsp;attribute)</PRE>
<DL>
<DD>Add an attribute element.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>attribute</CODE> - an attribute nested element.</DL>
</DD>
</DL>
<HR>

<A NAME="addConfiguredElement(org.apache.tools.ant.taskdefs.MacroDef.TemplateElement)"><!-- --></A><H3>
addConfiguredElement</H3>
<PRE>
public void <B>addConfiguredElement</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.TemplateElement.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.TemplateElement</A>&nbsp;element)</PRE>
<DL>
<DD>Add an element element.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>element</CODE> - an element nested element.</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()</PRE>
<DL>
<DD>Create a new ant type based on the embedded tasks and types.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="similar(java.lang.Object)"><!-- --></A><H3>
similar</H3>
<PRE>
public boolean <B>similar</B>(java.lang.Object&nbsp;obj)</PRE>
<DL>
<DD>Similar method for this definition
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>obj</CODE> - another definition
<DT><B>Returns:</B><DD>true if the definitions are similar</DL>
</DD>
</DL>
<HR>

<A NAME="sameDefinition(java.lang.Object)"><!-- --></A><H3>
sameDefinition</H3>
<PRE>
public boolean <B>sameDefinition</B>(java.lang.Object&nbsp;obj)</PRE>
<DL>
<DD>Equality method for this definition
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>obj</CODE> - another definition
<DT><B>Returns:</B><DD>true if the definitions are the same</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/LogStreamHandler.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.Attribute.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/MacroDef.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="MacroDef.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.Task">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
