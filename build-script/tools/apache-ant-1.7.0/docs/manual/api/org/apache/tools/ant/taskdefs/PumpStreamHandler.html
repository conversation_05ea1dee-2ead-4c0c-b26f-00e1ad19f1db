<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:24 EST 2006 -->
<TITLE>
PumpStreamHandler (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.PumpStreamHandler class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="PumpStreamHandler (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Recorder.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/PumpStreamHandler.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="PumpStreamHandler.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class PumpStreamHandler</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.PumpStreamHandler</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A></DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.JUnitLogStreamHandler.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.JUnitLogStreamHandler</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/LogStreamHandler.html" title="class in org.apache.tools.ant.taskdefs">LogStreamHandler</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>PumpStreamHandler</B><DT>extends java.lang.Object<DT>implements <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A></DL>
</PRE>

<P>
Copies standard output and error of subprocesses to standard output and
 error of the parent process.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
</DL>
<HR>

<P>

<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html#PumpStreamHandler()">PumpStreamHandler</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Construct a new <code>PumpStreamHandler</code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html#PumpStreamHandler(java.io.OutputStream)">PumpStreamHandler</A></B>(java.io.OutputStream&nbsp;outAndErr)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Construct a new <code>PumpStreamHandler</code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html#PumpStreamHandler(java.io.OutputStream, java.io.OutputStream)">PumpStreamHandler</A></B>(java.io.OutputStream&nbsp;out,
                  java.io.OutputStream&nbsp;err)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Construct a new <code>PumpStreamHandler</code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html#PumpStreamHandler(java.io.OutputStream, java.io.OutputStream, java.io.InputStream)">PumpStreamHandler</A></B>(java.io.OutputStream&nbsp;out,
                  java.io.OutputStream&nbsp;err,
                  java.io.InputStream&nbsp;input)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Construct a new <code>PumpStreamHandler</code>.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html#createProcessErrorPump(java.io.InputStream, java.io.OutputStream)">createProcessErrorPump</A></B>(java.io.InputStream&nbsp;is,
                       java.io.OutputStream&nbsp;os)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create the pump to handle error output.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html#createProcessOutputPump(java.io.InputStream, java.io.OutputStream)">createProcessOutputPump</A></B>(java.io.InputStream&nbsp;is,
                        java.io.OutputStream&nbsp;os)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create the pump to handle process output.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.Thread</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html#createPump(java.io.InputStream, java.io.OutputStream)">createPump</A></B>(java.io.InputStream&nbsp;is,
           java.io.OutputStream&nbsp;os)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a stream pumper to copy the given input stream to the
 given output stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.Thread</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html#createPump(java.io.InputStream, java.io.OutputStream, boolean)">createPump</A></B>(java.io.InputStream&nbsp;is,
           java.io.OutputStream&nbsp;os,
           boolean&nbsp;closeWhenExhausted)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a stream pumper to copy the given input stream to the
 given output stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.OutputStream</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html#getErr()">getErr</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the error stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.OutputStream</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html#getOut()">getOut</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the output stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html#setProcessErrorStream(java.io.InputStream)">setProcessErrorStream</A></B>(java.io.InputStream&nbsp;is)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the <code>InputStream</code> from which to read the
 standard error of the process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html#setProcessInputStream(java.io.OutputStream)">setProcessInputStream</A></B>(java.io.OutputStream&nbsp;os)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the <code>OutputStream</code> by means of which
 input can be sent to the process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html#setProcessOutputStream(java.io.InputStream)">setProcessOutputStream</A></B>(java.io.InputStream&nbsp;is)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the <code>InputStream</code> from which to read the
 standard output of the process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html#start()">start</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Start the <code>Thread</code>s.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html#stop()">stop</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Stop pumping the streams.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="PumpStreamHandler(java.io.OutputStream, java.io.OutputStream, java.io.InputStream)"><!-- --></A><H3>
PumpStreamHandler</H3>
<PRE>
public <B>PumpStreamHandler</B>(java.io.OutputStream&nbsp;out,
                         java.io.OutputStream&nbsp;err,
                         java.io.InputStream&nbsp;input)</PRE>
<DL>
<DD>Construct a new <code>PumpStreamHandler</code>.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>out</CODE> - the output <code>OutputStream</code>.<DD><CODE>err</CODE> - the error <code>OutputStream</code>.<DD><CODE>input</CODE> - the input <code>InputStream</code>.</DL>
</DL>
<HR>

<A NAME="PumpStreamHandler(java.io.OutputStream, java.io.OutputStream)"><!-- --></A><H3>
PumpStreamHandler</H3>
<PRE>
public <B>PumpStreamHandler</B>(java.io.OutputStream&nbsp;out,
                         java.io.OutputStream&nbsp;err)</PRE>
<DL>
<DD>Construct a new <code>PumpStreamHandler</code>.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>out</CODE> - the output <code>OutputStream</code>.<DD><CODE>err</CODE> - the error <code>OutputStream</code>.</DL>
</DL>
<HR>

<A NAME="PumpStreamHandler(java.io.OutputStream)"><!-- --></A><H3>
PumpStreamHandler</H3>
<PRE>
public <B>PumpStreamHandler</B>(java.io.OutputStream&nbsp;outAndErr)</PRE>
<DL>
<DD>Construct a new <code>PumpStreamHandler</code>.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>outAndErr</CODE> - the output/error <code>OutputStream</code>.</DL>
</DL>
<HR>

<A NAME="PumpStreamHandler()"><!-- --></A><H3>
PumpStreamHandler</H3>
<PRE>
public <B>PumpStreamHandler</B>()</PRE>
<DL>
<DD>Construct a new <code>PumpStreamHandler</code>.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setProcessOutputStream(java.io.InputStream)"><!-- --></A><H3>
setProcessOutputStream</H3>
<PRE>
public void <B>setProcessOutputStream</B>(java.io.InputStream&nbsp;is)</PRE>
<DL>
<DD>Set the <code>InputStream</code> from which to read the
 standard output of the process.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html#setProcessOutputStream(java.io.InputStream)">setProcessOutputStream</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>is</CODE> - the <code>InputStream</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setProcessErrorStream(java.io.InputStream)"><!-- --></A><H3>
setProcessErrorStream</H3>
<PRE>
public void <B>setProcessErrorStream</B>(java.io.InputStream&nbsp;is)</PRE>
<DL>
<DD>Set the <code>InputStream</code> from which to read the
 standard error of the process.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html#setProcessErrorStream(java.io.InputStream)">setProcessErrorStream</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>is</CODE> - the <code>InputStream</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setProcessInputStream(java.io.OutputStream)"><!-- --></A><H3>
setProcessInputStream</H3>
<PRE>
public void <B>setProcessInputStream</B>(java.io.OutputStream&nbsp;os)</PRE>
<DL>
<DD>Set the <code>OutputStream</code> by means of which
 input can be sent to the process.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html#setProcessInputStream(java.io.OutputStream)">setProcessInputStream</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>os</CODE> - the <code>OutputStream</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="start()"><!-- --></A><H3>
start</H3>
<PRE>
public void <B>start</B>()</PRE>
<DL>
<DD>Start the <code>Thread</code>s.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html#start()">start</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A></CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="stop()"><!-- --></A><H3>
stop</H3>
<PRE>
public void <B>stop</B>()</PRE>
<DL>
<DD>Stop pumping the streams.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html#stop()">stop</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A></CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="getErr()"><!-- --></A><H3>
getErr</H3>
<PRE>
protected java.io.OutputStream <B>getErr</B>()</PRE>
<DL>
<DD>Get the error stream.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD><code>OutputStream</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="getOut()"><!-- --></A><H3>
getOut</H3>
<PRE>
protected java.io.OutputStream <B>getOut</B>()</PRE>
<DL>
<DD>Get the output stream.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD><code>OutputStream</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="createProcessOutputPump(java.io.InputStream, java.io.OutputStream)"><!-- --></A><H3>
createProcessOutputPump</H3>
<PRE>
protected void <B>createProcessOutputPump</B>(java.io.InputStream&nbsp;is,
                                       java.io.OutputStream&nbsp;os)</PRE>
<DL>
<DD>Create the pump to handle process output.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>is</CODE> - the <code>InputStream</code>.<DD><CODE>os</CODE> - the <code>OutputStream</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="createProcessErrorPump(java.io.InputStream, java.io.OutputStream)"><!-- --></A><H3>
createProcessErrorPump</H3>
<PRE>
protected void <B>createProcessErrorPump</B>(java.io.InputStream&nbsp;is,
                                      java.io.OutputStream&nbsp;os)</PRE>
<DL>
<DD>Create the pump to handle error output.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>is</CODE> - the input stream to copy from.<DD><CODE>os</CODE> - the output stream to copy to.</DL>
</DD>
</DL>
<HR>

<A NAME="createPump(java.io.InputStream, java.io.OutputStream)"><!-- --></A><H3>
createPump</H3>
<PRE>
protected java.lang.Thread <B>createPump</B>(java.io.InputStream&nbsp;is,
                                      java.io.OutputStream&nbsp;os)</PRE>
<DL>
<DD>Creates a stream pumper to copy the given input stream to the
 given output stream.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>is</CODE> - the input stream to copy from.<DD><CODE>os</CODE> - the output stream to copy to.
<DT><B>Returns:</B><DD>a thread object that does the pumping.</DL>
</DD>
</DL>
<HR>

<A NAME="createPump(java.io.InputStream, java.io.OutputStream, boolean)"><!-- --></A><H3>
createPump</H3>
<PRE>
protected java.lang.Thread <B>createPump</B>(java.io.InputStream&nbsp;is,
                                      java.io.OutputStream&nbsp;os,
                                      boolean&nbsp;closeWhenExhausted)</PRE>
<DL>
<DD>Creates a stream pumper to copy the given input stream to the
 given output stream.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>is</CODE> - the input stream to copy from.<DD><CODE>os</CODE> - the output stream to copy to.<DD><CODE>closeWhenExhausted</CODE> - if true close the inputstream.
<DT><B>Returns:</B><DD>a thread object that does the pumping.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Recorder.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/PumpStreamHandler.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="PumpStreamHandler.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
