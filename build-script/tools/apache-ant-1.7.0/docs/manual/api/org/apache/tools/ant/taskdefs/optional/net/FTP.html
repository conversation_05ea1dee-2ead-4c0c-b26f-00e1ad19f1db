<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:29 EST 2006 -->
<TITLE>
FTP (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.optional.net.FTP class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="FTP (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV CLASS&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.Action.html" title="class in org.apache.tools.ant.taskdefs.optional.net"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/net/FTP.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="FTP.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.optional.net</FONT>
<BR>
Class FTP</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.optional.net.FTP</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>FTP</B><DT>extends <A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></DL>
</PRE>

<P>
Basic FTP client. Performs the following actions:
 <ul>
   <li> <strong>send</strong> - send files to a remote server. This is the
   default action.</li>
   <li> <strong>get</strong> - retrieve files from a remote server.</li>
   <li> <strong>del</strong> - delete files from a remote server.</li>
   <li> <strong>list</strong> - create a file listing.</li>
   <li> <strong>chmod</strong> - change unix file permissions.</li>
   <li> <strong>rmdir</strong> - remove directories, if empty, from a
   remote server.</li>
 </ul>
 <strong>Note:</strong> Some FTP servers - notably the Solaris server - seem
 to hold data ports open after a "retr" operation, allowing them to timeout
 instead of shutting them down cleanly. This happens in active or passive
 mode, and the ports will remain open even after ending the FTP session. FTP
 "send" operations seem to close ports immediately. This behavior may cause
 problems on some systems when downloading large sets of files.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.3</DD>
</DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.Action.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Action</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;an action to perform, one of
 "send", "put", "recv", "get", "del", "delete", "list", "mkdir", "chmod",
 "rmdir"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.FTPDirectoryScanner.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPDirectoryScanner</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;internal class allowing to read the contents of a remote file system
 using the FTP protocol
 used in particular for ftp get operations
 differences with DirectoryScanner
 "" (the root of the fileset) is never included in the included directories
 followSymlinks defaults to false</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.FTPSystemType.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPSystemType</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;one of the valid system type keys recognized by the systemTypeKey
 attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.Granularity.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Granularity</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;represents one of the valid timestamp adjustment values
 recognized by the <code>timestampGranularity</code> attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.LanguageCode.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.LanguageCode</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Enumerated class for languages.</TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#ACTION_STRS">ACTION_STRS</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#ACTION_TARGET_STRS">ACTION_TARGET_STRS</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#CHMOD">CHMOD</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#COMPLETED_ACTION_STRS">COMPLETED_ACTION_STRS</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#DEFAULT_FTP_PORT">DEFAULT_FTP_PORT</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Default port for FTP</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#DEL_FILES">DEL_FILES</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#GET_FILES">GET_FILES</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#LIST_FILES">LIST_FILES</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#MK_DIR">MK_DIR</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#RM_DIR">RM_DIR</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#SEND_FILES">SEND_FILES</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#SITE_CMD">SITE_CMD</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#FTP()">FTP</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;set)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A set of files to upload or download</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#checkAttributes()">checkAttributes</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Checks to see that all required parameters are set.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#createParents(org.apache.commons.net.ftp.FTPClient, java.lang.String)">createParents</A></B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
              java.lang.String&nbsp;filename)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates all parent directories specified in a complete relative
 pathname.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#delFile(org.apache.commons.net.ftp.FTPClient, java.lang.String)">delFile</A></B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
        java.lang.String&nbsp;filename)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Delete a file from the remote host.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#doSiteCommand(org.apache.commons.net.ftp.FTPClient, java.lang.String)">doSiteCommand</A></B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
              java.lang.String&nbsp;theCMD)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sends a site command to the ftp server</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Runs the task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#executeRetryable(org.apache.tools.ant.util.RetryHandler, org.apache.tools.ant.util.Retryable, java.lang.String)">executeRetryable</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/util/RetryHandler.html" title="class in org.apache.tools.ant.util">RetryHandler</A>&nbsp;h,
                 <A HREF="../../../../../../../org/apache/tools/ant/util/Retryable.html" title="interface in org.apache.tools.ant.util">Retryable</A>&nbsp;r,
                 java.lang.String&nbsp;descr)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Executable a retryable object.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#getFile(org.apache.commons.net.ftp.FTPClient, java.lang.String, java.lang.String)">getFile</A></B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
        java.lang.String&nbsp;dir,
        java.lang.String&nbsp;filename)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Retrieve a single file from the remote host.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#isUpToDate(org.apache.commons.net.ftp.FTPClient, java.io.File, java.lang.String)">isUpToDate</A></B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
           java.io.File&nbsp;localFile,
           java.lang.String&nbsp;remoteFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Checks to see if the remote file is current as compared with the local
 file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#listFile(org.apache.commons.net.ftp.FTPClient, java.io.BufferedWriter, java.lang.String)">listFile</A></B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
         java.io.BufferedWriter&nbsp;bw,
         java.lang.String&nbsp;filename)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;List information about a single file from the remote host.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#makeRemoteDir(org.apache.commons.net.ftp.FTPClient, java.lang.String)">makeRemoteDir</A></B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
              java.lang.String&nbsp;dir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create the specified directory on the remote host.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#resolveFile(java.lang.String)">resolveFile</A></B>(java.lang.String&nbsp;file)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Correct a file path to correspond to the remote host requirements.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#rmDir(org.apache.commons.net.ftp.FTPClient, java.lang.String)">rmDir</A></B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
      java.lang.String&nbsp;dirname)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Delete a directory, if empty, from the remote host.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#sendFile(org.apache.commons.net.ftp.FTPClient, java.lang.String, java.lang.String)">sendFile</A></B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
         java.lang.String&nbsp;dir,
         java.lang.String&nbsp;filename)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sends a single file to the remote host.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setAccount(java.lang.String)">setAccount</A></B>(java.lang.String&nbsp;pAccount)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the login account to use on the specified server.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setAction(org.apache.tools.ant.taskdefs.optional.net.FTP.Action)">setAction</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.Action.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Action</A>&nbsp;action)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the FTP action to be taken.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setAction(java.lang.String)">setAction</A></B>(java.lang.String&nbsp;action)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             setAction(String) is deprecated and is replaced with
      setAction(FTP.Action) to make Ant's Introspection mechanism do the
      work and also to encapsulate operations on the type in its own
      class.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setBinary(boolean)">setBinary</A></B>(boolean&nbsp;binary)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, uses binary mode, otherwise text mode (default is binary).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setChmod(java.lang.String)">setChmod</A></B>(java.lang.String&nbsp;theMode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the file permission mode (Unix only) for files sent to the
 server.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setDefaultDateFormatConfig(java.lang.String)">setDefaultDateFormatConfig</A></B>(java.lang.String&nbsp;defaultDateFormat)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the defaultDateFormatConfig attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setDepends(boolean)">setDepends</A></B>(boolean&nbsp;depends)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set to true to transmit only files that are new or changed from their
 remote counterparts.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setIgnoreNoncriticalErrors(boolean)">setIgnoreNoncriticalErrors</A></B>(boolean&nbsp;ignoreNoncriticalErrors)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;set the flag to skip errors on directory creation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setInitialSiteCommand(java.lang.String)">setInitialSiteCommand</A></B>(java.lang.String&nbsp;initialCommand)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the initialSiteCommand attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setListing(java.io.File)">setListing</A></B>(java.io.File&nbsp;listing)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The output file for the "list" action.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setNewer(boolean)">setNewer</A></B>(boolean&nbsp;newer)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A synonym for <tt>depends</tt>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setPassive(boolean)">setPassive</A></B>(boolean&nbsp;passive)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specifies whether to use passive mode.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setPassword(java.lang.String)">setPassword</A></B>(java.lang.String&nbsp;password)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the login password for the given Employee id.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setPort(int)">setPort</A></B>(int&nbsp;port)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the FTP port used by the remote server.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setPreserveLastModified(boolean)">setPreserveLastModified</A></B>(boolean&nbsp;preserveLastModified)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set to true to preserve modification times for "gotten" files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setRecentDateFormatConfig(java.lang.String)">setRecentDateFormatConfig</A></B>(java.lang.String&nbsp;recentDateFormat)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the recentDateFormatConfig attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setRemotedir(java.lang.String)">setRemotedir</A></B>(java.lang.String&nbsp;dir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the remote directory where files will be placed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setRetriesAllowed(java.lang.String)">setRetriesAllowed</A></B>(java.lang.String&nbsp;retriesAllowed)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Defines how many times to retry executing FTP command before giving up.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setSeparator(java.lang.String)">setSeparator</A></B>(java.lang.String&nbsp;separator)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the remote file separator character.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setServer(java.lang.String)">setServer</A></B>(java.lang.String&nbsp;server)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the FTP server to send files to.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setServerLanguageCodeConfig(org.apache.tools.ant.taskdefs.optional.net.FTP.LanguageCode)">setServerLanguageCodeConfig</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.LanguageCode.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.LanguageCode</A>&nbsp;serverLanguageCode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the serverLanguageCode attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setServerTimeZoneConfig(java.lang.String)">setServerTimeZoneConfig</A></B>(java.lang.String&nbsp;serverTimeZoneId)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the serverTimeZoneConfig attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setShortMonthNamesConfig(java.lang.String)">setShortMonthNamesConfig</A></B>(java.lang.String&nbsp;shortMonthNames)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the shortMonthNamesConfig attribute</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setSiteCommand(java.lang.String)">setSiteCommand</A></B>(java.lang.String&nbsp;siteCommand)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the siteCommand attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setSkipFailedTransfers(boolean)">setSkipFailedTransfers</A></B>(boolean&nbsp;skipFailedTransfers)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, enables unsuccessful file put, delete and get
 operations to be skipped with a warning and the remainder
 of the files still transferred.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setSystemTypeKey(org.apache.tools.ant.taskdefs.optional.net.FTP.FTPSystemType)">setSystemTypeKey</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.FTPSystemType.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPSystemType</A>&nbsp;systemKey)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the systemTypeKey attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setTimeDiffAuto(boolean)">setTimeDiffAuto</A></B>(boolean&nbsp;timeDiffAuto)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&quot;true&quot; to find out automatically the time difference
 between local and remote machine.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setTimeDiffMillis(long)">setTimeDiffMillis</A></B>(long&nbsp;timeDiffMillis)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;number of milliseconds to add to the time on the remote machine
 to get the time on the local machine.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setTimestampGranularity(org.apache.tools.ant.taskdefs.optional.net.FTP.Granularity)">setTimestampGranularity</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.Granularity.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Granularity</A>&nbsp;timestampGranularity)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the timestampGranularity attribute</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setUmask(java.lang.String)">setUmask</A></B>(java.lang.String&nbsp;theUmask)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the default mask for file creation on a unix server.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setUserid(java.lang.String)">setUserid</A></B>(java.lang.String&nbsp;userid)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the login Employee id to use on the specified server.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#setVerbose(boolean)">setVerbose</A></B>(boolean&nbsp;verbose)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set to true to receive notification about each file as it is
 transferred.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#transferFiles(org.apache.commons.net.ftp.FTPClient)">transferFiles</A></B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sends all files specified by the configured filesets to the remote
 server.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html#transferFiles(org.apache.commons.net.ftp.FTPClient, org.apache.tools.ant.types.FileSet)">transferFiles</A></B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
              <A HREF="../../../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;fs)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;For each file in the fileset, do the appropriate action: send, get,
 delete, or list.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="SEND_FILES"><!-- --></A><H3>
SEND_FILES</H3>
<PRE>
protected static final int <B>SEND_FILES</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.SEND_FILES">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="GET_FILES"><!-- --></A><H3>
GET_FILES</H3>
<PRE>
protected static final int <B>GET_FILES</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.GET_FILES">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEL_FILES"><!-- --></A><H3>
DEL_FILES</H3>
<PRE>
protected static final int <B>DEL_FILES</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.DEL_FILES">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LIST_FILES"><!-- --></A><H3>
LIST_FILES</H3>
<PRE>
protected static final int <B>LIST_FILES</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.LIST_FILES">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="MK_DIR"><!-- --></A><H3>
MK_DIR</H3>
<PRE>
protected static final int <B>MK_DIR</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.MK_DIR">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CHMOD"><!-- --></A><H3>
CHMOD</H3>
<PRE>
protected static final int <B>CHMOD</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.CHMOD">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="RM_DIR"><!-- --></A><H3>
RM_DIR</H3>
<PRE>
protected static final int <B>RM_DIR</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.RM_DIR">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="SITE_CMD"><!-- --></A><H3>
SITE_CMD</H3>
<PRE>
protected static final int <B>SITE_CMD</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.SITE_CMD">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_FTP_PORT"><!-- --></A><H3>
DEFAULT_FTP_PORT</H3>
<PRE>
public static final int <B>DEFAULT_FTP_PORT</B></PRE>
<DL>
<DD>Default port for FTP
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.optional.net.FTP.DEFAULT_FTP_PORT">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ACTION_STRS"><!-- --></A><H3>
ACTION_STRS</H3>
<PRE>
protected static final java.lang.String[] <B>ACTION_STRS</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="COMPLETED_ACTION_STRS"><!-- --></A><H3>
COMPLETED_ACTION_STRS</H3>
<PRE>
protected static final java.lang.String[] <B>COMPLETED_ACTION_STRS</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="ACTION_TARGET_STRS"><!-- --></A><H3>
ACTION_TARGET_STRS</H3>
<PRE>
protected static final java.lang.String[] <B>ACTION_TARGET_STRS</B></PRE>
<DL>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="FTP()"><!-- --></A><H3>
FTP</H3>
<PRE>
public <B>FTP</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setRemotedir(java.lang.String)"><!-- --></A><H3>
setRemotedir</H3>
<PRE>
public void <B>setRemotedir</B>(java.lang.String&nbsp;dir)</PRE>
<DL>
<DD>Sets the remote directory where files will be placed. This may be a
 relative or absolute path, and must be in the path syntax expected by
 the remote server. No correction of path syntax will be performed.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>dir</CODE> - the remote directory name.</DL>
</DD>
</DL>
<HR>

<A NAME="setServer(java.lang.String)"><!-- --></A><H3>
setServer</H3>
<PRE>
public void <B>setServer</B>(java.lang.String&nbsp;server)</PRE>
<DL>
<DD>Sets the FTP server to send files to.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>server</CODE> - the remote server name.</DL>
</DD>
</DL>
<HR>

<A NAME="setPort(int)"><!-- --></A><H3>
setPort</H3>
<PRE>
public void <B>setPort</B>(int&nbsp;port)</PRE>
<DL>
<DD>Sets the FTP port used by the remote server.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>port</CODE> - the port on which the remote server is listening.</DL>
</DD>
</DL>
<HR>

<A NAME="setUserid(java.lang.String)"><!-- --></A><H3>
setUserid</H3>
<PRE>
public void <B>setUserid</B>(java.lang.String&nbsp;userid)</PRE>
<DL>
<DD>Sets the login Employee id to use on the specified server.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>userid</CODE> - remote system userid.</DL>
</DD>
</DL>
<HR>

<A NAME="setPassword(java.lang.String)"><!-- --></A><H3>
setPassword</H3>
<PRE>
public void <B>setPassword</B>(java.lang.String&nbsp;password)</PRE>
<DL>
<DD>Sets the login password for the given Employee id.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>password</CODE> - the password on the remote system.</DL>
</DD>
</DL>
<HR>

<A NAME="setAccount(java.lang.String)"><!-- --></A><H3>
setAccount</H3>
<PRE>
public void <B>setAccount</B>(java.lang.String&nbsp;pAccount)</PRE>
<DL>
<DD>Sets the login account to use on the specified server.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pAccount</CODE> - the account name on remote system<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setBinary(boolean)"><!-- --></A><H3>
setBinary</H3>
<PRE>
public void <B>setBinary</B>(boolean&nbsp;binary)</PRE>
<DL>
<DD>If true, uses binary mode, otherwise text mode (default is binary).
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>binary</CODE> - if true use binary mode in transfers.</DL>
</DD>
</DL>
<HR>

<A NAME="setPassive(boolean)"><!-- --></A><H3>
setPassive</H3>
<PRE>
public void <B>setPassive</B>(boolean&nbsp;passive)</PRE>
<DL>
<DD>Specifies whether to use passive mode. Set to true if you are behind a
 firewall and cannot connect without it. Passive mode is disabled by
 default.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>passive</CODE> - true is passive mode should be used.</DL>
</DD>
</DL>
<HR>

<A NAME="setVerbose(boolean)"><!-- --></A><H3>
setVerbose</H3>
<PRE>
public void <B>setVerbose</B>(boolean&nbsp;verbose)</PRE>
<DL>
<DD>Set to true to receive notification about each file as it is
 transferred.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>verbose</CODE> - true if verbose notifications are required.</DL>
</DD>
</DL>
<HR>

<A NAME="setNewer(boolean)"><!-- --></A><H3>
setNewer</H3>
<PRE>
public void <B>setNewer</B>(boolean&nbsp;newer)</PRE>
<DL>
<DD>A synonym for <tt>depends</tt>. Set to true to transmit only new
 or changed files.

 See the related attributes timediffmillis and timediffauto.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>newer</CODE> - if true only transfer newer files.</DL>
</DD>
</DL>
<HR>

<A NAME="setTimeDiffMillis(long)"><!-- --></A><H3>
setTimeDiffMillis</H3>
<PRE>
public void <B>setTimeDiffMillis</B>(long&nbsp;timeDiffMillis)</PRE>
<DL>
<DD>number of milliseconds to add to the time on the remote machine
 to get the time on the local machine.

 use in conjunction with <code>newer</code>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>timeDiffMillis</CODE> - number of milliseconds<DT><B>Since:</B></DT>
  <DD>ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setTimeDiffAuto(boolean)"><!-- --></A><H3>
setTimeDiffAuto</H3>
<PRE>
public void <B>setTimeDiffAuto</B>(boolean&nbsp;timeDiffAuto)</PRE>
<DL>
<DD>&quot;true&quot; to find out automatically the time difference
 between local and remote machine.

 This requires right to create
 and delete a temporary file in the remote directory.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>timeDiffAuto</CODE> - true = find automatically the time diff<DT><B>Since:</B></DT>
  <DD>ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setPreserveLastModified(boolean)"><!-- --></A><H3>
setPreserveLastModified</H3>
<PRE>
public void <B>setPreserveLastModified</B>(boolean&nbsp;preserveLastModified)</PRE>
<DL>
<DD>Set to true to preserve modification times for "gotten" files.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>preserveLastModified</CODE> - if true preserver modification times.</DL>
</DD>
</DL>
<HR>

<A NAME="setDepends(boolean)"><!-- --></A><H3>
setDepends</H3>
<PRE>
public void <B>setDepends</B>(boolean&nbsp;depends)</PRE>
<DL>
<DD>Set to true to transmit only files that are new or changed from their
 remote counterparts. The default is to transmit all files.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>depends</CODE> - if true only transfer newer files.</DL>
</DD>
</DL>
<HR>

<A NAME="setSeparator(java.lang.String)"><!-- --></A><H3>
setSeparator</H3>
<PRE>
public void <B>setSeparator</B>(java.lang.String&nbsp;separator)</PRE>
<DL>
<DD>Sets the remote file separator character. This normally defaults to the
 Unix standard forward slash, but can be manually overridden using this
 call if the remote server requires some other separator. Only the first
 character of the string is used.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>separator</CODE> - the file separator on the remote system.</DL>
</DD>
</DL>
<HR>

<A NAME="setChmod(java.lang.String)"><!-- --></A><H3>
setChmod</H3>
<PRE>
public void <B>setChmod</B>(java.lang.String&nbsp;theMode)</PRE>
<DL>
<DD>Sets the file permission mode (Unix only) for files sent to the
 server.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>theMode</CODE> - unix style file mode for the files sent to the remote
        system.</DL>
</DD>
</DL>
<HR>

<A NAME="setUmask(java.lang.String)"><!-- --></A><H3>
setUmask</H3>
<PRE>
public void <B>setUmask</B>(java.lang.String&nbsp;theUmask)</PRE>
<DL>
<DD>Sets the default mask for file creation on a unix server.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>theUmask</CODE> - unix style umask for files created on the remote server.</DL>
</DD>
</DL>
<HR>

<A NAME="addFileset(org.apache.tools.ant.types.FileSet)"><!-- --></A><H3>
addFileset</H3>
<PRE>
public void <B>addFileset</B>(<A HREF="../../../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;set)</PRE>
<DL>
<DD>A set of files to upload or download
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>set</CODE> - the set of files to be added to the list of files to be
        transferred.</DL>
</DD>
</DL>
<HR>

<A NAME="setAction(java.lang.String)"><!-- --></A><H3>
setAction</H3>
<PRE>
public void <B>setAction</B>(java.lang.String&nbsp;action)
               throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             setAction(String) is deprecated and is replaced with
      setAction(FTP.Action) to make Ant's Introspection mechanism do the
      work and also to encapsulate operations on the type in its own
      class.</I>
<P>
<DD>Sets the FTP action to be taken. Currently accepts "put", "get", "del",
 "mkdir", "chmod", "list", and "site".
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>action</CODE> - the FTP action to be performed.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the action is not a valid action.</DL>
</DD>
</DL>
<HR>

<A NAME="setAction(org.apache.tools.ant.taskdefs.optional.net.FTP.Action)"><!-- --></A><H3>
setAction</H3>
<PRE>
public void <B>setAction</B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.Action.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Action</A>&nbsp;action)
               throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Sets the FTP action to be taken. Currently accepts "put", "get", "del",
 "mkdir", "chmod", "list", and "site".
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>action</CODE> - the FTP action to be performed.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the action is not a valid action.</DL>
</DD>
</DL>
<HR>

<A NAME="setListing(java.io.File)"><!-- --></A><H3>
setListing</H3>
<PRE>
public void <B>setListing</B>(java.io.File&nbsp;listing)</PRE>
<DL>
<DD>The output file for the "list" action. This attribute is ignored for
 any other actions.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>listing</CODE> - file in which to store the listing.</DL>
</DD>
</DL>
<HR>

<A NAME="setSkipFailedTransfers(boolean)"><!-- --></A><H3>
setSkipFailedTransfers</H3>
<PRE>
public void <B>setSkipFailedTransfers</B>(boolean&nbsp;skipFailedTransfers)</PRE>
<DL>
<DD>If true, enables unsuccessful file put, delete and get
 operations to be skipped with a warning and the remainder
 of the files still transferred.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>skipFailedTransfers</CODE> - true if failures in transfers are ignored.</DL>
</DD>
</DL>
<HR>

<A NAME="setIgnoreNoncriticalErrors(boolean)"><!-- --></A><H3>
setIgnoreNoncriticalErrors</H3>
<PRE>
public void <B>setIgnoreNoncriticalErrors</B>(boolean&nbsp;ignoreNoncriticalErrors)</PRE>
<DL>
<DD>set the flag to skip errors on directory creation.
 (and maybe later other server specific errors)
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ignoreNoncriticalErrors</CODE> - true if non-critical errors should not
        cause a failure.</DL>
</DD>
</DL>
<HR>

<A NAME="setSystemTypeKey(org.apache.tools.ant.taskdefs.optional.net.FTP.FTPSystemType)"><!-- --></A><H3>
setSystemTypeKey</H3>
<PRE>
public void <B>setSystemTypeKey</B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.FTPSystemType.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPSystemType</A>&nbsp;systemKey)</PRE>
<DL>
<DD>Sets the systemTypeKey attribute.
 Method for setting <code>FTPClientConfig</code> remote system key.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>systemKey</CODE> - the key to be set - BUT if blank
 the default value of null (which signifies "autodetect") will be kept.<DT><B>See Also:</B><DD><CODE>FTPClientConfig</CODE></DL>
</DD>
</DL>
<HR>

<A NAME="setDefaultDateFormatConfig(java.lang.String)"><!-- --></A><H3>
setDefaultDateFormatConfig</H3>
<PRE>
public void <B>setDefaultDateFormatConfig</B>(java.lang.String&nbsp;defaultDateFormat)</PRE>
<DL>
<DD>Sets the defaultDateFormatConfig attribute.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>defaultDateFormat</CODE> - configuration to be set, unless it is
 null or empty string, in which case ignored.<DT><B>See Also:</B><DD><CODE>FTPClientConfig</CODE></DL>
</DD>
</DL>
<HR>

<A NAME="setRecentDateFormatConfig(java.lang.String)"><!-- --></A><H3>
setRecentDateFormatConfig</H3>
<PRE>
public void <B>setRecentDateFormatConfig</B>(java.lang.String&nbsp;recentDateFormat)</PRE>
<DL>
<DD>Sets the recentDateFormatConfig attribute.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>recentDateFormat</CODE> - configuration to be set, unless it is
 null or empty string, in which case ignored.<DT><B>See Also:</B><DD><CODE>FTPClientConfig</CODE></DL>
</DD>
</DL>
<HR>

<A NAME="setServerLanguageCodeConfig(org.apache.tools.ant.taskdefs.optional.net.FTP.LanguageCode)"><!-- --></A><H3>
setServerLanguageCodeConfig</H3>
<PRE>
public void <B>setServerLanguageCodeConfig</B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.LanguageCode.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.LanguageCode</A>&nbsp;serverLanguageCode)</PRE>
<DL>
<DD>Sets the serverLanguageCode attribute.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>serverLanguageCode</CODE> - configuration to be set, unless it is
 null or empty string, in which case ignored.<DT><B>See Also:</B><DD><CODE>FTPClientConfig</CODE></DL>
</DD>
</DL>
<HR>

<A NAME="setServerTimeZoneConfig(java.lang.String)"><!-- --></A><H3>
setServerTimeZoneConfig</H3>
<PRE>
public void <B>setServerTimeZoneConfig</B>(java.lang.String&nbsp;serverTimeZoneId)</PRE>
<DL>
<DD>Sets the serverTimeZoneConfig attribute.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>serverTimeZoneId</CODE> - configuration to be set, unless it is
 null or empty string, in which case ignored.<DT><B>See Also:</B><DD><CODE>FTPClientConfig</CODE></DL>
</DD>
</DL>
<HR>

<A NAME="setShortMonthNamesConfig(java.lang.String)"><!-- --></A><H3>
setShortMonthNamesConfig</H3>
<PRE>
public void <B>setShortMonthNamesConfig</B>(java.lang.String&nbsp;shortMonthNames)</PRE>
<DL>
<DD>Sets the shortMonthNamesConfig attribute
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>shortMonthNames</CODE> - configuration to be set, unless it is
 null or empty string, in which case ignored.<DT><B>See Also:</B><DD><CODE>FTPClientConfig</CODE></DL>
</DD>
</DL>
<HR>

<A NAME="setRetriesAllowed(java.lang.String)"><!-- --></A><H3>
setRetriesAllowed</H3>
<PRE>
public void <B>setRetriesAllowed</B>(java.lang.String&nbsp;retriesAllowed)</PRE>
<DL>
<DD>Defines how many times to retry executing FTP command before giving up.
 Default is 0 - try once and if failure then give up.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>retriesAllowed</CODE> - number of retries to allow.  -1 means
 keep trying forever. "forever" may also be specified as a
 synonym for -1.</DL>
</DD>
</DL>
<HR>

<A NAME="setTimestampGranularity(org.apache.tools.ant.taskdefs.optional.net.FTP.Granularity)"><!-- --></A><H3>
setTimestampGranularity</H3>
<PRE>
public void <B>setTimestampGranularity</B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.Granularity.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.Granularity</A>&nbsp;timestampGranularity)</PRE>
<DL>
<DD>Sets the timestampGranularity attribute
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>timestampGranularity</CODE> - The timestampGranularity to set.</DL>
</DD>
</DL>
<HR>

<A NAME="setSiteCommand(java.lang.String)"><!-- --></A><H3>
setSiteCommand</H3>
<PRE>
public void <B>setSiteCommand</B>(java.lang.String&nbsp;siteCommand)</PRE>
<DL>
<DD>Sets the siteCommand attribute.  This attribute
 names the command that will be executed if the action
 is "site".
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>siteCommand</CODE> - The siteCommand to set.</DL>
</DD>
</DL>
<HR>

<A NAME="setInitialSiteCommand(java.lang.String)"><!-- --></A><H3>
setInitialSiteCommand</H3>
<PRE>
public void <B>setInitialSiteCommand</B>(java.lang.String&nbsp;initialCommand)</PRE>
<DL>
<DD>Sets the initialSiteCommand attribute.  This attribute
 names a site command that will be executed immediately
 after connection.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>initialCommand</CODE> - The initialSiteCommand to set.</DL>
</DD>
</DL>
<HR>

<A NAME="checkAttributes()"><!-- --></A><H3>
checkAttributes</H3>
<PRE>
protected void <B>checkAttributes</B>()
                        throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Checks to see that all required parameters are set.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the configuration is not valid.</DL>
</DD>
</DL>
<HR>

<A NAME="executeRetryable(org.apache.tools.ant.util.RetryHandler, org.apache.tools.ant.util.Retryable, java.lang.String)"><!-- --></A><H3>
executeRetryable</H3>
<PRE>
protected void <B>executeRetryable</B>(<A HREF="../../../../../../../org/apache/tools/ant/util/RetryHandler.html" title="class in org.apache.tools.ant.util">RetryHandler</A>&nbsp;h,
                                <A HREF="../../../../../../../org/apache/tools/ant/util/Retryable.html" title="interface in org.apache.tools.ant.util">Retryable</A>&nbsp;r,
                                java.lang.String&nbsp;descr)
                         throws java.io.IOException</PRE>
<DL>
<DD>Executable a retryable object.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>h</CODE> - the retry hander.<DD><CODE>r</CODE> - the object that should be retried until it succeeds
          or the number of retrys is reached.<DD><CODE>descr</CODE> - a description of the command that is being run.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there is a problem.</DL>
</DD>
</DL>
<HR>

<A NAME="transferFiles(org.apache.commons.net.ftp.FTPClient, org.apache.tools.ant.types.FileSet)"><!-- --></A><H3>
transferFiles</H3>
<PRE>
protected int <B>transferFiles</B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
                            <A HREF="../../../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;fs)
                     throws java.io.IOException,
                            <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>For each file in the fileset, do the appropriate action: send, get,
 delete, or list.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ftp</CODE> - the FTPClient instance used to perform FTP actions<DD><CODE>fs</CODE> - the fileset on which the actions are performed.
<DT><B>Returns:</B><DD>the number of files to be transferred.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there is a problem reading a file
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if there is a problem in the configuration.</DL>
</DD>
</DL>
<HR>

<A NAME="transferFiles(org.apache.commons.net.ftp.FTPClient)"><!-- --></A><H3>
transferFiles</H3>
<PRE>
protected void <B>transferFiles</B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp)
                      throws java.io.IOException,
                             <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Sends all files specified by the configured filesets to the remote
 server.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ftp</CODE> - the FTPClient instance used to perform FTP actions
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there is a problem reading a file
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if there is a problem in the configuration.</DL>
</DD>
</DL>
<HR>

<A NAME="resolveFile(java.lang.String)"><!-- --></A><H3>
resolveFile</H3>
<PRE>
protected java.lang.String <B>resolveFile</B>(java.lang.String&nbsp;file)</PRE>
<DL>
<DD>Correct a file path to correspond to the remote host requirements. This
 implementation currently assumes that the remote end can handle
 Unix-style paths with forward-slash separators. This can be overridden
 with the <code>separator</code> task parameter. No attempt is made to
 determine what syntax is appropriate for the remote host.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>file</CODE> - the remote file name to be resolved
<DT><B>Returns:</B><DD>the filename as it will appear on the server.</DL>
</DD>
</DL>
<HR>

<A NAME="createParents(org.apache.commons.net.ftp.FTPClient, java.lang.String)"><!-- --></A><H3>
createParents</H3>
<PRE>
protected void <B>createParents</B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
                             java.lang.String&nbsp;filename)
                      throws java.io.IOException,
                             <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Creates all parent directories specified in a complete relative
 pathname. Attempts to create existing directories will not cause
 errors.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ftp</CODE> - the FTP client instance to use to execute FTP actions on
        the remote server.<DD><CODE>filename</CODE> - the name of the file whose parents should be created.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - under non documented circumstances
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if it is impossible to cd to a remote directory</DL>
</DD>
</DL>
<HR>

<A NAME="isUpToDate(org.apache.commons.net.ftp.FTPClient, java.io.File, java.lang.String)"><!-- --></A><H3>
isUpToDate</H3>
<PRE>
protected boolean <B>isUpToDate</B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
                             java.io.File&nbsp;localFile,
                             java.lang.String&nbsp;remoteFile)
                      throws java.io.IOException,
                             <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Checks to see if the remote file is current as compared with the local
 file. Returns true if the target file is up to date.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ftp</CODE> - ftpclient<DD><CODE>localFile</CODE> - local file<DD><CODE>remoteFile</CODE> - remote file
<DT><B>Returns:</B><DD>true if the target file is up to date
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - in unknown circumstances
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the date of the remote files cannot be found and the action is
 GET_FILES</DL>
</DD>
</DL>
<HR>

<A NAME="doSiteCommand(org.apache.commons.net.ftp.FTPClient, java.lang.String)"><!-- --></A><H3>
doSiteCommand</H3>
<PRE>
protected void <B>doSiteCommand</B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
                             java.lang.String&nbsp;theCMD)
                      throws java.io.IOException,
                             <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Sends a site command to the ftp server
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ftp</CODE> - ftp client<DD><CODE>theCMD</CODE> - command to execute
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - in unknown circumstances
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - in unknown circumstances</DL>
</DD>
</DL>
<HR>

<A NAME="sendFile(org.apache.commons.net.ftp.FTPClient, java.lang.String, java.lang.String)"><!-- --></A><H3>
sendFile</H3>
<PRE>
protected void <B>sendFile</B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
                        java.lang.String&nbsp;dir,
                        java.lang.String&nbsp;filename)
                 throws java.io.IOException,
                        <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Sends a single file to the remote host. <code>filename</code> may
 contain a relative path specification. When this is the case, <code>sendFile</code>
 will attempt to create any necessary parent directories before sending
 the file. The file will then be sent using the entire relative path
 spec - no attempt is made to change directories. It is anticipated that
 this may eventually cause problems with some FTP servers, but it
 simplifies the coding.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ftp</CODE> - ftp client<DD><CODE>dir</CODE> - base directory of the file to be sent (local)<DD><CODE>filename</CODE> - relative path of the file to be send
        locally relative to dir
        remotely relative to the remotedir attribute
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - in unknown circumstances
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - in unknown circumstances</DL>
</DD>
</DL>
<HR>

<A NAME="delFile(org.apache.commons.net.ftp.FTPClient, java.lang.String)"><!-- --></A><H3>
delFile</H3>
<PRE>
protected void <B>delFile</B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
                       java.lang.String&nbsp;filename)
                throws java.io.IOException,
                       <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Delete a file from the remote host.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ftp</CODE> - ftp client<DD><CODE>filename</CODE> - file to delete
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - in unknown circumstances
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if skipFailedTransfers is set to false
 and the deletion could not be done</DL>
</DD>
</DL>
<HR>

<A NAME="rmDir(org.apache.commons.net.ftp.FTPClient, java.lang.String)"><!-- --></A><H3>
rmDir</H3>
<PRE>
protected void <B>rmDir</B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
                     java.lang.String&nbsp;dirname)
              throws java.io.IOException,
                     <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Delete a directory, if empty, from the remote host.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ftp</CODE> - ftp client<DD><CODE>dirname</CODE> - directory to delete
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - in unknown circumstances
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if skipFailedTransfers is set to false
 and the deletion could not be done</DL>
</DD>
</DL>
<HR>

<A NAME="getFile(org.apache.commons.net.ftp.FTPClient, java.lang.String, java.lang.String)"><!-- --></A><H3>
getFile</H3>
<PRE>
protected void <B>getFile</B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
                       java.lang.String&nbsp;dir,
                       java.lang.String&nbsp;filename)
                throws java.io.IOException,
                       <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Retrieve a single file from the remote host. <code>filename</code> may
 contain a relative path specification. <p>

 The file will then be retreived using the entire relative path spec -
 no attempt is made to change directories. It is anticipated that this
 may eventually cause problems with some FTP servers, but it simplifies
 the coding.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ftp</CODE> - the ftp client<DD><CODE>dir</CODE> - local base directory to which the file should go back<DD><CODE>filename</CODE> - relative path of the file based upon the ftp remote directory
        and/or the local base directory (dir)
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - in unknown circumstances
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if skipFailedTransfers is false
 and the file cannot be retrieved.</DL>
</DD>
</DL>
<HR>

<A NAME="listFile(org.apache.commons.net.ftp.FTPClient, java.io.BufferedWriter, java.lang.String)"><!-- --></A><H3>
listFile</H3>
<PRE>
protected void <B>listFile</B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
                        java.io.BufferedWriter&nbsp;bw,
                        java.lang.String&nbsp;filename)
                 throws java.io.IOException,
                        <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>List information about a single file from the remote host. <code>filename</code>
 may contain a relative path specification. <p>

 The file listing will then be retrieved using the entire relative path
 spec - no attempt is made to change directories. It is anticipated that
 this may eventually cause problems with some FTP servers, but it
 simplifies the coding.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ftp</CODE> - ftp client<DD><CODE>bw</CODE> - buffered writer<DD><CODE>filename</CODE> - the directory one wants to list
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - in unknown circumstances
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - in unknown circumstances</DL>
</DD>
</DL>
<HR>

<A NAME="makeRemoteDir(org.apache.commons.net.ftp.FTPClient, java.lang.String)"><!-- --></A><H3>
makeRemoteDir</H3>
<PRE>
protected void <B>makeRemoteDir</B>(org.apache.commons.net.ftp.FTPClient&nbsp;ftp,
                             java.lang.String&nbsp;dir)
                      throws java.io.IOException,
                             <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Create the specified directory on the remote host.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ftp</CODE> - The FTP client connection<DD><CODE>dir</CODE> - The directory to create (format must be correct for host
      type)
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - in unknown circumstances
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if ignoreNoncriticalErrors has not been set to true
         and a directory could not be created, for instance because it was
         already existing. Precisely, the codes 521, 550 and 553 will trigger
         a BuildException</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Runs the task.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the task fails or is not configured
         correctly.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV CLASS&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.Action.html" title="class in org.apache.tools.ant.taskdefs.optional.net"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/net/FTP.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="FTP.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
