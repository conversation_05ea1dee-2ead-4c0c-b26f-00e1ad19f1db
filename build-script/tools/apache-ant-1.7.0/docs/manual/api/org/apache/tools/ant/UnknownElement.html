<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:20 EST 2006 -->
<TITLE>
UnknownElement (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.UnknownElement class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="UnknownElement (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/TypeAdapter.html" title="interface in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/UnsupportedAttributeException.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/UnknownElement.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="UnknownElement.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.Task">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class UnknownElement</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.UnknownElement</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>UnknownElement</B><DT>extends <A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></DL>
</PRE>

<P>
Wrapper class that holds all the information necessary to create a task
 or data type that did not exist when Ant started, or one which
 has had its definition updated to use a different implementation class.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#UnknownElement(java.lang.String)">UnknownElement</A></B>(java.lang.String&nbsp;elementName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates an UnknownElement for the given element name.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#addChild(org.apache.tools.ant.UnknownElement)">addChild</A></B>(<A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A>&nbsp;child)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a child element to this element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#applyPreSet(org.apache.tools.ant.UnknownElement)">applyPreSet</A></B>(<A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A>&nbsp;u)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This is used then the realobject of the UE is a PreSetDefinition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#configure(java.lang.Object)">configure</A></B>(java.lang.Object&nbsp;realObject)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Configure the given object from this UnknownElement</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#copy(org.apache.tools.ant.Project)">copy</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;newProject)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Make a copy of the unknown element and set it in the new project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Executes the real object if it's a task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.List</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#getChildren()">getChildren</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#getComponentName()">getComponentName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#getNamespace()">getNamespace</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the namespace of the XML element associated with this component.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#getNotFoundException(java.lang.String, java.lang.String)">getNotFoundException</A></B>(java.lang.String&nbsp;what,
                     java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns a very verbose exception for when a task/data type cannot
 be found.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#getQName()">getQName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the qname of the XML element associated with this component.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#getRealThing()">getRealThing</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the configured object</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#getTag()">getTag</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the name of the XML element which generated this unknown
 element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#getTask()">getTask</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the task instance after it has been created and if it is a task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#getTaskName()">getTaskName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the name to use in logging messages.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#getWrapper()">getWrapper</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the RuntimeConfigurable instance for this UnknownElement, containing
 the configuration information.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#handleChildren(java.lang.Object, org.apache.tools.ant.RuntimeConfigurable)">handleChildren</A></B>(java.lang.Object&nbsp;parent,
               <A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A>&nbsp;parentWrapper)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates child elements, creates children of the children
 (recursively), and sets attributes of the child elements.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A></B>(java.lang.String&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Handles error output sent to System.err by this task or its real task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A></B>(java.lang.String&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Handles error output sent to System.err by this task or its real task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#handleFlush(java.lang.String)">handleFlush</A></B>(java.lang.String&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Handles output sent to System.out by this task or its real task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#handleInput(byte[], int, int)">handleInput</A></B>(byte[]&nbsp;buffer,
            int&nbsp;offset,
            int&nbsp;length)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Delegate to realThing if present and if it as task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#handleOutput(java.lang.String)">handleOutput</A></B>(java.lang.String&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Handles output sent to System.out by this task or its real task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#makeObject(org.apache.tools.ant.UnknownElement, org.apache.tools.ant.RuntimeConfigurable)">makeObject</A></B>(<A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A>&nbsp;ue,
           <A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A>&nbsp;w)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a named task or data type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#makeTask(org.apache.tools.ant.UnknownElement, org.apache.tools.ant.RuntimeConfigurable)">makeTask</A></B>(<A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A>&nbsp;ue,
         <A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A>&nbsp;w)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a named task and configures it up to the init() stage.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#maybeConfigure()">maybeConfigure</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates the real object instance and child elements, then configures
 the attributes and text of the real object.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#setNamespace(java.lang.String)">setNamespace</A></B>(java.lang.String&nbsp;namespace)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the namespace of the XML element associated with this component.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#setQName(java.lang.String)">setQName</A></B>(java.lang.String&nbsp;qname)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the namespace qname of the XML element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#setRealThing(java.lang.Object)">setRealThing</A></B>(java.lang.Object&nbsp;realThing)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the configured object</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html#similar(java.lang.Object)">similar</A></B>(java.lang.Object&nbsp;obj)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;like contents equals, but ignores project</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="UnknownElement(java.lang.String)"><!-- --></A><H3>
UnknownElement</H3>
<PRE>
public <B>UnknownElement</B>(java.lang.String&nbsp;elementName)</PRE>
<DL>
<DD>Creates an UnknownElement for the given element name.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>elementName</CODE> - The name of the unknown element.
                    Must not be <code>null</code>.</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="getChildren()"><!-- --></A><H3>
getChildren</H3>
<PRE>
public java.util.List <B>getChildren</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the list of nested UnknownElements for this UnknownElement.</DL>
</DD>
</DL>
<HR>

<A NAME="getTag()"><!-- --></A><H3>
getTag</H3>
<PRE>
public java.lang.String <B>getTag</B>()</PRE>
<DL>
<DD>Returns the name of the XML element which generated this unknown
 element.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the name of the XML element which generated this unknown
         element.</DL>
</DD>
</DL>
<HR>

<A NAME="getNamespace()"><!-- --></A><H3>
getNamespace</H3>
<PRE>
public java.lang.String <B>getNamespace</B>()</PRE>
<DL>
<DD>Return the namespace of the XML element associated with this component.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>Namespace URI used in the xmlns declaration.</DL>
</DD>
</DL>
<HR>

<A NAME="setNamespace(java.lang.String)"><!-- --></A><H3>
setNamespace</H3>
<PRE>
public void <B>setNamespace</B>(java.lang.String&nbsp;namespace)</PRE>
<DL>
<DD>Set the namespace of the XML element associated with this component.
 This method is typically called by the XML processor.
 If the namespace is "ant:current", the component helper
 is used to get the current antlib uri.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>namespace</CODE> - URI used in the xmlns declaration.</DL>
</DD>
</DL>
<HR>

<A NAME="getQName()"><!-- --></A><H3>
getQName</H3>
<PRE>
public java.lang.String <B>getQName</B>()</PRE>
<DL>
<DD>Return the qname of the XML element associated with this component.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>namespace Qname used in the element declaration.</DL>
</DD>
</DL>
<HR>

<A NAME="setQName(java.lang.String)"><!-- --></A><H3>
setQName</H3>
<PRE>
public void <B>setQName</B>(java.lang.String&nbsp;qname)</PRE>
<DL>
<DD>Set the namespace qname of the XML element.
 This method is typically called by the XML processor.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>qname</CODE> - the qualified name of the element</DL>
</DD>
</DL>
<HR>

<A NAME="getWrapper()"><!-- --></A><H3>
getWrapper</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A> <B>getWrapper</B>()</PRE>
<DL>
<DD>Get the RuntimeConfigurable instance for this UnknownElement, containing
 the configuration information.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A></CODE> in class <CODE><A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the configuration info.</DL>
</DD>
</DL>
<HR>

<A NAME="maybeConfigure()"><!-- --></A><H3>
maybeConfigure</H3>
<PRE>
public void <B>maybeConfigure</B>()
                    throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Creates the real object instance and child elements, then configures
 the attributes and text of the real object. This unknown element
 is then replaced with the real object in the containing target's list
 of children.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A></CODE> in class <CODE><A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the configuration fails</DL>
</DD>
</DL>
<HR>

<A NAME="configure(java.lang.Object)"><!-- --></A><H3>
configure</H3>
<PRE>
public void <B>configure</B>(java.lang.Object&nbsp;realObject)</PRE>
<DL>
<DD>Configure the given object from this UnknownElement
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>realObject</CODE> - the real object this UnknownElement is representing.</DL>
</DD>
</DL>
<HR>

<A NAME="handleOutput(java.lang.String)"><!-- --></A><H3>
handleOutput</H3>
<PRE>
protected void <B>handleOutput</B>(java.lang.String&nbsp;output)</PRE>
<DL>
<DD>Handles output sent to System.out by this task or its real task.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A></CODE> in class <CODE><A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - The output to log. Should not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="handleInput(byte[], int, int)"><!-- --></A><H3>
handleInput</H3>
<PRE>
protected int <B>handleInput</B>(byte[]&nbsp;buffer,
                          int&nbsp;offset,
                          int&nbsp;length)
                   throws java.io.IOException</PRE>
<DL>
<DD>Delegate to realThing if present and if it as task.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A></CODE> in class <CODE><A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>buffer</CODE> - the buffer into which data is to be read.<DD><CODE>offset</CODE> - the offset into the buffer at which data is stored.<DD><CODE>length</CODE> - the amount of data to read.
<DT><B>Returns:</B><DD>the number of bytes read.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the data cannot be read.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)"><CODE>Task.handleInput(byte[], int, int)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="handleFlush(java.lang.String)"><!-- --></A><H3>
handleFlush</H3>
<PRE>
protected void <B>handleFlush</B>(java.lang.String&nbsp;output)</PRE>
<DL>
<DD>Handles output sent to System.out by this task or its real task.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A></CODE> in class <CODE><A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - The output to log. Should not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="handleErrorOutput(java.lang.String)"><!-- --></A><H3>
handleErrorOutput</H3>
<PRE>
protected void <B>handleErrorOutput</B>(java.lang.String&nbsp;output)</PRE>
<DL>
<DD>Handles error output sent to System.err by this task or its real task.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A></CODE> in class <CODE><A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - The error output to log. Should not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="handleErrorFlush(java.lang.String)"><!-- --></A><H3>
handleErrorFlush</H3>
<PRE>
protected void <B>handleErrorFlush</B>(java.lang.String&nbsp;output)</PRE>
<DL>
<DD>Handles error output sent to System.err by this task or its real task.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A></CODE> in class <CODE><A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - The error output to log. Should not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()</PRE>
<DL>
<DD>Executes the real object if it's a task. If it's not a task
 (e.g. a data type) then this method does nothing.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="addChild(org.apache.tools.ant.UnknownElement)"><!-- --></A><H3>
addChild</H3>
<PRE>
public void <B>addChild</B>(<A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A>&nbsp;child)</PRE>
<DL>
<DD>Adds a child element to this element.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>child</CODE> - The child element to add. Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="handleChildren(java.lang.Object, org.apache.tools.ant.RuntimeConfigurable)"><!-- --></A><H3>
handleChildren</H3>
<PRE>
protected void <B>handleChildren</B>(java.lang.Object&nbsp;parent,
                              <A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A>&nbsp;parentWrapper)
                       throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Creates child elements, creates children of the children
 (recursively), and sets attributes of the child elements.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>parent</CODE> - The configured object for the parent.
               Must not be <code>null</code>.<DD><CODE>parentWrapper</CODE> - The wrapper containing child wrappers
                      to be configured. Must not be <code>null</code>
                      if there are any children.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the children cannot be configured.</DL>
</DD>
</DL>
<HR>

<A NAME="getComponentName()"><!-- --></A><H3>
getComponentName</H3>
<PRE>
protected java.lang.String <B>getComponentName</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the component name - uses ProjectHelper#genComponentName()</DL>
</DD>
</DL>
<HR>

<A NAME="applyPreSet(org.apache.tools.ant.UnknownElement)"><!-- --></A><H3>
applyPreSet</H3>
<PRE>
public void <B>applyPreSet</B>(<A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A>&nbsp;u)</PRE>
<DL>
<DD>This is used then the realobject of the UE is a PreSetDefinition.
 This is also used when a presetdef is used on a presetdef
 The attributes, elements and text are applied to this
 UE.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>u</CODE> - an UnknownElement containing the attributes, elements and text</DL>
</DD>
</DL>
<HR>

<A NAME="makeObject(org.apache.tools.ant.UnknownElement, org.apache.tools.ant.RuntimeConfigurable)"><!-- --></A><H3>
makeObject</H3>
<PRE>
protected java.lang.Object <B>makeObject</B>(<A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A>&nbsp;ue,
                                      <A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A>&nbsp;w)</PRE>
<DL>
<DD>Creates a named task or data type. If the real object is a task,
 it is configured up to the init() stage.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ue</CODE> - The unknown element to create the real object for.
           Must not be <code>null</code>.<DD><CODE>w</CODE> - Ignored in this implementation.
<DT><B>Returns:</B><DD>the task or data type represented by the given unknown element.</DL>
</DD>
</DL>
<HR>

<A NAME="makeTask(org.apache.tools.ant.UnknownElement, org.apache.tools.ant.RuntimeConfigurable)"><!-- --></A><H3>
makeTask</H3>
<PRE>
protected <A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A> <B>makeTask</B>(<A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A>&nbsp;ue,
                        <A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A>&nbsp;w)</PRE>
<DL>
<DD>Creates a named task and configures it up to the init() stage.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ue</CODE> - The UnknownElement to create the real task for.
           Must not be <code>null</code>.<DD><CODE>w</CODE> - Ignored.
<DT><B>Returns:</B><DD>the task specified by the given unknown element, or
         <code>null</code> if the task name is not recognised.</DL>
</DD>
</DL>
<HR>

<A NAME="getNotFoundException(java.lang.String, java.lang.String)"><!-- --></A><H3>
getNotFoundException</H3>
<PRE>
protected <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A> <B>getNotFoundException</B>(java.lang.String&nbsp;what,
                                              java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Returns a very verbose exception for when a task/data type cannot
 be found.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>what</CODE> - The kind of thing being created. For example, when
             a task name could not be found, this would be
             <code>"task"</code>. Should not be <code>null</code>.<DD><CODE>name</CODE> - The name of the element which could not be found.
             Should not be <code>null</code>.
<DT><B>Returns:</B><DD>a detailed description of what might have caused the problem.</DL>
</DD>
</DL>
<HR>

<A NAME="getTaskName()"><!-- --></A><H3>
getTaskName</H3>
<PRE>
public java.lang.String <B>getTaskName</B>()</PRE>
<DL>
<DD>Returns the name to use in logging messages.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A></CODE> in class <CODE><A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the name to use in logging messages.</DL>
</DD>
</DL>
<HR>

<A NAME="getTask()"><!-- --></A><H3>
getTask</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A> <B>getTask</B>()</PRE>
<DL>
<DD>Returns the task instance after it has been created and if it is a task.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a task instance or <code>null</code> if the real object is not
         a task.</DL>
</DD>
</DL>
<HR>

<A NAME="getRealThing()"><!-- --></A><H3>
getRealThing</H3>
<PRE>
public java.lang.Object <B>getRealThing</B>()</PRE>
<DL>
<DD>Return the configured object
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the real thing whatever it is<DT><B>Since:</B></DT>
  <DD>ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setRealThing(java.lang.Object)"><!-- --></A><H3>
setRealThing</H3>
<PRE>
public void <B>setRealThing</B>(java.lang.Object&nbsp;realThing)</PRE>
<DL>
<DD>Set the configured object
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>realThing</CODE> - the configured object<DT><B>Since:</B></DT>
  <DD>ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="similar(java.lang.Object)"><!-- --></A><H3>
similar</H3>
<PRE>
public boolean <B>similar</B>(java.lang.Object&nbsp;obj)</PRE>
<DL>
<DD>like contents equals, but ignores project
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>obj</CODE> - the object to check against
<DT><B>Returns:</B><DD>true if this unknownelement has the same contents the other</DL>
</DD>
</DL>
<HR>

<A NAME="copy(org.apache.tools.ant.Project)"><!-- --></A><H3>
copy</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A> <B>copy</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;newProject)</PRE>
<DL>
<DD>Make a copy of the unknown element and set it in the new project.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>newProject</CODE> - the project to create the UE in.
<DT><B>Returns:</B><DD>the copied UE.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/TypeAdapter.html" title="interface in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/UnsupportedAttributeException.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/UnknownElement.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="UnknownElement.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.Task">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
