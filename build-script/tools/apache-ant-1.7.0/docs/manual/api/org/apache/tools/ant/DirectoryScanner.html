<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:19 EST 2006 -->
<TITLE>
DirectoryScanner (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.DirectoryScanner class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="DirectoryScanner (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/Diagnostics.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/DynamicAttribute.html" title="interface in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/DirectoryScanner.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="DirectoryScanner.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class DirectoryScanner</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.DirectoryScanner</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD><A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A>, <A HREF="../../../../org/apache/tools/ant/types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</A>, <A HREF="../../../../org/apache/tools/ant/types/selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</A></DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../org/apache/tools/ant/types/ArchiveScanner.html" title="class in org.apache.tools.ant.types">ArchiveScanner</A>, <A HREF="../../../../org/apache/tools/ant/types/optional/depend/DependScanner.html" title="class in org.apache.tools.ant.types.optional.depend">DependScanner</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.FTPDirectoryScanner.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP.FTPDirectoryScanner</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>DirectoryScanner</B><DT>extends java.lang.Object<DT>implements <A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A>, <A HREF="../../../../org/apache/tools/ant/types/selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</A>, <A HREF="../../../../org/apache/tools/ant/types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</A></DL>
</PRE>

<P>
Class for scanning a directory for files/directories which match certain
 criteria.
 <p>
 These criteria consist of selectors and patterns which have been specified.
 With the selectors you can select which files you want to have included.
 Files which are not selected are excluded. With patterns you can include
 or exclude files based on their filename.
 <p>
 The idea is simple. A given directory is recursively scanned for all files
 and directories. Each file/directory is matched against a set of selectors,
 including special support for matching against filenames with include and
 and exclude patterns. Only files/directories which match at least one
 pattern of the include pattern list or other file selector, and don't match
 any pattern of the exclude pattern list or fail to match against a required
 selector will be placed in the list of files/directories found.
 <p>
 When no list of include patterns is supplied, "**" will be used, which
 means that everything will be matched. When no list of exclude patterns is
 supplied, an empty list is used, such that nothing will be excluded. When
 no selectors are supplied, none are applied.
 <p>
 The filename pattern matching is done as follows:
 The name to be matched is split up in path segments. A path segment is the
 name of a directory or file, which is bounded by
 <code>File.separator</code> ('/' under UNIX, '\' under Windows).
 For example, "abc/def/ghi/xyz.java" is split up in the segments "abc",
 "def","ghi" and "xyz.java".
 The same is done for the pattern against which should be matched.
 <p>
 The segments of the name and the pattern are then matched against each
 other. When '**' is used for a path segment in the pattern, it matches
 zero or more path segments of the name.
 <p>
 There is a special case regarding the use of <code>File.separator</code>s
 at the beginning of the pattern and the string to match:<br>
 When a pattern starts with a <code>File.separator</code>, the string
 to match must also start with a <code>File.separator</code>.
 When a pattern does not start with a <code>File.separator</code>, the
 string to match may not start with a <code>File.separator</code>.
 When one of these rules is not obeyed, the string will not
 match.
 <p>
 When a name path segment is matched against a pattern path segment, the
 following special characters can be used:<br>
 '*' matches zero or more characters<br>
 '?' matches one character.
 <p>
 Examples:
 <p>
 "**\*.class" matches all .class files/dirs in a directory tree.
 <p>
 "test\a??.java" matches all files/dirs which start with an 'a', then two
 more characters and then ".java", in a directory called test.
 <p>
 "**" matches everything in a directory tree.
 <p>
 "**\test\**\XYZ*" matches all files/dirs which start with "XYZ" and where
 there is a parent directory called test (e.g. "abc\test\def\ghi\XYZ123").
 <p>
 Case sensitivity may be turned off if necessary. By default, it is
 turned on.
 <p>
 Example of usage:
 <pre>
   String[] includes = {"*\*\*.class"};
   String[] excludes = {"modules\\\*\**"};
   ds.setIncludes(includes);
   ds.setExcludes(excludes);
   ds.setBasedir(new File("test"));
   ds.setCaseSensitive(true);
   ds.scan();

   System.out.println("FILES:");
   String[] files = ds.getIncludedFiles();
   for (int i = 0; i < files.length; i++) {
     System.out.println(files[i]);
   }
 </pre>
 This will scan a directory called test for .class files, but excludes all
 files in all proper subdirectories of a directory called "modules"
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#basedir">basedir</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The base directory to be scanned.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#DEFAULTEXCLUDES">DEFAULTEXCLUDES</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             Use the <A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#getDefaultExcludes()"><CODE>getDefaultExcludes</CODE></A>
             method instead.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#dirsDeselected">dirsDeselected</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The directories which matched at least one include and no excludes
 but which a selector discarded.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#dirsExcluded">dirsExcluded</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The directories which matched at least one include and at least one
 exclude.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#dirsIncluded">dirsIncluded</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The directories which matched at least one include and no excludes
 and were selected.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#dirsNotIncluded">dirsNotIncluded</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The directories which were found and did not match any includes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#everythingIncluded">everythingIncluded</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether or not everything tested so far has been included.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#excludes">excludes</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The patterns for the files to be excluded.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#filesDeselected">filesDeselected</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The files which matched at least one include and no excludes and
 which a selector discarded.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#filesExcluded">filesExcluded</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The files which matched at least one include and at least
 one exclude.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#filesIncluded">filesIncluded</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The files which matched at least one include and no excludes
 and were selected.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#filesNotIncluded">filesNotIncluded</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The files which did not match any includes or selectors.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#haveSlowResults">haveSlowResults</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether or not our results were built by a slow scan.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#includes">includes</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The patterns for the files to be included.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#isCaseSensitive">isCaseSensitive</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether or not the file system should be treated as a case sensitive
 one.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</A>[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#selectors">selectors</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Selectors that will filter which files are in our candidate list.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#DirectoryScanner()">DirectoryScanner</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sole constructor.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#addDefaultExclude(java.lang.String)">addDefaultExclude</A></B>(java.lang.String&nbsp;s)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a pattern to the default excludes unless it is already a
 default exclude.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#addDefaultExcludes()">addDefaultExcludes</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add default exclusions to the current exclusions set.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#addExcludes(java.lang.String[])">addExcludes</A></B>(java.lang.String[]&nbsp;excludes)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add to the list of exclude patterns to use.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#clearResults()">clearResults</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Clear the result caches for a scan.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#couldHoldIncluded(java.lang.String)">couldHoldIncluded</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Test whether or not a name matches the start of at least one include
 pattern.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#getBasedir()">getBasedir</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the base directory to be scanned.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#getDefaultExcludes()">getDefaultExcludes</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the list of patterns that should be excluded by default.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#getDeselectedDirectories()">getDeselectedDirectories</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the names of the directories which were selected out and
 therefore not ultimately included.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#getDeselectedFiles()">getDeselectedFiles</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the names of the files which were selected out and
 therefore not ultimately included.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#getExcludedDirectories()">getExcludedDirectories</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the names of the directories which matched at least one of the
 include patterns and at least one of the exclude patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#getExcludedFiles()">getExcludedFiles</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the names of the files which matched at least one of the
 include patterns and at least one of the exclude patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#getIncludedDirectories()">getIncludedDirectories</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the names of the directories which matched at least one of the
 include patterns and none of the exclude patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#getIncludedDirsCount()">getIncludedDirsCount</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the count of included directories.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#getIncludedFiles()">getIncludedFiles</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the names of the files which matched at least one of the
 include patterns and none of the exclude patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#getIncludedFilesCount()">getIncludedFilesCount</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the count of included files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#getNotIncludedDirectories()">getNotIncludedDirectories</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the names of the directories which matched none of the include
 patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#getNotIncludedFiles()">getNotIncludedFiles</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the names of the files which matched none of the include
 patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#getResource(java.lang.String)">getResource</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the named resource.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#isCaseSensitive()">isCaseSensitive</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Find out whether include exclude patterns are matched in a
 case sensitive way.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#isEverythingIncluded()">isEverythingIncluded</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return whether or not the scanner has included all the files or
 directories it has come across so far.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#isExcluded(java.lang.String)">isExcluded</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Test whether or not a name matches against at least one exclude
 pattern.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#isFollowSymlinks()">isFollowSymlinks</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get whether or not a DirectoryScanner follows symbolic links.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#isIncluded(java.lang.String)">isIncluded</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Test whether or not a name matches against at least one include
 pattern.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#isSelected(java.lang.String, java.io.File)">isSelected</A></B>(java.lang.String&nbsp;name,
           java.io.File&nbsp;file)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Test whether a file should be selected.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#match(java.lang.String, java.lang.String)">match</A></B>(java.lang.String&nbsp;pattern,
      java.lang.String&nbsp;str)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Test whether or not a string matches against a pattern.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#match(java.lang.String, java.lang.String, boolean)">match</A></B>(java.lang.String&nbsp;pattern,
      java.lang.String&nbsp;str,
      boolean&nbsp;isCaseSensitive)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Test whether or not a string matches against a pattern.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#matchPath(java.lang.String, java.lang.String)">matchPath</A></B>(java.lang.String&nbsp;pattern,
          java.lang.String&nbsp;str)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Test whether or not a given path matches a given pattern.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#matchPath(java.lang.String, java.lang.String, boolean)">matchPath</A></B>(java.lang.String&nbsp;pattern,
          java.lang.String&nbsp;str,
          boolean&nbsp;isCaseSensitive)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Test whether or not a given path matches a given pattern.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#matchPatternStart(java.lang.String, java.lang.String)">matchPatternStart</A></B>(java.lang.String&nbsp;pattern,
                  java.lang.String&nbsp;str)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Test whether or not a given path matches the start of a given
 pattern up to the first "**".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#matchPatternStart(java.lang.String, java.lang.String, boolean)">matchPatternStart</A></B>(java.lang.String&nbsp;pattern,
                  java.lang.String&nbsp;str,
                  boolean&nbsp;isCaseSensitive)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Test whether or not a given path matches the start of a given
 pattern up to the first "**".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#removeDefaultExclude(java.lang.String)">removeDefaultExclude</A></B>(java.lang.String&nbsp;s)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Remove a string if it is a default exclude.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#resetDefaultExcludes()">resetDefaultExcludes</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Go back to the hardwired default exclude patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#scan()">scan</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Scan for files which match at least one include pattern and don't match
 any exclude patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#scandir(java.io.File, java.lang.String, boolean)">scandir</A></B>(java.io.File&nbsp;dir,
        java.lang.String&nbsp;vpath,
        boolean&nbsp;fast)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Scan the given directory for files and directories.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#setBasedir(java.io.File)">setBasedir</A></B>(java.io.File&nbsp;basedir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the base directory to be scanned.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#setBasedir(java.lang.String)">setBasedir</A></B>(java.lang.String&nbsp;basedir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the base directory to be scanned.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#setCaseSensitive(boolean)">setCaseSensitive</A></B>(boolean&nbsp;isCaseSensitive)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether or not include and exclude patterns are matched
 in a case sensitive way.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#setExcludes(java.lang.String[])">setExcludes</A></B>(java.lang.String[]&nbsp;excludes)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the list of exclude patterns to use.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#setFollowSymlinks(boolean)">setFollowSymlinks</A></B>(boolean&nbsp;followSymlinks)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether or not symbolic links should be followed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#setIncludes(java.lang.String[])">setIncludes</A></B>(java.lang.String[]&nbsp;includes)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the list of include patterns to use.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#setSelectors(org.apache.tools.ant.types.selectors.FileSelector[])">setSelectors</A></B>(<A HREF="../../../../org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</A>[]&nbsp;selectors)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the selectors that will select the filelist.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#slowScan()">slowScan</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Top level invocation for a slow scan.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="DEFAULTEXCLUDES"><!-- --></A><H3>
DEFAULTEXCLUDES</H3>
<PRE>
protected static final java.lang.String[] <B>DEFAULTEXCLUDES</B></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             Use the <A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#getDefaultExcludes()"><CODE>getDefaultExcludes</CODE></A>
             method instead.</I><DD>Patterns which should be excluded by default.

 <p>Note that you can now add patterns to the list of default
 excludes.  Added patterns will not become part of this array
 that has only been kept around for backwards compatibility
 reasons.</p>
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="basedir"><!-- --></A><H3>
basedir</H3>
<PRE>
protected java.io.File <B>basedir</B></PRE>
<DL>
<DD>The base directory to be scanned.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="includes"><!-- --></A><H3>
includes</H3>
<PRE>
protected java.lang.String[] <B>includes</B></PRE>
<DL>
<DD>The patterns for the files to be included.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="excludes"><!-- --></A><H3>
excludes</H3>
<PRE>
protected java.lang.String[] <B>excludes</B></PRE>
<DL>
<DD>The patterns for the files to be excluded.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="selectors"><!-- --></A><H3>
selectors</H3>
<PRE>
protected <A HREF="../../../../org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</A>[] <B>selectors</B></PRE>
<DL>
<DD>Selectors that will filter which files are in our candidate list.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="filesIncluded"><!-- --></A><H3>
filesIncluded</H3>
<PRE>
protected java.util.Vector <B>filesIncluded</B></PRE>
<DL>
<DD>The files which matched at least one include and no excludes
 and were selected.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="filesNotIncluded"><!-- --></A><H3>
filesNotIncluded</H3>
<PRE>
protected java.util.Vector <B>filesNotIncluded</B></PRE>
<DL>
<DD>The files which did not match any includes or selectors.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="filesExcluded"><!-- --></A><H3>
filesExcluded</H3>
<PRE>
protected java.util.Vector <B>filesExcluded</B></PRE>
<DL>
<DD>The files which matched at least one include and at least
 one exclude.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="dirsIncluded"><!-- --></A><H3>
dirsIncluded</H3>
<PRE>
protected java.util.Vector <B>dirsIncluded</B></PRE>
<DL>
<DD>The directories which matched at least one include and no excludes
 and were selected.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="dirsNotIncluded"><!-- --></A><H3>
dirsNotIncluded</H3>
<PRE>
protected java.util.Vector <B>dirsNotIncluded</B></PRE>
<DL>
<DD>The directories which were found and did not match any includes.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="dirsExcluded"><!-- --></A><H3>
dirsExcluded</H3>
<PRE>
protected java.util.Vector <B>dirsExcluded</B></PRE>
<DL>
<DD>The directories which matched at least one include and at least one
 exclude.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="filesDeselected"><!-- --></A><H3>
filesDeselected</H3>
<PRE>
protected java.util.Vector <B>filesDeselected</B></PRE>
<DL>
<DD>The files which matched at least one include and no excludes and
 which a selector discarded.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="dirsDeselected"><!-- --></A><H3>
dirsDeselected</H3>
<PRE>
protected java.util.Vector <B>dirsDeselected</B></PRE>
<DL>
<DD>The directories which matched at least one include and no excludes
 but which a selector discarded.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="haveSlowResults"><!-- --></A><H3>
haveSlowResults</H3>
<PRE>
protected boolean <B>haveSlowResults</B></PRE>
<DL>
<DD>Whether or not our results were built by a slow scan.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="isCaseSensitive"><!-- --></A><H3>
isCaseSensitive</H3>
<PRE>
protected boolean <B>isCaseSensitive</B></PRE>
<DL>
<DD>Whether or not the file system should be treated as a case sensitive
 one.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="everythingIncluded"><!-- --></A><H3>
everythingIncluded</H3>
<PRE>
protected boolean <B>everythingIncluded</B></PRE>
<DL>
<DD>Whether or not everything tested so far has been included.
<P>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="DirectoryScanner()"><!-- --></A><H3>
DirectoryScanner</H3>
<PRE>
public <B>DirectoryScanner</B>()</PRE>
<DL>
<DD>Sole constructor.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="matchPatternStart(java.lang.String, java.lang.String)"><!-- --></A><H3>
matchPatternStart</H3>
<PRE>
protected static boolean <B>matchPatternStart</B>(java.lang.String&nbsp;pattern,
                                           java.lang.String&nbsp;str)</PRE>
<DL>
<DD>Test whether or not a given path matches the start of a given
 pattern up to the first "**".
 <p>
 This is not a general purpose test and should only be used if you
 can live with false positives. For example, <code>pattern=**\a</code>
 and <code>str=b</code> will yield <code>true</code>.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pattern</CODE> - The pattern to match against. Must not be
                <code>null</code>.<DD><CODE>str</CODE> - The path to match, as a String. Must not be
                <code>null</code>.
<DT><B>Returns:</B><DD>whether or not a given path matches the start of a given
 pattern up to the first "**".</DL>
</DD>
</DL>
<HR>

<A NAME="matchPatternStart(java.lang.String, java.lang.String, boolean)"><!-- --></A><H3>
matchPatternStart</H3>
<PRE>
protected static boolean <B>matchPatternStart</B>(java.lang.String&nbsp;pattern,
                                           java.lang.String&nbsp;str,
                                           boolean&nbsp;isCaseSensitive)</PRE>
<DL>
<DD>Test whether or not a given path matches the start of a given
 pattern up to the first "**".
 <p>
 This is not a general purpose test and should only be used if you
 can live with false positives. For example, <code>pattern=**\a</code>
 and <code>str=b</code> will yield <code>true</code>.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pattern</CODE> - The pattern to match against. Must not be
                <code>null</code>.<DD><CODE>str</CODE> - The path to match, as a String. Must not be
                <code>null</code>.<DD><CODE>isCaseSensitive</CODE> - Whether or not matching should be performed
                        case sensitively.
<DT><B>Returns:</B><DD>whether or not a given path matches the start of a given
 pattern up to the first "**".</DL>
</DD>
</DL>
<HR>

<A NAME="matchPath(java.lang.String, java.lang.String)"><!-- --></A><H3>
matchPath</H3>
<PRE>
protected static boolean <B>matchPath</B>(java.lang.String&nbsp;pattern,
                                   java.lang.String&nbsp;str)</PRE>
<DL>
<DD>Test whether or not a given path matches a given pattern.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pattern</CODE> - The pattern to match against. Must not be
                <code>null</code>.<DD><CODE>str</CODE> - The path to match, as a String. Must not be
                <code>null</code>.
<DT><B>Returns:</B><DD><code>true</code> if the pattern matches against the string,
         or <code>false</code> otherwise.</DL>
</DD>
</DL>
<HR>

<A NAME="matchPath(java.lang.String, java.lang.String, boolean)"><!-- --></A><H3>
matchPath</H3>
<PRE>
protected static boolean <B>matchPath</B>(java.lang.String&nbsp;pattern,
                                   java.lang.String&nbsp;str,
                                   boolean&nbsp;isCaseSensitive)</PRE>
<DL>
<DD>Test whether or not a given path matches a given pattern.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pattern</CODE> - The pattern to match against. Must not be
                <code>null</code>.<DD><CODE>str</CODE> - The path to match, as a String. Must not be
                <code>null</code>.<DD><CODE>isCaseSensitive</CODE> - Whether or not matching should be performed
                        case sensitively.
<DT><B>Returns:</B><DD><code>true</code> if the pattern matches against the string,
         or <code>false</code> otherwise.</DL>
</DD>
</DL>
<HR>

<A NAME="match(java.lang.String, java.lang.String)"><!-- --></A><H3>
match</H3>
<PRE>
public static boolean <B>match</B>(java.lang.String&nbsp;pattern,
                            java.lang.String&nbsp;str)</PRE>
<DL>
<DD>Test whether or not a string matches against a pattern.
 The pattern may contain two special characters:<br>
 '*' means zero or more characters<br>
 '?' means one and only one character
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pattern</CODE> - The pattern to match against.
                Must not be <code>null</code>.<DD><CODE>str</CODE> - The string which must be matched against the pattern.
                Must not be <code>null</code>.
<DT><B>Returns:</B><DD><code>true</code> if the string matches against the pattern,
         or <code>false</code> otherwise.</DL>
</DD>
</DL>
<HR>

<A NAME="match(java.lang.String, java.lang.String, boolean)"><!-- --></A><H3>
match</H3>
<PRE>
protected static boolean <B>match</B>(java.lang.String&nbsp;pattern,
                               java.lang.String&nbsp;str,
                               boolean&nbsp;isCaseSensitive)</PRE>
<DL>
<DD>Test whether or not a string matches against a pattern.
 The pattern may contain two special characters:<br>
 '*' means zero or more characters<br>
 '?' means one and only one character
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pattern</CODE> - The pattern to match against.
                Must not be <code>null</code>.<DD><CODE>str</CODE> - The string which must be matched against the pattern.
                Must not be <code>null</code>.<DD><CODE>isCaseSensitive</CODE> - Whether or not matching should be performed
                        case sensitively.
<DT><B>Returns:</B><DD><code>true</code> if the string matches against the pattern,
         or <code>false</code> otherwise.</DL>
</DD>
</DL>
<HR>

<A NAME="getDefaultExcludes()"><!-- --></A><H3>
getDefaultExcludes</H3>
<PRE>
public static java.lang.String[] <B>getDefaultExcludes</B>()</PRE>
<DL>
<DD>Get the list of patterns that should be excluded by default.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>An array of <code>String</code> based on the current
         contents of the <code>defaultExcludes</code>
         <code>Vector</code>.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addDefaultExclude(java.lang.String)"><!-- --></A><H3>
addDefaultExclude</H3>
<PRE>
public static boolean <B>addDefaultExclude</B>(java.lang.String&nbsp;s)</PRE>
<DL>
<DD>Add a pattern to the default excludes unless it is already a
 default exclude.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>s</CODE> - A string to add as an exclude pattern.
<DT><B>Returns:</B><DD><code>true</code> if the string was added;
            <code>false</code> if it already existed.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="removeDefaultExclude(java.lang.String)"><!-- --></A><H3>
removeDefaultExclude</H3>
<PRE>
public static boolean <B>removeDefaultExclude</B>(java.lang.String&nbsp;s)</PRE>
<DL>
<DD>Remove a string if it is a default exclude.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>s</CODE> - The string to attempt to remove.
<DT><B>Returns:</B><DD><code>true</code> if <code>s</code> was a default
            exclude (and thus was removed);
            <code>false</code> if <code>s</code> was not
            in the default excludes list to begin with.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="resetDefaultExcludes()"><!-- --></A><H3>
resetDefaultExcludes</H3>
<PRE>
public static void <B>resetDefaultExcludes</B>()</PRE>
<DL>
<DD>Go back to the hardwired default exclude patterns.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setBasedir(java.lang.String)"><!-- --></A><H3>
setBasedir</H3>
<PRE>
public void <B>setBasedir</B>(java.lang.String&nbsp;basedir)</PRE>
<DL>
<DD>Set the base directory to be scanned. This is the directory which is
 scanned recursively. All '/' and '\' characters are replaced by
 <code>File.separatorChar</code>, so the separator used need not match
 <code>File.separatorChar</code>.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html#setBasedir(java.lang.String)">setBasedir</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>basedir</CODE> - The base directory to scan.</DL>
</DD>
</DL>
<HR>

<A NAME="setBasedir(java.io.File)"><!-- --></A><H3>
setBasedir</H3>
<PRE>
public void <B>setBasedir</B>(java.io.File&nbsp;basedir)</PRE>
<DL>
<DD>Set the base directory to be scanned. This is the directory which is
 scanned recursively.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html#setBasedir(java.io.File)">setBasedir</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>basedir</CODE> - The base directory for scanning.</DL>
</DD>
</DL>
<HR>

<A NAME="getBasedir()"><!-- --></A><H3>
getBasedir</H3>
<PRE>
public java.io.File <B>getBasedir</B>()</PRE>
<DL>
<DD>Return the base directory to be scanned.
 This is the directory which is scanned recursively.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html#getBasedir()">getBasedir</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the base directory to be scanned.</DL>
</DD>
</DL>
<HR>

<A NAME="isCaseSensitive()"><!-- --></A><H3>
isCaseSensitive</H3>
<PRE>
public boolean <B>isCaseSensitive</B>()</PRE>
<DL>
<DD>Find out whether include exclude patterns are matched in a
 case sensitive way.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>whether or not the scanning is case sensitive.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setCaseSensitive(boolean)"><!-- --></A><H3>
setCaseSensitive</H3>
<PRE>
public void <B>setCaseSensitive</B>(boolean&nbsp;isCaseSensitive)</PRE>
<DL>
<DD>Set whether or not include and exclude patterns are matched
 in a case sensitive way.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html#setCaseSensitive(boolean)">setCaseSensitive</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>isCaseSensitive</CODE> - whether or not the file system should be
                        regarded as a case sensitive one.</DL>
</DD>
</DL>
<HR>

<A NAME="isFollowSymlinks()"><!-- --></A><H3>
isFollowSymlinks</H3>
<PRE>
public boolean <B>isFollowSymlinks</B>()</PRE>
<DL>
<DD>Get whether or not a DirectoryScanner follows symbolic links.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>flag indicating whether symbolic links should be followed.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setFollowSymlinks(boolean)"><!-- --></A><H3>
setFollowSymlinks</H3>
<PRE>
public void <B>setFollowSymlinks</B>(boolean&nbsp;followSymlinks)</PRE>
<DL>
<DD>Set whether or not symbolic links should be followed.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>followSymlinks</CODE> - whether or not symbolic links should be followed.</DL>
</DD>
</DL>
<HR>

<A NAME="setIncludes(java.lang.String[])"><!-- --></A><H3>
setIncludes</H3>
<PRE>
public void <B>setIncludes</B>(java.lang.String[]&nbsp;includes)</PRE>
<DL>
<DD>Set the list of include patterns to use. All '/' and '\' characters
 are replaced by <code>File.separatorChar</code>, so the separator used
 need not match <code>File.separatorChar</code>.
 <p>
 When a pattern ends with a '/' or '\', "**" is appended.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html#setIncludes(java.lang.String[])">setIncludes</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>includes</CODE> - A list of include patterns.
                 May be <code>null</code>, indicating that all files
                 should be included. If a non-<code>null</code>
                 list is given, all elements must be
                 non-<code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setExcludes(java.lang.String[])"><!-- --></A><H3>
setExcludes</H3>
<PRE>
public void <B>setExcludes</B>(java.lang.String[]&nbsp;excludes)</PRE>
<DL>
<DD>Set the list of exclude patterns to use. All '/' and '\' characters
 are replaced by <code>File.separatorChar</code>, so the separator used
 need not match <code>File.separatorChar</code>.
 <p>
 When a pattern ends with a '/' or '\', "**" is appended.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html#setExcludes(java.lang.String[])">setExcludes</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>excludes</CODE> - A list of exclude patterns.
                 May be <code>null</code>, indicating that no files
                 should be excluded. If a non-<code>null</code> list is
                 given, all elements must be non-<code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="addExcludes(java.lang.String[])"><!-- --></A><H3>
addExcludes</H3>
<PRE>
public void <B>addExcludes</B>(java.lang.String[]&nbsp;excludes)</PRE>
<DL>
<DD>Add to the list of exclude patterns to use. All '/' and '\'
 characters are replaced by <code>File.separatorChar</code>, so
 the separator used need not match <code>File.separatorChar</code>.
 <p>
 When a pattern ends with a '/' or '\', "**" is appended.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>excludes</CODE> - A list of exclude patterns.
                 May be <code>null</code>, in which case the
                 exclude patterns don't get changed at all.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setSelectors(org.apache.tools.ant.types.selectors.FileSelector[])"><!-- --></A><H3>
setSelectors</H3>
<PRE>
public void <B>setSelectors</B>(<A HREF="../../../../org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</A>[]&nbsp;selectors)</PRE>
<DL>
<DD>Set the selectors that will select the filelist.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/types/selectors/SelectorScanner.html#setSelectors(org.apache.tools.ant.types.selectors.FileSelector[])">setSelectors</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/types/selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selectors</CODE> - specifies the selectors to be invoked on a scan.</DL>
</DD>
</DL>
<HR>

<A NAME="isEverythingIncluded()"><!-- --></A><H3>
isEverythingIncluded</H3>
<PRE>
public boolean <B>isEverythingIncluded</B>()</PRE>
<DL>
<DD>Return whether or not the scanner has included all the files or
 directories it has come across so far.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD><code>true</code> if all files and directories which have
         been found so far have been included.</DL>
</DD>
</DL>
<HR>

<A NAME="scan()"><!-- --></A><H3>
scan</H3>
<PRE>
public void <B>scan</B>()
          throws java.lang.IllegalStateException</PRE>
<DL>
<DD>Scan for files which match at least one include pattern and don't match
 any exclude patterns. If there are selectors then the files must pass
 muster there, as well.  Scans under basedir, if set; otherwise the
 include patterns without leading wildcards specify the absolute paths of
 the files that may be included.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html#scan()">scan</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.lang.IllegalStateException</CODE> - if the base directory was set
            incorrectly (i.e. if it doesn't exist or isn't a directory).</DL>
</DD>
</DL>
<HR>

<A NAME="clearResults()"><!-- --></A><H3>
clearResults</H3>
<PRE>
protected void <B>clearResults</B>()</PRE>
<DL>
<DD>Clear the result caches for a scan.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="slowScan()"><!-- --></A><H3>
slowScan</H3>
<PRE>
protected void <B>slowScan</B>()</PRE>
<DL>
<DD>Top level invocation for a slow scan. A slow scan builds up a full
 list of excluded/included files/directories, whereas a fast scan
 will only have full results for included files, as it ignores
 directories which can't possibly hold any included files/directories.
 <p>
 Returns immediately if a slow scan has already been completed.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="scandir(java.io.File, java.lang.String, boolean)"><!-- --></A><H3>
scandir</H3>
<PRE>
protected void <B>scandir</B>(java.io.File&nbsp;dir,
                       java.lang.String&nbsp;vpath,
                       boolean&nbsp;fast)</PRE>
<DL>
<DD>Scan the given directory for files and directories. Found files and
 directories are placed in their respective collections, based on the
 matching of includes, excludes, and the selectors.  When a directory
 is found, it is scanned recursively.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>dir</CODE> - The directory to scan. Must not be <code>null</code>.<DD><CODE>vpath</CODE> - The path relative to the base directory (needed to
              prevent problems with an absolute path when using
              dir). Must not be <code>null</code>.<DD><CODE>fast</CODE> - Whether or not this call is part of a fast scan.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#filesIncluded"><CODE>filesIncluded</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#filesNotIncluded"><CODE>filesNotIncluded</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#filesExcluded"><CODE>filesExcluded</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#dirsIncluded"><CODE>dirsIncluded</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#dirsNotIncluded"><CODE>dirsNotIncluded</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#dirsExcluded"><CODE>dirsExcluded</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#slowScan()"><CODE>slowScan()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="isIncluded(java.lang.String)"><!-- --></A><H3>
isIncluded</H3>
<PRE>
protected boolean <B>isIncluded</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Test whether or not a name matches against at least one include
 pattern.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - The name to match. Must not be <code>null</code>.
<DT><B>Returns:</B><DD><code>true</code> when the name matches against at least one
         include pattern, or <code>false</code> otherwise.</DL>
</DD>
</DL>
<HR>

<A NAME="couldHoldIncluded(java.lang.String)"><!-- --></A><H3>
couldHoldIncluded</H3>
<PRE>
protected boolean <B>couldHoldIncluded</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Test whether or not a name matches the start of at least one include
 pattern.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - The name to match. Must not be <code>null</code>.
<DT><B>Returns:</B><DD><code>true</code> when the name matches against the start of at
         least one include pattern, or <code>false</code> otherwise.</DL>
</DD>
</DL>
<HR>

<A NAME="isExcluded(java.lang.String)"><!-- --></A><H3>
isExcluded</H3>
<PRE>
protected boolean <B>isExcluded</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Test whether or not a name matches against at least one exclude
 pattern.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - The name to match. Must not be <code>null</code>.
<DT><B>Returns:</B><DD><code>true</code> when the name matches against at least one
         exclude pattern, or <code>false</code> otherwise.</DL>
</DD>
</DL>
<HR>

<A NAME="isSelected(java.lang.String, java.io.File)"><!-- --></A><H3>
isSelected</H3>
<PRE>
protected boolean <B>isSelected</B>(java.lang.String&nbsp;name,
                             java.io.File&nbsp;file)</PRE>
<DL>
<DD>Test whether a file should be selected.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the filename to check for selecting.<DD><CODE>file</CODE> - the java.io.File object for this filename.
<DT><B>Returns:</B><DD><code>false</code> when the selectors says that the file
         should not be selected, <code>true</code> otherwise.</DL>
</DD>
</DL>
<HR>

<A NAME="getIncludedFiles()"><!-- --></A><H3>
getIncludedFiles</H3>
<PRE>
public java.lang.String[] <B>getIncludedFiles</B>()</PRE>
<DL>
<DD>Return the names of the files which matched at least one of the
 include patterns and none of the exclude patterns.
 The names are relative to the base directory.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html#getIncludedFiles()">getIncludedFiles</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the names of the files which matched at least one of the
         include patterns and none of the exclude patterns.</DL>
</DD>
</DL>
<HR>

<A NAME="getIncludedFilesCount()"><!-- --></A><H3>
getIncludedFilesCount</H3>
<PRE>
public int <B>getIncludedFilesCount</B>()</PRE>
<DL>
<DD>Return the count of included files.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD><code>int</code>.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getNotIncludedFiles()"><!-- --></A><H3>
getNotIncludedFiles</H3>
<PRE>
public java.lang.String[] <B>getNotIncludedFiles</B>()</PRE>
<DL>
<DD>Return the names of the files which matched none of the include
 patterns. The names are relative to the base directory. This involves
 performing a slow scan if one has not already been completed.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html#getNotIncludedFiles()">getNotIncludedFiles</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the names of the files which matched none of the include
         patterns.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#slowScan()"><CODE>slowScan()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getExcludedFiles()"><!-- --></A><H3>
getExcludedFiles</H3>
<PRE>
public java.lang.String[] <B>getExcludedFiles</B>()</PRE>
<DL>
<DD>Return the names of the files which matched at least one of the
 include patterns and at least one of the exclude patterns.
 The names are relative to the base directory. This involves
 performing a slow scan if one has not already been completed.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html#getExcludedFiles()">getExcludedFiles</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the names of the files which matched at least one of the
         include patterns and at least one of the exclude patterns.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#slowScan()"><CODE>slowScan()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getDeselectedFiles()"><!-- --></A><H3>
getDeselectedFiles</H3>
<PRE>
public java.lang.String[] <B>getDeselectedFiles</B>()</PRE>
<DL>
<DD><p>Return the names of the files which were selected out and
 therefore not ultimately included.</p>

 <p>The names are relative to the base directory. This involves
 performing a slow scan if one has not already been completed.</p>
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/types/selectors/SelectorScanner.html#getDeselectedFiles()">getDeselectedFiles</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/types/selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the names of the files which were deselected.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#slowScan()"><CODE>slowScan()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getIncludedDirectories()"><!-- --></A><H3>
getIncludedDirectories</H3>
<PRE>
public java.lang.String[] <B>getIncludedDirectories</B>()</PRE>
<DL>
<DD>Return the names of the directories which matched at least one of the
 include patterns and none of the exclude patterns.
 The names are relative to the base directory.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html#getIncludedDirectories()">getIncludedDirectories</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the names of the directories which matched at least one of the
 include patterns and none of the exclude patterns.</DL>
</DD>
</DL>
<HR>

<A NAME="getIncludedDirsCount()"><!-- --></A><H3>
getIncludedDirsCount</H3>
<PRE>
public int <B>getIncludedDirsCount</B>()</PRE>
<DL>
<DD>Return the count of included directories.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD><code>int</code>.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getNotIncludedDirectories()"><!-- --></A><H3>
getNotIncludedDirectories</H3>
<PRE>
public java.lang.String[] <B>getNotIncludedDirectories</B>()</PRE>
<DL>
<DD>Return the names of the directories which matched none of the include
 patterns. The names are relative to the base directory. This involves
 performing a slow scan if one has not already been completed.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html#getNotIncludedDirectories()">getNotIncludedDirectories</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the names of the directories which matched none of the include
 patterns.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#slowScan()"><CODE>slowScan()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getExcludedDirectories()"><!-- --></A><H3>
getExcludedDirectories</H3>
<PRE>
public java.lang.String[] <B>getExcludedDirectories</B>()</PRE>
<DL>
<DD>Return the names of the directories which matched at least one of the
 include patterns and at least one of the exclude patterns.
 The names are relative to the base directory. This involves
 performing a slow scan if one has not already been completed.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html#getExcludedDirectories()">getExcludedDirectories</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the names of the directories which matched at least one of the
 include patterns and at least one of the exclude patterns.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#slowScan()"><CODE>slowScan()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getDeselectedDirectories()"><!-- --></A><H3>
getDeselectedDirectories</H3>
<PRE>
public java.lang.String[] <B>getDeselectedDirectories</B>()</PRE>
<DL>
<DD><p>Return the names of the directories which were selected out and
 therefore not ultimately included.</p>

 <p>The names are relative to the base directory. This involves
 performing a slow scan if one has not already been completed.</p>
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/types/selectors/SelectorScanner.html#getDeselectedDirectories()">getDeselectedDirectories</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/types/selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the names of the directories which were deselected.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html#slowScan()"><CODE>slowScan()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="addDefaultExcludes()"><!-- --></A><H3>
addDefaultExcludes</H3>
<PRE>
public void <B>addDefaultExcludes</B>()</PRE>
<DL>
<DD>Add default exclusions to the current exclusions set.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html#addDefaultExcludes()">addDefaultExcludes</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A></CODE></DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="getResource(java.lang.String)"><!-- --></A><H3>
getResource</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A> <B>getResource</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Get the named resource.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/types/ResourceFactory.html#getResource(java.lang.String)">getResource</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - path name of the file relative to the dir attribute.
<DT><B>Returns:</B><DD>the resource with the given name.<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/Diagnostics.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/DynamicAttribute.html" title="interface in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/DirectoryScanner.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="DirectoryScanner.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
