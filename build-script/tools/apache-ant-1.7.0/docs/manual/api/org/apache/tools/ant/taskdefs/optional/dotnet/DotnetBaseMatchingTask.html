<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:27 EST 2006 -->
<TITLE>
DotnetBaseMatchingTask (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.optional.dotnet.DotnetBaseMatchingTask class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="DotnetBaseMatchingTask (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/CSharp.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetCompile.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="DotnetBaseMatchingTask.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.optional.dotnet</FONT>
<BR>
Class DotnetBaseMatchingTask</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.MatchingTask</A>
              <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.optional.dotnet.DotnetBaseMatchingTask</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable, <A HREF="../../../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetCompile.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">DotnetCompile</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/Ilasm.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">Ilasm</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>DotnetBaseMatchingTask</B><DT>extends <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></DL>
</PRE>

<P>
refactoring of some stuff so that different things (like ILASM)
 can use shared code.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html#filesets">filesets</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;filesets of file to compile</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html#isWindows">isWindows</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Are we running on Windows?</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html#outputFile">outputFile</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;output file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html#srcDir">srcDir</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;source directory upon which the search pattern is applied</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.MatchingTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#fileset">fileset</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html#DotnetBaseMatchingTask()">DotnetBaseMatchingTask</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html#addFilesAndExecute(org.apache.tools.ant.taskdefs.optional.dotnet.NetCommand, boolean)">addFilesAndExecute</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/NetCommand.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">NetCommand</A>&nbsp;command,
                   boolean&nbsp;ignoreTimestamps)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;finish off the command by adding all dependent files, execute</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html#addFilesToCommand(java.util.Hashtable, org.apache.tools.ant.taskdefs.optional.dotnet.NetCommand)">addFilesToCommand</A></B>(java.util.Hashtable&nbsp;filesToBuild,
                  <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/NetCommand.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">NetCommand</A>&nbsp;command)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add the list of files to a command</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html#addSrc(org.apache.tools.ant.types.FileSet)">addSrc</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;src)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a new source directory to the compile</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html#buildFileList(org.apache.tools.ant.taskdefs.optional.dotnet.NetCommand, java.util.Hashtable, long)">buildFileList</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/NetCommand.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">NetCommand</A>&nbsp;command,
              java.util.Hashtable&nbsp;filesToBuild,
              long&nbsp;outputTimestamp)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;create the list of files</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html#getDestFile()">getDestFile</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;get the destination file</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html#getOutputFileTimestamp()">getOutputFileTimestamp</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;determine the timestamp of the output file</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html#getSrcDir()">getSrcDir</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Overridden because we need to be able to set the srcDir.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html#setDestFile(java.io.File)">setDestFile</A></B>(java.io.File&nbsp;file)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the name of exe/library to create.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html#setSrcDir(java.io.File)">setSrcDir</A></B>(java.io.File&nbsp;srcDirName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the source directory of the files to be compiled.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.MatchingTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createExclude()">createExclude</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createExcludesFile()">createExcludesFile</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createInclude()">createInclude</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createIncludesFile()">createIncludesFile</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createPatternSet()">createPatternSet</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#hasSelectors()">hasSelectors</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#selectorCount()">selectorCount</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#selectorElements()">selectorElements</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setExcludes(java.lang.String)">setExcludes</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setIncludes(java.lang.String)">setIncludes</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#XsetItems(java.lang.String)">XsetItems</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#execute()">execute</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="outputFile"><!-- --></A><H3>
outputFile</H3>
<PRE>
protected java.io.File <B>outputFile</B></PRE>
<DL>
<DD>output file. If not supplied this is derived from the source file
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="filesets"><!-- --></A><H3>
filesets</H3>
<PRE>
protected java.util.Vector <B>filesets</B></PRE>
<DL>
<DD>filesets of file to compile
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="srcDir"><!-- --></A><H3>
srcDir</H3>
<PRE>
protected java.io.File <B>srcDir</B></PRE>
<DL>
<DD>source directory upon which the search pattern is applied
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="isWindows"><!-- --></A><H3>
isWindows</H3>
<PRE>
protected static final boolean <B>isWindows</B></PRE>
<DL>
<DD>Are we running on Windows?
<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="DotnetBaseMatchingTask()"><!-- --></A><H3>
DotnetBaseMatchingTask</H3>
<PRE>
public <B>DotnetBaseMatchingTask</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="getSrcDir()"><!-- --></A><H3>
getSrcDir</H3>
<PRE>
public java.io.File <B>getSrcDir</B>()</PRE>
<DL>
<DD>Overridden because we need to be able to set the srcDir.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the source directory.</DL>
</DD>
</DL>
<HR>

<A NAME="setSrcDir(java.io.File)"><!-- --></A><H3>
setSrcDir</H3>
<PRE>
public void <B>setSrcDir</B>(java.io.File&nbsp;srcDirName)</PRE>
<DL>
<DD>Set the source directory of the files to be compiled.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>srcDirName</CODE> - The new SrcDir value</DL>
</DD>
</DL>
<HR>

<A NAME="setDestFile(java.io.File)"><!-- --></A><H3>
setDestFile</H3>
<PRE>
public void <B>setDestFile</B>(java.io.File&nbsp;file)</PRE>
<DL>
<DD>Set the name of exe/library to create.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>file</CODE> - The new outputFile value</DL>
</DD>
</DL>
<HR>

<A NAME="addSrc(org.apache.tools.ant.types.FileSet)"><!-- --></A><H3>
addSrc</H3>
<PRE>
public void <B>addSrc</B>(<A HREF="../../../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;src)</PRE>
<DL>
<DD>add a new source directory to the compile
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>src</CODE> - a fileset.</DL>
</DD>
</DL>
<HR>

<A NAME="getDestFile()"><!-- --></A><H3>
getDestFile</H3>
<PRE>
public java.io.File <B>getDestFile</B>()</PRE>
<DL>
<DD>get the destination file
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the dest file or null for not assigned</DL>
</DD>
</DL>
<HR>

<A NAME="buildFileList(org.apache.tools.ant.taskdefs.optional.dotnet.NetCommand, java.util.Hashtable, long)"><!-- --></A><H3>
buildFileList</H3>
<PRE>
protected int <B>buildFileList</B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/NetCommand.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">NetCommand</A>&nbsp;command,
                            java.util.Hashtable&nbsp;filesToBuild,
                            long&nbsp;outputTimestamp)</PRE>
<DL>
<DD>create the list of files
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>command</CODE> - the command to create the files for.<DD><CODE>filesToBuild</CODE> - vector to add files to<DD><CODE>outputTimestamp</CODE> - timestamp to compare against
<DT><B>Returns:</B><DD>number of files out of date</DL>
</DD>
</DL>
<HR>

<A NAME="addFilesToCommand(java.util.Hashtable, org.apache.tools.ant.taskdefs.optional.dotnet.NetCommand)"><!-- --></A><H3>
addFilesToCommand</H3>
<PRE>
protected void <B>addFilesToCommand</B>(java.util.Hashtable&nbsp;filesToBuild,
                                 <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/NetCommand.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">NetCommand</A>&nbsp;command)</PRE>
<DL>
<DD>add the list of files to a command
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filesToBuild</CODE> - vector of files<DD><CODE>command</CODE> - the command to append to</DL>
</DD>
</DL>
<HR>

<A NAME="getOutputFileTimestamp()"><!-- --></A><H3>
getOutputFileTimestamp</H3>
<PRE>
protected long <B>getOutputFileTimestamp</B>()</PRE>
<DL>
<DD>determine the timestamp of the output file
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a timestamp or 0 for no output file known/exists</DL>
</DD>
</DL>
<HR>

<A NAME="addFilesAndExecute(org.apache.tools.ant.taskdefs.optional.dotnet.NetCommand, boolean)"><!-- --></A><H3>
addFilesAndExecute</H3>
<PRE>
protected void <B>addFilesAndExecute</B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/NetCommand.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">NetCommand</A>&nbsp;command,
                                  boolean&nbsp;ignoreTimestamps)</PRE>
<DL>
<DD>finish off the command by adding all dependent files, execute
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>command</CODE> - the command to update.<DD><CODE>ignoreTimestamps</CODE> - not used.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/CSharp.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetCompile.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="DotnetBaseMatchingTask.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
