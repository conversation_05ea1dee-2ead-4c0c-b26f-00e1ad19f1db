<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:24 EST 2006 -->
<TITLE>
PreSetDef.PreSetDefinition (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.PreSetDef.PreSetDefinition class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="PreSetDef.PreSetDefinition (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="PreSetDef.PreSetDefinition.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class PreSetDef.PreSetDefinition</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">org.apache.tools.ant.AntTypeDefinition</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.PreSetDef.PreSetDefinition</B>
</PRE>
<DL>
<DT><B>Enclosing class:</B><DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.html" title="class in org.apache.tools.ant.taskdefs">PreSetDef</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public static class <B>PreSetDef.PreSetDefinition</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></DL>
</PRE>

<P>
This class contains the unknown element and the object
 that is predefined.
<P>

<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant"><CODE>AntTypeDefinition</CODE></A></DL>
<HR>

<P>

<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html#PreSetDef.PreSetDefinition(org.apache.tools.ant.AntTypeDefinition, org.apache.tools.ant.UnknownElement)">PreSetDef.PreSetDefinition</A></B>(<A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A>&nbsp;parent,
                           <A HREF="../../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A>&nbsp;el)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new <code>PresetDefinition</code> instance.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html#checkClass(org.apache.tools.ant.Project)">checkClass</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Check if the attributes are correct.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html#create(org.apache.tools.ant.Project)">create</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Fake create an object, used by IntrospectionHelper and UnknownElement
 to see that this is a predefined object.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html#createObject(org.apache.tools.ant.Project)">createObject</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create an instance of the definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.ClassLoader</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html#getClassLoader()">getClassLoader</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the classloader for this definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html#getClassName()">getClassName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the classname of the definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html#getExposedClass(org.apache.tools.ant.Project)">getExposedClass</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the exposed class for this definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html#getPreSets()">getPreSets</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the preset values.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html#getTypeClass(org.apache.tools.ant.Project)">getTypeClass</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the definition class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html#sameDefinition(org.apache.tools.ant.AntTypeDefinition, org.apache.tools.ant.Project)">sameDefinition</A></B>(<A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A>&nbsp;other,
               <A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Equality method for this definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html#setAdapterClass(java.lang.Class)">setAdapterClass</A></B>(java.lang.Class&nbsp;adapterClass)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the adapter class for this definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html#setAdaptToClass(java.lang.Class)">setAdaptToClass</A></B>(java.lang.Class&nbsp;adaptToClass)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the assignable class for this definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html#setClass(java.lang.Class)">setClass</A></B>(java.lang.Class&nbsp;clazz)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Override so that it is not allowed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html#setClassLoader(java.lang.ClassLoader)">setClassLoader</A></B>(java.lang.ClassLoader&nbsp;classLoader)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the classloader to use to create an instance
 of the definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html#setClassName(java.lang.String)">setClassName</A></B>(java.lang.String&nbsp;className)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Override so that it is not allowed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html#similarDefinition(org.apache.tools.ant.AntTypeDefinition, org.apache.tools.ant.Project)">similarDefinition</A></B>(<A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A>&nbsp;other,
                  <A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Similar method for this definition.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.AntTypeDefinition"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#getName()">getName</A>, <A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#innerCreateAndSet(java.lang.Class, org.apache.tools.ant.Project)">innerCreateAndSet</A>, <A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#innerGetTypeClass()">innerGetTypeClass</A>, <A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#setName(java.lang.String)">setName</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="PreSetDef.PreSetDefinition(org.apache.tools.ant.AntTypeDefinition, org.apache.tools.ant.UnknownElement)"><!-- --></A><H3>
PreSetDef.PreSetDefinition</H3>
<PRE>
public <B>PreSetDef.PreSetDefinition</B>(<A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A>&nbsp;parent,
                                  <A HREF="../../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A>&nbsp;el)</PRE>
<DL>
<DD>Creates a new <code>PresetDefinition</code> instance.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>parent</CODE> - The parent of this predefinition.<DD><CODE>el</CODE> - The predefined attributes, nested elements and text.</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setClass(java.lang.Class)"><!-- --></A><H3>
setClass</H3>
<PRE>
public void <B>setClass</B>(java.lang.Class&nbsp;clazz)</PRE>
<DL>
<DD>Override so that it is not allowed.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#setClass(java.lang.Class)">setClass</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>clazz</CODE> - a <code>Class</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setClassName(java.lang.String)"><!-- --></A><H3>
setClassName</H3>
<PRE>
public void <B>setClassName</B>(java.lang.String&nbsp;className)</PRE>
<DL>
<DD>Override so that it is not allowed.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#setClassName(java.lang.String)">setClassName</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>className</CODE> - a <code>String</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="getClassName()"><!-- --></A><H3>
getClassName</H3>
<PRE>
public java.lang.String <B>getClassName</B>()</PRE>
<DL>
<DD>Get the classname of the definition.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#getClassName()">getClassName</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the name of the class of this definition.</DL>
</DD>
</DL>
<HR>

<A NAME="setAdapterClass(java.lang.Class)"><!-- --></A><H3>
setAdapterClass</H3>
<PRE>
public void <B>setAdapterClass</B>(java.lang.Class&nbsp;adapterClass)</PRE>
<DL>
<DD>Set the adapter class for this definition.
 NOT Supported
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#setAdapterClass(java.lang.Class)">setAdapterClass</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>adapterClass</CODE> - the adapterClass.</DL>
</DD>
</DL>
<HR>

<A NAME="setAdaptToClass(java.lang.Class)"><!-- --></A><H3>
setAdaptToClass</H3>
<PRE>
public void <B>setAdaptToClass</B>(java.lang.Class&nbsp;adaptToClass)</PRE>
<DL>
<DD>Set the assignable class for this definition.
 NOT SUPPORTED
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#setAdaptToClass(java.lang.Class)">setAdaptToClass</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>adaptToClass</CODE> - the assignable class.</DL>
</DD>
</DL>
<HR>

<A NAME="setClassLoader(java.lang.ClassLoader)"><!-- --></A><H3>
setClassLoader</H3>
<PRE>
public void <B>setClassLoader</B>(java.lang.ClassLoader&nbsp;classLoader)</PRE>
<DL>
<DD>Set the classloader to use to create an instance
 of the definition.
 NOT SUPPORTED
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#setClassLoader(java.lang.ClassLoader)">setClassLoader</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classLoader</CODE> - the classLoader.</DL>
</DD>
</DL>
<HR>

<A NAME="getClassLoader()"><!-- --></A><H3>
getClassLoader</H3>
<PRE>
public java.lang.ClassLoader <B>getClassLoader</B>()</PRE>
<DL>
<DD>Get the classloader for this definition.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#getClassLoader()">getClassLoader</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the classloader for this definition.</DL>
</DD>
</DL>
<HR>

<A NAME="getExposedClass(org.apache.tools.ant.Project)"><!-- --></A><H3>
getExposedClass</H3>
<PRE>
public java.lang.Class <B>getExposedClass</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Get the exposed class for this definition.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#getExposedClass(org.apache.tools.ant.Project)">getExposedClass</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the current project.
<DT><B>Returns:</B><DD>the exposed class.</DL>
</DD>
</DL>
<HR>

<A NAME="getTypeClass(org.apache.tools.ant.Project)"><!-- --></A><H3>
getTypeClass</H3>
<PRE>
public java.lang.Class <B>getTypeClass</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Get the definition class.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#getTypeClass(org.apache.tools.ant.Project)">getTypeClass</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the current project.
<DT><B>Returns:</B><DD>the type of the definition.</DL>
</DD>
</DL>
<HR>

<A NAME="checkClass(org.apache.tools.ant.Project)"><!-- --></A><H3>
checkClass</H3>
<PRE>
public void <B>checkClass</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Check if the attributes are correct.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#checkClass(org.apache.tools.ant.Project)">checkClass</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the current project.</DL>
</DD>
</DL>
<HR>

<A NAME="createObject(org.apache.tools.ant.Project)"><!-- --></A><H3>
createObject</H3>
<PRE>
public java.lang.Object <B>createObject</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Create an instance of the definition. The instance may be wrapped
 in a proxy class. This is a special version of create for
 IntrospectionHelper and UnknownElement.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the current project.
<DT><B>Returns:</B><DD>the created object.</DL>
</DD>
</DL>
<HR>

<A NAME="getPreSets()"><!-- --></A><H3>
getPreSets</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A> <B>getPreSets</B>()</PRE>
<DL>
<DD>Get the preset values.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the predefined attributes, elements and text as
         an UnknownElement.</DL>
</DD>
</DL>
<HR>

<A NAME="create(org.apache.tools.ant.Project)"><!-- --></A><H3>
create</H3>
<PRE>
public java.lang.Object <B>create</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Fake create an object, used by IntrospectionHelper and UnknownElement
 to see that this is a predefined object.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#create(org.apache.tools.ant.Project)">create</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the current project.
<DT><B>Returns:</B><DD>this object.</DL>
</DD>
</DL>
<HR>

<A NAME="sameDefinition(org.apache.tools.ant.AntTypeDefinition, org.apache.tools.ant.Project)"><!-- --></A><H3>
sameDefinition</H3>
<PRE>
public boolean <B>sameDefinition</B>(<A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A>&nbsp;other,
                              <A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Equality method for this definition.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#sameDefinition(org.apache.tools.ant.AntTypeDefinition, org.apache.tools.ant.Project)">sameDefinition</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>other</CODE> - another definition.<DD><CODE>project</CODE> - the current project.
<DT><B>Returns:</B><DD>true if the definitions are the same.</DL>
</DD>
</DL>
<HR>

<A NAME="similarDefinition(org.apache.tools.ant.AntTypeDefinition, org.apache.tools.ant.Project)"><!-- --></A><H3>
similarDefinition</H3>
<PRE>
public boolean <B>similarDefinition</B>(<A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A>&nbsp;other,
                                 <A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Similar method for this definition.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html#similarDefinition(org.apache.tools.ant.AntTypeDefinition, org.apache.tools.ant.Project)">similarDefinition</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>other</CODE> - another definition.<DD><CODE>project</CODE> - the current project.
<DT><B>Returns:</B><DD>true if the definitions are similar.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="PreSetDef.PreSetDefinition.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
