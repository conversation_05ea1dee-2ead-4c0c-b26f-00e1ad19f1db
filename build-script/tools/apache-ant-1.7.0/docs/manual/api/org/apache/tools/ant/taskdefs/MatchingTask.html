<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:23 EST 2006 -->
<TITLE>
MatchingTask (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.MatchingTask class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="MatchingTask (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestTask.Mode.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Mkdir.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/MatchingTask.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="MatchingTask.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class MatchingTask</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.MatchingTask</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable, <A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/Cab.html" title="class in org.apache.tools.ant.taskdefs.optional">Cab</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Checksum.html" title="class in org.apache.tools.ant.taskdefs">Checksum</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copydir.html" title="class in org.apache.tools.ant.taskdefs">Copydir</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/ejb/DDCreator.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">DDCreator</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Delete.html" title="class in org.apache.tools.ant.taskdefs">Delete</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/depend/Depend.html" title="class in org.apache.tools.ant.taskdefs.optional.depend">Depend</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/DependSet.html" title="class in org.apache.tools.ant.taskdefs">DependSet</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/dotnet/DotnetBaseMatchingTask.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">DotnetBaseMatchingTask</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/ejb/Ejbc.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">Ejbc</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/FixCRLF.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/image/Image.html" title="class in org.apache.tools.ant.taskdefs.optional.image">Image</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/jlink/JlinkTask.html" title="class in org.apache.tools.ant.taskdefs.optional.jlink">JlinkTask</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/Native2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional">Native2Ascii</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/NetRexxC.html" title="class in org.apache.tools.ant.taskdefs.optional">NetRexxC</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/RenameExtensions.html" title="class in org.apache.tools.ant.taskdefs.optional">RenameExtensions</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Replace.html" title="class in org.apache.tools.ant.taskdefs">Replace</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html" title="class in org.apache.tools.ant.taskdefs">Rmic</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.html" title="class in org.apache.tools.ant.taskdefs">Tar</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/i18n/Translate.html" title="class in org.apache.tools.ant.taskdefs.optional.i18n">Translate</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/optional/jsp/WLJspc.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">WLJspc</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public abstract class <B>MatchingTask</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A><DT>implements <A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></DL>
</PRE>

<P>
This is an abstract task that should be used by all those tasks that
 require to include or exclude files based on pattern matching.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.1</DD>
</DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#fileset">fileset</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#MatchingTask()">MatchingTask</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add an arbitary selector</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/AndSelector.html" title="class in org.apache.tools.ant.types.selectors">AndSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add an "And" selector entry on the selector list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/ContainsSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a contains selector entry on the selector list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/ContainsRegexpSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsRegexpSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a regular expression selector entry on the selector list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/ExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">ExtendSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add an extended selector entry on the selector list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/DateSelector.html" title="class in org.apache.tools.ant.types.selectors">DateSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a selector date entry on the selector list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/DependSelector.html" title="class in org.apache.tools.ant.types.selectors">DependSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a depends selector entry on the selector list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/DepthSelector.html" title="class in org.apache.tools.ant.types.selectors">DepthSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a depth selector entry on the selector list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/DifferentSelector.html" title="class in org.apache.tools.ant.types.selectors">DifferentSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a type selector entry on the type list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/FilenameSelector.html" title="class in org.apache.tools.ant.types.selectors">FilenameSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a selector filename entry on the selector list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/MajoritySelector.html" title="class in org.apache.tools.ant.types.selectors">MajoritySelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a majority selector entry on the selector list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/modifiedselector/ModifiedSelector.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add the modified selector</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/NoneSelector.html" title="class in org.apache.tools.ant.types.selectors">NoneSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a "None" selector entry on the selector list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/NotSelector.html" title="class in org.apache.tools.ant.types.selectors">NotSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a "Not" selector entry on the selector list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/OrSelector.html" title="class in org.apache.tools.ant.types.selectors">OrSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add an "Or" selector entry on the selector list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/PresentSelector.html" title="class in org.apache.tools.ant.types.selectors">PresentSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a present selector entry on the selector list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectSelector.html" title="class in org.apache.tools.ant.types.selectors">SelectSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a "Select" selector entry on the selector list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/SizeSelector.html" title="class in org.apache.tools.ant.types.selectors">SizeSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a selector size entry on the selector list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/TypeSelector.html" title="class in org.apache.tools.ant.types.selectors">TypeSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a type selector entry on the type list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</A>&nbsp;selector)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a new selector into this container.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createExclude()">createExclude</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a name entry on the exclude list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createExcludesFile()">createExcludesFile</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a name entry on the include files list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createInclude()">createInclude</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a name entry on the include list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createIncludesFile()">createIncludesFile</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a name entry on the include files list</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/PatternSet.html" title="class in org.apache.tools.ant.types">PatternSet</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createPatternSet()">createPatternSet</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a set of patterns</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</A></B>(java.io.File&nbsp;baseDir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the directory scanner needed to access the files to process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Accessor for the implicit fileset.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</A>[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the set of selectors as an array.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#hasSelectors()">hasSelectors</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicates whether there are any selectors here.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#selectorCount()">selectorCount</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gives the count of the number of selectors in this container</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Enumeration</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#selectorElements()">selectorElements</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns an enumerator for accessing the set of selectors.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</A></B>(boolean&nbsp;isCaseSensitive)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets case sensitivity of the file system</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</A></B>(boolean&nbsp;useDefaultExcludes)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets whether default exclusions should be used or not.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setExcludes(java.lang.String)">setExcludes</A></B>(java.lang.String&nbsp;excludes)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the set of exclude patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</A></B>(java.io.File&nbsp;excludesfile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the name of the file containing the includes patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</A></B>(boolean&nbsp;followSymlinks)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets whether or not symbolic links should be followed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setIncludes(java.lang.String)">setIncludes</A></B>(java.lang.String&nbsp;includes)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the set of include patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</A></B>(java.io.File&nbsp;includesfile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the name of the file containing the includes patterns.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the project object of this component..</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</A></B>(java.lang.String&nbsp;ignoreString)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;List of filenames and directory names to not include.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#XsetItems(java.lang.String)">XsetItems</A></B>(java.lang.String&nbsp;itemString)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set this to be the items in the base directory that you want to be
 included.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#execute()">execute</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="fileset"><!-- --></A><H3>
fileset</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A> <B>fileset</B></PRE>
<DL>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="MatchingTask()"><!-- --></A><H3>
MatchingTask</H3>
<PRE>
public <B>MatchingTask</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setProject(org.apache.tools.ant.Project)"><!-- --></A><H3>
setProject</H3>
<PRE>
public void <B>setProject</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Sets the project object of this component. This method is used by
 Project when a component is added to it so that the component has
 access to the functions of the project. It should not be used
 for any other purpose..
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - Project in whose scope this component belongs.
                Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="createInclude()"><!-- --></A><H3>
createInclude</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</A> <B>createInclude</B>()</PRE>
<DL>
<DD>add a name entry on the include list
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a NameEntry object to be configured</DL>
</DD>
</DL>
<HR>

<A NAME="createIncludesFile()"><!-- --></A><H3>
createIncludesFile</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</A> <B>createIncludesFile</B>()</PRE>
<DL>
<DD>add a name entry on the include files list
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>an NameEntry object to be configured</DL>
</DD>
</DL>
<HR>

<A NAME="createExclude()"><!-- --></A><H3>
createExclude</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</A> <B>createExclude</B>()</PRE>
<DL>
<DD>add a name entry on the exclude list
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>an NameEntry object to be configured</DL>
</DD>
</DL>
<HR>

<A NAME="createExcludesFile()"><!-- --></A><H3>
createExcludesFile</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/PatternSet.NameEntry.html" title="class in org.apache.tools.ant.types">PatternSet.NameEntry</A> <B>createExcludesFile</B>()</PRE>
<DL>
<DD>add a name entry on the include files list
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>an NameEntry object to be configured</DL>
</DD>
</DL>
<HR>

<A NAME="createPatternSet()"><!-- --></A><H3>
createPatternSet</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/PatternSet.html" title="class in org.apache.tools.ant.types">PatternSet</A> <B>createPatternSet</B>()</PRE>
<DL>
<DD>add a set of patterns
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>PatternSet object to be configured</DL>
</DD>
</DL>
<HR>

<A NAME="setIncludes(java.lang.String)"><!-- --></A><H3>
setIncludes</H3>
<PRE>
public void <B>setIncludes</B>(java.lang.String&nbsp;includes)</PRE>
<DL>
<DD>Sets the set of include patterns. Patterns may be separated by a comma
 or a space.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>includes</CODE> - the string containing the include patterns</DL>
</DD>
</DL>
<HR>

<A NAME="XsetItems(java.lang.String)"><!-- --></A><H3>
XsetItems</H3>
<PRE>
public void <B>XsetItems</B>(java.lang.String&nbsp;itemString)</PRE>
<DL>
<DD>Set this to be the items in the base directory that you want to be
 included. You can also specify "*" for the items (ie: items="*")
 and it will include all the items in the base directory.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>itemString</CODE> - the string containing the files to include.</DL>
</DD>
</DL>
<HR>

<A NAME="setExcludes(java.lang.String)"><!-- --></A><H3>
setExcludes</H3>
<PRE>
public void <B>setExcludes</B>(java.lang.String&nbsp;excludes)</PRE>
<DL>
<DD>Sets the set of exclude patterns. Patterns may be separated by a comma
 or a space.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>excludes</CODE> - the string containing the exclude patterns</DL>
</DD>
</DL>
<HR>

<A NAME="XsetIgnore(java.lang.String)"><!-- --></A><H3>
XsetIgnore</H3>
<PRE>
public void <B>XsetIgnore</B>(java.lang.String&nbsp;ignoreString)</PRE>
<DL>
<DD>List of filenames and directory names to not include. They should be
 either , or " " (space) separated. The ignored files will be logged.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ignoreString</CODE> - the string containing the files to ignore.</DL>
</DD>
</DL>
<HR>

<A NAME="setDefaultexcludes(boolean)"><!-- --></A><H3>
setDefaultexcludes</H3>
<PRE>
public void <B>setDefaultexcludes</B>(boolean&nbsp;useDefaultExcludes)</PRE>
<DL>
<DD>Sets whether default exclusions should be used or not.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>useDefaultExcludes</CODE> - "true"|"on"|"yes" when default exclusions
                           should be used, "false"|"off"|"no" when they
                           shouldn't be used.</DL>
</DD>
</DL>
<HR>

<A NAME="getDirectoryScanner(java.io.File)"><!-- --></A><H3>
getDirectoryScanner</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</A> <B>getDirectoryScanner</B>(java.io.File&nbsp;baseDir)</PRE>
<DL>
<DD>Returns the directory scanner needed to access the files to process.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>baseDir</CODE> - the base directory to use with the fileset
<DT><B>Returns:</B><DD>a directory scanner</DL>
</DD>
</DL>
<HR>

<A NAME="setIncludesfile(java.io.File)"><!-- --></A><H3>
setIncludesfile</H3>
<PRE>
public void <B>setIncludesfile</B>(java.io.File&nbsp;includesfile)</PRE>
<DL>
<DD>Sets the name of the file containing the includes patterns.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>includesfile</CODE> - A string containing the filename to fetch
 the include patterns from.</DL>
</DD>
</DL>
<HR>

<A NAME="setExcludesfile(java.io.File)"><!-- --></A><H3>
setExcludesfile</H3>
<PRE>
public void <B>setExcludesfile</B>(java.io.File&nbsp;excludesfile)</PRE>
<DL>
<DD>Sets the name of the file containing the includes patterns.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>excludesfile</CODE> - A string containing the filename to fetch
 the include patterns from.</DL>
</DD>
</DL>
<HR>

<A NAME="setCaseSensitive(boolean)"><!-- --></A><H3>
setCaseSensitive</H3>
<PRE>
public void <B>setCaseSensitive</B>(boolean&nbsp;isCaseSensitive)</PRE>
<DL>
<DD>Sets case sensitivity of the file system
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>isCaseSensitive</CODE> - "true"|"on"|"yes" if file system is case
                           sensitive, "false"|"off"|"no" when not.</DL>
</DD>
</DL>
<HR>

<A NAME="setFollowSymlinks(boolean)"><!-- --></A><H3>
setFollowSymlinks</H3>
<PRE>
public void <B>setFollowSymlinks</B>(boolean&nbsp;followSymlinks)</PRE>
<DL>
<DD>Sets whether or not symbolic links should be followed.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>followSymlinks</CODE> - whether or not symbolic links should be followed</DL>
</DD>
</DL>
<HR>

<A NAME="hasSelectors()"><!-- --></A><H3>
hasSelectors</H3>
<PRE>
public boolean <B>hasSelectors</B>()</PRE>
<DL>
<DD>Indicates whether there are any selectors here.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#hasSelectors()">hasSelectors</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>whether any selectors are in this container</DL>
</DD>
</DL>
<HR>

<A NAME="selectorCount()"><!-- --></A><H3>
selectorCount</H3>
<PRE>
public int <B>selectorCount</B>()</PRE>
<DL>
<DD>Gives the count of the number of selectors in this container
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#selectorCount()">selectorCount</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the number of selectors in this container</DL>
</DD>
</DL>
<HR>

<A NAME="getSelectors(org.apache.tools.ant.Project)"><!-- --></A><H3>
getSelectors</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</A>[] <B>getSelectors</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</PRE>
<DL>
<DD>Returns the set of selectors as an array.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the current project
<DT><B>Returns:</B><DD>an array of selectors in this container</DL>
</DD>
</DL>
<HR>

<A NAME="selectorElements()"><!-- --></A><H3>
selectorElements</H3>
<PRE>
public java.util.Enumeration <B>selectorElements</B>()</PRE>
<DL>
<DD>Returns an enumerator for accessing the set of selectors.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#selectorElements()">selectorElements</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>an enumerator that goes through each of the selectors</DL>
</DD>
</DL>
<HR>

<A NAME="appendSelector(org.apache.tools.ant.types.selectors.FileSelector)"><!-- --></A><H3>
appendSelector</H3>
<PRE>
public void <B>appendSelector</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>Add a new selector into this container.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the new selector to add</DL>
</DD>
</DL>
<HR>

<A NAME="addSelector(org.apache.tools.ant.types.selectors.SelectSelector)"><!-- --></A><H3>
addSelector</H3>
<PRE>
public void <B>addSelector</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectSelector.html" title="class in org.apache.tools.ant.types.selectors">SelectSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add a "Select" selector entry on the selector list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add</DL>
</DD>
</DL>
<HR>

<A NAME="addAnd(org.apache.tools.ant.types.selectors.AndSelector)"><!-- --></A><H3>
addAnd</H3>
<PRE>
public void <B>addAnd</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/AndSelector.html" title="class in org.apache.tools.ant.types.selectors">AndSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add an "And" selector entry on the selector list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add</DL>
</DD>
</DL>
<HR>

<A NAME="addOr(org.apache.tools.ant.types.selectors.OrSelector)"><!-- --></A><H3>
addOr</H3>
<PRE>
public void <B>addOr</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/OrSelector.html" title="class in org.apache.tools.ant.types.selectors">OrSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add an "Or" selector entry on the selector list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add</DL>
</DD>
</DL>
<HR>

<A NAME="addNot(org.apache.tools.ant.types.selectors.NotSelector)"><!-- --></A><H3>
addNot</H3>
<PRE>
public void <B>addNot</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/NotSelector.html" title="class in org.apache.tools.ant.types.selectors">NotSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add a "Not" selector entry on the selector list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add</DL>
</DD>
</DL>
<HR>

<A NAME="addNone(org.apache.tools.ant.types.selectors.NoneSelector)"><!-- --></A><H3>
addNone</H3>
<PRE>
public void <B>addNone</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/NoneSelector.html" title="class in org.apache.tools.ant.types.selectors">NoneSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add a "None" selector entry on the selector list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add</DL>
</DD>
</DL>
<HR>

<A NAME="addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)"><!-- --></A><H3>
addMajority</H3>
<PRE>
public void <B>addMajority</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/MajoritySelector.html" title="class in org.apache.tools.ant.types.selectors">MajoritySelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add a majority selector entry on the selector list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add</DL>
</DD>
</DL>
<HR>

<A NAME="addDate(org.apache.tools.ant.types.selectors.DateSelector)"><!-- --></A><H3>
addDate</H3>
<PRE>
public void <B>addDate</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/DateSelector.html" title="class in org.apache.tools.ant.types.selectors">DateSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add a selector date entry on the selector list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add</DL>
</DD>
</DL>
<HR>

<A NAME="addSize(org.apache.tools.ant.types.selectors.SizeSelector)"><!-- --></A><H3>
addSize</H3>
<PRE>
public void <B>addSize</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/SizeSelector.html" title="class in org.apache.tools.ant.types.selectors">SizeSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add a selector size entry on the selector list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add</DL>
</DD>
</DL>
<HR>

<A NAME="addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)"><!-- --></A><H3>
addFilename</H3>
<PRE>
public void <B>addFilename</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/FilenameSelector.html" title="class in org.apache.tools.ant.types.selectors">FilenameSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add a selector filename entry on the selector list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add</DL>
</DD>
</DL>
<HR>

<A NAME="addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)"><!-- --></A><H3>
addCustom</H3>
<PRE>
public void <B>addCustom</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/ExtendSelector.html" title="class in org.apache.tools.ant.types.selectors">ExtendSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add an extended selector entry on the selector list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add</DL>
</DD>
</DL>
<HR>

<A NAME="addContains(org.apache.tools.ant.types.selectors.ContainsSelector)"><!-- --></A><H3>
addContains</H3>
<PRE>
public void <B>addContains</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/ContainsSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add a contains selector entry on the selector list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add</DL>
</DD>
</DL>
<HR>

<A NAME="addPresent(org.apache.tools.ant.types.selectors.PresentSelector)"><!-- --></A><H3>
addPresent</H3>
<PRE>
public void <B>addPresent</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/PresentSelector.html" title="class in org.apache.tools.ant.types.selectors">PresentSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add a present selector entry on the selector list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add</DL>
</DD>
</DL>
<HR>

<A NAME="addDepth(org.apache.tools.ant.types.selectors.DepthSelector)"><!-- --></A><H3>
addDepth</H3>
<PRE>
public void <B>addDepth</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/DepthSelector.html" title="class in org.apache.tools.ant.types.selectors">DepthSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add a depth selector entry on the selector list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add</DL>
</DD>
</DL>
<HR>

<A NAME="addDepend(org.apache.tools.ant.types.selectors.DependSelector)"><!-- --></A><H3>
addDepend</H3>
<PRE>
public void <B>addDepend</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/DependSelector.html" title="class in org.apache.tools.ant.types.selectors">DependSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add a depends selector entry on the selector list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add</DL>
</DD>
</DL>
<HR>

<A NAME="addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)"><!-- --></A><H3>
addContainsRegexp</H3>
<PRE>
public void <B>addContainsRegexp</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/ContainsRegexpSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsRegexpSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add a regular expression selector entry on the selector list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add</DL>
</DD>
</DL>
<HR>

<A NAME="addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)"><!-- --></A><H3>
addDifferent</H3>
<PRE>
public void <B>addDifferent</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/DifferentSelector.html" title="class in org.apache.tools.ant.types.selectors">DifferentSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add a type selector entry on the type list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add<DT><B>Since:</B></DT>
  <DD>ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addType(org.apache.tools.ant.types.selectors.TypeSelector)"><!-- --></A><H3>
addType</H3>
<PRE>
public void <B>addType</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/TypeSelector.html" title="class in org.apache.tools.ant.types.selectors">TypeSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add a type selector entry on the type list
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add<DT><B>Since:</B></DT>
  <DD>ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)"><!-- --></A><H3>
addModified</H3>
<PRE>
public void <B>addModified</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/modifiedselector/ModifiedSelector.html" title="class in org.apache.tools.ant.types.selectors.modifiedselector">ModifiedSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add the modified selector
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add<DT><B>Since:</B></DT>
  <DD>ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="add(org.apache.tools.ant.types.selectors.FileSelector)"><!-- --></A><H3>
add</H3>
<PRE>
public void <B>add</B>(<A HREF="../../../../../org/apache/tools/ant/types/selectors/FileSelector.html" title="interface in org.apache.tools.ant.types.selectors">FileSelector</A>&nbsp;selector)</PRE>
<DL>
<DD>add an arbitary selector
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>selector</CODE> - the selector to add<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getImplicitFileSet()"><!-- --></A><H3>
getImplicitFileSet</H3>
<PRE>
protected final <A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A> <B>getImplicitFileSet</B>()</PRE>
<DL>
<DD>Accessor for the implicit fileset.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the implicit fileset<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestTask.Mode.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Mkdir.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/MatchingTask.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="MatchingTask.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
