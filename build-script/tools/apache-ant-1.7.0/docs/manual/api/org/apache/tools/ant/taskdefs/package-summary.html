<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:35 EST 2006 -->
<TITLE>
org.apache.tools.ant.taskdefs (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs package">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="org.apache.tools.ant.taskdefs (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Package</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/loader/package-summary.html"><B>PREV PACKAGE</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/compilers/package-summary.html"><B>NEXT PACKAGE</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/package-summary.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="package-summary.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<H2>
Package org.apache.tools.ant.taskdefs
</H2>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Interface Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AntStructure.StructurePrinter.html" title="interface in org.apache.tools.ant.taskdefs">AntStructure.StructurePrinter</A></B></TD>
<TD>Writes the actual structure information.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A></B></TD>
<TD>Used by <code>Execute</code> to handle input and output stream of
 subprocesses.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Get.DownloadProgress.html" title="interface in org.apache.tools.ant.taskdefs">Get.DownloadProgress</A></B></TD>
<TD>Interface implemented for reporting
 progess of downloading.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</A></B></TD>
<TD>Proxy interface for XSLT processors.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLiaison2.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison2</A></B></TD>
<TD>Extended Proxy interface for XSLT processors.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLiaison3.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison3</A></B></TD>
<TD>Extends Proxy interface for XSLT processors.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLogger.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLogger</A></B></TD>
<TD>Interface to log messages for XSLT</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLoggerAware.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLoggerAware</A></B></TD>
<TD>Interface for a class that one can set an XSLTLogger on.</TD>
</TR>
</TABLE>
&nbsp;

<P>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractCvsTask</A></B></TD>
<TD>original Cvs.java 1.20

  NOTE: This implementation has been moved here from Cvs.java with
  the addition of some accessors for extensibility.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</A></B></TD>
<TD>This is factored out from <A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html" title="class in org.apache.tools.ant.taskdefs"><CODE>SignJar</CODE></A>; a base class that can be used
 for both signing and verifying JAR files using jarsigner</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Ant.html" title="class in org.apache.tools.ant.taskdefs">Ant</A></B></TD>
<TD>Build a sub-project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Ant.Reference.html" title="class in org.apache.tools.ant.taskdefs">Ant.Reference</A></B></TD>
<TD>Helper class that implements the nested &lt;reference&gt;
 element of &lt;ant&gt; and &lt;antcall&gt;.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Ant.TargetElement.html" title="class in org.apache.tools.ant.taskdefs">Ant.TargetElement</A></B></TD>
<TD>Helper class that implements the nested &lt;target&gt;
 element of &lt;ant&gt; and &lt;antcall&gt;.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Antlib.html" title="class in org.apache.tools.ant.taskdefs">Antlib</A></B></TD>
<TD>Antlib task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AntlibDefinition.html" title="class in org.apache.tools.ant.taskdefs">AntlibDefinition</A></B></TD>
<TD>Base class for tasks that that can be used in antlibs.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AntStructure.html" title="class in org.apache.tools.ant.taskdefs">AntStructure</A></B></TD>
<TD>Creates a partial DTD for Ant from the currently known tasks.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html" title="class in org.apache.tools.ant.taskdefs">Apt</A></B></TD>
<TD>Apt Task for running the Annotation processing tool for JDK 1.5.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.Option.html" title="class in org.apache.tools.ant.taskdefs">Apt.Option</A></B></TD>
<TD>The nested option element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Available.html" title="class in org.apache.tools.ant.taskdefs">Available</A></B></TD>
<TD>Will set the given property if the requested resource is available at
 runtime.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Available.FileDir.html" title="class in org.apache.tools.ant.taskdefs">Available.FileDir</A></B></TD>
<TD>EnumeratedAttribute covering the file types to be checked for, either
 file or dir.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Basename.html" title="class in org.apache.tools.ant.taskdefs">Basename</A></B></TD>
<TD>Sets a property to the base name of a specified file, optionally minus a
 suffix.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/BuildNumber.html" title="class in org.apache.tools.ant.taskdefs">BuildNumber</A></B></TD>
<TD>Read, increment, and write a build number in a file
 It will first
 attempt to read a build number from a file, then set the property
 "build.number" to the value that was read in (or 0 if no such value).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/BUnzip2.html" title="class in org.apache.tools.ant.taskdefs">BUnzip2</A></B></TD>
<TD>Expands a file that has been compressed with the BZIP2
 algorithm.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/BZip2.html" title="class in org.apache.tools.ant.taskdefs">BZip2</A></B></TD>
<TD>Compresses a file with the BZIP2 algorithm.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/CallTarget.html" title="class in org.apache.tools.ant.taskdefs">CallTarget</A></B></TD>
<TD>Call another target in the same project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Checksum.html" title="class in org.apache.tools.ant.taskdefs">Checksum</A></B></TD>
<TD>Used to create or verify file checksums.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Checksum.FormatElement.html" title="class in org.apache.tools.ant.taskdefs">Checksum.FormatElement</A></B></TD>
<TD>Helper class for the format attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Chmod.html" title="class in org.apache.tools.ant.taskdefs">Chmod</A></B></TD>
<TD>Chmod equivalent for unix-like environments.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Classloader.html" title="class in org.apache.tools.ant.taskdefs">Classloader</A></B></TD>
<TD>EXPERIMENTAL
 Create or modifies ClassLoader.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Concat.html" title="class in org.apache.tools.ant.taskdefs">Concat</A></B></TD>
<TD>This class contains the 'concat' task, used to concatenate a series
 of files into a single stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Concat.TextElement.html" title="class in org.apache.tools.ant.taskdefs">Concat.TextElement</A></B></TD>
<TD>sub element points to a file or contains text</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ConditionTask.html" title="class in org.apache.tools.ant.taskdefs">ConditionTask</A></B></TD>
<TD>Task to set a property conditionally using &lt;uptodate&gt;, &lt;available&gt;,
 and many other supported conditions.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</A></B></TD>
<TD>Copies a file or directory to a new file
 or directory.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Copydir.html" title="class in org.apache.tools.ant.taskdefs">Copydir</A></B></TD>
<TD><B>Deprecated.</B>&nbsp;<I>The copydir task is deprecated since Ant 1.2.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Copyfile.html" title="class in org.apache.tools.ant.taskdefs">Copyfile</A></B></TD>
<TD><B>Deprecated.</B>&nbsp;<I>The copyfile task is deprecated since Ant 1.2.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/CopyPath.html" title="class in org.apache.tools.ant.taskdefs">CopyPath</A></B></TD>
<TD>Copy the contents of a path to a destination, using the mapper of choice</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Cvs.html" title="class in org.apache.tools.ant.taskdefs">Cvs</A></B></TD>
<TD>Performs operations on a CVS repository.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/CVSPass.html" title="class in org.apache.tools.ant.taskdefs">CVSPass</A></B></TD>
<TD>Adds an new entry to a CVS password file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/DefaultExcludes.html" title="class in org.apache.tools.ant.taskdefs">DefaultExcludes</A></B></TD>
<TD>Alters the default excludes for the <strong>entire</strong> build..</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/DefBase.html" title="class in org.apache.tools.ant.taskdefs">DefBase</A></B></TD>
<TD>Base class for Definitions handling uri and class loading.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Definer.html" title="class in org.apache.tools.ant.taskdefs">Definer</A></B></TD>
<TD>Base class for Taskdef and Typedef - handles all
 the attributes for Typedef.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Definer.Format.html" title="class in org.apache.tools.ant.taskdefs">Definer.Format</A></B></TD>
<TD>Enumerated type for format attribute</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Definer.OnError.html" title="class in org.apache.tools.ant.taskdefs">Definer.OnError</A></B></TD>
<TD>Enumerated type for onError attribute</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Delete.html" title="class in org.apache.tools.ant.taskdefs">Delete</A></B></TD>
<TD>Deletes a file or directory, or set of files defined by a fileset.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Deltree.html" title="class in org.apache.tools.ant.taskdefs">Deltree</A></B></TD>
<TD><B>Deprecated.</B>&nbsp;<I>The deltree task is deprecated since Ant 1.2.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/DependSet.html" title="class in org.apache.tools.ant.taskdefs">DependSet</A></B></TD>
<TD>Examines and removes out of date target files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/DiagnosticsTask.html" title="class in org.apache.tools.ant.taskdefs">DiagnosticsTask</A></B></TD>
<TD>This is a task that hands off work to the Diagnostics module.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Dirname.html" title="class in org.apache.tools.ant.taskdefs">Dirname</A></B></TD>
<TD>Determines the directory name of the specified file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Ear.html" title="class in org.apache.tools.ant.taskdefs">Ear</A></B></TD>
<TD>Creates a EAR archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Echo.html" title="class in org.apache.tools.ant.taskdefs">Echo</A></B></TD>
<TD>Writes a message to the Ant logging facilities.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Echo.EchoLevel.html" title="class in org.apache.tools.ant.taskdefs">Echo.EchoLevel</A></B></TD>
<TD>The enumerated values for the level attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/EchoXML.html" title="class in org.apache.tools.ant.taskdefs">EchoXML</A></B></TD>
<TD>Echo XML.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Exec.html" title="class in org.apache.tools.ant.taskdefs">Exec</A></B></TD>
<TD><B>Deprecated.</B>&nbsp;<I>since 1.2.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A></B></TD>
<TD>Executes a given command if the os platform is appropriate.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</A></B></TD>
<TD>Runs an external program.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html" title="class in org.apache.tools.ant.taskdefs">ExecuteJava</A></B></TD>
<TD>Execute a Java class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn</A></B></TD>
<TD>Executes a given command, supplying a set of files as arguments.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.FileDirBoth.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn.FileDirBoth</A></B></TD>
<TD>Enumerated attribute with the values "file", "dir" and "both"
 for the type attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs">ExecuteWatchdog</A></B></TD>
<TD>Destroys a process running for too long.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Exit.html" title="class in org.apache.tools.ant.taskdefs">Exit</A></B></TD>
<TD>Exits the active build, giving an additional message
 if available.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Expand.html" title="class in org.apache.tools.ant.taskdefs">Expand</A></B></TD>
<TD>Unzip a file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Filter.html" title="class in org.apache.tools.ant.taskdefs">Filter</A></B></TD>
<TD>Sets a token filter that is used by the file copy tasks
 to do token substitution.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/FixCRLF.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF</A></B></TD>
<TD>Converts text source files to local OS formatting conventions, as
 well as repair text files damaged by misconfigured or misguided editors or
 file transfer programs.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/FixCRLF.AddAsisRemove.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF.AddAsisRemove</A></B></TD>
<TD>Enumerated attribute with the values "asis", "add" and "remove".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/FixCRLF.CrLf.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF.CrLf</A></B></TD>
<TD>Enumerated attribute with the values "asis", "cr", "lf" and "crlf".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/GenerateKey.html" title="class in org.apache.tools.ant.taskdefs">GenerateKey</A></B></TD>
<TD>Generates a key in a keystore.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/GenerateKey.DistinguishedName.html" title="class in org.apache.tools.ant.taskdefs">GenerateKey.DistinguishedName</A></B></TD>
<TD>A class corresponding to the dname nested element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/GenerateKey.DnameParam.html" title="class in org.apache.tools.ant.taskdefs">GenerateKey.DnameParam</A></B></TD>
<TD>A DistinguishedName parameter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Get.html" title="class in org.apache.tools.ant.taskdefs">Get</A></B></TD>
<TD>Gets a particular file from a URL source.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Get.Base64Converter.html" title="class in org.apache.tools.ant.taskdefs">Get.Base64Converter</A></B></TD>
<TD>Provide this for Backward Compatibility.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Get.NullProgress.html" title="class in org.apache.tools.ant.taskdefs">Get.NullProgress</A></B></TD>
<TD>do nothing with progress info</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Get.VerboseProgress.html" title="class in org.apache.tools.ant.taskdefs">Get.VerboseProgress</A></B></TD>
<TD>verbose progress system prints to some output stream</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/GUnzip.html" title="class in org.apache.tools.ant.taskdefs">GUnzip</A></B></TD>
<TD>Expands a file that has been compressed with the GZIP
 algorithm.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/GZip.html" title="class in org.apache.tools.ant.taskdefs">GZip</A></B></TD>
<TD>Compresses a file with the GZIP algorithm.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ImportTask.html" title="class in org.apache.tools.ant.taskdefs">ImportTask</A></B></TD>
<TD>Task to import another build file into the current project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Input.html" title="class in org.apache.tools.ant.taskdefs">Input</A></B></TD>
<TD>Reads an input line from the console.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Input.HandlerType.html" title="class in org.apache.tools.ant.taskdefs">Input.HandlerType</A></B></TD>
<TD>EnumeratedAttribute representing the built-in input handler types:
 "default", "propertyfile", "greedy".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Jar.html" title="class in org.apache.tools.ant.taskdefs">Jar</A></B></TD>
<TD>Creates a JAR archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Jar.FilesetManifestConfig.html" title="class in org.apache.tools.ant.taskdefs">Jar.FilesetManifestConfig</A></B></TD>
<TD>The manifest config enumerated type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Java.html" title="class in org.apache.tools.ant.taskdefs">Java</A></B></TD>
<TD>Launcher for Java applications.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html" title="class in org.apache.tools.ant.taskdefs">Javac</A></B></TD>
<TD>Compiles Java source files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html" title="class in org.apache.tools.ant.taskdefs">Javadoc</A></B></TD>
<TD>Generates Javadoc documentation for a collection
 of source code.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.AccessType.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.AccessType</A></B></TD>
<TD>EnumeratedAttribute implementation supporting the Javadoc scoping
 values.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.ExtensionInfo.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.ExtensionInfo</A></B></TD>
<TD>A project aware class used for Javadoc extensions which take a name
 and a path such as doclet and taglet arguments.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.Html.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.Html</A></B></TD>
<TD>An HTML element in the Javadoc.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.PackageName.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.PackageName</A></B></TD>
<TD>Used to track info about the packages to be javadoc'd</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.SourceFile.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.SourceFile</A></B></TD>
<TD>This class is used to manage the source files to be processed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html" title="class in org.apache.tools.ant.taskdefs">JDBCTask</A></B></TD>
<TD>Handles JDBC configuration needed by SQL type tasks.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Jikes.html" title="class in org.apache.tools.ant.taskdefs">Jikes</A></B></TD>
<TD><B>Deprecated.</B>&nbsp;<I>since 1.2.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/JikesOutputParser.html" title="class in org.apache.tools.ant.taskdefs">JikesOutputParser</A></B></TD>
<TD><B>Deprecated.</B>&nbsp;<I>since 1.2.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/KeySubst.html" title="class in org.apache.tools.ant.taskdefs">KeySubst</A></B></TD>
<TD><B>Deprecated.</B>&nbsp;<I>KeySubst is deprecated since Ant 1.1.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Length.html" title="class in org.apache.tools.ant.taskdefs">Length</A></B></TD>
<TD>Gets lengths:  of files/resources, byte size; of strings, length (optionally trimmed).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Length.FileMode.html" title="class in org.apache.tools.ant.taskdefs">Length.FileMode</A></B></TD>
<TD>EnumeratedAttribute operation mode</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Length.When.html" title="class in org.apache.tools.ant.taskdefs">Length.When</A></B></TD>
<TD>EnumeratedAttribute for the when attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/LoadFile.html" title="class in org.apache.tools.ant.taskdefs">LoadFile</A></B></TD>
<TD>Load a file into a property</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/LoadProperties.html" title="class in org.apache.tools.ant.taskdefs">LoadProperties</A></B></TD>
<TD>Load a file's contents as Ant properties.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/LoadResource.html" title="class in org.apache.tools.ant.taskdefs">LoadResource</A></B></TD>
<TD>Load a resource into a property</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/LogOutputStream.html" title="class in org.apache.tools.ant.taskdefs">LogOutputStream</A></B></TD>
<TD>Logs each line written to this stream to the log system of ant.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/LogStreamHandler.html" title="class in org.apache.tools.ant.taskdefs">LogStreamHandler</A></B></TD>
<TD>Logs standard output and error of a subprocess to the log system of ant.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html" title="class in org.apache.tools.ant.taskdefs">MacroDef</A></B></TD>
<TD>Describe class <code>MacroDef</code> here.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.Attribute.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.Attribute</A></B></TD>
<TD>An attribute for the MacroDef task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.NestedSequential.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.NestedSequential</A></B></TD>
<TD>The class corresponding to the sequential nested element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.TemplateElement.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.TemplateElement</A></B></TD>
<TD>A nested element for the MacroDef task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.Text.html" title="class in org.apache.tools.ant.taskdefs">MacroDef.Text</A></B></TD>
<TD>A nested text element for the MacroDef task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroInstance.html" title="class in org.apache.tools.ant.taskdefs">MacroInstance</A></B></TD>
<TD>The class to be placed in the ant type definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroInstance.Element.html" title="class in org.apache.tools.ant.taskdefs">MacroInstance.Element</A></B></TD>
<TD>Embedded element in macro instance</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MakeUrl.html" title="class in org.apache.tools.ant.taskdefs">MakeUrl</A></B></TD>
<TD>This task takes file and turns them into a URL, which it then assigns
 to a property.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</A></B></TD>
<TD>Holds the data of a jar manifest.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Attribute</A></B></TD>
<TD>An attribute for the manifest.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</A></B></TD>
<TD>A manifest section - you can nest attribute elements into sections.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestClassPath.html" title="class in org.apache.tools.ant.taskdefs">ManifestClassPath</A></B></TD>
<TD>Converts a Path into a property suitable as a Manifest classpath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestTask.html" title="class in org.apache.tools.ant.taskdefs">ManifestTask</A></B></TD>
<TD>Creates a manifest file for inclusion in a JAR, Ant task wrapper
 around <A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html" title="class in org.apache.tools.ant.taskdefs"><CODE>Manifest</CODE></A>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestTask.Mode.html" title="class in org.apache.tools.ant.taskdefs">ManifestTask.Mode</A></B></TD>
<TD>Helper class for Manifest's mode attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></B></TD>
<TD>This is an abstract task that should be used by all those tasks that
 require to include or exclude files based on pattern matching.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Mkdir.html" title="class in org.apache.tools.ant.taskdefs">Mkdir</A></B></TD>
<TD>Creates a given directory.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Move.html" title="class in org.apache.tools.ant.taskdefs">Move</A></B></TD>
<TD>Moves a file or directory to a new file or directory.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Nice.html" title="class in org.apache.tools.ant.taskdefs">Nice</A></B></TD>
<TD>A task to provide "nice-ness" to the current thread, and/or to
 query the current value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Pack.html" title="class in org.apache.tools.ant.taskdefs">Pack</A></B></TD>
<TD>Abstract Base class for pack tasks.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Parallel.html" title="class in org.apache.tools.ant.taskdefs">Parallel</A></B></TD>
<TD>Executes the contained tasks in separate threads, continuing
 once all are completed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Parallel.TaskList.html" title="class in org.apache.tools.ant.taskdefs">Parallel.TaskList</A></B></TD>
<TD>Class which holds a list of tasks to execute</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Patch.html" title="class in org.apache.tools.ant.taskdefs">Patch</A></B></TD>
<TD>Patches a file by applying a 'diff' file to it; requires "patch" to be
 on the execution path.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PathConvert.html" title="class in org.apache.tools.ant.taskdefs">PathConvert</A></B></TD>
<TD>Converts path and classpath information to a specific target OS
 format.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PathConvert.TargetOs.html" title="class in org.apache.tools.ant.taskdefs">PathConvert.TargetOs</A></B></TD>
<TD>An enumeration of supported targets:
 "windows", "unix", "netware", and "os/2".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.html" title="class in org.apache.tools.ant.taskdefs">PreSetDef</A></B></TD>
<TD>The preset definition task generates a new definition
 based on a current definition with some attributes or
 elements preset.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html" title="class in org.apache.tools.ant.taskdefs">PreSetDef.PreSetDefinition</A></B></TD>
<TD>This class contains the unknown element and the object
 that is predefined.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html" title="class in org.apache.tools.ant.taskdefs">Property</A></B></TD>
<TD>Sets a property by name, or set of properties (from file or
 resource) in the project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html" title="class in org.apache.tools.ant.taskdefs">PumpStreamHandler</A></B></TD>
<TD>Copies standard output and error of subprocesses to standard output and
 error of the parent process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Recorder.html" title="class in org.apache.tools.ant.taskdefs">Recorder</A></B></TD>
<TD>Adds a listener to the current build process that records the
 output to a file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Recorder.ActionChoices.html" title="class in org.apache.tools.ant.taskdefs">Recorder.ActionChoices</A></B></TD>
<TD>A list of possible values for the <code>setAction()</code> method.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Recorder.VerbosityLevelChoices.html" title="class in org.apache.tools.ant.taskdefs">Recorder.VerbosityLevelChoices</A></B></TD>
<TD>A list of possible values for the <code>setLoglevel()</code> method.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html" title="class in org.apache.tools.ant.taskdefs">RecorderEntry</A></B></TD>
<TD>This is a class that represents a recorder.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html" title="class in org.apache.tools.ant.taskdefs">Redirector</A></B></TD>
<TD>The Redirector class manages the setup and connection of
 input and output redirection for an Ant project component.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rename.html" title="class in org.apache.tools.ant.taskdefs">Rename</A></B></TD>
<TD><B>Deprecated.</B>&nbsp;<I>The rename task is deprecated since Ant 1.2.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Replace.html" title="class in org.apache.tools.ant.taskdefs">Replace</A></B></TD>
<TD>Replaces all occurrences of one or more string tokens with given
 values in the indicated files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ResourceCount.html" title="class in org.apache.tools.ant.taskdefs">ResourceCount</A></B></TD>
<TD>Count resources from a ResourceCollection, storing to a property or
 writing to the log.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html" title="class in org.apache.tools.ant.taskdefs">Rmic</A></B></TD>
<TD>Runs the rmic compiler against classes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SendEmail.html" title="class in org.apache.tools.ant.taskdefs">SendEmail</A></B></TD>
<TD>A task to send SMTP email.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Sequential.html" title="class in org.apache.tools.ant.taskdefs">Sequential</A></B></TD>
<TD>Sequential is a container task - it can contain other Ant tasks.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html" title="class in org.apache.tools.ant.taskdefs">SignJar</A></B></TD>
<TD>Signs JAR or ZIP files with the javasign command line tool.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Sleep.html" title="class in org.apache.tools.ant.taskdefs">Sleep</A></B></TD>
<TD>Sleep, or pause, for a period of time.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html" title="class in org.apache.tools.ant.taskdefs">SQLExec</A></B></TD>
<TD>Executes a series of SQL statements on a database using JDBC.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.DelimiterType.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.DelimiterType</A></B></TD>
<TD>delimiters we support, "normal" and "row"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.OnError.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.OnError</A></B></TD>
<TD>The action a task should perform on an error,
 one of "continue", "stop" and "abort"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/StreamPumper.html" title="class in org.apache.tools.ant.taskdefs">StreamPumper</A></B></TD>
<TD>Copies all data from an input stream to an output stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/SubAnt.html" title="class in org.apache.tools.ant.taskdefs">SubAnt</A></B></TD>
<TD>Calls a given target for all defined sub-builds.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.html" title="class in org.apache.tools.ant.taskdefs">Sync</A></B></TD>
<TD>Synchronize a local target directory from the files defined
 in one or more filesets.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.MyCopy.html" title="class in org.apache.tools.ant.taskdefs">Sync.MyCopy</A></B></TD>
<TD>Subclass Copy in order to access it's file/dir maps.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.SyncTarget.html" title="class in org.apache.tools.ant.taskdefs">Sync.SyncTarget</A></B></TD>
<TD>Inner class used to hold exclude patterns and selectors to save
 stuff that happens to live in the target directory but should
 not get removed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.html" title="class in org.apache.tools.ant.taskdefs">Tar</A></B></TD>
<TD>Creates a tar archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarCompressionMethod.html" title="class in org.apache.tools.ant.taskdefs">Tar.TarCompressionMethod</A></B></TD>
<TD>Valid Modes for Compression attribute to Tar Task</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarFileSet.html" title="class in org.apache.tools.ant.taskdefs">Tar.TarFileSet</A></B></TD>
<TD>This is a FileSet with the option to specify permissions
 and other attributes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarLongFileMode.html" title="class in org.apache.tools.ant.taskdefs">Tar.TarLongFileMode</A></B></TD>
<TD>Set of options for long file handling in the task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Taskdef.html" title="class in org.apache.tools.ant.taskdefs">Taskdef</A></B></TD>
<TD>Adds a task definition to the current project, such that this new task can be
 used in the current project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/TaskOutputStream.html" title="class in org.apache.tools.ant.taskdefs">TaskOutputStream</A></B></TD>
<TD><B>Deprecated.</B>&nbsp;<I>since 1.2.x.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/TempFile.html" title="class in org.apache.tools.ant.taskdefs">TempFile</A></B></TD>
<TD>This task sets a property to  the name of a temporary file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Touch.html" title="class in org.apache.tools.ant.taskdefs">Touch</A></B></TD>
<TD>Touch a file and/or fileset(s) and/or filelist(s);
 corresponds to the Unix touch command.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Transform.html" title="class in org.apache.tools.ant.taskdefs">Transform</A></B></TD>
<TD>Has been merged into ExecuteOn, empty class for backwards compatibility.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Tstamp.html" title="class in org.apache.tools.ant.taskdefs">Tstamp</A></B></TD>
<TD>Sets properties to the current time, or offsets from the current time.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Tstamp.Unit.html" title="class in org.apache.tools.ant.taskdefs">Tstamp.Unit</A></B></TD>
<TD>set of valid units to use for time offsets.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Typedef.html" title="class in org.apache.tools.ant.taskdefs">Typedef</A></B></TD>
<TD>Adds a data type definition to the current project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Unpack.html" title="class in org.apache.tools.ant.taskdefs">Unpack</A></B></TD>
<TD>Abstract Base class for unpack tasks.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Untar.html" title="class in org.apache.tools.ant.taskdefs">Untar</A></B></TD>
<TD>Untar a file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Untar.UntarCompressionMethod.html" title="class in org.apache.tools.ant.taskdefs">Untar.UntarCompressionMethod</A></B></TD>
<TD>Valid Modes for Compression attribute to Untar Task</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/UpToDate.html" title="class in org.apache.tools.ant.taskdefs">UpToDate</A></B></TD>
<TD>Sets the given property if the specified target has a timestamp
 greater than all of the source files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/VerifyJar.html" title="class in org.apache.tools.ant.taskdefs">VerifyJar</A></B></TD>
<TD>JAR verification task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.html" title="class in org.apache.tools.ant.taskdefs">WaitFor</A></B></TD>
<TD>Wait for an external event to occur.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</A></B></TD>
<TD>The enumeration of units:
 millisecond, second, minute, hour, day, week</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/War.html" title="class in org.apache.tools.ant.taskdefs">War</A></B></TD>
<TD>An extension of &lt;jar&gt; to create a WAR archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/WhichResource.html" title="class in org.apache.tools.ant.taskdefs">WhichResource</A></B></TD>
<TD>Find a class or resource on the supplied classpath, or the
 system classpath if none is supplied.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html" title="class in org.apache.tools.ant.taskdefs">XmlProperty</A></B></TD>
<TD>Loads property values from a valid XML file, generating the
 property names from the file's element and attribute names.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess</A></B></TD>
<TD>Processes a set of XML documents via XSLT.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Factory.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory</A></B></TD>
<TD>The factory element to configure a transformer factory</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Factory.Attribute.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Factory.Attribute</A></B></TD>
<TD>A JAXP factory attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.OutputProperty.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.OutputProperty</A></B></TD>
<TD>Specify how the result tree should be output as specified
 in the <a href="http://www.w3.org/TR/xslt#output">
 specification</a>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Param.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess.Param</A></B></TD>
<TD>The Param inner class used to store XSL parameters</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html" title="class in org.apache.tools.ant.taskdefs">Zip</A></B></TD>
<TD>Create a Zip file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs">Zip.ArchiveState</A></B></TD>
<TD>Holds the up-to-date status and the out-of-date resources of
 the original archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.Duplicate.html" title="class in org.apache.tools.ant.taskdefs">Zip.Duplicate</A></B></TD>
<TD>Possible behaviors when a duplicate file is added:
 "add", "preserve" or "fail"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.WhenEmpty.html" title="class in org.apache.tools.ant.taskdefs">Zip.WhenEmpty</A></B></TD>
<TD>Possible behaviors when there are no matching files for the task:
 "fail", "skip", or "create".</TD>
</TR>
</TABLE>
&nbsp;

<P>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Exception Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></B></TD>
<TD>Exception thrown indicating problems in a JAR Manifest</TD>
</TR>
</TABLE>
&nbsp;

<P>
<DL>
</DL>
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Package</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/loader/package-summary.html"><B>PREV PACKAGE</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/compilers/package-summary.html"><B>NEXT PACKAGE</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/package-summary.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="package-summary.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
