<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:20 EST 2006 -->
<TITLE>
ProjectComponent (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.ProjectComponent class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="ProjectComponent (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/ProjectHelper.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/ProjectComponent.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ProjectComponent.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class ProjectComponent</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.ProjectComponent</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../org/apache/tools/ant/types/optional/AbstractScriptComponent.html" title="class in org.apache.tools.ant.types.optional">AbstractScriptComponent</A>, <A HREF="../../../../org/apache/tools/ant/types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Concat.TextElement.html" title="class in org.apache.tools.ant.taskdefs">Concat.TextElement</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html" title="class in org.apache.tools.ant.taskdefs.condition">ConditionBase</A>, <A HREF="../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A>, <A HREF="../../../../org/apache/tools/ant/util/FileTokenizer.html" title="class in org.apache.tools.ant.util">FileTokenizer</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/condition/HasMethod.html" title="class in org.apache.tools.ant.taskdefs.condition">HasMethod</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/condition/Http.html" title="class in org.apache.tools.ant.taskdefs.condition">Http</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/condition/IsFalse.html" title="class in org.apache.tools.ant.taskdefs.condition">IsFalse</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/condition/IsReachable.html" title="class in org.apache.tools.ant.taskdefs.condition">IsReachable</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/condition/IsReference.html" title="class in org.apache.tools.ant.taskdefs.condition">IsReference</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/condition/IsSet.html" title="class in org.apache.tools.ant.taskdefs.condition">IsSet</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/condition/IsTrue.html" title="class in org.apache.tools.ant.taskdefs.condition">IsTrue</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Javadoc.ExtensionInfo.html" title="class in org.apache.tools.ant.taskdefs">Javadoc.ExtensionInfo</A>, <A HREF="../../../../org/apache/tools/ant/util/LineTokenizer.html" title="class in org.apache.tools.ant.util">LineTokenizer</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/condition/Matches.html" title="class in org.apache.tools.ant.taskdefs.condition">Matches</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/email/Message.html" title="class in org.apache.tools.ant.taskdefs.email">Message</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/condition/ParserSupports.html" title="class in org.apache.tools.ant.taskdefs.condition">ParserSupports</A>, <A HREF="../../../../org/apache/tools/ant/types/spi/Provider.html" title="class in org.apache.tools.ant.types.spi">Provider</A>, <A HREF="../../../../org/apache/tools/ant/types/spi/Service.html" title="class in org.apache.tools.ant.types.spi">Service</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/condition/Socket.html" title="class in org.apache.tools.ant.taskdefs.condition">Socket</A>, <A HREF="../../../../org/apache/tools/ant/util/StringTokenizer.html" title="class in org.apache.tools.ant.util">StringTokenizer</A>, <A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>, <A HREF="../../../../org/apache/tools/ant/filters/TokenFilter.ChainableReaderFilter.html" title="class in org.apache.tools.ant.filters">TokenFilter.ChainableReaderFilter</A>, <A HREF="../../../../org/apache/tools/ant/filters/TokenFilter.ContainsString.html" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsString</A>, <A HREF="../../../../org/apache/tools/ant/filters/TokenFilter.DeleteCharacters.html" title="class in org.apache.tools.ant.filters">TokenFilter.DeleteCharacters</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/condition/TypeFound.html" title="class in org.apache.tools.ant.taskdefs.condition">TypeFound</A>, <A HREF="../../../../org/apache/tools/ant/util/XMLFragment.html" title="class in org.apache.tools.ant.util">XMLFragment</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public abstract class <B>ProjectComponent</B><DT>extends java.lang.Object<DT>implements java.lang.Cloneable</DL>
</PRE>

<P>
Base class for components of a project, including tasks and data types.
 Provides common facilities.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()"><CODE>getLocation()</CODE></A> method.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             You should not be directly accessing this variable directly.
             You should access project object via the getProject()
             or setProject() accessor/mutators.</I></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#ProjectComponent()">ProjectComponent</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sole constructor.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the description of the current action.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the file/location where this task was defined.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the project to which this component belongs.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String)">log</A></B>(java.lang.String&nbsp;msg)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logs a message with the default (INFO) priority.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String, int)">log</A></B>(java.lang.String&nbsp;msg,
    int&nbsp;msgLevel)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logs a message with the given priority.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A></B>(java.lang.String&nbsp;desc)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets a description of the current action.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A></B>(<A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A>&nbsp;location)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the file/location where this task was defined.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the project object of this component.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="project"><!-- --></A><H3>
project</H3>
<PRE>
protected <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A> <B>project</B></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             You should not be directly accessing this variable directly.
             You should access project object via the getProject()
             or setProject() accessor/mutators.</I><DD>Project object of this component.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="location"><!-- --></A><H3>
location</H3>
<PRE>
protected <A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A> <B>location</B></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()"><CODE>getLocation()</CODE></A> method.</I><DD>Location within the build file of this task definition.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="description"><!-- --></A><H3>
description</H3>
<PRE>
protected java.lang.String <B>description</B></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.</I><DD>Description of this component, if any.
<P>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ProjectComponent()"><!-- --></A><H3>
ProjectComponent</H3>
<PRE>
public <B>ProjectComponent</B>()</PRE>
<DL>
<DD>Sole constructor.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setProject(org.apache.tools.ant.Project)"><!-- --></A><H3>
setProject</H3>
<PRE>
public void <B>setProject</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Sets the project object of this component. This method is used by
 Project when a component is added to it so that the component has
 access to the functions of the project. It should not be used
 for any other purpose.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - Project in whose scope this component belongs.
                Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="getProject()"><!-- --></A><H3>
getProject</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A> <B>getProject</B>()</PRE>
<DL>
<DD>Returns the project to which this component belongs.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the components's project.</DL>
</DD>
</DL>
<HR>

<A NAME="getLocation()"><!-- --></A><H3>
getLocation</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A> <B>getLocation</B>()</PRE>
<DL>
<DD>Returns the file/location where this task was defined.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the file/location where this task was defined.
         Should not return <code>null</code>. Location.UNKNOWN_LOCATION
         is used for unknown locations.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/Location.html#UNKNOWN_LOCATION"><CODE>Location.UNKNOWN_LOCATION</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setLocation(org.apache.tools.ant.Location)"><!-- --></A><H3>
setLocation</H3>
<PRE>
public void <B>setLocation</B>(<A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A>&nbsp;location)</PRE>
<DL>
<DD>Sets the file/location where this task was defined.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>location</CODE> - The file/location where this task was defined.
                 Should not be <code>null</code>--use
                 Location.UNKNOWN_LOCATION if the location isn't known.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/Location.html#UNKNOWN_LOCATION"><CODE>Location.UNKNOWN_LOCATION</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setDescription(java.lang.String)"><!-- --></A><H3>
setDescription</H3>
<PRE>
public void <B>setDescription</B>(java.lang.String&nbsp;desc)</PRE>
<DL>
<DD>Sets a description of the current action. This may be used for logging
 purposes.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>desc</CODE> - Description of the current action.
             May be <code>null</code>, indicating that no description is
             available.</DL>
</DD>
</DL>
<HR>

<A NAME="getDescription()"><!-- --></A><H3>
getDescription</H3>
<PRE>
public java.lang.String <B>getDescription</B>()</PRE>
<DL>
<DD>Returns the description of the current action.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the description of the current action, or <code>null</code> if
         no description is available.</DL>
</DD>
</DL>
<HR>

<A NAME="log(java.lang.String)"><!-- --></A><H3>
log</H3>
<PRE>
public void <B>log</B>(java.lang.String&nbsp;msg)</PRE>
<DL>
<DD>Logs a message with the default (INFO) priority.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>msg</CODE> - The message to be logged. Should not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="log(java.lang.String, int)"><!-- --></A><H3>
log</H3>
<PRE>
public void <B>log</B>(java.lang.String&nbsp;msg,
                int&nbsp;msgLevel)</PRE>
<DL>
<DD>Logs a message with the given priority.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>msg</CODE> - The message to be logged. Should not be <code>null</code>.<DD><CODE>msgLevel</CODE> - the message priority at which this message is
                 to be logged.</DL>
</DD>
</DL>
<HR>

<A NAME="clone()"><!-- --></A><H3>
clone</H3>
<PRE>
public java.lang.Object <B>clone</B>()
                       throws java.lang.CloneNotSupportedException</PRE>
<DL>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>clone</CODE> in class <CODE>java.lang.Object</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a shallow copy of this projectcomponent.
<DT><B>Throws:</B>
<DD><CODE>java.lang.CloneNotSupportedException</CODE> - does not happen,
                                    but is declared to allow subclasses to do so.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/ProjectHelper.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/ProjectComponent.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ProjectComponent.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
