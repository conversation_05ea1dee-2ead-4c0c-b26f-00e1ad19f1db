<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:23 EST 2006 -->
<TITLE>
Javadoc.TagArgument (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.Javadoc.TagArgument class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Javadoc.TagArgument (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.SourceFile.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Javadoc.TagArgument.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Javadoc.TagArgument.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.types.DataType">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class Javadoc.TagArgument</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.AbstractFileSet</A>
              <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.FileSet</A>
                  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.Javadoc.TagArgument</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable, <A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>, <A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></DD>
</DL>
<DL>
<DT><B>Enclosing class:</B><DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html" title="class in org.apache.tools.ant.taskdefs">Javadoc</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>Javadoc.TagArgument</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A></DL>
</PRE>

<P>
Class representing a -tag argument.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.types.DataType"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/DataType.html#checked">checked</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#ref">ref</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.TagArgument.html#Javadoc.TagArgument()">Javadoc.TagArgument</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sole constructor.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.TagArgument.html#getParameter()">getParameter</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the -tag parameter this argument represented.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.TagArgument.html#setEnabled(boolean)">setEnabled</A></B>(boolean&nbsp;enabled)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets whether or not the tag is enabled.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.TagArgument.html#setName(java.lang.String)">setName</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the name of the tag.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.TagArgument.html#setScope(java.lang.String)">setScope</A></B>(java.lang.String&nbsp;verboseScope)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the scope of the tag.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.types.FileSet"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/FileSet.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/types/FileSet.html#isFilesystemOnly()">isFilesystemOnly</A>, <A HREF="../../../../../org/apache/tools/ant/types/FileSet.html#iterator()">iterator</A>, <A HREF="../../../../../org/apache/tools/ant/types/FileSet.html#size()">size</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.types.AbstractFileSet"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html" title="class in org.apache.tools.ant.types">AbstractFileSet</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#appendExcludes(java.lang.String[])">appendExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#appendIncludes(java.lang.String[])">appendIncludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#createExclude()">createExclude</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#createExcludesFile()">createExcludesFile</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#createInclude()">createInclude</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#createIncludesFile()">createIncludesFile</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#createPatternSet()">createPatternSet</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getDefaultexcludes()">getDefaultexcludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getDir()">getDir</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getDir(org.apache.tools.ant.Project)">getDir</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getDirectoryScanner()">getDirectoryScanner</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getDirectoryScanner(org.apache.tools.ant.Project)">getDirectoryScanner</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getRef(org.apache.tools.ant.Project)">getRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#hasPatterns()">hasPatterns</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#hasSelectors()">hasSelectors</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#isCaseSensitive()">isCaseSensitive</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#isFollowSymlinks()">isFollowSymlinks</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#mergeExcludes(org.apache.tools.ant.Project)">mergeExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#mergeIncludes(org.apache.tools.ant.Project)">mergeIncludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#mergePatterns(org.apache.tools.ant.Project)">mergePatterns</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#selectorCount()">selectorCount</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#selectorElements()">selectorElements</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setCaseSensitive(boolean)">setCaseSensitive</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setDefaultexcludes(boolean)">setDefaultexcludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setDir(java.io.File)">setDir</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setExcludes(java.lang.String)">setExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setExcludesfile(java.io.File)">setExcludesfile</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setFile(java.io.File)">setFile</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setFollowSymlinks(boolean)">setFollowSymlinks</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setIncludes(java.lang.String)">setIncludes</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setIncludesfile(java.io.File)">setIncludesfile</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setupDirectoryScanner(org.apache.tools.ant.FileScanner)">setupDirectoryScanner</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#setupDirectoryScanner(org.apache.tools.ant.FileScanner, org.apache.tools.ant.Project)">setupDirectoryScanner</A>, <A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html#toString()">toString</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.types.DataType"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/DataType.html#checkAttributesAllowed()">checkAttributesAllowed</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#checkChildrenAllowed()">checkChildrenAllowed</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#circularReference()">circularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference()">dieOnCircularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference(java.util.Stack, org.apache.tools.ant.Project)">dieOnCircularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef()">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(java.lang.Class, java.lang.String)">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(java.lang.Class, java.lang.String, org.apache.tools.ant.Project)">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getDataTypeName()">getDataTypeName</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getRefid()">getRefid</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType, java.util.Stack, org.apache.tools.ant.Project)">invokeCircularReferenceCheck</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#isChecked()">isChecked</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#isReference()">isReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#noChildrenAllowed()">noChildrenAllowed</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#setChecked(boolean)">setChecked</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#tooManyAttributes()">tooManyAttributes</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Javadoc.TagArgument()"><!-- --></A><H3>
Javadoc.TagArgument</H3>
<PRE>
public <B>Javadoc.TagArgument</B>()</PRE>
<DL>
<DD>Sole constructor.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setName(java.lang.String)"><!-- --></A><H3>
setName</H3>
<PRE>
public void <B>setName</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Sets the name of the tag.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - The name of the tag.
             Must not be <code>null</code> or empty.</DL>
</DD>
</DL>
<HR>

<A NAME="setScope(java.lang.String)"><!-- --></A><H3>
setScope</H3>
<PRE>
public void <B>setScope</B>(java.lang.String&nbsp;verboseScope)
              throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Sets the scope of the tag. This is in comma-separated
 form, with each element being one of "all" (the default),
 "overview", "packages", "types", "constructors", "methods",
 "fields". The elements are treated in a case-insensitive
 manner.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>verboseScope</CODE> - The scope of the tag.
                     Must not be <code>null</code>,
                     should not be empty.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if all is specified along with
 other elements, if any elements are repeated, if no
 elements are specified, or if any unrecognised elements are
 specified.</DL>
</DD>
</DL>
<HR>

<A NAME="setEnabled(boolean)"><!-- --></A><H3>
setEnabled</H3>
<PRE>
public void <B>setEnabled</B>(boolean&nbsp;enabled)</PRE>
<DL>
<DD>Sets whether or not the tag is enabled.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>enabled</CODE> - Whether or not this tag is enabled.</DL>
</DD>
</DL>
<HR>

<A NAME="getParameter()"><!-- --></A><H3>
getParameter</H3>
<PRE>
public java.lang.String <B>getParameter</B>()
                              throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Returns the -tag parameter this argument represented.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the -tag parameter as a string
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if either the name or description
                           is <code>null</code> or empty.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.SourceFile.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Javadoc.TagArgument.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Javadoc.TagArgument.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.types.DataType">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
