<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:34 EST 2006 -->
<TITLE>
MailMessage (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.mail.MailMessage class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="MailMessage (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/mail/ErrorInQuitException.html" title="class in org.apache.tools.mail"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/mail/SmtpResponseReader.html" title="class in org.apache.tools.mail"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/mail/MailMessage.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="MailMessage.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.mail</FONT>
<BR>
Class MailMessage</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.mail.MailMessage</B>
</PRE>
<HR>
<DL>
<DT><PRE>public class <B>MailMessage</B><DT>extends java.lang.Object</DL>
</PRE>

<P>
A class to help send SMTP email.
 This class is an improvement on the sun.net.smtp.SmtpClient class
 found in the JDK.  This version has extra functionality, and can be used
 with JVMs that did not extend from the JDK.  It's not as robust as
 the JavaMail Standard Extension classes, but it's easier to use and
 easier to install, and has an Open Source license.
 <p>
 It can be used like this:
 <blockquote><pre>
 String mailhost = "localhost";  // or another mail host
 String from = "Mail Message Servlet &lt;<EMAIL>&gt;";
 String to = "<EMAIL>";
 String cc1 = "<EMAIL>";
 String cc2 = "<EMAIL>";
 String bcc = "<EMAIL>";
 &nbsp;
 MailMessage msg = new MailMessage(mailhost);
 msg.setPort(25);
 msg.from(from);
 msg.to(to);
 msg.cc(cc1);
 msg.cc(cc2);
 msg.bcc(bcc);
 msg.setSubject("Test subject");
 PrintStream out = msg.getPrintStream();
 &nbsp;
 Enumeration enum = req.getParameterNames();
 while (enum.hasMoreElements()) {
   String name = (String)enum.nextElement();
   String value = req.getParameter(name);
   out.println(name + " = " + value);
 }
 &nbsp;
 msg.sendAndClose();
 </pre></blockquote>
 <p>
 Be sure to set the from address, then set the recepient
 addresses, then set the subject and other headers, then get the
 PrintStream, then write the message, and finally send and close.
 The class does minimal error checking internally; it counts on the mail
 host to complain if there's any malformatted input or out of order
 execution.
 <p>
 An attachment mechanism based on RFC 1521 could be implemented on top of
 this class.  In the meanwhile, JavaMail is the best solution for sending
 email with attachments.
 <p>
 Still to do:
 <ul>
 <li>Figure out how to close the connection in case of error
 </ul>
<P>

<P>
<DL>
<DT><B>Version:</B></DT>
  <DD>1.1, 2000/03/19, added angle brackets to address, helps some servers
 version 1.0, 1999/12/29</DD>
</DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/mail/MailMessage.html#DEFAULT_HOST">DEFAULT_HOST</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;default mailhost</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/mail/MailMessage.html#DEFAULT_PORT">DEFAULT_PORT</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;default port for SMTP: 25</TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/mail/MailMessage.html#MailMessage()">MailMessage</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructs a new MailMessage to send an email.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/mail/MailMessage.html#MailMessage(java.lang.String)">MailMessage</A></B>(java.lang.String&nbsp;host)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructs a new MailMessage to send an email.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/mail/MailMessage.html#MailMessage(java.lang.String, int)">MailMessage</A></B>(java.lang.String&nbsp;host,
            int&nbsp;port)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructs a new MailMessage to send an email.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/mail/MailMessage.html#bcc(java.lang.String)">bcc</A></B>(java.lang.String&nbsp;bcc)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the bcc address.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/mail/MailMessage.html#cc(java.lang.String)">cc</A></B>(java.lang.String&nbsp;cc)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the cc address.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/mail/MailMessage.html#from(java.lang.String)">from</A></B>(java.lang.String&nbsp;from)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the from address.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.PrintStream</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/mail/MailMessage.html#getPrintStream()">getPrintStream</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns a PrintStream that can be used to write the body of the message.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/mail/MailMessage.html#replyto(java.lang.String)">replyto</A></B>(java.lang.String&nbsp;rto)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the replyto address
 This method may be
 called multiple times.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/mail/MailMessage.html#sendAndClose()">sendAndClose</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sends the message and closes the connection to the server.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/mail/MailMessage.html#setHeader(java.lang.String, java.lang.String)">setHeader</A></B>(java.lang.String&nbsp;name,
          java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the named header to the given value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/mail/MailMessage.html#setPort(int)">setPort</A></B>(int&nbsp;port)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the port to connect to the SMTP host.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/mail/MailMessage.html#setSubject(java.lang.String)">setSubject</A></B>(java.lang.String&nbsp;subj)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the subject of the mail message.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/mail/MailMessage.html#to(java.lang.String)">to</A></B>(java.lang.String&nbsp;to)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the to address.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="DEFAULT_HOST"><!-- --></A><H3>
DEFAULT_HOST</H3>
<PRE>
public static final java.lang.String <B>DEFAULT_HOST</B></PRE>
<DL>
<DD>default mailhost
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.mail.MailMessage.DEFAULT_HOST">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_PORT"><!-- --></A><H3>
DEFAULT_PORT</H3>
<PRE>
public static final int <B>DEFAULT_PORT</B></PRE>
<DL>
<DD>default port for SMTP: 25
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.mail.MailMessage.DEFAULT_PORT">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="MailMessage()"><!-- --></A><H3>
MailMessage</H3>
<PRE>
public <B>MailMessage</B>()
            throws java.io.IOException</PRE>
<DL>
<DD>Constructs a new MailMessage to send an email.
 Use localhost as the mail server with port 25.
<P>
<DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there's any problem contacting the mail server</DL>
</DL>
<HR>

<A NAME="MailMessage(java.lang.String)"><!-- --></A><H3>
MailMessage</H3>
<PRE>
public <B>MailMessage</B>(java.lang.String&nbsp;host)
            throws java.io.IOException</PRE>
<DL>
<DD>Constructs a new MailMessage to send an email.
 Use the given host as the mail server with port 25.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>host</CODE> - the mail server to use
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there's any problem contacting the mail server</DL>
</DL>
<HR>

<A NAME="MailMessage(java.lang.String, int)"><!-- --></A><H3>
MailMessage</H3>
<PRE>
public <B>MailMessage</B>(java.lang.String&nbsp;host,
                   int&nbsp;port)
            throws java.io.IOException</PRE>
<DL>
<DD>Constructs a new MailMessage to send an email.
 Use the given host and port as the mail server.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>host</CODE> - the mail server to use<DD><CODE>port</CODE> - the port to connect to
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there's any problem contacting the mail server</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setPort(int)"><!-- --></A><H3>
setPort</H3>
<PRE>
public void <B>setPort</B>(int&nbsp;port)</PRE>
<DL>
<DD>Set the port to connect to the SMTP host.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>port</CODE> - the port to use for connection.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/mail/MailMessage.html#DEFAULT_PORT"><CODE>DEFAULT_PORT</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="from(java.lang.String)"><!-- --></A><H3>
from</H3>
<PRE>
public void <B>from</B>(java.lang.String&nbsp;from)
          throws java.io.IOException</PRE>
<DL>
<DD>Sets the from address.  Also sets the "From" header.  This method should
 be called only once.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>from</CODE> - the from address
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there's any problem reported by the mail server</DL>
</DD>
</DL>
<HR>

<A NAME="replyto(java.lang.String)"><!-- --></A><H3>
replyto</H3>
<PRE>
public void <B>replyto</B>(java.lang.String&nbsp;rto)</PRE>
<DL>
<DD>Sets the replyto address
 This method may be
 called multiple times.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rto</CODE> - the replyto address</DL>
</DD>
</DL>
<HR>

<A NAME="to(java.lang.String)"><!-- --></A><H3>
to</H3>
<PRE>
public void <B>to</B>(java.lang.String&nbsp;to)
        throws java.io.IOException</PRE>
<DL>
<DD>Sets the to address.  Also sets the "To" header.  This method may be
 called multiple times.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>to</CODE> - the to address
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there's any problem reported by the mail server</DL>
</DD>
</DL>
<HR>

<A NAME="cc(java.lang.String)"><!-- --></A><H3>
cc</H3>
<PRE>
public void <B>cc</B>(java.lang.String&nbsp;cc)
        throws java.io.IOException</PRE>
<DL>
<DD>Sets the cc address.  Also sets the "Cc" header.  This method may be
 called multiple times.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>cc</CODE> - the cc address
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there's any problem reported by the mail server</DL>
</DD>
</DL>
<HR>

<A NAME="bcc(java.lang.String)"><!-- --></A><H3>
bcc</H3>
<PRE>
public void <B>bcc</B>(java.lang.String&nbsp;bcc)
         throws java.io.IOException</PRE>
<DL>
<DD>Sets the bcc address.  Does NOT set any header since it's a *blind* copy.
 This method may be called multiple times.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>bcc</CODE> - the bcc address
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there's any problem reported by the mail server</DL>
</DD>
</DL>
<HR>

<A NAME="setSubject(java.lang.String)"><!-- --></A><H3>
setSubject</H3>
<PRE>
public void <B>setSubject</B>(java.lang.String&nbsp;subj)</PRE>
<DL>
<DD>Sets the subject of the mail message.  Actually sets the "Subject"
 header.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>subj</CODE> - the subject of the mail message</DL>
</DD>
</DL>
<HR>

<A NAME="setHeader(java.lang.String, java.lang.String)"><!-- --></A><H3>
setHeader</H3>
<PRE>
public void <B>setHeader</B>(java.lang.String&nbsp;name,
                      java.lang.String&nbsp;value)</PRE>
<DL>
<DD>Sets the named header to the given value.  RFC 822 provides the rules for
 what text may constitute a header name and value.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - name of the header<DD><CODE>value</CODE> - contents of the header</DL>
</DD>
</DL>
<HR>

<A NAME="getPrintStream()"><!-- --></A><H3>
getPrintStream</H3>
<PRE>
public java.io.PrintStream <B>getPrintStream</B>()
                                   throws java.io.IOException</PRE>
<DL>
<DD>Returns a PrintStream that can be used to write the body of the message.
 A stream is used since email bodies are byte-oriented.  A writer can
 be wrapped on top if necessary for internationalization.
 This is actually done in Message.java
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a printstream containing the data and the headers of the email
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there's any problem reported by the mail server<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/taskdefs/email/Message.html" title="class in org.apache.tools.ant.taskdefs.email"><CODE>Message</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="sendAndClose()"><!-- --></A><H3>
sendAndClose</H3>
<PRE>
public void <B>sendAndClose</B>()
                  throws java.io.IOException</PRE>
<DL>
<DD>Sends the message and closes the connection to the server.
 The MailMessage object cannot be reused.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there's any problem reported by the mail server</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/mail/ErrorInQuitException.html" title="class in org.apache.tools.mail"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/mail/SmtpResponseReader.html" title="class in org.apache.tools.mail"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/mail/MailMessage.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="MailMessage.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
