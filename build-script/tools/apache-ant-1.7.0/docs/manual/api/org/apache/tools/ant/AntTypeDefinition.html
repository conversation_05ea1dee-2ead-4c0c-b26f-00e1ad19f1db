<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:19 EST 2006 -->
<TITLE>
AntTypeDefinition (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.AntTypeDefinition class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="AntTypeDefinition (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/AntClassLoader.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/AntTypeDefinition.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="AntTypeDefinition.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class AntTypeDefinition</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.AntTypeDefinition</B>
</PRE>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html" title="class in org.apache.tools.ant.taskdefs">PreSetDef.PreSetDefinition</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>AntTypeDefinition</B><DT>extends java.lang.Object</DL>
</PRE>

<P>
This class contains all the information
 on a particular ant type,
 the classname, adaptor and the class
 it should be assignable from.
 This type replaces the task/datatype split
 of pre ant 1.6.
<P>

<P>
<HR>

<P>

<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#AntTypeDefinition()">AntTypeDefinition</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#checkClass(org.apache.tools.ant.Project)">checkClass</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Checks if the attributes are correct.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#create(org.apache.tools.ant.Project)">create</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create an instance of the definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.ClassLoader</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#getClassLoader()">getClassLoader</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the classloader for this definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#getClassName()">getClassName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the classname of the definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#getExposedClass(org.apache.tools.ant.Project)">getExposedClass</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the exposed class for this
 definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#getName()">getName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the definition's name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#getTypeClass(org.apache.tools.ant.Project)">getTypeClass</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the definition class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#innerCreateAndSet(java.lang.Class, org.apache.tools.ant.Project)">innerCreateAndSet</A></B>(java.lang.Class&nbsp;newclass,
                  <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Inner implementation of the <A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#createAndSet(org.apache.tools.ant.Project, java.lang.Class)"><CODE>createAndSet(Project, Class)</CODE></A> logic, with no
 exception catching</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#innerGetTypeClass()">innerGetTypeClass</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Try and load a class, with no attempt to catch any fault.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#sameDefinition(org.apache.tools.ant.AntTypeDefinition, org.apache.tools.ant.Project)">sameDefinition</A></B>(<A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A>&nbsp;other,
               <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Equality method for this definition (assumes the names are the same).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#setAdapterClass(java.lang.Class)">setAdapterClass</A></B>(java.lang.Class&nbsp;adapterClass)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the adapter class for this definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#setAdaptToClass(java.lang.Class)">setAdaptToClass</A></B>(java.lang.Class&nbsp;adaptToClass)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the assignable class for this definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#setClass(java.lang.Class)">setClass</A></B>(java.lang.Class&nbsp;clazz)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the class of the definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#setClassLoader(java.lang.ClassLoader)">setClassLoader</A></B>(java.lang.ClassLoader&nbsp;classLoader)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the classloader to use to create an instance
 of the definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#setClassName(java.lang.String)">setClassName</A></B>(java.lang.String&nbsp;className)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the classname of the definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#setName(java.lang.String)">setName</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the definition's name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#similarDefinition(org.apache.tools.ant.AntTypeDefinition, org.apache.tools.ant.Project)">similarDefinition</A></B>(<A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A>&nbsp;other,
                  <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Similar definition;
 used to compare two definitions defined twice with the same
 name and the same types.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="AntTypeDefinition()"><!-- --></A><H3>
AntTypeDefinition</H3>
<PRE>
public <B>AntTypeDefinition</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setName(java.lang.String)"><!-- --></A><H3>
setName</H3>
<PRE>
public void <B>setName</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Set the definition's name.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the name of the definition.</DL>
</DD>
</DL>
<HR>

<A NAME="getName()"><!-- --></A><H3>
getName</H3>
<PRE>
public java.lang.String <B>getName</B>()</PRE>
<DL>
<DD>Return the definition's name.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the name of the definition.</DL>
</DD>
</DL>
<HR>

<A NAME="setClass(java.lang.Class)"><!-- --></A><H3>
setClass</H3>
<PRE>
public void <B>setClass</B>(java.lang.Class&nbsp;clazz)</PRE>
<DL>
<DD>Set the class of the definition.
 As a side-effect may set the classloader and classname.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>clazz</CODE> - the class of this definition.</DL>
</DD>
</DL>
<HR>

<A NAME="setClassName(java.lang.String)"><!-- --></A><H3>
setClassName</H3>
<PRE>
public void <B>setClassName</B>(java.lang.String&nbsp;className)</PRE>
<DL>
<DD>Set the classname of the definition.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>className</CODE> - the classname of this definition.</DL>
</DD>
</DL>
<HR>

<A NAME="getClassName()"><!-- --></A><H3>
getClassName</H3>
<PRE>
public java.lang.String <B>getClassName</B>()</PRE>
<DL>
<DD>Get the classname of the definition.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the name of the class of this definition.</DL>
</DD>
</DL>
<HR>

<A NAME="setAdapterClass(java.lang.Class)"><!-- --></A><H3>
setAdapterClass</H3>
<PRE>
public void <B>setAdapterClass</B>(java.lang.Class&nbsp;adapterClass)</PRE>
<DL>
<DD>Set the adapter class for this definition.
 This class is used to adapt the definitions class if
 required.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>adapterClass</CODE> - the adapterClass.</DL>
</DD>
</DL>
<HR>

<A NAME="setAdaptToClass(java.lang.Class)"><!-- --></A><H3>
setAdaptToClass</H3>
<PRE>
public void <B>setAdaptToClass</B>(java.lang.Class&nbsp;adaptToClass)</PRE>
<DL>
<DD>Set the assignable class for this definition.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>adaptToClass</CODE> - the assignable class.</DL>
</DD>
</DL>
<HR>

<A NAME="setClassLoader(java.lang.ClassLoader)"><!-- --></A><H3>
setClassLoader</H3>
<PRE>
public void <B>setClassLoader</B>(java.lang.ClassLoader&nbsp;classLoader)</PRE>
<DL>
<DD>Set the classloader to use to create an instance
 of the definition.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classLoader</CODE> - the ClassLoader.</DL>
</DD>
</DL>
<HR>

<A NAME="getClassLoader()"><!-- --></A><H3>
getClassLoader</H3>
<PRE>
public java.lang.ClassLoader <B>getClassLoader</B>()</PRE>
<DL>
<DD>Get the classloader for this definition.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the classloader for this definition.</DL>
</DD>
</DL>
<HR>

<A NAME="getExposedClass(org.apache.tools.ant.Project)"><!-- --></A><H3>
getExposedClass</H3>
<PRE>
public java.lang.Class <B>getExposedClass</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Get the exposed class for this
 definition. This will be a proxy class
 (adapted class) if there is an adapter
 class and the definition class is not
 assignable from the assignable class.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the current project.
<DT><B>Returns:</B><DD>the exposed class.</DL>
</DD>
</DL>
<HR>

<A NAME="getTypeClass(org.apache.tools.ant.Project)"><!-- --></A><H3>
getTypeClass</H3>
<PRE>
public java.lang.Class <B>getTypeClass</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Get the definition class.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the current project.
<DT><B>Returns:</B><DD>the type of the definition.</DL>
</DD>
</DL>
<HR>

<A NAME="innerGetTypeClass()"><!-- --></A><H3>
innerGetTypeClass</H3>
<PRE>
public java.lang.Class <B>innerGetTypeClass</B>()
                                  throws java.lang.ClassNotFoundException</PRE>
<DL>
<DD>Try and load a class, with no attempt to catch any fault.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the class that implements this component
<DT><B>Throws:</B>
<DD><CODE>java.lang.ClassNotFoundException</CODE> - if the class cannot be found.
<DD><CODE>java.lang.NoClassDefFoundError</CODE> - if the there is an error
                                finding the class.</DL>
</DD>
</DL>
<HR>

<A NAME="create(org.apache.tools.ant.Project)"><!-- --></A><H3>
create</H3>
<PRE>
public java.lang.Object <B>create</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Create an instance of the definition.
 The instance may be wrapped in a proxy class.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the current project.
<DT><B>Returns:</B><DD>the created object.</DL>
</DD>
</DL>
<HR>

<A NAME="checkClass(org.apache.tools.ant.Project)"><!-- --></A><H3>
checkClass</H3>
<PRE>
public void <B>checkClass</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Checks if the attributes are correct.
 <dl>
   <li>if the class can be created.</li>
   <li>if an adapter class can be created</li>
   <li>if the type is assignable from adapto</li>
   <li>if the type can be used with the adapter class</li>
 </dl>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the current project.</DL>
</DD>
</DL>
<HR>

<A NAME="innerCreateAndSet(java.lang.Class, org.apache.tools.ant.Project)"><!-- --></A><H3>
innerCreateAndSet</H3>
<PRE>
public java.lang.Object <B>innerCreateAndSet</B>(java.lang.Class&nbsp;newclass,
                                          <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)
                                   throws java.lang.NoSuchMethodException,
                                          java.lang.InstantiationException,
                                          java.lang.IllegalAccessException,
                                          java.lang.reflect.InvocationTargetException</PRE>
<DL>
<DD>Inner implementation of the <A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html#createAndSet(org.apache.tools.ant.Project, java.lang.Class)"><CODE>createAndSet(Project, Class)</CODE></A> logic, with no
 exception catching
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>newclass</CODE> - class to create<DD><CODE>project</CODE> - the project to use
<DT><B>Returns:</B><DD>a newly constructed and bound instance.
<DT><B>Throws:</B>
<DD><CODE>java.lang.NoSuchMethodException</CODE> - no good construtor.
<DD><CODE>java.lang.InstantiationException</CODE> - cannot initialize the object.
<DD><CODE>java.lang.IllegalAccessException</CODE> - cannot access the object.
<DD><CODE>java.lang.reflect.InvocationTargetException</CODE> - error in invocation.</DL>
</DD>
</DL>
<HR>

<A NAME="sameDefinition(org.apache.tools.ant.AntTypeDefinition, org.apache.tools.ant.Project)"><!-- --></A><H3>
sameDefinition</H3>
<PRE>
public boolean <B>sameDefinition</B>(<A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A>&nbsp;other,
                              <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Equality method for this definition (assumes the names are the same).
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>other</CODE> - another definition.<DD><CODE>project</CODE> - the project the definition.
<DT><B>Returns:</B><DD>true if the definitions are the same.</DL>
</DD>
</DL>
<HR>

<A NAME="similarDefinition(org.apache.tools.ant.AntTypeDefinition, org.apache.tools.ant.Project)"><!-- --></A><H3>
similarDefinition</H3>
<PRE>
public boolean <B>similarDefinition</B>(<A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A>&nbsp;other,
                                 <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Similar definition;
 used to compare two definitions defined twice with the same
 name and the same types.
 The classloader may be different but have the same
 path so #sameDefinition cannot
 be used.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>other</CODE> - the definition to compare to.<DD><CODE>project</CODE> - the current project.
<DT><B>Returns:</B><DD>true if the definitions are the same.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/AntClassLoader.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/AntTypeDefinition.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="AntTypeDefinition.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
