<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:25 EST 2006 -->
<TITLE>
WaitFor (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.WaitFor class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="WaitFor (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/VerifyJar.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/WaitFor.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="WaitFor.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.ProjectComponent">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class WaitFor</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html" title="class in org.apache.tools.ant.taskdefs.condition">org.apache.tools.ant.taskdefs.condition.ConditionBase</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.WaitFor</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable, <A HREF="../../../../../org/apache/tools/ant/DynamicElement.html" title="interface in org.apache.tools.ant">DynamicElement</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>WaitFor</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html" title="class in org.apache.tools.ant.taskdefs.condition">ConditionBase</A></DL>
</PRE>

<P>
Wait for an external event to occur.

 Wait for an external process to start or to complete some
 task. This is useful with the <code>parallel</code> task to
 synchronize the execution of tests with server startup.

 The following attributes can be specified on a waitfor task:
 <ul>
 <li>maxwait - maximum length of time to wait before giving up</li>
 <li>maxwaitunit - The unit to be used to interpret maxwait attribute</li>
 <li>checkevery - amount of time to sleep between each check</li>
 <li>checkeveryunit - The unit to be used to interpret checkevery attribute</li>
 <li>timeoutproperty - name of a property to set if maxwait has been exceeded.</li>
 </ul>

 The maxwaitunit and checkeveryunit are allowed to have the following values:
 millisecond, second, minute, hour, day and week. The default is millisecond.

 For programmatic use/subclassing, there are two methods that may be overrridden,
 <code>processSuccess</code> and <code>processTimeout</code>
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The enumeration of units:
 millisecond, second, minute, hour, day, week</TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.html#WaitFor()">WaitFor</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor, names this task "waitfor".</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Check repeatedly for the specified conditions until they become
 true or the timeout expires.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.html#processSuccess()">processSuccess</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Actions to be taken on a successful waitfor.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.html#processTimeout()">processTimeout</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Actions to be taken on an unsuccessful wait.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.html#setCheckEvery(long)">setCheckEvery</A></B>(long&nbsp;time)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the time between each check</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.html#setCheckEveryUnit(org.apache.tools.ant.taskdefs.WaitFor.Unit)">setCheckEveryUnit</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</A>&nbsp;unit)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the check every time unit</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.html#setMaxWait(long)">setMaxWait</A></B>(long&nbsp;time)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the maximum length of time to wait.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.html#setMaxWaitUnit(org.apache.tools.ant.taskdefs.WaitFor.Unit)">setMaxWaitUnit</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</A>&nbsp;unit)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the max wait time unit</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.html#setTimeoutProperty(java.lang.String)">setTimeoutProperty</A></B>(java.lang.String&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Name the property to set after a timeout.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.condition.ConditionBase"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.condition.<A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html" title="class in org.apache.tools.ant.taskdefs.condition">ConditionBase</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#add(org.apache.tools.ant.taskdefs.condition.Condition)">add</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addAnd(org.apache.tools.ant.taskdefs.condition.And)">addAnd</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addAvailable(org.apache.tools.ant.taskdefs.Available)">addAvailable</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addChecksum(org.apache.tools.ant.taskdefs.Checksum)">addChecksum</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addContains(org.apache.tools.ant.taskdefs.condition.Contains)">addContains</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addEquals(org.apache.tools.ant.taskdefs.condition.Equals)">addEquals</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addFilesMatch(org.apache.tools.ant.taskdefs.condition.FilesMatch)">addFilesMatch</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addHttp(org.apache.tools.ant.taskdefs.condition.Http)">addHttp</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addIsFalse(org.apache.tools.ant.taskdefs.condition.IsFalse)">addIsFalse</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addIsFileSelected(org.apache.tools.ant.taskdefs.condition.IsFileSelected)">addIsFileSelected</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addIsReference(org.apache.tools.ant.taskdefs.condition.IsReference)">addIsReference</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addIsSet(org.apache.tools.ant.taskdefs.condition.IsSet)">addIsSet</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addIsTrue(org.apache.tools.ant.taskdefs.condition.IsTrue)">addIsTrue</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addNot(org.apache.tools.ant.taskdefs.condition.Not)">addNot</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addOr(org.apache.tools.ant.taskdefs.condition.Or)">addOr</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addOs(org.apache.tools.ant.taskdefs.condition.Os)">addOs</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addSocket(org.apache.tools.ant.taskdefs.condition.Socket)">addSocket</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#addUptodate(org.apache.tools.ant.taskdefs.UpToDate)">addUptodate</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#countConditions()">countConditions</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#createDynamicElement(java.lang.String)">createDynamicElement</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#getConditions()">getConditions</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html#setTaskName(java.lang.String)">setTaskName</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="WaitFor()"><!-- --></A><H3>
WaitFor</H3>
<PRE>
public <B>WaitFor</B>()</PRE>
<DL>
<DD>Constructor, names this task "waitfor".
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setMaxWait(long)"><!-- --></A><H3>
setMaxWait</H3>
<PRE>
public void <B>setMaxWait</B>(long&nbsp;time)</PRE>
<DL>
<DD>Set the maximum length of time to wait.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>time</CODE> - a <code>long</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="setMaxWaitUnit(org.apache.tools.ant.taskdefs.WaitFor.Unit)"><!-- --></A><H3>
setMaxWaitUnit</H3>
<PRE>
public void <B>setMaxWaitUnit</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</A>&nbsp;unit)</PRE>
<DL>
<DD>Set the max wait time unit
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>unit</CODE> - an enumerated <code>Unit</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="setCheckEvery(long)"><!-- --></A><H3>
setCheckEvery</H3>
<PRE>
public void <B>setCheckEvery</B>(long&nbsp;time)</PRE>
<DL>
<DD>Set the time between each check
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>time</CODE> - a <code>long</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="setCheckEveryUnit(org.apache.tools.ant.taskdefs.WaitFor.Unit)"><!-- --></A><H3>
setCheckEveryUnit</H3>
<PRE>
public void <B>setCheckEveryUnit</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</A>&nbsp;unit)</PRE>
<DL>
<DD>Set the check every time unit
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>unit</CODE> - an enumerated <code>Unit</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="setTimeoutProperty(java.lang.String)"><!-- --></A><H3>
setTimeoutProperty</H3>
<PRE>
public void <B>setTimeoutProperty</B>(java.lang.String&nbsp;p)</PRE>
<DL>
<DD>Name the property to set after a timeout.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the property name</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Check repeatedly for the specified conditions until they become
 true or the timeout expires.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="processSuccess()"><!-- --></A><H3>
processSuccess</H3>
<PRE>
protected void <B>processSuccess</B>()</PRE>
<DL>
<DD>Actions to be taken on a successful waitfor.
 This is an override point. The base implementation does nothing.
<P>
<DD><DL>
<DT><B>Since:</B></DT>
  <DD>Ant1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="processTimeout()"><!-- --></A><H3>
processTimeout</H3>
<PRE>
protected void <B>processTimeout</B>()</PRE>
<DL>
<DD>Actions to be taken on an unsuccessful wait.
 This is an override point. It is where the timeout processing takes place.
 The base implementation sets the timeoutproperty if there was a timeout
 and the property was defined.
<P>
<DD><DL>
<DT><B>Since:</B></DT>
  <DD>Ant1.7</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/VerifyJar.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/WaitFor.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="WaitFor.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.ProjectComponent">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
