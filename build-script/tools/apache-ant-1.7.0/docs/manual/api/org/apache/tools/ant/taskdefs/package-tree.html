<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:35 EST 2006 -->
<TITLE>
org.apache.tools.ant.taskdefs Class Hierarchy (Apache Ant API)
</TITLE>


<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="org.apache.tools.ant.taskdefs Class Hierarchy (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Tree</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/loader/package-tree.html"><B>PREV</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/compilers/package-tree.html"><B>NEXT</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/package-tree.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="package-tree.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<CENTER>
<H2>
Hierarchy For Package org.apache.tools.ant.taskdefs
</H2>
</CENTER>
<DL>
<DT><B>Package Hierarchies:</B><DD><A HREF="../../../../../overview-tree.html">All Packages</A></DL>
<HR>
<H2>
Class Hierarchy
</H2>
<UL>
<LI TYPE="circle">java.lang.Object<UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Ant.TargetElement.html" title="class in org.apache.tools.ant.taskdefs"><B>Ant.TargetElement</B></A><LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant"><B>AntTypeDefinition</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.PreSetDefinition.html" title="class in org.apache.tools.ant.taskdefs"><B>PreSetDef.PreSetDefinition</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.Option.html" title="class in org.apache.tools.ant.taskdefs"><B>Apt.Option</B></A><LI TYPE="circle">org.apache.tools.ant.util.<A HREF="../../../../../org/apache/tools/ant/util/Base64Converter.html" title="class in org.apache.tools.ant.util"><B>Base64Converter</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Get.Base64Converter.html" title="class in org.apache.tools.ant.taskdefs"><B>Get.Base64Converter</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/EnumeratedAttribute.html" title="class in org.apache.tools.ant.types"><B>EnumeratedAttribute</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Available.FileDir.html" title="class in org.apache.tools.ant.taskdefs"><B>Available.FileDir</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Checksum.FormatElement.html" title="class in org.apache.tools.ant.taskdefs"><B>Checksum.FormatElement</B></A><LI TYPE="circle">org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/Comparison.html" title="class in org.apache.tools.ant.types"><B>Comparison</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Length.When.html" title="class in org.apache.tools.ant.taskdefs"><B>Length.When</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Definer.Format.html" title="class in org.apache.tools.ant.taskdefs"><B>Definer.Format</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Definer.OnError.html" title="class in org.apache.tools.ant.taskdefs"><B>Definer.OnError</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.FileDirBoth.html" title="class in org.apache.tools.ant.taskdefs"><B>ExecuteOn.FileDirBoth</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/FixCRLF.AddAsisRemove.html" title="class in org.apache.tools.ant.taskdefs"><B>FixCRLF.AddAsisRemove</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/FixCRLF.CrLf.html" title="class in org.apache.tools.ant.taskdefs"><B>FixCRLF.CrLf</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Input.HandlerType.html" title="class in org.apache.tools.ant.taskdefs"><B>Input.HandlerType</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Jar.FilesetManifestConfig.html" title="class in org.apache.tools.ant.taskdefs"><B>Jar.FilesetManifestConfig</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.AccessType.html" title="class in org.apache.tools.ant.taskdefs"><B>Javadoc.AccessType</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Length.FileMode.html" title="class in org.apache.tools.ant.taskdefs"><B>Length.FileMode</B></A><LI TYPE="circle">org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/LogLevel.html" title="class in org.apache.tools.ant.types"><B>LogLevel</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Echo.EchoLevel.html" title="class in org.apache.tools.ant.taskdefs"><B>Echo.EchoLevel</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Recorder.VerbosityLevelChoices.html" title="class in org.apache.tools.ant.taskdefs"><B>Recorder.VerbosityLevelChoices</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestTask.Mode.html" title="class in org.apache.tools.ant.taskdefs"><B>ManifestTask.Mode</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/PathConvert.TargetOs.html" title="class in org.apache.tools.ant.taskdefs"><B>PathConvert.TargetOs</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Recorder.ActionChoices.html" title="class in org.apache.tools.ant.taskdefs"><B>Recorder.ActionChoices</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.DelimiterType.html" title="class in org.apache.tools.ant.taskdefs"><B>SQLExec.DelimiterType</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.OnError.html" title="class in org.apache.tools.ant.taskdefs"><B>SQLExec.OnError</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarCompressionMethod.html" title="class in org.apache.tools.ant.taskdefs"><B>Tar.TarCompressionMethod</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarLongFileMode.html" title="class in org.apache.tools.ant.taskdefs"><B>Tar.TarLongFileMode</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Tstamp.Unit.html" title="class in org.apache.tools.ant.taskdefs"><B>Tstamp.Unit</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Untar.UntarCompressionMethod.html" title="class in org.apache.tools.ant.taskdefs"><B>Untar.UntarCompressionMethod</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs"><B>WaitFor.Unit</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.Duplicate.html" title="class in org.apache.tools.ant.taskdefs"><B>Zip.Duplicate</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.WhenEmpty.html" title="class in org.apache.tools.ant.taskdefs"><B>Zip.WhenEmpty</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs"><B>Execute</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html" title="class in org.apache.tools.ant.taskdefs"><B>ExecuteJava</B></A> (implements java.lang.Runnable, org.apache.tools.ant.util.<A HREF="../../../../../org/apache/tools/ant/util/TimeoutObserver.html" title="interface in org.apache.tools.ant.util">TimeoutObserver</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs"><B>ExecuteWatchdog</B></A> (implements org.apache.tools.ant.util.<A HREF="../../../../../org/apache/tools/ant/util/TimeoutObserver.html" title="interface in org.apache.tools.ant.util">TimeoutObserver</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/FixCRLF.OneLiner.html" title="class in org.apache.tools.ant.taskdefs"><B>FixCRLF.OneLiner</B></A> (implements java.util.Enumeration&lt;E&gt;)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/GenerateKey.DistinguishedName.html" title="class in org.apache.tools.ant.taskdefs"><B>GenerateKey.DistinguishedName</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/GenerateKey.DnameParam.html" title="class in org.apache.tools.ant.taskdefs"><B>GenerateKey.DnameParam</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Get.NullProgress.html" title="class in org.apache.tools.ant.taskdefs"><B>Get.NullProgress</B></A> (implements org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Get.DownloadProgress.html" title="interface in org.apache.tools.ant.taskdefs">Get.DownloadProgress</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Get.VerboseProgress.html" title="class in org.apache.tools.ant.taskdefs"><B>Get.VerboseProgress</B></A> (implements org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Get.DownloadProgress.html" title="interface in org.apache.tools.ant.taskdefs">Get.DownloadProgress</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.DocletParam.html" title="class in org.apache.tools.ant.taskdefs"><B>Javadoc.DocletParam</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.GroupArgument.html" title="class in org.apache.tools.ant.taskdefs"><B>Javadoc.GroupArgument</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.Html.html" title="class in org.apache.tools.ant.taskdefs"><B>Javadoc.Html</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.LinkArgument.html" title="class in org.apache.tools.ant.taskdefs"><B>Javadoc.LinkArgument</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.PackageName.html" title="class in org.apache.tools.ant.taskdefs"><B>Javadoc.PackageName</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.ResourceCollectionContainer.html" title="class in org.apache.tools.ant.taskdefs"><B>Javadoc.ResourceCollectionContainer</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.SourceFile.html" title="class in org.apache.tools.ant.taskdefs"><B>Javadoc.SourceFile</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Jikes.html" title="class in org.apache.tools.ant.taskdefs"><B>Jikes</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/JikesOutputParser.html" title="class in org.apache.tools.ant.taskdefs"><B>JikesOutputParser</B></A> (implements org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.Attribute.html" title="class in org.apache.tools.ant.taskdefs"><B>MacroDef.Attribute</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.NestedSequential.html" title="class in org.apache.tools.ant.taskdefs"><B>MacroDef.NestedSequential</B></A> (implements org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.TemplateElement.html" title="class in org.apache.tools.ant.taskdefs"><B>MacroDef.TemplateElement</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.Text.html" title="class in org.apache.tools.ant.taskdefs"><B>MacroDef.Text</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroInstance.Element.html" title="class in org.apache.tools.ant.taskdefs"><B>MacroInstance.Element</B></A> (implements org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html" title="class in org.apache.tools.ant.taskdefs"><B>Manifest</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs"><B>Manifest.Attribute</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs"><B>Manifest.Section</B></A><LI TYPE="circle">java.io.OutputStream (implements java.io.Closeable, java.io.Flushable)
<UL>
<LI TYPE="circle">org.apache.tools.ant.util.<A HREF="../../../../../org/apache/tools/ant/util/LineOrientedOutputStream.html" title="class in org.apache.tools.ant.util"><B>LineOrientedOutputStream</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/LogOutputStream.html" title="class in org.apache.tools.ant.taskdefs"><B>LogOutputStream</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/TaskOutputStream.html" title="class in org.apache.tools.ant.taskdefs"><B>TaskOutputStream</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Parallel.TaskList.html" title="class in org.apache.tools.ant.taskdefs"><B>Parallel.TaskList</B></A> (implements org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/PathConvert.MapEntry.html" title="class in org.apache.tools.ant.taskdefs"><B>PathConvert.MapEntry</B></A><LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant"><B>ProjectComponent</B></A> (implements java.lang.Cloneable)
<UL>
<LI TYPE="circle">org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/Commandline.Argument.html" title="class in org.apache.tools.ant.types"><B>Commandline.Argument</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.util.facade.<A HREF="../../../../../org/apache/tools/ant/util/facade/ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.util.facade"><B>ImplementationSpecificArgument</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.taskdefs"><B>Javac.ImplementationSpecificArgument</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.taskdefs"><B>Rmic.ImplementationSpecificArgument</B></A></UL>
</UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Concat.TextElement.html" title="class in org.apache.tools.ant.taskdefs"><B>Concat.TextElement</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.condition.<A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/ConditionBase.html" title="class in org.apache.tools.ant.taskdefs.condition"><B>ConditionBase</B></A> (implements org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/DynamicElement.html" title="interface in org.apache.tools.ant">DynamicElement</A>)
<UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ConditionTask.html" title="class in org.apache.tools.ant.taskdefs"><B>ConditionTask</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/WaitFor.html" title="class in org.apache.tools.ant.taskdefs"><B>WaitFor</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types"><B>DataType</B></A> (implements java.lang.Cloneable)
<UL>
<LI TYPE="circle">org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/AbstractFileSet.html" title="class in org.apache.tools.ant.types"><B>AbstractFileSet</B></A> (implements java.lang.Cloneable, org.apache.tools.ant.types.selectors.<A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A>)
<UL>
<LI TYPE="circle">org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types"><B>FileSet</B></A> (implements org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>)
<UL>
<LI TYPE="circle">org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/ArchiveFileSet.html" title="class in org.apache.tools.ant.types"><B>ArchiveFileSet</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/TarFileSet.html" title="class in org.apache.tools.ant.types"><B>TarFileSet</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.TarFileSet.html" title="class in org.apache.tools.ant.taskdefs"><B>Tar.TarFileSet</B></A></UL>
</UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.TagArgument.html" title="class in org.apache.tools.ant.taskdefs"><B>Javadoc.TagArgument</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.SyncTarget.html" title="class in org.apache.tools.ant.taskdefs"><B>Sync.SyncTarget</B></A></UL>
</UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.ExtensionInfo.html" title="class in org.apache.tools.ant.taskdefs"><B>Javadoc.ExtensionInfo</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.DocletInfo.html" title="class in org.apache.tools.ant.taskdefs"><B>Javadoc.DocletInfo</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant"><B>Task</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html" title="class in org.apache.tools.ant.taskdefs"><B>AbstractCvsTask</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Cvs.html" title="class in org.apache.tools.ant.taskdefs"><B>Cvs</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs"><B>AbstractJarSignerTask</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html" title="class in org.apache.tools.ant.taskdefs"><B>SignJar</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/VerifyJar.html" title="class in org.apache.tools.ant.taskdefs"><B>VerifyJar</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Ant.html" title="class in org.apache.tools.ant.taskdefs"><B>Ant</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Antlib.html" title="class in org.apache.tools.ant.taskdefs"><B>Antlib</B></A> (implements org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/AntlibDefinition.html" title="class in org.apache.tools.ant.taskdefs"><B>AntlibDefinition</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/DefBase.html" title="class in org.apache.tools.ant.taskdefs"><B>DefBase</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Definer.html" title="class in org.apache.tools.ant.taskdefs"><B>Definer</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Typedef.html" title="class in org.apache.tools.ant.taskdefs"><B>Typedef</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Taskdef.html" title="class in org.apache.tools.ant.taskdefs"><B>Taskdef</B></A></UL>
</UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Input.Handler.html" title="class in org.apache.tools.ant.taskdefs"><B>Input.Handler</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroDef.html" title="class in org.apache.tools.ant.taskdefs"><B>MacroDef</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/PreSetDef.html" title="class in org.apache.tools.ant.taskdefs"><B>PreSetDef</B></A> (implements org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</A>)
</UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/AntStructure.html" title="class in org.apache.tools.ant.taskdefs"><B>AntStructure</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Available.html" title="class in org.apache.tools.ant.taskdefs"><B>Available</B></A> (implements org.apache.tools.ant.taskdefs.condition.<A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Basename.html" title="class in org.apache.tools.ant.taskdefs"><B>Basename</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/BuildNumber.html" title="class in org.apache.tools.ant.taskdefs"><B>BuildNumber</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/CallTarget.html" title="class in org.apache.tools.ant.taskdefs"><B>CallTarget</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Classloader.html" title="class in org.apache.tools.ant.taskdefs"><B>Classloader</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Concat.html" title="class in org.apache.tools.ant.taskdefs"><B>Concat</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html" title="class in org.apache.tools.ant.taskdefs"><B>Copy</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Move.html" title="class in org.apache.tools.ant.taskdefs"><B>Move</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.MyCopy.html" title="class in org.apache.tools.ant.taskdefs"><B>Sync.MyCopy</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Copyfile.html" title="class in org.apache.tools.ant.taskdefs"><B>Copyfile</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/CopyPath.html" title="class in org.apache.tools.ant.taskdefs"><B>CopyPath</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/CVSPass.html" title="class in org.apache.tools.ant.taskdefs"><B>CVSPass</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/DefaultExcludes.html" title="class in org.apache.tools.ant.taskdefs"><B>DefaultExcludes</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Deltree.html" title="class in org.apache.tools.ant.taskdefs"><B>Deltree</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/DiagnosticsTask.html" title="class in org.apache.tools.ant.taskdefs"><B>DiagnosticsTask</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Dirname.html" title="class in org.apache.tools.ant.taskdefs"><B>Dirname</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Echo.html" title="class in org.apache.tools.ant.taskdefs"><B>Echo</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.email.<A HREF="../../../../../org/apache/tools/ant/taskdefs/email/EmailTask.html" title="class in org.apache.tools.ant.taskdefs.email"><B>EmailTask</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/SendEmail.html" title="class in org.apache.tools.ant.taskdefs"><B>SendEmail</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Exec.html" title="class in org.apache.tools.ant.taskdefs"><B>Exec</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs"><B>ExecTask</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs"><B>ExecuteOn</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Chmod.html" title="class in org.apache.tools.ant.taskdefs"><B>Chmod</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Transform.html" title="class in org.apache.tools.ant.taskdefs"><B>Transform</B></A></UL>
</UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Exit.html" title="class in org.apache.tools.ant.taskdefs"><B>Exit</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Expand.html" title="class in org.apache.tools.ant.taskdefs"><B>Expand</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Untar.html" title="class in org.apache.tools.ant.taskdefs"><B>Untar</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Filter.html" title="class in org.apache.tools.ant.taskdefs"><B>Filter</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/GenerateKey.html" title="class in org.apache.tools.ant.taskdefs"><B>GenerateKey</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Get.html" title="class in org.apache.tools.ant.taskdefs"><B>Get</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ImportTask.html" title="class in org.apache.tools.ant.taskdefs"><B>ImportTask</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Input.html" title="class in org.apache.tools.ant.taskdefs"><B>Input</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Java.html" title="class in org.apache.tools.ant.taskdefs"><B>Java</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javadoc.html" title="class in org.apache.tools.ant.taskdefs"><B>Javadoc</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/JDBCTask.html" title="class in org.apache.tools.ant.taskdefs"><B>JDBCTask</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.html" title="class in org.apache.tools.ant.taskdefs"><B>SQLExec</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/KeySubst.html" title="class in org.apache.tools.ant.taskdefs"><B>KeySubst</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Length.html" title="class in org.apache.tools.ant.taskdefs"><B>Length</B></A> (implements org.apache.tools.ant.taskdefs.condition.<A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/LoadProperties.html" title="class in org.apache.tools.ant.taskdefs"><B>LoadProperties</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/LoadResource.html" title="class in org.apache.tools.ant.taskdefs"><B>LoadResource</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/LoadFile.html" title="class in org.apache.tools.ant.taskdefs"><B>LoadFile</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MacroInstance.html" title="class in org.apache.tools.ant.taskdefs"><B>MacroInstance</B></A> (implements org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/DynamicAttribute.html" title="interface in org.apache.tools.ant">DynamicAttribute</A>, org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MakeUrl.html" title="class in org.apache.tools.ant.taskdefs"><B>MakeUrl</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestClassPath.html" title="class in org.apache.tools.ant.taskdefs"><B>ManifestClassPath</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestTask.html" title="class in org.apache.tools.ant.taskdefs"><B>ManifestTask</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs"><B>MatchingTask</B></A> (implements org.apache.tools.ant.types.selectors.<A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A>)
<UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Checksum.html" title="class in org.apache.tools.ant.taskdefs"><B>Checksum</B></A> (implements org.apache.tools.ant.taskdefs.condition.<A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Copydir.html" title="class in org.apache.tools.ant.taskdefs"><B>Copydir</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Delete.html" title="class in org.apache.tools.ant.taskdefs"><B>Delete</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/DependSet.html" title="class in org.apache.tools.ant.taskdefs"><B>DependSet</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/FixCRLF.html" title="class in org.apache.tools.ant.taskdefs"><B>FixCRLF</B></A> (implements org.apache.tools.ant.filters.<A HREF="../../../../../org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Javac.html" title="class in org.apache.tools.ant.taskdefs"><B>Javac</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Apt.html" title="class in org.apache.tools.ant.taskdefs"><B>Apt</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Replace.html" title="class in org.apache.tools.ant.taskdefs"><B>Replace</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html" title="class in org.apache.tools.ant.taskdefs"><B>Rmic</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Tar.html" title="class in org.apache.tools.ant.taskdefs"><B>Tar</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.html" title="class in org.apache.tools.ant.taskdefs"><B>XSLTProcess</B></A> (implements org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLogger.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLogger</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.html" title="class in org.apache.tools.ant.taskdefs"><B>Zip</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Jar.html" title="class in org.apache.tools.ant.taskdefs"><B>Jar</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Ear.html" title="class in org.apache.tools.ant.taskdefs"><B>Ear</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/War.html" title="class in org.apache.tools.ant.taskdefs"><B>War</B></A></UL>
</UL>
</UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Mkdir.html" title="class in org.apache.tools.ant.taskdefs"><B>Mkdir</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Nice.html" title="class in org.apache.tools.ant.taskdefs"><B>Nice</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Pack.html" title="class in org.apache.tools.ant.taskdefs"><B>Pack</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/BZip2.html" title="class in org.apache.tools.ant.taskdefs"><B>BZip2</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/GZip.html" title="class in org.apache.tools.ant.taskdefs"><B>GZip</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Parallel.html" title="class in org.apache.tools.ant.taskdefs"><B>Parallel</B></A> (implements org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Patch.html" title="class in org.apache.tools.ant.taskdefs"><B>Patch</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/PathConvert.html" title="class in org.apache.tools.ant.taskdefs"><B>PathConvert</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Property.html" title="class in org.apache.tools.ant.taskdefs"><B>Property</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Recorder.html" title="class in org.apache.tools.ant.taskdefs"><B>Recorder</B></A> (implements org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Rename.html" title="class in org.apache.tools.ant.taskdefs"><B>Rename</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ResourceCount.html" title="class in org.apache.tools.ant.taskdefs"><B>ResourceCount</B></A> (implements org.apache.tools.ant.taskdefs.condition.<A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Sequential.html" title="class in org.apache.tools.ant.taskdefs"><B>Sequential</B></A> (implements org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Sleep.html" title="class in org.apache.tools.ant.taskdefs"><B>Sleep</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/SubAnt.html" title="class in org.apache.tools.ant.taskdefs"><B>SubAnt</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.html" title="class in org.apache.tools.ant.taskdefs"><B>Sync</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/TempFile.html" title="class in org.apache.tools.ant.taskdefs"><B>TempFile</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Touch.html" title="class in org.apache.tools.ant.taskdefs"><B>Touch</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Tstamp.html" title="class in org.apache.tools.ant.taskdefs"><B>Tstamp</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Unpack.html" title="class in org.apache.tools.ant.taskdefs"><B>Unpack</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/BUnzip2.html" title="class in org.apache.tools.ant.taskdefs"><B>BUnzip2</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/GUnzip.html" title="class in org.apache.tools.ant.taskdefs"><B>GUnzip</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/UpToDate.html" title="class in org.apache.tools.ant.taskdefs"><B>UpToDate</B></A> (implements org.apache.tools.ant.taskdefs.condition.<A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/Condition.html" title="interface in org.apache.tools.ant.taskdefs.condition">Condition</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/WhichResource.html" title="class in org.apache.tools.ant.taskdefs"><B>WhichResource</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/XmlProperty.html" title="class in org.apache.tools.ant.taskdefs"><B>XmlProperty</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.util.<A HREF="../../../../../org/apache/tools/ant/util/XMLFragment.html" title="class in org.apache.tools.ant.util"><B>XMLFragment</B></A> (implements org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/DynamicElementNS.html" title="interface in org.apache.tools.ant">DynamicElementNS</A>)
<UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/EchoXML.html" title="class in org.apache.tools.ant.taskdefs"><B>EchoXML</B></A></UL>
</UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/PumpStreamHandler.html" title="class in org.apache.tools.ant.taskdefs"><B>PumpStreamHandler</B></A> (implements org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A>)
<UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/LogStreamHandler.html" title="class in org.apache.tools.ant.taskdefs"><B>LogStreamHandler</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html" title="class in org.apache.tools.ant.taskdefs"><B>RecorderEntry</B></A> (implements org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A>, org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html" title="class in org.apache.tools.ant.taskdefs"><B>Redirector</B></A><LI TYPE="circle">org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types"><B>Reference</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Ant.Reference.html" title="class in org.apache.tools.ant.taskdefs"><B>Ant.Reference</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Replace.NestedString.html" title="class in org.apache.tools.ant.taskdefs"><B>Replace.NestedString</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Replace.Replacefilter.html" title="class in org.apache.tools.ant.taskdefs"><B>Replace.Replacefilter</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/SQLExec.Transaction.html" title="class in org.apache.tools.ant.taskdefs"><B>SQLExec.Transaction</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/StreamPumper.html" title="class in org.apache.tools.ant.taskdefs"><B>StreamPumper</B></A> (implements java.lang.Runnable)
<LI TYPE="circle">java.lang.Throwable (implements java.io.Serializable)
<UL>
<LI TYPE="circle">java.lang.Exception<UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs"><B>ManifestException</B></A></UL>
</UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Tstamp.CustomFormat.html" title="class in org.apache.tools.ant.taskdefs"><B>Tstamp.CustomFormat</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Factory.html" title="class in org.apache.tools.ant.taskdefs"><B>XSLTProcess.Factory</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Factory.Attribute.html" title="class in org.apache.tools.ant.taskdefs"><B>XSLTProcess.Factory.Attribute</B></A> (implements org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/DynamicConfigurator.html" title="interface in org.apache.tools.ant">DynamicConfigurator</A>)
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.OutputProperty.html" title="class in org.apache.tools.ant.taskdefs"><B>XSLTProcess.OutputProperty</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTProcess.Param.html" title="class in org.apache.tools.ant.taskdefs"><B>XSLTProcess.Param</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Zip.ArchiveState.html" title="class in org.apache.tools.ant.taskdefs"><B>Zip.ArchiveState</B></A></UL>
</UL>
<H2>
Interface Hierarchy
</H2>
<UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/AntStructure.StructurePrinter.html" title="interface in org.apache.tools.ant.taskdefs"><B>AntStructure.StructurePrinter</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs"><B>ExecuteStreamHandler</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Get.DownloadProgress.html" title="interface in org.apache.tools.ant.taskdefs"><B>Get.DownloadProgress</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs"><B>XSLTLiaison</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLiaison2.html" title="interface in org.apache.tools.ant.taskdefs"><B>XSLTLiaison2</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLiaison3.html" title="interface in org.apache.tools.ant.taskdefs"><B>XSLTLiaison3</B></A></UL>
</UL>
<LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLogger.html" title="interface in org.apache.tools.ant.taskdefs"><B>XSLTLogger</B></A><LI TYPE="circle">org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/XSLTLoggerAware.html" title="interface in org.apache.tools.ant.taskdefs"><B>XSLTLoggerAware</B></A></UL>
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Tree</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/loader/package-tree.html"><B>PREV</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/compilers/package-tree.html"><B>NEXT</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/package-tree.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="package-tree.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
