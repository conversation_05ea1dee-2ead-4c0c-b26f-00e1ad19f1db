<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:24 EST 2006 -->
<TITLE>
RecorderEntry (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.RecorderEntry class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="RecorderEntry (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Recorder.VerbosityLevelChoices.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/RecorderEntry.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="RecorderEntry.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class RecorderEntry</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.RecorderEntry</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.util.EventListener, <A HREF="../../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A>, <A HREF="../../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A>, <A HREF="../../../../../org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>RecorderEntry</B><DT>extends java.lang.Object<DT>implements <A HREF="../../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A>, <A HREF="../../../../../org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</A></DL>
</PRE>

<P>
This is a class that represents a recorder. This is the listener to the
 build process.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.4</DD>
</DL>
<HR>

<P>

<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected </CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#RecorderEntry(java.lang.String)">RecorderEntry</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#buildFinished(org.apache.tools.ant.BuildEvent)">buildFinished</A></B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Signals that the last target has finished..</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#buildStarted(org.apache.tools.ant.BuildEvent)">buildStarted</A></B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Signals that a build has started..</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#cleanup()">cleanup</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#getFilename()">getFilename</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</A></B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Signals a message logging event..</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#setEmacsMode(boolean)">setEmacsMode</A></B>(boolean&nbsp;emacsMode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets this logger to produce emacs (and other editor) friendly output..</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#setErrorPrintStream(java.io.PrintStream)">setErrorPrintStream</A></B>(java.io.PrintStream&nbsp;err)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the output stream to which this logger is to send error messages..</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#setMessageOutputLevel(int)">setMessageOutputLevel</A></B>(int&nbsp;level)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the highest level of message this logger should respond to..</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#setOutputPrintStream(java.io.PrintStream)">setOutputPrintStream</A></B>(java.io.PrintStream&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the output stream to which this logger is to send its output..</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#setProject(org.apache.tools.ant.Project)">setProject</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the project associated with this recorder entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#setRecordState(java.lang.Boolean)">setRecordState</A></B>(java.lang.Boolean&nbsp;state)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Turns off or on this recorder.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#subBuildFinished(org.apache.tools.ant.BuildEvent)">subBuildFinished</A></B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Cleans up any resources held by this recorder entry at the end
 of a subbuild if it has been created for the subbuild's project
 instance.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#subBuildStarted(org.apache.tools.ant.BuildEvent)">subBuildStarted</A></B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Empty implementation to satisfy the BuildListener interface.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</A></B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Signals that a target has finished..</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</A></B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Signals that a target is starting..</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#taskFinished(org.apache.tools.ant.BuildEvent)">taskFinished</A></B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Signals that a task has finished..</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html#taskStarted(org.apache.tools.ant.BuildEvent)">taskStarted</A></B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Signals that a task is starting..</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="RecorderEntry(java.lang.String)"><!-- --></A><H3>
RecorderEntry</H3>
<PRE>
protected <B>RecorderEntry</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - The name of this recorder (used as the filename).</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="getFilename()"><!-- --></A><H3>
getFilename</H3>
<PRE>
public java.lang.String <B>getFilename</B>()</PRE>
<DL>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the name of the file the output is sent to.</DL>
</DD>
</DL>
<HR>

<A NAME="setRecordState(java.lang.Boolean)"><!-- --></A><H3>
setRecordState</H3>
<PRE>
public void <B>setRecordState</B>(java.lang.Boolean&nbsp;state)</PRE>
<DL>
<DD>Turns off or on this recorder.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>state</CODE> - true for on, false for off, null for no change.</DL>
</DD>
</DL>
<HR>

<A NAME="buildStarted(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
buildStarted</H3>
<PRE>
public void <B>buildStarted</B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Signals that a build has started. This event
 is fired before any targets have started..
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildListener.html#buildStarted(org.apache.tools.ant.BuildEvent)">buildStarted</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - An event with any relevant extra information.
              Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="buildFinished(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
buildFinished</H3>
<PRE>
public void <B>buildFinished</B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Signals that the last target has finished. This event
 will still be fired if an error occurred during the build..
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildListener.html#buildFinished(org.apache.tools.ant.BuildEvent)">buildFinished</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - An event with any relevant extra information.
              Must not be <code>null</code>.<DT><B>See Also:</B><DD><A HREF="../../../../../org/apache/tools/ant/BuildEvent.html#getException()"><CODE>BuildEvent.getException()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="subBuildFinished(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
subBuildFinished</H3>
<PRE>
public void <B>subBuildFinished</B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Cleans up any resources held by this recorder entry at the end
 of a subbuild if it has been created for the subbuild's project
 instance.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/SubBuildListener.html#subBuildFinished(org.apache.tools.ant.BuildEvent)">subBuildFinished</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - the buildFinished event<DT><B>Since:</B></DT>
  <DD>Ant 1.6.2</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../org/apache/tools/ant/BuildEvent.html#getException()"><CODE>BuildEvent.getException()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="subBuildStarted(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
subBuildStarted</H3>
<PRE>
public void <B>subBuildStarted</B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Empty implementation to satisfy the BuildListener interface.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/SubBuildListener.html#subBuildStarted(org.apache.tools.ant.BuildEvent)">subBuildStarted</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - the buildStarted event<DT><B>Since:</B></DT>
  <DD>Ant 1.6.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="targetStarted(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
targetStarted</H3>
<PRE>
public void <B>targetStarted</B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Signals that a target is starting..
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildListener.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - An event with any relevant extra information.
              Must not be <code>null</code>.<DT><B>See Also:</B><DD><A HREF="../../../../../org/apache/tools/ant/BuildEvent.html#getTarget()"><CODE>BuildEvent.getTarget()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="targetFinished(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
targetFinished</H3>
<PRE>
public void <B>targetFinished</B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Signals that a target has finished. This event will
 still be fired if an error occurred during the build..
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildListener.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - An event with any relevant extra information.
              Must not be <code>null</code>.<DT><B>See Also:</B><DD><A HREF="../../../../../org/apache/tools/ant/BuildEvent.html#getException()"><CODE>BuildEvent.getException()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="taskStarted(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
taskStarted</H3>
<PRE>
public void <B>taskStarted</B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Signals that a task is starting..
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildListener.html#taskStarted(org.apache.tools.ant.BuildEvent)">taskStarted</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - An event with any relevant extra information.
              Must not be <code>null</code>.<DT><B>See Also:</B><DD><A HREF="../../../../../org/apache/tools/ant/BuildEvent.html#getTask()"><CODE>BuildEvent.getTask()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="taskFinished(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
taskFinished</H3>
<PRE>
public void <B>taskFinished</B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Signals that a task has finished. This event will still
 be fired if an error occurred during the build..
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildListener.html#taskFinished(org.apache.tools.ant.BuildEvent)">taskFinished</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - An event with any relevant extra information.
              Must not be <code>null</code>.<DT><B>See Also:</B><DD><A HREF="../../../../../org/apache/tools/ant/BuildEvent.html#getException()"><CODE>BuildEvent.getException()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="messageLogged(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
messageLogged</H3>
<PRE>
public void <B>messageLogged</B>(<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Signals a message logging event..
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - An event with any relevant extra information.
              Must not be <code>null</code>.<DT><B>See Also:</B><DD><A HREF="../../../../../org/apache/tools/ant/BuildEvent.html#getMessage()"><CODE>BuildEvent.getMessage()</CODE></A>, 
<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html#getException()"><CODE>BuildEvent.getException()</CODE></A>, 
<A HREF="../../../../../org/apache/tools/ant/BuildEvent.html#getPriority()"><CODE>BuildEvent.getPriority()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setMessageOutputLevel(int)"><!-- --></A><H3>
setMessageOutputLevel</H3>
<PRE>
public void <B>setMessageOutputLevel</B>(int&nbsp;level)</PRE>
<DL>
<DD>Sets the highest level of message this logger should respond to.

 Only messages with a message level lower than or equal to the
 given level should be written to the log.
 <P>
 Constants for the message levels are in the
 <A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant"><CODE>Project</CODE></A> class. The order of the levels, from least
 to most verbose, is <code>MSG_ERR</code>, <code>MSG_WARN</code>,
 <code>MSG_INFO</code>, <code>MSG_VERBOSE</code>,
 <code>MSG_DEBUG</code>..
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildLogger.html#setMessageOutputLevel(int)">setMessageOutputLevel</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>level</CODE> - the logging level for the logger.</DL>
</DD>
</DL>
<HR>

<A NAME="setOutputPrintStream(java.io.PrintStream)"><!-- --></A><H3>
setOutputPrintStream</H3>
<PRE>
public void <B>setOutputPrintStream</B>(java.io.PrintStream&nbsp;output)</PRE>
<DL>
<DD>Sets the output stream to which this logger is to send its output..
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildLogger.html#setOutputPrintStream(java.io.PrintStream)">setOutputPrintStream</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - The output stream for the logger.
               Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setEmacsMode(boolean)"><!-- --></A><H3>
setEmacsMode</H3>
<PRE>
public void <B>setEmacsMode</B>(boolean&nbsp;emacsMode)</PRE>
<DL>
<DD>Sets this logger to produce emacs (and other editor) friendly output..
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildLogger.html#setEmacsMode(boolean)">setEmacsMode</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>emacsMode</CODE> - <code>true</code> if output is to be unadorned so that
                  emacs and other editors can parse files names, etc.</DL>
</DD>
</DL>
<HR>

<A NAME="setErrorPrintStream(java.io.PrintStream)"><!-- --></A><H3>
setErrorPrintStream</H3>
<PRE>
public void <B>setErrorPrintStream</B>(java.io.PrintStream&nbsp;err)</PRE>
<DL>
<DD>Sets the output stream to which this logger is to send error messages..
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildLogger.html#setErrorPrintStream(java.io.PrintStream)">setErrorPrintStream</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>err</CODE> - The error stream for the logger.
            Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setProject(org.apache.tools.ant.Project)"><!-- --></A><H3>
setProject</H3>
<PRE>
public void <B>setProject</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Set the project associated with this recorder entry.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the project instance<DT><B>Since:</B></DT>
  <DD>1.6.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="cleanup()"><!-- --></A><H3>
cleanup</H3>
<PRE>
public void <B>cleanup</B>()</PRE>
<DL>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Since:</B></DT>
  <DD>1.6.2</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Recorder.VerbosityLevelChoices.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/RecorderEntry.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="RecorderEntry.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
