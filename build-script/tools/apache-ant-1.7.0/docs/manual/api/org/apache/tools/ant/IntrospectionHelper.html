<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:20 EST 2006 -->
<TITLE>
IntrospectionHelper (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.IntrospectionHelper class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="IntrospectionHelper (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.Creator.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/IntrospectionHelper.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="IntrospectionHelper.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class IntrospectionHelper</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.IntrospectionHelper</B>
</PRE>
<HR>
<DL>
<DT><PRE>public final class <B>IntrospectionHelper</B><DT>extends java.lang.Object</DL>
</PRE>

<P>
Helper class that collects the methods a task or nested element
 holds to set attributes, create nested elements or hold PCDATA
 elements.
 The class is final as it has a private constructor.
<P>

<P>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.Creator.html" title="class in org.apache.tools.ant">IntrospectionHelper.Creator</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;creator - allows use of create/store external
 to IntrospectionHelper.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#addText(org.apache.tools.ant.Project, java.lang.Object, java.lang.String)">addText</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
        java.lang.Object&nbsp;element,
        java.lang.String&nbsp;text)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds PCDATA to an element, using the element's
 <code>void addText(String)</code> method, if it has one.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#clearCache()">clearCache</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Clears the static cache of on build finished.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#createElement(org.apache.tools.ant.Project, java.lang.Object, java.lang.String)">createElement</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
              java.lang.Object&nbsp;parent,
              java.lang.String&nbsp;elementName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             This is not a namespace aware method.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.reflect.Method</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#getAddTextMethod()">getAddTextMethod</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the addText method when the introspected
 class supports nested text.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Map</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#getAttributeMap()">getAttributeMap</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns a read-only map of attributes supported
 by the introspected class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.reflect.Method</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#getAttributeMethod(java.lang.String)">getAttributeMethod</A></B>(java.lang.String&nbsp;attributeName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the setter method of a named attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Enumeration</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#getAttributes()">getAttributes</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns an enumeration of the names of the attributes supported
 by the introspected class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#getAttributeType(java.lang.String)">getAttributeType</A></B>(java.lang.String&nbsp;attributeName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the type of a named attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.Creator.html" title="class in org.apache.tools.ant">IntrospectionHelper.Creator</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#getElementCreator(org.apache.tools.ant.Project, java.lang.String, java.lang.Object, java.lang.String, org.apache.tools.ant.UnknownElement)">getElementCreator</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                  java.lang.String&nbsp;parentUri,
                  java.lang.Object&nbsp;parent,
                  java.lang.String&nbsp;elementName,
                  <A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A>&nbsp;ue)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;returns an object that creates and stores an object
 for an element of a parent.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.reflect.Method</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#getElementMethod(java.lang.String)">getElementMethod</A></B>(java.lang.String&nbsp;elementName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the adder or creator method of a named nested element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#getElementName(org.apache.tools.ant.Project, java.lang.Object)">getElementName</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
               java.lang.Object&nbsp;element)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns a description of the type of the given element in
 relation to a given project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#getElementType(java.lang.String)">getElementType</A></B>(java.lang.String&nbsp;elementName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the type of a named nested element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.List</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#getExtensionPoints()">getExtensionPoints</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns a read-only list of extension points supported
 by the introspected class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html" title="class in org.apache.tools.ant">IntrospectionHelper</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#getHelper(java.lang.Class)">getHelper</A></B>(java.lang.Class&nbsp;c)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns a helper for the given class, either from the cache
 or by creating a new instance.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html" title="class in org.apache.tools.ant">IntrospectionHelper</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#getHelper(org.apache.tools.ant.Project, java.lang.Class)">getHelper</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p,
          java.lang.Class&nbsp;c)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns a helper for the given class, either from the cache
 or by creating a new instance.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Map</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#getNestedElementMap()">getNestedElementMap</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns a read-only map of nested elements supported
 by the introspected class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Enumeration</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#getNestedElements()">getNestedElements</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns an enumeration of the names of the nested elements supported
 by the introspected class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#isContainer()">isContainer</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicates whether the introspected class is a task container,
 supporting arbitrary nested tasks/types.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#isDynamic()">isDynamic</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicates whether the introspected class is a dynamic one,
 supporting arbitrary nested elements and/or attributes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#setAttribute(org.apache.tools.ant.Project, java.lang.Object, java.lang.String, java.lang.String)">setAttribute</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p,
             java.lang.Object&nbsp;element,
             java.lang.String&nbsp;attributeName,
             java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the named attribute in the given element, which is part of the
 given project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#storeElement(org.apache.tools.ant.Project, java.lang.Object, java.lang.Object, java.lang.String)">storeElement</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
             java.lang.Object&nbsp;parent,
             java.lang.Object&nbsp;child,
             java.lang.String&nbsp;elementName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Stores a named nested element using a storage method determined
 by the initial introspection.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#supportsCharacters()">supportsCharacters</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns whether or not the introspected class supports PCDATA.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#supportsNestedElement(java.lang.String)">supportsNestedElement</A></B>(java.lang.String&nbsp;elementName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicates if this element supports a nested element of the
 given name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#supportsNestedElement(java.lang.String, java.lang.String)">supportsNestedElement</A></B>(java.lang.String&nbsp;parentUri,
                      java.lang.String&nbsp;elementName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicate if this element supports a nested element of the
 given name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#throwNotSupported(org.apache.tools.ant.Project, java.lang.Object, java.lang.String)">throwNotSupported</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                  java.lang.Object&nbsp;parent,
                  java.lang.String&nbsp;elementName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Utility method to throw a NotSupported exception</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="getHelper(java.lang.Class)"><!-- --></A><H3>
getHelper</H3>
<PRE>
public static <A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html" title="class in org.apache.tools.ant">IntrospectionHelper</A> <B>getHelper</B>(java.lang.Class&nbsp;c)</PRE>
<DL>
<DD>Returns a helper for the given class, either from the cache
 or by creating a new instance.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>c</CODE> - The class for which a helper is required.
          Must not be <code>null</code>.
<DT><B>Returns:</B><DD>a helper for the specified class</DL>
</DD>
</DL>
<HR>

<A NAME="getHelper(org.apache.tools.ant.Project, java.lang.Class)"><!-- --></A><H3>
getHelper</H3>
<PRE>
public static <A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html" title="class in org.apache.tools.ant">IntrospectionHelper</A> <B>getHelper</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p,
                                            java.lang.Class&nbsp;c)</PRE>
<DL>
<DD>Returns a helper for the given class, either from the cache
 or by creating a new instance.

 The method will make sure the helper will be cleaned up at the end of
 the project, and only one instance will be created for each class.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the project instance.<DD><CODE>c</CODE> - The class for which a helper is required.
          Must not be <code>null</code>.
<DT><B>Returns:</B><DD>a helper for the specified class</DL>
</DD>
</DL>
<HR>

<A NAME="setAttribute(org.apache.tools.ant.Project, java.lang.Object, java.lang.String, java.lang.String)"><!-- --></A><H3>
setAttribute</H3>
<PRE>
public void <B>setAttribute</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p,
                         java.lang.Object&nbsp;element,
                         java.lang.String&nbsp;attributeName,
                         java.lang.String&nbsp;value)
                  throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Sets the named attribute in the given element, which is part of the
 given project.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - The project containing the element. This is used when files
          need to be resolved. Must not be <code>null</code>.<DD><CODE>element</CODE> - The element to set the attribute in. Must not be
                <code>null</code>.<DD><CODE>attributeName</CODE> - The name of the attribute to set. Must not be
                      <code>null</code>.<DD><CODE>value</CODE> - The value to set the attribute to. This may be interpreted
              or converted to the necessary type if the setter method
              doesn't just take a string. Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the introspected class doesn't support
                           the given attribute, or if the setting
                           method fails.</DL>
</DD>
</DL>
<HR>

<A NAME="addText(org.apache.tools.ant.Project, java.lang.Object, java.lang.String)"><!-- --></A><H3>
addText</H3>
<PRE>
public void <B>addText</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                    java.lang.Object&nbsp;element,
                    java.lang.String&nbsp;text)
             throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Adds PCDATA to an element, using the element's
 <code>void addText(String)</code> method, if it has one. If no
 such method is present, a BuildException is thrown if the
 given text contains non-whitespace.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - The project which the element is part of.
                Must not be <code>null</code>.<DD><CODE>element</CODE> - The element to add the text to.
                Must not be <code>null</code>.<DD><CODE>text</CODE> - The text to add.
                Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if non-whitespace text is provided and no
                           method is available to handle it, or if
                           the handling method fails.</DL>
</DD>
</DL>
<HR>

<A NAME="throwNotSupported(org.apache.tools.ant.Project, java.lang.Object, java.lang.String)"><!-- --></A><H3>
throwNotSupported</H3>
<PRE>
public void <B>throwNotSupported</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                              java.lang.Object&nbsp;parent,
                              java.lang.String&nbsp;elementName)</PRE>
<DL>
<DD>Utility method to throw a NotSupported exception
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the Project instance.<DD><CODE>parent</CODE> - the object which doesn't support a requested element<DD><CODE>elementName</CODE> - the name of the Element which is trying to be created.</DL>
</DD>
</DL>
<HR>

<A NAME="createElement(org.apache.tools.ant.Project, java.lang.Object, java.lang.String)"><!-- --></A><H3>
createElement</H3>
<PRE>
public java.lang.Object <B>createElement</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                                      java.lang.Object&nbsp;parent,
                                      java.lang.String&nbsp;elementName)
                               throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             This is not a namespace aware method.</I>
<P>
<DD>Creates a named nested element. Depending on the results of the
 initial introspection, either a method in the given parent instance
 or a simple no-arg constructor is used to create an instance of the
 specified element type.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - Project to which the parent object belongs.
                Must not be <code>null</code>. If the resulting
                object is an instance of ProjectComponent, its
                Project reference is set to this parameter value.<DD><CODE>parent</CODE> - Parent object used to create the instance.
                Must not be <code>null</code>.<DD><CODE>elementName</CODE> - Name of the element to create an instance of.
                    Must not be <code>null</code>.
<DT><B>Returns:</B><DD>an instance of the specified element type
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if no method is available to create the
                           element instance, or if the creating method
                           fails.</DL>
</DD>
</DL>
<HR>

<A NAME="getElementCreator(org.apache.tools.ant.Project, java.lang.String, java.lang.Object, java.lang.String, org.apache.tools.ant.UnknownElement)"><!-- --></A><H3>
getElementCreator</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.Creator.html" title="class in org.apache.tools.ant">IntrospectionHelper.Creator</A> <B>getElementCreator</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                                                     java.lang.String&nbsp;parentUri,
                                                     java.lang.Object&nbsp;parent,
                                                     java.lang.String&nbsp;elementName,
                                                     <A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A>&nbsp;ue)</PRE>
<DL>
<DD>returns an object that creates and stores an object
 for an element of a parent.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - Project to which the parent object belongs.<DD><CODE>parentUri</CODE> - The namespace uri of the parent object.<DD><CODE>parent</CODE> - Parent object used to create the creator object to
                     create and store and instance of a subelement.<DD><CODE>elementName</CODE> - Name of the element to create an instance of.<DD><CODE>ue</CODE> - The unknown element associated with the element.
<DT><B>Returns:</B><DD>a creator object to create and store the element instance.</DL>
</DD>
</DL>
<HR>

<A NAME="isDynamic()"><!-- --></A><H3>
isDynamic</H3>
<PRE>
public boolean <B>isDynamic</B>()</PRE>
<DL>
<DD>Indicates whether the introspected class is a dynamic one,
 supporting arbitrary nested elements and/or attributes.
<P>
<DD><DL>

<DT><B>Returns:</B><DD><code>true<code> if the introspected class is dynamic;
         <code>false<code> otherwise.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/DynamicElement.html" title="interface in org.apache.tools.ant"><CODE>DynamicElement</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/DynamicElementNS.html" title="interface in org.apache.tools.ant"><CODE>DynamicElementNS</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="isContainer()"><!-- --></A><H3>
isContainer</H3>
<PRE>
public boolean <B>isContainer</B>()</PRE>
<DL>
<DD>Indicates whether the introspected class is a task container,
 supporting arbitrary nested tasks/types.
<P>
<DD><DL>

<DT><B>Returns:</B><DD><code>true<code> if the introspected class is a container;
         <code>false<code> otherwise.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant"><CODE>TaskContainer</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="supportsNestedElement(java.lang.String)"><!-- --></A><H3>
supportsNestedElement</H3>
<PRE>
public boolean <B>supportsNestedElement</B>(java.lang.String&nbsp;elementName)</PRE>
<DL>
<DD>Indicates if this element supports a nested element of the
 given name.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>elementName</CODE> - the name of the nested element being checked
<DT><B>Returns:</B><DD>true if the given nested element is supported</DL>
</DD>
</DL>
<HR>

<A NAME="supportsNestedElement(java.lang.String, java.lang.String)"><!-- --></A><H3>
supportsNestedElement</H3>
<PRE>
public boolean <B>supportsNestedElement</B>(java.lang.String&nbsp;parentUri,
                                     java.lang.String&nbsp;elementName)</PRE>
<DL>
<DD>Indicate if this element supports a nested element of the
 given name.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>parentUri</CODE> - the uri of the parent<DD><CODE>elementName</CODE> - the name of the nested element being checked
<DT><B>Returns:</B><DD>true if the given nested element is supported</DL>
</DD>
</DL>
<HR>

<A NAME="storeElement(org.apache.tools.ant.Project, java.lang.Object, java.lang.Object, java.lang.String)"><!-- --></A><H3>
storeElement</H3>
<PRE>
public void <B>storeElement</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                         java.lang.Object&nbsp;parent,
                         java.lang.Object&nbsp;child,
                         java.lang.String&nbsp;elementName)
                  throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Stores a named nested element using a storage method determined
 by the initial introspection. If no appropriate storage method
 is available, this method returns immediately.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - Ignored in this implementation.
                May be <code>null</code>.<DD><CODE>parent</CODE> - Parent instance to store the child in.
                Must not be <code>null</code>.<DD><CODE>child</CODE> - Child instance to store in the parent.
                Should not be <code>null</code>.<DD><CODE>elementName</CODE> - Name of the child element to store.
                     May be <code>null</code>, in which case
                     this method returns immediately.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the storage method fails.</DL>
</DD>
</DL>
<HR>

<A NAME="getElementType(java.lang.String)"><!-- --></A><H3>
getElementType</H3>
<PRE>
public java.lang.Class <B>getElementType</B>(java.lang.String&nbsp;elementName)
                               throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Returns the type of a named nested element.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>elementName</CODE> - The name of the element to find the type of.
                    Must not be <code>null</code>.
<DT><B>Returns:</B><DD>the type of the nested element with the specified name.
         This will never be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the introspected class does not
                           support the named nested element.</DL>
</DD>
</DL>
<HR>

<A NAME="getAttributeType(java.lang.String)"><!-- --></A><H3>
getAttributeType</H3>
<PRE>
public java.lang.Class <B>getAttributeType</B>(java.lang.String&nbsp;attributeName)
                                 throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Returns the type of a named attribute.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>attributeName</CODE> - The name of the attribute to find the type of.
                      Must not be <code>null</code>.
<DT><B>Returns:</B><DD>the type of the attribute with the specified name.
         This will never be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the introspected class does not
                           support the named attribute.</DL>
</DD>
</DL>
<HR>

<A NAME="getAddTextMethod()"><!-- --></A><H3>
getAddTextMethod</H3>
<PRE>
public java.lang.reflect.Method <B>getAddTextMethod</B>()
                                          throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Returns the addText method when the introspected
 class supports nested text.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the method on this introspected class that adds nested text.
         Cannot be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the introspected class does not
         support the nested text.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getElementMethod(java.lang.String)"><!-- --></A><H3>
getElementMethod</H3>
<PRE>
public java.lang.reflect.Method <B>getElementMethod</B>(java.lang.String&nbsp;elementName)
                                          throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Returns the adder or creator method of a named nested element.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>elementName</CODE> - The name of the attribute to find the setter
         method of. Must not be <code>null</code>.
<DT><B>Returns:</B><DD>the method on this introspected class that adds or creates this
         nested element. Can be <code>null</code> when the introspected
         class is a dynamic configurator!
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the introspected class does not
         support the named nested element.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getAttributeMethod(java.lang.String)"><!-- --></A><H3>
getAttributeMethod</H3>
<PRE>
public java.lang.reflect.Method <B>getAttributeMethod</B>(java.lang.String&nbsp;attributeName)
                                            throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Returns the setter method of a named attribute.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>attributeName</CODE> - The name of the attribute to find the setter
         method of. Must not be <code>null</code>.
<DT><B>Returns:</B><DD>the method on this introspected class that sets this attribute.
         This will never be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the introspected class does not
         support the named attribute.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="supportsCharacters()"><!-- --></A><H3>
supportsCharacters</H3>
<PRE>
public boolean <B>supportsCharacters</B>()</PRE>
<DL>
<DD>Returns whether or not the introspected class supports PCDATA.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>whether or not the introspected class supports PCDATA.</DL>
</DD>
</DL>
<HR>

<A NAME="getAttributes()"><!-- --></A><H3>
getAttributes</H3>
<PRE>
public java.util.Enumeration <B>getAttributes</B>()</PRE>
<DL>
<DD>Returns an enumeration of the names of the attributes supported
 by the introspected class.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>an enumeration of the names of the attributes supported
         by the introspected class.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#getAttributeMap()"><CODE>getAttributeMap()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getAttributeMap()"><!-- --></A><H3>
getAttributeMap</H3>
<PRE>
public java.util.Map <B>getAttributeMap</B>()</PRE>
<DL>
<DD>Returns a read-only map of attributes supported
 by the introspected class.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>an attribute name to attribute <code>Class</code>
         unmodifiable map. Can be empty, but never <code>null</code>.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getNestedElements()"><!-- --></A><H3>
getNestedElements</H3>
<PRE>
public java.util.Enumeration <B>getNestedElements</B>()</PRE>
<DL>
<DD>Returns an enumeration of the names of the nested elements supported
 by the introspected class.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>an enumeration of the names of the nested elements supported
         by the introspected class.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html#getNestedElementMap()"><CODE>getNestedElementMap()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getNestedElementMap()"><!-- --></A><H3>
getNestedElementMap</H3>
<PRE>
public java.util.Map <B>getNestedElementMap</B>()</PRE>
<DL>
<DD>Returns a read-only map of nested elements supported
 by the introspected class.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a nested-element name to nested-element <code>Class</code>
         unmodifiable map. Can be empty, but never <code>null</code>.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getExtensionPoints()"><!-- --></A><H3>
getExtensionPoints</H3>
<PRE>
public java.util.List <B>getExtensionPoints</B>()</PRE>
<DL>
<DD>Returns a read-only list of extension points supported
 by the introspected class.
 <p>
 A task/type or nested element with void methods named <code>add()<code>
 or <code>addConfigured()</code>, taking a single class or interface
 argument, supports extensions point. This method returns the list of
 all these <em>void add[Configured](type)</em> methods.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a list of void, single argument add() or addConfigured()
         <code>Method<code>s of all supported extension points.
         These methods are sorted such that if the argument type of a
         method derives from another type also an argument of a method
         of this list, the method with the most derived argument will
         always appear first. Can be empty, but never <code>null</code>.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getElementName(org.apache.tools.ant.Project, java.lang.Object)"><!-- --></A><H3>
getElementName</H3>
<PRE>
protected java.lang.String <B>getElementName</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                                          java.lang.Object&nbsp;element)</PRE>
<DL>
<DD>Returns a description of the type of the given element in
 relation to a given project. This is used for logging purposes
 when the element is asked to cope with some data it has no
 way of handling.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - The project the element is defined in.
                Must not be <code>null</code>.<DD><CODE>element</CODE> - The element to describe.
                Must not be <code>null</code>.
<DT><B>Returns:</B><DD>a description of the element type</DL>
</DD>
</DL>
<HR>

<A NAME="clearCache()"><!-- --></A><H3>
clearCache</H3>
<PRE>
public static void <B>clearCache</B>()</PRE>
<DL>
<DD>Clears the static cache of on build finished.
<P>
<DD><DL>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.Creator.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/IntrospectionHelper.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="IntrospectionHelper.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
