<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:35 EST 2006 -->
<TITLE>
org.apache.tools.ant (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant package">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="org.apache.tools.ant (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Package</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV PACKAGE&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/dispatch/package-summary.html"><B>NEXT PACKAGE</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/package-summary.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="package-summary.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<H2>
Package org.apache.tools.ant
</H2>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Interface Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></B></TD>
<TD>Instances of classes that implement this interface can register
 to be notified when things happened during a build.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A></B></TD>
<TD>Interface used by Ant to log the build output.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/DynamicAttribute.html" title="interface in org.apache.tools.ant">DynamicAttribute</A></B></TD>
<TD>Enables a task to control unknown attributes</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/DynamicAttributeNS.html" title="interface in org.apache.tools.ant">DynamicAttributeNS</A></B></TD>
<TD>Enables a task to control unknown attributes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/DynamicConfigurator.html" title="interface in org.apache.tools.ant">DynamicConfigurator</A></B></TD>
<TD>Enables a task to control unknown attributes and elements.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/DynamicConfiguratorNS.html" title="interface in org.apache.tools.ant">DynamicConfiguratorNS</A></B></TD>
<TD>Enables a task to control unknown attributes and elements.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/DynamicElement.html" title="interface in org.apache.tools.ant">DynamicElement</A></B></TD>
<TD>Enables a task to control unknown elements.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/DynamicElementNS.html" title="interface in org.apache.tools.ant">DynamicElementNS</A></B></TD>
<TD>Enables a task to control unknown elements.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/Executor.html" title="interface in org.apache.tools.ant">Executor</A></B></TD>
<TD>Target executor abstraction.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A></B></TD>
<TD>An interface used to describe the actions required of any type of
 directory scanner.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</A></B></TD>
<TD>Instances of classes that implement this interface can register
 to be also notified when things happened during a subbuild.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</A></B></TD>
<TD>Interface for objects which can contain tasks.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/TypeAdapter.html" title="interface in org.apache.tools.ant">TypeAdapter</A></B></TD>
<TD>Used to wrap types.</TD>
</TR>
</TABLE>
&nbsp;

<P>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</A></B></TD>
<TD>Used to load classes within ant with a different classpath from
 that used to start ant.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></B></TD>
<TD>This class contains all the information
 on a particular ant type,
 the classname, adaptor and the class
 it should be assignable from.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A></B></TD>
<TD>Class representing an event occurring during a build.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html" title="class in org.apache.tools.ant">ComponentHelper</A></B></TD>
<TD>Component creation and configuration.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</A></B></TD>
<TD>Writes build events to a PrintStream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/DemuxInputStream.html" title="class in org.apache.tools.ant">DemuxInputStream</A></B></TD>
<TD>Passes input requests to the project object for demuxing into
 individual tasks and threads.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/DemuxOutputStream.html" title="class in org.apache.tools.ant">DemuxOutputStream</A></B></TD>
<TD>Logs content written by a thread and forwards the buffers onto the
 project object which will forward the content to the appropriate
 task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/Diagnostics.html" title="class in org.apache.tools.ant">Diagnostics</A></B></TD>
<TD>A little diagnostic helper that output some information that may help
 in support.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html" title="class in org.apache.tools.ant">DirectoryScanner</A></B></TD>
<TD>Class for scanning a directory for files/directories which match certain
 criteria.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html" title="class in org.apache.tools.ant">IntrospectionHelper</A></B></TD>
<TD>Helper class that collects the methods a task or nested element
 holds to set attributes, create nested elements or hold PCDATA
 elements.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.Creator.html" title="class in org.apache.tools.ant">IntrospectionHelper.Creator</A></B></TD>
<TD>creator - allows use of create/store external
 to IntrospectionHelper.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A></B></TD>
<TD>Stores the location of a piece of text within a file (file name,
 line number and column number).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/MagicNames.html" title="class in org.apache.tools.ant">MagicNames</A></B></TD>
<TD>Magic names used within Ant.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/Main.html" title="class in org.apache.tools.ant">Main</A></B></TD>
<TD>Command line entry point into Ant.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/NoBannerLogger.html" title="class in org.apache.tools.ant">NoBannerLogger</A></B></TD>
<TD>Extends DefaultLogger to strip out empty targets.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/PathTokenizer.html" title="class in org.apache.tools.ant">PathTokenizer</A></B></TD>
<TD>A Path tokenizer takes a path and returns the components that make up
 that path.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A></B></TD>
<TD>Central representation of an Ant project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TD>
<TD>Base class for components of a project, including tasks and data types.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/ProjectHelper.html" title="class in org.apache.tools.ant">ProjectHelper</A></B></TD>
<TD>Configures a Project (complete with Targets and Tasks) based on
 a XML build file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/PropertyHelper.html" title="class in org.apache.tools.ant">PropertyHelper</A></B></TD>
<TD>NOT FINAL.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A></B></TD>
<TD>Wrapper class that holds the attributes of an element, its children, and
 any text within it.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A></B></TD>
<TD>Class to implement a target object with required parameters.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TD>
<TD>Base class for all tasks.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/TaskAdapter.html" title="class in org.apache.tools.ant">TaskAdapter</A></B></TD>
<TD>Uses introspection to "adapt" an arbitrary Bean which doesn't
 itself extend Task, but still contains an execute method and optionally
 a setProject method.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A></B></TD>
<TD>Wrapper class that holds all the information necessary to create a task
 or data type that did not exist when Ant started, or one which
 has had its definition updated to use a different implementation class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/XmlLogger.html" title="class in org.apache.tools.ant">XmlLogger</A></B></TD>
<TD>Generates a file in the current directory with
 an XML description of what happened during a build.</TD>
</TR>
</TABLE>
&nbsp;

<P>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Exception Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></B></TD>
<TD>Signals an error condition during a build</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/ExitException.html" title="class in org.apache.tools.ant">ExitException</A></B></TD>
<TD>Used to report exit status of classes which call System.exit().</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/ExitStatusException.html" title="class in org.apache.tools.ant">ExitStatusException</A></B></TD>
<TD>BuildException + exit status.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/UnsupportedAttributeException.html" title="class in org.apache.tools.ant">UnsupportedAttributeException</A></B></TD>
<TD>Used to report attempts to set an unsupported attribute</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="15%"><B><A HREF="../../../../org/apache/tools/ant/UnsupportedElementException.html" title="class in org.apache.tools.ant">UnsupportedElementException</A></B></TD>
<TD>Used to report attempts to set an unsupported element
 When the attempt to set the element is made,
 the code does not not know the name of the task/type
 based on a mapping from the classname to the task/type.</TD>
</TR>
</TABLE>
&nbsp;

<P>
<DL>
</DL>
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Package</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV PACKAGE&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/dispatch/package-summary.html"><B>NEXT PACKAGE</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/package-summary.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="package-summary.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
