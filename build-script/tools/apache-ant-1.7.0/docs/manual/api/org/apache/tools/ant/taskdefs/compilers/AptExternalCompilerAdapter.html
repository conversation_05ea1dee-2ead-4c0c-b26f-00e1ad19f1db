<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:25 EST 2006 -->
<TITLE>
AptExternalCompilerAdapter (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.compilers.AptExternalCompilerAdapter class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="AptExternalCompilerAdapter (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/AptCompilerAdapter.html" title="class in org.apache.tools.ant.taskdefs.compilers"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/CompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.compilers"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../index.html?org/apache/tools/ant/taskdefs/compilers/AptExternalCompilerAdapter.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="AptExternalCompilerAdapter.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.compilers</FONT>
<BR>
Class AptExternalCompilerAdapter</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html" title="class in org.apache.tools.ant.taskdefs.compilers">org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter</A>
      <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.compilers.AptExternalCompilerAdapter</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD><A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/CompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.compilers">CompilerAdapter</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>AptExternalCompilerAdapter</B><DT>extends <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html" title="class in org.apache.tools.ant.taskdefs.compilers">DefaultCompilerAdapter</A></DL>
</PRE>

<P>
The implementation of the apt compiler for JDK 1.5 using an external process
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.compilers.<A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html" title="class in org.apache.tools.ant.taskdefs.compilers">DefaultCompilerAdapter</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#attributes">attributes</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#bootclasspath">bootclasspath</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#compileClasspath">compileClasspath</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#compileList">compileList</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#compileSourcepath">compileSourcepath</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#debug">debug</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#depend">depend</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#deprecation">deprecation</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#destDir">destDir</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#encoding">encoding</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#extdirs">extdirs</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#includeAntRuntime">includeAntRuntime</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#includeJavaRuntime">includeJavaRuntime</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#location">location</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#lSep">lSep</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#memoryInitialSize">memoryInitialSize</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#memoryMaximumSize">memoryMaximumSize</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#optimize">optimize</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#project">project</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#src">src</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#target">target</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#verbose">verbose</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/AptExternalCompilerAdapter.html#AptExternalCompilerAdapter()">AptExternalCompilerAdapter</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/AptExternalCompilerAdapter.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Performs a compile using the Javac externally.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/Apt.html" title="class in org.apache.tools.ant.taskdefs">Apt</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/AptExternalCompilerAdapter.html#getApt()">getApt</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the facade task that fronts this adapter</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.compilers.<A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html" title="class in org.apache.tools.ant.taskdefs.compilers">DefaultCompilerAdapter</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#addCurrentCompilerArgs(org.apache.tools.ant.types.Commandline)">addCurrentCompilerArgs</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#addExtdirsToClasspath(org.apache.tools.ant.types.Path)">addExtdirsToClasspath</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#assumeJava11()">assumeJava11</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#assumeJava12()">assumeJava12</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#assumeJava13()">assumeJava13</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#assumeJava14()">assumeJava14</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#assumeJava15()">assumeJava15</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#assumeJava16()">assumeJava16</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#executeExternalCompile(java.lang.String[], int)">executeExternalCompile</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#executeExternalCompile(java.lang.String[], int, boolean)">executeExternalCompile</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#getBootClassPath()">getBootClassPath</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#getCompileClasspath()">getCompileClasspath</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#getJavac()">getJavac</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#getNoDebugArgument()">getNoDebugArgument</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#getProject()">getProject</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#logAndAddFilesToCompile(org.apache.tools.ant.types.Commandline)">logAndAddFilesToCompile</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#setJavac(org.apache.tools.ant.taskdefs.Javac)">setJavac</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#setupJavacCommand()">setupJavacCommand</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#setupJavacCommand(boolean)">setupJavacCommand</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#setupJavacCommandlineSwitches(org.apache.tools.ant.types.Commandline)">setupJavacCommandlineSwitches</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#setupJavacCommandlineSwitches(org.apache.tools.ant.types.Commandline, boolean)">setupJavacCommandlineSwitches</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#setupModernJavacCommand()">setupModernJavacCommand</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#setupModernJavacCommandlineSwitches(org.apache.tools.ant.types.Commandline)">setupModernJavacCommandlineSwitches</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="AptExternalCompilerAdapter()"><!-- --></A><H3>
AptExternalCompilerAdapter</H3>
<PRE>
public <B>AptExternalCompilerAdapter</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="getApt()"><!-- --></A><H3>
getApt</H3>
<PRE>
protected <A HREF="../../../../../../org/apache/tools/ant/taskdefs/Apt.html" title="class in org.apache.tools.ant.taskdefs">Apt</A> <B>getApt</B>()</PRE>
<DL>
<DD>Get the facade task that fronts this adapter
<P>
<DD><DL>

<DT><B>Returns:</B><DD>task instance<DT><B>See Also:</B><DD><A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#getJavac()"><CODE>DefaultCompilerAdapter.getJavac()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public boolean <B>execute</B>()
                throws <A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Performs a compile using the Javac externally.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true  the compilation was successful.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if there is a problem.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/AptCompilerAdapter.html" title="class in org.apache.tools.ant.taskdefs.compilers"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/compilers/CompilerAdapter.html" title="interface in org.apache.tools.ant.taskdefs.compilers"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../index.html?org/apache/tools/ant/taskdefs/compilers/AptExternalCompilerAdapter.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="AptExternalCompilerAdapter.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
