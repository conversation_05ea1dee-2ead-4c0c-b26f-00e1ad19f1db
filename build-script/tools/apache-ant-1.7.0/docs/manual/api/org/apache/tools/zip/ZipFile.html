<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:34 EST 2006 -->
<TITLE>
ZipFile (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.zip.ZipFile class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="ZipFile (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipLong.html" title="class in org.apache.tools.zip"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/zip/ZipFile.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ZipFile.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.zip</FONT>
<BR>
Class ZipFile</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.zip.ZipFile</B>
</PRE>
<HR>
<DL>
<DT><PRE>public class <B>ZipFile</B><DT>extends java.lang.Object</DL>
</PRE>

<P>
Replacement for <code>java.util.ZipFile</code>.

 <p>This class adds support for file name encodings other than UTF-8
 (which is required to work on ZIP files created by native zip tools
 and is able to skip a preamble like the one found in self
 extracting archives.  Furthermore it returns instances of
 <code>org.apache.tools.zip.ZipEntry</code> instead of
 <code>java.util.zip.ZipEntry</code>.</p>

 <p>It doesn't extend <code>java.util.zip.ZipFile</code> as it would
 have to reimplement all methods anyway.  Like
 <code>java.util.ZipFile</code>, it uses RandomAccessFile under the
 covers and supports compressed and uncompressed entries.</p>

 <p>The method signatures mimic the ones of
 <code>java.util.zip.ZipFile</code>, with a couple of exceptions:

 <ul>
   <li>There is no getName method.</li>
   <li>entries has been renamed to getEntries.</li>
   <li>getEntries and getEntry return
   <code>org.apache.tools.zip.ZipEntry</code> instances.</li>
   <li>close is allowed to throw IOException.</li>
 </ul>
<P>

<P>
<HR>

<P>

<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipFile.html#ZipFile(java.io.File)">ZipFile</A></B>(java.io.File&nbsp;f)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Opens the given file for reading, assuming the platform's
 native encoding for file names.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipFile.html#ZipFile(java.io.File, java.lang.String)">ZipFile</A></B>(java.io.File&nbsp;f,
        java.lang.String&nbsp;encoding)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Opens the given file for reading, assuming the specified
 encoding for file names.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipFile.html#ZipFile(java.lang.String)">ZipFile</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Opens the given file for reading, assuming the platform's
 native encoding for file names.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipFile.html#ZipFile(java.lang.String, java.lang.String)">ZipFile</A></B>(java.lang.String&nbsp;name,
        java.lang.String&nbsp;encoding)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Opens the given file for reading, assuming the specified
 encoding for file names.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipFile.html#close()">close</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Closes the archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipFile.html#closeQuietly(org.apache.tools.zip.ZipFile)">closeQuietly</A></B>(<A HREF="../../../../org/apache/tools/zip/ZipFile.html" title="class in org.apache.tools.zip">ZipFile</A>&nbsp;zipfile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;close a zipfile quietly; throw no io fault, do nothing
 on a null parameter</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.util.Date</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipFile.html#fromDosTime(org.apache.tools.zip.ZipLong)">fromDosTime</A></B>(<A HREF="../../../../org/apache/tools/zip/ZipLong.html" title="class in org.apache.tools.zip">ZipLong</A>&nbsp;zipDosTime)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convert a DOS date/time field to a Date object.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipFile.html#getEncoding()">getEncoding</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The encoding to use for filenames and the file comment.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Enumeration</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipFile.html#getEntries()">getEntries</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns all entries.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipFile.html#getEntry(java.lang.String)">getEntry</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns a named entry - or <code>null</code> if no entry by
 that name exists.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.InputStream</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipFile.html#getInputStream(org.apache.tools.zip.ZipEntry)">getInputStream</A></B>(<A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</A>&nbsp;ze)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns an InputStream for reading the contents of the given entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipFile.html#getString(byte[])">getString</A></B>(byte[]&nbsp;bytes)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Retrieve a String from the given bytes using the encoding set
 for this ZipFile.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ZipFile(java.io.File)"><!-- --></A><H3>
ZipFile</H3>
<PRE>
public <B>ZipFile</B>(java.io.File&nbsp;f)
        throws java.io.IOException</PRE>
<DL>
<DD>Opens the given file for reading, assuming the platform's
 native encoding for file names.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>f</CODE> - the archive.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if an error occurs while reading the file.</DL>
</DL>
<HR>

<A NAME="ZipFile(java.lang.String)"><!-- --></A><H3>
ZipFile</H3>
<PRE>
public <B>ZipFile</B>(java.lang.String&nbsp;name)
        throws java.io.IOException</PRE>
<DL>
<DD>Opens the given file for reading, assuming the platform's
 native encoding for file names.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - name of the archive.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if an error occurs while reading the file.</DL>
</DL>
<HR>

<A NAME="ZipFile(java.lang.String, java.lang.String)"><!-- --></A><H3>
ZipFile</H3>
<PRE>
public <B>ZipFile</B>(java.lang.String&nbsp;name,
               java.lang.String&nbsp;encoding)
        throws java.io.IOException</PRE>
<DL>
<DD>Opens the given file for reading, assuming the specified
 encoding for file names.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - name of the archive.<DD><CODE>encoding</CODE> - the encoding to use for file names
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if an error occurs while reading the file.</DL>
</DL>
<HR>

<A NAME="ZipFile(java.io.File, java.lang.String)"><!-- --></A><H3>
ZipFile</H3>
<PRE>
public <B>ZipFile</B>(java.io.File&nbsp;f,
               java.lang.String&nbsp;encoding)
        throws java.io.IOException</PRE>
<DL>
<DD>Opens the given file for reading, assuming the specified
 encoding for file names.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>f</CODE> - the archive.<DD><CODE>encoding</CODE> - the encoding to use for file names
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if an error occurs while reading the file.</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="getEncoding()"><!-- --></A><H3>
getEncoding</H3>
<PRE>
public java.lang.String <B>getEncoding</B>()</PRE>
<DL>
<DD>The encoding to use for filenames and the file comment.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>null if using the platform's default character encoding.</DL>
</DD>
</DL>
<HR>

<A NAME="close()"><!-- --></A><H3>
close</H3>
<PRE>
public void <B>close</B>()
           throws java.io.IOException</PRE>
<DL>
<DD>Closes the archive.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if an error occurs closing the archive.</DL>
</DD>
</DL>
<HR>

<A NAME="closeQuietly(org.apache.tools.zip.ZipFile)"><!-- --></A><H3>
closeQuietly</H3>
<PRE>
public static void <B>closeQuietly</B>(<A HREF="../../../../org/apache/tools/zip/ZipFile.html" title="class in org.apache.tools.zip">ZipFile</A>&nbsp;zipfile)</PRE>
<DL>
<DD>close a zipfile quietly; throw no io fault, do nothing
 on a null parameter
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>zipfile</CODE> - file to close, can be null</DL>
</DD>
</DL>
<HR>

<A NAME="getEntries()"><!-- --></A><H3>
getEntries</H3>
<PRE>
public java.util.Enumeration <B>getEntries</B>()</PRE>
<DL>
<DD>Returns all entries.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>all entries as <A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip"><CODE>ZipEntry</CODE></A> instances</DL>
</DD>
</DL>
<HR>

<A NAME="getEntry(java.lang.String)"><!-- --></A><H3>
getEntry</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</A> <B>getEntry</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Returns a named entry - or <code>null</code> if no entry by
 that name exists.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - name of the entry.
<DT><B>Returns:</B><DD>the ZipEntry corresponding to the given name - or
 <code>null</code> if not present.</DL>
</DD>
</DL>
<HR>

<A NAME="getInputStream(org.apache.tools.zip.ZipEntry)"><!-- --></A><H3>
getInputStream</H3>
<PRE>
public java.io.InputStream <B>getInputStream</B>(<A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</A>&nbsp;ze)
                                   throws java.io.IOException,
                                          java.util.zip.ZipException</PRE>
<DL>
<DD>Returns an InputStream for reading the contents of the given entry.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ze</CODE> - the entry to get the stream for.
<DT><B>Returns:</B><DD>a stream to read the entry from.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if unable to create an input stream from the zipenty
<DD><CODE>java.util.zip.ZipException</CODE> - if the zipentry has an unsupported compression method</DL>
</DD>
</DL>
<HR>

<A NAME="fromDosTime(org.apache.tools.zip.ZipLong)"><!-- --></A><H3>
fromDosTime</H3>
<PRE>
protected static java.util.Date <B>fromDosTime</B>(<A HREF="../../../../org/apache/tools/zip/ZipLong.html" title="class in org.apache.tools.zip">ZipLong</A>&nbsp;zipDosTime)</PRE>
<DL>
<DD>Convert a DOS date/time field to a Date object.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>zipDosTime</CODE> - contains the stored DOS time.
<DT><B>Returns:</B><DD>a Date instance corresponding to the given time.</DL>
</DD>
</DL>
<HR>

<A NAME="getString(byte[])"><!-- --></A><H3>
getString</H3>
<PRE>
protected java.lang.String <B>getString</B>(byte[]&nbsp;bytes)
                              throws java.util.zip.ZipException</PRE>
<DL>
<DD>Retrieve a String from the given bytes using the encoding set
 for this ZipFile.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>bytes</CODE> - the byte array to transform
<DT><B>Returns:</B><DD>String obtained by using the given encoding
<DT><B>Throws:</B>
<DD><CODE>java.util.zip.ZipException</CODE> - if the encoding cannot be recognized.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipLong.html" title="class in org.apache.tools.zip"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/zip/ZipFile.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ZipFile.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
