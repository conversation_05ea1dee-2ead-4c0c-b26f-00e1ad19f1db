<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:34 EST 2006 -->
<TITLE>
ZipExtraField (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.zip.ZipExtraField interface">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="ZipExtraField (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipFile.html" title="class in org.apache.tools.zip"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/zip/ZipExtraField.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ZipExtraField.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.zip</FONT>
<BR>
Interface ZipExtraField</H2>
<DL>
<DT><B>All Known Implementing Classes:</B> <DD><A HREF="../../../../org/apache/tools/zip/AsiExtraField.html" title="class in org.apache.tools.zip">AsiExtraField</A>, <A HREF="../../../../org/apache/tools/zip/JarMarker.html" title="class in org.apache.tools.zip">JarMarker</A>, <A HREF="../../../../org/apache/tools/zip/UnrecognizedExtraField.html" title="class in org.apache.tools.zip">UnrecognizedExtraField</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public interface <B>ZipExtraField</B></DL>
</PRE>

<P>
General format of extra field data.

 <p>Extra fields usually appear twice per file, once in the local
 file data and once in the central directory.  Usually they are the
 same, but they don't have to be.  <CODE>java.util.zip.ZipOutputStream</CODE> will
 only use the local file data in both places.</p>
<P>

<P>
<HR>

<P>

<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipExtraField.html#getCentralDirectoryData()">getCentralDirectoryData</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The actual data to put central directory - without Header-ID or
 length specifier.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipShort.html" title="class in org.apache.tools.zip">ZipShort</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipExtraField.html#getCentralDirectoryLength()">getCentralDirectoryLength</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Length of the extra field in the central directory - without
 Header-ID or length specifier.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipShort.html" title="class in org.apache.tools.zip">ZipShort</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipExtraField.html#getHeaderId()">getHeaderId</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The Header-ID.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipExtraField.html#getLocalFileDataData()">getLocalFileDataData</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The actual data to put into local file data - without Header-ID
 or length specifier.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipShort.html" title="class in org.apache.tools.zip">ZipShort</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipExtraField.html#getLocalFileDataLength()">getLocalFileDataLength</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Length of the extra field in the local file data - without
 Header-ID or length specifier.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipExtraField.html#parseFromLocalFileData(byte[], int, int)">parseFromLocalFileData</A></B>(byte[]&nbsp;data,
                       int&nbsp;offset,
                       int&nbsp;length)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Populate data from this array as if it was in local file data.</TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="getHeaderId()"><!-- --></A><H3>
getHeaderId</H3>
<PRE>
<A HREF="../../../../org/apache/tools/zip/ZipShort.html" title="class in org.apache.tools.zip">ZipShort</A> <B>getHeaderId</B>()</PRE>
<DL>
<DD>The Header-ID.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the header id<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getLocalFileDataLength()"><!-- --></A><H3>
getLocalFileDataLength</H3>
<PRE>
<A HREF="../../../../org/apache/tools/zip/ZipShort.html" title="class in org.apache.tools.zip">ZipShort</A> <B>getLocalFileDataLength</B>()</PRE>
<DL>
<DD>Length of the extra field in the local file data - without
 Header-ID or length specifier.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the length of the field in the local file data<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getCentralDirectoryLength()"><!-- --></A><H3>
getCentralDirectoryLength</H3>
<PRE>
<A HREF="../../../../org/apache/tools/zip/ZipShort.html" title="class in org.apache.tools.zip">ZipShort</A> <B>getCentralDirectoryLength</B>()</PRE>
<DL>
<DD>Length of the extra field in the central directory - without
 Header-ID or length specifier.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the length of the field in the central directory<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getLocalFileDataData()"><!-- --></A><H3>
getLocalFileDataData</H3>
<PRE>
byte[] <B>getLocalFileDataData</B>()</PRE>
<DL>
<DD>The actual data to put into local file data - without Header-ID
 or length specifier.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the data<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getCentralDirectoryData()"><!-- --></A><H3>
getCentralDirectoryData</H3>
<PRE>
byte[] <B>getCentralDirectoryData</B>()</PRE>
<DL>
<DD>The actual data to put central directory - without Header-ID or
 length specifier.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the data<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="parseFromLocalFileData(byte[], int, int)"><!-- --></A><H3>
parseFromLocalFileData</H3>
<PRE>
void <B>parseFromLocalFileData</B>(byte[]&nbsp;data,
                            int&nbsp;offset,
                            int&nbsp;length)
                            throws java.util.zip.ZipException</PRE>
<DL>
<DD>Populate data from this array as if it was in local file data.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>data</CODE> - an array of bytes<DD><CODE>offset</CODE> - the start offset<DD><CODE>length</CODE> - the number of bytes in the array from offset
<DT><B>Throws:</B>
<DD><CODE>java.util.zip.ZipException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipFile.html" title="class in org.apache.tools.zip"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/zip/ZipExtraField.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ZipExtraField.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
