<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:21 EST 2006 -->
<TITLE>
ProjectHelper2.TargetHandler (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.helper.ProjectHelper2.TargetHandler class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="ProjectHelper2.TargetHandler (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.RootHandler.html" title="class in org.apache.tools.ant.helper"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelperImpl.html" title="class in org.apache.tools.ant.helper"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/helper/ProjectHelper2.TargetHandler.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ProjectHelper2.TargetHandler.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.helper</FONT>
<BR>
Class ProjectHelper2.TargetHandler</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">org.apache.tools.ant.helper.ProjectHelper2.AntHandler</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.helper.ProjectHelper2.TargetHandler</B>
</PRE>
<DL>
<DT><B>Enclosing class:</B><DD><A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.html" title="class in org.apache.tools.ant.helper">ProjectHelper2</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public static class <B>ProjectHelper2.TargetHandler</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</A></DL>
</PRE>

<P>
Handler for "target" elements.
<P>

<P>
<HR>

<P>

<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.TargetHandler.html#ProjectHelper2.TargetHandler()">ProjectHelper2.TargetHandler</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.TargetHandler.html#onEndElement(java.lang.String, java.lang.String, org.apache.tools.ant.helper.AntXMLContext)">onEndElement</A></B>(java.lang.String&nbsp;uri,
             java.lang.String&nbsp;tag,
             <A HREF="../../../../../org/apache/tools/ant/helper/AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</A>&nbsp;context)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Handle the end of the project, sets the current target of the
 context to be the implicit target.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.TargetHandler.html#onStartChild(java.lang.String, java.lang.String, java.lang.String, org.xml.sax.Attributes, org.apache.tools.ant.helper.AntXMLContext)">onStartChild</A></B>(java.lang.String&nbsp;uri,
             java.lang.String&nbsp;name,
             java.lang.String&nbsp;qname,
             org.xml.sax.Attributes&nbsp;attrs,
             <A HREF="../../../../../org/apache/tools/ant/helper/AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</A>&nbsp;context)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Handles the start of an element within a target.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.TargetHandler.html#onStartElement(java.lang.String, java.lang.String, java.lang.String, org.xml.sax.Attributes, org.apache.tools.ant.helper.AntXMLContext)">onStartElement</A></B>(java.lang.String&nbsp;uri,
               java.lang.String&nbsp;tag,
               java.lang.String&nbsp;qname,
               org.xml.sax.Attributes&nbsp;attrs,
               <A HREF="../../../../../org/apache/tools/ant/helper/AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</A>&nbsp;context)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Initialisation routine called after handler creation
 with the element name and attributes.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.helper.ProjectHelper2.AntHandler"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.helper.<A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.AntHandler.html#characters(char[], int, int, org.apache.tools.ant.helper.AntXMLContext)">characters</A>, <A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.AntHandler.html#checkNamespace(java.lang.String)">checkNamespace</A>, <A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.AntHandler.html#onEndChild(java.lang.String, java.lang.String, java.lang.String, org.apache.tools.ant.helper.AntXMLContext)">onEndChild</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ProjectHelper2.TargetHandler()"><!-- --></A><H3>
ProjectHelper2.TargetHandler</H3>
<PRE>
public <B>ProjectHelper2.TargetHandler</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="onStartElement(java.lang.String, java.lang.String, java.lang.String, org.xml.sax.Attributes, org.apache.tools.ant.helper.AntXMLContext)"><!-- --></A><H3>
onStartElement</H3>
<PRE>
public void <B>onStartElement</B>(java.lang.String&nbsp;uri,
                           java.lang.String&nbsp;tag,
                           java.lang.String&nbsp;qname,
                           org.xml.sax.Attributes&nbsp;attrs,
                           <A HREF="../../../../../org/apache/tools/ant/helper/AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</A>&nbsp;context)
                    throws org.xml.sax.SAXParseException</PRE>
<DL>
<DD>Initialisation routine called after handler creation
 with the element name and attributes. The attributes which
 this handler can deal with are: <code>"name"</code>,
 <code>"depends"</code>, <code>"if"</code>,
 <code>"unless"</code>, <code>"id"</code> and
 <code>"description"</code>.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.AntHandler.html#onStartElement(java.lang.String, java.lang.String, java.lang.String, org.xml.sax.Attributes, org.apache.tools.ant.helper.AntXMLContext)">onStartElement</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>uri</CODE> - The namespace URI for this element.<DD><CODE>tag</CODE> - Name of the element which caused this handler
            to be created. Should not be <code>null</code>.
            Ignored in this implementation.<DD><CODE>qname</CODE> - The qualified name for this element.<DD><CODE>attrs</CODE> - Attributes of the element which caused this
              handler to be created. Must not be <code>null</code>.<DD><CODE>context</CODE> - The current context.
<DT><B>Throws:</B>
<DD><CODE>org.xml.sax.SAXParseException</CODE> - if an unexpected attribute is encountered
            or if the <code>"name"</code> attribute is missing.</DL>
</DD>
</DL>
<HR>

<A NAME="onStartChild(java.lang.String, java.lang.String, java.lang.String, org.xml.sax.Attributes, org.apache.tools.ant.helper.AntXMLContext)"><!-- --></A><H3>
onStartChild</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</A> <B>onStartChild</B>(java.lang.String&nbsp;uri,
                                              java.lang.String&nbsp;name,
                                              java.lang.String&nbsp;qname,
                                              org.xml.sax.Attributes&nbsp;attrs,
                                              <A HREF="../../../../../org/apache/tools/ant/helper/AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</A>&nbsp;context)
                                       throws org.xml.sax.SAXParseException</PRE>
<DL>
<DD>Handles the start of an element within a target.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.AntHandler.html#onStartChild(java.lang.String, java.lang.String, java.lang.String, org.xml.sax.Attributes, org.apache.tools.ant.helper.AntXMLContext)">onStartChild</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>uri</CODE> - The namespace URI for this element.<DD><CODE>name</CODE> - The name of the element being started.
            Will not be <code>null</code>.<DD><CODE>qname</CODE> - The qualified name for this element.<DD><CODE>attrs</CODE> - Attributes of the element being started.
              Will not be <code>null</code>.<DD><CODE>context</CODE> - The current context.
<DT><B>Returns:</B><DD>an element handler.
<DT><B>Throws:</B>
<DD><CODE>org.xml.sax.SAXParseException</CODE> - if an error occurs when initialising
                              the appropriate child handler</DL>
</DD>
</DL>
<HR>

<A NAME="onEndElement(java.lang.String, java.lang.String, org.apache.tools.ant.helper.AntXMLContext)"><!-- --></A><H3>
onEndElement</H3>
<PRE>
public void <B>onEndElement</B>(java.lang.String&nbsp;uri,
                         java.lang.String&nbsp;tag,
                         <A HREF="../../../../../org/apache/tools/ant/helper/AntXMLContext.html" title="class in org.apache.tools.ant.helper">AntXMLContext</A>&nbsp;context)</PRE>
<DL>
<DD>Handle the end of the project, sets the current target of the
 context to be the implicit target.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.AntHandler.html#onEndElement(java.lang.String, java.lang.String, org.apache.tools.ant.helper.AntXMLContext)">onEndElement</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.AntHandler.html" title="class in org.apache.tools.ant.helper">ProjectHelper2.AntHandler</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>uri</CODE> - The namespace URI of the element.<DD><CODE>tag</CODE> - The name of the element.<DD><CODE>context</CODE> - The current context.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelper2.RootHandler.html" title="class in org.apache.tools.ant.helper"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/helper/ProjectHelperImpl.html" title="class in org.apache.tools.ant.helper"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/helper/ProjectHelper2.TargetHandler.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ProjectHelper2.TargetHandler.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
