<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:22 EST 2006 -->
<TITLE>
ExecuteJava (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.ExecuteJava class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="ExecuteJava (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/ExecuteJava.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ExecuteJava.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class ExecuteJava</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.ExecuteJava</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Runnable, <A HREF="../../../../../org/apache/tools/ant/util/TimeoutObserver.html" title="interface in org.apache.tools.ant.util">TimeoutObserver</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>ExecuteJava</B><DT>extends java.lang.Object<DT>implements java.lang.Runnable, <A HREF="../../../../../org/apache/tools/ant/util/TimeoutObserver.html" title="interface in org.apache.tools.ant.util">TimeoutObserver</A></DL>
</PRE>

<P>
Execute a Java class.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
</DL>
<HR>

<P>

<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html#ExecuteJava()">ExecuteJava</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html#execute(org.apache.tools.ant.Project)">execute</A></B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Execute the Java class against the specified Ant Project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html#fork(org.apache.tools.ant.ProjectComponent)">fork</A></B>(<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A>&nbsp;pc)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Run the Java command in a separate VM, this does not give you
 the full flexibility of the Java task, but may be enough for
 simple needs.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html#killedProcess()">killedProcess</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get whether the process was killed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html#run()">run</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Run this ExecuteJava in a Thread.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the classpath to be used when running the Java class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html#setJavaCommand(org.apache.tools.ant.types.Commandline)">setJavaCommand</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;javaCommand)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the Java "command" for this ExecuteJava.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html#setOutput(java.io.PrintStream)">setOutput</A></B>(java.io.PrintStream&nbsp;out)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.4.x.
             manage output at the task level.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html#setPermissions(org.apache.tools.ant.types.Permissions)">setPermissions</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Permissions.html" title="class in org.apache.tools.ant.types">Permissions</A>&nbsp;permissions)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the permissions for the application run.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html#setSystemProperties(org.apache.tools.ant.types.CommandlineJava.SysProperties)">setSystemProperties</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/CommandlineJava.SysProperties.html" title="class in org.apache.tools.ant.types">CommandlineJava.SysProperties</A>&nbsp;s)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the system properties to use when running the Java class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html#setTimeout(java.lang.Long)">setTimeout</A></B>(java.lang.Long&nbsp;timeout)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the timeout for this ExecuteJava.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html#setupCommandLineForVMS(org.apache.tools.ant.taskdefs.Execute, java.lang.String[])">setupCommandLineForVMS</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</A>&nbsp;exe,
                       java.lang.String[]&nbsp;command)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;On VMS platform, we need to create a special java options file
 containing the arguments and classpath for the java command.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteJava.html#timeoutOccured(org.apache.tools.ant.util.Watchdog)">timeoutOccured</A></B>(<A HREF="../../../../../org/apache/tools/ant/util/Watchdog.html" title="class in org.apache.tools.ant.util">Watchdog</A>&nbsp;w)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Mark timeout as having occurred.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ExecuteJava()"><!-- --></A><H3>
ExecuteJava</H3>
<PRE>
public <B>ExecuteJava</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setJavaCommand(org.apache.tools.ant.types.Commandline)"><!-- --></A><H3>
setJavaCommand</H3>
<PRE>
public void <B>setJavaCommand</B>(<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;javaCommand)</PRE>
<DL>
<DD>Set the Java "command" for this ExecuteJava.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>javaCommand</CODE> - the classname and arguments in a Commandline.</DL>
</DD>
</DL>
<HR>

<A NAME="setClasspath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setClasspath</H3>
<PRE>
public void <B>setClasspath</B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;p)</PRE>
<DL>
<DD>Set the classpath to be used when running the Java class.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - an Ant Path object containing the classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="setSystemProperties(org.apache.tools.ant.types.CommandlineJava.SysProperties)"><!-- --></A><H3>
setSystemProperties</H3>
<PRE>
public void <B>setSystemProperties</B>(<A HREF="../../../../../org/apache/tools/ant/types/CommandlineJava.SysProperties.html" title="class in org.apache.tools.ant.types">CommandlineJava.SysProperties</A>&nbsp;s)</PRE>
<DL>
<DD>Set the system properties to use when running the Java class.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>s</CODE> - CommandlineJava system properties.</DL>
</DD>
</DL>
<HR>

<A NAME="setPermissions(org.apache.tools.ant.types.Permissions)"><!-- --></A><H3>
setPermissions</H3>
<PRE>
public void <B>setPermissions</B>(<A HREF="../../../../../org/apache/tools/ant/types/Permissions.html" title="class in org.apache.tools.ant.types">Permissions</A>&nbsp;permissions)</PRE>
<DL>
<DD>Set the permissions for the application run.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>permissions</CODE> - the Permissions to use.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setOutput(java.io.PrintStream)"><!-- --></A><H3>
setOutput</H3>
<PRE>
public void <B>setOutput</B>(java.io.PrintStream&nbsp;out)</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.4.x.
             manage output at the task level.</I>
<P>
<DD>Set the stream to which all output (System.out as well as System.err)
 will be written.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>out</CODE> - the PrintStream where output should be sent.</DL>
</DD>
</DL>
<HR>

<A NAME="setTimeout(java.lang.Long)"><!-- --></A><H3>
setTimeout</H3>
<PRE>
public void <B>setTimeout</B>(java.lang.Long&nbsp;timeout)</PRE>
<DL>
<DD>Set the timeout for this ExecuteJava.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>timeout</CODE> - timeout as Long.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="execute(org.apache.tools.ant.Project)"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>(<A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)
             throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Execute the Java class against the specified Ant Project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the Project to use.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error.</DL>
</DD>
</DL>
<HR>

<A NAME="run()"><!-- --></A><H3>
run</H3>
<PRE>
public void <B>run</B>()</PRE>
<DL>
<DD>Run this ExecuteJava in a Thread.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE>run</CODE> in interface <CODE>java.lang.Runnable</CODE></DL>
</DD>
<DD><DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="timeoutOccured(org.apache.tools.ant.util.Watchdog)"><!-- --></A><H3>
timeoutOccured</H3>
<PRE>
public void <B>timeoutOccured</B>(<A HREF="../../../../../org/apache/tools/ant/util/Watchdog.html" title="class in org.apache.tools.ant.util">Watchdog</A>&nbsp;w)</PRE>
<DL>
<DD>Mark timeout as having occurred.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/util/TimeoutObserver.html#timeoutOccured(org.apache.tools.ant.util.Watchdog)">timeoutOccured</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/util/TimeoutObserver.html" title="interface in org.apache.tools.ant.util">TimeoutObserver</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>w</CODE> - the responsible Watchdog.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="killedProcess()"><!-- --></A><H3>
killedProcess</H3>
<PRE>
public boolean <B>killedProcess</B>()</PRE>
<DL>
<DD>Get whether the process was killed.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD><code>true</code> if the process was killed, false otherwise.<DT><B>Since:</B></DT>
  <DD>1.19, Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="fork(org.apache.tools.ant.ProjectComponent)"><!-- --></A><H3>
fork</H3>
<PRE>
public int <B>fork</B>(<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A>&nbsp;pc)
         throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Run the Java command in a separate VM, this does not give you
 the full flexibility of the Java task, but may be enough for
 simple needs.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pc</CODE> - the ProjectComponent to use for logging, etc.
<DT><B>Returns:</B><DD>the exit status of the subprocess.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setupCommandLineForVMS(org.apache.tools.ant.taskdefs.Execute, java.lang.String[])"><!-- --></A><H3>
setupCommandLineForVMS</H3>
<PRE>
public static void <B>setupCommandLineForVMS</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</A>&nbsp;exe,
                                          java.lang.String[]&nbsp;command)</PRE>
<DL>
<DD>On VMS platform, we need to create a special java options file
 containing the arguments and classpath for the java command.
 The special file is supported by the "-V" switch on the VMS JVM.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>exe</CODE> - the Execute instance to alter.<DD><CODE>command</CODE> - the command-line.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/ExecuteJava.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ExecuteJava.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
