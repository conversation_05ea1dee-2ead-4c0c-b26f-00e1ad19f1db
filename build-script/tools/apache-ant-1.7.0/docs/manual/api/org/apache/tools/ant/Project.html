<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:20 EST 2006 -->
<TITLE>
Project (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.Project class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Project (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/PathTokenizer.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/Project.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Project.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class Project</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.Project</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD><A HREF="../../../../org/apache/tools/ant/types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>Project</B><DT>extends java.lang.Object<DT>implements <A HREF="../../../../org/apache/tools/ant/types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</A></DL>
</PRE>

<P>
Central representation of an Ant project. This class defines an
 Ant project with all of its targets, tasks and various other
 properties. It also provides the mechanism to kick off a build using
 a particular target name.
 <p>
 This class also encapsulates methods which allow files to be referred
 to using abstract path names which are translated to native system
 file paths at runtime.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#JAVA_1_0">JAVA_1_0</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use <A HREF="../../../../org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_0"><CODE>JavaEnvUtils.JAVA_1_0</CODE></A> instead.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#JAVA_1_1">JAVA_1_1</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use <A HREF="../../../../org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_1"><CODE>JavaEnvUtils.JAVA_1_1</CODE></A> instead.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#JAVA_1_2">JAVA_1_2</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use <A HREF="../../../../org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_2"><CODE>JavaEnvUtils.JAVA_1_2</CODE></A> instead.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#JAVA_1_3">JAVA_1_3</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use <A HREF="../../../../org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_3"><CODE>JavaEnvUtils.JAVA_1_3</CODE></A> instead.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#JAVA_1_4">JAVA_1_4</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use <A HREF="../../../../org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_4"><CODE>JavaEnvUtils.JAVA_1_4</CODE></A> instead.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#MSG_DEBUG">MSG_DEBUG</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message priority of &quot;debug&quot;.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#MSG_ERR">MSG_ERR</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message priority of &quot;error&quot;.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#MSG_INFO">MSG_INFO</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message priority of &quot;information&quot;.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#MSG_VERBOSE">MSG_VERBOSE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message priority of &quot;verbose&quot;.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#MSG_WARN">MSG_WARN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message priority of &quot;warning&quot;.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#TOKEN_END">TOKEN_END</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Default filter end token.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#TOKEN_START">TOKEN_START</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Default filter start token.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#Project()">Project</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a new Ant project.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#addBuildListener(org.apache.tools.ant.BuildListener)">addBuildListener</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A>&nbsp;listener)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a build listener to the list.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#addDataTypeDefinition(java.lang.String, java.lang.Class)">addDataTypeDefinition</A></B>(java.lang.String&nbsp;typeName,
                      java.lang.Class&nbsp;typeClass)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a new datatype definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#addFilter(java.lang.String, java.lang.String)">addFilter</A></B>(java.lang.String&nbsp;token,
          java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.4.x.
             Use getGlobalFilterSet().addFilter(token,value)</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#addIdReference(java.lang.String, java.lang.Object)">addIdReference</A></B>(java.lang.String&nbsp;id,
               java.lang.Object&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add an id reference.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#addOrReplaceTarget(java.lang.String, org.apache.tools.ant.Target)">addOrReplaceTarget</A></B>(java.lang.String&nbsp;targetName,
                   <A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a target to the project, or replaces one with the same
 name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#addOrReplaceTarget(org.apache.tools.ant.Target)">addOrReplaceTarget</A></B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a target to the project, or replaces one with the same
 name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#addReference(java.lang.String, java.lang.Object)">addReference</A></B>(java.lang.String&nbsp;referenceName,
             java.lang.Object&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a reference to the project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#addTarget(java.lang.String, org.apache.tools.ant.Target)">addTarget</A></B>(java.lang.String&nbsp;targetName,
          <A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a <em>new</em> target to the project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#addTarget(org.apache.tools.ant.Target)">addTarget</A></B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a <em>new</em> target to the project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#addTaskDefinition(java.lang.String, java.lang.Class)">addTaskDefinition</A></B>(java.lang.String&nbsp;taskName,
                  java.lang.Class&nbsp;taskClass)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a new task definition to the project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#checkTaskClass(java.lang.Class)">checkTaskClass</A></B>(java.lang.Class&nbsp;taskClass)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Check whether or not a class is suitable for serving as Ant task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#copyFile(java.io.File, java.io.File)">copyFile</A></B>(java.io.File&nbsp;sourceFile,
         java.io.File&nbsp;destFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.4.x</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#copyFile(java.io.File, java.io.File, boolean)">copyFile</A></B>(java.io.File&nbsp;sourceFile,
         java.io.File&nbsp;destFile,
         boolean&nbsp;filtering)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.4.x</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#copyFile(java.io.File, java.io.File, boolean, boolean)">copyFile</A></B>(java.io.File&nbsp;sourceFile,
         java.io.File&nbsp;destFile,
         boolean&nbsp;filtering,
         boolean&nbsp;overwrite)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.4.x</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#copyFile(java.io.File, java.io.File, boolean, boolean, boolean)">copyFile</A></B>(java.io.File&nbsp;sourceFile,
         java.io.File&nbsp;destFile,
         boolean&nbsp;filtering,
         boolean&nbsp;overwrite,
         boolean&nbsp;preserveLastModified)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.4.x</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#copyFile(java.lang.String, java.lang.String)">copyFile</A></B>(java.lang.String&nbsp;sourceFile,
         java.lang.String&nbsp;destFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.4.x</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#copyFile(java.lang.String, java.lang.String, boolean)">copyFile</A></B>(java.lang.String&nbsp;sourceFile,
         java.lang.String&nbsp;destFile,
         boolean&nbsp;filtering)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.4.x</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#copyFile(java.lang.String, java.lang.String, boolean, boolean)">copyFile</A></B>(java.lang.String&nbsp;sourceFile,
         java.lang.String&nbsp;destFile,
         boolean&nbsp;filtering,
         boolean&nbsp;overwrite)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.4.x</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#copyFile(java.lang.String, java.lang.String, boolean, boolean, boolean)">copyFile</A></B>(java.lang.String&nbsp;sourceFile,
         java.lang.String&nbsp;destFile,
         boolean&nbsp;filtering,
         boolean&nbsp;overwrite,
         boolean&nbsp;preserveLastModified)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.4.x</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#copyInheritedProperties(org.apache.tools.ant.Project)">copyInheritedProperties</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;other)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Copy all Employee properties that have not been set on the
 command line or a GUI tool from this instance to the Project
 instance given as the argument.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#copyUserProperties(org.apache.tools.ant.Project)">copyUserProperties</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;other)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Copy all Employee properties that have been set on the command
 line or a GUI tool from this instance to the Project instance
 given as the argument.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#createClassLoader(java.lang.ClassLoader, org.apache.tools.ant.types.Path)">createClassLoader</A></B>(java.lang.ClassLoader&nbsp;parent,
                  <A HREF="../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;path)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Factory method to create a class loader for loading classes from
 a given path.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#createClassLoader(org.apache.tools.ant.types.Path)">createClassLoader</A></B>(<A HREF="../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;path)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Factory method to create a class loader for loading classes from
 a given path.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#createDataType(java.lang.String)">createDataType</A></B>(java.lang.String&nbsp;typeName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a new instance of a data type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#createSubProject()">createSubProject</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create and initialize a subproject.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#createTask(java.lang.String)">createTask</A></B>(java.lang.String&nbsp;taskType)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a new instance of a task, adding it to a list of
 created tasks for later invalidation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#defaultInput(byte[], int, int)">defaultInput</A></B>(byte[]&nbsp;buffer,
             int&nbsp;offset,
             int&nbsp;length)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Read data from the default input stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#demuxFlush(java.lang.String, boolean)">demuxFlush</A></B>(java.lang.String&nbsp;output,
           boolean&nbsp;isError)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Demultiplex flush operations so that each task receives the appropriate
 messages.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#demuxInput(byte[], int, int)">demuxInput</A></B>(byte[]&nbsp;buffer,
           int&nbsp;offset,
           int&nbsp;length)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Demux an input request to the correct task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#demuxOutput(java.lang.String, boolean)">demuxOutput</A></B>(java.lang.String&nbsp;output,
            boolean&nbsp;isWarning)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Demultiplex output so that each task receives the appropriate
 messages.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#executeSortedTargets(java.util.Vector)">executeSortedTargets</A></B>(java.util.Vector&nbsp;sortedTargets)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Execute a <code>Vector</code> of sorted targets.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#executeTarget(java.lang.String)">executeTarget</A></B>(java.lang.String&nbsp;targetName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Execute the specified target and any targets it depends on.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#executeTargets(java.util.Vector)">executeTargets</A></B>(java.util.Vector&nbsp;names)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Execute the specified sequence of targets, and the targets
 they depend on.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#fireBuildFinished(java.lang.Throwable)">fireBuildFinished</A></B>(java.lang.Throwable&nbsp;exception)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Send a &quot;build finished&quot; event to the build listeners
 for this project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#fireBuildStarted()">fireBuildStarted</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Send a &quot;build started&quot; event
 to the build listeners for this project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#fireMessageLogged(org.apache.tools.ant.Project, java.lang.String, int)">fireMessageLogged</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                  java.lang.String&nbsp;message,
                  int&nbsp;priority)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Send a &quot;message logged&quot; project level event
 to the build listeners for this project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#fireMessageLogged(org.apache.tools.ant.Project, java.lang.String, java.lang.Throwable, int)">fireMessageLogged</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                  java.lang.String&nbsp;message,
                  java.lang.Throwable&nbsp;throwable,
                  int&nbsp;priority)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Send a &quot;message logged&quot; project level event
 to the build listeners for this project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#fireMessageLogged(org.apache.tools.ant.Target, java.lang.String, int)">fireMessageLogged</A></B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target,
                  java.lang.String&nbsp;message,
                  int&nbsp;priority)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Send a &quot;message logged&quot; target level event
 to the build listeners for this project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#fireMessageLogged(org.apache.tools.ant.Target, java.lang.String, java.lang.Throwable, int)">fireMessageLogged</A></B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target,
                  java.lang.String&nbsp;message,
                  java.lang.Throwable&nbsp;throwable,
                  int&nbsp;priority)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Send a &quot;message logged&quot; target level event
 to the build listeners for this project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#fireMessageLogged(org.apache.tools.ant.Task, java.lang.String, int)">fireMessageLogged</A></B>(<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task,
                  java.lang.String&nbsp;message,
                  int&nbsp;priority)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Send a &quot;message logged&quot; task level event
 to the build listeners for this project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#fireMessageLogged(org.apache.tools.ant.Task, java.lang.String, java.lang.Throwable, int)">fireMessageLogged</A></B>(<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task,
                  java.lang.String&nbsp;message,
                  java.lang.Throwable&nbsp;throwable,
                  int&nbsp;priority)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Send a &quot;message logged&quot; task level event
 to the build listeners for this project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#fireSubBuildFinished(java.lang.Throwable)">fireSubBuildFinished</A></B>(java.lang.Throwable&nbsp;exception)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Send a &quot;subbuild finished&quot; event to the build listeners for
 this project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#fireSubBuildStarted()">fireSubBuildStarted</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Send a &quot;subbuild started&quot; event to the build listeners for
 this project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#fireTargetFinished(org.apache.tools.ant.Target, java.lang.Throwable)">fireTargetFinished</A></B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target,
                   java.lang.Throwable&nbsp;exception)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Send a &quot;target finished&quot; event to the build listeners
 for this project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#fireTargetStarted(org.apache.tools.ant.Target)">fireTargetStarted</A></B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Send a &quot;target started&quot; event to the build listeners
 for this project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#fireTaskFinished(org.apache.tools.ant.Task, java.lang.Throwable)">fireTaskFinished</A></B>(<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task,
                 java.lang.Throwable&nbsp;exception)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Send a &quot;task finished&quot; event to the build listeners for this
 project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#fireTaskStarted(org.apache.tools.ant.Task)">fireTaskStarted</A></B>(<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Send a &quot;task started&quot; event to the build listeners
 for this project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getBaseDir()">getBaseDir</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the base directory of the project as a file object.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getBuildListeners()">getBuildListeners</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return a copy of the list of build listeners for the project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.ClassLoader</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getCoreLoader()">getCoreLoader</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the core classloader to use for this project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getDataTypeDefinitions()">getDataTypeDefinitions</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the current datatype definition hashtable.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.InputStream</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getDefaultInputStream()">getDefaultInputStream</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get this project's input stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getDefaultTarget()">getDefaultTarget</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the name of the default target of the project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getDescription()">getDescription</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the project description, if one has been set.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getElementName(java.lang.Object)">getElementName</A></B>(java.lang.Object&nbsp;element)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return a description of the type of the given element, with
 special handling for instances of tasks and data types.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/Executor.html" title="interface in org.apache.tools.ant">Executor</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getExecutor()">getExecutor</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get this Project's Executor (setting it if necessary).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getFilters()">getFilters</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.4.x
             Use getGlobalFilterSet().getFilterHash().</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/types/FilterSet.html" title="class in org.apache.tools.ant.types">FilterSet</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getGlobalFilterSet()">getGlobalFilterSet</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the set of global filters.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/input/InputHandler.html" title="interface in org.apache.tools.ant.input">InputHandler</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getInputHandler()">getInputHandler</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Retrieve the current input handler.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getJavaVersion()">getJavaVersion</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use org.apache.tools.ant.util.JavaEnvUtils instead.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getName()">getName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the project name, if one has been set.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getProperties()">getProperties</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return a copy of the properties table.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getProperty(java.lang.String)">getProperty</A></B>(java.lang.String&nbsp;propertyName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the value of a property, if it is set.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getReference(java.lang.String)">getReference</A></B>(java.lang.String&nbsp;key)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Look up a reference by its key (ID).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getReferences()">getReferences</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return a map of the references in the project (String to Object).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getResource(java.lang.String)">getResource</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Resolve the file relative to the project's basedir and return it as a
 FileResource.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getTargets()">getTargets</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the hashtable of targets.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getTaskDefinitions()">getTaskDefinitions</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the current task definition hashtable.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getThreadTask(java.lang.Thread)">getThreadTask</A></B>(java.lang.Thread&nbsp;thread)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the current task associated with a thread, if any.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getUserProperties()">getUserProperties</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return a copy of the Employee property hashtable.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#getUserProperty(java.lang.String)">getUserProperty</A></B>(java.lang.String&nbsp;propertyName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the value of a Employee property, if it is set.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#inheritIDReferences(org.apache.tools.ant.Project)">inheritIDReferences</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;parent)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Inherit the id references.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#init()">init</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Initialise the project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#initProperties()">initProperties</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Initializes the properties.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#initSubProject(org.apache.tools.ant.Project)">initSubProject</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;subProject)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Initialize a subproject.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#isKeepGoingMode()">isKeepGoingMode</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the keep-going mode.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#log(java.lang.String)">log</A></B>(java.lang.String&nbsp;message)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Write a message to the log with the default log level
 of MSG_INFO .</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#log(java.lang.String, int)">log</A></B>(java.lang.String&nbsp;message,
    int&nbsp;msgLevel)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Write a project level message to the log with the given log level.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#log(java.lang.String, java.lang.Throwable, int)">log</A></B>(java.lang.String&nbsp;message,
    java.lang.Throwable&nbsp;throwable,
    int&nbsp;msgLevel)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Write a project level message to the log with the given log level.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#log(org.apache.tools.ant.Target, java.lang.String, int)">log</A></B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target,
    java.lang.String&nbsp;message,
    int&nbsp;msgLevel)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Write a target level message to the log with the given log level.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#log(org.apache.tools.ant.Target, java.lang.String, java.lang.Throwable, int)">log</A></B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target,
    java.lang.String&nbsp;message,
    java.lang.Throwable&nbsp;throwable,
    int&nbsp;msgLevel)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Write a target level message to the log with the given log level.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#log(org.apache.tools.ant.Task, java.lang.String, int)">log</A></B>(<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task,
    java.lang.String&nbsp;message,
    int&nbsp;msgLevel)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Write a task level message to the log with the given log level.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#log(org.apache.tools.ant.Task, java.lang.String, java.lang.Throwable, int)">log</A></B>(<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task,
    java.lang.String&nbsp;message,
    java.lang.Throwable&nbsp;throwable,
    int&nbsp;msgLevel)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Write a task level message to the log with the given log level.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#registerThreadTask(java.lang.Thread, org.apache.tools.ant.Task)">registerThreadTask</A></B>(java.lang.Thread&nbsp;thread,
                   <A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Register a task as the current task for a thread.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#removeBuildListener(org.apache.tools.ant.BuildListener)">removeBuildListener</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A>&nbsp;listener)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Remove a build listener from the list.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#replaceProperties(java.lang.String)">replaceProperties</A></B>(java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Replace ${} style constructions in the given value with the
 string value of the corresponding data types.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#resolveFile(java.lang.String)">resolveFile</A></B>(java.lang.String&nbsp;fileName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the canonical form of a filename.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#resolveFile(java.lang.String, java.io.File)">resolveFile</A></B>(java.lang.String&nbsp;fileName,
            java.io.File&nbsp;rootDir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.4.x</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setBaseDir(java.io.File)">setBaseDir</A></B>(java.io.File&nbsp;baseDir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the base directory for the project, checking that
 the given file exists and is a directory.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setBasedir(java.lang.String)">setBasedir</A></B>(java.lang.String&nbsp;baseD)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the base directory for the project, checking that
 the given filename exists and is a directory.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setCoreLoader(java.lang.ClassLoader)">setCoreLoader</A></B>(java.lang.ClassLoader&nbsp;coreLoader)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the core classloader for the project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setDefault(java.lang.String)">setDefault</A></B>(java.lang.String&nbsp;defaultTarget)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the default target of the project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setDefaultInputStream(java.io.InputStream)">setDefaultInputStream</A></B>(java.io.InputStream&nbsp;defaultInputStream)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the default System input stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setDefaultTarget(java.lang.String)">setDefaultTarget</A></B>(java.lang.String&nbsp;defaultTarget)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use setDefault.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setDescription(java.lang.String)">setDescription</A></B>(java.lang.String&nbsp;description)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the project description.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setExecutor(org.apache.tools.ant.Executor)">setExecutor</A></B>(<A HREF="../../../../org/apache/tools/ant/Executor.html" title="interface in org.apache.tools.ant">Executor</A>&nbsp;e)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the Executor instance for this Project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setFileLastModified(java.io.File, long)">setFileLastModified</A></B>(java.io.File&nbsp;file,
                    long&nbsp;time)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.4.x</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setInheritedProperty(java.lang.String, java.lang.String)">setInheritedProperty</A></B>(java.lang.String&nbsp;name,
                     java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set a Employee property, which cannot be overwritten by set/unset
 property calls.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setInputHandler(org.apache.tools.ant.input.InputHandler)">setInputHandler</A></B>(<A HREF="../../../../org/apache/tools/ant/input/InputHandler.html" title="interface in org.apache.tools.ant.input">InputHandler</A>&nbsp;handler)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the input handler.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setJavaVersionProperty()">setJavaVersionProperty</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the <code>ant.java.version</code> property and tests for
 unsupported JVM versions.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setKeepGoingMode(boolean)">setKeepGoingMode</A></B>(boolean&nbsp;keepGoingMode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set &quot;keep-going&quot; mode.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setName(java.lang.String)">setName</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the name of the project, also setting the Employee
 property <code>ant.project.name</code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setNewProperty(java.lang.String, java.lang.String)">setNewProperty</A></B>(java.lang.String&nbsp;name,
               java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set a property if no value currently exists.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setProjectReference(java.lang.Object)">setProjectReference</A></B>(java.lang.Object&nbsp;obj)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set a reference to this Project on the parameterized object.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setProperty(java.lang.String, java.lang.String)">setProperty</A></B>(java.lang.String&nbsp;name,
            java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set a property.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setSystemProperties()">setSystemProperties</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add all system properties which aren't already defined as
 Employee properties to the project properties.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#setUserProperty(java.lang.String, java.lang.String)">setUserProperty</A></B>(java.lang.String&nbsp;name,
                java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set a Employee property, which cannot be overwritten by
 set/unset property calls.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#toBoolean(java.lang.String)">toBoolean</A></B>(java.lang.String&nbsp;s)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the boolean equivalent of a string, which is considered
 <code>true</code> if either <code>"on"</code>, <code>"true"</code>,
 or <code>"yes"</code> is found, ignoring case.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#topoSort(java.lang.String[], java.util.Hashtable, boolean)">topoSort</A></B>(java.lang.String[]&nbsp;root,
         java.util.Hashtable&nbsp;targetTable,
         boolean&nbsp;returnAll)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Topologically sort a set of targets.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#topoSort(java.lang.String, java.util.Hashtable)">topoSort</A></B>(java.lang.String&nbsp;root,
         java.util.Hashtable&nbsp;targetTable)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Topologically sort a set of targets.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#topoSort(java.lang.String, java.util.Hashtable, boolean)">topoSort</A></B>(java.lang.String&nbsp;root,
         java.util.Hashtable&nbsp;targetTable,
         boolean&nbsp;returnAll)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Topologically sort a set of targets.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Project.html#translatePath(java.lang.String)">translatePath</A></B>(java.lang.String&nbsp;toProcess)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.7
             Use FileUtils.translatePath instead.</I></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="MSG_ERR"><!-- --></A><H3>
MSG_ERR</H3>
<PRE>
public static final int <B>MSG_ERR</B></PRE>
<DL>
<DD>Message priority of &quot;error&quot;.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.Project.MSG_ERR">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="MSG_WARN"><!-- --></A><H3>
MSG_WARN</H3>
<PRE>
public static final int <B>MSG_WARN</B></PRE>
<DL>
<DD>Message priority of &quot;warning&quot;.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.Project.MSG_WARN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="MSG_INFO"><!-- --></A><H3>
MSG_INFO</H3>
<PRE>
public static final int <B>MSG_INFO</B></PRE>
<DL>
<DD>Message priority of &quot;information&quot;.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.Project.MSG_INFO">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="MSG_VERBOSE"><!-- --></A><H3>
MSG_VERBOSE</H3>
<PRE>
public static final int <B>MSG_VERBOSE</B></PRE>
<DL>
<DD>Message priority of &quot;verbose&quot;.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.Project.MSG_VERBOSE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="MSG_DEBUG"><!-- --></A><H3>
MSG_DEBUG</H3>
<PRE>
public static final int <B>MSG_DEBUG</B></PRE>
<DL>
<DD>Message priority of &quot;debug&quot;.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.Project.MSG_DEBUG">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="JAVA_1_0"><!-- --></A><H3>
JAVA_1_0</H3>
<PRE>
public static final java.lang.String <B>JAVA_1_0</B></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use <A HREF="../../../../org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_0"><CODE>JavaEnvUtils.JAVA_1_0</CODE></A> instead.</I><DD>Version constant for Java 1.0 .
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.Project.JAVA_1_0">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="JAVA_1_1"><!-- --></A><H3>
JAVA_1_1</H3>
<PRE>
public static final java.lang.String <B>JAVA_1_1</B></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use <A HREF="../../../../org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_1"><CODE>JavaEnvUtils.JAVA_1_1</CODE></A> instead.</I><DD>Version constant for Java 1.1 .
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.Project.JAVA_1_1">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="JAVA_1_2"><!-- --></A><H3>
JAVA_1_2</H3>
<PRE>
public static final java.lang.String <B>JAVA_1_2</B></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use <A HREF="../../../../org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_2"><CODE>JavaEnvUtils.JAVA_1_2</CODE></A> instead.</I><DD>Version constant for Java 1.2 .
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.Project.JAVA_1_2">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="JAVA_1_3"><!-- --></A><H3>
JAVA_1_3</H3>
<PRE>
public static final java.lang.String <B>JAVA_1_3</B></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use <A HREF="../../../../org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_3"><CODE>JavaEnvUtils.JAVA_1_3</CODE></A> instead.</I><DD>Version constant for Java 1.3 .
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.Project.JAVA_1_3">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="JAVA_1_4"><!-- --></A><H3>
JAVA_1_4</H3>
<PRE>
public static final java.lang.String <B>JAVA_1_4</B></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use <A HREF="../../../../org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_4"><CODE>JavaEnvUtils.JAVA_1_4</CODE></A> instead.</I><DD>Version constant for Java 1.4 .
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.Project.JAVA_1_4">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="TOKEN_START"><!-- --></A><H3>
TOKEN_START</H3>
<PRE>
public static final java.lang.String <B>TOKEN_START</B></PRE>
<DL>
<DD>Default filter start token.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.Project.TOKEN_START">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="TOKEN_END"><!-- --></A><H3>
TOKEN_END</H3>
<PRE>
public static final java.lang.String <B>TOKEN_END</B></PRE>
<DL>
<DD>Default filter end token.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.Project.TOKEN_END">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Project()"><!-- --></A><H3>
Project</H3>
<PRE>
public <B>Project</B>()</PRE>
<DL>
<DD>Create a new Ant project.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setInputHandler(org.apache.tools.ant.input.InputHandler)"><!-- --></A><H3>
setInputHandler</H3>
<PRE>
public void <B>setInputHandler</B>(<A HREF="../../../../org/apache/tools/ant/input/InputHandler.html" title="interface in org.apache.tools.ant.input">InputHandler</A>&nbsp;handler)</PRE>
<DL>
<DD>Set the input handler.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>handler</CODE> - the InputHandler instance to use for gathering input.</DL>
</DD>
</DL>
<HR>

<A NAME="setDefaultInputStream(java.io.InputStream)"><!-- --></A><H3>
setDefaultInputStream</H3>
<PRE>
public void <B>setDefaultInputStream</B>(java.io.InputStream&nbsp;defaultInputStream)</PRE>
<DL>
<DD>Set the default System input stream. Normally this stream is set to
 System.in. This inputStream is used when no task input redirection is
 being performed.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>defaultInputStream</CODE> - the default input stream to use when input
        is requested.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getDefaultInputStream()"><!-- --></A><H3>
getDefaultInputStream</H3>
<PRE>
public java.io.InputStream <B>getDefaultInputStream</B>()</PRE>
<DL>
<DD>Get this project's input stream.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the InputStream instance in use by this Project instance to
 read input.</DL>
</DD>
</DL>
<HR>

<A NAME="getInputHandler()"><!-- --></A><H3>
getInputHandler</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/input/InputHandler.html" title="interface in org.apache.tools.ant.input">InputHandler</A> <B>getInputHandler</B>()</PRE>
<DL>
<DD>Retrieve the current input handler.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the InputHandler instance currently in place for the project
         instance.</DL>
</DD>
</DL>
<HR>

<A NAME="createSubProject()"><!-- --></A><H3>
createSubProject</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A> <B>createSubProject</B>()</PRE>
<DL>
<DD>Create and initialize a subproject. By default the subproject will be of
 the same type as its parent. If a no-arg constructor is unavailable, the
 <code>Project</code> class will be used.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a Project instance configured as a subproject of this Project.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="initSubProject(org.apache.tools.ant.Project)"><!-- --></A><H3>
initSubProject</H3>
<PRE>
public void <B>initSubProject</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;subProject)</PRE>
<DL>
<DD>Initialize a subproject.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>subProject</CODE> - the subproject to initialize.</DL>
</DD>
</DL>
<HR>

<A NAME="init()"><!-- --></A><H3>
init</H3>
<PRE>
public void <B>init</B>()
          throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Initialise the project.

 This involves setting the default task definitions and loading the
 system properties.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the default task list cannot be loaded.</DL>
</DD>
</DL>
<HR>

<A NAME="initProperties()"><!-- --></A><H3>
initProperties</H3>
<PRE>
public void <B>initProperties</B>()
                    throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Initializes the properties.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if an vital property could not be set.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="createClassLoader(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
createClassLoader</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</A> <B>createClassLoader</B>(<A HREF="../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;path)</PRE>
<DL>
<DD>Factory method to create a class loader for loading classes from
 a given path.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>path</CODE> - the path from which classes are to be loaded.
<DT><B>Returns:</B><DD>an appropriate classloader.</DL>
</DD>
</DL>
<HR>

<A NAME="createClassLoader(java.lang.ClassLoader, org.apache.tools.ant.types.Path)"><!-- --></A><H3>
createClassLoader</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/AntClassLoader.html" title="class in org.apache.tools.ant">AntClassLoader</A> <B>createClassLoader</B>(java.lang.ClassLoader&nbsp;parent,
                                        <A HREF="../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;path)</PRE>
<DL>
<DD>Factory method to create a class loader for loading classes from
 a given path.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>parent</CODE> - the parent classloader for the new loader.<DD><CODE>path</CODE> - the path from which classes are to be loaded.
<DT><B>Returns:</B><DD>an appropriate classloader.</DL>
</DD>
</DL>
<HR>

<A NAME="setCoreLoader(java.lang.ClassLoader)"><!-- --></A><H3>
setCoreLoader</H3>
<PRE>
public void <B>setCoreLoader</B>(java.lang.ClassLoader&nbsp;coreLoader)</PRE>
<DL>
<DD>Set the core classloader for the project. If a <code>null</code>
 classloader is specified, the parent classloader should be used.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>coreLoader</CODE> - The classloader to use for the project.
                   May be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="getCoreLoader()"><!-- --></A><H3>
getCoreLoader</H3>
<PRE>
public java.lang.ClassLoader <B>getCoreLoader</B>()</PRE>
<DL>
<DD>Return the core classloader to use for this project.
 This may be <code>null</code>, indicating that
 the parent classloader should be used.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the core classloader to use for this project.</DL>
</DD>
</DL>
<HR>

<A NAME="addBuildListener(org.apache.tools.ant.BuildListener)"><!-- --></A><H3>
addBuildListener</H3>
<PRE>
public void <B>addBuildListener</B>(<A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A>&nbsp;listener)</PRE>
<DL>
<DD>Add a build listener to the list. This listener will
 be notified of build events for this project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>listener</CODE> - The listener to add to the list.
                 Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="removeBuildListener(org.apache.tools.ant.BuildListener)"><!-- --></A><H3>
removeBuildListener</H3>
<PRE>
public void <B>removeBuildListener</B>(<A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A>&nbsp;listener)</PRE>
<DL>
<DD>Remove a build listener from the list. This listener
 will no longer be notified of build events for this project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>listener</CODE> - The listener to remove from the list.
                 Should not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="getBuildListeners()"><!-- --></A><H3>
getBuildListeners</H3>
<PRE>
public java.util.Vector <B>getBuildListeners</B>()</PRE>
<DL>
<DD>Return a copy of the list of build listeners for the project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a list of build listeners for the project</DL>
</DD>
</DL>
<HR>

<A NAME="log(java.lang.String)"><!-- --></A><H3>
log</H3>
<PRE>
public void <B>log</B>(java.lang.String&nbsp;message)</PRE>
<DL>
<DD>Write a message to the log with the default log level
 of MSG_INFO .
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>message</CODE> - The text to log. Should not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="log(java.lang.String, int)"><!-- --></A><H3>
log</H3>
<PRE>
public void <B>log</B>(java.lang.String&nbsp;message,
                int&nbsp;msgLevel)</PRE>
<DL>
<DD>Write a project level message to the log with the given log level.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>message</CODE> - The text to log. Should not be <code>null</code>.<DD><CODE>msgLevel</CODE> - The log priority level to use.</DL>
</DD>
</DL>
<HR>

<A NAME="log(java.lang.String, java.lang.Throwable, int)"><!-- --></A><H3>
log</H3>
<PRE>
public void <B>log</B>(java.lang.String&nbsp;message,
                java.lang.Throwable&nbsp;throwable,
                int&nbsp;msgLevel)</PRE>
<DL>
<DD>Write a project level message to the log with the given log level.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>message</CODE> - The text to log. Should not be <code>null</code>.<DD><CODE>throwable</CODE> - The exception causing this log, may be <code>null</code>.<DD><CODE>msgLevel</CODE> - The log priority level to use.<DT><B>Since:</B></DT>
  <DD>1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="log(org.apache.tools.ant.Task, java.lang.String, int)"><!-- --></A><H3>
log</H3>
<PRE>
public void <B>log</B>(<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task,
                java.lang.String&nbsp;message,
                int&nbsp;msgLevel)</PRE>
<DL>
<DD>Write a task level message to the log with the given log level.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>task</CODE> - The task to use in the log. Must not be <code>null</code>.<DD><CODE>message</CODE> - The text to log. Should not be <code>null</code>.<DD><CODE>msgLevel</CODE> - The log priority level to use.</DL>
</DD>
</DL>
<HR>

<A NAME="log(org.apache.tools.ant.Task, java.lang.String, java.lang.Throwable, int)"><!-- --></A><H3>
log</H3>
<PRE>
public void <B>log</B>(<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task,
                java.lang.String&nbsp;message,
                java.lang.Throwable&nbsp;throwable,
                int&nbsp;msgLevel)</PRE>
<DL>
<DD>Write a task level message to the log with the given log level.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>task</CODE> - The task to use in the log. Must not be <code>null</code>.<DD><CODE>message</CODE> - The text to log. Should not be <code>null</code>.<DD><CODE>throwable</CODE> - The exception causing this log, may be <code>null</code>.<DD><CODE>msgLevel</CODE> - The log priority level to use.<DT><B>Since:</B></DT>
  <DD>1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="log(org.apache.tools.ant.Target, java.lang.String, int)"><!-- --></A><H3>
log</H3>
<PRE>
public void <B>log</B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target,
                java.lang.String&nbsp;message,
                int&nbsp;msgLevel)</PRE>
<DL>
<DD>Write a target level message to the log with the given log level.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>target</CODE> - The target to use in the log.
               Must not be <code>null</code>.<DD><CODE>message</CODE> - The text to log. Should not be <code>null</code>.<DD><CODE>msgLevel</CODE> - The log priority level to use.</DL>
</DD>
</DL>
<HR>

<A NAME="log(org.apache.tools.ant.Target, java.lang.String, java.lang.Throwable, int)"><!-- --></A><H3>
log</H3>
<PRE>
public void <B>log</B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target,
                java.lang.String&nbsp;message,
                java.lang.Throwable&nbsp;throwable,
                int&nbsp;msgLevel)</PRE>
<DL>
<DD>Write a target level message to the log with the given log level.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>target</CODE> - The target to use in the log.
               Must not be <code>null</code>.<DD><CODE>message</CODE> - The text to log. Should not be <code>null</code>.<DD><CODE>throwable</CODE> - The exception causing this log, may be <code>null</code>.<DD><CODE>msgLevel</CODE> - The log priority level to use.<DT><B>Since:</B></DT>
  <DD>1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getGlobalFilterSet()"><!-- --></A><H3>
getGlobalFilterSet</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/types/FilterSet.html" title="class in org.apache.tools.ant.types">FilterSet</A> <B>getGlobalFilterSet</B>()</PRE>
<DL>
<DD>Return the set of global filters.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the set of global filters.</DL>
</DD>
</DL>
<HR>

<A NAME="setProperty(java.lang.String, java.lang.String)"><!-- --></A><H3>
setProperty</H3>
<PRE>
public void <B>setProperty</B>(java.lang.String&nbsp;name,
                        java.lang.String&nbsp;value)</PRE>
<DL>
<DD>Set a property. Any existing property of the same name
 is overwritten, unless it is a Employee property.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - The name of property to set.
             Must not be <code>null</code>.<DD><CODE>value</CODE> - The new value of the property.
              Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setNewProperty(java.lang.String, java.lang.String)"><!-- --></A><H3>
setNewProperty</H3>
<PRE>
public void <B>setNewProperty</B>(java.lang.String&nbsp;name,
                           java.lang.String&nbsp;value)</PRE>
<DL>
<DD>Set a property if no value currently exists. If the property
 exists already, a message is logged and the method returns with
 no other effect.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - The name of property to set.
             Must not be <code>null</code>.<DD><CODE>value</CODE> - The new value of the property.
              Must not be <code>null</code>.<DT><B>Since:</B></DT>
  <DD>1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setUserProperty(java.lang.String, java.lang.String)"><!-- --></A><H3>
setUserProperty</H3>
<PRE>
public void <B>setUserProperty</B>(java.lang.String&nbsp;name,
                            java.lang.String&nbsp;value)</PRE>
<DL>
<DD>Set a Employee property, which cannot be overwritten by
 set/unset property calls. Any previous value is overwritten.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - The name of property to set.
             Must not be <code>null</code>.<DD><CODE>value</CODE> - The new value of the property.
              Must not be <code>null</code>.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/Project.html#setProperty(java.lang.String, java.lang.String)"><CODE>setProperty(String,String)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setInheritedProperty(java.lang.String, java.lang.String)"><!-- --></A><H3>
setInheritedProperty</H3>
<PRE>
public void <B>setInheritedProperty</B>(java.lang.String&nbsp;name,
                                 java.lang.String&nbsp;value)</PRE>
<DL>
<DD>Set a Employee property, which cannot be overwritten by set/unset
 property calls. Any previous value is overwritten. Also marks
 these properties as properties that have not come from the
 command line.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - The name of property to set.
             Must not be <code>null</code>.<DD><CODE>value</CODE> - The new value of the property.
              Must not be <code>null</code>.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/Project.html#setProperty(java.lang.String, java.lang.String)"><CODE>setProperty(String,String)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getProperty(java.lang.String)"><!-- --></A><H3>
getProperty</H3>
<PRE>
public java.lang.String <B>getProperty</B>(java.lang.String&nbsp;propertyName)</PRE>
<DL>
<DD>Return the value of a property, if it is set.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>propertyName</CODE> - The name of the property.
             May be <code>null</code>, in which case
             the return value is also <code>null</code>.
<DT><B>Returns:</B><DD>the property value, or <code>null</code> for no match
         or if a <code>null</code> name is provided.</DL>
</DD>
</DL>
<HR>

<A NAME="replaceProperties(java.lang.String)"><!-- --></A><H3>
replaceProperties</H3>
<PRE>
public java.lang.String <B>replaceProperties</B>(java.lang.String&nbsp;value)
                                   throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Replace ${} style constructions in the given value with the
 string value of the corresponding data types.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - The string to be scanned for property references.
              May be <code>null</code>.
<DT><B>Returns:</B><DD>the given string with embedded property names replaced
         by values, or <code>null</code> if the given string is
         <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the given value has an unclosed
                           property name, e.g. <code>${xxx</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="getUserProperty(java.lang.String)"><!-- --></A><H3>
getUserProperty</H3>
<PRE>
public java.lang.String <B>getUserProperty</B>(java.lang.String&nbsp;propertyName)</PRE>
<DL>
<DD>Return the value of a Employee property, if it is set.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>propertyName</CODE> - The name of the property.
             May be <code>null</code>, in which case
             the return value is also <code>null</code>.
<DT><B>Returns:</B><DD>the property value, or <code>null</code> for no match
         or if a <code>null</code> name is provided.</DL>
</DD>
</DL>
<HR>

<A NAME="getProperties()"><!-- --></A><H3>
getProperties</H3>
<PRE>
public java.util.Hashtable <B>getProperties</B>()</PRE>
<DL>
<DD>Return a copy of the properties table.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a hashtable containing all properties
         (including Employee properties).</DL>
</DD>
</DL>
<HR>

<A NAME="getUserProperties()"><!-- --></A><H3>
getUserProperties</H3>
<PRE>
public java.util.Hashtable <B>getUserProperties</B>()</PRE>
<DL>
<DD>Return a copy of the Employee property hashtable.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a hashtable containing just the Employee properties.</DL>
</DD>
</DL>
<HR>

<A NAME="copyUserProperties(org.apache.tools.ant.Project)"><!-- --></A><H3>
copyUserProperties</H3>
<PRE>
public void <B>copyUserProperties</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;other)</PRE>
<DL>
<DD>Copy all Employee properties that have been set on the command
 line or a GUI tool from this instance to the Project instance
 given as the argument.

 <p>To copy all &quot;Employee&quot; properties, you will also have to call
 <A HREF="../../../../org/apache/tools/ant/Project.html#copyInheritedProperties(org.apache.tools.ant.Project)"><CODE>copyInheritedProperties</CODE></A>.</p>
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>other</CODE> - the project to copy the properties to.  Must not be null.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="copyInheritedProperties(org.apache.tools.ant.Project)"><!-- --></A><H3>
copyInheritedProperties</H3>
<PRE>
public void <B>copyInheritedProperties</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;other)</PRE>
<DL>
<DD>Copy all Employee properties that have not been set on the
 command line or a GUI tool from this instance to the Project
 instance given as the argument.

 <p>To copy all &quot;Employee&quot; properties, you will also have to call
 <A HREF="../../../../org/apache/tools/ant/Project.html#copyUserProperties(org.apache.tools.ant.Project)"><CODE>copyUserProperties</CODE></A>.</p>
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>other</CODE> - the project to copy the properties to.  Must not be null.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setDefaultTarget(java.lang.String)"><!-- --></A><H3>
setDefaultTarget</H3>
<PRE>
public void <B>setDefaultTarget</B>(java.lang.String&nbsp;defaultTarget)</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use setDefault.</I>
<P>
<DD>Set the default target of the project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>defaultTarget</CODE> - The name of the default target for this project.
                      May be <code>null</code>, indicating that there is
                      no default target.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/Project.html#setDefault(java.lang.String)"><CODE>setDefault(String)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getDefaultTarget()"><!-- --></A><H3>
getDefaultTarget</H3>
<PRE>
public java.lang.String <B>getDefaultTarget</B>()</PRE>
<DL>
<DD>Return the name of the default target of the project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>name of the default target or
         <code>null</code> if no default has been set.</DL>
</DD>
</DL>
<HR>

<A NAME="setDefault(java.lang.String)"><!-- --></A><H3>
setDefault</H3>
<PRE>
public void <B>setDefault</B>(java.lang.String&nbsp;defaultTarget)</PRE>
<DL>
<DD>Set the default target of the project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>defaultTarget</CODE> - The name of the default target for this project.
                      May be <code>null</code>, indicating that there is
                      no default target.</DL>
</DD>
</DL>
<HR>

<A NAME="setName(java.lang.String)"><!-- --></A><H3>
setName</H3>
<PRE>
public void <B>setName</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Set the name of the project, also setting the Employee
 property <code>ant.project.name</code>.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - The name of the project.
             Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="getName()"><!-- --></A><H3>
getName</H3>
<PRE>
public java.lang.String <B>getName</B>()</PRE>
<DL>
<DD>Return the project name, if one has been set.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the project name, or <code>null</code> if it hasn't been set.</DL>
</DD>
</DL>
<HR>

<A NAME="setDescription(java.lang.String)"><!-- --></A><H3>
setDescription</H3>
<PRE>
public void <B>setDescription</B>(java.lang.String&nbsp;description)</PRE>
<DL>
<DD>Set the project description.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>description</CODE> - The description of the project.
                    May be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="getDescription()"><!-- --></A><H3>
getDescription</H3>
<PRE>
public java.lang.String <B>getDescription</B>()</PRE>
<DL>
<DD>Return the project description, if one has been set.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the project description, or <code>null</code> if it hasn't
         been set.</DL>
</DD>
</DL>
<HR>

<A NAME="addFilter(java.lang.String, java.lang.String)"><!-- --></A><H3>
addFilter</H3>
<PRE>
public void <B>addFilter</B>(java.lang.String&nbsp;token,
                      java.lang.String&nbsp;value)</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.4.x.
             Use getGlobalFilterSet().addFilter(token,value)</I>
<P>
<DD>Add a filter to the set of global filters.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>token</CODE> - The token to filter.
              Must not be <code>null</code>.<DD><CODE>value</CODE> - The replacement value.
              Must not be <code>null</code>.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/Project.html#getGlobalFilterSet()"><CODE>getGlobalFilterSet()</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/types/FilterSet.html#addFilter(java.lang.String, java.lang.String)"><CODE>FilterSet.addFilter(String,String)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getFilters()"><!-- --></A><H3>
getFilters</H3>
<PRE>
public java.util.Hashtable <B>getFilters</B>()</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.4.x
             Use getGlobalFilterSet().getFilterHash().</I>
<P>
<DD>Return a hashtable of global filters, mapping tokens to values.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a hashtable of global filters, mapping tokens to values
         (String to String).<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/Project.html#getGlobalFilterSet()"><CODE>getGlobalFilterSet()</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/types/FilterSet.html#getFilterHash()"><CODE>FilterSet.getFilterHash()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setBasedir(java.lang.String)"><!-- --></A><H3>
setBasedir</H3>
<PRE>
public void <B>setBasedir</B>(java.lang.String&nbsp;baseD)
                throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Set the base directory for the project, checking that
 the given filename exists and is a directory.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>baseD</CODE> - The project base directory.
              Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the directory if invalid.</DL>
</DD>
</DL>
<HR>

<A NAME="setBaseDir(java.io.File)"><!-- --></A><H3>
setBaseDir</H3>
<PRE>
public void <B>setBaseDir</B>(java.io.File&nbsp;baseDir)
                throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Set the base directory for the project, checking that
 the given file exists and is a directory.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>baseDir</CODE> - The project base directory.
                Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the specified file doesn't exist or
                           isn't a directory.</DL>
</DD>
</DL>
<HR>

<A NAME="getBaseDir()"><!-- --></A><H3>
getBaseDir</H3>
<PRE>
public java.io.File <B>getBaseDir</B>()</PRE>
<DL>
<DD>Return the base directory of the project as a file object.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the project base directory, or <code>null</code> if the
         base directory has not been successfully set to a valid value.</DL>
</DD>
</DL>
<HR>

<A NAME="setKeepGoingMode(boolean)"><!-- --></A><H3>
setKeepGoingMode</H3>
<PRE>
public void <B>setKeepGoingMode</B>(boolean&nbsp;keepGoingMode)</PRE>
<DL>
<DD>Set &quot;keep-going&quot; mode. In this mode Ant will try to execute
 as many targets as possible. All targets that do not depend
 on failed target(s) will be executed.  If the keepGoing settor/getter
 methods are used in conjunction with the <code>ant.executor.class</code>
 property, they will have no effect.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>keepGoingMode</CODE> - &quot;keep-going&quot; mode<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="isKeepGoingMode()"><!-- --></A><H3>
isKeepGoingMode</H3>
<PRE>
public boolean <B>isKeepGoingMode</B>()</PRE>
<DL>
<DD>Return the keep-going mode.  If the keepGoing settor/getter
 methods are used in conjunction with the <code>ant.executor.class</code>
 property, they will have no effect.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>&quot;keep-going&quot; mode<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getJavaVersion()"><!-- --></A><H3>
getJavaVersion</H3>
<PRE>
public static java.lang.String <B>getJavaVersion</B>()</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.5.x.
             Use org.apache.tools.ant.util.JavaEnvUtils instead.</I>
<P>
<DD>Return the version of Java this class is running under.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the version of Java as a String, e.g. "1.1" .<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/util/JavaEnvUtils.html#getJavaVersion()"><CODE>JavaEnvUtils.getJavaVersion()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setJavaVersionProperty()"><!-- --></A><H3>
setJavaVersionProperty</H3>
<PRE>
public void <B>setJavaVersionProperty</B>()
                            throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Set the <code>ant.java.version</code> property and tests for
 unsupported JVM versions. If the version is supported,
 verbose log messages are generated to record the Java version
 and operating system name.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if this Java version is not supported.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/util/JavaEnvUtils.html#getJavaVersion()"><CODE>JavaEnvUtils.getJavaVersion()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setSystemProperties()"><!-- --></A><H3>
setSystemProperties</H3>
<PRE>
public void <B>setSystemProperties</B>()</PRE>
<DL>
<DD>Add all system properties which aren't already defined as
 Employee properties to the project properties.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="addTaskDefinition(java.lang.String, java.lang.Class)"><!-- --></A><H3>
addTaskDefinition</H3>
<PRE>
public void <B>addTaskDefinition</B>(java.lang.String&nbsp;taskName,
                              java.lang.Class&nbsp;taskClass)
                       throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Add a new task definition to the project.
 Attempting to override an existing definition with an
 equivalent one (i.e. with the same classname) results in
 a verbose log message. Attempting to override an existing definition
 with a different one results in a warning log message and
 invalidates any tasks which have already been created with the
 old definition.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>taskName</CODE> - The name of the task to add.
                 Must not be <code>null</code>.<DD><CODE>taskClass</CODE> - The full name of the class implementing the task.
                  Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the class is unsuitable for being an Ant
                           task. An error level message is logged before
                           this exception is thrown.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/Project.html#checkTaskClass(java.lang.Class)"><CODE>checkTaskClass(Class)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="checkTaskClass(java.lang.Class)"><!-- --></A><H3>
checkTaskClass</H3>
<PRE>
public void <B>checkTaskClass</B>(java.lang.Class&nbsp;taskClass)
                    throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Check whether or not a class is suitable for serving as Ant task.
 Ant task implementation classes must be public, concrete, and have
 a no-arg constructor.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>taskClass</CODE> - The class to be checked.
                  Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the class is unsuitable for being an Ant
                           task. An error level message is logged before
                           this exception is thrown.</DL>
</DD>
</DL>
<HR>

<A NAME="getTaskDefinitions()"><!-- --></A><H3>
getTaskDefinitions</H3>
<PRE>
public java.util.Hashtable <B>getTaskDefinitions</B>()</PRE>
<DL>
<DD>Return the current task definition hashtable. The returned hashtable is
 &quot;live&quot; and so should not be modified.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a map of from task name to implementing class
         (String to Class).</DL>
</DD>
</DL>
<HR>

<A NAME="addDataTypeDefinition(java.lang.String, java.lang.Class)"><!-- --></A><H3>
addDataTypeDefinition</H3>
<PRE>
public void <B>addDataTypeDefinition</B>(java.lang.String&nbsp;typeName,
                                  java.lang.Class&nbsp;typeClass)</PRE>
<DL>
<DD>Add a new datatype definition.
 Attempting to override an existing definition with an
 equivalent one (i.e. with the same classname) results in
 a verbose log message. Attempting to override an existing definition
 with a different one results in a warning log message, but the
 definition is changed.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>typeName</CODE> - The name of the datatype.
                 Must not be <code>null</code>.<DD><CODE>typeClass</CODE> - The full name of the class implementing the datatype.
                  Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="getDataTypeDefinitions()"><!-- --></A><H3>
getDataTypeDefinitions</H3>
<PRE>
public java.util.Hashtable <B>getDataTypeDefinitions</B>()</PRE>
<DL>
<DD>Return the current datatype definition hashtable. The returned
 hashtable is &quot;live&quot; and so should not be modified.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a map of from datatype name to implementing class
         (String to Class).</DL>
</DD>
</DL>
<HR>

<A NAME="addTarget(org.apache.tools.ant.Target)"><!-- --></A><H3>
addTarget</H3>
<PRE>
public void <B>addTarget</B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target)
               throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Add a <em>new</em> target to the project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>target</CODE> - The target to be added to the project.
               Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the target already exists in the project<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/Project.html#addOrReplaceTarget(org.apache.tools.ant.Target)"><CODE>addOrReplaceTarget(Target)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="addTarget(java.lang.String, org.apache.tools.ant.Target)"><!-- --></A><H3>
addTarget</H3>
<PRE>
public void <B>addTarget</B>(java.lang.String&nbsp;targetName,
                      <A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target)
               throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Add a <em>new</em> target to the project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>targetName</CODE> - The name to use for the target.
             Must not be <code>null</code>.<DD><CODE>target</CODE> - The target to be added to the project.
               Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the target already exists in the project.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/Project.html#addOrReplaceTarget(java.lang.String, org.apache.tools.ant.Target)"><CODE>addOrReplaceTarget(String, Target)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="addOrReplaceTarget(org.apache.tools.ant.Target)"><!-- --></A><H3>
addOrReplaceTarget</H3>
<PRE>
public void <B>addOrReplaceTarget</B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target)</PRE>
<DL>
<DD>Add a target to the project, or replaces one with the same
 name.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>target</CODE> - The target to be added or replaced in the project.
               Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="addOrReplaceTarget(java.lang.String, org.apache.tools.ant.Target)"><!-- --></A><H3>
addOrReplaceTarget</H3>
<PRE>
public void <B>addOrReplaceTarget</B>(java.lang.String&nbsp;targetName,
                               <A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target)</PRE>
<DL>
<DD>Add a target to the project, or replaces one with the same
 name.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>targetName</CODE> - The name to use for the target.
                   Must not be <code>null</code>.<DD><CODE>target</CODE> - The target to be added or replaced in the project.
               Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="getTargets()"><!-- --></A><H3>
getTargets</H3>
<PRE>
public java.util.Hashtable <B>getTargets</B>()</PRE>
<DL>
<DD>Return the hashtable of targets. The returned hashtable
 is &quot;live&quot; and so should not be modified.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a map from name to target (String to Target).</DL>
</DD>
</DL>
<HR>

<A NAME="createTask(java.lang.String)"><!-- --></A><H3>
createTask</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A> <B>createTask</B>(java.lang.String&nbsp;taskType)
                throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Create a new instance of a task, adding it to a list of
 created tasks for later invalidation. This causes all tasks
 to be remembered until the containing project is removed
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>taskType</CODE> - The name of the task to create an instance of.
                 Must not be <code>null</code>.
<DT><B>Returns:</B><DD>an instance of the specified task, or <code>null</code> if
         the task name is not recognised.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the task name is recognised but task
                           creation fails.</DL>
</DD>
</DL>
<HR>

<A NAME="createDataType(java.lang.String)"><!-- --></A><H3>
createDataType</H3>
<PRE>
public java.lang.Object <B>createDataType</B>(java.lang.String&nbsp;typeName)
                                throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Create a new instance of a data type.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>typeName</CODE> - The name of the data type to create an instance of.
                 Must not be <code>null</code>.
<DT><B>Returns:</B><DD>an instance of the specified data type, or <code>null</code> if
         the data type name is not recognised.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the data type name is recognised but
                           instance creation fails.</DL>
</DD>
</DL>
<HR>

<A NAME="setExecutor(org.apache.tools.ant.Executor)"><!-- --></A><H3>
setExecutor</H3>
<PRE>
public void <B>setExecutor</B>(<A HREF="../../../../org/apache/tools/ant/Executor.html" title="interface in org.apache.tools.ant">Executor</A>&nbsp;e)</PRE>
<DL>
<DD>Set the Executor instance for this Project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>e</CODE> - the Executor to use.</DL>
</DD>
</DL>
<HR>

<A NAME="getExecutor()"><!-- --></A><H3>
getExecutor</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/Executor.html" title="interface in org.apache.tools.ant">Executor</A> <B>getExecutor</B>()</PRE>
<DL>
<DD>Get this Project's Executor (setting it if necessary).
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>an Executor instance.</DL>
</DD>
</DL>
<HR>

<A NAME="executeTargets(java.util.Vector)"><!-- --></A><H3>
executeTargets</H3>
<PRE>
public void <B>executeTargets</B>(java.util.Vector&nbsp;names)
                    throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Execute the specified sequence of targets, and the targets
 they depend on.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>names</CODE> - A vector of target name strings to execute.
              Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the build failed.</DL>
</DD>
</DL>
<HR>

<A NAME="demuxOutput(java.lang.String, boolean)"><!-- --></A><H3>
demuxOutput</H3>
<PRE>
public void <B>demuxOutput</B>(java.lang.String&nbsp;output,
                        boolean&nbsp;isWarning)</PRE>
<DL>
<DD>Demultiplex output so that each task receives the appropriate
 messages. If the current thread is not currently executing a task,
 the message is logged directly.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - Message to handle. Should not be <code>null</code>.<DD><CODE>isWarning</CODE> - Whether the text represents an warning (<code>true</code>)
        or information (<code>false</code>).</DL>
</DD>
</DL>
<HR>

<A NAME="defaultInput(byte[], int, int)"><!-- --></A><H3>
defaultInput</H3>
<PRE>
public int <B>defaultInput</B>(byte[]&nbsp;buffer,
                        int&nbsp;offset,
                        int&nbsp;length)
                 throws java.io.IOException</PRE>
<DL>
<DD>Read data from the default input stream. If no default has been
 specified, System.in is used.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>buffer</CODE> - the buffer into which data is to be read.<DD><CODE>offset</CODE> - the offset into the buffer at which data is stored.<DD><CODE>length</CODE> - the amount of data to read.
<DT><B>Returns:</B><DD>the number of bytes read.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the data cannot be read.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="demuxInput(byte[], int, int)"><!-- --></A><H3>
demuxInput</H3>
<PRE>
public int <B>demuxInput</B>(byte[]&nbsp;buffer,
                      int&nbsp;offset,
                      int&nbsp;length)
               throws java.io.IOException</PRE>
<DL>
<DD>Demux an input request to the correct task.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>buffer</CODE> - the buffer into which data is to be read.<DD><CODE>offset</CODE> - the offset into the buffer at which data is stored.<DD><CODE>length</CODE> - the amount of data to read.
<DT><B>Returns:</B><DD>the number of bytes read.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the data cannot be read.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="demuxFlush(java.lang.String, boolean)"><!-- --></A><H3>
demuxFlush</H3>
<PRE>
public void <B>demuxFlush</B>(java.lang.String&nbsp;output,
                       boolean&nbsp;isError)</PRE>
<DL>
<DD>Demultiplex flush operations so that each task receives the appropriate
 messages. If the current thread is not currently executing a task,
 the message is logged directly.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - Message to handle. Should not be <code>null</code>.<DD><CODE>isError</CODE> - Whether the text represents an error (<code>true</code>)
        or information (<code>false</code>).<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="executeTarget(java.lang.String)"><!-- --></A><H3>
executeTarget</H3>
<PRE>
public void <B>executeTarget</B>(java.lang.String&nbsp;targetName)
                   throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Execute the specified target and any targets it depends on.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>targetName</CODE> - The name of the target to execute.
                   Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the build failed.</DL>
</DD>
</DL>
<HR>

<A NAME="executeSortedTargets(java.util.Vector)"><!-- --></A><H3>
executeSortedTargets</H3>
<PRE>
public void <B>executeSortedTargets</B>(java.util.Vector&nbsp;sortedTargets)
                          throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Execute a <code>Vector</code> of sorted targets.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sortedTargets</CODE> - the aforementioned <code>Vector</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error.</DL>
</DD>
</DL>
<HR>

<A NAME="resolveFile(java.lang.String, java.io.File)"><!-- --></A><H3>
resolveFile</H3>
<PRE>
public java.io.File <B>resolveFile</B>(java.lang.String&nbsp;fileName,
                                java.io.File&nbsp;rootDir)</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.4.x</I>
<P>
<DD>Return the canonical form of a filename.
 <p>
 If the specified file name is relative it is resolved
 with respect to the given root directory.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fileName</CODE> - The name of the file to resolve.
                 Must not be <code>null</code>.<DD><CODE>rootDir</CODE> - The directory respective to which relative file names
                 are resolved. May be <code>null</code>, in which case
                 the current directory is used.
<DT><B>Returns:</B><DD>the resolved File.</DL>
</DD>
</DL>
<HR>

<A NAME="resolveFile(java.lang.String)"><!-- --></A><H3>
resolveFile</H3>
<PRE>
public java.io.File <B>resolveFile</B>(java.lang.String&nbsp;fileName)</PRE>
<DL>
<DD>Return the canonical form of a filename.
 <p>
 If the specified file name is relative it is resolved
 with respect to the project's base directory.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fileName</CODE> - The name of the file to resolve.
                 Must not be <code>null</code>.
<DT><B>Returns:</B><DD>the resolved File.</DL>
</DD>
</DL>
<HR>

<A NAME="translatePath(java.lang.String)"><!-- --></A><H3>
translatePath</H3>
<PRE>
public static java.lang.String <B>translatePath</B>(java.lang.String&nbsp;toProcess)</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.7
             Use FileUtils.translatePath instead.</I>
<P>
<DD>Translate a path into its native (platform specific) format.
 <p>
 This method uses PathTokenizer to separate the input path
 into its components. This handles DOS style paths in a relatively
 sensible way. The file separators are then converted to their platform
 specific versions.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>toProcess</CODE> - The path to be translated.
                  May be <code>null</code>.
<DT><B>Returns:</B><DD>the native version of the specified path or
         an empty string if the path is <code>null</code> or empty.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/PathTokenizer.html" title="class in org.apache.tools.ant"><CODE>PathTokenizer</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.lang.String, java.lang.String)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.lang.String&nbsp;sourceFile,
                     java.lang.String&nbsp;destFile)
              throws java.io.IOException</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.4.x</I>
<P>
<DD>Convenience method to copy a file from a source to a destination.
 No filtering is performed.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - Name of file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - Name of file to copy to.
                 Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.lang.String, java.lang.String, boolean)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.lang.String&nbsp;sourceFile,
                     java.lang.String&nbsp;destFile,
                     boolean&nbsp;filtering)
              throws java.io.IOException</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.4.x</I>
<P>
<DD>Convenience method to copy a file from a source to a destination
 specifying if token filtering should be used.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - Name of file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - Name of file to copy to.
                 Must not be <code>null</code>.<DD><CODE>filtering</CODE> - Whether or not token filtering should be used during
                  the copy.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.lang.String, java.lang.String, boolean, boolean)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.lang.String&nbsp;sourceFile,
                     java.lang.String&nbsp;destFile,
                     boolean&nbsp;filtering,
                     boolean&nbsp;overwrite)
              throws java.io.IOException</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.4.x</I>
<P>
<DD>Convenience method to copy a file from a source to a
 destination specifying if token filtering should be used and if
 source files may overwrite newer destination files.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - Name of file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - Name of file to copy to.
                 Must not be <code>null</code>.<DD><CODE>filtering</CODE> - Whether or not token filtering should be used during
                  the copy.<DD><CODE>overwrite</CODE> - Whether or not the destination file should be
                  overwritten if it already exists.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.lang.String, java.lang.String, boolean, boolean, boolean)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.lang.String&nbsp;sourceFile,
                     java.lang.String&nbsp;destFile,
                     boolean&nbsp;filtering,
                     boolean&nbsp;overwrite,
                     boolean&nbsp;preserveLastModified)
              throws java.io.IOException</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.4.x</I>
<P>
<DD>Convenience method to copy a file from a source to a
 destination specifying if token filtering should be used, if
 source files may overwrite newer destination files, and if the
 last modified time of the resulting file should be set to
 that of the source file.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - Name of file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - Name of file to copy to.
                 Must not be <code>null</code>.<DD><CODE>filtering</CODE> - Whether or not token filtering should be used during
                  the copy.<DD><CODE>overwrite</CODE> - Whether or not the destination file should be
                  overwritten if it already exists.<DD><CODE>preserveLastModified</CODE> - Whether or not the last modified time of
                             the resulting file should be set to that
                             of the source file.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.io.File, java.io.File)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.io.File&nbsp;sourceFile,
                     java.io.File&nbsp;destFile)
              throws java.io.IOException</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.4.x</I>
<P>
<DD>Convenience method to copy a file from a source to a destination.
 No filtering is performed.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - File to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - File to copy to.
                 Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.io.File, java.io.File, boolean)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.io.File&nbsp;sourceFile,
                     java.io.File&nbsp;destFile,
                     boolean&nbsp;filtering)
              throws java.io.IOException</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.4.x</I>
<P>
<DD>Convenience method to copy a file from a source to a destination
 specifying if token filtering should be used.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - File to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - File to copy to.
                 Must not be <code>null</code>.<DD><CODE>filtering</CODE> - Whether or not token filtering should be used during
                  the copy.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.io.File, java.io.File, boolean, boolean)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.io.File&nbsp;sourceFile,
                     java.io.File&nbsp;destFile,
                     boolean&nbsp;filtering,
                     boolean&nbsp;overwrite)
              throws java.io.IOException</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.4.x</I>
<P>
<DD>Convenience method to copy a file from a source to a
 destination specifying if token filtering should be used and if
 source files may overwrite newer destination files.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - File to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - File to copy to.
                 Must not be <code>null</code>.<DD><CODE>filtering</CODE> - Whether or not token filtering should be used during
                  the copy.<DD><CODE>overwrite</CODE> - Whether or not the destination file should be
                  overwritten if it already exists.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the file cannot be copied.</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.io.File, java.io.File, boolean, boolean, boolean)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.io.File&nbsp;sourceFile,
                     java.io.File&nbsp;destFile,
                     boolean&nbsp;filtering,
                     boolean&nbsp;overwrite,
                     boolean&nbsp;preserveLastModified)
              throws java.io.IOException</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.4.x</I>
<P>
<DD>Convenience method to copy a file from a source to a
 destination specifying if token filtering should be used, if
 source files may overwrite newer destination files, and if the
 last modified time of the resulting file should be set to
 that of the source file.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - File to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - File to copy to.
                 Must not be <code>null</code>.<DD><CODE>filtering</CODE> - Whether or not token filtering should be used during
                  the copy.<DD><CODE>overwrite</CODE> - Whether or not the destination file should be
                  overwritten if it already exists.<DD><CODE>preserveLastModified</CODE> - Whether or not the last modified time of
                             the resulting file should be set to that
                             of the source file.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the file cannot be copied.</DL>
</DD>
</DL>
<HR>

<A NAME="setFileLastModified(java.io.File, long)"><!-- --></A><H3>
setFileLastModified</H3>
<PRE>
public void <B>setFileLastModified</B>(java.io.File&nbsp;file,
                                long&nbsp;time)
                         throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.4.x</I>
<P>
<DD>Call File.setLastModified(long time) on Java above 1.1, and logs
 a warning on Java 1.1.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>file</CODE> - The file to set the last modified time on.
             Must not be <code>null</code>.<DD><CODE>time</CODE> - the required modification time.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the last modified time cannot be set
                           despite running on a platform with a version
                           above 1.1.</DL>
</DD>
</DL>
<HR>

<A NAME="toBoolean(java.lang.String)"><!-- --></A><H3>
toBoolean</H3>
<PRE>
public static boolean <B>toBoolean</B>(java.lang.String&nbsp;s)</PRE>
<DL>
<DD>Return the boolean equivalent of a string, which is considered
 <code>true</code> if either <code>"on"</code>, <code>"true"</code>,
 or <code>"yes"</code> is found, ignoring case.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>s</CODE> - The string to convert to a boolean value.
<DT><B>Returns:</B><DD><code>true</code> if the given string is <code>"on"</code>,
         <code>"true"</code> or <code>"yes"</code>, or
         <code>false</code> otherwise.</DL>
</DD>
</DL>
<HR>

<A NAME="topoSort(java.lang.String, java.util.Hashtable)"><!-- --></A><H3>
topoSort</H3>
<PRE>
public final java.util.Vector <B>topoSort</B>(java.lang.String&nbsp;root,
                                       java.util.Hashtable&nbsp;targetTable)
                                throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Topologically sort a set of targets.  Equivalent to calling
 <code>topoSort(new String[] {root}, targets, true)</code>.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>root</CODE> - The name of the root target. The sort is created in such
             a way that the sequence of Targets up to the root
             target is the minimum possible such sequence.
             Must not be <code>null</code>.<DD><CODE>targetTable</CODE> - A Hashtable mapping names to Targets.
                Must not be <code>null</code>.
<DT><B>Returns:</B><DD>a Vector of ALL Target objects in sorted order.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if there is a cyclic dependency among the
                           targets, or if a named target does not exist.</DL>
</DD>
</DL>
<HR>

<A NAME="topoSort(java.lang.String, java.util.Hashtable, boolean)"><!-- --></A><H3>
topoSort</H3>
<PRE>
public final java.util.Vector <B>topoSort</B>(java.lang.String&nbsp;root,
                                       java.util.Hashtable&nbsp;targetTable,
                                       boolean&nbsp;returnAll)
                                throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Topologically sort a set of targets.  Equivalent to calling
 <code>topoSort(new String[] {root}, targets, returnAll)</code>.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>root</CODE> - The name of the root target. The sort is created in such
             a way that the sequence of Targets up to the root
             target is the minimum possible such sequence.
             Must not be <code>null</code>.<DD><CODE>targetTable</CODE> - A Hashtable mapping names to Targets.
                Must not be <code>null</code>.<DD><CODE>returnAll</CODE> - <code>boolean</code> indicating whether to return all
                  targets, or the execution sequence only.
<DT><B>Returns:</B><DD>a Vector of Target objects in sorted order.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if there is a cyclic dependency among the
                           targets, or if a named target does not exist.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="topoSort(java.lang.String[], java.util.Hashtable, boolean)"><!-- --></A><H3>
topoSort</H3>
<PRE>
public final java.util.Vector <B>topoSort</B>(java.lang.String[]&nbsp;root,
                                       java.util.Hashtable&nbsp;targetTable,
                                       boolean&nbsp;returnAll)
                                throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Topologically sort a set of targets.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>root</CODE> - <code>String[]</code> containing the names of the root targets.
             The sort is created in such a way that the ordered sequence of
             Targets is the minimum possible such sequence to the specified
             root targets.
             Must not be <code>null</code>.<DD><CODE>targetTable</CODE> - A map of names to targets (String to Target).
                Must not be <code>null</code>.<DD><CODE>returnAll</CODE> - <code>boolean</code> indicating whether to return all
                  targets, or the execution sequence only.
<DT><B>Returns:</B><DD>a Vector of Target objects in sorted order.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if there is a cyclic dependency among the
                           targets, or if a named target does not exist.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="inheritIDReferences(org.apache.tools.ant.Project)"><!-- --></A><H3>
inheritIDReferences</H3>
<PRE>
public void <B>inheritIDReferences</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;parent)</PRE>
<DL>
<DD>Inherit the id references.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>parent</CODE> - the parent project of this project.</DL>
</DD>
</DL>
<HR>

<A NAME="addIdReference(java.lang.String, java.lang.Object)"><!-- --></A><H3>
addIdReference</H3>
<PRE>
public void <B>addIdReference</B>(java.lang.String&nbsp;id,
                           java.lang.Object&nbsp;value)</PRE>
<DL>
<DD>Add an id reference.
 Used for broken build files.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>id</CODE> - the id to set.<DD><CODE>value</CODE> - the value to set it to (Unknown element in this case.</DL>
</DD>
</DL>
<HR>

<A NAME="addReference(java.lang.String, java.lang.Object)"><!-- --></A><H3>
addReference</H3>
<PRE>
public void <B>addReference</B>(java.lang.String&nbsp;referenceName,
                         java.lang.Object&nbsp;value)</PRE>
<DL>
<DD>Add a reference to the project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>referenceName</CODE> - The name of the reference. Must not be <code>null</code>.<DD><CODE>value</CODE> - The value of the reference.</DL>
</DD>
</DL>
<HR>

<A NAME="getReferences()"><!-- --></A><H3>
getReferences</H3>
<PRE>
public java.util.Hashtable <B>getReferences</B>()</PRE>
<DL>
<DD>Return a map of the references in the project (String to Object).
 The returned hashtable is &quot;live&quot; and so must not be modified.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a map of the references in the project (String to Object).</DL>
</DD>
</DL>
<HR>

<A NAME="getReference(java.lang.String)"><!-- --></A><H3>
getReference</H3>
<PRE>
public java.lang.Object <B>getReference</B>(java.lang.String&nbsp;key)</PRE>
<DL>
<DD>Look up a reference by its key (ID).
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>key</CODE> - The key for the desired reference.
            Must not be <code>null</code>.
<DT><B>Returns:</B><DD>the reference with the specified ID, or <code>null</code> if
         there is no such reference in the project.</DL>
</DD>
</DL>
<HR>

<A NAME="getElementName(java.lang.Object)"><!-- --></A><H3>
getElementName</H3>
<PRE>
public java.lang.String <B>getElementName</B>(java.lang.Object&nbsp;element)</PRE>
<DL>
<DD>Return a description of the type of the given element, with
 special handling for instances of tasks and data types.
 <p>
 This is useful for logging purposes.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>element</CODE> - The element to describe.
                Must not be <code>null</code>.
<DT><B>Returns:</B><DD>a description of the element type.<DT><B>Since:</B></DT>
  <DD>1.95, Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="fireBuildStarted()"><!-- --></A><H3>
fireBuildStarted</H3>
<PRE>
public void <B>fireBuildStarted</B>()</PRE>
<DL>
<DD>Send a &quot;build started&quot; event
 to the build listeners for this project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="fireBuildFinished(java.lang.Throwable)"><!-- --></A><H3>
fireBuildFinished</H3>
<PRE>
public void <B>fireBuildFinished</B>(java.lang.Throwable&nbsp;exception)</PRE>
<DL>
<DD>Send a &quot;build finished&quot; event to the build listeners
 for this project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>exception</CODE> - an exception indicating a reason for a build
                  failure. May be <code>null</code>, indicating
                  a successful build.</DL>
</DD>
</DL>
<HR>

<A NAME="fireSubBuildStarted()"><!-- --></A><H3>
fireSubBuildStarted</H3>
<PRE>
public void <B>fireSubBuildStarted</B>()</PRE>
<DL>
<DD>Send a &quot;subbuild started&quot; event to the build listeners for
 this project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.6.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="fireSubBuildFinished(java.lang.Throwable)"><!-- --></A><H3>
fireSubBuildFinished</H3>
<PRE>
public void <B>fireSubBuildFinished</B>(java.lang.Throwable&nbsp;exception)</PRE>
<DL>
<DD>Send a &quot;subbuild finished&quot; event to the build listeners for
 this project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>exception</CODE> - an exception indicating a reason for a build
                  failure. May be <code>null</code>, indicating
                  a successful build.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="fireTargetStarted(org.apache.tools.ant.Target)"><!-- --></A><H3>
fireTargetStarted</H3>
<PRE>
protected void <B>fireTargetStarted</B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target)</PRE>
<DL>
<DD>Send a &quot;target started&quot; event to the build listeners
 for this project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>target</CODE> - The target which is starting to build.
               Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="fireTargetFinished(org.apache.tools.ant.Target, java.lang.Throwable)"><!-- --></A><H3>
fireTargetFinished</H3>
<PRE>
protected void <B>fireTargetFinished</B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target,
                                  java.lang.Throwable&nbsp;exception)</PRE>
<DL>
<DD>Send a &quot;target finished&quot; event to the build listeners
 for this project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>target</CODE> - The target which has finished building.
                  Must not be <code>null</code>.<DD><CODE>exception</CODE> - an exception indicating a reason for a build
                  failure. May be <code>null</code>, indicating
                  a successful build.</DL>
</DD>
</DL>
<HR>

<A NAME="fireTaskStarted(org.apache.tools.ant.Task)"><!-- --></A><H3>
fireTaskStarted</H3>
<PRE>
protected void <B>fireTaskStarted</B>(<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task)</PRE>
<DL>
<DD>Send a &quot;task started&quot; event to the build listeners
 for this project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>task</CODE> - The target which is starting to execute.
               Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="fireTaskFinished(org.apache.tools.ant.Task, java.lang.Throwable)"><!-- --></A><H3>
fireTaskFinished</H3>
<PRE>
protected void <B>fireTaskFinished</B>(<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task,
                                java.lang.Throwable&nbsp;exception)</PRE>
<DL>
<DD>Send a &quot;task finished&quot; event to the build listeners for this
 project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>task</CODE> - The task which has finished executing.
                  Must not be <code>null</code>.<DD><CODE>exception</CODE> - an exception indicating a reason for a build
                  failure. May be <code>null</code>, indicating
                  a successful build.</DL>
</DD>
</DL>
<HR>

<A NAME="fireMessageLogged(org.apache.tools.ant.Project, java.lang.String, int)"><!-- --></A><H3>
fireMessageLogged</H3>
<PRE>
protected void <B>fireMessageLogged</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                                 java.lang.String&nbsp;message,
                                 int&nbsp;priority)</PRE>
<DL>
<DD>Send a &quot;message logged&quot; project level event
 to the build listeners for this project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - The project generating the event.
                 Should not be <code>null</code>.<DD><CODE>message</CODE> - The message to send. Should not be <code>null</code>.<DD><CODE>priority</CODE> - The priority of the message.</DL>
</DD>
</DL>
<HR>

<A NAME="fireMessageLogged(org.apache.tools.ant.Project, java.lang.String, java.lang.Throwable, int)"><!-- --></A><H3>
fireMessageLogged</H3>
<PRE>
protected void <B>fireMessageLogged</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                                 java.lang.String&nbsp;message,
                                 java.lang.Throwable&nbsp;throwable,
                                 int&nbsp;priority)</PRE>
<DL>
<DD>Send a &quot;message logged&quot; project level event
 to the build listeners for this project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - The project generating the event.
                 Should not be <code>null</code>.<DD><CODE>message</CODE> - The message to send. Should not be <code>null</code>.<DD><CODE>throwable</CODE> - The exception that caused this message. May be <code>null</code>.<DD><CODE>priority</CODE> - The priority of the message.<DT><B>Since:</B></DT>
  <DD>1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="fireMessageLogged(org.apache.tools.ant.Target, java.lang.String, int)"><!-- --></A><H3>
fireMessageLogged</H3>
<PRE>
protected void <B>fireMessageLogged</B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target,
                                 java.lang.String&nbsp;message,
                                 int&nbsp;priority)</PRE>
<DL>
<DD>Send a &quot;message logged&quot; target level event
 to the build listeners for this project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>target</CODE> - The target generating the event.
                 Must not be <code>null</code>.<DD><CODE>message</CODE> - The message to send. Should not be <code>null</code>.<DD><CODE>priority</CODE> - The priority of the message.</DL>
</DD>
</DL>
<HR>

<A NAME="fireMessageLogged(org.apache.tools.ant.Target, java.lang.String, java.lang.Throwable, int)"><!-- --></A><H3>
fireMessageLogged</H3>
<PRE>
protected void <B>fireMessageLogged</B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target,
                                 java.lang.String&nbsp;message,
                                 java.lang.Throwable&nbsp;throwable,
                                 int&nbsp;priority)</PRE>
<DL>
<DD>Send a &quot;message logged&quot; target level event
 to the build listeners for this project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>target</CODE> - The target generating the event.
                 Must not be <code>null</code>.<DD><CODE>message</CODE> - The message to send. Should not be <code>null</code>.<DD><CODE>throwable</CODE> - The exception that caused this message. May be <code>null</code>.<DD><CODE>priority</CODE> - The priority of the message.<DT><B>Since:</B></DT>
  <DD>1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="fireMessageLogged(org.apache.tools.ant.Task, java.lang.String, int)"><!-- --></A><H3>
fireMessageLogged</H3>
<PRE>
protected void <B>fireMessageLogged</B>(<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task,
                                 java.lang.String&nbsp;message,
                                 int&nbsp;priority)</PRE>
<DL>
<DD>Send a &quot;message logged&quot; task level event
 to the build listeners for this project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>task</CODE> - The task generating the event.
                 Must not be <code>null</code>.<DD><CODE>message</CODE> - The message to send. Should not be <code>null</code>.<DD><CODE>priority</CODE> - The priority of the message.</DL>
</DD>
</DL>
<HR>

<A NAME="fireMessageLogged(org.apache.tools.ant.Task, java.lang.String, java.lang.Throwable, int)"><!-- --></A><H3>
fireMessageLogged</H3>
<PRE>
protected void <B>fireMessageLogged</B>(<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task,
                                 java.lang.String&nbsp;message,
                                 java.lang.Throwable&nbsp;throwable,
                                 int&nbsp;priority)</PRE>
<DL>
<DD>Send a &quot;message logged&quot; task level event
 to the build listeners for this project.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>task</CODE> - The task generating the event.
                 Must not be <code>null</code>.<DD><CODE>message</CODE> - The message to send. Should not be <code>null</code>.<DD><CODE>throwable</CODE> - The exception that caused this message. May be <code>null</code>.<DD><CODE>priority</CODE> - The priority of the message.<DT><B>Since:</B></DT>
  <DD>1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="registerThreadTask(java.lang.Thread, org.apache.tools.ant.Task)"><!-- --></A><H3>
registerThreadTask</H3>
<PRE>
public void <B>registerThreadTask</B>(java.lang.Thread&nbsp;thread,
                               <A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task)</PRE>
<DL>
<DD>Register a task as the current task for a thread.
 If the task is null, the thread's entry is removed.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>thread</CODE> - the thread on which the task is registered.<DD><CODE>task</CODE> - the task to be registered.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getThreadTask(java.lang.Thread)"><!-- --></A><H3>
getThreadTask</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A> <B>getThreadTask</B>(java.lang.Thread&nbsp;thread)</PRE>
<DL>
<DD>Get the current task associated with a thread, if any.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>thread</CODE> - the thread for which the task is required.
<DT><B>Returns:</B><DD>the task which is currently registered for the given thread or
         null if no task is registered.</DL>
</DD>
</DL>
<HR>

<A NAME="setProjectReference(java.lang.Object)"><!-- --></A><H3>
setProjectReference</H3>
<PRE>
public final void <B>setProjectReference</B>(java.lang.Object&nbsp;obj)</PRE>
<DL>
<DD>Set a reference to this Project on the parameterized object.
 Need to set the project before other set/add elements
 are called.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>obj</CODE> - the object to invoke setProject(this) on.</DL>
</DD>
</DL>
<HR>

<A NAME="getResource(java.lang.String)"><!-- --></A><H3>
getResource</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A> <B>getResource</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Resolve the file relative to the project's basedir and return it as a
 FileResource.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/types/ResourceFactory.html#getResource(java.lang.String)">getResource</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the name of the file to resolve.
<DT><B>Returns:</B><DD>the file resource.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/PathTokenizer.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/Project.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Project.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
