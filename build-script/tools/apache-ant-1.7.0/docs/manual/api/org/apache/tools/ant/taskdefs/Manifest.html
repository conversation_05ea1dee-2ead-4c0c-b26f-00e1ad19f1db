<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:23 EST 2006 -->
<TITLE>
Manifest (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.Manifest class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Manifest (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/MakeUrl.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Manifest.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Manifest.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class Manifest</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.Manifest</B>
</PRE>
<HR>
<DL>
<DT><PRE>public class <B>Manifest</B><DT>extends java.lang.Object</DL>
</PRE>

<P>
Holds the data of a jar manifest.

 Manifests are processed according to the
 <a href="http://java.sun.com/j2se/1.5.0/docs/guide/jar/jar.html">Jar
 file specification.</a>.
 Specifically, a manifest element consists of
 a set of attributes and sections. These sections in turn may contain
 attributes. Note in particular that this may result in manifest lines
 greater than 72 bytes being wrapped and continued on the next
 line. If an application can not handle the continuation mechanism, it
 is a defect in the application, not this task.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.4</DD>
</DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Attribute</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;An attribute for the manifest.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A manifest section - you can nest attribute elements into sections.</TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#ATTRIBUTE_CLASSPATH">ATTRIBUTE_CLASSPATH</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The Class-Path Header is special - it can be duplicated</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#ATTRIBUTE_FROM">ATTRIBUTE_FROM</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The From Header is disallowed in a Manifest</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#ATTRIBUTE_MANIFEST_VERSION">ATTRIBUTE_MANIFEST_VERSION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The standard manifest version header</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#ATTRIBUTE_NAME">ATTRIBUTE_NAME</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The Name Attribute is the first in a named section</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#ATTRIBUTE_SIGNATURE_VERSION">ATTRIBUTE_SIGNATURE_VERSION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The standard Signature Version header</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#DEFAULT_MANIFEST_VERSION">DEFAULT_MANIFEST_VERSION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Default Manifest version if one is not specified</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#EOL">EOL</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The End-Of-Line marker in manifests</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#ERROR_FROM_FORBIDDEN">ERROR_FROM_FORBIDDEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Error for attributes</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#JAR_ENCODING">JAR_ENCODING</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Encoding to be used for JAR files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#MAX_LINE_LENGTH">MAX_LINE_LENGTH</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The max length of a line in a Manifest</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#MAX_SECTION_LENGTH">MAX_SECTION_LENGTH</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Max length of a line section which is continued.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#Manifest()">Manifest</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Construct an empty manifest</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#Manifest(java.io.Reader)">Manifest</A></B>(java.io.Reader&nbsp;r)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Read a manifest file from the given reader</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#addConfiguredAttribute(org.apache.tools.ant.taskdefs.Manifest.Attribute)">addConfiguredAttribute</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Attribute</A>&nbsp;attribute)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add an attribute to the manifest - it is added to the main section.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#addConfiguredSection(org.apache.tools.ant.taskdefs.Manifest.Section)">addConfiguredSection</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</A>&nbsp;section)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a section to the manifest</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#equals(java.lang.Object)">equals</A></B>(java.lang.Object&nbsp;rhs)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#getDefaultManifest()">getDefaultManifest</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Construct a manifest from Ant's default manifest file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#getMainSection()">getMainSection</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the main section of the manifest</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#getManifestVersion()">getManifestVersion</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the version of the manifest</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#getSection(java.lang.String)">getSection</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get a particular section from the manifest</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Enumeration</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#getSectionNames()">getSectionNames</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the section names in this manifest.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Enumeration</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#getWarnings()">getWarnings</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the warnings for this manifest.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#hashCode()">hashCode</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#merge(org.apache.tools.ant.taskdefs.Manifest)">merge</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</A>&nbsp;other)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Merge the contents of the given manifest into this manifest</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#merge(org.apache.tools.ant.taskdefs.Manifest, boolean)">merge</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</A>&nbsp;other,
      boolean&nbsp;overwriteMain)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Merge the contents of the given manifest into this manifest</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#toString()">toString</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convert the manifest to its string representation</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html#write(java.io.PrintWriter)">write</A></B>(java.io.PrintWriter&nbsp;writer)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Write the manifest out to a print writer.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ATTRIBUTE_MANIFEST_VERSION"><!-- --></A><H3>
ATTRIBUTE_MANIFEST_VERSION</H3>
<PRE>
public static final java.lang.String <B>ATTRIBUTE_MANIFEST_VERSION</B></PRE>
<DL>
<DD>The standard manifest version header
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.ATTRIBUTE_MANIFEST_VERSION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ATTRIBUTE_SIGNATURE_VERSION"><!-- --></A><H3>
ATTRIBUTE_SIGNATURE_VERSION</H3>
<PRE>
public static final java.lang.String <B>ATTRIBUTE_SIGNATURE_VERSION</B></PRE>
<DL>
<DD>The standard Signature Version header
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.ATTRIBUTE_SIGNATURE_VERSION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ATTRIBUTE_NAME"><!-- --></A><H3>
ATTRIBUTE_NAME</H3>
<PRE>
public static final java.lang.String <B>ATTRIBUTE_NAME</B></PRE>
<DL>
<DD>The Name Attribute is the first in a named section
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.ATTRIBUTE_NAME">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ATTRIBUTE_FROM"><!-- --></A><H3>
ATTRIBUTE_FROM</H3>
<PRE>
public static final java.lang.String <B>ATTRIBUTE_FROM</B></PRE>
<DL>
<DD>The From Header is disallowed in a Manifest
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.ATTRIBUTE_FROM">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ATTRIBUTE_CLASSPATH"><!-- --></A><H3>
ATTRIBUTE_CLASSPATH</H3>
<PRE>
public static final java.lang.String <B>ATTRIBUTE_CLASSPATH</B></PRE>
<DL>
<DD>The Class-Path Header is special - it can be duplicated
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.ATTRIBUTE_CLASSPATH">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_MANIFEST_VERSION"><!-- --></A><H3>
DEFAULT_MANIFEST_VERSION</H3>
<PRE>
public static final java.lang.String <B>DEFAULT_MANIFEST_VERSION</B></PRE>
<DL>
<DD>Default Manifest version if one is not specified
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.DEFAULT_MANIFEST_VERSION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="MAX_LINE_LENGTH"><!-- --></A><H3>
MAX_LINE_LENGTH</H3>
<PRE>
public static final int <B>MAX_LINE_LENGTH</B></PRE>
<DL>
<DD>The max length of a line in a Manifest
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.MAX_LINE_LENGTH">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="MAX_SECTION_LENGTH"><!-- --></A><H3>
MAX_SECTION_LENGTH</H3>
<PRE>
public static final int <B>MAX_SECTION_LENGTH</B></PRE>
<DL>
<DD>Max length of a line section which is continued. Need to allow
 for the CRLF.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.MAX_SECTION_LENGTH">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="EOL"><!-- --></A><H3>
EOL</H3>
<PRE>
public static final java.lang.String <B>EOL</B></PRE>
<DL>
<DD>The End-Of-Line marker in manifests
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.EOL">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_FROM_FORBIDDEN"><!-- --></A><H3>
ERROR_FROM_FORBIDDEN</H3>
<PRE>
public static final java.lang.String <B>ERROR_FROM_FORBIDDEN</B></PRE>
<DL>
<DD>Error for attributes
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.ERROR_FROM_FORBIDDEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="JAR_ENCODING"><!-- --></A><H3>
JAR_ENCODING</H3>
<PRE>
public static final java.lang.String <B>JAR_ENCODING</B></PRE>
<DL>
<DD>Encoding to be used for JAR files.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Manifest.JAR_ENCODING">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Manifest()"><!-- --></A><H3>
Manifest</H3>
<PRE>
public <B>Manifest</B>()</PRE>
<DL>
<DD>Construct an empty manifest
<P>
</DL>
<HR>

<A NAME="Manifest(java.io.Reader)"><!-- --></A><H3>
Manifest</H3>
<PRE>
public <B>Manifest</B>(java.io.Reader&nbsp;r)
         throws <A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A>,
                java.io.IOException</PRE>
<DL>
<DD>Read a manifest file from the given reader
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>r</CODE> - is the reader from which the Manifest is read
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></CODE> - if the manifest is not valid according
         to the JAR spec
<DD><CODE>java.io.IOException</CODE> - if the manifest cannot be read from the reader.</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="getDefaultManifest()"><!-- --></A><H3>
getDefaultManifest</H3>
<PRE>
public static <A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</A> <B>getDefaultManifest</B>()
                                   throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Construct a manifest from Ant's default manifest file.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the default manifest.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if there is a problem loading the
            default manifest</DL>
</DD>
</DL>
<HR>

<A NAME="addConfiguredSection(org.apache.tools.ant.taskdefs.Manifest.Section)"><!-- --></A><H3>
addConfiguredSection</H3>
<PRE>
public void <B>addConfiguredSection</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</A>&nbsp;section)
                          throws <A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></PRE>
<DL>
<DD>Add a section to the manifest
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>section</CODE> - the manifest section to be added
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></CODE> - if the secti0on is not valid.</DL>
</DD>
</DL>
<HR>

<A NAME="addConfiguredAttribute(org.apache.tools.ant.taskdefs.Manifest.Attribute)"><!-- --></A><H3>
addConfiguredAttribute</H3>
<PRE>
public void <B>addConfiguredAttribute</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Attribute</A>&nbsp;attribute)
                            throws <A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></PRE>
<DL>
<DD>Add an attribute to the manifest - it is added to the main section.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>attribute</CODE> - the attribute to be added.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></CODE> - if the attribute is not valid.</DL>
</DD>
</DL>
<HR>

<A NAME="merge(org.apache.tools.ant.taskdefs.Manifest)"><!-- --></A><H3>
merge</H3>
<PRE>
public void <B>merge</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</A>&nbsp;other)
           throws <A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></PRE>
<DL>
<DD>Merge the contents of the given manifest into this manifest
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>other</CODE> - the Manifest to be merged with this one.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></CODE> - if there is a problem merging the
         manifest according to the Manifest spec.</DL>
</DD>
</DL>
<HR>

<A NAME="merge(org.apache.tools.ant.taskdefs.Manifest, boolean)"><!-- --></A><H3>
merge</H3>
<PRE>
public void <B>merge</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</A>&nbsp;other,
                  boolean&nbsp;overwriteMain)
           throws <A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></PRE>
<DL>
<DD>Merge the contents of the given manifest into this manifest
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>other</CODE> - the Manifest to be merged with this one.<DD><CODE>overwriteMain</CODE> - whether to overwrite the main section
        of the current manifest
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></CODE> - if there is a problem merging the
         manifest according to the Manifest spec.</DL>
</DD>
</DL>
<HR>

<A NAME="write(java.io.PrintWriter)"><!-- --></A><H3>
write</H3>
<PRE>
public void <B>write</B>(java.io.PrintWriter&nbsp;writer)
           throws java.io.IOException</PRE>
<DL>
<DD>Write the manifest out to a print writer.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>writer</CODE> - the Writer to which the manifest is written
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the manifest cannot be written</DL>
</DD>
</DL>
<HR>

<A NAME="toString()"><!-- --></A><H3>
toString</H3>
<PRE>
public java.lang.String <B>toString</B>()</PRE>
<DL>
<DD>Convert the manifest to its string representation
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>toString</CODE> in class <CODE>java.lang.Object</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a multiline string with the Manifest as it
         appears in a Manifest file.</DL>
</DD>
</DL>
<HR>

<A NAME="getWarnings()"><!-- --></A><H3>
getWarnings</H3>
<PRE>
public java.util.Enumeration <B>getWarnings</B>()</PRE>
<DL>
<DD>Get the warnings for this manifest.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>an enumeration of warning strings</DL>
</DD>
</DL>
<HR>

<A NAME="hashCode()"><!-- --></A><H3>
hashCode</H3>
<PRE>
public int <B>hashCode</B>()</PRE>
<DL>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>hashCode</CODE> in class <CODE>java.lang.Object</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a hashcode based on the version, main and sections.<DT><B>See Also:</B><DD><CODE>Object.hashCode()</CODE></DL>
</DD>
</DL>
<HR>

<A NAME="equals(java.lang.Object)"><!-- --></A><H3>
equals</H3>
<PRE>
public boolean <B>equals</B>(java.lang.Object&nbsp;rhs)</PRE>
<DL>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>equals</CODE> in class <CODE>java.lang.Object</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rhs</CODE> - the object to check for equality.
<DT><B>Returns:</B><DD>true if the version, main and sections are the same.<DT><B>See Also:</B><DD><CODE>Object.equals(java.lang.Object)</CODE></DL>
</DD>
</DL>
<HR>

<A NAME="getManifestVersion()"><!-- --></A><H3>
getManifestVersion</H3>
<PRE>
public java.lang.String <B>getManifestVersion</B>()</PRE>
<DL>
<DD>Get the version of the manifest
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the manifest's version string</DL>
</DD>
</DL>
<HR>

<A NAME="getMainSection()"><!-- --></A><H3>
getMainSection</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</A> <B>getMainSection</B>()</PRE>
<DL>
<DD>Get the main section of the manifest
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the main section of the manifest</DL>
</DD>
</DL>
<HR>

<A NAME="getSection(java.lang.String)"><!-- --></A><H3>
getSection</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</A> <B>getSection</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Get a particular section from the manifest
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the name of the section desired.
<DT><B>Returns:</B><DD>the specified section or null if that section
 does not exist in the manifest</DL>
</DD>
</DL>
<HR>

<A NAME="getSectionNames()"><!-- --></A><H3>
getSectionNames</H3>
<PRE>
public java.util.Enumeration <B>getSectionNames</B>()</PRE>
<DL>
<DD>Get the section names in this manifest.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>an Enumeration of section names</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/MakeUrl.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Manifest.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Manifest.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
