<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:34 EST 2006 -->
<TITLE>
RegexpPatternMapper (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.util.RegexpPatternMapper class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="RegexpPatternMapper (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/util/ReflectWrapper.html" title="class in org.apache.tools.ant.util"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/util/ResourceUtils.html" title="class in org.apache.tools.ant.util"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/util/RegexpPatternMapper.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="RegexpPatternMapper.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.util</FONT>
<BR>
Class RegexpPatternMapper</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.util.RegexpPatternMapper</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD><A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>RegexpPatternMapper</B><DT>extends java.lang.Object<DT>implements <A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A></DL>
</PRE>

<P>
Implementation of FileNameMapper that does regular expression
 replacements.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/util/regexp/RegexpMatcher.html" title="interface in org.apache.tools.ant.util.regexp">RegexpMatcher</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/RegexpPatternMapper.html#reg">reg</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.StringBuffer</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/RegexpPatternMapper.html#result">result</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;char[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/RegexpPatternMapper.html#to">to</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/RegexpPatternMapper.html#RegexpPatternMapper()">RegexpPatternMapper</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for RegexpPatternMapper.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/RegexpPatternMapper.html#mapFileName(java.lang.String)">mapFileName</A></B>(java.lang.String&nbsp;sourceFileName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns null if the source file name doesn't match the
 &quot;from&quot; pattern, an one-element array containing the
 translated file otherwise.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/RegexpPatternMapper.html#replaceReferences(java.lang.String)">replaceReferences</A></B>(java.lang.String&nbsp;source)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Replace all backreferences in the to pattern with the matched
 groups of the source.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/RegexpPatternMapper.html#setCaseSensitive(boolean)">setCaseSensitive</A></B>(boolean&nbsp;caseSensitive)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Attribute specifing whether to ignore the case difference
 in the names.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/RegexpPatternMapper.html#setFrom(java.lang.String)">setFrom</A></B>(java.lang.String&nbsp;from)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the &quot;from&quot; pattern.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/RegexpPatternMapper.html#setHandleDirSep(boolean)">setHandleDirSep</A></B>(boolean&nbsp;handleDirSep)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Attribute specifing whether to ignore the difference
 between / and \ (the two common directory characters).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/RegexpPatternMapper.html#setTo(java.lang.String)">setTo</A></B>(java.lang.String&nbsp;to)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the &quot;to&quot; pattern.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="reg"><!-- --></A><H3>
reg</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/util/regexp/RegexpMatcher.html" title="interface in org.apache.tools.ant.util.regexp">RegexpMatcher</A> <B>reg</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="to"><!-- --></A><H3>
to</H3>
<PRE>
protected char[] <B>to</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="result"><!-- --></A><H3>
result</H3>
<PRE>
protected java.lang.StringBuffer <B>result</B></PRE>
<DL>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="RegexpPatternMapper()"><!-- --></A><H3>
RegexpPatternMapper</H3>
<PRE>
public <B>RegexpPatternMapper</B>()
                    throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Constructor for RegexpPatternMapper.
<P>
<DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error.</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setHandleDirSep(boolean)"><!-- --></A><H3>
setHandleDirSep</H3>
<PRE>
public void <B>setHandleDirSep</B>(boolean&nbsp;handleDirSep)</PRE>
<DL>
<DD>Attribute specifing whether to ignore the difference
 between / and \ (the two common directory characters).
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>handleDirSep</CODE> - a boolean, default is false.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setCaseSensitive(boolean)"><!-- --></A><H3>
setCaseSensitive</H3>
<PRE>
public void <B>setCaseSensitive</B>(boolean&nbsp;caseSensitive)</PRE>
<DL>
<DD>Attribute specifing whether to ignore the case difference
 in the names.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>caseSensitive</CODE> - a boolean, default is false.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setFrom(java.lang.String)"><!-- --></A><H3>
setFrom</H3>
<PRE>
public void <B>setFrom</B>(java.lang.String&nbsp;from)
             throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Sets the &quot;from&quot; pattern. Required.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html#setFrom(java.lang.String)">setFrom</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>from</CODE> - the from pattern.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error.</DL>
</DD>
</DL>
<HR>

<A NAME="setTo(java.lang.String)"><!-- --></A><H3>
setTo</H3>
<PRE>
public void <B>setTo</B>(java.lang.String&nbsp;to)</PRE>
<DL>
<DD>Sets the &quot;to&quot; pattern. Required.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html#setTo(java.lang.String)">setTo</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>to</CODE> - the to pattern.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error.</DL>
</DD>
</DL>
<HR>

<A NAME="mapFileName(java.lang.String)"><!-- --></A><H3>
mapFileName</H3>
<PRE>
public java.lang.String[] <B>mapFileName</B>(java.lang.String&nbsp;sourceFileName)</PRE>
<DL>
<DD>Returns null if the source file name doesn't match the
 &quot;from&quot; pattern, an one-element array containing the
 translated file otherwise.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html#mapFileName(java.lang.String)">mapFileName</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFileName</CODE> - the source file name
<DT><B>Returns:</B><DD>a one-element array containing the translated file or
         null if the to pattern did not match</DL>
</DD>
</DL>
<HR>

<A NAME="replaceReferences(java.lang.String)"><!-- --></A><H3>
replaceReferences</H3>
<PRE>
protected java.lang.String <B>replaceReferences</B>(java.lang.String&nbsp;source)</PRE>
<DL>
<DD>Replace all backreferences in the to pattern with the matched
 groups of the source.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>source</CODE> - the source file name.
<DT><B>Returns:</B><DD>the translated file name.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/util/ReflectWrapper.html" title="class in org.apache.tools.ant.util"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/util/ResourceUtils.html" title="class in org.apache.tools.ant.util"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/util/RegexpPatternMapper.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="RegexpPatternMapper.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
