<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:24 EST 2006 -->
<TITLE>
Sync.MyCopy (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.Sync.MyCopy class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Sync.MyCopy (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.SyncTarget.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Sync.MyCopy.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Sync.MyCopy.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.Copy">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class Sync.MyCopy</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Copy</A>
              <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.Sync.MyCopy</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<DL>
<DT><B>Enclosing class:</B><DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.html" title="class in org.apache.tools.ant.taskdefs">Sync</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public static class <B>Sync.MyCopy</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</A></DL>
</PRE>

<P>
Subclass Copy in order to access it's file/dir maps.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.Copy"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#completeDirMap">completeDirMap</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#destDir">destDir</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#destFile">destFile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#dirCopyMap">dirCopyMap</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#failonerror">failonerror</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#file">file</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#fileCopyMap">fileCopyMap</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#fileUtils">fileUtils</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#filtering">filtering</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#flatten">flatten</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#forceOverwrite">forceOverwrite</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#includeEmpty">includeEmpty</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#mapperElement">mapperElement</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#preserveLastModified">preserveLastModified</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#rcs">rcs</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#verbosity">verbosity</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.MyCopy.html#Sync.MyCopy()">Sync.MyCopy</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for MyCopy.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.MyCopy.html#getIncludeEmptyDirs()">getIncludeEmptyDirs</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the includeEmptyDirs attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.MyCopy.html#getToDir()">getToDir</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the destination directory.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.MyCopy.html#scan(java.io.File, java.io.File, java.lang.String[], java.lang.String[])">scan</A></B>(java.io.File&nbsp;fromDir,
     java.io.File&nbsp;toDir,
     java.lang.String[]&nbsp;files,
     java.lang.String[]&nbsp;dirs)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Compares source files to destination files to see if they should be
 copied.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Map</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.MyCopy.html#scan(org.apache.tools.ant.types.Resource[], java.io.File)">scan</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>[]&nbsp;resources,
     java.io.File&nbsp;toDir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Compares source resources to destination files to see if they
 should be copied.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.MyCopy.html#supportsNonFileResources()">supportsNonFileResources</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Yes, we can.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.Copy"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#add(org.apache.tools.ant.util.FileNameMapper)">add</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#add(org.apache.tools.ant.types.ResourceCollection)">add</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#buildMap(java.io.File, java.io.File, java.lang.String[], org.apache.tools.ant.util.FileNameMapper, java.util.Hashtable)">buildMap</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#buildMap(org.apache.tools.ant.types.Resource[], java.io.File, org.apache.tools.ant.util.FileNameMapper)">buildMap</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#createFilterChain()">createFilterChain</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#createFilterSet()">createFilterSet</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#createMapper()">createMapper</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#doFileOperations()">doFileOperations</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#doResourceOperations(java.util.Map)">doResourceOperations</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#execute()">execute</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#getEncoding()">getEncoding</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#getFileUtils()">getFileUtils</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#getFilterChains()">getFilterChains</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#getFilterSets()">getFilterSets</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#getOutputEncoding()">getOutputEncoding</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#getPreserveLastModified()">getPreserveLastModified</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#isEnableMultipleMapping()">isEnableMultipleMapping</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#setEnableMultipleMappings(boolean)">setEnableMultipleMappings</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#setEncoding(java.lang.String)">setEncoding</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#setFailOnError(boolean)">setFailOnError</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#setFile(java.io.File)">setFile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#setFiltering(boolean)">setFiltering</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#setFlatten(boolean)">setFlatten</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#setGranularity(long)">setGranularity</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#setIncludeEmptyDirs(boolean)">setIncludeEmptyDirs</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#setOutputEncoding(java.lang.String)">setOutputEncoding</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#setOverwrite(boolean)">setOverwrite</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#setPreserveLastModified(boolean)">setPreserveLastModified</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#setPreserveLastModified(java.lang.String)">setPreserveLastModified</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#setTodir(java.io.File)">setTodir</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#setTofile(java.io.File)">setTofile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#setVerbose(boolean)">setVerbose</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#validateAttributes()">validateAttributes</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Sync.MyCopy()"><!-- --></A><H3>
Sync.MyCopy</H3>
<PRE>
public <B>Sync.MyCopy</B>()</PRE>
<DL>
<DD>Constructor for MyCopy.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="scan(java.io.File, java.io.File, java.lang.String[], java.lang.String[])"><!-- --></A><H3>
scan</H3>
<PRE>
protected void <B>scan</B>(java.io.File&nbsp;fromDir,
                    java.io.File&nbsp;toDir,
                    java.lang.String[]&nbsp;files,
                    java.lang.String[]&nbsp;dirs)</PRE>
<DL>
<DD>Compares source files to destination files to see if they should be
 copied.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#scan(java.io.File, java.io.File, java.lang.String[], java.lang.String[])">scan</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fromDir</CODE> - The source directory.<DD><CODE>toDir</CODE> - The destination directory.<DD><CODE>files</CODE> - A list of files to copy.<DD><CODE>dirs</CODE> - A list of directories to copy.</DL>
</DD>
</DL>
<HR>

<A NAME="scan(org.apache.tools.ant.types.Resource[], java.io.File)"><!-- --></A><H3>
scan</H3>
<PRE>
protected java.util.Map <B>scan</B>(<A HREF="../../../../../org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A>[]&nbsp;resources,
                             java.io.File&nbsp;toDir)</PRE>
<DL>
<DD>Compares source resources to destination files to see if they
 should be copied.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#scan(org.apache.tools.ant.types.Resource[], java.io.File)">scan</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>resources</CODE> - The source resources.<DD><CODE>toDir</CODE> - The destination directory.
<DT><B>Returns:</B><DD>a Map with the out-of-date resources as keys and an
 array of target file names as values.</DL>
</DD>
</DL>
<HR>

<A NAME="getToDir()"><!-- --></A><H3>
getToDir</H3>
<PRE>
public java.io.File <B>getToDir</B>()</PRE>
<DL>
<DD>Get the destination directory.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the destination directory</DL>
</DD>
</DL>
<HR>

<A NAME="getIncludeEmptyDirs()"><!-- --></A><H3>
getIncludeEmptyDirs</H3>
<PRE>
public boolean <B>getIncludeEmptyDirs</B>()</PRE>
<DL>
<DD>Get the includeEmptyDirs attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true if emptyDirs are to be included</DL>
</DD>
</DL>
<HR>

<A NAME="supportsNonFileResources()"><!-- --></A><H3>
supportsNonFileResources</H3>
<PRE>
protected boolean <B>supportsNonFileResources</B>()</PRE>
<DL>
<DD>Yes, we can.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html#supportsNonFileResources()">supportsNonFileResources</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>true always.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Sync.SyncTarget.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Sync.MyCopy.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Sync.MyCopy.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.Copy">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
