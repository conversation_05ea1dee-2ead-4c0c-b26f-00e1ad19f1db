<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:34 EST 2006 -->
<TITLE>
CBZip2OutputStream (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.bzip2.CBZip2OutputStream class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="CBZip2OutputStream (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/bzip2/CBZip2InputStream.html" title="class in org.apache.tools.bzip2"><B>PREV CLASS</B></A>&nbsp;
&nbsp;NEXT CLASS</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/bzip2/CBZip2OutputStream.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="CBZip2OutputStream.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.bzip2</FONT>
<BR>
Class CBZip2OutputStream</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by ">java.io.OutputStream
      <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.bzip2.CBZip2OutputStream</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.io.Closeable, java.io.Flushable, <A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html" title="interface in org.apache.tools.bzip2">BZip2Constants</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>CBZip2OutputStream</B><DT>extends java.io.OutputStream<DT>implements <A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html" title="interface in org.apache.tools.bzip2">BZip2Constants</A></DL>
</PRE>

<P>
An output stream that compresses into the BZip2 format (without the file
 header chars) into another stream.

 <p>The compression requires large amounts of memory. Thus you
 should call the <A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#close()"><CODE>close()</CODE></A> method as soon as
 possible, to force <tt>CBZip2OutputStream</tt> to release the
 allocated memory.</p>

 <p>You can shrink the amount of allocated memory and maybe raise
 the compression speed by choosing a lower blocksize, which in turn
 may cause a lower compression ratio.  You can avoid unnecessary
 memory allocation by avoiding using a blocksize which is bigger
 than the size of the input. </p>

 <p>You can compute the memory usage for compressing by the
 following formula:</p>
 <pre>
 <code>400k + (9 * blocksize)</code>.
 </pre>
 
 <p>To get the memory required for decompression by <A HREF="../../../../org/apache/tools/bzip2/CBZip2InputStream.html" title="class in org.apache.tools.bzip2"><CODE>CBZip2InputStream</CODE></A> use</p>
 <pre>
 <code>65k + (5 * blocksize)</code>.
 </pre>

 <table width="100%" border="1">
  <colgroup>
    <col width="33%" />
    <col width="33%" />
    <col width="33%" />
  </colgroup>
  <tr>
    <th colspan="3">Memory usage by blocksize</th>
  </tr><tr>
    <th align="right">Blocksize</th>
    <th align="right">Compression<br>memory usage</th>
    <th align="right">Decompression<br>memory usage</th>
  </tr><tr>
    <td align="right">100k</td>
    <td align="right">1300k</td>
    <td align="right"> 565k</td>
  </tr><tr>
    <td align="right">200k</td>
    <td align="right">2200k</td>
    <td align="right">1065k</td>
  </tr><tr>
    <td align="right">300k</td>
    <td align="right">3100k</td>
    <td align="right">1565k</td>
  </tr><tr>
    <td align="right">400k</td>
    <td align="right">4000k</td>
    <td align="right">2065k</td>
  </tr><tr>
    <td align="right">500k</td>
    <td align="right">4900k</td>
    <td align="right">2565k</td>
  </tr><tr>
    <td align="right">600k</td>
    <td align="right">5800k</td>
    <td align="right">3065k</td>
  </tr><tr>
    <td align="right">700k</td>
    <td align="right">6700k</td>
    <td align="right">3565k</td>
  </tr><tr>
    <td align="right">800k</td>
    <td align="right">7600k</td>
    <td align="right">4065k</td>
  </tr><tr>
    <td align="right">900k</td>
    <td align="right">8500k</td>
    <td align="right">4565k</td>
  </tr>
 </table>

 <p>For decompression <tt>CBZip2InputStream</tt> allocates less
 memory if the bzipped input is smaller than one block.</p>

 <p>Instances of this class are not threadsafe.</p>

 <p>
 TODO:    Update to BZip2 1.0.1
 </p>
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#CLEARMASK">CLEARMASK</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This constant is accessible by subclasses for historical purposes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#DEPTH_THRESH">DEPTH_THRESH</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This constant is accessible by subclasses for historical purposes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#GREATER_ICOST">GREATER_ICOST</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This constant is accessible by subclasses for historical purposes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#LESSER_ICOST">LESSER_ICOST</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This constant is accessible by subclasses for historical purposes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#MAX_BLOCKSIZE">MAX_BLOCKSIZE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The maximum supported blocksize <tt> == 9</tt>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#MIN_BLOCKSIZE">MIN_BLOCKSIZE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The minimum supported blocksize <tt> == 1</tt>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#QSORT_STACK_SIZE">QSORT_STACK_SIZE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This constant is accessible by subclasses for historical purposes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#SETMASK">SETMASK</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This constant is accessible by subclasses for historical purposes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#SMALL_THRESH">SMALL_THRESH</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This constant is accessible by subclasses for historical purposes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#WORK_FACTOR">WORK_FACTOR</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This constant is accessible by subclasses for historical purposes.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.bzip2.BZip2Constants"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from interface org.apache.tools.bzip2.<A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html" title="interface in org.apache.tools.bzip2">BZip2Constants</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#baseBlockSize">baseBlockSize</A>, <A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#G_SIZE">G_SIZE</A>, <A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#MAX_ALPHA_SIZE">MAX_ALPHA_SIZE</A>, <A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#MAX_CODE_LEN">MAX_CODE_LEN</A>, <A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#MAX_SELECTORS">MAX_SELECTORS</A>, <A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#N_GROUPS">N_GROUPS</A>, <A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#N_ITERS">N_ITERS</A>, <A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#NUM_OVERSHOOT_BYTES">NUM_OVERSHOOT_BYTES</A>, <A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#rNums">rNums</A>, <A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#RUNA">RUNA</A>, <A HREF="../../../../org/apache/tools/bzip2/BZip2Constants.html#RUNB">RUNB</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#CBZip2OutputStream(java.io.OutputStream)">CBZip2OutputStream</A></B>(java.io.OutputStream&nbsp;out)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructs a new <tt>CBZip2OutputStream</tt> with a blocksize of 900k.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#CBZip2OutputStream(java.io.OutputStream, int)">CBZip2OutputStream</A></B>(java.io.OutputStream&nbsp;out,
                   int&nbsp;blockSize)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructs a new <tt>CBZip2OutputStream</tt> with specified blocksize.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#chooseBlockSize(long)">chooseBlockSize</A></B>(long&nbsp;inputLength)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Chooses a blocksize based on the given length of the data to compress.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#close()">close</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#finalize()">finalize</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Overriden to close the stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#flush()">flush</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#getBlockSize()">getBlockSize</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the blocksize parameter specified at construction time.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#hbMakeCodeLengths(char[], int[], int, int)">hbMakeCodeLengths</A></B>(char[]&nbsp;len,
                  int[]&nbsp;freq,
                  int&nbsp;alphaSize,
                  int&nbsp;maxLen)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This method is accessible by subclasses for historical purposes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#write(byte[], int, int)">write</A></B>(byte[]&nbsp;buf,
      int&nbsp;offs,
      int&nbsp;len)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#write(int)">write</A></B>(int&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.io.OutputStream"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.io.OutputStream</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>write</CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="MIN_BLOCKSIZE"><!-- --></A><H3>
MIN_BLOCKSIZE</H3>
<PRE>
public static final int <B>MIN_BLOCKSIZE</B></PRE>
<DL>
<DD>The minimum supported blocksize <tt> == 1</tt>.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.MIN_BLOCKSIZE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="MAX_BLOCKSIZE"><!-- --></A><H3>
MAX_BLOCKSIZE</H3>
<PRE>
public static final int <B>MAX_BLOCKSIZE</B></PRE>
<DL>
<DD>The maximum supported blocksize <tt> == 9</tt>.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.MAX_BLOCKSIZE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="SETMASK"><!-- --></A><H3>
SETMASK</H3>
<PRE>
protected static final int <B>SETMASK</B></PRE>
<DL>
<DD>This constant is accessible by subclasses for historical purposes.
 If you don't know what it means then you don't need it.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.SETMASK">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CLEARMASK"><!-- --></A><H3>
CLEARMASK</H3>
<PRE>
protected static final int <B>CLEARMASK</B></PRE>
<DL>
<DD>This constant is accessible by subclasses for historical purposes.
 If you don't know what it means then you don't need it.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.CLEARMASK">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="GREATER_ICOST"><!-- --></A><H3>
GREATER_ICOST</H3>
<PRE>
protected static final int <B>GREATER_ICOST</B></PRE>
<DL>
<DD>This constant is accessible by subclasses for historical purposes.
 If you don't know what it means then you don't need it.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.GREATER_ICOST">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LESSER_ICOST"><!-- --></A><H3>
LESSER_ICOST</H3>
<PRE>
protected static final int <B>LESSER_ICOST</B></PRE>
<DL>
<DD>This constant is accessible by subclasses for historical purposes.
 If you don't know what it means then you don't need it.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.LESSER_ICOST">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="SMALL_THRESH"><!-- --></A><H3>
SMALL_THRESH</H3>
<PRE>
protected static final int <B>SMALL_THRESH</B></PRE>
<DL>
<DD>This constant is accessible by subclasses for historical purposes.
 If you don't know what it means then you don't need it.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.SMALL_THRESH">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEPTH_THRESH"><!-- --></A><H3>
DEPTH_THRESH</H3>
<PRE>
protected static final int <B>DEPTH_THRESH</B></PRE>
<DL>
<DD>This constant is accessible by subclasses for historical purposes.
 If you don't know what it means then you don't need it.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.DEPTH_THRESH">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="WORK_FACTOR"><!-- --></A><H3>
WORK_FACTOR</H3>
<PRE>
protected static final int <B>WORK_FACTOR</B></PRE>
<DL>
<DD>This constant is accessible by subclasses for historical purposes.
 If you don't know what it means then you don't need it.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.WORK_FACTOR">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="QSORT_STACK_SIZE"><!-- --></A><H3>
QSORT_STACK_SIZE</H3>
<PRE>
protected static final int <B>QSORT_STACK_SIZE</B></PRE>
<DL>
<DD>This constant is accessible by subclasses for historical purposes.
 If you don't know what it means then you don't need it.
 <p>
      If you are ever unlucky/improbable enough
      to get a stack overflow whilst sorting,
      increase the following constant and try
      again.  In practice I have never seen the
      stack go above 27 elems, so the following
      limit seems very generous.
 </p>
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.bzip2.CBZip2OutputStream.QSORT_STACK_SIZE">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="CBZip2OutputStream(java.io.OutputStream)"><!-- --></A><H3>
CBZip2OutputStream</H3>
<PRE>
public <B>CBZip2OutputStream</B>(java.io.OutputStream&nbsp;out)
                   throws java.io.IOException</PRE>
<DL>
<DD>Constructs a new <tt>CBZip2OutputStream</tt> with a blocksize of 900k.
 
 <p><b>Attention: </b>The caller is resonsible to write the two
 BZip2 magic bytes <tt>"BZ"</tt> to the specified stream prior
 to calling this constructor.</p>
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>out</CODE> - *  the destination stream.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if an I/O error occurs in the specified stream.
<DD><CODE>java.lang.NullPointerException</CODE> - if <code>out == null</code>.</DL>
</DL>
<HR>

<A NAME="CBZip2OutputStream(java.io.OutputStream, int)"><!-- --></A><H3>
CBZip2OutputStream</H3>
<PRE>
public <B>CBZip2OutputStream</B>(java.io.OutputStream&nbsp;out,
                          int&nbsp;blockSize)
                   throws java.io.IOException</PRE>
<DL>
<DD>Constructs a new <tt>CBZip2OutputStream</tt> with specified blocksize.
 
 <p><b>Attention: </b>The caller is resonsible to write the two
 BZip2 magic bytes <tt>"BZ"</tt> to the specified stream prior
 to calling this constructor.</p>
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>out</CODE> - the destination stream.<DD><CODE>blockSize</CODE> - the blockSize as 100k units.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if an I/O error occurs in the specified stream.
<DD><CODE>java.lang.IllegalArgumentException</CODE> - if <code>(blockSize < 1) || (blockSize > 9)</code>.
<DD><CODE>java.lang.NullPointerException</CODE> - if <code>out == null</code>.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#MIN_BLOCKSIZE"><CODE>MIN_BLOCKSIZE</CODE></A>, 
<A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#MAX_BLOCKSIZE"><CODE>MAX_BLOCKSIZE</CODE></A></DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="hbMakeCodeLengths(char[], int[], int, int)"><!-- --></A><H3>
hbMakeCodeLengths</H3>
<PRE>
protected static void <B>hbMakeCodeLengths</B>(char[]&nbsp;len,
                                        int[]&nbsp;freq,
                                        int&nbsp;alphaSize,
                                        int&nbsp;maxLen)</PRE>
<DL>
<DD>This method is accessible by subclasses for historical purposes.
 If you don't know what it does then you don't need it.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="chooseBlockSize(long)"><!-- --></A><H3>
chooseBlockSize</H3>
<PRE>
public static int <B>chooseBlockSize</B>(long&nbsp;inputLength)</PRE>
<DL>
<DD>Chooses a blocksize based on the given length of the data to compress.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>inputLength</CODE> - The length of the data which will be compressed by
  <tt>CBZip2OutputStream</tt>.
<DT><B>Returns:</B><DD>The blocksize, between <A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#MIN_BLOCKSIZE"><CODE>MIN_BLOCKSIZE</CODE></A> and <A HREF="../../../../org/apache/tools/bzip2/CBZip2OutputStream.html#MAX_BLOCKSIZE"><CODE>MAX_BLOCKSIZE</CODE></A>
  both inclusive. For a negative <tt>inputLength</tt> this method returns
  <tt>MAX_BLOCKSIZE</tt> always.</DL>
</DD>
</DL>
<HR>

<A NAME="write(int)"><!-- --></A><H3>
write</H3>
<PRE>
public void <B>write</B>(int&nbsp;b)
           throws java.io.IOException</PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE>write</CODE> in class <CODE>java.io.OutputStream</CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE></DL>
</DD>
</DL>
<HR>

<A NAME="finalize()"><!-- --></A><H3>
finalize</H3>
<PRE>
protected void <B>finalize</B>()
                 throws java.lang.Throwable</PRE>
<DL>
<DD>Overriden to close the stream.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>finalize</CODE> in class <CODE>java.lang.Object</CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.lang.Throwable</CODE></DL>
</DD>
</DL>
<HR>

<A NAME="close()"><!-- --></A><H3>
close</H3>
<PRE>
public void <B>close</B>()
           throws java.io.IOException</PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE>close</CODE> in interface <CODE>java.io.Closeable</CODE><DT><B>Overrides:</B><DD><CODE>close</CODE> in class <CODE>java.io.OutputStream</CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE></DL>
</DD>
</DL>
<HR>

<A NAME="flush()"><!-- --></A><H3>
flush</H3>
<PRE>
public void <B>flush</B>()
           throws java.io.IOException</PRE>
<DL>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE>flush</CODE> in interface <CODE>java.io.Flushable</CODE><DT><B>Overrides:</B><DD><CODE>flush</CODE> in class <CODE>java.io.OutputStream</CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE></DL>
</DD>
</DL>
<HR>

<A NAME="getBlockSize()"><!-- --></A><H3>
getBlockSize</H3>
<PRE>
public final int <B>getBlockSize</B>()</PRE>
<DL>
<DD>Returns the blocksize parameter specified at construction time.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="write(byte[], int, int)"><!-- --></A><H3>
write</H3>
<PRE>
public void <B>write</B>(byte[]&nbsp;buf,
                  int&nbsp;offs,
                  int&nbsp;len)
           throws java.io.IOException</PRE>
<DL>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>write</CODE> in class <CODE>java.io.OutputStream</CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE></DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/bzip2/CBZip2InputStream.html" title="class in org.apache.tools.bzip2"><B>PREV CLASS</B></A>&nbsp;
&nbsp;NEXT CLASS</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/bzip2/CBZip2OutputStream.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="CBZip2OutputStream.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
