<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:35 EST 2006 -->
<TITLE>
org.apache.tools.zip Class Hierarchy (Apache Ant API)
</TITLE>


<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="org.apache.tools.zip Class Hierarchy (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Tree</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/tar/package-tree.html"><B>PREV</B></A>&nbsp;
&nbsp;NEXT</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/zip/package-tree.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="package-tree.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<CENTER>
<H2>
Hierarchy For Package org.apache.tools.zip
</H2>
</CENTER>
<DL>
<DT><B>Package Hierarchies:</B><DD><A HREF="../../../../overview-tree.html">All Packages</A></DL>
<HR>
<H2>
Class Hierarchy
</H2>
<UL>
<LI TYPE="circle">java.lang.Object<UL>
<LI TYPE="circle">org.apache.tools.zip.<A HREF="../../../../org/apache/tools/zip/AsiExtraField.html" title="class in org.apache.tools.zip"><B>AsiExtraField</B></A> (implements java.lang.Cloneable, org.apache.tools.zip.<A HREF="../../../../org/apache/tools/zip/UnixStat.html" title="interface in org.apache.tools.zip">UnixStat</A>, org.apache.tools.zip.<A HREF="../../../../org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</A>)
<LI TYPE="circle">org.apache.tools.zip.<A HREF="../../../../org/apache/tools/zip/ExtraFieldUtils.html" title="class in org.apache.tools.zip"><B>ExtraFieldUtils</B></A><LI TYPE="circle">org.apache.tools.zip.<A HREF="../../../../org/apache/tools/zip/JarMarker.html" title="class in org.apache.tools.zip"><B>JarMarker</B></A> (implements org.apache.tools.zip.<A HREF="../../../../org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</A>)
<LI TYPE="circle">java.io.OutputStream (implements java.io.Closeable, java.io.Flushable)
<UL>
<LI TYPE="circle">java.io.FilterOutputStream<UL>
<LI TYPE="circle">org.apache.tools.zip.<A HREF="../../../../org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip"><B>ZipOutputStream</B></A></UL>
</UL>
<LI TYPE="circle">org.apache.tools.zip.<A HREF="../../../../org/apache/tools/zip/UnrecognizedExtraField.html" title="class in org.apache.tools.zip"><B>UnrecognizedExtraField</B></A> (implements org.apache.tools.zip.<A HREF="../../../../org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</A>)
<LI TYPE="circle">java.util.zip.ZipEntry (implements java.lang.Cloneable)
<UL>
<LI TYPE="circle">org.apache.tools.zip.<A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip"><B>ZipEntry</B></A> (implements java.lang.Cloneable)
</UL>
<LI TYPE="circle">org.apache.tools.zip.<A HREF="../../../../org/apache/tools/zip/ZipFile.html" title="class in org.apache.tools.zip"><B>ZipFile</B></A><LI TYPE="circle">org.apache.tools.zip.<A HREF="../../../../org/apache/tools/zip/ZipLong.html" title="class in org.apache.tools.zip"><B>ZipLong</B></A> (implements java.lang.Cloneable)
<LI TYPE="circle">org.apache.tools.zip.<A HREF="../../../../org/apache/tools/zip/ZipShort.html" title="class in org.apache.tools.zip"><B>ZipShort</B></A> (implements java.lang.Cloneable)
</UL>
</UL>
<H2>
Interface Hierarchy
</H2>
<UL>
<LI TYPE="circle">org.apache.tools.zip.<A HREF="../../../../org/apache/tools/zip/UnixStat.html" title="interface in org.apache.tools.zip"><B>UnixStat</B></A><LI TYPE="circle">org.apache.tools.zip.<A HREF="../../../../org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip"><B>ZipExtraField</B></A></UL>
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Tree</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/tar/package-tree.html"><B>PREV</B></A>&nbsp;
&nbsp;NEXT</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/zip/package-tree.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="package-tree.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
