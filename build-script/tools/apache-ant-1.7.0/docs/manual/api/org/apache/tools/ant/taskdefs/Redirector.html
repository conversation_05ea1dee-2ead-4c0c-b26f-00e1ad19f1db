<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:24 EST 2006 -->
<TITLE>
Redirector (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.Redirector class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Redirector (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Rename.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Redirector.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Redirector.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class Redirector</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.Redirector</B>
</PRE>
<HR>
<DL>
<DT><PRE>public class <B>Redirector</B><DT>extends java.lang.Object</DL>
</PRE>

<P>
The Redirector class manages the setup and connection of
 input and output redirection for an Ant project component.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
<HR>

<P>

<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#Redirector(org.apache.tools.ant.ProjectComponent)">Redirector</A></B>(<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A>&nbsp;managingTask)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a redirector instance for the given task</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#Redirector(org.apache.tools.ant.Task)">Redirector</A></B>(<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;managingTask)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a redirector instance for the given task</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#complete()">complete</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Complete redirection.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#createHandler()">createHandler</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create the StreamHandler to use with our Execute instance.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#createStreams()">createStreams</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create the input, error and output streams based on the
 configuration options.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.OutputStream</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#getErrorStream()">getErrorStream</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the error stream for the redirector</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.InputStream</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#getInputStream()">getInputStream</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the input stream for the redirector</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.OutputStream</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#getOutputStream()">getOutputStream</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the output stream for the redirector</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A></B>(java.lang.String&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Handle a flush operation on the error stream</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A></B>(java.lang.String&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Process error output</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#handleFlush(java.lang.String)">handleFlush</A></B>(java.lang.String&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Process data due to a flush operation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#handleInput(byte[], int, int)">handleInput</A></B>(byte[]&nbsp;buffer,
            int&nbsp;offset,
            int&nbsp;length)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Handle an input request</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#handleOutput(java.lang.String)">handleOutput</A></B>(java.lang.String&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Pass output sent to System.out to specified output.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setAlwaysLog(boolean)">setAlwaysLog</A></B>(boolean&nbsp;alwaysLog)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, (error and non-error) output will be "teed", redirected
 as specified while being sent to Ant's logging mechanism as if no
 redirection had taken place.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setAppend(boolean)">setAppend</A></B>(boolean&nbsp;append)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether output should be appended to or overwrite an existing file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setAppendProperties(boolean)">setAppendProperties</A></B>(boolean&nbsp;appendProperties)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This <code>Redirector</code>'s subordinate
 <code>PropertyOutputStream</code>s will not set their respective
 properties <code>while (appendProperties && append)</code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setCreateEmptyFiles(boolean)">setCreateEmptyFiles</A></B>(boolean&nbsp;createEmptyFiles)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether output and error files should be created even when empty.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setError(java.io.File)">setError</A></B>(java.io.File&nbsp;error)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the file to which standard error is to be redirected.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setError(java.io.File[])">setError</A></B>(java.io.File[]&nbsp;error)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the files to which standard error is to be redirected.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setErrorEncoding(java.lang.String)">setErrorEncoding</A></B>(java.lang.String&nbsp;errorEncoding)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the error encoding.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setErrorFilterChains(java.util.Vector)">setErrorFilterChains</A></B>(java.util.Vector&nbsp;errorFilterChains)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the error <code>FilterChain</code>s.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setErrorProperty(java.lang.String)">setErrorProperty</A></B>(java.lang.String&nbsp;errorProperty)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Property name whose value should be set to the error of
 the process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setInput(java.io.File)">setInput</A></B>(java.io.File&nbsp;input)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the input to use for the task</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setInput(java.io.File[])">setInput</A></B>(java.io.File[]&nbsp;input)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the input to use for the task</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setInputEncoding(java.lang.String)">setInputEncoding</A></B>(java.lang.String&nbsp;inputEncoding)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the input encoding.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setInputFilterChains(java.util.Vector)">setInputFilterChains</A></B>(java.util.Vector&nbsp;inputFilterChains)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the input <code>FilterChain</code>s.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setInputString(java.lang.String)">setInputString</A></B>(java.lang.String&nbsp;inputString)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the string to use as input</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setLogError(boolean)">setLogError</A></B>(boolean&nbsp;logError)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Controls whether error output of exec is logged.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setLogInputString(boolean)">setLogInputString</A></B>(boolean&nbsp;logInputString)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether to include the value of the input string in log messages.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setOutput(java.io.File)">setOutput</A></B>(java.io.File&nbsp;out)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;File the output of the process is redirected to.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setOutput(java.io.File[])">setOutput</A></B>(java.io.File[]&nbsp;out)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Files the output of the process is redirected to.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setOutputEncoding(java.lang.String)">setOutputEncoding</A></B>(java.lang.String&nbsp;outputEncoding)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the output encoding.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setOutputFilterChains(java.util.Vector)">setOutputFilterChains</A></B>(java.util.Vector&nbsp;outputFilterChains)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the output <code>FilterChain</code>s.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setOutputProperty(java.lang.String)">setOutputProperty</A></B>(java.lang.String&nbsp;outputProperty)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Property name whose value should be set to the output of
 the process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html#setProperties()">setProperties</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Notify the <code>Redirector</code> that it is now okay
 to set any output and/or error properties.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Redirector(org.apache.tools.ant.Task)"><!-- --></A><H3>
Redirector</H3>
<PRE>
public <B>Redirector</B>(<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;managingTask)</PRE>
<DL>
<DD>Create a redirector instance for the given task
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>managingTask</CODE> - the task for which the redirector is to work</DL>
</DL>
<HR>

<A NAME="Redirector(org.apache.tools.ant.ProjectComponent)"><!-- --></A><H3>
Redirector</H3>
<PRE>
public <B>Redirector</B>(<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A>&nbsp;managingTask)</PRE>
<DL>
<DD>Create a redirector instance for the given task
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>managingTask</CODE> - the project component for which the
 redirector is to work<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setInput(java.io.File)"><!-- --></A><H3>
setInput</H3>
<PRE>
public void <B>setInput</B>(java.io.File&nbsp;input)</PRE>
<DL>
<DD>Set the input to use for the task
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>input</CODE> - the file from which input is read.</DL>
</DD>
</DL>
<HR>

<A NAME="setInput(java.io.File[])"><!-- --></A><H3>
setInput</H3>
<PRE>
public void <B>setInput</B>(java.io.File[]&nbsp;input)</PRE>
<DL>
<DD>Set the input to use for the task
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>input</CODE> - the files from which input is read.</DL>
</DD>
</DL>
<HR>

<A NAME="setInputString(java.lang.String)"><!-- --></A><H3>
setInputString</H3>
<PRE>
public void <B>setInputString</B>(java.lang.String&nbsp;inputString)</PRE>
<DL>
<DD>Set the string to use as input
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>inputString</CODE> - the string which is used as the input source</DL>
</DD>
</DL>
<HR>

<A NAME="setLogInputString(boolean)"><!-- --></A><H3>
setLogInputString</H3>
<PRE>
public void <B>setLogInputString</B>(boolean&nbsp;logInputString)</PRE>
<DL>
<DD>Set whether to include the value of the input string in log messages.
 Defaults to true.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>logInputString</CODE> - true or false.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setOutput(java.io.File)"><!-- --></A><H3>
setOutput</H3>
<PRE>
public void <B>setOutput</B>(java.io.File&nbsp;out)</PRE>
<DL>
<DD>File the output of the process is redirected to. If error is not
 redirected, it too will appear in the output
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>out</CODE> - the file to which output stream is written</DL>
</DD>
</DL>
<HR>

<A NAME="setOutput(java.io.File[])"><!-- --></A><H3>
setOutput</H3>
<PRE>
public void <B>setOutput</B>(java.io.File[]&nbsp;out)</PRE>
<DL>
<DD>Files the output of the process is redirected to. If error is not
 redirected, it too will appear in the output
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>out</CODE> - the files to which output stream is written</DL>
</DD>
</DL>
<HR>

<A NAME="setOutputEncoding(java.lang.String)"><!-- --></A><H3>
setOutputEncoding</H3>
<PRE>
public void <B>setOutputEncoding</B>(java.lang.String&nbsp;outputEncoding)</PRE>
<DL>
<DD>Set the output encoding.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>outputEncoding</CODE> - <code>String</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setErrorEncoding(java.lang.String)"><!-- --></A><H3>
setErrorEncoding</H3>
<PRE>
public void <B>setErrorEncoding</B>(java.lang.String&nbsp;errorEncoding)</PRE>
<DL>
<DD>Set the error encoding.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>errorEncoding</CODE> - <code>String</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setInputEncoding(java.lang.String)"><!-- --></A><H3>
setInputEncoding</H3>
<PRE>
public void <B>setInputEncoding</B>(java.lang.String&nbsp;inputEncoding)</PRE>
<DL>
<DD>Set the input encoding.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>inputEncoding</CODE> - <code>String</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setLogError(boolean)"><!-- --></A><H3>
setLogError</H3>
<PRE>
public void <B>setLogError</B>(boolean&nbsp;logError)</PRE>
<DL>
<DD>Controls whether error output of exec is logged. This is only useful
 when output is being redirected and error output is desired in the
 Ant log
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>logError</CODE> - if true the standard error is sent to the Ant log system
        and not sent to output.</DL>
</DD>
</DL>
<HR>

<A NAME="setAppendProperties(boolean)"><!-- --></A><H3>
setAppendProperties</H3>
<PRE>
public void <B>setAppendProperties</B>(boolean&nbsp;appendProperties)</PRE>
<DL>
<DD>This <code>Redirector</code>'s subordinate
 <code>PropertyOutputStream</code>s will not set their respective
 properties <code>while (appendProperties && append)</code>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>appendProperties</CODE> - whether to append properties.</DL>
</DD>
</DL>
<HR>

<A NAME="setError(java.io.File)"><!-- --></A><H3>
setError</H3>
<PRE>
public void <B>setError</B>(java.io.File&nbsp;error)</PRE>
<DL>
<DD>Set the file to which standard error is to be redirected.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>error</CODE> - the file to which error is to be written</DL>
</DD>
</DL>
<HR>

<A NAME="setError(java.io.File[])"><!-- --></A><H3>
setError</H3>
<PRE>
public void <B>setError</B>(java.io.File[]&nbsp;error)</PRE>
<DL>
<DD>Set the files to which standard error is to be redirected.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>error</CODE> - the file to which error is to be written</DL>
</DD>
</DL>
<HR>

<A NAME="setOutputProperty(java.lang.String)"><!-- --></A><H3>
setOutputProperty</H3>
<PRE>
public void <B>setOutputProperty</B>(java.lang.String&nbsp;outputProperty)</PRE>
<DL>
<DD>Property name whose value should be set to the output of
 the process.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>outputProperty</CODE> - the name of the property to be set with the
        task's output.</DL>
</DD>
</DL>
<HR>

<A NAME="setAppend(boolean)"><!-- --></A><H3>
setAppend</H3>
<PRE>
public void <B>setAppend</B>(boolean&nbsp;append)</PRE>
<DL>
<DD>Whether output should be appended to or overwrite an existing file.
 Defaults to false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>append</CODE> - if true output and error streams are appended to their
        respective files, if specified.</DL>
</DD>
</DL>
<HR>

<A NAME="setAlwaysLog(boolean)"><!-- --></A><H3>
setAlwaysLog</H3>
<PRE>
public void <B>setAlwaysLog</B>(boolean&nbsp;alwaysLog)</PRE>
<DL>
<DD>If true, (error and non-error) output will be "teed", redirected
 as specified while being sent to Ant's logging mechanism as if no
 redirection had taken place.  Defaults to false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>alwaysLog</CODE> - <code>boolean</code><DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setCreateEmptyFiles(boolean)"><!-- --></A><H3>
setCreateEmptyFiles</H3>
<PRE>
public void <B>setCreateEmptyFiles</B>(boolean&nbsp;createEmptyFiles)</PRE>
<DL>
<DD>Whether output and error files should be created even when empty.
 Defaults to true.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>createEmptyFiles</CODE> - <code>boolean</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setErrorProperty(java.lang.String)"><!-- --></A><H3>
setErrorProperty</H3>
<PRE>
public void <B>setErrorProperty</B>(java.lang.String&nbsp;errorProperty)</PRE>
<DL>
<DD>Property name whose value should be set to the error of
 the process.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>errorProperty</CODE> - the name of the property to be set
        with the error output.</DL>
</DD>
</DL>
<HR>

<A NAME="setInputFilterChains(java.util.Vector)"><!-- --></A><H3>
setInputFilterChains</H3>
<PRE>
public void <B>setInputFilterChains</B>(java.util.Vector&nbsp;inputFilterChains)</PRE>
<DL>
<DD>Set the input <code>FilterChain</code>s.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>inputFilterChains</CODE> - <code>Vector</code> containing <code>FilterChain</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setOutputFilterChains(java.util.Vector)"><!-- --></A><H3>
setOutputFilterChains</H3>
<PRE>
public void <B>setOutputFilterChains</B>(java.util.Vector&nbsp;outputFilterChains)</PRE>
<DL>
<DD>Set the output <code>FilterChain</code>s.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>outputFilterChains</CODE> - <code>Vector</code> containing <code>FilterChain</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setErrorFilterChains(java.util.Vector)"><!-- --></A><H3>
setErrorFilterChains</H3>
<PRE>
public void <B>setErrorFilterChains</B>(java.util.Vector&nbsp;errorFilterChains)</PRE>
<DL>
<DD>Set the error <code>FilterChain</code>s.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>errorFilterChains</CODE> - <code>Vector</code> containing <code>FilterChain</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="createStreams()"><!-- --></A><H3>
createStreams</H3>
<PRE>
public void <B>createStreams</B>()</PRE>
<DL>
<DD>Create the input, error and output streams based on the
 configuration options.
<P>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="createHandler()"><!-- --></A><H3>
createHandler</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A> <B>createHandler</B>()
                                   throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Create the StreamHandler to use with our Execute instance.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the execute stream handler to manage the input, output and
 error streams.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the execute stream handler cannot be created.</DL>
</DD>
</DL>
<HR>

<A NAME="handleOutput(java.lang.String)"><!-- --></A><H3>
handleOutput</H3>
<PRE>
protected void <B>handleOutput</B>(java.lang.String&nbsp;output)</PRE>
<DL>
<DD>Pass output sent to System.out to specified output.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - the data to be output</DL>
</DD>
</DL>
<HR>

<A NAME="handleInput(byte[], int, int)"><!-- --></A><H3>
handleInput</H3>
<PRE>
protected int <B>handleInput</B>(byte[]&nbsp;buffer,
                          int&nbsp;offset,
                          int&nbsp;length)
                   throws java.io.IOException</PRE>
<DL>
<DD>Handle an input request
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>buffer</CODE> - the buffer into which data is to be read.<DD><CODE>offset</CODE> - the offset into the buffer at which data is stored.<DD><CODE>length</CODE> - the amount of data to read
<DT><B>Returns:</B><DD>the number of bytes read
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the data cannot be read</DL>
</DD>
</DL>
<HR>

<A NAME="handleFlush(java.lang.String)"><!-- --></A><H3>
handleFlush</H3>
<PRE>
protected void <B>handleFlush</B>(java.lang.String&nbsp;output)</PRE>
<DL>
<DD>Process data due to a flush operation.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - the data being flushed.</DL>
</DD>
</DL>
<HR>

<A NAME="handleErrorOutput(java.lang.String)"><!-- --></A><H3>
handleErrorOutput</H3>
<PRE>
protected void <B>handleErrorOutput</B>(java.lang.String&nbsp;output)</PRE>
<DL>
<DD>Process error output
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - the error output data.</DL>
</DD>
</DL>
<HR>

<A NAME="handleErrorFlush(java.lang.String)"><!-- --></A><H3>
handleErrorFlush</H3>
<PRE>
protected void <B>handleErrorFlush</B>(java.lang.String&nbsp;output)</PRE>
<DL>
<DD>Handle a flush operation on the error stream
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - the error information being flushed.</DL>
</DD>
</DL>
<HR>

<A NAME="getOutputStream()"><!-- --></A><H3>
getOutputStream</H3>
<PRE>
public java.io.OutputStream <B>getOutputStream</B>()</PRE>
<DL>
<DD>Get the output stream for the redirector
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the redirector's output stream or null if no output
         has been configured</DL>
</DD>
</DL>
<HR>

<A NAME="getErrorStream()"><!-- --></A><H3>
getErrorStream</H3>
<PRE>
public java.io.OutputStream <B>getErrorStream</B>()</PRE>
<DL>
<DD>Get the error stream for the redirector
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the redirector's error stream or null if no output
         has been configured</DL>
</DD>
</DL>
<HR>

<A NAME="getInputStream()"><!-- --></A><H3>
getInputStream</H3>
<PRE>
public java.io.InputStream <B>getInputStream</B>()</PRE>
<DL>
<DD>Get the input stream for the redirector
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the redirector's input stream or null if no output
         has been configured</DL>
</DD>
</DL>
<HR>

<A NAME="complete()"><!-- --></A><H3>
complete</H3>
<PRE>
public void <B>complete</B>()
              throws java.io.IOException</PRE>
<DL>
<DD>Complete redirection.

 This operation will close any streams and create any specified
 property values.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the output properties cannot be read from their
 output streams.</DL>
</DD>
</DL>
<HR>

<A NAME="setProperties()"><!-- --></A><H3>
setProperties</H3>
<PRE>
public void <B>setProperties</B>()</PRE>
<DL>
<DD>Notify the <code>Redirector</code> that it is now okay
 to set any output and/or error properties.
<P>
<DD><DL>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Rename.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Redirector.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Redirector.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
