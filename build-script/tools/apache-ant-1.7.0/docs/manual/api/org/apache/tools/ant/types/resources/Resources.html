<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:32 EST 2006 -->
<TITLE>
Resources (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.types.resources.Resources class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Resources (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/types/resources/PropertyResource.html" title="class in org.apache.tools.ant.types.resources"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/types/resources/Restrict.html" title="class in org.apache.tools.ant.types.resources"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../index.html?org/apache/tools/ant/types/resources/Resources.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Resources.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.types.resources</FONT>
<BR>
Class Resources</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</A>
          <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.types.resources.Resources</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable, <A HREF="../../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>Resources</B><DT>extends <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A><DT>implements <A HREF="../../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A></DL>
</PRE>

<P>
Generic ResourceCollection: Either stores nested ResourceCollections,
 making no attempt to remove duplicates, or references another ResourceCollection.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.util.Iterator</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/types/resources/Resources.html#EMPTY_ITERATOR">EMPTY_ITERATOR</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;static empty Iterator</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="../../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/types/resources/Resources.html#NONE">NONE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;static empty ResourceCollection</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.types.DataType"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.types.<A HREF="../../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#checked">checked</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#ref">ref</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/types/resources/Resources.html#Resources()">Resources</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/types/resources/Resources.html#add(org.apache.tools.ant.types.ResourceCollection)">add</A></B>(<A HREF="../../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;c)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a ResourceCollection.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/types/resources/Resources.html#dieOnCircularReference(java.util.Stack, org.apache.tools.ant.Project)">dieOnCircularReference</A></B>(java.util.Stack&nbsp;stk,
                       <A HREF="../../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Overrides the version of DataType to recurse on all DataType
 child elements that may have been added.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/types/resources/Resources.html#isFilesystemOnly()">isFilesystemOnly</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Fulfill the ResourceCollection contract.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Iterator</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/types/resources/Resources.html#iterator()">iterator</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Fulfill the ResourceCollection contract.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/types/resources/Resources.html#size()">size</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Fulfill the ResourceCollection contract.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/types/resources/Resources.html#toString()">toString</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Format this BaseResourceCollectionContainer as a String.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.types.DataType"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.types.<A HREF="../../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#checkAttributesAllowed()">checkAttributesAllowed</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#checkChildrenAllowed()">checkChildrenAllowed</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#circularReference()">circularReference</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#clone()">clone</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference()">dieOnCircularReference</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef()">getCheckedRef</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(java.lang.Class, java.lang.String)">getCheckedRef</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(java.lang.Class, java.lang.String, org.apache.tools.ant.Project)">getCheckedRef</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#getDataTypeName()">getDataTypeName</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#getRefid()">getRefid</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType, java.util.Stack, org.apache.tools.ant.Project)">invokeCircularReferenceCheck</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#isChecked()">isChecked</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#isReference()">isReference</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#noChildrenAllowed()">noChildrenAllowed</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#setChecked(boolean)">setChecked</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</A>, <A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#tooManyAttributes()">tooManyAttributes</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String)">log</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="NONE"><!-- --></A><H3>
NONE</H3>
<PRE>
public static final <A HREF="../../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A> <B>NONE</B></PRE>
<DL>
<DD>static empty ResourceCollection
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="EMPTY_ITERATOR"><!-- --></A><H3>
EMPTY_ITERATOR</H3>
<PRE>
public static final java.util.Iterator <B>EMPTY_ITERATOR</B></PRE>
<DL>
<DD>static empty Iterator
<P>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Resources()"><!-- --></A><H3>
Resources</H3>
<PRE>
public <B>Resources</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="add(org.apache.tools.ant.types.ResourceCollection)"><!-- --></A><H3>
add</H3>
<PRE>
public void <B>add</B>(<A HREF="../../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A>&nbsp;c)</PRE>
<DL>
<DD>Add a ResourceCollection.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>c</CODE> - the ResourceCollection to add.</DL>
</DD>
</DL>
<HR>

<A NAME="iterator()"><!-- --></A><H3>
iterator</H3>
<PRE>
public java.util.Iterator <B>iterator</B>()</PRE>
<DL>
<DD>Fulfill the ResourceCollection contract.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/types/ResourceCollection.html#iterator()">iterator</A></CODE> in interface <CODE><A HREF="../../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>an Iterator of Resources.</DL>
</DD>
</DL>
<HR>

<A NAME="size()"><!-- --></A><H3>
size</H3>
<PRE>
public int <B>size</B>()</PRE>
<DL>
<DD>Fulfill the ResourceCollection contract.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/types/ResourceCollection.html#size()">size</A></CODE> in interface <CODE><A HREF="../../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>number of elements as int.</DL>
</DD>
</DL>
<HR>

<A NAME="isFilesystemOnly()"><!-- --></A><H3>
isFilesystemOnly</H3>
<PRE>
public boolean <B>isFilesystemOnly</B>()</PRE>
<DL>
<DD>Fulfill the ResourceCollection contract.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/types/ResourceCollection.html#isFilesystemOnly()">isFilesystemOnly</A></CODE> in interface <CODE><A HREF="../../../../../../org/apache/tools/ant/types/ResourceCollection.html" title="interface in org.apache.tools.ant.types">ResourceCollection</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>true if all Resources represent files.</DL>
</DD>
</DL>
<HR>

<A NAME="toString()"><!-- --></A><H3>
toString</H3>
<PRE>
public java.lang.String <B>toString</B>()</PRE>
<DL>
<DD>Format this BaseResourceCollectionContainer as a String.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#toString()">toString</A></CODE> in class <CODE><A HREF="../../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a descriptive <code>String</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="dieOnCircularReference(java.util.Stack, org.apache.tools.ant.Project)"><!-- --></A><H3>
dieOnCircularReference</H3>
<PRE>
protected void <B>dieOnCircularReference</B>(java.util.Stack&nbsp;stk,
                                      <A HREF="../../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)
                               throws <A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Overrides the version of DataType to recurse on all DataType
 child elements that may have been added.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference(java.util.Stack, org.apache.tools.ant.Project)">dieOnCircularReference</A></CODE> in class <CODE><A HREF="../../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>stk</CODE> - the stack of data types to use (recursively).<DD><CODE>p</CODE> - the project to use to dereference the references.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/types/resources/PropertyResource.html" title="class in org.apache.tools.ant.types.resources"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/types/resources/Restrict.html" title="class in org.apache.tools.ant.types.resources"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../index.html?org/apache/tools/ant/types/resources/Resources.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Resources.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
