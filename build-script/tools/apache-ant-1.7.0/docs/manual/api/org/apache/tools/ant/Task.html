<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:20 EST 2006 -->
<TITLE>
Task (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.Task class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Task (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/TaskAdapter.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/Task.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Task.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class Task</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.Task</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractCvsTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Ant.html" title="class in org.apache.tools.ant.taskdefs">Ant</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Antlib.html" title="class in org.apache.tools.ant.taskdefs">Antlib</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/AntlibDefinition.html" title="class in org.apache.tools.ant.taskdefs">AntlibDefinition</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/ANTLR.html" title="class in org.apache.tools.ant.taskdefs.optional">ANTLR</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html" title="class in org.apache.tools.ant.taskdefs.optional.scm">AntStarTeamCheckOut</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/AntStructure.html" title="class in org.apache.tools.ant.taskdefs">AntStructure</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Available.html" title="class in org.apache.tools.ant.taskdefs">Available</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Basename.html" title="class in org.apache.tools.ant.taskdefs">Basename</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/ejb/BorlandGenerateClient.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">BorlandGenerateClient</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/BuildNumber.html" title="class in org.apache.tools.ant.taskdefs">BuildNumber</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/CallTarget.html" title="class in org.apache.tools.ant.taskdefs">CallTarget</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Classloader.html" title="class in org.apache.tools.ant.taskdefs">Classloader</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">ClearCase</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Concat.html" title="class in org.apache.tools.ant.taskdefs">Concat</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/ccm/Continuus.html" title="class in org.apache.tools.ant.taskdefs.optional.ccm">Continuus</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Copy.html" title="class in org.apache.tools.ant.taskdefs">Copy</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Copyfile.html" title="class in org.apache.tools.ant.taskdefs">Copyfile</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/CopyPath.html" title="class in org.apache.tools.ant.taskdefs">CopyPath</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/CVSPass.html" title="class in org.apache.tools.ant.taskdefs">CVSPass</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/DefaultExcludes.html" title="class in org.apache.tools.ant.taskdefs">DefaultExcludes</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Deltree.html" title="class in org.apache.tools.ant.taskdefs">Deltree</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/DiagnosticsTask.html" title="class in org.apache.tools.ant.taskdefs">DiagnosticsTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Dirname.html" title="class in org.apache.tools.ant.taskdefs">Dirname</A>, <A HREF="../../../../org/apache/tools/ant/dispatch/DispatchTask.html" title="class in org.apache.tools.ant.dispatch">DispatchTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Echo.html" title="class in org.apache.tools.ant.taskdefs">Echo</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/EchoProperties.html" title="class in org.apache.tools.ant.taskdefs.optional">EchoProperties</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/email/EmailTask.html" title="class in org.apache.tools.ant.taskdefs.email">EmailTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Exec.html" title="class in org.apache.tools.ant.taskdefs">Exec</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Exit.html" title="class in org.apache.tools.ant.taskdefs">Exit</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Expand.html" title="class in org.apache.tools.ant.taskdefs">Expand</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Filter.html" title="class in org.apache.tools.ant.taskdefs">Filter</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/net/FTP.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/GenerateKey.html" title="class in org.apache.tools.ant.taskdefs">GenerateKey</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Get.html" title="class in org.apache.tools.ant.taskdefs">Get</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/dotnet/Ildasm.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">Ildasm</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/ImportTask.html" title="class in org.apache.tools.ant.taskdefs">ImportTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/dotnet/ImportTypelib.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">ImportTypelib</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Input.html" title="class in org.apache.tools.ant.taskdefs">Input</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/ejb/IPlanetEjbcTask.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">IPlanetEjbcTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/extension/JarLibAvailableTask.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">JarLibAvailableTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/extension/JarLibDisplayTask.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">JarLibDisplayTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/extension/JarLibManifestTask.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">JarLibManifestTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/extension/JarLibResolveTask.html" title="class in org.apache.tools.ant.taskdefs.optional.extension">JarLibResolveTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Java.html" title="class in org.apache.tools.ant.taskdefs">Java</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/javacc/JavaCC.html" title="class in org.apache.tools.ant.taskdefs.optional.javacc">JavaCC</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Javadoc.html" title="class in org.apache.tools.ant.taskdefs">Javadoc</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/Javah.html" title="class in org.apache.tools.ant.taskdefs.optional">Javah</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/JDBCTask.html" title="class in org.apache.tools.ant.taskdefs">JDBCTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/jdepend/JDependTask.html" title="class in org.apache.tools.ant.taskdefs.optional.jdepend">JDependTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/javacc/JJDoc.html" title="class in org.apache.tools.ant.taskdefs.optional.javacc">JJDoc</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/javacc/JJTree.html" title="class in org.apache.tools.ant.taskdefs.optional.javacc">JJTree</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/KeySubst.html" title="class in org.apache.tools.ant.taskdefs">KeySubst</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Length.html" title="class in org.apache.tools.ant.taskdefs">Length</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/LoadProperties.html" title="class in org.apache.tools.ant.taskdefs">LoadProperties</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/LoadResource.html" title="class in org.apache.tools.ant.taskdefs">LoadResource</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/MacroInstance.html" title="class in org.apache.tools.ant.taskdefs">MacroInstance</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/MakeUrl.html" title="class in org.apache.tools.ant.taskdefs">MakeUrl</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/ManifestClassPath.html" title="class in org.apache.tools.ant.taskdefs">ManifestClassPath</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/ManifestTask.html" title="class in org.apache.tools.ant.taskdefs">ManifestTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Mkdir.html" title="class in org.apache.tools.ant.taskdefs">Mkdir</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/vss/MSVSS.html" title="class in org.apache.tools.ant.taskdefs.optional.vss">MSVSS</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Nice.html" title="class in org.apache.tools.ant.taskdefs">Nice</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/perforce/P4Base.html" title="class in org.apache.tools.ant.taskdefs.optional.perforce">P4Base</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Pack.html" title="class in org.apache.tools.ant.taskdefs">Pack</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Parallel.html" title="class in org.apache.tools.ant.taskdefs">Parallel</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Patch.html" title="class in org.apache.tools.ant.taskdefs">Patch</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/PathConvert.html" title="class in org.apache.tools.ant.taskdefs">PathConvert</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Property.html" title="class in org.apache.tools.ant.taskdefs">Property</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/PropertyFile.html" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/pvcs/Pvcs.html" title="class in org.apache.tools.ant.taskdefs.optional.pvcs">Pvcs</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Recorder.html" title="class in org.apache.tools.ant.taskdefs">Recorder</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Rename.html" title="class in org.apache.tools.ant.taskdefs">Rename</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/ReplaceRegExp.html" title="class in org.apache.tools.ant.taskdefs.optional">ReplaceRegExp</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/ResourceCount.html" title="class in org.apache.tools.ant.taskdefs">ResourceCount</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/net/RExecTask.html" title="class in org.apache.tools.ant.taskdefs.optional.net">RExecTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/Rpm.html" title="class in org.apache.tools.ant.taskdefs.optional">Rpm</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/Script.html" title="class in org.apache.tools.ant.taskdefs.optional">Script</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/script/ScriptDefBase.html" title="class in org.apache.tools.ant.taskdefs.optional.script">ScriptDefBase</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Sequential.html" title="class in org.apache.tools.ant.taskdefs">Sequential</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/j2ee/ServerDeploy.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">ServerDeploy</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/net/SetProxy.html" title="class in org.apache.tools.ant.taskdefs.optional.net">SetProxy</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Sleep.html" title="class in org.apache.tools.ant.taskdefs">Sleep</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOS</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/sound/SoundTask.html" title="class in org.apache.tools.ant.taskdefs.optional.sound">SoundTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/splash/SplashTask.html" title="class in org.apache.tools.ant.taskdefs.optional.splash">SplashTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/ssh/SSHBase.html" title="class in org.apache.tools.ant.taskdefs.optional.ssh">SSHBase</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/starteam/StarTeamTask.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam">StarTeamTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/SubAnt.html" title="class in org.apache.tools.ant.taskdefs">SubAnt</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Sync.html" title="class in org.apache.tools.ant.taskdefs">Sync</A>, <A HREF="../../../../org/apache/tools/ant/TaskAdapter.html" title="class in org.apache.tools.ant">TaskAdapter</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/net/TelnetTask.html" title="class in org.apache.tools.ant.taskdefs.optional.net">TelnetTask</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/TempFile.html" title="class in org.apache.tools.ant.taskdefs">TempFile</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Touch.html" title="class in org.apache.tools.ant.taskdefs">Touch</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Tstamp.html" title="class in org.apache.tools.ant.taskdefs">Tstamp</A>, <A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/Unpack.html" title="class in org.apache.tools.ant.taskdefs">Unpack</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/UpToDate.html" title="class in org.apache.tools.ant.taskdefs">UpToDate</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/WhichResource.html" title="class in org.apache.tools.ant.taskdefs">WhichResource</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/ejb/WLRun.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WLRun</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/ejb/WLStop.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WLStop</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/dotnet/WsdlToDotnet.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">WsdlToDotnet</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/XmlProperty.html" title="class in org.apache.tools.ant.taskdefs">XmlProperty</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/junit/XMLResultAggregator.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">XMLResultAggregator</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public abstract class <B>Task</B><DT>extends <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></DL>
</PRE>

<P>
Base class for all tasks.

 Use Project.createTask to create a new task instance rather than
 using this class directly for construction.
<P>

<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/Project.html#createTask(java.lang.String)"><CODE>Project.createTask(java.lang.String)</CODE></A></DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#target">target</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <A HREF="../../../../org/apache/tools/ant/Task.html#getOwningTarget()"><CODE>getOwningTarget()</CODE></A> method.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#taskName">taskName</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <A HREF="../../../../org/apache/tools/ant/Task.html#getTaskName()"><CODE>getTaskName()</CODE></A> method.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#taskType">taskType</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <A HREF="../../../../org/apache/tools/ant/Task.html#getTaskType()"><CODE>getTaskType()</CODE></A> method.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <A HREF="../../../../org/apache/tools/ant/Task.html#getWrapper()"><CODE>getWrapper()</CODE></A> method.</I></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#Task()">Task</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sole constructor.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A></B>(<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;owner)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Bind a task to another; use this when configuring a newly created
 task to do work on behalf of another.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Called by the project to let the task do its work.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the container target of this task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the wrapper used for runtime configuration.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the name to use in logging messages.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the type of task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the runtime configurable structure for this task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A></B>(java.lang.String&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Handles an error line by logging it with the WARN priority.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A></B>(java.lang.String&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Handles an error output by logging it with the WARN priority.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A></B>(java.lang.String&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Handles output by logging it with the INFO priority.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A></B>(byte[]&nbsp;buffer,
            int&nbsp;offset,
            int&nbsp;length)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Handle an input request by this task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A></B>(java.lang.String&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Handles output by logging it with the INFO priority.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#init()">init</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Called by the project to let the task initialize properly.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Has this task been marked invalid?</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A></B>(java.lang.String&nbsp;msg)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logs a message with the default (INFO) priority.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A></B>(java.lang.String&nbsp;msg,
    int&nbsp;msgLevel)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logs a message with the given priority.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A></B>(java.lang.String&nbsp;msg,
    java.lang.Throwable&nbsp;t,
    int&nbsp;msgLevel)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logs a message with the given priority.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A></B>(java.lang.Throwable&nbsp;t,
    int&nbsp;msgLevel)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logs a message with the given priority.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Configures this task - if it hasn't been done already.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#perform()">perform</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Performs this task if it's still valid, or gets a replacement
 version and performs that otherwise.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Force the task to be reconfigured from its RuntimeConfigurable.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A></B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the target container of this task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A></B>(<A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A>&nbsp;wrapper)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the wrapper to be used for runtime configuration.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the name to use in logging messages.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></B>(java.lang.String&nbsp;type)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the name with which the task has been invoked.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="target"><!-- --></A><H3>
target</H3>
<PRE>
protected <A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A> <B>target</B></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <A HREF="../../../../org/apache/tools/ant/Task.html#getOwningTarget()"><CODE>getOwningTarget()</CODE></A> method.</I><DD>Target this task belongs to, if any.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="taskName"><!-- --></A><H3>
taskName</H3>
<PRE>
protected java.lang.String <B>taskName</B></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <A HREF="../../../../org/apache/tools/ant/Task.html#getTaskName()"><CODE>getTaskName()</CODE></A> method.</I><DD>Name of this task to be used for logging purposes.
 This defaults to the same as the type, but may be
 overridden by the Employee. For instance, the name "java"
 isn't terribly descriptive for a task used within
 another task - the outer task code can probably
 provide a better one.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="taskType"><!-- --></A><H3>
taskType</H3>
<PRE>
protected java.lang.String <B>taskType</B></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <A HREF="../../../../org/apache/tools/ant/Task.html#getTaskType()"><CODE>getTaskType()</CODE></A> method.</I><DD>Type of this task.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="wrapper"><!-- --></A><H3>
wrapper</H3>
<PRE>
protected <A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A> <B>wrapper</B></PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <A HREF="../../../../org/apache/tools/ant/Task.html#getWrapper()"><CODE>getWrapper()</CODE></A> method.</I><DD>Wrapper for this object, used to configure it at runtime.
<P>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Task()"><!-- --></A><H3>
Task</H3>
<PRE>
public <B>Task</B>()</PRE>
<DL>
<DD>Sole constructor.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setOwningTarget(org.apache.tools.ant.Target)"><!-- --></A><H3>
setOwningTarget</H3>
<PRE>
public void <B>setOwningTarget</B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target)</PRE>
<DL>
<DD>Sets the target container of this task.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>target</CODE> - Target in whose scope this task belongs.
               May be <code>null</code>, indicating a top-level task.</DL>
</DD>
</DL>
<HR>

<A NAME="getOwningTarget()"><!-- --></A><H3>
getOwningTarget</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A> <B>getOwningTarget</B>()</PRE>
<DL>
<DD>Returns the container target of this task.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>The target containing this task, or <code>null</code> if
         this task is a top-level task.</DL>
</DD>
</DL>
<HR>

<A NAME="setTaskName(java.lang.String)"><!-- --></A><H3>
setTaskName</H3>
<PRE>
public void <B>setTaskName</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Sets the name to use in logging messages.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - The name to use in logging messages.
             Should not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="getTaskName()"><!-- --></A><H3>
getTaskName</H3>
<PRE>
public java.lang.String <B>getTaskName</B>()</PRE>
<DL>
<DD>Returns the name to use in logging messages.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the name to use in logging messages.</DL>
</DD>
</DL>
<HR>

<A NAME="setTaskType(java.lang.String)"><!-- --></A><H3>
setTaskType</H3>
<PRE>
public void <B>setTaskType</B>(java.lang.String&nbsp;type)</PRE>
<DL>
<DD>Sets the name with which the task has been invoked.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>type</CODE> - The name the task has been invoked as.
             Should not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="init()"><!-- --></A><H3>
init</H3>
<PRE>
public void <B>init</B>()
          throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Called by the project to let the task initialize properly.
 The default implementation is a no-op.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if something goes wrong with the build</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Called by the project to let the task do its work. This method may be
 called more than once, if the task is invoked more than once.
 For example,
 if target1 and target2 both depend on target3, then running
 "ant target1 target2" will run all tasks in target3 twice.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if something goes wrong with the build.</DL>
</DD>
</DL>
<HR>

<A NAME="getRuntimeConfigurableWrapper()"><!-- --></A><H3>
getRuntimeConfigurableWrapper</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A> <B>getRuntimeConfigurableWrapper</B>()</PRE>
<DL>
<DD>Returns the wrapper used for runtime configuration.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the wrapper used for runtime configuration. This
         method will generate a new wrapper (and cache it)
         if one isn't set already.</DL>
</DD>
</DL>
<HR>

<A NAME="setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)"><!-- --></A><H3>
setRuntimeConfigurableWrapper</H3>
<PRE>
public void <B>setRuntimeConfigurableWrapper</B>(<A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A>&nbsp;wrapper)</PRE>
<DL>
<DD>Sets the wrapper to be used for runtime configuration.

 This method should be used only by the ProjectHelper and Ant internals.
 It is public to allow helper plugins to operate on tasks, normal tasks
 should never use it.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>wrapper</CODE> - The wrapper to be used for runtime configuration.
                May be <code>null</code>, in which case the next call
                to getRuntimeConfigurableWrapper will generate a new
                wrapper.</DL>
</DD>
</DL>
<HR>

<A NAME="maybeConfigure()"><!-- --></A><H3>
maybeConfigure</H3>
<PRE>
public void <B>maybeConfigure</B>()
                    throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Configures this task - if it hasn't been done already.
 If the task has been invalidated, it is replaced with an
 UnknownElement task which uses the new definition in the project.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the task cannot be configured.</DL>
</DD>
</DL>
<HR>

<A NAME="reconfigure()"><!-- --></A><H3>
reconfigure</H3>
<PRE>
public void <B>reconfigure</B>()</PRE>
<DL>
<DD>Force the task to be reconfigured from its RuntimeConfigurable.
<P>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="handleOutput(java.lang.String)"><!-- --></A><H3>
handleOutput</H3>
<PRE>
protected void <B>handleOutput</B>(java.lang.String&nbsp;output)</PRE>
<DL>
<DD>Handles output by logging it with the INFO priority.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - The output to log. Should not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="handleFlush(java.lang.String)"><!-- --></A><H3>
handleFlush</H3>
<PRE>
protected void <B>handleFlush</B>(java.lang.String&nbsp;output)</PRE>
<DL>
<DD>Handles output by logging it with the INFO priority.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - The output to log. Should not be <code>null</code>.<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="handleInput(byte[], int, int)"><!-- --></A><H3>
handleInput</H3>
<PRE>
protected int <B>handleInput</B>(byte[]&nbsp;buffer,
                          int&nbsp;offset,
                          int&nbsp;length)
                   throws java.io.IOException</PRE>
<DL>
<DD>Handle an input request by this task.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>buffer</CODE> - the buffer into which data is to be read.<DD><CODE>offset</CODE> - the offset into the buffer at which data is stored.<DD><CODE>length</CODE> - the amount of data to read.
<DT><B>Returns:</B><DD>the number of bytes read.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the data cannot be read.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="handleErrorOutput(java.lang.String)"><!-- --></A><H3>
handleErrorOutput</H3>
<PRE>
protected void <B>handleErrorOutput</B>(java.lang.String&nbsp;output)</PRE>
<DL>
<DD>Handles an error output by logging it with the WARN priority.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - The error output to log. Should not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="handleErrorFlush(java.lang.String)"><!-- --></A><H3>
handleErrorFlush</H3>
<PRE>
protected void <B>handleErrorFlush</B>(java.lang.String&nbsp;output)</PRE>
<DL>
<DD>Handles an error line by logging it with the WARN priority.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - The error output to log. Should not be <code>null</code>.<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="log(java.lang.String)"><!-- --></A><H3>
log</H3>
<PRE>
public void <B>log</B>(java.lang.String&nbsp;msg)</PRE>
<DL>
<DD>Logs a message with the default (INFO) priority.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String)">log</A></CODE> in class <CODE><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>msg</CODE> - The message to be logged. Should not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="log(java.lang.String, int)"><!-- --></A><H3>
log</H3>
<PRE>
public void <B>log</B>(java.lang.String&nbsp;msg,
                int&nbsp;msgLevel)</PRE>
<DL>
<DD>Logs a message with the given priority. This delegates
 the actual logging to the project.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String, int)">log</A></CODE> in class <CODE><A HREF="../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>msg</CODE> - The message to be logged. Should not be <code>null</code>.<DD><CODE>msgLevel</CODE> - The message priority at which this message is to
                 be logged.</DL>
</DD>
</DL>
<HR>

<A NAME="log(java.lang.Throwable, int)"><!-- --></A><H3>
log</H3>
<PRE>
public void <B>log</B>(java.lang.Throwable&nbsp;t,
                int&nbsp;msgLevel)</PRE>
<DL>
<DD>Logs a message with the given priority. This delegates
 the actual logging to the project.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>t</CODE> - The exception to be logged. Should not be <code>null</code>.<DD><CODE>msgLevel</CODE> - The message priority at which this message is to
                 be logged.<DT><B>Since:</B></DT>
  <DD>1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="log(java.lang.String, java.lang.Throwable, int)"><!-- --></A><H3>
log</H3>
<PRE>
public void <B>log</B>(java.lang.String&nbsp;msg,
                java.lang.Throwable&nbsp;t,
                int&nbsp;msgLevel)</PRE>
<DL>
<DD>Logs a message with the given priority. This delegates
 the actual logging to the project.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>msg</CODE> - The message to be logged. Should not be <code>null</code>.<DD><CODE>t</CODE> - The exception to be logged. May be <code>null</code>.<DD><CODE>msgLevel</CODE> - The message priority at which this message is to
                 be logged.<DT><B>Since:</B></DT>
  <DD>1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="perform()"><!-- --></A><H3>
perform</H3>
<PRE>
public final void <B>perform</B>()</PRE>
<DL>
<DD>Performs this task if it's still valid, or gets a replacement
 version and performs that otherwise.

 Performing a task consists of firing a task started event,
 configuring the task, executing it, and then firing task finished
 event. If a runtime exception is thrown, the task finished event
 is still fired, but with the exception as the cause.
<P>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="isInvalid()"><!-- --></A><H3>
isInvalid</H3>
<PRE>
protected final boolean <B>isInvalid</B>()</PRE>
<DL>
<DD>Has this task been marked invalid?
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true if this task is no longer valid. A new task should be
 configured in this case.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getTaskType()"><!-- --></A><H3>
getTaskType</H3>
<PRE>
public java.lang.String <B>getTaskType</B>()</PRE>
<DL>
<DD>Return the type of task.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the type of task.</DL>
</DD>
</DL>
<HR>

<A NAME="getWrapper()"><!-- --></A><H3>
getWrapper</H3>
<PRE>
protected <A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A> <B>getWrapper</B>()</PRE>
<DL>
<DD>Return the runtime configurable structure for this task.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the runtime structure for this task.</DL>
</DD>
</DL>
<HR>

<A NAME="bindToOwner(org.apache.tools.ant.Task)"><!-- --></A><H3>
bindToOwner</H3>
<PRE>
public final void <B>bindToOwner</B>(<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;owner)</PRE>
<DL>
<DD>Bind a task to another; use this when configuring a newly created
 task to do work on behalf of another.
 Project, OwningTarget, TaskName, Location and Description are all copied

 Important: this method does not call <A HREF="../../../../org/apache/tools/ant/Task.html#init()"><CODE>init()</CODE></A>.
 If you are creating a task to delegate work to, call <A HREF="../../../../org/apache/tools/ant/Task.html#init()"><CODE>init()</CODE></A>
 to initialize it.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>owner</CODE> - owning target<DT><B>Since:</B></DT>
  <DD>Ant1.7</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/TaskAdapter.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/Task.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Task.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
