<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:34 EST 2006 -->
<TITLE>
TarConstants (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.tar.TarConstants interface">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="TarConstants (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarBuffer.html" title="class in org.apache.tools.tar"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/tar/TarConstants.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="TarConstants.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;METHOD</FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;METHOD</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.tar</FONT>
<BR>
Interface TarConstants</H2>
<DL>
<DT><B>All Known Implementing Classes:</B> <DD><A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public interface <B>TarConstants</B></DL>
</PRE>

<P>
This interface contains all the definitions used in the package.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#CHKSUMLEN">CHKSUMLEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The length of the checksum field in a header buffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#DEVLEN">DEVLEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The length of the devices field in a header buffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#GIDLEN">GIDLEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The length of the group id field in a header buffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#GNAMELEN">GNAMELEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The length of the group name field in a header buffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#GNU_LONGLINK">GNU_LONGLINK</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The namr of the GNU tar entry which contains a long name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#GNU_TMAGIC">GNU_TMAGIC</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The magic tag representing a GNU tar archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;byte</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_BLK">LF_BLK</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Block device file type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;byte</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_CHR">LF_CHR</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Character device file type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;byte</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_CONTIG">LF_CONTIG</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Contiguous file type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;byte</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_DIR">LF_DIR</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Directory file type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;byte</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_FIFO">LF_FIFO</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;FIFO (pipe) file type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;byte</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_GNUTYPE_LONGNAME">LF_GNUTYPE_LONGNAME</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Identifies the *next* file on the tape as having a long name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;byte</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_LINK">LF_LINK</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Link file type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;byte</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_NORMAL">LF_NORMAL</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Normal file type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;byte</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_OLDNORM">LF_OLDNORM</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;LF_ constants represent the "link flag" of an entry, or more commonly,
 the "entry type".</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;byte</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_SYMLINK">LF_SYMLINK</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Symbolic link file type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#MAGICLEN">MAGICLEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The length of the magic field in a header buffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#MAXSIZE">MAXSIZE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The maximum size of a file in a tar archive (That's 11 sevens, octal).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#MODELEN">MODELEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The length of the mode field in a header buffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#MODTIMELEN">MODTIMELEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The length of the modification time field in a header buffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#NAMELEN">NAMELEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The length of the name field in a header buffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#SIZELEN">SIZELEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The length of the size field in a header buffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#TMAGIC">TMAGIC</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The magic tag representing a POSIX tar archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#UIDLEN">UIDLEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The length of the Employee id field in a header buffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarConstants.html#UNAMELEN">UNAMELEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The length of the Employee name field in a header buffer.</TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="NAMELEN"><!-- --></A><H3>
NAMELEN</H3>
<PRE>
static final int <B>NAMELEN</B></PRE>
<DL>
<DD>The length of the name field in a header buffer.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.NAMELEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="MODELEN"><!-- --></A><H3>
MODELEN</H3>
<PRE>
static final int <B>MODELEN</B></PRE>
<DL>
<DD>The length of the mode field in a header buffer.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.MODELEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="UIDLEN"><!-- --></A><H3>
UIDLEN</H3>
<PRE>
static final int <B>UIDLEN</B></PRE>
<DL>
<DD>The length of the Employee id field in a header buffer.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.UIDLEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="GIDLEN"><!-- --></A><H3>
GIDLEN</H3>
<PRE>
static final int <B>GIDLEN</B></PRE>
<DL>
<DD>The length of the group id field in a header buffer.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.GIDLEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CHKSUMLEN"><!-- --></A><H3>
CHKSUMLEN</H3>
<PRE>
static final int <B>CHKSUMLEN</B></PRE>
<DL>
<DD>The length of the checksum field in a header buffer.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.CHKSUMLEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="SIZELEN"><!-- --></A><H3>
SIZELEN</H3>
<PRE>
static final int <B>SIZELEN</B></PRE>
<DL>
<DD>The length of the size field in a header buffer.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.SIZELEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="MAXSIZE"><!-- --></A><H3>
MAXSIZE</H3>
<PRE>
static final long <B>MAXSIZE</B></PRE>
<DL>
<DD>The maximum size of a file in a tar archive (That's 11 sevens, octal).
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.MAXSIZE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="MAGICLEN"><!-- --></A><H3>
MAGICLEN</H3>
<PRE>
static final int <B>MAGICLEN</B></PRE>
<DL>
<DD>The length of the magic field in a header buffer.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.MAGICLEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="MODTIMELEN"><!-- --></A><H3>
MODTIMELEN</H3>
<PRE>
static final int <B>MODTIMELEN</B></PRE>
<DL>
<DD>The length of the modification time field in a header buffer.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.MODTIMELEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="UNAMELEN"><!-- --></A><H3>
UNAMELEN</H3>
<PRE>
static final int <B>UNAMELEN</B></PRE>
<DL>
<DD>The length of the Employee name field in a header buffer.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.UNAMELEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="GNAMELEN"><!-- --></A><H3>
GNAMELEN</H3>
<PRE>
static final int <B>GNAMELEN</B></PRE>
<DL>
<DD>The length of the group name field in a header buffer.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.GNAMELEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEVLEN"><!-- --></A><H3>
DEVLEN</H3>
<PRE>
static final int <B>DEVLEN</B></PRE>
<DL>
<DD>The length of the devices field in a header buffer.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.DEVLEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LF_OLDNORM"><!-- --></A><H3>
LF_OLDNORM</H3>
<PRE>
static final byte <B>LF_OLDNORM</B></PRE>
<DL>
<DD>LF_ constants represent the "link flag" of an entry, or more commonly,
 the "entry type". This is the "old way" of indicating a normal file.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_OLDNORM">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LF_NORMAL"><!-- --></A><H3>
LF_NORMAL</H3>
<PRE>
static final byte <B>LF_NORMAL</B></PRE>
<DL>
<DD>Normal file type.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_NORMAL">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LF_LINK"><!-- --></A><H3>
LF_LINK</H3>
<PRE>
static final byte <B>LF_LINK</B></PRE>
<DL>
<DD>Link file type.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_LINK">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LF_SYMLINK"><!-- --></A><H3>
LF_SYMLINK</H3>
<PRE>
static final byte <B>LF_SYMLINK</B></PRE>
<DL>
<DD>Symbolic link file type.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_SYMLINK">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LF_CHR"><!-- --></A><H3>
LF_CHR</H3>
<PRE>
static final byte <B>LF_CHR</B></PRE>
<DL>
<DD>Character device file type.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_CHR">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LF_BLK"><!-- --></A><H3>
LF_BLK</H3>
<PRE>
static final byte <B>LF_BLK</B></PRE>
<DL>
<DD>Block device file type.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_BLK">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LF_DIR"><!-- --></A><H3>
LF_DIR</H3>
<PRE>
static final byte <B>LF_DIR</B></PRE>
<DL>
<DD>Directory file type.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_DIR">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LF_FIFO"><!-- --></A><H3>
LF_FIFO</H3>
<PRE>
static final byte <B>LF_FIFO</B></PRE>
<DL>
<DD>FIFO (pipe) file type.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_FIFO">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LF_CONTIG"><!-- --></A><H3>
LF_CONTIG</H3>
<PRE>
static final byte <B>LF_CONTIG</B></PRE>
<DL>
<DD>Contiguous file type.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_CONTIG">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="TMAGIC"><!-- --></A><H3>
TMAGIC</H3>
<PRE>
static final java.lang.String <B>TMAGIC</B></PRE>
<DL>
<DD>The magic tag representing a POSIX tar archive.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.TMAGIC">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="GNU_TMAGIC"><!-- --></A><H3>
GNU_TMAGIC</H3>
<PRE>
static final java.lang.String <B>GNU_TMAGIC</B></PRE>
<DL>
<DD>The magic tag representing a GNU tar archive.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.GNU_TMAGIC">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="GNU_LONGLINK"><!-- --></A><H3>
GNU_LONGLINK</H3>
<PRE>
static final java.lang.String <B>GNU_LONGLINK</B></PRE>
<DL>
<DD>The namr of the GNU tar entry which contains a long name.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.GNU_LONGLINK">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LF_GNUTYPE_LONGNAME"><!-- --></A><H3>
LF_GNUTYPE_LONGNAME</H3>
<PRE>
static final byte <B>LF_GNUTYPE_LONGNAME</B></PRE>
<DL>
<DD>Identifies the *next* file on the tape as having a long name.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarConstants.LF_GNUTYPE_LONGNAME">Constant Field Values</A></DL>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarBuffer.html" title="class in org.apache.tools.tar"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/tar/TarConstants.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="TarConstants.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;METHOD</FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;METHOD</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
