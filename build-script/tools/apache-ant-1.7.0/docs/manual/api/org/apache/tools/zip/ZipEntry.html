<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:34 EST 2006 -->
<TITLE>
ZipEntry (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.zip.ZipEntry class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="ZipEntry (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/zip/UnrecognizedExtraField.html" title="class in org.apache.tools.zip"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/zip/ZipEntry.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ZipEntry.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.zip</FONT>
<BR>
Class ZipEntry</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by ">java.util.zip.ZipEntry
      <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.zip.ZipEntry</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>ZipEntry</B><DT>extends java.util.zip.ZipEntry<DT>implements java.lang.Cloneable</DL>
</PRE>

<P>
Extension that adds better handling of extra fields and provides
 access to the internal and external file attributes.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENATT">CENATT</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENATX">CENATX</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENCOM">CENCOM</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENCRC">CENCRC</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENDSK">CENDSK</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENEXT">CENEXT</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENFLG">CENFLG</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENHDR">CENHDR</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENHOW">CENHOW</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENLEN">CENLEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENNAM">CENNAM</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENOFF">CENOFF</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENSIG">CENSIG</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENSIZ">CENSIZ</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENTIM">CENTIM</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENVEM">CENVEM</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#CENVER">CENVER</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#ENDCOM">ENDCOM</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#ENDHDR">ENDHDR</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#ENDOFF">ENDOFF</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#ENDSIG">ENDSIG</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#ENDSIZ">ENDSIZ</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#ENDSUB">ENDSUB</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#ENDTOT">ENDTOT</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#EXTCRC">EXTCRC</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#EXTHDR">EXTHDR</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#EXTLEN">EXTLEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#EXTSIG">EXTSIG</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#EXTSIZ">EXTSIZ</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#LOCCRC">LOCCRC</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#LOCEXT">LOCEXT</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#LOCFLG">LOCFLG</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#LOCHDR">LOCHDR</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#LOCHOW">LOCHOW</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#LOCLEN">LOCLEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#LOCNAM">LOCNAM</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#LOCSIG">LOCSIG</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#LOCSIZ">LOCSIZ</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#LOCTIM">LOCTIM</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#LOCVER">LOCVER</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_java.util.zip.ZipEntry"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class java.util.zip.ZipEntry</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>DEFLATED, STORED</CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected </CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#ZipEntry()">ZipEntry</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#ZipEntry(java.lang.String)">ZipEntry</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new zip entry with the specified name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#ZipEntry(java.util.zip.ZipEntry)">ZipEntry</A></B>(java.util.zip.ZipEntry&nbsp;entry)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new zip entry with fields taken from the specified zip entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#ZipEntry(org.apache.tools.zip.ZipEntry)">ZipEntry</A></B>(<A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</A>&nbsp;entry)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new zip entry with fields taken from the specified zip entry.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#addExtraField(org.apache.tools.zip.ZipExtraField)">addExtraField</A></B>(<A HREF="../../../../org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</A>&nbsp;ze)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds an extra fields - replacing an already present extra field
 of the same type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#clone()">clone</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Overwrite clone.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#equals(java.lang.Object)">equals</A></B>(java.lang.Object&nbsp;o)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The equality method.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#getCentralDirectoryExtra()">getCentralDirectoryExtra</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Retrieves the extra data for the central directory.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#getExternalAttributes()">getExternalAttributes</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Retrieves the external file attributes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</A>[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#getExtraFields()">getExtraFields</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Retrieves extra fields.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#getInternalAttributes()">getInternalAttributes</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Retrieves the internal file attributes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#getLocalFileDataExtra()">getLocalFileDataExtra</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Retrieves the extra data for the local file data.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#getName()">getName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the name of the entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#getPlatform()">getPlatform</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Platform specification to put into the &quot;version made
 by&quot; part of the central file header.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#getUnixMode()">getUnixMode</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Unix permission.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#hashCode()">hashCode</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the hashCode of the entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#isDirectory()">isDirectory</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Is this entry a directory?</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#removeExtraField(org.apache.tools.zip.ZipShort)">removeExtraField</A></B>(<A HREF="../../../../org/apache/tools/zip/ZipShort.html" title="class in org.apache.tools.zip">ZipShort</A>&nbsp;type)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Remove an extra fields.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#setComprSize(long)">setComprSize</A></B>(long&nbsp;size)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.7.
             Use setCompressedSize directly.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#setExternalAttributes(long)">setExternalAttributes</A></B>(long&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the external file attributes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#setExtra()">setExtra</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Unfortunately <CODE>java.util.zip.ZipOutputStream</CODE> seems to access the extra data
 directly, so overriding getExtra doesn't help - we need to
 modify super's data directly.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#setExtra(byte[])">setExtra</A></B>(byte[]&nbsp;extra)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Throws an Exception if extra data cannot be parsed into extra fields.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#setExtraFields(org.apache.tools.zip.ZipExtraField[])">setExtraFields</A></B>(<A HREF="../../../../org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</A>[]&nbsp;fields)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Replaces all currently attached extra fields with the new array.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#setInternalAttributes(int)">setInternalAttributes</A></B>(int&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the internal file attributes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#setName(java.lang.String)">setName</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the name of the entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#setPlatform(int)">setPlatform</A></B>(int&nbsp;platform)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the platform (UNIX or FAT).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/zip/ZipEntry.html#setUnixMode(int)">setUnixMode</A></B>(int&nbsp;mode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets Unix permissions in a way that is understood by Info-Zip's
 unzip command.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.util.zip.ZipEntry"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.util.zip.ZipEntry</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>getComment, getCompressedSize, getCrc, getExtra, getMethod, getSize, getTime, setComment, setCompressedSize, setCrc, setMethod, setSize, setTime, toString</CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>finalize, getClass, notify, notifyAll, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="LOCSIG"><!-- --></A><H3>
LOCSIG</H3>
<PRE>
public static final long <B>LOCSIG</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCSIG">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="EXTSIG"><!-- --></A><H3>
EXTSIG</H3>
<PRE>
public static final long <B>EXTSIG</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.EXTSIG">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENSIG"><!-- --></A><H3>
CENSIG</H3>
<PRE>
public static final long <B>CENSIG</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENSIG">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ENDSIG"><!-- --></A><H3>
ENDSIG</H3>
<PRE>
public static final long <B>ENDSIG</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.ENDSIG">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LOCHDR"><!-- --></A><H3>
LOCHDR</H3>
<PRE>
public static final int <B>LOCHDR</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCHDR">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="EXTHDR"><!-- --></A><H3>
EXTHDR</H3>
<PRE>
public static final int <B>EXTHDR</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.EXTHDR">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENHDR"><!-- --></A><H3>
CENHDR</H3>
<PRE>
public static final int <B>CENHDR</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENHDR">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ENDHDR"><!-- --></A><H3>
ENDHDR</H3>
<PRE>
public static final int <B>ENDHDR</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.ENDHDR">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LOCVER"><!-- --></A><H3>
LOCVER</H3>
<PRE>
public static final int <B>LOCVER</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCVER">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LOCFLG"><!-- --></A><H3>
LOCFLG</H3>
<PRE>
public static final int <B>LOCFLG</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCFLG">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LOCHOW"><!-- --></A><H3>
LOCHOW</H3>
<PRE>
public static final int <B>LOCHOW</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCHOW">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LOCTIM"><!-- --></A><H3>
LOCTIM</H3>
<PRE>
public static final int <B>LOCTIM</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCTIM">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LOCCRC"><!-- --></A><H3>
LOCCRC</H3>
<PRE>
public static final int <B>LOCCRC</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCCRC">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LOCSIZ"><!-- --></A><H3>
LOCSIZ</H3>
<PRE>
public static final int <B>LOCSIZ</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCSIZ">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LOCLEN"><!-- --></A><H3>
LOCLEN</H3>
<PRE>
public static final int <B>LOCLEN</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCLEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LOCNAM"><!-- --></A><H3>
LOCNAM</H3>
<PRE>
public static final int <B>LOCNAM</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCNAM">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LOCEXT"><!-- --></A><H3>
LOCEXT</H3>
<PRE>
public static final int <B>LOCEXT</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.LOCEXT">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="EXTCRC"><!-- --></A><H3>
EXTCRC</H3>
<PRE>
public static final int <B>EXTCRC</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.EXTCRC">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="EXTSIZ"><!-- --></A><H3>
EXTSIZ</H3>
<PRE>
public static final int <B>EXTSIZ</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.EXTSIZ">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="EXTLEN"><!-- --></A><H3>
EXTLEN</H3>
<PRE>
public static final int <B>EXTLEN</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.EXTLEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENVEM"><!-- --></A><H3>
CENVEM</H3>
<PRE>
public static final int <B>CENVEM</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENVEM">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENVER"><!-- --></A><H3>
CENVER</H3>
<PRE>
public static final int <B>CENVER</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENVER">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENFLG"><!-- --></A><H3>
CENFLG</H3>
<PRE>
public static final int <B>CENFLG</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENFLG">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENHOW"><!-- --></A><H3>
CENHOW</H3>
<PRE>
public static final int <B>CENHOW</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENHOW">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENTIM"><!-- --></A><H3>
CENTIM</H3>
<PRE>
public static final int <B>CENTIM</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENTIM">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENCRC"><!-- --></A><H3>
CENCRC</H3>
<PRE>
public static final int <B>CENCRC</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENCRC">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENSIZ"><!-- --></A><H3>
CENSIZ</H3>
<PRE>
public static final int <B>CENSIZ</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENSIZ">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENLEN"><!-- --></A><H3>
CENLEN</H3>
<PRE>
public static final int <B>CENLEN</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENLEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENNAM"><!-- --></A><H3>
CENNAM</H3>
<PRE>
public static final int <B>CENNAM</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENNAM">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENEXT"><!-- --></A><H3>
CENEXT</H3>
<PRE>
public static final int <B>CENEXT</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENEXT">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENCOM"><!-- --></A><H3>
CENCOM</H3>
<PRE>
public static final int <B>CENCOM</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENCOM">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENDSK"><!-- --></A><H3>
CENDSK</H3>
<PRE>
public static final int <B>CENDSK</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENDSK">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENATT"><!-- --></A><H3>
CENATT</H3>
<PRE>
public static final int <B>CENATT</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENATT">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENATX"><!-- --></A><H3>
CENATX</H3>
<PRE>
public static final int <B>CENATX</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENATX">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="CENOFF"><!-- --></A><H3>
CENOFF</H3>
<PRE>
public static final int <B>CENOFF</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.CENOFF">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ENDSUB"><!-- --></A><H3>
ENDSUB</H3>
<PRE>
public static final int <B>ENDSUB</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.ENDSUB">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ENDTOT"><!-- --></A><H3>
ENDTOT</H3>
<PRE>
public static final int <B>ENDTOT</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.ENDTOT">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ENDSIZ"><!-- --></A><H3>
ENDSIZ</H3>
<PRE>
public static final int <B>ENDSIZ</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.ENDSIZ">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ENDOFF"><!-- --></A><H3>
ENDOFF</H3>
<PRE>
public static final int <B>ENDOFF</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.ENDOFF">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ENDCOM"><!-- --></A><H3>
ENDCOM</H3>
<PRE>
public static final int <B>ENDCOM</B></PRE>
<DL>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.zip.ZipEntry.ENDCOM">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ZipEntry(java.lang.String)"><!-- --></A><H3>
ZipEntry</H3>
<PRE>
public <B>ZipEntry</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Creates a new zip entry with the specified name.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the name of the entry<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DL>
<HR>

<A NAME="ZipEntry(java.util.zip.ZipEntry)"><!-- --></A><H3>
ZipEntry</H3>
<PRE>
public <B>ZipEntry</B>(java.util.zip.ZipEntry&nbsp;entry)
         throws java.util.zip.ZipException</PRE>
<DL>
<DD>Creates a new zip entry with fields taken from the specified zip entry.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>entry</CODE> - the entry to get fields from
<DT><B>Throws:</B>
<DD><CODE>java.util.zip.ZipException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DL>
<HR>

<A NAME="ZipEntry(org.apache.tools.zip.ZipEntry)"><!-- --></A><H3>
ZipEntry</H3>
<PRE>
public <B>ZipEntry</B>(<A HREF="../../../../org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</A>&nbsp;entry)
         throws java.util.zip.ZipException</PRE>
<DL>
<DD>Creates a new zip entry with fields taken from the specified zip entry.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>entry</CODE> - the entry to get fields from
<DT><B>Throws:</B>
<DD><CODE>java.util.zip.ZipException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DL>
<HR>

<A NAME="ZipEntry()"><!-- --></A><H3>
ZipEntry</H3>
<PRE>
protected <B>ZipEntry</B>()</PRE>
<DL>
<DL>
<DT><B>Since:</B></DT>
  <DD>1.9</DD>
</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="clone()"><!-- --></A><H3>
clone</H3>
<PRE>
public java.lang.Object <B>clone</B>()</PRE>
<DL>
<DD>Overwrite clone.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>clone</CODE> in class <CODE>java.util.zip.ZipEntry</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a cloned copy of this ZipEntry<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getInternalAttributes()"><!-- --></A><H3>
getInternalAttributes</H3>
<PRE>
public int <B>getInternalAttributes</B>()</PRE>
<DL>
<DD>Retrieves the internal file attributes.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the internal file attributes<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setInternalAttributes(int)"><!-- --></A><H3>
setInternalAttributes</H3>
<PRE>
public void <B>setInternalAttributes</B>(int&nbsp;value)</PRE>
<DL>
<DD>Sets the internal file attributes.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - an <code>int</code> value<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getExternalAttributes()"><!-- --></A><H3>
getExternalAttributes</H3>
<PRE>
public long <B>getExternalAttributes</B>()</PRE>
<DL>
<DD>Retrieves the external file attributes.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the external file attributes<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setExternalAttributes(long)"><!-- --></A><H3>
setExternalAttributes</H3>
<PRE>
public void <B>setExternalAttributes</B>(long&nbsp;value)</PRE>
<DL>
<DD>Sets the external file attributes.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - an <code>long</code> value<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setUnixMode(int)"><!-- --></A><H3>
setUnixMode</H3>
<PRE>
public void <B>setUnixMode</B>(int&nbsp;mode)</PRE>
<DL>
<DD>Sets Unix permissions in a way that is understood by Info-Zip's
 unzip command.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>mode</CODE> - an <code>int</code> value<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getUnixMode()"><!-- --></A><H3>
getUnixMode</H3>
<PRE>
public int <B>getUnixMode</B>()</PRE>
<DL>
<DD>Unix permission.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the unix permissions<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getPlatform()"><!-- --></A><H3>
getPlatform</H3>
<PRE>
public int <B>getPlatform</B>()</PRE>
<DL>
<DD>Platform specification to put into the &quot;version made
 by&quot; part of the central file header.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>0 (MS-DOS FAT) unless <A HREF="../../../../org/apache/tools/zip/ZipEntry.html#setUnixMode(int)"><CODE>setUnixMode</CODE></A>
 has been called, in which case 3 (Unix) will be returned.<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setPlatform(int)"><!-- --></A><H3>
setPlatform</H3>
<PRE>
protected void <B>setPlatform</B>(int&nbsp;platform)</PRE>
<DL>
<DD>Set the platform (UNIX or FAT).
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>platform</CODE> - an <code>int</code> value - 0 is FAT, 3 is UNIX<DT><B>Since:</B></DT>
  <DD>1.9</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setExtraFields(org.apache.tools.zip.ZipExtraField[])"><!-- --></A><H3>
setExtraFields</H3>
<PRE>
public void <B>setExtraFields</B>(<A HREF="../../../../org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</A>[]&nbsp;fields)</PRE>
<DL>
<DD>Replaces all currently attached extra fields with the new array.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fields</CODE> - an array of extra fields<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getExtraFields()"><!-- --></A><H3>
getExtraFields</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</A>[] <B>getExtraFields</B>()</PRE>
<DL>
<DD>Retrieves extra fields.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>an array of the extra fields<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addExtraField(org.apache.tools.zip.ZipExtraField)"><!-- --></A><H3>
addExtraField</H3>
<PRE>
public void <B>addExtraField</B>(<A HREF="../../../../org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip">ZipExtraField</A>&nbsp;ze)</PRE>
<DL>
<DD>Adds an extra fields - replacing an already present extra field
 of the same type.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ze</CODE> - an extra field<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="removeExtraField(org.apache.tools.zip.ZipShort)"><!-- --></A><H3>
removeExtraField</H3>
<PRE>
public void <B>removeExtraField</B>(<A HREF="../../../../org/apache/tools/zip/ZipShort.html" title="class in org.apache.tools.zip">ZipShort</A>&nbsp;type)</PRE>
<DL>
<DD>Remove an extra fields.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>type</CODE> - the type of extra field to remove<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setExtra(byte[])"><!-- --></A><H3>
setExtra</H3>
<PRE>
public void <B>setExtra</B>(byte[]&nbsp;extra)
              throws java.lang.RuntimeException</PRE>
<DL>
<DD>Throws an Exception if extra data cannot be parsed into extra fields.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>setExtra</CODE> in class <CODE>java.util.zip.ZipEntry</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>extra</CODE> - an array of bytes to be parsed into extra fields
<DT><B>Throws:</B>
<DD><CODE>java.lang.RuntimeException</CODE> - if the bytes cannot be parsed
<DD><CODE>java.lang.RuntimeException</CODE> - on error<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setExtra()"><!-- --></A><H3>
setExtra</H3>
<PRE>
protected void <B>setExtra</B>()</PRE>
<DL>
<DD>Unfortunately <CODE>java.util.zip.ZipOutputStream</CODE> seems to access the extra data
 directly, so overriding getExtra doesn't help - we need to
 modify super's data directly.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getLocalFileDataExtra()"><!-- --></A><H3>
getLocalFileDataExtra</H3>
<PRE>
public byte[] <B>getLocalFileDataExtra</B>()</PRE>
<DL>
<DD>Retrieves the extra data for the local file data.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the extra data for local file<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getCentralDirectoryExtra()"><!-- --></A><H3>
getCentralDirectoryExtra</H3>
<PRE>
public byte[] <B>getCentralDirectoryExtra</B>()</PRE>
<DL>
<DD>Retrieves the extra data for the central directory.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the central directory extra data<DT><B>Since:</B></DT>
  <DD>1.1</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setComprSize(long)"><!-- --></A><H3>
setComprSize</H3>
<PRE>
public void <B>setComprSize</B>(long&nbsp;size)</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.7.
             Use setCompressedSize directly.</I>
<P>
<DD>Make this class work in JDK 1.1 like a 1.2 class.

 <p>This either stores the size for later usage or invokes
 setCompressedSize via reflection.</p>
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>size</CODE> - the size to use<DT><B>Since:</B></DT>
  <DD>1.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getName()"><!-- --></A><H3>
getName</H3>
<PRE>
public java.lang.String <B>getName</B>()</PRE>
<DL>
<DD>Get the name of the entry.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>getName</CODE> in class <CODE>java.util.zip.ZipEntry</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the entry name<DT><B>Since:</B></DT>
  <DD>1.9</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="isDirectory()"><!-- --></A><H3>
isDirectory</H3>
<PRE>
public boolean <B>isDirectory</B>()</PRE>
<DL>
<DD>Is this entry a directory?
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>isDirectory</CODE> in class <CODE>java.util.zip.ZipEntry</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>true if the entry is a directory<DT><B>Since:</B></DT>
  <DD>1.10</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setName(java.lang.String)"><!-- --></A><H3>
setName</H3>
<PRE>
protected void <B>setName</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Set the name of the entry.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the name to use</DL>
</DD>
</DL>
<HR>

<A NAME="hashCode()"><!-- --></A><H3>
hashCode</H3>
<PRE>
public int <B>hashCode</B>()</PRE>
<DL>
<DD>Get the hashCode of the entry.
 This uses the name as the hashcode.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>hashCode</CODE> in class <CODE>java.util.zip.ZipEntry</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a hashcode.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="equals(java.lang.Object)"><!-- --></A><H3>
equals</H3>
<PRE>
public boolean <B>equals</B>(java.lang.Object&nbsp;o)</PRE>
<DL>
<DD>The equality method. In this case, the implementation returns 'this == o'
 which is basically the equals method of the Object class.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>equals</CODE> in class <CODE>java.lang.Object</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>o</CODE> - the object to compare to
<DT><B>Returns:</B><DD>true if this object is the same as <code>o</code><DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/zip/UnrecognizedExtraField.html" title="class in org.apache.tools.zip"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/zip/ZipExtraField.html" title="interface in org.apache.tools.zip"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/zip/ZipEntry.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ZipEntry.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
