<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:35 EST 2006 -->
<TITLE>
org.apache.tools.ant (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant package">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">


</HEAD>

<BODY BGCOLOR="white">
<FONT size="+1" CLASS="FrameTitleFont">
<A HREF="../../../../org/apache/tools/ant/package-summary.html" target="classFrame">org.apache.tools.ant</A></FONT>
<TABLE BORDER="0" WIDTH="100%" SUMMARY="">
<TR>
<TD NOWRAP><FONT size="+1" CLASS="FrameHeadingFont">
Interfaces</FONT>&nbsp;
<FONT CLASS="FrameItemFont">
<BR>
<A HREF="BuildListener.html" title="interface in org.apache.tools.ant" target="classFrame"><I>BuildListener</I></A>
<BR>
<A HREF="BuildLogger.html" title="interface in org.apache.tools.ant" target="classFrame"><I>BuildLogger</I></A>
<BR>
<A HREF="DynamicAttribute.html" title="interface in org.apache.tools.ant" target="classFrame"><I>DynamicAttribute</I></A>
<BR>
<A HREF="DynamicAttributeNS.html" title="interface in org.apache.tools.ant" target="classFrame"><I>DynamicAttributeNS</I></A>
<BR>
<A HREF="DynamicConfigurator.html" title="interface in org.apache.tools.ant" target="classFrame"><I>DynamicConfigurator</I></A>
<BR>
<A HREF="DynamicConfiguratorNS.html" title="interface in org.apache.tools.ant" target="classFrame"><I>DynamicConfiguratorNS</I></A>
<BR>
<A HREF="DynamicElement.html" title="interface in org.apache.tools.ant" target="classFrame"><I>DynamicElement</I></A>
<BR>
<A HREF="DynamicElementNS.html" title="interface in org.apache.tools.ant" target="classFrame"><I>DynamicElementNS</I></A>
<BR>
<A HREF="Executor.html" title="interface in org.apache.tools.ant" target="classFrame"><I>Executor</I></A>
<BR>
<A HREF="FileScanner.html" title="interface in org.apache.tools.ant" target="classFrame"><I>FileScanner</I></A>
<BR>
<A HREF="SubBuildListener.html" title="interface in org.apache.tools.ant" target="classFrame"><I>SubBuildListener</I></A>
<BR>
<A HREF="TaskContainer.html" title="interface in org.apache.tools.ant" target="classFrame"><I>TaskContainer</I></A>
<BR>
<A HREF="TypeAdapter.html" title="interface in org.apache.tools.ant" target="classFrame"><I>TypeAdapter</I></A></FONT></TD>
</TR>
</TABLE>


<TABLE BORDER="0" WIDTH="100%" SUMMARY="">
<TR>
<TD NOWRAP><FONT size="+1" CLASS="FrameHeadingFont">
Classes</FONT>&nbsp;
<FONT CLASS="FrameItemFont">
<BR>
<A HREF="AntClassLoader.html" title="class in org.apache.tools.ant" target="classFrame">AntClassLoader</A>
<BR>
<A HREF="AntTypeDefinition.html" title="class in org.apache.tools.ant" target="classFrame">AntTypeDefinition</A>
<BR>
<A HREF="BuildEvent.html" title="class in org.apache.tools.ant" target="classFrame">BuildEvent</A>
<BR>
<A HREF="ComponentHelper.html" title="class in org.apache.tools.ant" target="classFrame">ComponentHelper</A>
<BR>
<A HREF="DefaultLogger.html" title="class in org.apache.tools.ant" target="classFrame">DefaultLogger</A>
<BR>
<A HREF="DemuxInputStream.html" title="class in org.apache.tools.ant" target="classFrame">DemuxInputStream</A>
<BR>
<A HREF="DemuxOutputStream.html" title="class in org.apache.tools.ant" target="classFrame">DemuxOutputStream</A>
<BR>
<A HREF="Diagnostics.html" title="class in org.apache.tools.ant" target="classFrame">Diagnostics</A>
<BR>
<A HREF="DirectoryScanner.html" title="class in org.apache.tools.ant" target="classFrame">DirectoryScanner</A>
<BR>
<A HREF="IntrospectionHelper.html" title="class in org.apache.tools.ant" target="classFrame">IntrospectionHelper</A>
<BR>
<A HREF="IntrospectionHelper.Creator.html" title="class in org.apache.tools.ant" target="classFrame">IntrospectionHelper.Creator</A>
<BR>
<A HREF="Location.html" title="class in org.apache.tools.ant" target="classFrame">Location</A>
<BR>
<A HREF="MagicNames.html" title="class in org.apache.tools.ant" target="classFrame">MagicNames</A>
<BR>
<A HREF="Main.html" title="class in org.apache.tools.ant" target="classFrame">Main</A>
<BR>
<A HREF="NoBannerLogger.html" title="class in org.apache.tools.ant" target="classFrame">NoBannerLogger</A>
<BR>
<A HREF="PathTokenizer.html" title="class in org.apache.tools.ant" target="classFrame">PathTokenizer</A>
<BR>
<A HREF="Project.html" title="class in org.apache.tools.ant" target="classFrame">Project</A>
<BR>
<A HREF="ProjectComponent.html" title="class in org.apache.tools.ant" target="classFrame">ProjectComponent</A>
<BR>
<A HREF="ProjectHelper.html" title="class in org.apache.tools.ant" target="classFrame">ProjectHelper</A>
<BR>
<A HREF="PropertyHelper.html" title="class in org.apache.tools.ant" target="classFrame">PropertyHelper</A>
<BR>
<A HREF="RuntimeConfigurable.html" title="class in org.apache.tools.ant" target="classFrame">RuntimeConfigurable</A>
<BR>
<A HREF="Target.html" title="class in org.apache.tools.ant" target="classFrame">Target</A>
<BR>
<A HREF="Task.html" title="class in org.apache.tools.ant" target="classFrame">Task</A>
<BR>
<A HREF="TaskAdapter.html" title="class in org.apache.tools.ant" target="classFrame">TaskAdapter</A>
<BR>
<A HREF="UnknownElement.html" title="class in org.apache.tools.ant" target="classFrame">UnknownElement</A>
<BR>
<A HREF="XmlLogger.html" title="class in org.apache.tools.ant" target="classFrame">XmlLogger</A></FONT></TD>
</TR>
</TABLE>


<TABLE BORDER="0" WIDTH="100%" SUMMARY="">
<TR>
<TD NOWRAP><FONT size="+1" CLASS="FrameHeadingFont">
Exceptions</FONT>&nbsp;
<FONT CLASS="FrameItemFont">
<BR>
<A HREF="BuildException.html" title="class in org.apache.tools.ant" target="classFrame">BuildException</A>
<BR>
<A HREF="ExitException.html" title="class in org.apache.tools.ant" target="classFrame">ExitException</A>
<BR>
<A HREF="ExitStatusException.html" title="class in org.apache.tools.ant" target="classFrame">ExitStatusException</A>
<BR>
<A HREF="UnsupportedAttributeException.html" title="class in org.apache.tools.ant" target="classFrame">UnsupportedAttributeException</A>
<BR>
<A HREF="UnsupportedElementException.html" title="class in org.apache.tools.ant" target="classFrame">UnsupportedElementException</A></FONT></TD>
</TR>
</TABLE>


</BODY>
</HTML>
