<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:19 EST 2006 -->
<TITLE>
ComponentHelper (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.ComponentHelper class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="ComponentHelper (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/DefaultLogger.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/ComponentHelper.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ComponentHelper.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class ComponentHelper</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.ComponentHelper</B>
</PRE>
<HR>
<DL>
<DT><PRE>public class <B>ComponentHelper</B><DT>extends java.lang.Object</DL>
</PRE>

<P>
Component creation and configuration.

 The class is based around handing component
 definitions in an AntTypeTable.

 The old task/type methods have been kept
 for backward compatibly.
 Project will just delegate its calls to this class.

 A very simple hook mechanism is provided that allows users to plug
 in custom code. It is also possible to replace the default behavior
 ( for example in an app embedding ant )
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant1.6</DD>
</DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#COMPONENT_HELPER_REFERENCE">COMPONENT_HELPER_REFERENCE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;reference under which we register ourselves with a project -"ant.ComponentHelper"</TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected </CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#ComponentHelper()">ComponentHelper</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new ComponentHelper instance.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#addDataTypeDefinition(org.apache.tools.ant.AntTypeDefinition)">addDataTypeDefinition</A></B>(<A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A>&nbsp;def)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Describe <code>addDataTypeDefinition</code> method here.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#addDataTypeDefinition(java.lang.String, java.lang.Class)">addDataTypeDefinition</A></B>(java.lang.String&nbsp;typeName,
                      java.lang.Class&nbsp;typeClass)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a new datatype definition.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#addTaskDefinition(java.lang.String, java.lang.Class)">addTaskDefinition</A></B>(java.lang.String&nbsp;taskName,
                  java.lang.Class&nbsp;taskClass)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a new task definition to the project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#checkTaskClass(java.lang.Class)">checkTaskClass</A></B>(java.lang.Class&nbsp;taskClass)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Checks whether or not a class is suitable for serving as Ant task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#createComponent(java.lang.String)">createComponent</A></B>(java.lang.String&nbsp;componentName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create an object for a component.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#createComponent(org.apache.tools.ant.UnknownElement, java.lang.String, java.lang.String)">createComponent</A></B>(<A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A>&nbsp;ue,
                java.lang.String&nbsp;ns,
                java.lang.String&nbsp;componentType)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Factory method to create the components.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#createDataType(java.lang.String)">createDataType</A></B>(java.lang.String&nbsp;typeName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new instance of a data type.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#createTask(java.lang.String)">createTask</A></B>(java.lang.String&nbsp;taskType)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new instance of a task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#diagnoseCreationFailure(java.lang.String, java.lang.String)">diagnoseCreationFailure</A></B>(java.lang.String&nbsp;componentName,
                        java.lang.String&nbsp;type)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Handler called to do decent diagnosis on instantiation failure.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#enterAntLib(java.lang.String)">enterAntLib</A></B>(java.lang.String&nbsp;uri)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Called at the start of processing an antlib.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#exitAntLib()">exitAntLib</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Called at the end of processing an antlib.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#getAntTypeTable()">getAntTypeTable</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the current datatype definition hashtable.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#getComponentClass(java.lang.String)">getComponentClass</A></B>(java.lang.String&nbsp;componentName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the class of the component name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="../../../../org/apache/tools/ant/ComponentHelper.html" title="class in org.apache.tools.ant">ComponentHelper</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#getComponentHelper(org.apache.tools.ant.Project)">getComponentHelper</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Find a project component for a specific project, creating
 it if it does not exist.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#getCurrentAntlibUri()">getCurrentAntlibUri</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#getDataTypeDefinitions()">getDataTypeDefinitions</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the current type definition hashtable.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#getDefinition(java.lang.String)">getDefinition</A></B>(java.lang.String&nbsp;componentName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the antTypeDefinition for a componentName.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#getElementName(java.lang.Object)">getElementName</A></B>(java.lang.Object&nbsp;element)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns a description of the type of the given element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#getElementName(java.lang.Object, boolean)">getElementName</A></B>(java.lang.Object&nbsp;o,
               boolean&nbsp;brief)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns a description of the type of the given element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#getElementName(org.apache.tools.ant.Project, java.lang.Object, boolean)">getElementName</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p,
               java.lang.Object&nbsp;o,
               boolean&nbsp;brief)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenient way to get some element name even when you may not have a
 Project context.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/ComponentHelper.html" title="class in org.apache.tools.ant">ComponentHelper</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#getNext()">getNext</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the next chained component helper.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#getTaskDefinitions()">getTaskDefinitions</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the current task definition hashtable.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#initDefaultDefinitions()">initDefaultDefinitions</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This method is initialization code implementing the original ant component
 loading from /org/apache/tools/ant/taskdefs/default.properties
 and /org/apache/tools/ant/types/default.properties.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#initSubProject(org.apache.tools.ant.ComponentHelper)">initSubProject</A></B>(<A HREF="../../../../org/apache/tools/ant/ComponentHelper.html" title="class in org.apache.tools.ant">ComponentHelper</A>&nbsp;helper)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Used with creating child projects.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#setNext(org.apache.tools.ant.ComponentHelper)">setNext</A></B>(<A HREF="../../../../org/apache/tools/ant/ComponentHelper.html" title="class in org.apache.tools.ant">ComponentHelper</A>&nbsp;next)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the next chained component helper.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#setProject(org.apache.tools.ant.Project)">setProject</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the project for this component helper.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="COMPONENT_HELPER_REFERENCE"><!-- --></A><H3>
COMPONENT_HELPER_REFERENCE</H3>
<PRE>
public static final java.lang.String <B>COMPONENT_HELPER_REFERENCE</B></PRE>
<DL>
<DD>reference under which we register ourselves with a project -"ant.ComponentHelper"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.ant.ComponentHelper.COMPONENT_HELPER_REFERENCE">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ComponentHelper()"><!-- --></A><H3>
ComponentHelper</H3>
<PRE>
protected <B>ComponentHelper</B>()</PRE>
<DL>
<DD>Creates a new ComponentHelper instance.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="getComponentHelper(org.apache.tools.ant.Project)"><!-- --></A><H3>
getComponentHelper</H3>
<PRE>
public static <A HREF="../../../../org/apache/tools/ant/ComponentHelper.html" title="class in org.apache.tools.ant">ComponentHelper</A> <B>getComponentHelper</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Find a project component for a specific project, creating
 it if it does not exist.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the project.
<DT><B>Returns:</B><DD>the project component for a specific project.</DL>
</DD>
</DL>
<HR>

<A NAME="setNext(org.apache.tools.ant.ComponentHelper)"><!-- --></A><H3>
setNext</H3>
<PRE>
public void <B>setNext</B>(<A HREF="../../../../org/apache/tools/ant/ComponentHelper.html" title="class in org.apache.tools.ant">ComponentHelper</A>&nbsp;next)</PRE>
<DL>
<DD>Set the next chained component helper.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>next</CODE> - the next chained component helper.</DL>
</DD>
</DL>
<HR>

<A NAME="getNext()"><!-- --></A><H3>
getNext</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/ComponentHelper.html" title="class in org.apache.tools.ant">ComponentHelper</A> <B>getNext</B>()</PRE>
<DL>
<DD>Get the next chained component helper.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the next chained component helper.</DL>
</DD>
</DL>
<HR>

<A NAME="setProject(org.apache.tools.ant.Project)"><!-- --></A><H3>
setProject</H3>
<PRE>
public void <B>setProject</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Sets the project for this component helper.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the project for this helper.</DL>
</DD>
</DL>
<HR>

<A NAME="initSubProject(org.apache.tools.ant.ComponentHelper)"><!-- --></A><H3>
initSubProject</H3>
<PRE>
public void <B>initSubProject</B>(<A HREF="../../../../org/apache/tools/ant/ComponentHelper.html" title="class in org.apache.tools.ant">ComponentHelper</A>&nbsp;helper)</PRE>
<DL>
<DD>Used with creating child projects. Each child
 project inherits the component definitions
 from its parent.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>helper</CODE> - the component helper of the parent project.</DL>
</DD>
</DL>
<HR>

<A NAME="createComponent(org.apache.tools.ant.UnknownElement, java.lang.String, java.lang.String)"><!-- --></A><H3>
createComponent</H3>
<PRE>
public java.lang.Object <B>createComponent</B>(<A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant">UnknownElement</A>&nbsp;ue,
                                        java.lang.String&nbsp;ns,
                                        java.lang.String&nbsp;componentType)
                                 throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Factory method to create the components.

 This should be called by UnknownElement.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>ue</CODE> - The Unknown Element creating this component.<DD><CODE>ns</CODE> - Namespace URI. Also available as ue.getNamespace().<DD><CODE>componentType</CODE> - The component type,
                       Also available as ue.getComponentName().
<DT><B>Returns:</B><DD>the created component.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if an error occurs.</DL>
</DD>
</DL>
<HR>

<A NAME="createComponent(java.lang.String)"><!-- --></A><H3>
createComponent</H3>
<PRE>
public java.lang.Object <B>createComponent</B>(java.lang.String&nbsp;componentName)</PRE>
<DL>
<DD>Create an object for a component.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>componentName</CODE> - the name of the component, if
                      the component is in a namespace, the
                      name is prefixed with the namespace uri and ":".
<DT><B>Returns:</B><DD>the class if found or null if not.</DL>
</DD>
</DL>
<HR>

<A NAME="getComponentClass(java.lang.String)"><!-- --></A><H3>
getComponentClass</H3>
<PRE>
public java.lang.Class <B>getComponentClass</B>(java.lang.String&nbsp;componentName)</PRE>
<DL>
<DD>Return the class of the component name.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>componentName</CODE> - the name of the component, if
                      the component is in a namespace, the
                      name is prefixed with the namespace uri and ":".
<DT><B>Returns:</B><DD>the class if found or null if not.</DL>
</DD>
</DL>
<HR>

<A NAME="getDefinition(java.lang.String)"><!-- --></A><H3>
getDefinition</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A> <B>getDefinition</B>(java.lang.String&nbsp;componentName)</PRE>
<DL>
<DD>Return the antTypeDefinition for a componentName.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>componentName</CODE> - the name of the component.
<DT><B>Returns:</B><DD>the ant definition or null if not present.</DL>
</DD>
</DL>
<HR>

<A NAME="initDefaultDefinitions()"><!-- --></A><H3>
initDefaultDefinitions</H3>
<PRE>
public void <B>initDefaultDefinitions</B>()</PRE>
<DL>
<DD>This method is initialization code implementing the original ant component
 loading from /org/apache/tools/ant/taskdefs/default.properties
 and /org/apache/tools/ant/types/default.properties.
<P>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="addTaskDefinition(java.lang.String, java.lang.Class)"><!-- --></A><H3>
addTaskDefinition</H3>
<PRE>
public void <B>addTaskDefinition</B>(java.lang.String&nbsp;taskName,
                              java.lang.Class&nbsp;taskClass)</PRE>
<DL>
<DD>Adds a new task definition to the project.
 Attempting to override an existing definition with an
 equivalent one (i.e. with the same classname) results in
 a verbose log message. Attempting to override an existing definition
 with a different one results in a warning log message.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>taskName</CODE> - The name of the task to add.
                 Must not be <code>null</code>.<DD><CODE>taskClass</CODE> - The full name of the class implementing the task.
                  Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the class is unsuitable for being an Ant
                           task. An error level message is logged before
                           this exception is thrown.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/ComponentHelper.html#checkTaskClass(java.lang.Class)"><CODE>checkTaskClass(Class)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="checkTaskClass(java.lang.Class)"><!-- --></A><H3>
checkTaskClass</H3>
<PRE>
public void <B>checkTaskClass</B>(java.lang.Class&nbsp;taskClass)
                    throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Checks whether or not a class is suitable for serving as Ant task.
 Ant task implementation classes must be public, concrete, and have
 a no-arg constructor.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>taskClass</CODE> - The class to be checked.
                  Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the class is unsuitable for being an Ant
                           task. An error level message is logged before
                           this exception is thrown.</DL>
</DD>
</DL>
<HR>

<A NAME="getTaskDefinitions()"><!-- --></A><H3>
getTaskDefinitions</H3>
<PRE>
public java.util.Hashtable <B>getTaskDefinitions</B>()</PRE>
<DL>
<DD>Returns the current task definition hashtable. The returned hashtable is
 "live" and so should not be modified.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a map of from task name to implementing class
         (String to Class).</DL>
</DD>
</DL>
<HR>

<A NAME="getDataTypeDefinitions()"><!-- --></A><H3>
getDataTypeDefinitions</H3>
<PRE>
public java.util.Hashtable <B>getDataTypeDefinitions</B>()</PRE>
<DL>
<DD>Returns the current type definition hashtable. The returned hashtable is
 "live" and so should not be modified.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a map of from type name to implementing class
         (String to Class).</DL>
</DD>
</DL>
<HR>

<A NAME="addDataTypeDefinition(java.lang.String, java.lang.Class)"><!-- --></A><H3>
addDataTypeDefinition</H3>
<PRE>
public void <B>addDataTypeDefinition</B>(java.lang.String&nbsp;typeName,
                                  java.lang.Class&nbsp;typeClass)</PRE>
<DL>
<DD>Adds a new datatype definition.
 Attempting to override an existing definition with an
 equivalent one (i.e. with the same classname) results in
 a verbose log message. Attempting to override an existing definition
 with a different one results in a warning log message, but the
 definition is changed.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>typeName</CODE> - The name of the datatype.
                 Must not be <code>null</code>.<DD><CODE>typeClass</CODE> - The full name of the class implementing the datatype.
                  Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="addDataTypeDefinition(org.apache.tools.ant.AntTypeDefinition)"><!-- --></A><H3>
addDataTypeDefinition</H3>
<PRE>
public void <B>addDataTypeDefinition</B>(<A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant">AntTypeDefinition</A>&nbsp;def)</PRE>
<DL>
<DD>Describe <code>addDataTypeDefinition</code> method here.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>def</CODE> - an <code>AntTypeDefinition</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="getAntTypeTable()"><!-- --></A><H3>
getAntTypeTable</H3>
<PRE>
public java.util.Hashtable <B>getAntTypeTable</B>()</PRE>
<DL>
<DD>Returns the current datatype definition hashtable. The returned
 hashtable is "live" and so should not be modified.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a map of from datatype name to implementing class
         (String to Class).</DL>
</DD>
</DL>
<HR>

<A NAME="createTask(java.lang.String)"><!-- --></A><H3>
createTask</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A> <B>createTask</B>(java.lang.String&nbsp;taskType)
                throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Creates a new instance of a task.

  Called from Project.createTask(), which can be called by tasks.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>taskType</CODE> - The name of the task to create an instance of.
                 Must not be <code>null</code>.
<DT><B>Returns:</B><DD>an instance of the specified task, or <code>null</code> if
         the task name is not recognised.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the task name is recognised but task
                           creation fails.</DL>
</DD>
</DL>
<HR>

<A NAME="createDataType(java.lang.String)"><!-- --></A><H3>
createDataType</H3>
<PRE>
public java.lang.Object <B>createDataType</B>(java.lang.String&nbsp;typeName)
                                throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Creates a new instance of a data type.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>typeName</CODE> - The name of the data type to create an instance of.
                 Must not be <code>null</code>.
<DT><B>Returns:</B><DD>an instance of the specified data type, or <code>null</code> if
         the data type name is not recognised.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the data type name is recognised but
                           instance creation fails.</DL>
</DD>
</DL>
<HR>

<A NAME="getElementName(java.lang.Object)"><!-- --></A><H3>
getElementName</H3>
<PRE>
public java.lang.String <B>getElementName</B>(java.lang.Object&nbsp;element)</PRE>
<DL>
<DD>Returns a description of the type of the given element.
 <p>
 This is useful for logging purposes.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>element</CODE> - The element to describe.
                Must not be <code>null</code>.
<DT><B>Returns:</B><DD>a description of the element type.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getElementName(java.lang.Object, boolean)"><!-- --></A><H3>
getElementName</H3>
<PRE>
public java.lang.String <B>getElementName</B>(java.lang.Object&nbsp;o,
                                       boolean&nbsp;brief)</PRE>
<DL>
<DD>Returns a description of the type of the given element.
 <p>
 This is useful for logging purposes.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>o</CODE> - The element to describe.
              Must not be <code>null</code>.<DD><CODE>brief</CODE> - whether to use a brief description.
<DT><B>Returns:</B><DD>a description of the element type.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getElementName(org.apache.tools.ant.Project, java.lang.Object, boolean)"><!-- --></A><H3>
getElementName</H3>
<PRE>
public static java.lang.String <B>getElementName</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p,
                                              java.lang.Object&nbsp;o,
                                              boolean&nbsp;brief)</PRE>
<DL>
<DD>Convenient way to get some element name even when you may not have a
 Project context.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - The optional Project instance.<DD><CODE>o</CODE> - The element to describe.
                Must not be <code>null</code>.<DD><CODE>brief</CODE> - whether to use a brief description.
<DT><B>Returns:</B><DD>a description of the element type.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="enterAntLib(java.lang.String)"><!-- --></A><H3>
enterAntLib</H3>
<PRE>
public void <B>enterAntLib</B>(java.lang.String&nbsp;uri)</PRE>
<DL>
<DD>Called at the start of processing an antlib.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>uri</CODE> - the uri that is associated with this antlib.</DL>
</DD>
</DL>
<HR>

<A NAME="getCurrentAntlibUri()"><!-- --></A><H3>
getCurrentAntlibUri</H3>
<PRE>
public java.lang.String <B>getCurrentAntlibUri</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the current antlib uri.</DL>
</DD>
</DL>
<HR>

<A NAME="exitAntLib()"><!-- --></A><H3>
exitAntLib</H3>
<PRE>
public void <B>exitAntLib</B>()</PRE>
<DL>
<DD>Called at the end of processing an antlib.
<P>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="diagnoseCreationFailure(java.lang.String, java.lang.String)"><!-- --></A><H3>
diagnoseCreationFailure</H3>
<PRE>
public java.lang.String <B>diagnoseCreationFailure</B>(java.lang.String&nbsp;componentName,
                                                java.lang.String&nbsp;type)</PRE>
<DL>
<DD>Handler called to do decent diagnosis on instantiation failure.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>componentName</CODE> - component name.<DD><CODE>type</CODE> - component type, used in error messages
<DT><B>Returns:</B><DD>a string containing as much diagnostics info as possible.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/DefaultLogger.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/ComponentHelper.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ComponentHelper.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
