<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:21 EST 2006 -->
<TITLE>
AbstractJarSignerTask (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.AbstractJarSignerTask class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="AbstractJarSignerTask (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Ant.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="AbstractJarSignerTask.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class AbstractJarSignerTask</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.AbstractJarSignerTask</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html" title="class in org.apache.tools.ant.taskdefs">SignJar</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/VerifyJar.html" title="class in org.apache.tools.ant.taskdefs">VerifyJar</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public abstract class <B>AbstractJarSignerTask</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></DL>
</PRE>

<P>
This is factored out from <A HREF="../../../../../org/apache/tools/ant/taskdefs/SignJar.html" title="class in org.apache.tools.ant.taskdefs"><CODE>SignJar</CODE></A>; a base class that can be used
 for both signing and verifying JAR files using jarsigner
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#alias">alias</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The alias of signer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#ERROR_NO_SOURCE">ERROR_NO_SOURCE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;error string for unit test verification: "jar must be set through jar attribute or nested filesets"</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#filesets">filesets</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;the filesets of the jars to sign</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#jar">jar</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The name of the jar file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#JARSIGNER_COMMAND">JARSIGNER_COMMAND</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;name of JDK program we are looking for</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#keypass">keypass</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;password for the key in the store</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#keystore">keystore</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The url or path of keystore file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#maxMemory">maxMemory</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The maximum amount of memory to use for Jar signer</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#storepass">storepass</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;password for the store</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#storetype">storetype</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;type of store,-storetype param</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#verbose">verbose</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;verbose output</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#AbstractJarSignerTask()">AbstractJarSignerTask</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;set)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a set of files to sign</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#addSysproperty(org.apache.tools.ant.types.Environment.Variable)">addSysproperty</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</A>&nbsp;sysp)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a system property.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#addValue(org.apache.tools.ant.taskdefs.ExecTask, java.lang.String)">addValue</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A>&nbsp;cmd,
         java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;add a value argument to a command</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#beginExecution()">beginExecution</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;init processing logic; this is retained through our execution(s)</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#bindToKeystore(org.apache.tools.ant.taskdefs.ExecTask)">bindToKeystore</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A>&nbsp;cmd)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bind to a keystore if the attributes are there</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#createJarSigner()">createJarSigner</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;create the jarsigner executable task</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#createPath()">createPath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a path of files to sign.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#createUnifiedSourcePath()">createUnifiedSourcePath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;clone our path and add all explicitly specified FileSets as
 well, patch in the jar attribute as a new fileset if it is
 defined.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#createUnifiedSources()">createUnifiedSources</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;clone our filesets vector, and patch in the jar attribute as a new
 fileset, if is defined</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#declareSysProperty(org.apache.tools.ant.taskdefs.ExecTask, org.apache.tools.ant.types.Environment.Variable)">declareSysProperty</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A>&nbsp;cmd,
                   <A HREF="../../../../../org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</A>&nbsp;property)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#endExecution()">endExecution</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;any cleanup logic</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/RedirectorElement.html" title="class in org.apache.tools.ant.types">RedirectorElement</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#getRedirector()">getRedirector</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;get the redirector.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#hasResources()">hasResources</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Has either a path or a fileset been specified?</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setAlias(java.lang.String)">setAlias</A></B>(java.lang.String&nbsp;alias)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;the alias to sign under; required</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setCommonOptions(org.apache.tools.ant.taskdefs.ExecTask)">setCommonOptions</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A>&nbsp;cmd)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;these are options common to signing and verifying</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setJar(java.io.File)">setJar</A></B>(java.io.File&nbsp;jar)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;the jar file to sign; required</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setKeypass(java.lang.String)">setKeypass</A></B>(java.lang.String&nbsp;keypass)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;password for private key (if different); optional</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setKeystore(java.lang.String)">setKeystore</A></B>(java.lang.String&nbsp;keystore)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;keystore location; required</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setMaxmemory(java.lang.String)">setMaxmemory</A></B>(java.lang.String&nbsp;max)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the maximum memory to be used by the jarsigner process</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setStorepass(java.lang.String)">setStorepass</A></B>(java.lang.String&nbsp;storepass)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;password for keystore integrity; required</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setStoretype(java.lang.String)">setStoretype</A></B>(java.lang.String&nbsp;storetype)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;keystore type; optional</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#setVerbose(boolean)">setVerbose</A></B>(boolean&nbsp;verbose)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Enable verbose output when signing ; optional: default false</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#execute()">execute</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="jar"><!-- --></A><H3>
jar</H3>
<PRE>
protected java.io.File <B>jar</B></PRE>
<DL>
<DD>The name of the jar file.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="alias"><!-- --></A><H3>
alias</H3>
<PRE>
protected java.lang.String <B>alias</B></PRE>
<DL>
<DD>The alias of signer.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="keystore"><!-- --></A><H3>
keystore</H3>
<PRE>
protected java.lang.String <B>keystore</B></PRE>
<DL>
<DD>The url or path of keystore file.
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="storepass"><!-- --></A><H3>
storepass</H3>
<PRE>
protected java.lang.String <B>storepass</B></PRE>
<DL>
<DD>password for the store
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="storetype"><!-- --></A><H3>
storetype</H3>
<PRE>
protected java.lang.String <B>storetype</B></PRE>
<DL>
<DD>type of store,-storetype param
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="keypass"><!-- --></A><H3>
keypass</H3>
<PRE>
protected java.lang.String <B>keypass</B></PRE>
<DL>
<DD>password for the key in the store
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="verbose"><!-- --></A><H3>
verbose</H3>
<PRE>
protected boolean <B>verbose</B></PRE>
<DL>
<DD>verbose output
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="maxMemory"><!-- --></A><H3>
maxMemory</H3>
<PRE>
protected java.lang.String <B>maxMemory</B></PRE>
<DL>
<DD>The maximum amount of memory to use for Jar signer
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="filesets"><!-- --></A><H3>
filesets</H3>
<PRE>
protected java.util.Vector <B>filesets</B></PRE>
<DL>
<DD>the filesets of the jars to sign
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="JARSIGNER_COMMAND"><!-- --></A><H3>
JARSIGNER_COMMAND</H3>
<PRE>
protected static final java.lang.String <B>JARSIGNER_COMMAND</B></PRE>
<DL>
<DD>name of JDK program we are looking for
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.AbstractJarSignerTask.JARSIGNER_COMMAND">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_NO_SOURCE"><!-- --></A><H3>
ERROR_NO_SOURCE</H3>
<PRE>
public static final java.lang.String <B>ERROR_NO_SOURCE</B></PRE>
<DL>
<DD>error string for unit test verification: "jar must be set through jar attribute or nested filesets"
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.AbstractJarSignerTask.ERROR_NO_SOURCE">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="AbstractJarSignerTask()"><!-- --></A><H3>
AbstractJarSignerTask</H3>
<PRE>
public <B>AbstractJarSignerTask</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setMaxmemory(java.lang.String)"><!-- --></A><H3>
setMaxmemory</H3>
<PRE>
public void <B>setMaxmemory</B>(java.lang.String&nbsp;max)</PRE>
<DL>
<DD>Set the maximum memory to be used by the jarsigner process
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>max</CODE> - a string indicating the maximum memory according to the JVM
            conventions (e.g. 128m is 128 Megabytes)</DL>
</DD>
</DL>
<HR>

<A NAME="setJar(java.io.File)"><!-- --></A><H3>
setJar</H3>
<PRE>
public void <B>setJar</B>(java.io.File&nbsp;jar)</PRE>
<DL>
<DD>the jar file to sign; required
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>jar</CODE> - the jar file to sign</DL>
</DD>
</DL>
<HR>

<A NAME="setAlias(java.lang.String)"><!-- --></A><H3>
setAlias</H3>
<PRE>
public void <B>setAlias</B>(java.lang.String&nbsp;alias)</PRE>
<DL>
<DD>the alias to sign under; required
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>alias</CODE> - the alias to sign under</DL>
</DD>
</DL>
<HR>

<A NAME="setKeystore(java.lang.String)"><!-- --></A><H3>
setKeystore</H3>
<PRE>
public void <B>setKeystore</B>(java.lang.String&nbsp;keystore)</PRE>
<DL>
<DD>keystore location; required
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>keystore</CODE> - the keystore location</DL>
</DD>
</DL>
<HR>

<A NAME="setStorepass(java.lang.String)"><!-- --></A><H3>
setStorepass</H3>
<PRE>
public void <B>setStorepass</B>(java.lang.String&nbsp;storepass)</PRE>
<DL>
<DD>password for keystore integrity; required
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>storepass</CODE> - the password for the keystore</DL>
</DD>
</DL>
<HR>

<A NAME="setStoretype(java.lang.String)"><!-- --></A><H3>
setStoretype</H3>
<PRE>
public void <B>setStoretype</B>(java.lang.String&nbsp;storetype)</PRE>
<DL>
<DD>keystore type; optional
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>storetype</CODE> - the keystore type</DL>
</DD>
</DL>
<HR>

<A NAME="setKeypass(java.lang.String)"><!-- --></A><H3>
setKeypass</H3>
<PRE>
public void <B>setKeypass</B>(java.lang.String&nbsp;keypass)</PRE>
<DL>
<DD>password for private key (if different); optional
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>keypass</CODE> - the password for the key (if different)</DL>
</DD>
</DL>
<HR>

<A NAME="setVerbose(boolean)"><!-- --></A><H3>
setVerbose</H3>
<PRE>
public void <B>setVerbose</B>(boolean&nbsp;verbose)</PRE>
<DL>
<DD>Enable verbose output when signing ; optional: default false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>verbose</CODE> - if true enable verbose output</DL>
</DD>
</DL>
<HR>

<A NAME="addFileset(org.apache.tools.ant.types.FileSet)"><!-- --></A><H3>
addFileset</H3>
<PRE>
public void <B>addFileset</B>(<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types">FileSet</A>&nbsp;set)</PRE>
<DL>
<DD>Adds a set of files to sign
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>set</CODE> - a set of files to sign<DT><B>Since:</B></DT>
  <DD>Ant 1.4</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addSysproperty(org.apache.tools.ant.types.Environment.Variable)"><!-- --></A><H3>
addSysproperty</H3>
<PRE>
public void <B>addSysproperty</B>(<A HREF="../../../../../org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</A>&nbsp;sysp)</PRE>
<DL>
<DD>Add a system property.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sysp</CODE> - system property.</DL>
</DD>
</DL>
<HR>

<A NAME="createPath()"><!-- --></A><H3>
createPath</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createPath</B>()</PRE>
<DL>
<DD>Adds a path of files to sign.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a path of files to sign.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="beginExecution()"><!-- --></A><H3>
beginExecution</H3>
<PRE>
protected void <B>beginExecution</B>()</PRE>
<DL>
<DD>init processing logic; this is retained through our execution(s)
<P>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="endExecution()"><!-- --></A><H3>
endExecution</H3>
<PRE>
protected void <B>endExecution</B>()</PRE>
<DL>
<DD>any cleanup logic
<P>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="getRedirector()"><!-- --></A><H3>
getRedirector</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/RedirectorElement.html" title="class in org.apache.tools.ant.types">RedirectorElement</A> <B>getRedirector</B>()</PRE>
<DL>
<DD>get the redirector. Non-null between invocations of
 <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#beginExecution()"><CODE>beginExecution()</CODE></A> and <A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#endExecution()"><CODE>endExecution()</CODE></A>
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a redirector or null</DL>
</DD>
</DL>
<HR>

<A NAME="setCommonOptions(org.apache.tools.ant.taskdefs.ExecTask)"><!-- --></A><H3>
setCommonOptions</H3>
<PRE>
protected void <B>setCommonOptions</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A>&nbsp;cmd)</PRE>
<DL>
<DD>these are options common to signing and verifying
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>cmd</CODE> - command to configure</DL>
</DD>
</DL>
<HR>

<A NAME="declareSysProperty(org.apache.tools.ant.taskdefs.ExecTask, org.apache.tools.ant.types.Environment.Variable)"><!-- --></A><H3>
declareSysProperty</H3>
<PRE>
protected void <B>declareSysProperty</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A>&nbsp;cmd,
                                  <A HREF="../../../../../org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</A>&nbsp;property)
                           throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>cmd</CODE> - command to configure<DD><CODE>property</CODE> - property to set
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the property is not correctly defined.</DL>
</DD>
</DL>
<HR>

<A NAME="bindToKeystore(org.apache.tools.ant.taskdefs.ExecTask)"><!-- --></A><H3>
bindToKeystore</H3>
<PRE>
protected void <B>bindToKeystore</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A>&nbsp;cmd)</PRE>
<DL>
<DD>bind to a keystore if the attributes are there
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>cmd</CODE> - command to configure</DL>
</DD>
</DL>
<HR>

<A NAME="createJarSigner()"><!-- --></A><H3>
createJarSigner</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A> <B>createJarSigner</B>()</PRE>
<DL>
<DD>create the jarsigner executable task
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a task set up with the executable of jarsigner, failonerror=true
 and bound to our redirector</DL>
</DD>
</DL>
<HR>

<A NAME="createUnifiedSources()"><!-- --></A><H3>
createUnifiedSources</H3>
<PRE>
protected java.util.Vector <B>createUnifiedSources</B>()</PRE>
<DL>
<DD>clone our filesets vector, and patch in the jar attribute as a new
 fileset, if is defined
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a vector of FileSet instances</DL>
</DD>
</DL>
<HR>

<A NAME="createUnifiedSourcePath()"><!-- --></A><H3>
createUnifiedSourcePath</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createUnifiedSourcePath</B>()</PRE>
<DL>
<DD>clone our path and add all explicitly specified FileSets as
 well, patch in the jar attribute as a new fileset if it is
 defined.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a path that contains all files to sign<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="hasResources()"><!-- --></A><H3>
hasResources</H3>
<PRE>
protected boolean <B>hasResources</B>()</PRE>
<DL>
<DD>Has either a path or a fileset been specified?
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true if a path or fileset has been specified.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addValue(org.apache.tools.ant.taskdefs.ExecTask, java.lang.String)"><!-- --></A><H3>
addValue</H3>
<PRE>
protected void <B>addValue</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A>&nbsp;cmd,
                        java.lang.String&nbsp;value)</PRE>
<DL>
<DD>add a value argument to a command
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>cmd</CODE> - command to manipulate<DD><CODE>value</CODE> - value to add</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/AbstractCvsTask.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Ant.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="AbstractJarSignerTask.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
