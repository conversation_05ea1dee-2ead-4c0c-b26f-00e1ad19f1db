<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:29 EST 2006 -->
<TITLE>
JUnitTest (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.optional.junit.JUnitTest class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="JUnitTest (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirrorImpl.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTestRunner.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="JUnitTest.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.junit.BaseTest">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.optional.junit</FONT>
<BR>
Class JUnitTest</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">org.apache.tools.ant.taskdefs.optional.junit.BaseTest</A>
      <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.optional.junit.JUnitTest</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>JUnitTest</B><DT>extends <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BaseTest</A><DT>implements java.lang.Cloneable</DL>
</PRE>

<P>
<p> Run a single JUnit test.

 <p> The JUnit test is actually run by <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTestRunner.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><CODE>JUnitTestRunner</CODE></A>.
 So read the doc comments for that class :)
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><CODE>JUnitTask</CODE></A>, 
<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTestRunner.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><CODE>JUnitTestRunner</CODE></A></DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.junit.BaseTest"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.optional.junit.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BaseTest</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#destDir">destDir</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#errorProperty">errorProperty</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#failureProperty">failureProperty</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#filtertrace">filtertrace</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#fork">fork</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#formatters">formatters</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#haltOnError">haltOnError</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#haltOnFail">haltOnFail</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#ifProperty">ifProperty</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#unlessProperty">unlessProperty</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#JUnitTest()">JUnitTest</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;No arg constructor.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#JUnitTest(java.lang.String)">JUnitTest</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor with name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#JUnitTest(java.lang.String, boolean, boolean, boolean)">JUnitTest</A></B>(java.lang.String&nbsp;name,
          boolean&nbsp;haltOnError,
          boolean&nbsp;haltOnFailure,
          boolean&nbsp;filtertrace)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor with options.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#clone()">clone</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#errorCount()">errorCount</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the number of errors.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#failureCount()">failureCount</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the number of failures.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/FormatterElement.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</A>[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#getFormatters()">getFormatters</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the formatters set for this test.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#getName()">getName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the name of the test class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#getOutfile()">getOutfile</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the name of the output file</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Properties</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#getProperties()">getProperties</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the properties used in the test.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#getRunTime()">getRunTime</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the run time.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#runCount()">runCount</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the number of runs.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#setCounts(long, long, long)">setCounts</A></B>(long&nbsp;runs,
          long&nbsp;failures,
          long&nbsp;errors)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the number of runs, failures and errors.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#setName(java.lang.String)">setName</A></B>(java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the name of the test class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#setOutfile(java.lang.String)">setOutfile</A></B>(java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the name of the output file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#setProperties(java.util.Hashtable)">setProperties</A></B>(java.util.Hashtable&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the properties to be used in the test.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#setRunTime(long)">setRunTime</A></B>(long&nbsp;runTime)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the runtime.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html#shouldRun(org.apache.tools.ant.Project)">shouldRun</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Check if this test should run based on the if and unless
 attributes.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.optional.junit.BaseTest"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.optional.junit.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">BaseTest</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#addFormatter(org.apache.tools.ant.taskdefs.optional.junit.FormatterElement)">addFormatter</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#getErrorProperty()">getErrorProperty</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#getFailureProperty()">getFailureProperty</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#getFiltertrace()">getFiltertrace</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#getFork()">getFork</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#getHaltonerror()">getHaltonerror</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#getHaltonfailure()">getHaltonfailure</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#getTodir()">getTodir</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#setErrorProperty(java.lang.String)">setErrorProperty</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#setFailureProperty(java.lang.String)">setFailureProperty</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#setFiltertrace(boolean)">setFiltertrace</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#setFork(boolean)">setFork</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#setHaltonerror(boolean)">setHaltonerror</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#setHaltonfailure(boolean)">setHaltonfailure</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#setIf(java.lang.String)">setIf</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#setTodir(java.io.File)">setTodir</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/BaseTest.html#setUnless(java.lang.String)">setUnless</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="JUnitTest()"><!-- --></A><H3>
JUnitTest</H3>
<PRE>
public <B>JUnitTest</B>()</PRE>
<DL>
<DD>No arg constructor.
<P>
</DL>
<HR>

<A NAME="JUnitTest(java.lang.String)"><!-- --></A><H3>
JUnitTest</H3>
<PRE>
public <B>JUnitTest</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Constructor with name.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the name of the test.</DL>
</DL>
<HR>

<A NAME="JUnitTest(java.lang.String, boolean, boolean, boolean)"><!-- --></A><H3>
JUnitTest</H3>
<PRE>
public <B>JUnitTest</B>(java.lang.String&nbsp;name,
                 boolean&nbsp;haltOnError,
                 boolean&nbsp;haltOnFailure,
                 boolean&nbsp;filtertrace)</PRE>
<DL>
<DD>Constructor with options.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the name of the test.<DD><CODE>haltOnError</CODE> - if true halt the tests if there is an error.<DD><CODE>haltOnFailure</CODE> - if true halt the tests if there is a failure.<DD><CODE>filtertrace</CODE> - if true filter stack traces.</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setName(java.lang.String)"><!-- --></A><H3>
setName</H3>
<PRE>
public void <B>setName</B>(java.lang.String&nbsp;value)</PRE>
<DL>
<DD>Set the name of the test class.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - the name to use.</DL>
</DD>
</DL>
<HR>

<A NAME="setOutfile(java.lang.String)"><!-- --></A><H3>
setOutfile</H3>
<PRE>
public void <B>setOutfile</B>(java.lang.String&nbsp;value)</PRE>
<DL>
<DD>Set the name of the output file.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - the name of the output file to use.</DL>
</DD>
</DL>
<HR>

<A NAME="getName()"><!-- --></A><H3>
getName</H3>
<PRE>
public java.lang.String <B>getName</B>()</PRE>
<DL>
<DD>Get the name of the test class.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the name of the test.</DL>
</DD>
</DL>
<HR>

<A NAME="getOutfile()"><!-- --></A><H3>
getOutfile</H3>
<PRE>
public java.lang.String <B>getOutfile</B>()</PRE>
<DL>
<DD>Get the name of the output file
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the name of the output file.</DL>
</DD>
</DL>
<HR>

<A NAME="setCounts(long, long, long)"><!-- --></A><H3>
setCounts</H3>
<PRE>
public void <B>setCounts</B>(long&nbsp;runs,
                      long&nbsp;failures,
                      long&nbsp;errors)</PRE>
<DL>
<DD>Set the number of runs, failures and errors.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>runs</CODE> - the number of runs.<DD><CODE>failures</CODE> - the number of failures.<DD><CODE>errors</CODE> - the number of errors.</DL>
</DD>
</DL>
<HR>

<A NAME="setRunTime(long)"><!-- --></A><H3>
setRunTime</H3>
<PRE>
public void <B>setRunTime</B>(long&nbsp;runTime)</PRE>
<DL>
<DD>Set the runtime.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>runTime</CODE> - the time in milliseconds.</DL>
</DD>
</DL>
<HR>

<A NAME="runCount()"><!-- --></A><H3>
runCount</H3>
<PRE>
public long <B>runCount</B>()</PRE>
<DL>
<DD>Get the number of runs.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the number of runs.</DL>
</DD>
</DL>
<HR>

<A NAME="failureCount()"><!-- --></A><H3>
failureCount</H3>
<PRE>
public long <B>failureCount</B>()</PRE>
<DL>
<DD>Get the number of failures.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the number of failures.</DL>
</DD>
</DL>
<HR>

<A NAME="errorCount()"><!-- --></A><H3>
errorCount</H3>
<PRE>
public long <B>errorCount</B>()</PRE>
<DL>
<DD>Get the number of errors.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the number of errors.</DL>
</DD>
</DL>
<HR>

<A NAME="getRunTime()"><!-- --></A><H3>
getRunTime</H3>
<PRE>
public long <B>getRunTime</B>()</PRE>
<DL>
<DD>Get the run time.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the run time in milliseconds.</DL>
</DD>
</DL>
<HR>

<A NAME="getProperties()"><!-- --></A><H3>
getProperties</H3>
<PRE>
public java.util.Properties <B>getProperties</B>()</PRE>
<DL>
<DD>Get the properties used in the test.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the properties.</DL>
</DD>
</DL>
<HR>

<A NAME="setProperties(java.util.Hashtable)"><!-- --></A><H3>
setProperties</H3>
<PRE>
public void <B>setProperties</B>(java.util.Hashtable&nbsp;p)</PRE>
<DL>
<DD>Set the properties to be used in the test.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the properties.
          This is a copy of the projects ant properties.</DL>
</DD>
</DL>
<HR>

<A NAME="shouldRun(org.apache.tools.ant.Project)"><!-- --></A><H3>
shouldRun</H3>
<PRE>
public boolean <B>shouldRun</B>(<A HREF="../../../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</PRE>
<DL>
<DD>Check if this test should run based on the if and unless
 attributes.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the project to use to check if the if and unless
          properties exist in.
<DT><B>Returns:</B><DD>true if this test or testsuite should be run.</DL>
</DD>
</DL>
<HR>

<A NAME="getFormatters()"><!-- --></A><H3>
getFormatters</H3>
<PRE>
public <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/FormatterElement.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</A>[] <B>getFormatters</B>()</PRE>
<DL>
<DD>Get the formatters set for this test.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the formatters as an array.</DL>
</DD>
</DL>
<HR>

<A NAME="clone()"><!-- --></A><H3>
clone</H3>
<PRE>
public java.lang.Object <B>clone</B>()</PRE>
<DL>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>clone</CODE> in class <CODE>java.lang.Object</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a clone of this test.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirrorImpl.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/junit/JUnitTestRunner.html" title="class in org.apache.tools.ant.taskdefs.optional.junit"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/junit/JUnitTest.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="JUnitTest.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.junit.BaseTest">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
