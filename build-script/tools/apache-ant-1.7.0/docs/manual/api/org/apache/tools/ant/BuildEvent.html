<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:19 EST 2006 -->
<TITLE>
BuildEvent (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.BuildEvent class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="BuildEvent (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/BuildEvent.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="BuildEvent.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_java.util.EventObject">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class BuildEvent</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by ">java.util.EventObject
      <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.BuildEvent</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.io.Serializable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>BuildEvent</B><DT>extends java.util.EventObject</DL>
</PRE>

<P>
Class representing an event occurring during a build. An
 event is built by specifying either a project, a task or a target.
 A project level event will only have a project reference;
 a target level event will have project and target references;
 a task level event will have project, target and task references.
<P>

<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../serialized-form.html#org.apache.tools.ant.BuildEvent">Serialized Form</A></DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_java.util.EventObject"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class java.util.EventObject</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>source</CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#BuildEvent(org.apache.tools.ant.Project)">BuildEvent</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Construct a BuildEvent for a project level event.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#BuildEvent(org.apache.tools.ant.Target)">BuildEvent</A></B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Construct a BuildEvent for a target level event.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#BuildEvent(org.apache.tools.ant.Task)">BuildEvent</A></B>(<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Construct a BuildEvent for a task level event.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Throwable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getException()">getException</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the exception that was thrown, if any.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getMessage()">getMessage</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the logging message.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getPriority()">getPriority</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the priority of the logging message.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getProject()">getProject</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the project that fired this event.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getTarget()">getTarget</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the target that fired this event.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getTask()">getTask</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the task that fired this event.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#setException(java.lang.Throwable)">setException</A></B>(java.lang.Throwable&nbsp;exception)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the exception associated with this event.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#setMessage(java.lang.String, int)">setMessage</A></B>(java.lang.String&nbsp;message,
           int&nbsp;priority)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the message and priority associated with this event.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.util.EventObject"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.util.EventObject</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>getSource, toString</CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="BuildEvent(org.apache.tools.ant.Project)"><!-- --></A><H3>
BuildEvent</H3>
<PRE>
public <B>BuildEvent</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</PRE>
<DL>
<DD>Construct a BuildEvent for a project level event.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - the project that emitted the event.
                Should not be <code>null</code>.</DL>
</DL>
<HR>

<A NAME="BuildEvent(org.apache.tools.ant.Target)"><!-- --></A><H3>
BuildEvent</H3>
<PRE>
public <B>BuildEvent</B>(<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A>&nbsp;target)</PRE>
<DL>
<DD>Construct a BuildEvent for a target level event.
 The project associated with the event is derived
 from the given target.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>target</CODE> - the target that emitted the event.
               Must not be <code>null</code>.</DL>
</DL>
<HR>

<A NAME="BuildEvent(org.apache.tools.ant.Task)"><!-- --></A><H3>
BuildEvent</H3>
<PRE>
public <B>BuildEvent</B>(<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;task)</PRE>
<DL>
<DD>Construct a BuildEvent for a task level event.
 The project and target associated with the event
 are derived from the given task.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>task</CODE> - the task that emitted the event.
             Must not be <code>null</code>.</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setMessage(java.lang.String, int)"><!-- --></A><H3>
setMessage</H3>
<PRE>
public void <B>setMessage</B>(java.lang.String&nbsp;message,
                       int&nbsp;priority)</PRE>
<DL>
<DD>Sets the message and priority associated with this event.
 This is used for "messageLogged" events.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>message</CODE> - the message to be associated with this event.
                Should not be <code>null</code>.<DD><CODE>priority</CODE> - the priority to be associated with this event,
                 as defined in the <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant"><CODE>Project</CODE></A> class.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)"><CODE>BuildListener.messageLogged(BuildEvent)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="setException(java.lang.Throwable)"><!-- --></A><H3>
setException</H3>
<PRE>
public void <B>setException</B>(java.lang.Throwable&nbsp;exception)</PRE>
<DL>
<DD>Sets the exception associated with this event. This is used
 for "messageLogged", "taskFinished", "targetFinished", and "buildFinished"
 events.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>exception</CODE> - The exception to be associated with this event.
                  May be <code>null</code>.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)"><CODE>BuildListener.messageLogged(BuildEvent)</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/BuildListener.html#taskFinished(org.apache.tools.ant.BuildEvent)"><CODE>BuildListener.taskFinished(BuildEvent)</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/BuildListener.html#targetFinished(org.apache.tools.ant.BuildEvent)"><CODE>BuildListener.targetFinished(BuildEvent)</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/BuildListener.html#buildFinished(org.apache.tools.ant.BuildEvent)"><CODE>BuildListener.buildFinished(BuildEvent)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getProject()"><!-- --></A><H3>
getProject</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A> <B>getProject</B>()</PRE>
<DL>
<DD>Returns the project that fired this event.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the project that fired this event</DL>
</DD>
</DL>
<HR>

<A NAME="getTarget()"><!-- --></A><H3>
getTarget</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A> <B>getTarget</B>()</PRE>
<DL>
<DD>Returns the target that fired this event.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the project that fired this event, or <code>null</code>
          if this event is a project level event.</DL>
</DD>
</DL>
<HR>

<A NAME="getTask()"><!-- --></A><H3>
getTask</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A> <B>getTask</B>()</PRE>
<DL>
<DD>Returns the task that fired this event.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the task that fired this event, or <code>null</code>
         if this event is a project or target level event.</DL>
</DD>
</DL>
<HR>

<A NAME="getMessage()"><!-- --></A><H3>
getMessage</H3>
<PRE>
public java.lang.String <B>getMessage</B>()</PRE>
<DL>
<DD>Returns the logging message. This field will only be set
 for "messageLogged" events.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the message associated with this event, or <code>null</code>
         if no message has been set.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)"><CODE>BuildListener.messageLogged(BuildEvent)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getPriority()"><!-- --></A><H3>
getPriority</H3>
<PRE>
public int <B>getPriority</B>()</PRE>
<DL>
<DD>Returns the priority of the logging message. This field will only
 be set for "messageLogged" events. The meaning of this priority
 is as specified by the constants in the <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant"><CODE>Project</CODE></A> class.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the priority associated with this event.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)"><CODE>BuildListener.messageLogged(BuildEvent)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getException()"><!-- --></A><H3>
getException</H3>
<PRE>
public java.lang.Throwable <B>getException</B>()</PRE>
<DL>
<DD>Returns the exception that was thrown, if any. This field will only
 be set for "messageLogged", "taskFinished", "targetFinished", and "buildFinished"
 events.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the exception associated with this exception, or
         <code>null</code> if no exception has been set.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)"><CODE>BuildListener.messageLogged(BuildEvent)</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/BuildListener.html#taskFinished(org.apache.tools.ant.BuildEvent)"><CODE>BuildListener.taskFinished(BuildEvent)</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/BuildListener.html#targetFinished(org.apache.tools.ant.BuildEvent)"><CODE>BuildListener.targetFinished(BuildEvent)</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/BuildListener.html#buildFinished(org.apache.tools.ant.BuildEvent)"><CODE>BuildListener.buildFinished(BuildEvent)</CODE></A></DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/BuildEvent.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="BuildEvent.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_java.util.EventObject">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
