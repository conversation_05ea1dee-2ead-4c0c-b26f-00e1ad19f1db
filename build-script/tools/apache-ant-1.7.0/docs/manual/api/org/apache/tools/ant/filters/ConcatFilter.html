<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:20 EST 2006 -->
<TITLE>
ConcatFilter (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.filters.ConcatFilter class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="ConcatFilter (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/filters/ClassConstants.html" title="class in org.apache.tools.ant.filters"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/filters/EscapeUnicode.html" title="class in org.apache.tools.ant.filters"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/filters/ConcatFilter.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ConcatFilter.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_java.io.FilterReader">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.filters</FONT>
<BR>
Class ConcatFilter</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by ">java.io.Reader
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by ">java.io.FilterReader
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/filters/BaseFilterReader.html" title="class in org.apache.tools.ant.filters">org.apache.tools.ant.filters.BaseFilterReader</A>
              <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/filters/BaseParamFilterReader.html" title="class in org.apache.tools.ant.filters">org.apache.tools.ant.filters.BaseParamFilterReader</A>
                  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.filters.ConcatFilter</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.io.Closeable, java.lang.Readable, <A HREF="../../../../../org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</A>, <A HREF="../../../../../org/apache/tools/ant/types/Parameterizable.html" title="interface in org.apache.tools.ant.types">Parameterizable</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public final class <B>ConcatFilter</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/filters/BaseParamFilterReader.html" title="class in org.apache.tools.ant.filters">BaseParamFilterReader</A><DT>implements <A HREF="../../../../../org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</A></DL>
</PRE>

<P>
Concats a file before and/or after the file.

 <p>Example:<pre>
 <copy todir="build">
     <fileset dir="src" includes="*.java"/>
     <filterchain>
         <concatfilter prepend="apache-license-java.txt"/>
     </filterchain>
 </copy>
 </pre>
 Copies all java sources from <i>src</i> to <i>build</i> and adds the
 content of <i>apache-license-java.txt</i> add the beginning of each
 file.</p>
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>1.6</DD>
<DT><B>Version:</B></DT>
  <DD>2003-09-23</DD>
</DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_java.io.FilterReader"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class java.io.FilterReader</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>in</CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_java.io.Reader"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class java.io.Reader</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>lock</CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/filters/ConcatFilter.html#ConcatFilter()">ConcatFilter</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for "dummy" instances.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/filters/ConcatFilter.html#ConcatFilter(java.io.Reader)">ConcatFilter</A></B>(java.io.Reader&nbsp;in)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new filtered reader.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.Reader</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/filters/ConcatFilter.html#chain(java.io.Reader)">chain</A></B>(java.io.Reader&nbsp;rdr)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new ConcatReader using the passed in
 Reader for instantiation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/filters/ConcatFilter.html#getAppend()">getAppend</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns <i>append</i> attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/filters/ConcatFilter.html#getPrepend()">getPrepend</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns <i>prepend</i> attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/filters/ConcatFilter.html#read()">read</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the next character in the filtered stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/filters/ConcatFilter.html#setAppend(java.io.File)">setAppend</A></B>(java.io.File&nbsp;append)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets <i>append</i> attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/filters/ConcatFilter.html#setPrepend(java.io.File)">setPrepend</A></B>(java.io.File&nbsp;prepend)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets <i>prepend</i> attribute.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.filters.BaseParamFilterReader"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.filters.<A HREF="../../../../../org/apache/tools/ant/filters/BaseParamFilterReader.html" title="class in org.apache.tools.ant.filters">BaseParamFilterReader</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/filters/BaseParamFilterReader.html#getParameters()">getParameters</A>, <A HREF="../../../../../org/apache/tools/ant/filters/BaseParamFilterReader.html#setParameters(org.apache.tools.ant.types.Parameter[])">setParameters</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.filters.BaseFilterReader"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.filters.<A HREF="../../../../../org/apache/tools/ant/filters/BaseFilterReader.html" title="class in org.apache.tools.ant.filters">BaseFilterReader</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/filters/BaseFilterReader.html#getInitialized()">getInitialized</A>, <A HREF="../../../../../org/apache/tools/ant/filters/BaseFilterReader.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/filters/BaseFilterReader.html#read(char[], int, int)">read</A>, <A HREF="../../../../../org/apache/tools/ant/filters/BaseFilterReader.html#readFully()">readFully</A>, <A HREF="../../../../../org/apache/tools/ant/filters/BaseFilterReader.html#readLine()">readLine</A>, <A HREF="../../../../../org/apache/tools/ant/filters/BaseFilterReader.html#setInitialized(boolean)">setInitialized</A>, <A HREF="../../../../../org/apache/tools/ant/filters/BaseFilterReader.html#setProject(org.apache.tools.ant.Project)">setProject</A>, <A HREF="../../../../../org/apache/tools/ant/filters/BaseFilterReader.html#skip(long)">skip</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.io.FilterReader"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.io.FilterReader</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>close, mark, markSupported, ready, reset</CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.io.Reader"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.io.Reader</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>read, read</CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ConcatFilter()"><!-- --></A><H3>
ConcatFilter</H3>
<PRE>
public <B>ConcatFilter</B>()</PRE>
<DL>
<DD>Constructor for "dummy" instances.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../org/apache/tools/ant/filters/BaseFilterReader.html#BaseFilterReader()"><CODE>BaseFilterReader.BaseFilterReader()</CODE></A></DL>
</DL>
<HR>

<A NAME="ConcatFilter(java.io.Reader)"><!-- --></A><H3>
ConcatFilter</H3>
<PRE>
public <B>ConcatFilter</B>(java.io.Reader&nbsp;in)</PRE>
<DL>
<DD>Creates a new filtered reader.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>in</CODE> - A Reader object providing the underlying stream.
           Must not be <code>null</code>.</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="read()"><!-- --></A><H3>
read</H3>
<PRE>
public int <B>read</B>()
         throws java.io.IOException</PRE>
<DL>
<DD>Returns the next character in the filtered stream. If the desired
 number of lines have already been read, the resulting stream is
 effectively at an end. Otherwise, the next character from the
 underlying stream is read and returned.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>read</CODE> in class <CODE>java.io.FilterReader</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the next character in the resulting stream, or -1
 if the end of the resulting stream has been reached
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the underlying stream throws an IOException
 during reading</DL>
</DD>
</DL>
<HR>

<A NAME="setPrepend(java.io.File)"><!-- --></A><H3>
setPrepend</H3>
<PRE>
public void <B>setPrepend</B>(java.io.File&nbsp;prepend)</PRE>
<DL>
<DD>Sets <i>prepend</i> attribute.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>prepend</CODE> - new value</DL>
</DD>
</DL>
<HR>

<A NAME="getPrepend()"><!-- --></A><H3>
getPrepend</H3>
<PRE>
public java.io.File <B>getPrepend</B>()</PRE>
<DL>
<DD>Returns <i>prepend</i> attribute.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>prepend attribute</DL>
</DD>
</DL>
<HR>

<A NAME="setAppend(java.io.File)"><!-- --></A><H3>
setAppend</H3>
<PRE>
public void <B>setAppend</B>(java.io.File&nbsp;append)</PRE>
<DL>
<DD>Sets <i>append</i> attribute.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>append</CODE> - new value</DL>
</DD>
</DL>
<HR>

<A NAME="getAppend()"><!-- --></A><H3>
getAppend</H3>
<PRE>
public java.io.File <B>getAppend</B>()</PRE>
<DL>
<DD>Returns <i>append</i> attribute.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>append attribute</DL>
</DD>
</DL>
<HR>

<A NAME="chain(java.io.Reader)"><!-- --></A><H3>
chain</H3>
<PRE>
public java.io.Reader <B>chain</B>(java.io.Reader&nbsp;rdr)</PRE>
<DL>
<DD>Creates a new ConcatReader using the passed in
 Reader for instantiation.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/filters/ChainableReader.html#chain(java.io.Reader)">chain</A></CODE> in interface <CODE><A HREF="../../../../../org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rdr</CODE> - A Reader object providing the underlying stream.
            Must not be <code>null</code>.
<DT><B>Returns:</B><DD>a new filter based on this configuration, but filtering
         the specified reader</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/filters/ClassConstants.html" title="class in org.apache.tools.ant.filters"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/filters/EscapeUnicode.html" title="class in org.apache.tools.ant.filters"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/filters/ConcatFilter.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ConcatFilter.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_java.io.FilterReader">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
