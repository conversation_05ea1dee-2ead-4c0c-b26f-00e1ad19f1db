<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:34 EST 2006 -->
<TITLE>
FileUtils (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.util.FileUtils class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="FileUtils (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/util/FileTokenizer.html" title="class in org.apache.tools.ant.util"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/util/FlatFileNameMapper.html" title="class in org.apache.tools.ant.util"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/util/FileUtils.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="FileUtils.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.util</FONT>
<BR>
Class FileUtils</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.util.FileUtils</B>
</PRE>
<HR>
<DL>
<DT><PRE>public class <B>FileUtils</B><DT>extends java.lang.Object</DL>
</PRE>

<P>
This class also encapsulates methods which allow Files to be
 referred to using abstract path names which are translated to native
 system file paths at runtime as well as copying files or setting
 their last modification time.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#FAT_FILE_TIMESTAMP_GRANULARITY">FAT_FILE_TIMESTAMP_GRANULARITY</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The granularity of timestamps under FAT.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#NTFS_FILE_TIMESTAMP_GRANULARITY">NTFS_FILE_TIMESTAMP_GRANULARITY</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The granularity of timestamps under the NT File System.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#UNIX_FILE_TIMESTAMP_GRANULARITY">UNIX_FILE_TIMESTAMP_GRANULARITY</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The granularity of timestamps under Unix.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected </CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#FileUtils()">FileUtils</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Empty constructor.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#close(java.io.InputStream)">close</A></B>(java.io.InputStream&nbsp;device)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Close a stream without throwing any exception if something went wrong.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#close(java.io.OutputStream)">close</A></B>(java.io.OutputStream&nbsp;device)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Close a stream without throwing any exception if something went wrong.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#close(java.io.Reader)">close</A></B>(java.io.Reader&nbsp;device)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Close a stream without throwing any exception if something went wrong.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#close(java.io.Writer)">close</A></B>(java.io.Writer&nbsp;device)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Close a Writer without throwing any exception if something went wrong.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#contentEquals(java.io.File, java.io.File)">contentEquals</A></B>(java.io.File&nbsp;f1,
              java.io.File&nbsp;f2)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Compares the contents of two files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#contentEquals(java.io.File, java.io.File, boolean)">contentEquals</A></B>(java.io.File&nbsp;f1,
              java.io.File&nbsp;f2,
              boolean&nbsp;textfile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Compares the contents of two files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#copyFile(java.io.File, java.io.File)">copyFile</A></B>(java.io.File&nbsp;sourceFile,
         java.io.File&nbsp;destFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to copy a file from a source to a destination.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#copyFile(java.io.File, java.io.File, org.apache.tools.ant.types.FilterSetCollection)">copyFile</A></B>(java.io.File&nbsp;sourceFile,
         java.io.File&nbsp;destFile,
         <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to copy a file from a source to a destination
 specifying if token filtering must be used.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#copyFile(java.io.File, java.io.File, org.apache.tools.ant.types.FilterSetCollection, boolean)">copyFile</A></B>(java.io.File&nbsp;sourceFile,
         java.io.File&nbsp;destFile,
         <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
         boolean&nbsp;overwrite)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used and if
 source files may overwrite newer destination files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#copyFile(java.io.File, java.io.File, org.apache.tools.ant.types.FilterSetCollection, boolean, boolean)">copyFile</A></B>(java.io.File&nbsp;sourceFile,
         java.io.File&nbsp;destFile,
         <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
         boolean&nbsp;overwrite,
         boolean&nbsp;preserveLastModified)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used, if
 source files may overwrite newer destination files and the
 last modified time of <code>destFile</code> file should be made equal
 to the last modified time of <code>sourceFile</code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#copyFile(java.io.File, java.io.File, org.apache.tools.ant.types.FilterSetCollection, boolean, boolean, java.lang.String)">copyFile</A></B>(java.io.File&nbsp;sourceFile,
         java.io.File&nbsp;destFile,
         <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
         boolean&nbsp;overwrite,
         boolean&nbsp;preserveLastModified,
         java.lang.String&nbsp;encoding)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used, if
 source files may overwrite newer destination files, the last
 modified time of <code>destFile</code> file should be made
 equal to the last modified time of <code>sourceFile</code> and
 which character encoding to assume.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#copyFile(java.io.File, java.io.File, org.apache.tools.ant.types.FilterSetCollection, java.util.Vector, boolean, boolean, java.lang.String, org.apache.tools.ant.Project)">copyFile</A></B>(java.io.File&nbsp;sourceFile,
         java.io.File&nbsp;destFile,
         <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
         java.util.Vector&nbsp;filterChains,
         boolean&nbsp;overwrite,
         boolean&nbsp;preserveLastModified,
         java.lang.String&nbsp;encoding,
         <A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used, if
 filter chains must be used, if source files may overwrite
 newer destination files and the last modified time of
 <code>destFile</code> file should be made equal
 to the last modified time of <code>sourceFile</code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#copyFile(java.io.File, java.io.File, org.apache.tools.ant.types.FilterSetCollection, java.util.Vector, boolean, boolean, java.lang.String, java.lang.String, org.apache.tools.ant.Project)">copyFile</A></B>(java.io.File&nbsp;sourceFile,
         java.io.File&nbsp;destFile,
         <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
         java.util.Vector&nbsp;filterChains,
         boolean&nbsp;overwrite,
         boolean&nbsp;preserveLastModified,
         java.lang.String&nbsp;inputEncoding,
         java.lang.String&nbsp;outputEncoding,
         <A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used, if
 filter chains must be used, if source files may overwrite
 newer destination files and the last modified time of
 <code>destFile</code> file should be made equal
 to the last modified time of <code>sourceFile</code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#copyFile(java.lang.String, java.lang.String)">copyFile</A></B>(java.lang.String&nbsp;sourceFile,
         java.lang.String&nbsp;destFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to copy a file from a source to a destination.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#copyFile(java.lang.String, java.lang.String, org.apache.tools.ant.types.FilterSetCollection)">copyFile</A></B>(java.lang.String&nbsp;sourceFile,
         java.lang.String&nbsp;destFile,
         <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to copy a file from a source to a destination
 specifying if token filtering must be used.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#copyFile(java.lang.String, java.lang.String, org.apache.tools.ant.types.FilterSetCollection, boolean)">copyFile</A></B>(java.lang.String&nbsp;sourceFile,
         java.lang.String&nbsp;destFile,
         <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
         boolean&nbsp;overwrite)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used and if
 source files may overwrite newer destination files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#copyFile(java.lang.String, java.lang.String, org.apache.tools.ant.types.FilterSetCollection, boolean, boolean)">copyFile</A></B>(java.lang.String&nbsp;sourceFile,
         java.lang.String&nbsp;destFile,
         <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
         boolean&nbsp;overwrite,
         boolean&nbsp;preserveLastModified)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used, if
 source files may overwrite newer destination files and the
 last modified time of <code>destFile</code> file should be made equal
 to the last modified time of <code>sourceFile</code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#copyFile(java.lang.String, java.lang.String, org.apache.tools.ant.types.FilterSetCollection, boolean, boolean, java.lang.String)">copyFile</A></B>(java.lang.String&nbsp;sourceFile,
         java.lang.String&nbsp;destFile,
         <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
         boolean&nbsp;overwrite,
         boolean&nbsp;preserveLastModified,
         java.lang.String&nbsp;encoding)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used, if
 source files may overwrite newer destination files and the
 last modified time of <code>destFile</code> file should be made equal
 to the last modified time of <code>sourceFile</code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#copyFile(java.lang.String, java.lang.String, org.apache.tools.ant.types.FilterSetCollection, java.util.Vector, boolean, boolean, java.lang.String, org.apache.tools.ant.Project)">copyFile</A></B>(java.lang.String&nbsp;sourceFile,
         java.lang.String&nbsp;destFile,
         <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
         java.util.Vector&nbsp;filterChains,
         boolean&nbsp;overwrite,
         boolean&nbsp;preserveLastModified,
         java.lang.String&nbsp;encoding,
         <A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used, if
 filter chains must be used, if source files may overwrite
 newer destination files and the last modified time of
 <code>destFile</code> file should be made equal
 to the last modified time of <code>sourceFile</code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#copyFile(java.lang.String, java.lang.String, org.apache.tools.ant.types.FilterSetCollection, java.util.Vector, boolean, boolean, java.lang.String, java.lang.String, org.apache.tools.ant.Project)">copyFile</A></B>(java.lang.String&nbsp;sourceFile,
         java.lang.String&nbsp;destFile,
         <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
         java.util.Vector&nbsp;filterChains,
         boolean&nbsp;overwrite,
         boolean&nbsp;preserveLastModified,
         java.lang.String&nbsp;inputEncoding,
         java.lang.String&nbsp;outputEncoding,
         <A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used, if
 filter chains must be used, if source files may overwrite
 newer destination files and the last modified time of
 <code>destFile</code> file should be made equal
 to the last modified time of <code>sourceFile</code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#createNewFile(java.io.File)">createNewFile</A></B>(java.io.File&nbsp;f)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This was originally an emulation of File.createNewFile for JDK 1.1,
 but it is now implemented using that method (Ant 1.6.3 onwards).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#createNewFile(java.io.File, boolean)">createNewFile</A></B>(java.io.File&nbsp;f,
              boolean&nbsp;mkdirs)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a new file, optionally creating parent directories.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#createTempFile(java.lang.String, java.lang.String, java.io.File)">createTempFile</A></B>(java.lang.String&nbsp;prefix,
               java.lang.String&nbsp;suffix,
               java.io.File&nbsp;parentDir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a temporary file in a given directory.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#createTempFile(java.lang.String, java.lang.String, java.io.File, boolean)">createTempFile</A></B>(java.lang.String&nbsp;prefix,
               java.lang.String&nbsp;suffix,
               java.io.File&nbsp;parentDir,
               boolean&nbsp;deleteOnExit)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create a temporary file in a given directory.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#delete(java.io.File)">delete</A></B>(java.io.File&nbsp;file)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Delete the file with <CODE>File.delete()</CODE> if the argument is not null.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#dissect(java.lang.String)">dissect</A></B>(java.lang.String&nbsp;path)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Dissect the specified absolute path.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#fileNameEquals(java.io.File, java.io.File)">fileNameEquals</A></B>(java.io.File&nbsp;f1,
               java.io.File&nbsp;f2)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Compares two filenames.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#fromURI(java.lang.String)">fromURI</A></B>(java.lang.String&nbsp;uri)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructs a file path from a <code>file:</code> URI.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#getDefaultEncoding()">getDefaultEncoding</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the default encoding.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#getFileTimestampGranularity()">getFileTimestampGranularity</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the granularity of file timestamps.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.net.URL</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#getFileURL(java.io.File)">getFileURL</A></B>(java.io.File&nbsp;file)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the URL for a file taking into account # characters.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html" title="class in org.apache.tools.ant.util">FileUtils</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#getFileUtils()">getFileUtils</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Method to retrieve The FileUtils, which is shared by all users of this
 method.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#getParentFile(java.io.File)">getParentFile</A></B>(java.io.File&nbsp;f)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.7.
             Just use <CODE>File.getParentFile()</CODE> directly.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#getPath(java.util.List)">getPath</A></B>(java.util.List&nbsp;pathStack)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets path from a <code>List</code> of <code>String</code>s.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#getPath(java.util.List, char)">getPath</A></B>(java.util.List&nbsp;pathStack,
        char&nbsp;separatorChar)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets path from a <code>List</code> of <code>String</code>s.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#getPathStack(java.lang.String)">getPathStack</A></B>(java.lang.String&nbsp;path)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets all names of the path as an array of <code>String</code>s.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#getRelativePath(java.io.File, java.io.File)">getRelativePath</A></B>(java.io.File&nbsp;fromFile,
                java.io.File&nbsp;toFile)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Calculates the relative path between two files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#isAbsolutePath(java.lang.String)">isAbsolutePath</A></B>(java.lang.String&nbsp;filename)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Verifies that the specified filename represents an absolute path.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#isContextRelativePath(java.lang.String)">isContextRelativePath</A></B>(java.lang.String&nbsp;filename)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;On DOS and NetWare, the evaluation of certain file
 specifications is context-dependent.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#isLeadingPath(java.io.File, java.io.File)">isLeadingPath</A></B>(java.io.File&nbsp;leading,
              java.io.File&nbsp;path)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Learn whether one path "leads" another.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#isSymbolicLink(java.io.File, java.lang.String)">isSymbolicLink</A></B>(java.io.File&nbsp;parent,
               java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Checks whether a given file is a symbolic link.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#isUpToDate(java.io.File, java.io.File)">isUpToDate</A></B>(java.io.File&nbsp;source,
           java.io.File&nbsp;dest)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns true if the source is older than the dest.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#isUpToDate(java.io.File, java.io.File, long)">isUpToDate</A></B>(java.io.File&nbsp;source,
           java.io.File&nbsp;dest,
           long&nbsp;granularity)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns true if the source is older than the dest.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#isUpToDate(long, long)">isUpToDate</A></B>(long&nbsp;sourceTime,
           long&nbsp;destTime)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Compare two timestamps for being up to date using the
 current granularity.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#isUpToDate(long, long, long)">isUpToDate</A></B>(long&nbsp;sourceTime,
           long&nbsp;destTime,
           long&nbsp;granularity)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Compare two timestamps for being up to date using
 the specified granularity.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;<A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html" title="class in org.apache.tools.ant.util">FileUtils</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#newFileUtils()">newFileUtils</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.7.
             Use getFileUtils instead,
 FileUtils do not have state.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#normalize(java.lang.String)">normalize</A></B>(java.lang.String&nbsp;path)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&quot;Normalize&quot; the given absolute path.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#readFully(java.io.Reader)">readFully</A></B>(java.io.Reader&nbsp;rdr)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Read from reader till EOF.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#readFully(java.io.Reader, int)">readFully</A></B>(java.io.Reader&nbsp;rdr,
          int&nbsp;bufferSize)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Read from reader till EOF.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#removeLeadingPath(java.io.File, java.io.File)">removeLeadingPath</A></B>(java.io.File&nbsp;leading,
                  java.io.File&nbsp;path)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Removes a leading path from a second path.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#rename(java.io.File, java.io.File)">rename</A></B>(java.io.File&nbsp;from,
       java.io.File&nbsp;to)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Renames a file, even if that involves crossing file system boundaries.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#resolveFile(java.io.File, java.lang.String)">resolveFile</A></B>(java.io.File&nbsp;file,
            java.lang.String&nbsp;filename)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Interpret the filename as a file relative to the given file
 unless the filename already represents an absolute filename.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#setFileLastModified(java.io.File, long)">setFileLastModified</A></B>(java.io.File&nbsp;file,
                    long&nbsp;time)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Calls File.setLastModified(long time).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#toURI(java.lang.String)">toURI</A></B>(java.lang.String&nbsp;path)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructs a <code>file:</code> URI that represents the
 external form of the given pathname.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#toVMSPath(java.io.File)">toVMSPath</A></B>(java.io.File&nbsp;f)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns a VMS String representation of a <code>File</code> object.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html#translatePath(java.lang.String)">translatePath</A></B>(java.lang.String&nbsp;toProcess)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Translate a path into its native (platform specific) format.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="FAT_FILE_TIMESTAMP_GRANULARITY"><!-- --></A><H3>
FAT_FILE_TIMESTAMP_GRANULARITY</H3>
<PRE>
public static final long <B>FAT_FILE_TIMESTAMP_GRANULARITY</B></PRE>
<DL>
<DD>The granularity of timestamps under FAT.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.util.FileUtils.FAT_FILE_TIMESTAMP_GRANULARITY">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="UNIX_FILE_TIMESTAMP_GRANULARITY"><!-- --></A><H3>
UNIX_FILE_TIMESTAMP_GRANULARITY</H3>
<PRE>
public static final long <B>UNIX_FILE_TIMESTAMP_GRANULARITY</B></PRE>
<DL>
<DD>The granularity of timestamps under Unix.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.util.FileUtils.UNIX_FILE_TIMESTAMP_GRANULARITY">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="NTFS_FILE_TIMESTAMP_GRANULARITY"><!-- --></A><H3>
NTFS_FILE_TIMESTAMP_GRANULARITY</H3>
<PRE>
public static final long <B>NTFS_FILE_TIMESTAMP_GRANULARITY</B></PRE>
<DL>
<DD>The granularity of timestamps under the NT File System.
 NTFS has a granularity of 100 nanoseconds, which is less
 than 1 millisecond, so we round this up to 1 millisecond.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.util.FileUtils.NTFS_FILE_TIMESTAMP_GRANULARITY">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="FileUtils()"><!-- --></A><H3>
FileUtils</H3>
<PRE>
protected <B>FileUtils</B>()</PRE>
<DL>
<DD>Empty constructor.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="newFileUtils()"><!-- --></A><H3>
newFileUtils</H3>
<PRE>
public static <A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html" title="class in org.apache.tools.ant.util">FileUtils</A> <B>newFileUtils</B>()</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.7.
             Use getFileUtils instead,
 FileUtils do not have state.</I>
<P>
<DD>Factory method.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a new instance of FileUtils.</DL>
</DD>
</DL>
<HR>

<A NAME="getFileUtils()"><!-- --></A><H3>
getFileUtils</H3>
<PRE>
public static <A HREF="../../../../../org/apache/tools/ant/util/FileUtils.html" title="class in org.apache.tools.ant.util">FileUtils</A> <B>getFileUtils</B>()</PRE>
<DL>
<DD>Method to retrieve The FileUtils, which is shared by all users of this
 method.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>an instance of FileUtils.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getFileURL(java.io.File)"><!-- --></A><H3>
getFileURL</H3>
<PRE>
public java.net.URL <B>getFileURL</B>(java.io.File&nbsp;file)
                        throws java.net.MalformedURLException</PRE>
<DL>
<DD>Get the URL for a file taking into account # characters.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>file</CODE> - the file whose URL representation is required.
<DT><B>Returns:</B><DD>The FileURL value.
<DT><B>Throws:</B>
<DD><CODE>java.net.MalformedURLException</CODE> - if the URL representation cannot be
      formed.</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.lang.String, java.lang.String)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.lang.String&nbsp;sourceFile,
                     java.lang.String&nbsp;destFile)
              throws java.io.IOException</PRE>
<DL>
<DD>Convenience method to copy a file from a source to a destination.
 No filtering is performed.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - Name of file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - Name of file to copy to.
                 Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.lang.String, java.lang.String, org.apache.tools.ant.types.FilterSetCollection)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.lang.String&nbsp;sourceFile,
                     java.lang.String&nbsp;destFile,
                     <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters)
              throws java.io.IOException</PRE>
<DL>
<DD>Convenience method to copy a file from a source to a destination
 specifying if token filtering must be used.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - Name of file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - Name of file to copy to.
                 Must not be <code>null</code>.<DD><CODE>filters</CODE> - the collection of filters to apply to this copy.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.lang.String, java.lang.String, org.apache.tools.ant.types.FilterSetCollection, boolean)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.lang.String&nbsp;sourceFile,
                     java.lang.String&nbsp;destFile,
                     <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
                     boolean&nbsp;overwrite)
              throws java.io.IOException</PRE>
<DL>
<DD>Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used and if
 source files may overwrite newer destination files.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - Name of file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - Name of file to copy to.
                 Must not be <code>null</code>.<DD><CODE>filters</CODE> - the collection of filters to apply to this copy.<DD><CODE>overwrite</CODE> - Whether or not the destination file should be
                  overwritten if it already exists.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.lang.String, java.lang.String, org.apache.tools.ant.types.FilterSetCollection, boolean, boolean)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.lang.String&nbsp;sourceFile,
                     java.lang.String&nbsp;destFile,
                     <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
                     boolean&nbsp;overwrite,
                     boolean&nbsp;preserveLastModified)
              throws java.io.IOException</PRE>
<DL>
<DD>Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used, if
 source files may overwrite newer destination files and the
 last modified time of <code>destFile</code> file should be made equal
 to the last modified time of <code>sourceFile</code>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - Name of file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - Name of file to copy to.
                 Must not be <code>null</code>.<DD><CODE>filters</CODE> - the collection of filters to apply to this copy.<DD><CODE>overwrite</CODE> - Whether or not the destination file should be
                  overwritten if it already exists.<DD><CODE>preserveLastModified</CODE> - Whether or not the last modified time of
                             the resulting file should be set to that
                             of the source file.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.lang.String, java.lang.String, org.apache.tools.ant.types.FilterSetCollection, boolean, boolean, java.lang.String)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.lang.String&nbsp;sourceFile,
                     java.lang.String&nbsp;destFile,
                     <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
                     boolean&nbsp;overwrite,
                     boolean&nbsp;preserveLastModified,
                     java.lang.String&nbsp;encoding)
              throws java.io.IOException</PRE>
<DL>
<DD>Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used, if
 source files may overwrite newer destination files and the
 last modified time of <code>destFile</code> file should be made equal
 to the last modified time of <code>sourceFile</code>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - Name of file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - Name of file to copy to.
                 Must not be <code>null</code>.<DD><CODE>filters</CODE> - the collection of filters to apply to this copy.<DD><CODE>overwrite</CODE> - Whether or not the destination file should be
                  overwritten if it already exists.<DD><CODE>preserveLastModified</CODE> - Whether or not the last modified time of
                             the resulting file should be set to that
                             of the source file.<DD><CODE>encoding</CODE> - the encoding used to read and write the files.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.lang.String, java.lang.String, org.apache.tools.ant.types.FilterSetCollection, java.util.Vector, boolean, boolean, java.lang.String, org.apache.tools.ant.Project)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.lang.String&nbsp;sourceFile,
                     java.lang.String&nbsp;destFile,
                     <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
                     java.util.Vector&nbsp;filterChains,
                     boolean&nbsp;overwrite,
                     boolean&nbsp;preserveLastModified,
                     java.lang.String&nbsp;encoding,
                     <A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)
              throws java.io.IOException</PRE>
<DL>
<DD>Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used, if
 filter chains must be used, if source files may overwrite
 newer destination files and the last modified time of
 <code>destFile</code> file should be made equal
 to the last modified time of <code>sourceFile</code>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - Name of file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - Name of file to copy to.
                 Must not be <code>null</code>.<DD><CODE>filters</CODE> - the collection of filters to apply to this copy.<DD><CODE>filterChains</CODE> - filterChains to apply during the copy.<DD><CODE>overwrite</CODE> - Whether or not the destination file should be
                  overwritten if it already exists.<DD><CODE>preserveLastModified</CODE> - Whether or not the last modified time of
                             the resulting file should be set to that
                             of the source file.<DD><CODE>encoding</CODE> - the encoding used to read and write the files.<DD><CODE>project</CODE> - the project instance.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.lang.String, java.lang.String, org.apache.tools.ant.types.FilterSetCollection, java.util.Vector, boolean, boolean, java.lang.String, java.lang.String, org.apache.tools.ant.Project)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.lang.String&nbsp;sourceFile,
                     java.lang.String&nbsp;destFile,
                     <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
                     java.util.Vector&nbsp;filterChains,
                     boolean&nbsp;overwrite,
                     boolean&nbsp;preserveLastModified,
                     java.lang.String&nbsp;inputEncoding,
                     java.lang.String&nbsp;outputEncoding,
                     <A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)
              throws java.io.IOException</PRE>
<DL>
<DD>Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used, if
 filter chains must be used, if source files may overwrite
 newer destination files and the last modified time of
 <code>destFile</code> file should be made equal
 to the last modified time of <code>sourceFile</code>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - Name of file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - Name of file to copy to.
                 Must not be <code>null</code>.<DD><CODE>filters</CODE> - the collection of filters to apply to this copy.<DD><CODE>filterChains</CODE> - filterChains to apply during the copy.<DD><CODE>overwrite</CODE> - Whether or not the destination file should be
                  overwritten if it already exists.<DD><CODE>preserveLastModified</CODE> - Whether or not the last modified time of
                             the resulting file should be set to that
                             of the source file.<DD><CODE>inputEncoding</CODE> - the encoding used to read the files.<DD><CODE>outputEncoding</CODE> - the encoding used to write the files.<DD><CODE>project</CODE> - the project instance.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.io.File, java.io.File)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.io.File&nbsp;sourceFile,
                     java.io.File&nbsp;destFile)
              throws java.io.IOException</PRE>
<DL>
<DD>Convenience method to copy a file from a source to a destination.
 No filtering is performed.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - the file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - the file to copy to.
                 Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.io.File, java.io.File, org.apache.tools.ant.types.FilterSetCollection)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.io.File&nbsp;sourceFile,
                     java.io.File&nbsp;destFile,
                     <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters)
              throws java.io.IOException</PRE>
<DL>
<DD>Convenience method to copy a file from a source to a destination
 specifying if token filtering must be used.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - the file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - the file to copy to.
                 Must not be <code>null</code>.<DD><CODE>filters</CODE> - the collection of filters to apply to this copy.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.io.File, java.io.File, org.apache.tools.ant.types.FilterSetCollection, boolean)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.io.File&nbsp;sourceFile,
                     java.io.File&nbsp;destFile,
                     <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
                     boolean&nbsp;overwrite)
              throws java.io.IOException</PRE>
<DL>
<DD>Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used and if
 source files may overwrite newer destination files.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - the file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - the file to copy to.
                 Must not be <code>null</code>.<DD><CODE>filters</CODE> - the collection of filters to apply to this copy.<DD><CODE>overwrite</CODE> - Whether or not the destination file should be
                  overwritten if it already exists.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.io.File, java.io.File, org.apache.tools.ant.types.FilterSetCollection, boolean, boolean)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.io.File&nbsp;sourceFile,
                     java.io.File&nbsp;destFile,
                     <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
                     boolean&nbsp;overwrite,
                     boolean&nbsp;preserveLastModified)
              throws java.io.IOException</PRE>
<DL>
<DD>Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used, if
 source files may overwrite newer destination files and the
 last modified time of <code>destFile</code> file should be made equal
 to the last modified time of <code>sourceFile</code>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - the file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - the file to copy to.
                 Must not be <code>null</code>.<DD><CODE>filters</CODE> - the collection of filters to apply to this copy.<DD><CODE>overwrite</CODE> - Whether or not the destination file should be
                  overwritten if it already exists.<DD><CODE>preserveLastModified</CODE> - Whether or not the last modified time of
                             the resulting file should be set to that
                             of the source file.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.io.File, java.io.File, org.apache.tools.ant.types.FilterSetCollection, boolean, boolean, java.lang.String)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.io.File&nbsp;sourceFile,
                     java.io.File&nbsp;destFile,
                     <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
                     boolean&nbsp;overwrite,
                     boolean&nbsp;preserveLastModified,
                     java.lang.String&nbsp;encoding)
              throws java.io.IOException</PRE>
<DL>
<DD>Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used, if
 source files may overwrite newer destination files, the last
 modified time of <code>destFile</code> file should be made
 equal to the last modified time of <code>sourceFile</code> and
 which character encoding to assume.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - the file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - the file to copy to.
                 Must not be <code>null</code>.<DD><CODE>filters</CODE> - the collection of filters to apply to this copy.<DD><CODE>overwrite</CODE> - Whether or not the destination file should be
                  overwritten if it already exists.<DD><CODE>preserveLastModified</CODE> - Whether or not the last modified time of
                             the resulting file should be set to that
                             of the source file.<DD><CODE>encoding</CODE> - the encoding used to read and write the files.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.io.File, java.io.File, org.apache.tools.ant.types.FilterSetCollection, java.util.Vector, boolean, boolean, java.lang.String, org.apache.tools.ant.Project)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.io.File&nbsp;sourceFile,
                     java.io.File&nbsp;destFile,
                     <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
                     java.util.Vector&nbsp;filterChains,
                     boolean&nbsp;overwrite,
                     boolean&nbsp;preserveLastModified,
                     java.lang.String&nbsp;encoding,
                     <A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)
              throws java.io.IOException</PRE>
<DL>
<DD>Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used, if
 filter chains must be used, if source files may overwrite
 newer destination files and the last modified time of
 <code>destFile</code> file should be made equal
 to the last modified time of <code>sourceFile</code>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - the file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - the file to copy to.
                 Must not be <code>null</code>.<DD><CODE>filters</CODE> - the collection of filters to apply to this copy.<DD><CODE>filterChains</CODE> - filterChains to apply during the copy.<DD><CODE>overwrite</CODE> - Whether or not the destination file should be
                  overwritten if it already exists.<DD><CODE>preserveLastModified</CODE> - Whether or not the last modified time of
                             the resulting file should be set to that
                             of the source file.<DD><CODE>encoding</CODE> - the encoding used to read and write the files.<DD><CODE>project</CODE> - the project instance.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="copyFile(java.io.File, java.io.File, org.apache.tools.ant.types.FilterSetCollection, java.util.Vector, boolean, boolean, java.lang.String, java.lang.String, org.apache.tools.ant.Project)"><!-- --></A><H3>
copyFile</H3>
<PRE>
public void <B>copyFile</B>(java.io.File&nbsp;sourceFile,
                     java.io.File&nbsp;destFile,
                     <A HREF="../../../../../org/apache/tools/ant/types/FilterSetCollection.html" title="class in org.apache.tools.ant.types">FilterSetCollection</A>&nbsp;filters,
                     java.util.Vector&nbsp;filterChains,
                     boolean&nbsp;overwrite,
                     boolean&nbsp;preserveLastModified,
                     java.lang.String&nbsp;inputEncoding,
                     java.lang.String&nbsp;outputEncoding,
                     <A HREF="../../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project)
              throws java.io.IOException</PRE>
<DL>
<DD>Convenience method to copy a file from a source to a
 destination specifying if token filtering must be used, if
 filter chains must be used, if source files may overwrite
 newer destination files and the last modified time of
 <code>destFile</code> file should be made equal
 to the last modified time of <code>sourceFile</code>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceFile</CODE> - the file to copy from.
                   Must not be <code>null</code>.<DD><CODE>destFile</CODE> - the file to copy to.
                 Must not be <code>null</code>.<DD><CODE>filters</CODE> - the collection of filters to apply to this copy.<DD><CODE>filterChains</CODE> - filterChains to apply during the copy.<DD><CODE>overwrite</CODE> - Whether or not the destination file should be
                  overwritten if it already exists.<DD><CODE>preserveLastModified</CODE> - Whether or not the last modified time of
                             the resulting file should be set to that
                             of the source file.<DD><CODE>inputEncoding</CODE> - the encoding used to read the files.<DD><CODE>outputEncoding</CODE> - the encoding used to write the files.<DD><CODE>project</CODE> - the project instance.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the copying fails.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setFileLastModified(java.io.File, long)"><!-- --></A><H3>
setFileLastModified</H3>
<PRE>
public void <B>setFileLastModified</B>(java.io.File&nbsp;file,
                                long&nbsp;time)</PRE>
<DL>
<DD>Calls File.setLastModified(long time). Originally written to
 to dynamically bind to that call on Java1.2+.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>file</CODE> - the file whose modified time is to be set<DD><CODE>time</CODE> - the time to which the last modified time is to be set.
             if this is -1, the current time is used.</DL>
</DD>
</DL>
<HR>

<A NAME="resolveFile(java.io.File, java.lang.String)"><!-- --></A><H3>
resolveFile</H3>
<PRE>
public java.io.File <B>resolveFile</B>(java.io.File&nbsp;file,
                                java.lang.String&nbsp;filename)</PRE>
<DL>
<DD>Interpret the filename as a file relative to the given file
 unless the filename already represents an absolute filename.
 Differs from <code>new File(file, filename)</code> in that
 the resulting File's path will always be a normalized,
 absolute pathname.  Also, if it is determined that
 <code>filename</code> is context-relative, <code>file</code>
 will be discarded and the reference will be resolved using
 available context/state information about the filesystem.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>file</CODE> - the "reference" file for relative paths. This
 instance must be an absolute file and must not contain
 &quot;./&quot; or &quot;../&quot; sequences (same for \ instead
 of /).  If it is null, this call is equivalent to
 <code>new java.io.File(filename).getAbsoluteFile()</code>.<DD><CODE>filename</CODE> - a file name.
<DT><B>Returns:</B><DD>an absolute file.
<DT><B>Throws:</B>
<DD><CODE>java.lang.NullPointerException</CODE> - if filename is null.</DL>
</DD>
</DL>
<HR>

<A NAME="isContextRelativePath(java.lang.String)"><!-- --></A><H3>
isContextRelativePath</H3>
<PRE>
public static boolean <B>isContextRelativePath</B>(java.lang.String&nbsp;filename)</PRE>
<DL>
<DD>On DOS and NetWare, the evaluation of certain file
 specifications is context-dependent.  These are filenames
 beginning with a single separator (relative to current root directory)
 and filenames with a drive specification and no intervening separator
 (relative to current directory of the specified root).
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filename</CODE> - the filename to evaluate.
<DT><B>Returns:</B><DD>true if the filename is relative to system context.
<DT><B>Throws:</B>
<DD><CODE>java.lang.NullPointerException</CODE> - if filename is null.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="isAbsolutePath(java.lang.String)"><!-- --></A><H3>
isAbsolutePath</H3>
<PRE>
public static boolean <B>isAbsolutePath</B>(java.lang.String&nbsp;filename)</PRE>
<DL>
<DD>Verifies that the specified filename represents an absolute path.
 Differs from new java.io.File("filename").isAbsolute() in that a path
 beginning with a double file separator--signifying a Windows UNC--must
 at minimum match "\\a\b" to be considered an absolute path.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filename</CODE> - the filename to be checked.
<DT><B>Returns:</B><DD>true if the filename represents an absolute path.
<DT><B>Throws:</B>
<DD><CODE>java.lang.NullPointerException</CODE> - if filename is null.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="translatePath(java.lang.String)"><!-- --></A><H3>
translatePath</H3>
<PRE>
public static java.lang.String <B>translatePath</B>(java.lang.String&nbsp;toProcess)</PRE>
<DL>
<DD>Translate a path into its native (platform specific) format.
 <p>
 This method uses PathTokenizer to separate the input path
 into its components. This handles DOS style paths in a relatively
 sensible way. The file separators are then converted to their platform
 specific versions.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>toProcess</CODE> - The path to be translated.
                  May be <code>null</code>.
<DT><B>Returns:</B><DD>the native version of the specified path or
         an empty string if the path is <code>null</code> or empty.<DT><B>Since:</B></DT>
  <DD>ant 1.7</DD>
<DT><B>See Also:</B><DD><A HREF="../../../../../org/apache/tools/ant/PathTokenizer.html" title="class in org.apache.tools.ant"><CODE>PathTokenizer</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="normalize(java.lang.String)"><!-- --></A><H3>
normalize</H3>
<PRE>
public java.io.File <B>normalize</B>(java.lang.String&nbsp;path)</PRE>
<DL>
<DD>&quot;Normalize&quot; the given absolute path.

 <p>This includes:
 <ul>
   <li>Uppercase the drive letter if there is one.</li>
   <li>Remove redundant slashes after the drive spec.</li>
   <li>Resolve all ./, .\, ../ and ..\ sequences.</li>
   <li>DOS style paths that start with a drive letter will have
     \ as the separator.</li>
 </ul>
 Unlike <CODE>File.getCanonicalPath()</CODE> this method
 specifically does not resolve symbolic links.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>path</CODE> - the path to be normalized.
<DT><B>Returns:</B><DD>the normalized version of the path.
<DT><B>Throws:</B>
<DD><CODE>java.lang.NullPointerException</CODE> - if path is null.</DL>
</DD>
</DL>
<HR>

<A NAME="dissect(java.lang.String)"><!-- --></A><H3>
dissect</H3>
<PRE>
public java.lang.String[] <B>dissect</B>(java.lang.String&nbsp;path)</PRE>
<DL>
<DD>Dissect the specified absolute path.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>path</CODE> - the path to dissect.
<DT><B>Returns:</B><DD>String[] {root, remaining path}.
<DT><B>Throws:</B>
<DD><CODE>java.lang.NullPointerException</CODE> - if path is null.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="toVMSPath(java.io.File)"><!-- --></A><H3>
toVMSPath</H3>
<PRE>
public java.lang.String <B>toVMSPath</B>(java.io.File&nbsp;f)</PRE>
<DL>
<DD>Returns a VMS String representation of a <code>File</code> object.
 This is useful since the JVM by default internally converts VMS paths
 to Unix style.
 The returned String is always an absolute path.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>f</CODE> - The <code>File</code> to get the VMS path for.
<DT><B>Returns:</B><DD>The absolute VMS path to <code>f</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="createTempFile(java.lang.String, java.lang.String, java.io.File)"><!-- --></A><H3>
createTempFile</H3>
<PRE>
public java.io.File <B>createTempFile</B>(java.lang.String&nbsp;prefix,
                                   java.lang.String&nbsp;suffix,
                                   java.io.File&nbsp;parentDir)</PRE>
<DL>
<DD>Create a temporary file in a given directory.

 <p>The file denoted by the returned abstract pathname did not
 exist before this method was invoked, any subsequent invocation
 of this method will yield a different file name.</p>
 <p>
 The filename is prefixNNNNNsuffix where NNNN is a random number.
 </p>
 <p>This method is different from File.createTempFile() of JDK 1.2
 as it doesn't create the file itself.  It uses the location pointed
 to by java.io.tmpdir when the parentDir attribute is null.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>prefix</CODE> - prefix before the random number.<DD><CODE>suffix</CODE> - file extension; include the '.'.<DD><CODE>parentDir</CODE> - Directory to create the temporary file in;
 java.io.tmpdir used if not specified.
<DT><B>Returns:</B><DD>a File reference to the new temporary file.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="createTempFile(java.lang.String, java.lang.String, java.io.File, boolean)"><!-- --></A><H3>
createTempFile</H3>
<PRE>
public java.io.File <B>createTempFile</B>(java.lang.String&nbsp;prefix,
                                   java.lang.String&nbsp;suffix,
                                   java.io.File&nbsp;parentDir,
                                   boolean&nbsp;deleteOnExit)</PRE>
<DL>
<DD>Create a temporary file in a given directory.

 <p>The file denoted by the returned abstract pathname did not
 exist before this method was invoked, any subsequent invocation
 of this method will yield a different file name.</p>
 <p>
 The filename is prefixNNNNNsuffix where NNNN is a random number.
 </p>
 <p>This method is different from File.createTempFile() of JDK 1.2
 as it doesn't create the file itself.  It uses the location pointed
 to by java.io.tmpdir when the parentDir attribute is null.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>prefix</CODE> - prefix before the random number.<DD><CODE>suffix</CODE> - file extension; include the '.'.<DD><CODE>parentDir</CODE> - Directory to create the temporary file in;<DD><CODE>deleteOnExit</CODE> - whether to set the tempfile for deletion on
        normal VM exit.
 java.io.tmpdir used if not specified.
<DT><B>Returns:</B><DD>a File reference to the new temporary file.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="contentEquals(java.io.File, java.io.File)"><!-- --></A><H3>
contentEquals</H3>
<PRE>
public boolean <B>contentEquals</B>(java.io.File&nbsp;f1,
                             java.io.File&nbsp;f2)
                      throws java.io.IOException</PRE>
<DL>
<DD>Compares the contents of two files.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>f1</CODE> - the file whose content is to be compared.<DD><CODE>f2</CODE> - the other file whose content is to be compared.
<DT><B>Returns:</B><DD>true if the content of the files is the same.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the files cannot be read.</DL>
</DD>
</DL>
<HR>

<A NAME="contentEquals(java.io.File, java.io.File, boolean)"><!-- --></A><H3>
contentEquals</H3>
<PRE>
public boolean <B>contentEquals</B>(java.io.File&nbsp;f1,
                             java.io.File&nbsp;f2,
                             boolean&nbsp;textfile)
                      throws java.io.IOException</PRE>
<DL>
<DD>Compares the contents of two files.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>f1</CODE> - the file whose content is to be compared.<DD><CODE>f2</CODE> - the other file whose content is to be compared.<DD><CODE>textfile</CODE> - true if the file is to be treated as a text file and
        differences in kind of line break are to be ignored.
<DT><B>Returns:</B><DD>true if the content of the files is the same.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the files cannot be read.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getParentFile(java.io.File)"><!-- --></A><H3>
getParentFile</H3>
<PRE>
public java.io.File <B>getParentFile</B>(java.io.File&nbsp;f)</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.7.
             Just use <CODE>File.getParentFile()</CODE> directly.</I>
<P>
<DD>This was originally an emulation of <CODE>File.getParentFile()</CODE> for JDK 1.1,
 but it is now implemented using that method (Ant 1.6.3 onwards).
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>f</CODE> - the file whose parent is required.
<DT><B>Returns:</B><DD>the given file's parent, or null if the file does not have a
         parent.<DT><B>Since:</B></DT>
  <DD>1.10</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="readFully(java.io.Reader)"><!-- --></A><H3>
readFully</H3>
<PRE>
public static final java.lang.String <B>readFully</B>(java.io.Reader&nbsp;rdr)
                                        throws java.io.IOException</PRE>
<DL>
<DD>Read from reader till EOF.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rdr</CODE> - the reader from which to read.
<DT><B>Returns:</B><DD>the contents read out of the given reader.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the contents could not be read out from the
         reader.</DL>
</DD>
</DL>
<HR>

<A NAME="readFully(java.io.Reader, int)"><!-- --></A><H3>
readFully</H3>
<PRE>
public static final java.lang.String <B>readFully</B>(java.io.Reader&nbsp;rdr,
                                               int&nbsp;bufferSize)
                                        throws java.io.IOException</PRE>
<DL>
<DD>Read from reader till EOF.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rdr</CODE> - the reader from which to read.<DD><CODE>bufferSize</CODE> - the buffer size to use when reading.
<DT><B>Returns:</B><DD>the contents read out of the given reader.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the contents could not be read out from the
         reader.</DL>
</DD>
</DL>
<HR>

<A NAME="createNewFile(java.io.File)"><!-- --></A><H3>
createNewFile</H3>
<PRE>
public boolean <B>createNewFile</B>(java.io.File&nbsp;f)
                      throws java.io.IOException</PRE>
<DL>
<DD>This was originally an emulation of File.createNewFile for JDK 1.1,
 but it is now implemented using that method (Ant 1.6.3 onwards).

 <p>This method has historically <strong>not</strong> guaranteed that the
 operation was atomic. In its current implementation it is.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>f</CODE> - the file to be created.
<DT><B>Returns:</B><DD>true if the file did not exist already.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="createNewFile(java.io.File, boolean)"><!-- --></A><H3>
createNewFile</H3>
<PRE>
public boolean <B>createNewFile</B>(java.io.File&nbsp;f,
                             boolean&nbsp;mkdirs)
                      throws java.io.IOException</PRE>
<DL>
<DD>Create a new file, optionally creating parent directories.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>f</CODE> - the file to be created.<DD><CODE>mkdirs</CODE> - <code>boolean</code> whether to create parent directories.
<DT><B>Returns:</B><DD>true if the file did not exist already.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="isSymbolicLink(java.io.File, java.lang.String)"><!-- --></A><H3>
isSymbolicLink</H3>
<PRE>
public boolean <B>isSymbolicLink</B>(java.io.File&nbsp;parent,
                              java.lang.String&nbsp;name)
                       throws java.io.IOException</PRE>
<DL>
<DD>Checks whether a given file is a symbolic link.

 <p>It doesn't really test for symbolic links but whether the
 canonical and absolute paths of the file are identical--this
 may lead to false positives on some platforms.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>parent</CODE> - the parent directory of the file to test<DD><CODE>name</CODE> - the name of the file to test.
<DT><B>Returns:</B><DD>true if the file is a symbolic link.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="removeLeadingPath(java.io.File, java.io.File)"><!-- --></A><H3>
removeLeadingPath</H3>
<PRE>
public java.lang.String <B>removeLeadingPath</B>(java.io.File&nbsp;leading,
                                          java.io.File&nbsp;path)</PRE>
<DL>
<DD>Removes a leading path from a second path.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>leading</CODE> - The leading path, must not be null, must be absolute.<DD><CODE>path</CODE> - The path to remove from, must not be null, must be absolute.
<DT><B>Returns:</B><DD>path's normalized absolute if it doesn't start with
 leading; path's path with leading's path removed otherwise.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="isLeadingPath(java.io.File, java.io.File)"><!-- --></A><H3>
isLeadingPath</H3>
<PRE>
public boolean <B>isLeadingPath</B>(java.io.File&nbsp;leading,
                             java.io.File&nbsp;path)</PRE>
<DL>
<DD>Learn whether one path "leads" another.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>leading</CODE> - The leading path, must not be null, must be absolute.<DD><CODE>path</CODE> - The path to remove from, must not be null, must be absolute.
<DT><B>Returns:</B><DD>true if path starts with leading; false otherwise.<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="toURI(java.lang.String)"><!-- --></A><H3>
toURI</H3>
<PRE>
public java.lang.String <B>toURI</B>(java.lang.String&nbsp;path)</PRE>
<DL>
<DD>Constructs a <code>file:</code> URI that represents the
 external form of the given pathname.

 <p>Will be an absolute URI if the given path is absolute.</p>

 <p>This code encodes non ASCII characters too.</p>

 <p>The coding of the output is the same as what File.toURI().toASCIIString() produces</p>

 See <a href="http://www.w3.org/TR/xml11/#dt-sysid">dt-sysid</a>
 which makes some mention of how
 characters not supported by URI Reference syntax should be escaped.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>path</CODE> - the path in the local file system.
<DT><B>Returns:</B><DD>the URI version of the local path.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="fromURI(java.lang.String)"><!-- --></A><H3>
fromURI</H3>
<PRE>
public java.lang.String <B>fromURI</B>(java.lang.String&nbsp;uri)</PRE>
<DL>
<DD>Constructs a file path from a <code>file:</code> URI.

 <p>Will be an absolute path if the given URI is absolute.</p>

 <p>Swallows '%' that are not followed by two characters,
 doesn't deal with non-ASCII characters.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>uri</CODE> - the URI designating a file in the local filesystem.
<DT><B>Returns:</B><DD>the local file system path for the file.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="fileNameEquals(java.io.File, java.io.File)"><!-- --></A><H3>
fileNameEquals</H3>
<PRE>
public boolean <B>fileNameEquals</B>(java.io.File&nbsp;f1,
                              java.io.File&nbsp;f2)</PRE>
<DL>
<DD>Compares two filenames.

 <p>Unlike java.io.File#equals this method will try to compare
 the absolute paths and &quot;normalize&quot; the filenames
 before comparing them.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>f1</CODE> - the file whose name is to be compared.<DD><CODE>f2</CODE> - the other file whose name is to be compared.
<DT><B>Returns:</B><DD>true if the file are for the same file.<DT><B>Since:</B></DT>
  <DD>Ant 1.5.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="rename(java.io.File, java.io.File)"><!-- --></A><H3>
rename</H3>
<PRE>
public void <B>rename</B>(java.io.File&nbsp;from,
                   java.io.File&nbsp;to)
            throws java.io.IOException</PRE>
<DL>
<DD>Renames a file, even if that involves crossing file system boundaries.

 <p>This will remove <code>to</code> (if it exists), ensure that
 <code>to</code>'s parent directory exists and move
 <code>from</code>, which involves deleting <code>from</code> as
 well.</p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>from</CODE> - the file to move.<DD><CODE>to</CODE> - the new file name.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if anything bad happens during this
 process.  Note that <code>to</code> may have been deleted
 already when this happens.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getFileTimestampGranularity()"><!-- --></A><H3>
getFileTimestampGranularity</H3>
<PRE>
public long <B>getFileTimestampGranularity</B>()</PRE>
<DL>
<DD>Get the granularity of file timestamps.
 The choice is made based on OS, which is incorrect--it should really be
 by filesystem. We do not have an easy way to probe for file systems,
 however, so this heuristic gives us a decent default.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the difference, in milliseconds, which two file timestamps must have
 in order for the two files to be considered to have different timestamps.</DL>
</DD>
</DL>
<HR>

<A NAME="isUpToDate(java.io.File, java.io.File, long)"><!-- --></A><H3>
isUpToDate</H3>
<PRE>
public boolean <B>isUpToDate</B>(java.io.File&nbsp;source,
                          java.io.File&nbsp;dest,
                          long&nbsp;granularity)</PRE>
<DL>
<DD>Returns true if the source is older than the dest.
 If the dest file does not exist, then the test returns false; it is
 implicitly not up do date.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>source</CODE> - source file (should be the older).<DD><CODE>dest</CODE> - dest file (should be the newer).<DD><CODE>granularity</CODE> - an offset added to the source time.
<DT><B>Returns:</B><DD>true if the source is older than the dest after accounting
              for granularity.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="isUpToDate(java.io.File, java.io.File)"><!-- --></A><H3>
isUpToDate</H3>
<PRE>
public boolean <B>isUpToDate</B>(java.io.File&nbsp;source,
                          java.io.File&nbsp;dest)</PRE>
<DL>
<DD>Returns true if the source is older than the dest.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>source</CODE> - source file (should be the older).<DD><CODE>dest</CODE> - dest file (should be the newer).
<DT><B>Returns:</B><DD>true if the source is older than the dest, taking the granularity into account.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.3</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="isUpToDate(long, long, long)"><!-- --></A><H3>
isUpToDate</H3>
<PRE>
public boolean <B>isUpToDate</B>(long&nbsp;sourceTime,
                          long&nbsp;destTime,
                          long&nbsp;granularity)</PRE>
<DL>
<DD>Compare two timestamps for being up to date using
 the specified granularity.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceTime</CODE> - timestamp of source file.<DD><CODE>destTime</CODE> - timestamp of dest file.<DD><CODE>granularity</CODE> - os/filesys granularity.
<DT><B>Returns:</B><DD>true if the dest file is considered up to date.</DL>
</DD>
</DL>
<HR>

<A NAME="isUpToDate(long, long)"><!-- --></A><H3>
isUpToDate</H3>
<PRE>
public boolean <B>isUpToDate</B>(long&nbsp;sourceTime,
                          long&nbsp;destTime)</PRE>
<DL>
<DD>Compare two timestamps for being up to date using the
 current granularity.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceTime</CODE> - timestamp of source file.<DD><CODE>destTime</CODE> - timestamp of dest file.
<DT><B>Returns:</B><DD>true if the dest file is considered up to date.</DL>
</DD>
</DL>
<HR>

<A NAME="close(java.io.Writer)"><!-- --></A><H3>
close</H3>
<PRE>
public static void <B>close</B>(java.io.Writer&nbsp;device)</PRE>
<DL>
<DD>Close a Writer without throwing any exception if something went wrong.
 Do not attempt to close it if the argument is null.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>device</CODE> - output writer, can be null.</DL>
</DD>
</DL>
<HR>

<A NAME="close(java.io.Reader)"><!-- --></A><H3>
close</H3>
<PRE>
public static void <B>close</B>(java.io.Reader&nbsp;device)</PRE>
<DL>
<DD>Close a stream without throwing any exception if something went wrong.
 Do not attempt to close it if the argument is null.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>device</CODE> - Reader, can be null.</DL>
</DD>
</DL>
<HR>

<A NAME="close(java.io.OutputStream)"><!-- --></A><H3>
close</H3>
<PRE>
public static void <B>close</B>(java.io.OutputStream&nbsp;device)</PRE>
<DL>
<DD>Close a stream without throwing any exception if something went wrong.
 Do not attempt to close it if the argument is null.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>device</CODE> - stream, can be null.</DL>
</DD>
</DL>
<HR>

<A NAME="close(java.io.InputStream)"><!-- --></A><H3>
close</H3>
<PRE>
public static void <B>close</B>(java.io.InputStream&nbsp;device)</PRE>
<DL>
<DD>Close a stream without throwing any exception if something went wrong.
 Do not attempt to close it if the argument is null.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>device</CODE> - stream, can be null.</DL>
</DD>
</DL>
<HR>

<A NAME="delete(java.io.File)"><!-- --></A><H3>
delete</H3>
<PRE>
public static void <B>delete</B>(java.io.File&nbsp;file)</PRE>
<DL>
<DD>Delete the file with <CODE>File.delete()</CODE> if the argument is not null.
 Do nothing on a null argument.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>file</CODE> - file to delete.</DL>
</DD>
</DL>
<HR>

<A NAME="getRelativePath(java.io.File, java.io.File)"><!-- --></A><H3>
getRelativePath</H3>
<PRE>
public static java.lang.String <B>getRelativePath</B>(java.io.File&nbsp;fromFile,
                                               java.io.File&nbsp;toFile)
                                        throws java.lang.Exception</PRE>
<DL>
<DD>Calculates the relative path between two files.
 <p>
 Implementation note:<br/> This function my throw an IOException if an
 I/O error occurs because its use of the canonical pathname may require
 filesystem queries.
 </p>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fromFile</CODE> - the <code>File</code> to calculate the path from<DD><CODE>toFile</CODE> - the <code>File</code> to calculate the path to
<DT><B>Returns:</B><DD>the relative path between the files
<DT><B>Throws:</B>
<DD><CODE>java.lang.Exception</CODE> - for undocumented reasons<DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
<DT><B>See Also:</B><DD><CODE>File.getCanonicalPath()</CODE></DL>
</DD>
</DL>
<HR>

<A NAME="getPathStack(java.lang.String)"><!-- --></A><H3>
getPathStack</H3>
<PRE>
public static java.lang.String[] <B>getPathStack</B>(java.lang.String&nbsp;path)</PRE>
<DL>
<DD>Gets all names of the path as an array of <code>String</code>s.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>path</CODE> - to get names from
<DT><B>Returns:</B><DD><code>String</code>s, never <code>null</code><DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getPath(java.util.List)"><!-- --></A><H3>
getPath</H3>
<PRE>
public static java.lang.String <B>getPath</B>(java.util.List&nbsp;pathStack)</PRE>
<DL>
<DD>Gets path from a <code>List</code> of <code>String</code>s.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pathStack</CODE> - <code>List</code> of <code>String</code>s to be concated
            as a path.
<DT><B>Returns:</B><DD><code>String</code>, never <code>null</code><DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getPath(java.util.List, char)"><!-- --></A><H3>
getPath</H3>
<PRE>
public static java.lang.String <B>getPath</B>(java.util.List&nbsp;pathStack,
                                       char&nbsp;separatorChar)</PRE>
<DL>
<DD>Gets path from a <code>List</code> of <code>String</code>s.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pathStack</CODE> - <code>List</code> of <code>String</code>s to be concated
            as a path.<DD><CODE>separatorChar</CODE> - <code>char</code> to be used as separator between names in
            path
<DT><B>Returns:</B><DD><code>String</code>, never <code>null</code><DT><B>Since:</B></DT>
  <DD>Ant 1.7</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getDefaultEncoding()"><!-- --></A><H3>
getDefaultEncoding</H3>
<PRE>
public java.lang.String <B>getDefaultEncoding</B>()</PRE>
<DL>
<DD>Get the default encoding.
 This is done by opening an InputStreamReader on
 a dummy InputStream and getting the encoding.
 Could use System.getProperty("file.encoding"), but cannot
 see where this is documented.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the default file encoding.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/util/FileTokenizer.html" title="class in org.apache.tools.ant.util"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/util/FlatFileNameMapper.html" title="class in org.apache.tools.ant.util"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/util/FileUtils.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="FileUtils.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
