<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:22 EST 2006 -->
<TITLE>
ExecTask (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.ExecTask class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="ExecTask (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Exec.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/ExecTask.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ExecTask.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class ExecTask</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.ExecTask</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>ExecTask</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></DL>
</PRE>

<P>
Executes a given command if the os platform is appropriate.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.2</DD>
</DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#cmdl">cmdl</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#failOnError">failOnError</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#newEnvironment">newEnvironment</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html" title="class in org.apache.tools.ant.taskdefs">Redirector</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#redirector">redirector</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/types/RedirectorElement.html" title="class in org.apache.tools.ant.types">RedirectorElement</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#redirectorElement">redirectorElement</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#ExecTask()">ExecTask</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create an instance.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#ExecTask(org.apache.tools.ant.Task)">ExecTask</A></B>(<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;owner)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;create an instance that is helping another task.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#addConfiguredRedirector(org.apache.tools.ant.types.RedirectorElement)">addConfiguredRedirector</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/RedirectorElement.html" title="class in org.apache.tools.ant.types">RedirectorElement</A>&nbsp;redirectorElement)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a <code>RedirectorElement</code> to this task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#addEnv(org.apache.tools.ant.types.Environment.Variable)">addEnv</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</A>&nbsp;var)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add an environment variable to the launched process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#checkConfiguration()">checkConfiguration</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Has the Employee set all necessary attributes?</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#createArg()">createArg</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a command-line argument.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#createHandler()">createHandler</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create the StreamHandler to use with our Execute instance.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs">ExecuteWatchdog</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#createWatchdog()">createWatchdog</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create the Watchdog to kill a runaway process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Do the work.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#getResolveExecutable()">getResolveExecutable</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicates whether to attempt to resolve the executable to a
 file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#isValidOs()">isValidOs</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Is this the OS the Employee wanted?</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#logFlush()">logFlush</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Flush the output stream - if there is one.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#maybeSetResultPropertyValue(int)">maybeSetResultPropertyValue</A></B>(int&nbsp;result)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Helper method to set result property to the
 passed in value if appropriate.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#prepareExec()">prepareExec</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Create an Execute instance with the correct working directory set.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#resolveExecutable(java.lang.String, boolean)">resolveExecutable</A></B>(java.lang.String&nbsp;exec,
                  boolean&nbsp;mustSearchPath)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The method attempts to figure out where the executable is so that we can feed
 the full path.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#runExec(org.apache.tools.ant.taskdefs.Execute)">runExec</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</A>&nbsp;exe)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Run the command using the given Execute instance.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#runExecute(org.apache.tools.ant.taskdefs.Execute)">runExecute</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</A>&nbsp;exe)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A Utility method for this classes and subclasses to run an
 Execute instance (an external command).</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setAppend(boolean)">setAppend</A></B>(boolean&nbsp;append)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether output should be appended to or overwrite an existing file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setCommand(org.apache.tools.ant.types.Commandline)">setCommand</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;cmdl)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets a command line.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setDir(java.io.File)">setDir</A></B>(java.io.File&nbsp;d)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the working directory of the process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setError(java.io.File)">setError</A></B>(java.io.File&nbsp;error)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the File to which the error stream of the process should be redirected.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setErrorProperty(java.lang.String)">setErrorProperty</A></B>(java.lang.String&nbsp;errorProperty)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the name of the property whose value should be set to the error of
 the process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setExecutable(java.lang.String)">setExecutable</A></B>(java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the name of the executable program.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setFailIfExecutionFails(boolean)">setFailIfExecutionFails</A></B>(boolean&nbsp;flag)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether to stop the build if program cannot be started.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setFailonerror(boolean)">setFailonerror</A></B>(boolean&nbsp;fail)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Fail if the command exits with a non-zero return code.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setInput(java.io.File)">setInput</A></B>(java.io.File&nbsp;input)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the input file to use for the task.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setInputString(java.lang.String)">setInputString</A></B>(java.lang.String&nbsp;inputString)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the string to use as input.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setLogError(boolean)">setLogError</A></B>(boolean&nbsp;logError)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Controls whether error output of exec is logged.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setNewenvironment(boolean)">setNewenvironment</A></B>(boolean&nbsp;newenv)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Do not propagate old environment when new environment variables are specified.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setOs(java.lang.String)">setOs</A></B>(java.lang.String&nbsp;os)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;List of operating systems on which the command may be executed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setOsFamily(java.lang.String)">setOsFamily</A></B>(java.lang.String&nbsp;osFamily)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Restrict this execution to a single OS Family</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setOutput(java.io.File)">setOutput</A></B>(java.io.File&nbsp;out)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;File the output of the process is redirected to.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setOutputproperty(java.lang.String)">setOutputproperty</A></B>(java.lang.String&nbsp;outputProp)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the property name whose value should be set to the output of
 the process.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setResolveExecutable(boolean)">setResolveExecutable</A></B>(boolean&nbsp;resolveExecutable)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether to attempt to resolve the executable to a file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setResultProperty(java.lang.String)">setResultProperty</A></B>(java.lang.String&nbsp;resultProperty)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the name of a property in which the return code of the
 command should be stored.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setSearchPath(boolean)">setSearchPath</A></B>(boolean&nbsp;searchPath)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether to search nested, then
 system PATH environment variables for the executable.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setSpawn(boolean)">setSpawn</A></B>(boolean&nbsp;spawn)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether or not you want the process to be spawned.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setTimeout(java.lang.Integer)">setTimeout</A></B>(java.lang.Integer&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the timeout in milliseconds after which the process will be killed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setTimeout(java.lang.Long)">setTimeout</A></B>(java.lang.Long&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the timeout in milliseconds after which the process will be killed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setupRedirector()">setupRedirector</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set up properties on the redirector that we needed to store locally.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setVMLauncher(boolean)">setVMLauncher</A></B>(boolean&nbsp;vmLauncher)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set whether to launch new process with VM, otherwise use the OS's shell.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="failOnError"><!-- --></A><H3>
failOnError</H3>
<PRE>
protected boolean <B>failOnError</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="newEnvironment"><!-- --></A><H3>
newEnvironment</H3>
<PRE>
protected boolean <B>newEnvironment</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="cmdl"><!-- --></A><H3>
cmdl</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A> <B>cmdl</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="redirector"><!-- --></A><H3>
redirector</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/taskdefs/Redirector.html" title="class in org.apache.tools.ant.taskdefs">Redirector</A> <B>redirector</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="redirectorElement"><!-- --></A><H3>
redirectorElement</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/types/RedirectorElement.html" title="class in org.apache.tools.ant.types">RedirectorElement</A> <B>redirectorElement</B></PRE>
<DL>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ExecTask()"><!-- --></A><H3>
ExecTask</H3>
<PRE>
public <B>ExecTask</B>()</PRE>
<DL>
<DD>Create an instance.
 Needs to be configured by binding to a project.
<P>
</DL>
<HR>

<A NAME="ExecTask(org.apache.tools.ant.Task)"><!-- --></A><H3>
ExecTask</H3>
<PRE>
public <B>ExecTask</B>(<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A>&nbsp;owner)</PRE>
<DL>
<DD>create an instance that is helping another task.
 Project, OwningTarget, TaskName and description are all
 pulled out
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>owner</CODE> - task that we belong to</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setSpawn(boolean)"><!-- --></A><H3>
setSpawn</H3>
<PRE>
public void <B>setSpawn</B>(boolean&nbsp;spawn)</PRE>
<DL>
<DD>Set whether or not you want the process to be spawned.
 Default is false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>spawn</CODE> - if true you do not want Ant to wait for the end of the process.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setTimeout(java.lang.Long)"><!-- --></A><H3>
setTimeout</H3>
<PRE>
public void <B>setTimeout</B>(java.lang.Long&nbsp;value)</PRE>
<DL>
<DD>Set the timeout in milliseconds after which the process will be killed.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - timeout in milliseconds.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setTimeout(java.lang.Integer)"><!-- --></A><H3>
setTimeout</H3>
<PRE>
public void <B>setTimeout</B>(java.lang.Integer&nbsp;value)</PRE>
<DL>
<DD>Set the timeout in milliseconds after which the process will be killed.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - timeout in milliseconds.</DL>
</DD>
</DL>
<HR>

<A NAME="setExecutable(java.lang.String)"><!-- --></A><H3>
setExecutable</H3>
<PRE>
public void <B>setExecutable</B>(java.lang.String&nbsp;value)</PRE>
<DL>
<DD>Set the name of the executable program.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>value</CODE> - the name of the executable program.</DL>
</DD>
</DL>
<HR>

<A NAME="setDir(java.io.File)"><!-- --></A><H3>
setDir</H3>
<PRE>
public void <B>setDir</B>(java.io.File&nbsp;d)</PRE>
<DL>
<DD>Set the working directory of the process.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>d</CODE> - the working directory of the process.</DL>
</DD>
</DL>
<HR>

<A NAME="setOs(java.lang.String)"><!-- --></A><H3>
setOs</H3>
<PRE>
public void <B>setOs</B>(java.lang.String&nbsp;os)</PRE>
<DL>
<DD>List of operating systems on which the command may be executed.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>os</CODE> - list of operating systems on which the command may be executed.</DL>
</DD>
</DL>
<HR>

<A NAME="setCommand(org.apache.tools.ant.types.Commandline)"><!-- --></A><H3>
setCommand</H3>
<PRE>
public void <B>setCommand</B>(<A HREF="../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A>&nbsp;cmdl)</PRE>
<DL>
<DD>Sets a command line.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>cmdl</CODE> - command line.</DL>
</DD>
</DL>
<HR>

<A NAME="setOutput(java.io.File)"><!-- --></A><H3>
setOutput</H3>
<PRE>
public void <B>setOutput</B>(java.io.File&nbsp;out)</PRE>
<DL>
<DD>File the output of the process is redirected to. If error is not
 redirected, it too will appear in the output.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>out</CODE> - name of a file to which output should be sent.</DL>
</DD>
</DL>
<HR>

<A NAME="setInput(java.io.File)"><!-- --></A><H3>
setInput</H3>
<PRE>
public void <B>setInput</B>(java.io.File&nbsp;input)</PRE>
<DL>
<DD>Set the input file to use for the task.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>input</CODE> - name of a file from which to get input.</DL>
</DD>
</DL>
<HR>

<A NAME="setInputString(java.lang.String)"><!-- --></A><H3>
setInputString</H3>
<PRE>
public void <B>setInputString</B>(java.lang.String&nbsp;inputString)</PRE>
<DL>
<DD>Set the string to use as input.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>inputString</CODE> - the string which is used as the input source.</DL>
</DD>
</DL>
<HR>

<A NAME="setLogError(boolean)"><!-- --></A><H3>
setLogError</H3>
<PRE>
public void <B>setLogError</B>(boolean&nbsp;logError)</PRE>
<DL>
<DD>Controls whether error output of exec is logged. This is only useful when
 output is being redirected and error output is desired in the Ant log.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>logError</CODE> - set to true to log error output in the normal ant log.</DL>
</DD>
</DL>
<HR>

<A NAME="setError(java.io.File)"><!-- --></A><H3>
setError</H3>
<PRE>
public void <B>setError</B>(java.io.File&nbsp;error)</PRE>
<DL>
<DD>Set the File to which the error stream of the process should be redirected.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>error</CODE> - a file to which stderr should be sent.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setOutputproperty(java.lang.String)"><!-- --></A><H3>
setOutputproperty</H3>
<PRE>
public void <B>setOutputproperty</B>(java.lang.String&nbsp;outputProp)</PRE>
<DL>
<DD>Sets the property name whose value should be set to the output of
 the process.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>outputProp</CODE> - name of property.</DL>
</DD>
</DL>
<HR>

<A NAME="setErrorProperty(java.lang.String)"><!-- --></A><H3>
setErrorProperty</H3>
<PRE>
public void <B>setErrorProperty</B>(java.lang.String&nbsp;errorProperty)</PRE>
<DL>
<DD>Sets the name of the property whose value should be set to the error of
 the process.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>errorProperty</CODE> - name of property.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setFailonerror(boolean)"><!-- --></A><H3>
setFailonerror</H3>
<PRE>
public void <B>setFailonerror</B>(boolean&nbsp;fail)</PRE>
<DL>
<DD>Fail if the command exits with a non-zero return code.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fail</CODE> - if true fail the command on non-zero return code.</DL>
</DD>
</DL>
<HR>

<A NAME="setNewenvironment(boolean)"><!-- --></A><H3>
setNewenvironment</H3>
<PRE>
public void <B>setNewenvironment</B>(boolean&nbsp;newenv)</PRE>
<DL>
<DD>Do not propagate old environment when new environment variables are specified.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>newenv</CODE> - if true, do not propagate old environment
 when new environment variables are specified.</DL>
</DD>
</DL>
<HR>

<A NAME="setResolveExecutable(boolean)"><!-- --></A><H3>
setResolveExecutable</H3>
<PRE>
public void <B>setResolveExecutable</B>(boolean&nbsp;resolveExecutable)</PRE>
<DL>
<DD>Set whether to attempt to resolve the executable to a file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>resolveExecutable</CODE> - if true, attempt to resolve the
 path of the executable.</DL>
</DD>
</DL>
<HR>

<A NAME="setSearchPath(boolean)"><!-- --></A><H3>
setSearchPath</H3>
<PRE>
public void <B>setSearchPath</B>(boolean&nbsp;searchPath)</PRE>
<DL>
<DD>Set whether to search nested, then
 system PATH environment variables for the executable.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>searchPath</CODE> - if true, search PATHs.</DL>
</DD>
</DL>
<HR>

<A NAME="getResolveExecutable()"><!-- --></A><H3>
getResolveExecutable</H3>
<PRE>
public boolean <B>getResolveExecutable</B>()</PRE>
<DL>
<DD>Indicates whether to attempt to resolve the executable to a
 file.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the resolveExecutable flag<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addEnv(org.apache.tools.ant.types.Environment.Variable)"><!-- --></A><H3>
addEnv</H3>
<PRE>
public void <B>addEnv</B>(<A HREF="../../../../../org/apache/tools/ant/types/Environment.Variable.html" title="class in org.apache.tools.ant.types">Environment.Variable</A>&nbsp;var)</PRE>
<DL>
<DD>Add an environment variable to the launched process.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>var</CODE> - new environment variable.</DL>
</DD>
</DL>
<HR>

<A NAME="createArg()"><!-- --></A><H3>
createArg</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Commandline.Argument.html" title="class in org.apache.tools.ant.types">Commandline.Argument</A> <B>createArg</B>()</PRE>
<DL>
<DD>Adds a command-line argument.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>new command line argument created.</DL>
</DD>
</DL>
<HR>

<A NAME="setResultProperty(java.lang.String)"><!-- --></A><H3>
setResultProperty</H3>
<PRE>
public void <B>setResultProperty</B>(java.lang.String&nbsp;resultProperty)</PRE>
<DL>
<DD>Sets the name of a property in which the return code of the
 command should be stored. Only of interest if failonerror=false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>resultProperty</CODE> - name of property.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="maybeSetResultPropertyValue(int)"><!-- --></A><H3>
maybeSetResultPropertyValue</H3>
<PRE>
protected void <B>maybeSetResultPropertyValue</B>(int&nbsp;result)</PRE>
<DL>
<DD>Helper method to set result property to the
 passed in value if appropriate.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>result</CODE> - value desired for the result property value.</DL>
</DD>
</DL>
<HR>

<A NAME="setFailIfExecutionFails(boolean)"><!-- --></A><H3>
setFailIfExecutionFails</H3>
<PRE>
public void <B>setFailIfExecutionFails</B>(boolean&nbsp;flag)</PRE>
<DL>
<DD>Set whether to stop the build if program cannot be started.
 Defaults to true.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>flag</CODE> - stop the build if program cannot be started.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setAppend(boolean)"><!-- --></A><H3>
setAppend</H3>
<PRE>
public void <B>setAppend</B>(boolean&nbsp;append)</PRE>
<DL>
<DD>Set whether output should be appended to or overwrite an existing file.
 Defaults to false.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>append</CODE> - if true append is desired.<DT><B>Since:</B></DT>
  <DD>1.30, Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addConfiguredRedirector(org.apache.tools.ant.types.RedirectorElement)"><!-- --></A><H3>
addConfiguredRedirector</H3>
<PRE>
public void <B>addConfiguredRedirector</B>(<A HREF="../../../../../org/apache/tools/ant/types/RedirectorElement.html" title="class in org.apache.tools.ant.types">RedirectorElement</A>&nbsp;redirectorElement)</PRE>
<DL>
<DD>Add a <code>RedirectorElement</code> to this task.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>redirectorElement</CODE> - <code>RedirectorElement</code>.<DT><B>Since:</B></DT>
  <DD>Ant 1.6.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setOsFamily(java.lang.String)"><!-- --></A><H3>
setOsFamily</H3>
<PRE>
public void <B>setOsFamily</B>(java.lang.String&nbsp;osFamily)</PRE>
<DL>
<DD>Restrict this execution to a single OS Family
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>osFamily</CODE> - the family to restrict to.</DL>
</DD>
</DL>
<HR>

<A NAME="resolveExecutable(java.lang.String, boolean)"><!-- --></A><H3>
resolveExecutable</H3>
<PRE>
protected java.lang.String <B>resolveExecutable</B>(java.lang.String&nbsp;exec,
                                             boolean&nbsp;mustSearchPath)</PRE>
<DL>
<DD>The method attempts to figure out where the executable is so that we can feed
 the full path. We first try basedir, then the exec dir, and then
 fallback to the straight executable name (i.e. on the path).
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>exec</CODE> - the name of the executable.<DD><CODE>mustSearchPath</CODE> - if true, the executable will be looked up in
 the PATH environment and the absolute path is returned.
<DT><B>Returns:</B><DD>the executable as a full path if it can be determined.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Do the work.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - in a number of circumstances:
 <ul>
 <li>if failIfExecFails is set to true and the process cannot be started</li>
 <li>the java13command launcher can send build exceptions</li>
 <li>this list is not exhaustive or limitative</li>
 </ul></DL>
</DD>
</DL>
<HR>

<A NAME="checkConfiguration()"><!-- --></A><H3>
checkConfiguration</H3>
<PRE>
protected void <B>checkConfiguration</B>()
                           throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Has the Employee set all necessary attributes?
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if there are missing required parameters.</DL>
</DD>
</DL>
<HR>

<A NAME="setupRedirector()"><!-- --></A><H3>
setupRedirector</H3>
<PRE>
protected void <B>setupRedirector</B>()</PRE>
<DL>
<DD>Set up properties on the redirector that we needed to store locally.
<P>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="isValidOs()"><!-- --></A><H3>
isValidOs</H3>
<PRE>
protected boolean <B>isValidOs</B>()</PRE>
<DL>
<DD>Is this the OS the Employee wanted?
<P>
<DD><DL>

<DT><B>Returns:</B><DD>boolean.
 <ul>
 <li>
 <li><code>true</code> if the os and osfamily attributes are null.</li>
 <li><code>true</code> if osfamily is set, and the os family and must match
 that of the current OS, according to the logic of
 <A HREF="../../../../../org/apache/tools/ant/taskdefs/condition/Os.html#isOs(java.lang.String, java.lang.String, java.lang.String, java.lang.String)"><CODE>Os.isOs(String, String, String, String)</CODE></A>, and the result of the
 <code>os</code> attribute must also evaluate true.
 </li>
 <li>
 <code>true</code> if os is set, and the system.property os.name
 is found in the os attribute,</li>
 <li><code>false</code> otherwise.</li>
 </ul></DL>
</DD>
</DL>
<HR>

<A NAME="setVMLauncher(boolean)"><!-- --></A><H3>
setVMLauncher</H3>
<PRE>
public void <B>setVMLauncher</B>(boolean&nbsp;vmLauncher)</PRE>
<DL>
<DD>Set whether to launch new process with VM, otherwise use the OS's shell.
 Default value is true.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>vmLauncher</CODE> - true if we want to launch new process with VM,
 false if we want to use the OS's shell.</DL>
</DD>
</DL>
<HR>

<A NAME="prepareExec()"><!-- --></A><H3>
prepareExec</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</A> <B>prepareExec</B>()
                       throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Create an Execute instance with the correct working directory set.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>an instance of the Execute class.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - under unknown circumstances.</DL>
</DD>
</DL>
<HR>

<A NAME="runExecute(org.apache.tools.ant.taskdefs.Execute)"><!-- --></A><H3>
runExecute</H3>
<PRE>
protected final void <B>runExecute</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</A>&nbsp;exe)
                         throws java.io.IOException</PRE>
<DL>
<DD>A Utility method for this classes and subclasses to run an
 Execute instance (an external command).
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>exe</CODE> - instance of the execute class.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - in case of problem to attach to the stdin/stdout/stderr
 streams of the process.</DL>
</DD>
</DL>
<HR>

<A NAME="runExec(org.apache.tools.ant.taskdefs.Execute)"><!-- --></A><H3>
runExec</H3>
<PRE>
protected void <B>runExec</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</A>&nbsp;exe)
                throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Run the command using the given Execute instance. This may be
 overridden by subclasses.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>exe</CODE> - instance of Execute to run.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the new process could not be started
 only if failIfExecFails is set to true (the default).</DL>
</DD>
</DL>
<HR>

<A NAME="createHandler()"><!-- --></A><H3>
createHandler</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteStreamHandler.html" title="interface in org.apache.tools.ant.taskdefs">ExecuteStreamHandler</A> <B>createHandler</B>()
                                      throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Create the StreamHandler to use with our Execute instance.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>instance of ExecuteStreamHandler.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - under unknown circumstances.</DL>
</DD>
</DL>
<HR>

<A NAME="createWatchdog()"><!-- --></A><H3>
createWatchdog</H3>
<PRE>
protected <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteWatchdog.html" title="class in org.apache.tools.ant.taskdefs">ExecuteWatchdog</A> <B>createWatchdog</B>()
                                  throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Create the Watchdog to kill a runaway process.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>instance of ExecuteWatchdog.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - under unknown circumstances.</DL>
</DD>
</DL>
<HR>

<A NAME="logFlush()"><!-- --></A><H3>
logFlush</H3>
<PRE>
protected void <B>logFlush</B>()</PRE>
<DL>
<DD>Flush the output stream - if there is one.
<P>
<DD><DL>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Exec.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/ExecTask.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="ExecTask.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
