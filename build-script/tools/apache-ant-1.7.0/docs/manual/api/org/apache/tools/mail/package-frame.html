<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:35 EST 2006 -->
<TITLE>
org.apache.tools.mail (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.mail package">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">


</HEAD>

<BODY BGCOLOR="white">
<FONT size="+1" CLASS="FrameTitleFont">
<A HREF="../../../../org/apache/tools/mail/package-summary.html" target="classFrame">org.apache.tools.mail</A></FONT>
<TABLE BORDER="0" WIDTH="100%" SUMMARY="">
<TR>
<TD NOWRAP><FONT size="+1" CLASS="FrameHeadingFont">
Classes</FONT>&nbsp;
<FONT CLASS="FrameItemFont">
<BR>
<A HREF="MailMessage.html" title="class in org.apache.tools.mail" target="classFrame">MailMessage</A>
<BR>
<A HREF="SmtpResponseReader.html" title="class in org.apache.tools.mail" target="classFrame">SmtpResponseReader</A></FONT></TD>
</TR>
</TABLE>


<TABLE BORDER="0" WIDTH="100%" SUMMARY="">
<TR>
<TD NOWRAP><FONT size="+1" CLASS="FrameHeadingFont">
Exceptions</FONT>&nbsp;
<FONT CLASS="FrameItemFont">
<BR>
<A HREF="ErrorInQuitException.html" title="class in org.apache.tools.mail" target="classFrame">ErrorInQuitException</A></FONT></TD>
</TR>
</TABLE>


</BODY>
</HTML>
