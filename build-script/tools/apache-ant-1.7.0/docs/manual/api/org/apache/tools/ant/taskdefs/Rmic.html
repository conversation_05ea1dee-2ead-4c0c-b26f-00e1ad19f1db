<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:24 EST 2006 -->
<TITLE>
Rmic (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.Rmic class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Rmic (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ResourceCount.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Rmic.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Rmic.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class Rmic</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.MatchingTask</A>
              <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.Rmic</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable, <A HREF="../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>Rmic</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></DL>
</PRE>

<P>
Runs the rmic compiler against classes.</p>
 <p>Rmic can be run on a single class (as specified with the classname
 attribute) or a number of classes at once (all classes below base that
 are neither _Stub nor _Skel classes).  If you want to rmic a single
 class and this class is a class nested into another class, you have to
 specify the classname in the form <code>Outer$$Inner</code> instead of
 <code>Outer.Inner</code>.</p>
 <p>It is possible to refine the set of files that are being rmiced. This can
 be done with the <i>includes</i>, <i>includesfile</i>, <i>excludes</i>,
 <i>excludesfile</i> and <i>defaultexcludes</i>
 attributes. With the <i>includes</i> or <i>includesfile</i> attribute you
 specify the files you want to have included by using patterns. The
 <i>exclude</i> or <i>excludesfile</i> attribute is used to specify
 the files you want to have excluded. This is also done with patterns. And
 finally with the <i>defaultexcludes</i> attribute, you can specify whether
 you want to use default exclusions or not. See the section on
 directory based tasks</a>, on how the
 inclusion/exclusion of files works, and how to write patterns.</p>
 <p>This task forms an implicit FileSet and
 supports all attributes of <code>&lt;fileset&gt;</code>
 (<code>dir</code> becomes <code>base</code>) as well as the nested
 <code>&lt;include&gt;</code>, <code>&lt;exclude&gt;</code> and
 <code>&lt;patternset&gt;</code> elements.</p>
 <p>It is possible to use different compilers. This can be selected
 with the &quot;build.rmic&quot; property or the <code>compiler</code>
 attribute. <a name="compilervalues">There are three choices</a>:</p>
 <ul>
   <li>sun (the standard compiler of the JDK)</li>
   <li>kaffe (the standard compiler of
       <a href="http://www.kaffe.org">Kaffe</a>)</li>
   <li>weblogic</li>
 </ul>

 <p> The <a href="http://dione.zcu.cz/~toman40/miniRMI/">miniRMI</a>
 project contains a compiler implementation for this task as well,
 please consult miniRMI's documentation to learn how to use it.</p>
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.1</DD>
</DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.taskdefs">Rmic.ImplementationSpecificArgument</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds an "compiler" attribute to Commandline$Attribute used to
 filter command line attributes based on the current
 implementation.</TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#ERROR_BASE_NOT_SET">ERROR_BASE_NOT_SET</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base attribute not set message</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#ERROR_LOADING_CAUSED_EXCEPTION">ERROR_LOADING_CAUSED_EXCEPTION</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;loaded error message</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#ERROR_NO_BASE_EXISTS">ERROR_NO_BASE_EXISTS</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base not exists message</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#ERROR_NOT_A_DIR">ERROR_NOT_A_DIR</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;base not a directory message</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#ERROR_NOT_DEFINED">ERROR_NOT_DEFINED</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;not defined message</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#ERROR_NOT_FOUND">ERROR_NOT_FOUND</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;could not be found message</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#ERROR_RMIC_FAILED">ERROR_RMIC_FAILED</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;rmic failed message</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#ERROR_UNABLE_TO_VERIFY_CLASS">ERROR_UNABLE_TO_VERIFY_CLASS</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;unable to verify message</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.MatchingTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#fileset">fileset</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#Rmic()">Rmic</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for Rmic.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#createClasspath()">createClasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a nested classpath element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.taskdefs">Rmic.ImplementationSpecificArgument</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#createCompilerArg()">createCompilerArg</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds an implementation specific command line argument.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#createExtdirs()">createExtdirs</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Maybe creates a nested extdirs element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;execute by creating an instance of an implementation
 class and getting to do the work</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getBase()">getBase</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the base directory to output generated class.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getClassname()">getClassname</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the class name to compile.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getClasspath()">getClasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the classpath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getCompileList()">getCompileList</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getCompiler()">getCompiler</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;get the name of the current compiler</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getCurrentCompilerArgs()">getCurrentCompilerArgs</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the additional implementation specific command line arguments.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getDebug()">getDebug</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the debug flag.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getExtdirs()">getExtdirs</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the extension directories that will be used during the
 compilation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getFileList()">getFileList</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets file list to compile.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getFiltering()">getFiltering</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets whether token filtering is set</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getIdl()">getIdl</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets IDL flags.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getIdlopts()">getIdlopts</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets additional arguments for idl compile.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getIiop()">getIiop</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets iiop flags.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getIiopopts()">getIiopopts</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets additional arguments for iiop.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getIncludeantruntime()">getIncludeantruntime</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets whether or not the ant classpath is to be included in the
 task's classpath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getIncludejavaruntime()">getIncludejavaruntime</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets whether or not the java runtime should be included in this
 task's classpath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.ClassLoader</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getLoader()">getLoader</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Classloader for the Employee-specified classpath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getRemoteInterface(java.lang.Class)">getRemoteInterface</A></B>(java.lang.Class&nbsp;testClass)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the topmost interface that extends Remote for a given
 class - if one exists.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getSourceBase()">getSourceBase</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the source dirs to find the source java files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getStubVersion()">getStubVersion</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the JDK version for the generated stub code.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#getVerify()">getVerify</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get verify flag.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#isValidRmiRemote(java.lang.String)">isValidRmiRemote</A></B>(java.lang.String&nbsp;classname)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Load named class and test whether it can be rmic'ed</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#scanDir(java.io.File, java.lang.String[], org.apache.tools.ant.util.FileNameMapper)">scanDir</A></B>(java.io.File&nbsp;baseDir,
        java.lang.String[]&nbsp;files,
        <A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A>&nbsp;mapper)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Scans the directory looking for class files to be compiled.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setBase(java.io.File)">setBase</A></B>(java.io.File&nbsp;base)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the location to store the compiled files; required</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setClassname(java.lang.String)">setClassname</A></B>(java.lang.String&nbsp;classname)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the class to run <code>rmic</code> against;
 optional</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the classpath to be used for this compilation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setClasspathRef(org.apache.tools.ant.types.Reference)">setClasspathRef</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;pathRef)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds to the classpath a reference to
 a &lt;path&gt; defined elsewhere.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setCompiler(java.lang.String)">setCompiler</A></B>(java.lang.String&nbsp;compiler)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the compiler implementation to use; optional,
 defaults to the value of the <code>build.rmic</code> property,
 or failing that, default compiler for the current VM</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setDebug(boolean)">setDebug</A></B>(boolean&nbsp;debug)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Generate debug info (passes -g to rmic);
 optional, defaults to false</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setExtdirs(org.apache.tools.ant.types.Path)">setExtdirs</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;extDirs)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the extension directories that will be used during the
 compilation; optional.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setFiltering(boolean)">setFiltering</A></B>(boolean&nbsp;filter)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets token filtering [optional], default=false</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setIdl(boolean)">setIdl</A></B>(boolean&nbsp;idl)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicates that IDL output should be
 generated.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setIdlopts(java.lang.String)">setIdlopts</A></B>(java.lang.String&nbsp;idlOpts)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;pass additional arguments for IDL compile</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setIiop(boolean)">setIiop</A></B>(boolean&nbsp;iiop)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicates that IIOP compatible stubs should
 be generated; optional, defaults to false
 if not set.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setIiopopts(java.lang.String)">setIiopopts</A></B>(java.lang.String&nbsp;iiopOpts)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set additional arguments for iiop</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setIncludeantruntime(boolean)">setIncludeantruntime</A></B>(boolean&nbsp;include)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets whether or not to include ant's own classpath in this task's
 classpath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setIncludejavaruntime(boolean)">setIncludejavaruntime</A></B>(boolean&nbsp;include)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;task's classpath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setSourceBase(java.io.File)">setSourceBase</A></B>(java.io.File&nbsp;sourceBase)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;optional directory to save generated source files to.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setStubVersion(java.lang.String)">setStubVersion</A></B>(java.lang.String&nbsp;stubVersion)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Specify the JDK version for the generated stub code.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.html#setVerify(boolean)">setVerify</A></B>(boolean&nbsp;verify)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Flag to enable verification so that the classes
 found by the directory match are
 checked to see if they implement java.rmi.Remote.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.MatchingTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createExclude()">createExclude</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createExcludesFile()">createExcludesFile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createInclude()">createInclude</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createIncludesFile()">createIncludesFile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createPatternSet()">createPatternSet</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#hasSelectors()">hasSelectors</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#selectorCount()">selectorCount</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#selectorElements()">selectorElements</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setExcludes(java.lang.String)">setExcludes</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setIncludes(java.lang.String)">setIncludes</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#XsetItems(java.lang.String)">XsetItems</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="ERROR_RMIC_FAILED"><!-- --></A><H3>
ERROR_RMIC_FAILED</H3>
<PRE>
public static final java.lang.String <B>ERROR_RMIC_FAILED</B></PRE>
<DL>
<DD>rmic failed message
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Rmic.ERROR_RMIC_FAILED">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_UNABLE_TO_VERIFY_CLASS"><!-- --></A><H3>
ERROR_UNABLE_TO_VERIFY_CLASS</H3>
<PRE>
public static final java.lang.String <B>ERROR_UNABLE_TO_VERIFY_CLASS</B></PRE>
<DL>
<DD>unable to verify message
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Rmic.ERROR_UNABLE_TO_VERIFY_CLASS">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_NOT_FOUND"><!-- --></A><H3>
ERROR_NOT_FOUND</H3>
<PRE>
public static final java.lang.String <B>ERROR_NOT_FOUND</B></PRE>
<DL>
<DD>could not be found message
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Rmic.ERROR_NOT_FOUND">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_NOT_DEFINED"><!-- --></A><H3>
ERROR_NOT_DEFINED</H3>
<PRE>
public static final java.lang.String <B>ERROR_NOT_DEFINED</B></PRE>
<DL>
<DD>not defined message
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Rmic.ERROR_NOT_DEFINED">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_LOADING_CAUSED_EXCEPTION"><!-- --></A><H3>
ERROR_LOADING_CAUSED_EXCEPTION</H3>
<PRE>
public static final java.lang.String <B>ERROR_LOADING_CAUSED_EXCEPTION</B></PRE>
<DL>
<DD>loaded error message
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Rmic.ERROR_LOADING_CAUSED_EXCEPTION">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_NO_BASE_EXISTS"><!-- --></A><H3>
ERROR_NO_BASE_EXISTS</H3>
<PRE>
public static final java.lang.String <B>ERROR_NO_BASE_EXISTS</B></PRE>
<DL>
<DD>base not exists message
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Rmic.ERROR_NO_BASE_EXISTS">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_NOT_A_DIR"><!-- --></A><H3>
ERROR_NOT_A_DIR</H3>
<PRE>
public static final java.lang.String <B>ERROR_NOT_A_DIR</B></PRE>
<DL>
<DD>base not a directory message
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Rmic.ERROR_NOT_A_DIR">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_BASE_NOT_SET"><!-- --></A><H3>
ERROR_BASE_NOT_SET</H3>
<PRE>
public static final java.lang.String <B>ERROR_BASE_NOT_SET</B></PRE>
<DL>
<DD>base attribute not set message
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../constant-values.html#org.apache.tools.ant.taskdefs.Rmic.ERROR_BASE_NOT_SET">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Rmic()"><!-- --></A><H3>
Rmic</H3>
<PRE>
public <B>Rmic</B>()</PRE>
<DL>
<DD>Constructor for Rmic.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setBase(java.io.File)"><!-- --></A><H3>
setBase</H3>
<PRE>
public void <B>setBase</B>(java.io.File&nbsp;base)</PRE>
<DL>
<DD>Sets the location to store the compiled files; required
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>base</CODE> - the location to store the compiled files</DL>
</DD>
</DL>
<HR>

<A NAME="getBase()"><!-- --></A><H3>
getBase</H3>
<PRE>
public java.io.File <B>getBase</B>()</PRE>
<DL>
<DD>Gets the base directory to output generated class.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the location of the compiled files</DL>
</DD>
</DL>
<HR>

<A NAME="setClassname(java.lang.String)"><!-- --></A><H3>
setClassname</H3>
<PRE>
public void <B>setClassname</B>(java.lang.String&nbsp;classname)</PRE>
<DL>
<DD>Sets the class to run <code>rmic</code> against;
 optional
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classname</CODE> - the name of the class for rmic to create code for</DL>
</DD>
</DL>
<HR>

<A NAME="getClassname()"><!-- --></A><H3>
getClassname</H3>
<PRE>
public java.lang.String <B>getClassname</B>()</PRE>
<DL>
<DD>Gets the class name to compile.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the name of the class to compile</DL>
</DD>
</DL>
<HR>

<A NAME="setSourceBase(java.io.File)"><!-- --></A><H3>
setSourceBase</H3>
<PRE>
public void <B>setSourceBase</B>(java.io.File&nbsp;sourceBase)</PRE>
<DL>
<DD>optional directory to save generated source files to.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>sourceBase</CODE> - the directory to save source files to.</DL>
</DD>
</DL>
<HR>

<A NAME="getSourceBase()"><!-- --></A><H3>
getSourceBase</H3>
<PRE>
public java.io.File <B>getSourceBase</B>()</PRE>
<DL>
<DD>Gets the source dirs to find the source java files.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>sourceBase the directory containing the source files.</DL>
</DD>
</DL>
<HR>

<A NAME="setStubVersion(java.lang.String)"><!-- --></A><H3>
setStubVersion</H3>
<PRE>
public void <B>setStubVersion</B>(java.lang.String&nbsp;stubVersion)</PRE>
<DL>
<DD>Specify the JDK version for the generated stub code.
 Specify &quot;1.1&quot; to pass the &quot;-v1.1&quot; option to rmic.</td>
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>stubVersion</CODE> - the JDK version</DL>
</DD>
</DL>
<HR>

<A NAME="getStubVersion()"><!-- --></A><H3>
getStubVersion</H3>
<PRE>
public java.lang.String <B>getStubVersion</B>()</PRE>
<DL>
<DD>Gets the JDK version for the generated stub code.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>stubVersion</DL>
</DD>
</DL>
<HR>

<A NAME="setFiltering(boolean)"><!-- --></A><H3>
setFiltering</H3>
<PRE>
public void <B>setFiltering</B>(boolean&nbsp;filter)</PRE>
<DL>
<DD>Sets token filtering [optional], default=false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filter</CODE> - turn on token filtering</DL>
</DD>
</DL>
<HR>

<A NAME="getFiltering()"><!-- --></A><H3>
getFiltering</H3>
<PRE>
public boolean <B>getFiltering</B>()</PRE>
<DL>
<DD>Gets whether token filtering is set
<P>
<DD><DL>

<DT><B>Returns:</B><DD>filtering</DL>
</DD>
</DL>
<HR>

<A NAME="setDebug(boolean)"><!-- --></A><H3>
setDebug</H3>
<PRE>
public void <B>setDebug</B>(boolean&nbsp;debug)</PRE>
<DL>
<DD>Generate debug info (passes -g to rmic);
 optional, defaults to false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>debug</CODE> - turn on debug info</DL>
</DD>
</DL>
<HR>

<A NAME="getDebug()"><!-- --></A><H3>
getDebug</H3>
<PRE>
public boolean <B>getDebug</B>()</PRE>
<DL>
<DD>Gets the debug flag.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>debug</DL>
</DD>
</DL>
<HR>

<A NAME="setClasspath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setClasspath</H3>
<PRE>
public void <B>setClasspath</B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;classpath)</PRE>
<DL>
<DD>Set the classpath to be used for this compilation.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classpath</CODE> - the classpath used for this compilation</DL>
</DD>
</DL>
<HR>

<A NAME="createClasspath()"><!-- --></A><H3>
createClasspath</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createClasspath</B>()</PRE>
<DL>
<DD>Creates a nested classpath element.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>classpath</DL>
</DD>
</DL>
<HR>

<A NAME="setClasspathRef(org.apache.tools.ant.types.Reference)"><!-- --></A><H3>
setClasspathRef</H3>
<PRE>
public void <B>setClasspathRef</B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;pathRef)</PRE>
<DL>
<DD>Adds to the classpath a reference to
 a &lt;path&gt; defined elsewhere.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pathRef</CODE> - the reference to add to the classpath</DL>
</DD>
</DL>
<HR>

<A NAME="getClasspath()"><!-- --></A><H3>
getClasspath</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>getClasspath</B>()</PRE>
<DL>
<DD>Gets the classpath.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the classpath</DL>
</DD>
</DL>
<HR>

<A NAME="setVerify(boolean)"><!-- --></A><H3>
setVerify</H3>
<PRE>
public void <B>setVerify</B>(boolean&nbsp;verify)</PRE>
<DL>
<DD>Flag to enable verification so that the classes
 found by the directory match are
 checked to see if they implement java.rmi.Remote.
 optional; This defaults to false if not set.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>verify</CODE> - turn on verification for classes</DL>
</DD>
</DL>
<HR>

<A NAME="getVerify()"><!-- --></A><H3>
getVerify</H3>
<PRE>
public boolean <B>getVerify</B>()</PRE>
<DL>
<DD>Get verify flag.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>verify</DL>
</DD>
</DL>
<HR>

<A NAME="setIiop(boolean)"><!-- --></A><H3>
setIiop</H3>
<PRE>
public void <B>setIiop</B>(boolean&nbsp;iiop)</PRE>
<DL>
<DD>Indicates that IIOP compatible stubs should
 be generated; optional, defaults to false
 if not set.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>iiop</CODE> - generate IIOP compatible stubs</DL>
</DD>
</DL>
<HR>

<A NAME="getIiop()"><!-- --></A><H3>
getIiop</H3>
<PRE>
public boolean <B>getIiop</B>()</PRE>
<DL>
<DD>Gets iiop flags.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>iiop</DL>
</DD>
</DL>
<HR>

<A NAME="setIiopopts(java.lang.String)"><!-- --></A><H3>
setIiopopts</H3>
<PRE>
public void <B>setIiopopts</B>(java.lang.String&nbsp;iiopOpts)</PRE>
<DL>
<DD>Set additional arguments for iiop
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>iiopOpts</CODE> - additional arguments for iiop</DL>
</DD>
</DL>
<HR>

<A NAME="getIiopopts()"><!-- --></A><H3>
getIiopopts</H3>
<PRE>
public java.lang.String <B>getIiopopts</B>()</PRE>
<DL>
<DD>Gets additional arguments for iiop.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>iiopOpts</DL>
</DD>
</DL>
<HR>

<A NAME="setIdl(boolean)"><!-- --></A><H3>
setIdl</H3>
<PRE>
public void <B>setIdl</B>(boolean&nbsp;idl)</PRE>
<DL>
<DD>Indicates that IDL output should be
 generated.  This defaults to false
 if not set.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>idl</CODE> - generate IDL output</DL>
</DD>
</DL>
<HR>

<A NAME="getIdl()"><!-- --></A><H3>
getIdl</H3>
<PRE>
public boolean <B>getIdl</B>()</PRE>
<DL>
<DD>Gets IDL flags.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the idl flag</DL>
</DD>
</DL>
<HR>

<A NAME="setIdlopts(java.lang.String)"><!-- --></A><H3>
setIdlopts</H3>
<PRE>
public void <B>setIdlopts</B>(java.lang.String&nbsp;idlOpts)</PRE>
<DL>
<DD>pass additional arguments for IDL compile
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>idlOpts</CODE> - additional IDL arguments</DL>
</DD>
</DL>
<HR>

<A NAME="getIdlopts()"><!-- --></A><H3>
getIdlopts</H3>
<PRE>
public java.lang.String <B>getIdlopts</B>()</PRE>
<DL>
<DD>Gets additional arguments for idl compile.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the idl options</DL>
</DD>
</DL>
<HR>

<A NAME="getFileList()"><!-- --></A><H3>
getFileList</H3>
<PRE>
public java.util.Vector <B>getFileList</B>()</PRE>
<DL>
<DD>Gets file list to compile.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the list of files to compile.</DL>
</DD>
</DL>
<HR>

<A NAME="setIncludeantruntime(boolean)"><!-- --></A><H3>
setIncludeantruntime</H3>
<PRE>
public void <B>setIncludeantruntime</B>(boolean&nbsp;include)</PRE>
<DL>
<DD>Sets whether or not to include ant's own classpath in this task's
 classpath.
 Optional; default is <code>true</code>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>include</CODE> - if true include ant's classpath</DL>
</DD>
</DL>
<HR>

<A NAME="getIncludeantruntime()"><!-- --></A><H3>
getIncludeantruntime</H3>
<PRE>
public boolean <B>getIncludeantruntime</B>()</PRE>
<DL>
<DD>Gets whether or not the ant classpath is to be included in the
 task's classpath.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true if ant's classpath is to be included</DL>
</DD>
</DL>
<HR>

<A NAME="setIncludejavaruntime(boolean)"><!-- --></A><H3>
setIncludejavaruntime</H3>
<PRE>
public void <B>setIncludejavaruntime</B>(boolean&nbsp;include)</PRE>
<DL>
<DD>task's classpath.
 Enables or disables including the default run-time
 libraries from the executing VM; optional,
 defaults to false
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>include</CODE> - if true include default run-time libraries</DL>
</DD>
</DL>
<HR>

<A NAME="getIncludejavaruntime()"><!-- --></A><H3>
getIncludejavaruntime</H3>
<PRE>
public boolean <B>getIncludejavaruntime</B>()</PRE>
<DL>
<DD>Gets whether or not the java runtime should be included in this
 task's classpath.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true if default run-time libraries are included</DL>
</DD>
</DL>
<HR>

<A NAME="setExtdirs(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setExtdirs</H3>
<PRE>
public void <B>setExtdirs</B>(<A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;extDirs)</PRE>
<DL>
<DD>Sets the extension directories that will be used during the
 compilation; optional.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>extDirs</CODE> - the extension directories to be used</DL>
</DD>
</DL>
<HR>

<A NAME="createExtdirs()"><!-- --></A><H3>
createExtdirs</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createExtdirs</B>()</PRE>
<DL>
<DD>Maybe creates a nested extdirs element.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>path object to be configured with the extension directories</DL>
</DD>
</DL>
<HR>

<A NAME="getExtdirs()"><!-- --></A><H3>
getExtdirs</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>getExtdirs</B>()</PRE>
<DL>
<DD>Gets the extension directories that will be used during the
 compilation.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the extension directories to be used</DL>
</DD>
</DL>
<HR>

<A NAME="getCompileList()"><!-- --></A><H3>
getCompileList</H3>
<PRE>
public java.util.Vector <B>getCompileList</B>()</PRE>
<DL>
<DD><DL>

<DT><B>Returns:</B><DD>the compile list.</DL>
</DD>
</DL>
<HR>

<A NAME="setCompiler(java.lang.String)"><!-- --></A><H3>
setCompiler</H3>
<PRE>
public void <B>setCompiler</B>(java.lang.String&nbsp;compiler)</PRE>
<DL>
<DD>Sets the compiler implementation to use; optional,
 defaults to the value of the <code>build.rmic</code> property,
 or failing that, default compiler for the current VM
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>compiler</CODE> - the compiler implemention to use<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getCompiler()"><!-- --></A><H3>
getCompiler</H3>
<PRE>
public java.lang.String <B>getCompiler</B>()</PRE>
<DL>
<DD>get the name of the current compiler
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the name of the compiler<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="createCompilerArg()"><!-- --></A><H3>
createCompilerArg</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.taskdefs">Rmic.ImplementationSpecificArgument</A> <B>createCompilerArg</B>()</PRE>
<DL>
<DD>Adds an implementation specific command line argument.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>an object to be configured with a command line argument<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getCurrentCompilerArgs()"><!-- --></A><H3>
getCurrentCompilerArgs</H3>
<PRE>
public java.lang.String[] <B>getCurrentCompilerArgs</B>()</PRE>
<DL>
<DD>Get the additional implementation specific command line arguments.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>array of command line arguments, guaranteed to be non-null.<DT><B>Since:</B></DT>
  <DD>Ant 1.5</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>execute by creating an instance of an implementation
 class and getting to do the work
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if there's a problem with baseDir or RMIC</DL>
</DD>
</DL>
<HR>

<A NAME="scanDir(java.io.File, java.lang.String[], org.apache.tools.ant.util.FileNameMapper)"><!-- --></A><H3>
scanDir</H3>
<PRE>
protected void <B>scanDir</B>(java.io.File&nbsp;baseDir,
                       java.lang.String[]&nbsp;files,
                       <A HREF="../../../../../org/apache/tools/ant/util/FileNameMapper.html" title="interface in org.apache.tools.ant.util">FileNameMapper</A>&nbsp;mapper)</PRE>
<DL>
<DD>Scans the directory looking for class files to be compiled.
 The result is returned in the class variable compileList.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>baseDir</CODE> - the base direction<DD><CODE>files</CODE> - the list of files to scan<DD><CODE>mapper</CODE> - the mapper of files to target files</DL>
</DD>
</DL>
<HR>

<A NAME="isValidRmiRemote(java.lang.String)"><!-- --></A><H3>
isValidRmiRemote</H3>
<PRE>
public boolean <B>isValidRmiRemote</B>(java.lang.String&nbsp;classname)</PRE>
<DL>
<DD>Load named class and test whether it can be rmic'ed
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classname</CODE> - the name of the class to be tested
<DT><B>Returns:</B><DD>true if the class can be rmic'ed</DL>
</DD>
</DL>
<HR>

<A NAME="getRemoteInterface(java.lang.Class)"><!-- --></A><H3>
getRemoteInterface</H3>
<PRE>
public java.lang.Class <B>getRemoteInterface</B>(java.lang.Class&nbsp;testClass)</PRE>
<DL>
<DD>Returns the topmost interface that extends Remote for a given
 class - if one exists.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>testClass</CODE> - the class to be tested
<DT><B>Returns:</B><DD>the topmost interface that extends Remote, or null if there
         is none.</DL>
</DD>
</DL>
<HR>

<A NAME="getLoader()"><!-- --></A><H3>
getLoader</H3>
<PRE>
public java.lang.ClassLoader <B>getLoader</B>()</PRE>
<DL>
<DD>Classloader for the Employee-specified classpath.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the classloader</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ResourceCount.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Rmic.ImplementationSpecificArgument.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Rmic.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Rmic.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
