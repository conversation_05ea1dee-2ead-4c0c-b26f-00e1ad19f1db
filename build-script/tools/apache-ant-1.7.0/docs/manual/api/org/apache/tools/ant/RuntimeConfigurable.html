<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:20 EST 2006 -->
<TITLE>
RuntimeConfigurable (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.RuntimeConfigurable class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="RuntimeConfigurable (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/PropertyHelper.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/RuntimeConfigurable.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="RuntimeConfigurable.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class RuntimeConfigurable</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.RuntimeConfigurable</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.io.Serializable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>RuntimeConfigurable</B><DT>extends java.lang.Object<DT>implements java.io.Serializable</DL>
</PRE>

<P>
Wrapper class that holds the attributes of an element, its children, and
 any text within it. It then takes care of configuring that element at
 runtime.
<P>

<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../serialized-form.html#org.apache.tools.ant.RuntimeConfigurable">Serialized Form</A></DL>
<HR>

<P>

<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#RuntimeConfigurable(java.lang.Object, java.lang.String)">RuntimeConfigurable</A></B>(java.lang.Object&nbsp;proxy,
                    java.lang.String&nbsp;elementTag)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sole constructor creating a wrapper for the specified object.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#addChild(org.apache.tools.ant.RuntimeConfigurable)">addChild</A></B>(<A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A>&nbsp;child)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a child element to the wrapped element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#addText(char[], int, int)">addText</A></B>(char[]&nbsp;buf,
        int&nbsp;start,
        int&nbsp;count)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds characters from #PCDATA areas to the wrapped element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#addText(java.lang.String)">addText</A></B>(java.lang.String&nbsp;data)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds characters from #PCDATA areas to the wrapped element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#applyPreSet(org.apache.tools.ant.RuntimeConfigurable)">applyPreSet</A></B>(<A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A>&nbsp;r)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Apply presets, attributes and text are set if not currently set.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Hashtable</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#getAttributeMap()">getAttributeMap</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the attribute map.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;org.xml.sax.AttributeList</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#getAttributes()">getAttributes</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>Deprecated since Ant 1.6 in favor of <A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#getAttributeMap()"><CODE>getAttributeMap()</CODE></A>.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Enumeration</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#getChildren()">getChildren</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns an enumeration of all child wrappers.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#getElementTag()">getElementTag</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the tag name of the wrapped element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#getId()">getId</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Returns the id for this element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#getPolyType()">getPolyType</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the polymorphic type for this element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#getProxy()">getProxy</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the object for which this RuntimeConfigurable holds the configuration
 information.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.StringBuffer</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#getText()">getText</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the text content of this element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#maybeConfigure(org.apache.tools.ant.Project)">maybeConfigure</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Configures the wrapped element and all its children.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#maybeConfigure(org.apache.tools.ant.Project, boolean)">maybeConfigure</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p,
               boolean&nbsp;configureChildren)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Configures the wrapped element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#reconfigure(org.apache.tools.ant.Project)">reconfigure</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Reconfigure the element, even if it has already been configured.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#removeAttribute(java.lang.String)">removeAttribute</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Delete an attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#setAttribute(java.lang.String, java.lang.String)">setAttribute</A></B>(java.lang.String&nbsp;name,
             java.lang.String&nbsp;value)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set an attribute to a given value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#setAttributes(org.xml.sax.AttributeList)">setAttributes</A></B>(org.xml.sax.AttributeList&nbsp;attributes)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<B>Deprecated.</B>&nbsp;<I>since 1.6.x.</I></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#setElementTag(java.lang.String)">setElementTag</A></B>(java.lang.String&nbsp;elementTag)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the element tag.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#setPolyType(java.lang.String)">setPolyType</A></B>(java.lang.String&nbsp;polyType)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the polymorphic type for this element.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#setProxy(java.lang.Object)">setProxy</A></B>(java.lang.Object&nbsp;proxy)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the element to configure.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="RuntimeConfigurable(java.lang.Object, java.lang.String)"><!-- --></A><H3>
RuntimeConfigurable</H3>
<PRE>
public <B>RuntimeConfigurable</B>(java.lang.Object&nbsp;proxy,
                           java.lang.String&nbsp;elementTag)</PRE>
<DL>
<DD>Sole constructor creating a wrapper for the specified object.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>proxy</CODE> - The element to configure. Must not be <code>null</code>.<DD><CODE>elementTag</CODE> - The tag name generating this element.</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setProxy(java.lang.Object)"><!-- --></A><H3>
setProxy</H3>
<PRE>
public void <B>setProxy</B>(java.lang.Object&nbsp;proxy)</PRE>
<DL>
<DD>Sets the element to configure.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>proxy</CODE> - The element to configure. Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="getProxy()"><!-- --></A><H3>
getProxy</H3>
<PRE>
public java.lang.Object <B>getProxy</B>()</PRE>
<DL>
<DD>Get the object for which this RuntimeConfigurable holds the configuration
 information.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the object whose configure is held by this instance.</DL>
</DD>
</DL>
<HR>

<A NAME="getId()"><!-- --></A><H3>
getId</H3>
<PRE>
public java.lang.String <B>getId</B>()</PRE>
<DL>
<DD>Returns the id for this element.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the id.</DL>
</DD>
</DL>
<HR>

<A NAME="getPolyType()"><!-- --></A><H3>
getPolyType</H3>
<PRE>
public java.lang.String <B>getPolyType</B>()</PRE>
<DL>
<DD>Get the polymorphic type for this element.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the ant component type name, null if not set.</DL>
</DD>
</DL>
<HR>

<A NAME="setPolyType(java.lang.String)"><!-- --></A><H3>
setPolyType</H3>
<PRE>
public void <B>setPolyType</B>(java.lang.String&nbsp;polyType)</PRE>
<DL>
<DD>Set the polymorphic type for this element.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>polyType</CODE> - the ant component type name, null if not set.</DL>
</DD>
</DL>
<HR>

<A NAME="setAttributes(org.xml.sax.AttributeList)"><!-- --></A><H3>
setAttributes</H3>
<PRE>
public void <B>setAttributes</B>(org.xml.sax.AttributeList&nbsp;attributes)</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>since 1.6.x.</I>
<P>
<DD>Sets the attributes for the wrapped element.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>attributes</CODE> - List of attributes defined in the XML for this
                   element. May be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setAttribute(java.lang.String, java.lang.String)"><!-- --></A><H3>
setAttribute</H3>
<PRE>
public void <B>setAttribute</B>(java.lang.String&nbsp;name,
                         java.lang.String&nbsp;value)</PRE>
<DL>
<DD>Set an attribute to a given value.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the name of the attribute.<DD><CODE>value</CODE> - the attribute's value.</DL>
</DD>
</DL>
<HR>

<A NAME="removeAttribute(java.lang.String)"><!-- --></A><H3>
removeAttribute</H3>
<PRE>
public void <B>removeAttribute</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Delete an attribute.  Not for the faint of heart.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the name of the attribute to be removed.</DL>
</DD>
</DL>
<HR>

<A NAME="getAttributeMap()"><!-- --></A><H3>
getAttributeMap</H3>
<PRE>
public java.util.Hashtable <B>getAttributeMap</B>()</PRE>
<DL>
<DD>Return the attribute map.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>Attribute name to attribute value map.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getAttributes()"><!-- --></A><H3>
getAttributes</H3>
<PRE>
public org.xml.sax.AttributeList <B>getAttributes</B>()</PRE>
<DL>
<DD><B>Deprecated.</B>&nbsp;<I>Deprecated since Ant 1.6 in favor of <A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html#getAttributeMap()"><CODE>getAttributeMap()</CODE></A>.</I>
<P>
<DD>Returns the list of attributes for the wrapped element.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>An AttributeList representing the attributes defined in the
         XML for this element. May be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="addChild(org.apache.tools.ant.RuntimeConfigurable)"><!-- --></A><H3>
addChild</H3>
<PRE>
public void <B>addChild</B>(<A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A>&nbsp;child)</PRE>
<DL>
<DD>Adds a child element to the wrapped element.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>child</CODE> - The child element wrapper to add to this one.
              Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="getChildren()"><!-- --></A><H3>
getChildren</H3>
<PRE>
public java.util.Enumeration <B>getChildren</B>()</PRE>
<DL>
<DD>Returns an enumeration of all child wrappers.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>an enumeration of the child wrappers.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addText(java.lang.String)"><!-- --></A><H3>
addText</H3>
<PRE>
public void <B>addText</B>(java.lang.String&nbsp;data)</PRE>
<DL>
<DD>Adds characters from #PCDATA areas to the wrapped element.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>data</CODE> - Text to add to the wrapped element.
        Should not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="addText(char[], int, int)"><!-- --></A><H3>
addText</H3>
<PRE>
public void <B>addText</B>(char[]&nbsp;buf,
                    int&nbsp;start,
                    int&nbsp;count)</PRE>
<DL>
<DD>Adds characters from #PCDATA areas to the wrapped element.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>buf</CODE> - A character array of the text within the element.
            Must not be <code>null</code>.<DD><CODE>start</CODE> - The start element in the array.<DD><CODE>count</CODE> - The number of characters to read from the array.</DL>
</DD>
</DL>
<HR>

<A NAME="getText()"><!-- --></A><H3>
getText</H3>
<PRE>
public java.lang.StringBuffer <B>getText</B>()</PRE>
<DL>
<DD>Get the text content of this element. Various text chunks are
 concatenated, there is no way ( currently ) of keeping track of
 multiple fragments.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the text content of this element.<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setElementTag(java.lang.String)"><!-- --></A><H3>
setElementTag</H3>
<PRE>
public void <B>setElementTag</B>(java.lang.String&nbsp;elementTag)</PRE>
<DL>
<DD>Set the element tag.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>elementTag</CODE> - The tag name generating this element.</DL>
</DD>
</DL>
<HR>

<A NAME="getElementTag()"><!-- --></A><H3>
getElementTag</H3>
<PRE>
public java.lang.String <B>getElementTag</B>()</PRE>
<DL>
<DD>Returns the tag name of the wrapped element.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>The tag name of the wrapped element. This is unlikely
         to be <code>null</code>, but may be.</DL>
</DD>
</DL>
<HR>

<A NAME="maybeConfigure(org.apache.tools.ant.Project)"><!-- --></A><H3>
maybeConfigure</H3>
<PRE>
public void <B>maybeConfigure</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)
                    throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Configures the wrapped element and all its children.
 The attributes and text for the wrapped element are configured,
 and then each child is configured and added. Each time the
 wrapper is configured, the attributes and text for it are
 reset.

 If the element has an <code>id</code> attribute, a reference
 is added to the project as well.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - The project containing the wrapped element.
          Must not be <code>null</code>.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the configuration fails, for instance due
            to invalid attributes or children, or text being added to
            an element which doesn't accept it.</DL>
</DD>
</DL>
<HR>

<A NAME="maybeConfigure(org.apache.tools.ant.Project, boolean)"><!-- --></A><H3>
maybeConfigure</H3>
<PRE>
public void <B>maybeConfigure</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p,
                           boolean&nbsp;configureChildren)
                    throws <A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Configures the wrapped element.  The attributes and text for
 the wrapped element are configured.  Each time the wrapper is
 configured, the attributes and text for it are reset.

 If the element has an <code>id</code> attribute, a reference
 is added to the project as well.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - The project containing the wrapped element.
          Must not be <code>null</code>.<DD><CODE>configureChildren</CODE> - ignored.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if the configuration fails, for instance due
            to invalid attributes , or text being added to
            an element which doesn't accept it.</DL>
</DD>
</DL>
<HR>

<A NAME="reconfigure(org.apache.tools.ant.Project)"><!-- --></A><H3>
reconfigure</H3>
<PRE>
public void <B>reconfigure</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;p)</PRE>
<DL>
<DD>Reconfigure the element, even if it has already been configured.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>p</CODE> - the project instance for this configuration.</DL>
</DD>
</DL>
<HR>

<A NAME="applyPreSet(org.apache.tools.ant.RuntimeConfigurable)"><!-- --></A><H3>
applyPreSet</H3>
<PRE>
public void <B>applyPreSet</B>(<A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">RuntimeConfigurable</A>&nbsp;r)</PRE>
<DL>
<DD>Apply presets, attributes and text are set if not currently set.
 Nested elements are prepended.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>r</CODE> - a <code>RuntimeConfigurable</code> value.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/PropertyHelper.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/RuntimeConfigurable.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="RuntimeConfigurable.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
