<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:28 EST 2006 -->
<TITLE>
JspC (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.optional.jsp.JspC class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="JspC (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/Jasper41Mangler.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.WebAppParameter.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/jsp/JspC.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="JspC.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.optional.jsp</FONT>
<BR>
Class JspC</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.MatchingTask</A>
              <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.optional.jsp.JspC</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable, <A HREF="../../../../../../../org/apache/tools/ant/types/selectors/SelectorContainer.html" title="interface in org.apache.tools.ant.types.selectors">SelectorContainer</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>JspC</B><DT>extends <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></DL>
</PRE>

<P>
Runs a JSP compiler.

 <p> This task takes the given jsp files and compiles them into java
 files. It is then up to the Employee to compile the java files into classes.

 <p> The task requires the srcdir and destdir attributes to be
 set. This Task is a MatchingTask, so the files to be compiled can be
 specified using includes/excludes attributes or nested include/exclude
 elements. Optional attributes are verbose (set the verbosity level passed
 to jasper), package (name of the destination package for generated java
 classes and classpath (the classpath to use when running the jsp
 compiler).
 <p> This task supports the nested elements classpath (A Path) and
 classpathref (A Reference) which can be used in preference to the
 attribute classpath, if the jsp compiler is not already in the ant
 classpath.

 <p><h4>Usage</h4>
 <pre>
 &lt;jspc srcdir="${basedir}/src/war"
       destdir="${basedir}/gensrc"
       package="com.i3sp.jsp"
       verbose="9"&gt;
   &lt;include name="**\/*.jsp" /&gt;
 &lt;/jspc&gt;
 </pre>

 <p> Large Amount of cutting and pasting from the Javac task...
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>1.5</DD>
</DL>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;class</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.WebAppParameter.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC.WebAppParameter</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;static inner class used as a parameter element</TD>
</TR>
</TABLE>
&nbsp;<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#compileList">compileList</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#failOnError">failOnError</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;flag to control action on execution trouble</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.WebAppParameter.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC.WebAppParameter</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#webApp">webApp</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;web apps</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.MatchingTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#fileset">fileset</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#JspC()">JspC</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#addWebApp(org.apache.tools.ant.taskdefs.optional.jsp.JspC.WebAppParameter)">addWebApp</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.WebAppParameter.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC.WebAppParameter</A>&nbsp;webappParam)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a single webapp.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#createClasspath()">createClasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a path to the classpath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#createCompilerclasspath()">createCompilerclasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Support nested compiler classpath, used to locate compiler adapter</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#deleteEmptyJavaFiles()">deleteEmptyJavaFiles</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;delete any java output files that are empty
 this is to get around a little defect in jasper: when it
 fails, it leaves incomplete files around.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;execute by building up a list of files that
 have changed and hand them off to a jsp compiler</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#getClasspath()">getClasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the classpath.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#getCompileList()">getCompileList</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;get the list of files to compile</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#getCompilerclasspath()">getCompilerclasspath</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;get the classpath used to find the compiler adapter</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#getDestdir()">getDestdir</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the destination directory.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#getFailonerror()">getFailonerror</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Gets the failonerror flag.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#getIeplugin()">getIeplugin</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the IE CLASSID value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#getPackage()">getPackage</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the name of the package.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#getSrcDir()">getSrcDir</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the source dir.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#getUribase()">getUribase</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the uri base value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#getUriroot()">getUriroot</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the uri root value.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#getVerbose()">getVerbose</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the verbose level.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.WebAppParameter.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC.WebAppParameter</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#getWebApp()">getWebApp</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the web app.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#getWebinc()">getWebinc</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the webinc attribute.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#getWebxml()">getWebxml</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Filename for web.xml.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#isMapped()">isMapped</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, generate separate write() calls for each HTML line
 in the JSP.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#mapToJavaFile(org.apache.tools.ant.taskdefs.optional.jsp.JspMangler, java.io.File, java.io.File, java.io.File)">mapToJavaFile</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspMangler.html" title="interface in org.apache.tools.ant.taskdefs.optional.jsp">JspMangler</A>&nbsp;mangler,
              java.io.File&nbsp;srcFile,
              java.io.File&nbsp;srcDir,
              java.io.File&nbsp;dest)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;get a filename from our jsp file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#resetFileLists()">resetFileLists</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Clear the list of files to be compiled and copied..</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#scanDir(java.io.File, java.io.File, org.apache.tools.ant.taskdefs.optional.jsp.JspMangler, java.lang.String[])">scanDir</A></B>(java.io.File&nbsp;srcDir,
        java.io.File&nbsp;dest,
        <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspMangler.html" title="interface in org.apache.tools.ant.taskdefs.optional.jsp">JspMangler</A>&nbsp;mangler,
        java.lang.String[]&nbsp;files)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Scans the directory looking for source files to be compiled.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#setClasspath(org.apache.tools.ant.types.Path)">setClasspath</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;cp)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the classpath to be used for this compilation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#setClasspathRef(org.apache.tools.ant.types.Reference)">setClasspathRef</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Adds a reference to a classpath defined elsewhere</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#setCompiler(java.lang.String)">setCompiler</A></B>(java.lang.String&nbsp;compiler)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Class name of a JSP compiler adapter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#setCompilerclasspath(org.apache.tools.ant.types.Path)">setCompilerclasspath</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;cp)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the classpath to be used to find this compiler adapter</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#setDestdir(java.io.File)">setDestdir</A></B>(java.io.File&nbsp;destDir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the destination directory into which the JSP source
 files should be compiled.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#setFailonerror(boolean)">setFailonerror</A></B>(boolean&nbsp;fail)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether or not the build should halt if compilation fails.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#setIeplugin(java.lang.String)">setIeplugin</A></B>(java.lang.String&nbsp;iepluginid)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Java Plugin CLASSID for Internet Explorer</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#setMapped(boolean)">setMapped</A></B>(boolean&nbsp;mapped)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If true, generate separate write() calls for each HTML line
 in the JSP.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#setPackage(java.lang.String)">setPackage</A></B>(java.lang.String&nbsp;pkg)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the name of the package the compiled jsp files should be in.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#setSrcDir(org.apache.tools.ant.types.Path)">setSrcDir</A></B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;srcDir)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the path for source JSP files.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#setUribase(java.io.File)">setUribase</A></B>(java.io.File&nbsp;uribase)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The URI context of relative URI references in the JSP pages.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#setUriroot(java.io.File)">setUriroot</A></B>(java.io.File&nbsp;uriroot)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The root directory that uri files should be resolved
  against.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#setVerbose(int)">setVerbose</A></B>(int&nbsp;i)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the verbose level of the compiler</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#setWebinc(java.io.File)">setWebinc</A></B>(java.io.File&nbsp;webinc)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;output filename for the fraction of web.xml that lists
  servlets.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.html#setWebxml(java.io.File)">setWebxml</A></B>(java.io.File&nbsp;webxml)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Filename for web.xml.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.MatchingTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html" title="class in org.apache.tools.ant.taskdefs">MatchingTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#add(org.apache.tools.ant.types.selectors.FileSelector)">add</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addAnd(org.apache.tools.ant.types.selectors.AndSelector)">addAnd</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addContains(org.apache.tools.ant.types.selectors.ContainsSelector)">addContains</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addContainsRegexp(org.apache.tools.ant.types.selectors.ContainsRegexpSelector)">addContainsRegexp</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addCustom(org.apache.tools.ant.types.selectors.ExtendSelector)">addCustom</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDate(org.apache.tools.ant.types.selectors.DateSelector)">addDate</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDepend(org.apache.tools.ant.types.selectors.DependSelector)">addDepend</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDepth(org.apache.tools.ant.types.selectors.DepthSelector)">addDepth</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addDifferent(org.apache.tools.ant.types.selectors.DifferentSelector)">addDifferent</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addFilename(org.apache.tools.ant.types.selectors.FilenameSelector)">addFilename</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addMajority(org.apache.tools.ant.types.selectors.MajoritySelector)">addMajority</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addModified(org.apache.tools.ant.types.selectors.modifiedselector.ModifiedSelector)">addModified</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addNone(org.apache.tools.ant.types.selectors.NoneSelector)">addNone</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addNot(org.apache.tools.ant.types.selectors.NotSelector)">addNot</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addOr(org.apache.tools.ant.types.selectors.OrSelector)">addOr</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addPresent(org.apache.tools.ant.types.selectors.PresentSelector)">addPresent</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addSelector(org.apache.tools.ant.types.selectors.SelectSelector)">addSelector</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addSize(org.apache.tools.ant.types.selectors.SizeSelector)">addSize</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#addType(org.apache.tools.ant.types.selectors.TypeSelector)">addType</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#appendSelector(org.apache.tools.ant.types.selectors.FileSelector)">appendSelector</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createExclude()">createExclude</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createExcludesFile()">createExcludesFile</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createInclude()">createInclude</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createIncludesFile()">createIncludesFile</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#createPatternSet()">createPatternSet</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getDirectoryScanner(java.io.File)">getDirectoryScanner</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getImplicitFileSet()">getImplicitFileSet</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#getSelectors(org.apache.tools.ant.Project)">getSelectors</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#hasSelectors()">hasSelectors</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#selectorCount()">selectorCount</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#selectorElements()">selectorElements</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setCaseSensitive(boolean)">setCaseSensitive</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setDefaultexcludes(boolean)">setDefaultexcludes</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setExcludes(java.lang.String)">setExcludes</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setExcludesfile(java.io.File)">setExcludesfile</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setFollowSymlinks(boolean)">setFollowSymlinks</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setIncludes(java.lang.String)">setIncludes</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setIncludesfile(java.io.File)">setIncludesfile</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#setProject(org.apache.tools.ant.Project)">setProject</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#XsetIgnore(java.lang.String)">XsetIgnore</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/MatchingTask.html#XsetItems(java.lang.String)">XsetItems</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="compileList"><!-- --></A><H3>
compileList</H3>
<PRE>
protected java.util.Vector <B>compileList</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="failOnError"><!-- --></A><H3>
failOnError</H3>
<PRE>
protected boolean <B>failOnError</B></PRE>
<DL>
<DD>flag to control action on execution trouble
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="webApp"><!-- --></A><H3>
webApp</H3>
<PRE>
protected <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.WebAppParameter.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC.WebAppParameter</A> <B>webApp</B></PRE>
<DL>
<DD>web apps
<P>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="JspC()"><!-- --></A><H3>
JspC</H3>
<PRE>
public <B>JspC</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setSrcDir(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setSrcDir</H3>
<PRE>
public void <B>setSrcDir</B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;srcDir)</PRE>
<DL>
<DD>Set the path for source JSP files.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>srcDir</CODE> - the source path.</DL>
</DD>
</DL>
<HR>

<A NAME="getSrcDir()"><!-- --></A><H3>
getSrcDir</H3>
<PRE>
public <A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>getSrcDir</B>()</PRE>
<DL>
<DD>Get the source dir.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the source path.</DL>
</DD>
</DL>
<HR>

<A NAME="setDestdir(java.io.File)"><!-- --></A><H3>
setDestdir</H3>
<PRE>
public void <B>setDestdir</B>(java.io.File&nbsp;destDir)</PRE>
<DL>
<DD>Set the destination directory into which the JSP source
 files should be compiled.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>destDir</CODE> - the destination directory.</DL>
</DD>
</DL>
<HR>

<A NAME="getDestdir()"><!-- --></A><H3>
getDestdir</H3>
<PRE>
public java.io.File <B>getDestdir</B>()</PRE>
<DL>
<DD>Get the destination directory.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the directory.</DL>
</DD>
</DL>
<HR>

<A NAME="setPackage(java.lang.String)"><!-- --></A><H3>
setPackage</H3>
<PRE>
public void <B>setPackage</B>(java.lang.String&nbsp;pkg)</PRE>
<DL>
<DD>Set the name of the package the compiled jsp files should be in.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>pkg</CODE> - the name of the package.</DL>
</DD>
</DL>
<HR>

<A NAME="getPackage()"><!-- --></A><H3>
getPackage</H3>
<PRE>
public java.lang.String <B>getPackage</B>()</PRE>
<DL>
<DD>Get the name of the package.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the package.</DL>
</DD>
</DL>
<HR>

<A NAME="setVerbose(int)"><!-- --></A><H3>
setVerbose</H3>
<PRE>
public void <B>setVerbose</B>(int&nbsp;i)</PRE>
<DL>
<DD>Set the verbose level of the compiler
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>i</CODE> - the verbose level to use.</DL>
</DD>
</DL>
<HR>

<A NAME="getVerbose()"><!-- --></A><H3>
getVerbose</H3>
<PRE>
public int <B>getVerbose</B>()</PRE>
<DL>
<DD>Get the verbose level.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the level.</DL>
</DD>
</DL>
<HR>

<A NAME="setFailonerror(boolean)"><!-- --></A><H3>
setFailonerror</H3>
<PRE>
public void <B>setFailonerror</B>(boolean&nbsp;fail)</PRE>
<DL>
<DD>Whether or not the build should halt if compilation fails.
 Defaults to <code>true</code>.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>fail</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="getFailonerror()"><!-- --></A><H3>
getFailonerror</H3>
<PRE>
public boolean <B>getFailonerror</B>()</PRE>
<DL>
<DD>Gets the failonerror flag.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the flag.</DL>
</DD>
</DL>
<HR>

<A NAME="getIeplugin()"><!-- --></A><H3>
getIeplugin</H3>
<PRE>
public java.lang.String <B>getIeplugin</B>()</PRE>
<DL>
<DD>Get the IE CLASSID value.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the value.</DL>
</DD>
</DL>
<HR>

<A NAME="setIeplugin(java.lang.String)"><!-- --></A><H3>
setIeplugin</H3>
<PRE>
public void <B>setIeplugin</B>(java.lang.String&nbsp;iepluginid)</PRE>
<DL>
<DD>Java Plugin CLASSID for Internet Explorer
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>iepluginid</CODE> - the id to use.</DL>
</DD>
</DL>
<HR>

<A NAME="isMapped()"><!-- --></A><H3>
isMapped</H3>
<PRE>
public boolean <B>isMapped</B>()</PRE>
<DL>
<DD>If true, generate separate write() calls for each HTML line
 in the JSP.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>mapping status</DL>
</DD>
</DL>
<HR>

<A NAME="setMapped(boolean)"><!-- --></A><H3>
setMapped</H3>
<PRE>
public void <B>setMapped</B>(boolean&nbsp;mapped)</PRE>
<DL>
<DD>If true, generate separate write() calls for each HTML line
 in the JSP.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>mapped</CODE> - a <code>boolean</code> value.</DL>
</DD>
</DL>
<HR>

<A NAME="setUribase(java.io.File)"><!-- --></A><H3>
setUribase</H3>
<PRE>
public void <B>setUribase</B>(java.io.File&nbsp;uribase)</PRE>
<DL>
<DD>The URI context of relative URI references in the JSP pages.
 If it does not exist then it is derived from the location
 of the file relative to the declared or derived value of uriroot.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>uribase</CODE> - The new Uribase value</DL>
</DD>
</DL>
<HR>

<A NAME="getUribase()"><!-- --></A><H3>
getUribase</H3>
<PRE>
public java.io.File <B>getUribase</B>()</PRE>
<DL>
<DD>Get the uri base value.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the value.</DL>
</DD>
</DL>
<HR>

<A NAME="setUriroot(java.io.File)"><!-- --></A><H3>
setUriroot</H3>
<PRE>
public void <B>setUriroot</B>(java.io.File&nbsp;uriroot)</PRE>
<DL>
<DD>The root directory that uri files should be resolved
  against. (Default is the directory jspc is invoked from)
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>uriroot</CODE> - The new Uribase value</DL>
</DD>
</DL>
<HR>

<A NAME="getUriroot()"><!-- --></A><H3>
getUriroot</H3>
<PRE>
public java.io.File <B>getUriroot</B>()</PRE>
<DL>
<DD>Get the uri root value.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the value.</DL>
</DD>
</DL>
<HR>

<A NAME="setClasspath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setClasspath</H3>
<PRE>
public void <B>setClasspath</B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;cp)</PRE>
<DL>
<DD>Set the classpath to be used for this compilation.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>cp</CODE> - the path to be used.</DL>
</DD>
</DL>
<HR>

<A NAME="createClasspath()"><!-- --></A><H3>
createClasspath</H3>
<PRE>
public <A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createClasspath</B>()</PRE>
<DL>
<DD>Adds a path to the classpath.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a path to be configured.</DL>
</DD>
</DL>
<HR>

<A NAME="setClasspathRef(org.apache.tools.ant.types.Reference)"><!-- --></A><H3>
setClasspathRef</H3>
<PRE>
public void <B>setClasspathRef</B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</PRE>
<DL>
<DD>Adds a reference to a classpath defined elsewhere
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>r</CODE> - a reference to a classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="getClasspath()"><!-- --></A><H3>
getClasspath</H3>
<PRE>
public <A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>getClasspath</B>()</PRE>
<DL>
<DD>Get the classpath.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="setCompilerclasspath(org.apache.tools.ant.types.Path)"><!-- --></A><H3>
setCompilerclasspath</H3>
<PRE>
public void <B>setCompilerclasspath</B>(<A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A>&nbsp;cp)</PRE>
<DL>
<DD>Set the classpath to be used to find this compiler adapter
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>cp</CODE> - the compiler classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="getCompilerclasspath()"><!-- --></A><H3>
getCompilerclasspath</H3>
<PRE>
public <A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>getCompilerclasspath</B>()</PRE>
<DL>
<DD>get the classpath used to find the compiler adapter
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the compiler classpath.</DL>
</DD>
</DL>
<HR>

<A NAME="createCompilerclasspath()"><!-- --></A><H3>
createCompilerclasspath</H3>
<PRE>
public <A HREF="../../../../../../../org/apache/tools/ant/types/Path.html" title="class in org.apache.tools.ant.types">Path</A> <B>createCompilerclasspath</B>()</PRE>
<DL>
<DD>Support nested compiler classpath, used to locate compiler adapter
<P>
<DD><DL>

<DT><B>Returns:</B><DD>a path to be configured.</DL>
</DD>
</DL>
<HR>

<A NAME="setWebxml(java.io.File)"><!-- --></A><H3>
setWebxml</H3>
<PRE>
public void <B>setWebxml</B>(java.io.File&nbsp;webxml)</PRE>
<DL>
<DD>Filename for web.xml.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>webxml</CODE> - The new Webxml value</DL>
</DD>
</DL>
<HR>

<A NAME="getWebxml()"><!-- --></A><H3>
getWebxml</H3>
<PRE>
public java.io.File <B>getWebxml</B>()</PRE>
<DL>
<DD>Filename for web.xml.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>The filename for web.xml.</DL>
</DD>
</DL>
<HR>

<A NAME="setWebinc(java.io.File)"><!-- --></A><H3>
setWebinc</H3>
<PRE>
public void <B>setWebinc</B>(java.io.File&nbsp;webinc)</PRE>
<DL>
<DD>output filename for the fraction of web.xml that lists
  servlets.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>webinc</CODE> - The new Webinc value</DL>
</DD>
</DL>
<HR>

<A NAME="getWebinc()"><!-- --></A><H3>
getWebinc</H3>
<PRE>
public java.io.File <B>getWebinc</B>()</PRE>
<DL>
<DD>Get the webinc attribute.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the webinc attribute.</DL>
</DD>
</DL>
<HR>

<A NAME="addWebApp(org.apache.tools.ant.taskdefs.optional.jsp.JspC.WebAppParameter)"><!-- --></A><H3>
addWebApp</H3>
<PRE>
public void <B>addWebApp</B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.WebAppParameter.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC.WebAppParameter</A>&nbsp;webappParam)
               throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Adds a single webapp.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>webappParam</CODE> - add a web app parameter
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if more than one webapp is specified.</DL>
</DD>
</DL>
<HR>

<A NAME="getWebApp()"><!-- --></A><H3>
getWebApp</H3>
<PRE>
public <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.WebAppParameter.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp">JspC.WebAppParameter</A> <B>getWebApp</B>()</PRE>
<DL>
<DD>Get the web app.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the web app attribute.</DL>
</DD>
</DL>
<HR>

<A NAME="setCompiler(java.lang.String)"><!-- --></A><H3>
setCompiler</H3>
<PRE>
public void <B>setCompiler</B>(java.lang.String&nbsp;compiler)</PRE>
<DL>
<DD>Class name of a JSP compiler adapter.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>compiler</CODE> - the compiler class name.</DL>
</DD>
</DL>
<HR>

<A NAME="getCompileList()"><!-- --></A><H3>
getCompileList</H3>
<PRE>
public java.util.Vector <B>getCompileList</B>()</PRE>
<DL>
<DD>get the list of files to compile
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the list of files.</DL>
</DD>
</DL>
<HR>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public void <B>execute</B>()
             throws <A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>execute by building up a list of files that
 have changed and hand them off to a jsp compiler
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#execute()">execute</A></CODE> in class <CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error.</DL>
</DD>
</DL>
<HR>

<A NAME="resetFileLists()"><!-- --></A><H3>
resetFileLists</H3>
<PRE>
protected void <B>resetFileLists</B>()</PRE>
<DL>
<DD>Clear the list of files to be compiled and copied..
<P>
<DD><DL>
</DL>
</DD>
</DL>
<HR>

<A NAME="scanDir(java.io.File, java.io.File, org.apache.tools.ant.taskdefs.optional.jsp.JspMangler, java.lang.String[])"><!-- --></A><H3>
scanDir</H3>
<PRE>
protected void <B>scanDir</B>(java.io.File&nbsp;srcDir,
                       java.io.File&nbsp;dest,
                       <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspMangler.html" title="interface in org.apache.tools.ant.taskdefs.optional.jsp">JspMangler</A>&nbsp;mangler,
                       java.lang.String[]&nbsp;files)</PRE>
<DL>
<DD>Scans the directory looking for source files to be compiled.
 The results are returned in the class variable compileList
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>srcDir</CODE> - the source directory.<DD><CODE>dest</CODE> - the destination directory.<DD><CODE>mangler</CODE> - the jsp filename mangler.<DD><CODE>files</CODE> - the file names to mangle.</DL>
</DD>
</DL>
<HR>

<A NAME="mapToJavaFile(org.apache.tools.ant.taskdefs.optional.jsp.JspMangler, java.io.File, java.io.File, java.io.File)"><!-- --></A><H3>
mapToJavaFile</H3>
<PRE>
protected java.io.File <B>mapToJavaFile</B>(<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspMangler.html" title="interface in org.apache.tools.ant.taskdefs.optional.jsp">JspMangler</A>&nbsp;mangler,
                                     java.io.File&nbsp;srcFile,
                                     java.io.File&nbsp;srcDir,
                                     java.io.File&nbsp;dest)</PRE>
<DL>
<DD>get a filename from our jsp file.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>mangler</CODE> - the jsp filename managler.<DD><CODE>srcFile</CODE> - the source file.<DD><CODE>srcDir</CODE> - the source directory.<DD><CODE>dest</CODE> - the destination directory.
<DT><B>Returns:</B><DD>the filename.<DT><B>To do:</B></DT>
  <DD>support packages and subdirs</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="deleteEmptyJavaFiles()"><!-- --></A><H3>
deleteEmptyJavaFiles</H3>
<PRE>
public void <B>deleteEmptyJavaFiles</B>()</PRE>
<DL>
<DD>delete any java output files that are empty
 this is to get around a little defect in jasper: when it
 fails, it leaves incomplete files around.
<P>
<DD><DL>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/Jasper41Mangler.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/jsp/JspC.WebAppParameter.html" title="class in org.apache.tools.ant.taskdefs.optional.jsp"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/jsp/JspC.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="JspC.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_class_summary">NESTED</A>&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
