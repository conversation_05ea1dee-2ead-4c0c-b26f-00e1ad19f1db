<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:34 EST 2006 -->
<TITLE>
TarBuffer (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.tar.TarBuffer class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="TarBuffer (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV CLASS&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarConstants.html" title="interface in org.apache.tools.tar"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/tar/TarBuffer.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="TarBuffer.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.tar</FONT>
<BR>
Class TarBuffer</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.tar.TarBuffer</B>
</PRE>
<HR>
<DL>
<DT><PRE>public class <B>TarBuffer</B><DT>extends java.lang.Object</DL>
</PRE>

<P>
The TarBuffer class implements the tar archive concept
 of a buffered input stream. This concept goes back to the
 days of blocked tape drives and special io devices. In the
 Java universe, the only real function that this class
 performs is to ensure that files have the correct "block"
 size, or other tars will complain.
 <p>
 You should never have a need to access this class directly.
 TarBuffers are created by Tar IO Streams.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#DEFAULT_BLKSIZE">DEFAULT_BLKSIZE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Default block size</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#DEFAULT_RCDSIZE">DEFAULT_RCDSIZE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Default record size</TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#TarBuffer(java.io.InputStream)">TarBuffer</A></B>(java.io.InputStream&nbsp;inStream)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for a TarBuffer on an input stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#TarBuffer(java.io.InputStream, int)">TarBuffer</A></B>(java.io.InputStream&nbsp;inStream,
          int&nbsp;blockSize)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for a TarBuffer on an input stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#TarBuffer(java.io.InputStream, int, int)">TarBuffer</A></B>(java.io.InputStream&nbsp;inStream,
          int&nbsp;blockSize,
          int&nbsp;recordSize)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for a TarBuffer on an input stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#TarBuffer(java.io.OutputStream)">TarBuffer</A></B>(java.io.OutputStream&nbsp;outStream)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for a TarBuffer on an output stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#TarBuffer(java.io.OutputStream, int)">TarBuffer</A></B>(java.io.OutputStream&nbsp;outStream,
          int&nbsp;blockSize)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for a TarBuffer on an output stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#TarBuffer(java.io.OutputStream, int, int)">TarBuffer</A></B>(java.io.OutputStream&nbsp;outStream,
          int&nbsp;blockSize,
          int&nbsp;recordSize)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for a TarBuffer on an output stream.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#close()">close</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Close the TarBuffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#getBlockSize()">getBlockSize</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the TAR Buffer's block size.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#getCurrentBlockNum()">getCurrentBlockNum</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the current block number, zero based.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#getCurrentRecordNum()">getCurrentRecordNum</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the current record number, within the current block, zero based.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#getRecordSize()">getRecordSize</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the TAR Buffer's record size.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#isEOFRecord(byte[])">isEOFRecord</A></B>(byte[]&nbsp;record)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Determine if an archive record indicate End of Archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#readRecord()">readRecord</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Read a record from the input stream and return the data.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#setDebug(boolean)">setDebug</A></B>(boolean&nbsp;debug)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the debugging flag for the buffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#skipRecord()">skipRecord</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Skip over a record on the input stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#writeRecord(byte[])">writeRecord</A></B>(byte[]&nbsp;record)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Write an archive record to the archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarBuffer.html#writeRecord(byte[], int)">writeRecord</A></B>(byte[]&nbsp;buf,
            int&nbsp;offset)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Write an archive record to the archive, where the record may be
 inside of a larger array buffer.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="DEFAULT_RCDSIZE"><!-- --></A><H3>
DEFAULT_RCDSIZE</H3>
<PRE>
public static final int <B>DEFAULT_RCDSIZE</B></PRE>
<DL>
<DD>Default record size
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarBuffer.DEFAULT_RCDSIZE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_BLKSIZE"><!-- --></A><H3>
DEFAULT_BLKSIZE</H3>
<PRE>
public static final int <B>DEFAULT_BLKSIZE</B></PRE>
<DL>
<DD>Default block size
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarBuffer.DEFAULT_BLKSIZE">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="TarBuffer(java.io.InputStream)"><!-- --></A><H3>
TarBuffer</H3>
<PRE>
public <B>TarBuffer</B>(java.io.InputStream&nbsp;inStream)</PRE>
<DL>
<DD>Constructor for a TarBuffer on an input stream.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>inStream</CODE> - the input stream to use</DL>
</DL>
<HR>

<A NAME="TarBuffer(java.io.InputStream, int)"><!-- --></A><H3>
TarBuffer</H3>
<PRE>
public <B>TarBuffer</B>(java.io.InputStream&nbsp;inStream,
                 int&nbsp;blockSize)</PRE>
<DL>
<DD>Constructor for a TarBuffer on an input stream.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>inStream</CODE> - the input stream to use<DD><CODE>blockSize</CODE> - the block size to use</DL>
</DL>
<HR>

<A NAME="TarBuffer(java.io.InputStream, int, int)"><!-- --></A><H3>
TarBuffer</H3>
<PRE>
public <B>TarBuffer</B>(java.io.InputStream&nbsp;inStream,
                 int&nbsp;blockSize,
                 int&nbsp;recordSize)</PRE>
<DL>
<DD>Constructor for a TarBuffer on an input stream.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>inStream</CODE> - the input stream to use<DD><CODE>blockSize</CODE> - the block size to use<DD><CODE>recordSize</CODE> - the record size to use</DL>
</DL>
<HR>

<A NAME="TarBuffer(java.io.OutputStream)"><!-- --></A><H3>
TarBuffer</H3>
<PRE>
public <B>TarBuffer</B>(java.io.OutputStream&nbsp;outStream)</PRE>
<DL>
<DD>Constructor for a TarBuffer on an output stream.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>outStream</CODE> - the output stream to use</DL>
</DL>
<HR>

<A NAME="TarBuffer(java.io.OutputStream, int)"><!-- --></A><H3>
TarBuffer</H3>
<PRE>
public <B>TarBuffer</B>(java.io.OutputStream&nbsp;outStream,
                 int&nbsp;blockSize)</PRE>
<DL>
<DD>Constructor for a TarBuffer on an output stream.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>outStream</CODE> - the output stream to use<DD><CODE>blockSize</CODE> - the block size to use</DL>
</DL>
<HR>

<A NAME="TarBuffer(java.io.OutputStream, int, int)"><!-- --></A><H3>
TarBuffer</H3>
<PRE>
public <B>TarBuffer</B>(java.io.OutputStream&nbsp;outStream,
                 int&nbsp;blockSize,
                 int&nbsp;recordSize)</PRE>
<DL>
<DD>Constructor for a TarBuffer on an output stream.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>outStream</CODE> - the output stream to use<DD><CODE>blockSize</CODE> - the block size to use<DD><CODE>recordSize</CODE> - the record size to use</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="getBlockSize()"><!-- --></A><H3>
getBlockSize</H3>
<PRE>
public int <B>getBlockSize</B>()</PRE>
<DL>
<DD>Get the TAR Buffer's block size. Blocks consist of multiple records.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the block size</DL>
</DD>
</DL>
<HR>

<A NAME="getRecordSize()"><!-- --></A><H3>
getRecordSize</H3>
<PRE>
public int <B>getRecordSize</B>()</PRE>
<DL>
<DD>Get the TAR Buffer's record size.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the record size</DL>
</DD>
</DL>
<HR>

<A NAME="setDebug(boolean)"><!-- --></A><H3>
setDebug</H3>
<PRE>
public void <B>setDebug</B>(boolean&nbsp;debug)</PRE>
<DL>
<DD>Set the debugging flag for the buffer.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>debug</CODE> - If true, print debugging output.</DL>
</DD>
</DL>
<HR>

<A NAME="isEOFRecord(byte[])"><!-- --></A><H3>
isEOFRecord</H3>
<PRE>
public boolean <B>isEOFRecord</B>(byte[]&nbsp;record)</PRE>
<DL>
<DD>Determine if an archive record indicate End of Archive. End of
 archive is indicated by a record that consists entirely of null bytes.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>record</CODE> - The record data to check.
<DT><B>Returns:</B><DD>true if the record data is an End of Archive</DL>
</DD>
</DL>
<HR>

<A NAME="skipRecord()"><!-- --></A><H3>
skipRecord</H3>
<PRE>
public void <B>skipRecord</B>()
                throws java.io.IOException</PRE>
<DL>
<DD>Skip over a record on the input stream.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="readRecord()"><!-- --></A><H3>
readRecord</H3>
<PRE>
public byte[] <B>readRecord</B>()
                  throws java.io.IOException</PRE>
<DL>
<DD>Read a record from the input stream and return the data.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>The record data.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="getCurrentBlockNum()"><!-- --></A><H3>
getCurrentBlockNum</H3>
<PRE>
public int <B>getCurrentBlockNum</B>()</PRE>
<DL>
<DD>Get the current block number, zero based.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>The current zero based block number.</DL>
</DD>
</DL>
<HR>

<A NAME="getCurrentRecordNum()"><!-- --></A><H3>
getCurrentRecordNum</H3>
<PRE>
public int <B>getCurrentRecordNum</B>()</PRE>
<DL>
<DD>Get the current record number, within the current block, zero based.
 Thus, current offset = (currentBlockNum * recsPerBlk) + currentRecNum.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>The current zero based record number.</DL>
</DD>
</DL>
<HR>

<A NAME="writeRecord(byte[])"><!-- --></A><H3>
writeRecord</H3>
<PRE>
public void <B>writeRecord</B>(byte[]&nbsp;record)
                 throws java.io.IOException</PRE>
<DL>
<DD>Write an archive record to the archive.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>record</CODE> - The record data to write to the archive.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="writeRecord(byte[], int)"><!-- --></A><H3>
writeRecord</H3>
<PRE>
public void <B>writeRecord</B>(byte[]&nbsp;buf,
                        int&nbsp;offset)
                 throws java.io.IOException</PRE>
<DL>
<DD>Write an archive record to the archive, where the record may be
 inside of a larger array buffer. The buffer must be "offset plus
 record size" long.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>buf</CODE> - The buffer containing the record data to write.<DD><CODE>offset</CODE> - The offset of the record data within buf.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="close()"><!-- --></A><H3>
close</H3>
<PRE>
public void <B>close</B>()
           throws java.io.IOException</PRE>
<DL>
<DD>Close the TarBuffer. If this is an output buffer, also flush the
 current block before closing.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV CLASS&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarConstants.html" title="interface in org.apache.tools.tar"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/tar/TarBuffer.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="TarBuffer.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
