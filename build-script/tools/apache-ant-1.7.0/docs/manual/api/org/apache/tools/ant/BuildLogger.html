<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:19 EST 2006 -->
<TITLE>
BuildLogger (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.BuildLogger interface">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="BuildLogger (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/ComponentHelper.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/BuildLogger.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="BuildLogger.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Interface BuildLogger</H2>
<DL>
<DT><B>All Superinterfaces:</B> <DD><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A>, java.util.EventListener</DD>
</DL>
<DL>
<DT><B>All Known Implementing Classes:</B> <DD><A HREF="../../../../org/apache/tools/ant/listener/AnsiColorLogger.html" title="class in org.apache.tools.ant.listener">AnsiColorLogger</A>, <A HREF="../../../../org/apache/tools/ant/listener/CommonsLoggingListener.html" title="class in org.apache.tools.ant.listener">CommonsLoggingListener</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</A>, <A HREF="../../../../org/apache/tools/ant/listener/MailLogger.html" title="class in org.apache.tools.ant.listener">MailLogger</A>, <A HREF="../../../../org/apache/tools/ant/NoBannerLogger.html" title="class in org.apache.tools.ant">NoBannerLogger</A>, <A HREF="../../../../org/apache/tools/ant/taskdefs/RecorderEntry.html" title="class in org.apache.tools.ant.taskdefs">RecorderEntry</A>, <A HREF="../../../../org/apache/tools/ant/listener/TimestampedLogger.html" title="class in org.apache.tools.ant.listener">TimestampedLogger</A>, <A HREF="../../../../org/apache/tools/ant/XmlLogger.html" title="class in org.apache.tools.ant">XmlLogger</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public interface <B>BuildLogger</B><DT>extends <A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></DL>
</PRE>

<P>
Interface used by Ant to log the build output.

 A build logger is a build listener which has the 'right' to send output to
 the ant log, which is usually <code>System.out</code> unless redirected by
 the <code>-logfile</code> option.
<P>

<P>
<HR>

<P>

<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildLogger.html#setEmacsMode(boolean)">setEmacsMode</A></B>(boolean&nbsp;emacsMode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets this logger to produce emacs (and other editor) friendly output.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildLogger.html#setErrorPrintStream(java.io.PrintStream)">setErrorPrintStream</A></B>(java.io.PrintStream&nbsp;err)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the output stream to which this logger is to send error messages.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildLogger.html#setMessageOutputLevel(int)">setMessageOutputLevel</A></B>(int&nbsp;level)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the highest level of message this logger should respond to.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/BuildLogger.html#setOutputPrintStream(java.io.PrintStream)">setOutputPrintStream</A></B>(java.io.PrintStream&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the output stream to which this logger is to send its output.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.BuildListener"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from interface org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#buildFinished(org.apache.tools.ant.BuildEvent)">buildFinished</A>, <A HREF="../../../../org/apache/tools/ant/BuildListener.html#buildStarted(org.apache.tools.ant.BuildEvent)">buildStarted</A>, <A HREF="../../../../org/apache/tools/ant/BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</A>, <A HREF="../../../../org/apache/tools/ant/BuildListener.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</A>, <A HREF="../../../../org/apache/tools/ant/BuildListener.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</A>, <A HREF="../../../../org/apache/tools/ant/BuildListener.html#taskFinished(org.apache.tools.ant.BuildEvent)">taskFinished</A>, <A HREF="../../../../org/apache/tools/ant/BuildListener.html#taskStarted(org.apache.tools.ant.BuildEvent)">taskStarted</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setMessageOutputLevel(int)"><!-- --></A><H3>
setMessageOutputLevel</H3>
<PRE>
void <B>setMessageOutputLevel</B>(int&nbsp;level)</PRE>
<DL>
<DD>Sets the highest level of message this logger should respond to.

 Only messages with a message level lower than or equal to the
 given level should be written to the log.
 <P>
 Constants for the message levels are in the
 <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant"><CODE>Project</CODE></A> class. The order of the levels, from least
 to most verbose, is <code>MSG_ERR</code>, <code>MSG_WARN</code>,
 <code>MSG_INFO</code>, <code>MSG_VERBOSE</code>,
 <code>MSG_DEBUG</code>.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>level</CODE> - the logging level for the logger.</DL>
</DD>
</DL>
<HR>

<A NAME="setOutputPrintStream(java.io.PrintStream)"><!-- --></A><H3>
setOutputPrintStream</H3>
<PRE>
void <B>setOutputPrintStream</B>(java.io.PrintStream&nbsp;output)</PRE>
<DL>
<DD>Sets the output stream to which this logger is to send its output.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - The output stream for the logger.
               Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setEmacsMode(boolean)"><!-- --></A><H3>
setEmacsMode</H3>
<PRE>
void <B>setEmacsMode</B>(boolean&nbsp;emacsMode)</PRE>
<DL>
<DD>Sets this logger to produce emacs (and other editor) friendly output.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>emacsMode</CODE> - <code>true</code> if output is to be unadorned so that
                  emacs and other editors can parse files names, etc.</DL>
</DD>
</DL>
<HR>

<A NAME="setErrorPrintStream(java.io.PrintStream)"><!-- --></A><H3>
setErrorPrintStream</H3>
<PRE>
void <B>setErrorPrintStream</B>(java.io.PrintStream&nbsp;err)</PRE>
<DL>
<DD>Sets the output stream to which this logger is to send error messages.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>err</CODE> - The error stream for the logger.
            Must not be <code>null</code>.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/ComponentHelper.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/BuildLogger.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="BuildLogger.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;CONSTR&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
