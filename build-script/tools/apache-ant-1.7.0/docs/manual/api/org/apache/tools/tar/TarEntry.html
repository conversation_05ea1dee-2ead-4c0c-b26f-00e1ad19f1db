<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:34 EST 2006 -->
<TITLE>
TarEntry (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.tar.TarEntry class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="TarEntry (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarConstants.html" title="interface in org.apache.tools.tar"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarInputStream.html" title="class in org.apache.tools.tar"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/tar/TarEntry.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="TarEntry.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.tar</FONT>
<BR>
Class TarEntry</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.tar.TarEntry</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD><A HREF="../../../../org/apache/tools/tar/TarConstants.html" title="interface in org.apache.tools.tar">TarConstants</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>TarEntry</B><DT>extends java.lang.Object<DT>implements <A HREF="../../../../org/apache/tools/tar/TarConstants.html" title="interface in org.apache.tools.tar">TarConstants</A></DL>
</PRE>

<P>
This class represents an entry in a Tar archive. It consists
 of the entry's header, as well as the entry's File. Entries
 can be instantiated in one of three ways, depending on how
 they are to be used.
 <p>
 TarEntries that are created from the header bytes read from
 an archive are instantiated with the TarEntry( byte[] )
 constructor. These entries will be used when extracting from
 or listing the contents of an archive. These entries have their
 header filled in using the header bytes. They also set the File
 to null, since they reference an archive entry not a file.
 <p>
 TarEntries that are created from Files that are to be written
 into an archive are instantiated with the TarEntry( File )
 constructor. These entries have their header filled in using
 the File's information. They also keep a reference to the File
 for convenience when writing entries.
 <p>
 Finally, TarEntries can be constructed from nothing but a name.
 This allows the programmer to construct the entry by hand, for
 instance when only an InputStream is available for writing to
 the archive, and the header information is constructed from
 other information. In this case the header fields are set to
 defaults and the File is set to null.

 <p>
 The C structure for a Tar Entry's header is:
 <pre>
 struct header {
 char name[NAMSIZ];
 char mode[8];
 char uid[8];
 char gid[8];
 char size[12];
 char mtime[12];
 char chksum[8];
 char linkflag;
 char linkname[NAMSIZ];
 char magic[8];
 char uname[TUNMLEN];
 char gname[TGNMLEN];
 char devmajor[8];
 char devminor[8];
 } header;
 </pre>
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#DEFAULT_DIR_MODE">DEFAULT_DIR_MODE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Default permissions bits for directories</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#DEFAULT_FILE_MODE">DEFAULT_FILE_MODE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Default permissions bits for files</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#MAX_NAMELEN">MAX_NAMELEN</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Maximum length of a Employee's name in the tar file</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#MILLIS_PER_SECOND">MILLIS_PER_SECOND</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convert millis to seconds</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.tar.TarConstants"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from interface org.apache.tools.tar.<A HREF="../../../../org/apache/tools/tar/TarConstants.html" title="interface in org.apache.tools.tar">TarConstants</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../org/apache/tools/tar/TarConstants.html#CHKSUMLEN">CHKSUMLEN</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#DEVLEN">DEVLEN</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#GIDLEN">GIDLEN</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#GNAMELEN">GNAMELEN</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#GNU_LONGLINK">GNU_LONGLINK</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#GNU_TMAGIC">GNU_TMAGIC</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_BLK">LF_BLK</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_CHR">LF_CHR</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_CONTIG">LF_CONTIG</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_DIR">LF_DIR</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_FIFO">LF_FIFO</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_GNUTYPE_LONGNAME">LF_GNUTYPE_LONGNAME</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_LINK">LF_LINK</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_NORMAL">LF_NORMAL</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_OLDNORM">LF_OLDNORM</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#LF_SYMLINK">LF_SYMLINK</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#MAGICLEN">MAGICLEN</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#MAXSIZE">MAXSIZE</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#MODELEN">MODELEN</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#MODTIMELEN">MODTIMELEN</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#NAMELEN">NAMELEN</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#SIZELEN">SIZELEN</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#TMAGIC">TMAGIC</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#UIDLEN">UIDLEN</A>, <A HREF="../../../../org/apache/tools/tar/TarConstants.html#UNAMELEN">UNAMELEN</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#TarEntry(byte[])">TarEntry</A></B>(byte[]&nbsp;headerBuf)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Construct an entry from an archive's header bytes.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#TarEntry(java.io.File)">TarEntry</A></B>(java.io.File&nbsp;file)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Construct an entry for a file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#TarEntry(java.lang.String)">TarEntry</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Construct an entry with only a name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#TarEntry(java.lang.String, byte)">TarEntry</A></B>(java.lang.String&nbsp;name,
         byte&nbsp;linkFlag)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Construct an entry with a name an a link flag.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#equals(java.lang.Object)">equals</A></B>(java.lang.Object&nbsp;it)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Determine if the two entries are equal.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#equals(org.apache.tools.tar.TarEntry)">equals</A></B>(<A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</A>&nbsp;it)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Determine if the two entries are equal.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</A>[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#getDirectoryEntries()">getDirectoryEntries</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If this entry represents a file, and the file is a directory, return
 an array of TarEntries for this entry's children.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.io.File</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#getFile()">getFile</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get this entry's file.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#getGroupId()">getGroupId</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get this entry's group id.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#getGroupName()">getGroupName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get this entry's group name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#getLinkName()">getLinkName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get this entry's link name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#getMode()">getMode</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get this entry's mode.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Date</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#getModTime()">getModTime</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set this entry's modification time.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#getName()">getName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get this entry's name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#getSize()">getSize</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get this entry's file size.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#getUserId()">getUserId</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get this entry's Employee id.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#getUserName()">getUserName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get this entry's Employee name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#hashCode()">hashCode</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Hashcodes are based on entry names.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#isDescendent(org.apache.tools.tar.TarEntry)">isDescendent</A></B>(<A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</A>&nbsp;desc)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Determine if the given entry is a descendant of this entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#isDirectory()">isDirectory</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return whether or not this entry represents a directory.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#isGNULongNameEntry()">isGNULongNameEntry</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Indicate if this entry is a GNU long name block</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#parseTarHeader(byte[])">parseTarHeader</A></B>(byte[]&nbsp;header)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Parse an entry's header information from a header buffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#setGroupId(int)">setGroupId</A></B>(int&nbsp;groupId)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set this entry's group id.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#setGroupName(java.lang.String)">setGroupName</A></B>(java.lang.String&nbsp;groupName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set this entry's group name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#setIds(int, int)">setIds</A></B>(int&nbsp;userId,
       int&nbsp;groupId)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to set this entry's group and Employee ids.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#setMode(int)">setMode</A></B>(int&nbsp;mode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the mode for this entry</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#setModTime(java.util.Date)">setModTime</A></B>(java.util.Date&nbsp;time)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set this entry's modification time.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#setModTime(long)">setModTime</A></B>(long&nbsp;time)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set this entry's modification time.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#setName(java.lang.String)">setName</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set this entry's name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#setNames(java.lang.String, java.lang.String)">setNames</A></B>(java.lang.String&nbsp;userName,
         java.lang.String&nbsp;groupName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to set this entry's group and Employee names.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#setSize(long)">setSize</A></B>(long&nbsp;size)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set this entry's file size.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#setUserId(int)">setUserId</A></B>(int&nbsp;userId)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set this entry's Employee id.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#setUserName(java.lang.String)">setUserName</A></B>(java.lang.String&nbsp;userName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set this entry's Employee name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarEntry.html#writeEntryHeader(byte[])">writeEntryHeader</A></B>(byte[]&nbsp;outbuf)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Write an entry's header information to a header buffer.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, finalize, getClass, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="MAX_NAMELEN"><!-- --></A><H3>
MAX_NAMELEN</H3>
<PRE>
public static final int <B>MAX_NAMELEN</B></PRE>
<DL>
<DD>Maximum length of a Employee's name in the tar file
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarEntry.MAX_NAMELEN">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_DIR_MODE"><!-- --></A><H3>
DEFAULT_DIR_MODE</H3>
<PRE>
public static final int <B>DEFAULT_DIR_MODE</B></PRE>
<DL>
<DD>Default permissions bits for directories
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarEntry.DEFAULT_DIR_MODE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="DEFAULT_FILE_MODE"><!-- --></A><H3>
DEFAULT_FILE_MODE</H3>
<PRE>
public static final int <B>DEFAULT_FILE_MODE</B></PRE>
<DL>
<DD>Default permissions bits for files
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarEntry.DEFAULT_FILE_MODE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="MILLIS_PER_SECOND"><!-- --></A><H3>
MILLIS_PER_SECOND</H3>
<PRE>
public static final int <B>MILLIS_PER_SECOND</B></PRE>
<DL>
<DD>Convert millis to seconds
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarEntry.MILLIS_PER_SECOND">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="TarEntry(java.lang.String)"><!-- --></A><H3>
TarEntry</H3>
<PRE>
public <B>TarEntry</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Construct an entry with only a name. This allows the programmer
 to construct the entry's header "by hand". File is set to null.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the entry name</DL>
</DL>
<HR>

<A NAME="TarEntry(java.lang.String, byte)"><!-- --></A><H3>
TarEntry</H3>
<PRE>
public <B>TarEntry</B>(java.lang.String&nbsp;name,
                byte&nbsp;linkFlag)</PRE>
<DL>
<DD>Construct an entry with a name an a link flag.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the entry name<DD><CODE>linkFlag</CODE> - the entry link flag.</DL>
</DL>
<HR>

<A NAME="TarEntry(java.io.File)"><!-- --></A><H3>
TarEntry</H3>
<PRE>
public <B>TarEntry</B>(java.io.File&nbsp;file)</PRE>
<DL>
<DD>Construct an entry for a file. File is set to file, and the
 header is constructed from information from the file.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>file</CODE> - The file that the entry represents.</DL>
</DL>
<HR>

<A NAME="TarEntry(byte[])"><!-- --></A><H3>
TarEntry</H3>
<PRE>
public <B>TarEntry</B>(byte[]&nbsp;headerBuf)</PRE>
<DL>
<DD>Construct an entry from an archive's header bytes. File is set
 to null.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>headerBuf</CODE> - The header bytes from a tar archive entry.</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="equals(org.apache.tools.tar.TarEntry)"><!-- --></A><H3>
equals</H3>
<PRE>
public boolean <B>equals</B>(<A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</A>&nbsp;it)</PRE>
<DL>
<DD>Determine if the two entries are equal. Equality is determined
 by the header names being equal.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>it</CODE> - Entry to be checked for equality.
<DT><B>Returns:</B><DD>True if the entries are equal.</DL>
</DD>
</DL>
<HR>

<A NAME="equals(java.lang.Object)"><!-- --></A><H3>
equals</H3>
<PRE>
public boolean <B>equals</B>(java.lang.Object&nbsp;it)</PRE>
<DL>
<DD>Determine if the two entries are equal. Equality is determined
 by the header names being equal.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>equals</CODE> in class <CODE>java.lang.Object</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>it</CODE> - Entry to be checked for equality.
<DT><B>Returns:</B><DD>True if the entries are equal.</DL>
</DD>
</DL>
<HR>

<A NAME="hashCode()"><!-- --></A><H3>
hashCode</H3>
<PRE>
public int <B>hashCode</B>()</PRE>
<DL>
<DD>Hashcodes are based on entry names.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>hashCode</CODE> in class <CODE>java.lang.Object</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the entry hashcode</DL>
</DD>
</DL>
<HR>

<A NAME="isDescendent(org.apache.tools.tar.TarEntry)"><!-- --></A><H3>
isDescendent</H3>
<PRE>
public boolean <B>isDescendent</B>(<A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</A>&nbsp;desc)</PRE>
<DL>
<DD>Determine if the given entry is a descendant of this entry.
 Descendancy is determined by the name of the descendant
 starting with this entry's name.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>desc</CODE> - Entry to be checked as a descendent of this.
<DT><B>Returns:</B><DD>True if entry is a descendant of this.</DL>
</DD>
</DL>
<HR>

<A NAME="getName()"><!-- --></A><H3>
getName</H3>
<PRE>
public java.lang.String <B>getName</B>()</PRE>
<DL>
<DD>Get this entry's name.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>This entry's name.</DL>
</DD>
</DL>
<HR>

<A NAME="setName(java.lang.String)"><!-- --></A><H3>
setName</H3>
<PRE>
public void <B>setName</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>Set this entry's name.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - This entry's new name.</DL>
</DD>
</DL>
<HR>

<A NAME="setMode(int)"><!-- --></A><H3>
setMode</H3>
<PRE>
public void <B>setMode</B>(int&nbsp;mode)</PRE>
<DL>
<DD>Set the mode for this entry
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>mode</CODE> - the mode for this entry</DL>
</DD>
</DL>
<HR>

<A NAME="getLinkName()"><!-- --></A><H3>
getLinkName</H3>
<PRE>
public java.lang.String <B>getLinkName</B>()</PRE>
<DL>
<DD>Get this entry's link name.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>This entry's link name.</DL>
</DD>
</DL>
<HR>

<A NAME="getUserId()"><!-- --></A><H3>
getUserId</H3>
<PRE>
public int <B>getUserId</B>()</PRE>
<DL>
<DD>Get this entry's Employee id.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>This entry's Employee id.</DL>
</DD>
</DL>
<HR>

<A NAME="setUserId(int)"><!-- --></A><H3>
setUserId</H3>
<PRE>
public void <B>setUserId</B>(int&nbsp;userId)</PRE>
<DL>
<DD>Set this entry's Employee id.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>userId</CODE> - This entry's new Employee id.</DL>
</DD>
</DL>
<HR>

<A NAME="getGroupId()"><!-- --></A><H3>
getGroupId</H3>
<PRE>
public int <B>getGroupId</B>()</PRE>
<DL>
<DD>Get this entry's group id.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>This entry's group id.</DL>
</DD>
</DL>
<HR>

<A NAME="setGroupId(int)"><!-- --></A><H3>
setGroupId</H3>
<PRE>
public void <B>setGroupId</B>(int&nbsp;groupId)</PRE>
<DL>
<DD>Set this entry's group id.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>groupId</CODE> - This entry's new group id.</DL>
</DD>
</DL>
<HR>

<A NAME="getUserName()"><!-- --></A><H3>
getUserName</H3>
<PRE>
public java.lang.String <B>getUserName</B>()</PRE>
<DL>
<DD>Get this entry's Employee name.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>This entry's Employee name.</DL>
</DD>
</DL>
<HR>

<A NAME="setUserName(java.lang.String)"><!-- --></A><H3>
setUserName</H3>
<PRE>
public void <B>setUserName</B>(java.lang.String&nbsp;userName)</PRE>
<DL>
<DD>Set this entry's Employee name.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>userName</CODE> - This entry's new Employee name.</DL>
</DD>
</DL>
<HR>

<A NAME="getGroupName()"><!-- --></A><H3>
getGroupName</H3>
<PRE>
public java.lang.String <B>getGroupName</B>()</PRE>
<DL>
<DD>Get this entry's group name.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>This entry's group name.</DL>
</DD>
</DL>
<HR>

<A NAME="setGroupName(java.lang.String)"><!-- --></A><H3>
setGroupName</H3>
<PRE>
public void <B>setGroupName</B>(java.lang.String&nbsp;groupName)</PRE>
<DL>
<DD>Set this entry's group name.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>groupName</CODE> - This entry's new group name.</DL>
</DD>
</DL>
<HR>

<A NAME="setIds(int, int)"><!-- --></A><H3>
setIds</H3>
<PRE>
public void <B>setIds</B>(int&nbsp;userId,
                   int&nbsp;groupId)</PRE>
<DL>
<DD>Convenience method to set this entry's group and Employee ids.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>userId</CODE> - This entry's new Employee id.<DD><CODE>groupId</CODE> - This entry's new group id.</DL>
</DD>
</DL>
<HR>

<A NAME="setNames(java.lang.String, java.lang.String)"><!-- --></A><H3>
setNames</H3>
<PRE>
public void <B>setNames</B>(java.lang.String&nbsp;userName,
                     java.lang.String&nbsp;groupName)</PRE>
<DL>
<DD>Convenience method to set this entry's group and Employee names.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>userName</CODE> - This entry's new Employee name.<DD><CODE>groupName</CODE> - This entry's new group name.</DL>
</DD>
</DL>
<HR>

<A NAME="setModTime(long)"><!-- --></A><H3>
setModTime</H3>
<PRE>
public void <B>setModTime</B>(long&nbsp;time)</PRE>
<DL>
<DD>Set this entry's modification time. The parameter passed
 to this method is in "Java time".
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>time</CODE> - This entry's new modification time.</DL>
</DD>
</DL>
<HR>

<A NAME="setModTime(java.util.Date)"><!-- --></A><H3>
setModTime</H3>
<PRE>
public void <B>setModTime</B>(java.util.Date&nbsp;time)</PRE>
<DL>
<DD>Set this entry's modification time.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>time</CODE> - This entry's new modification time.</DL>
</DD>
</DL>
<HR>

<A NAME="getModTime()"><!-- --></A><H3>
getModTime</H3>
<PRE>
public java.util.Date <B>getModTime</B>()</PRE>
<DL>
<DD>Set this entry's modification time.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>time This entry's new modification time.</DL>
</DD>
</DL>
<HR>

<A NAME="getFile()"><!-- --></A><H3>
getFile</H3>
<PRE>
public java.io.File <B>getFile</B>()</PRE>
<DL>
<DD>Get this entry's file.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>This entry's file.</DL>
</DD>
</DL>
<HR>

<A NAME="getMode()"><!-- --></A><H3>
getMode</H3>
<PRE>
public int <B>getMode</B>()</PRE>
<DL>
<DD>Get this entry's mode.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>This entry's mode.</DL>
</DD>
</DL>
<HR>

<A NAME="getSize()"><!-- --></A><H3>
getSize</H3>
<PRE>
public long <B>getSize</B>()</PRE>
<DL>
<DD>Get this entry's file size.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>This entry's file size.</DL>
</DD>
</DL>
<HR>

<A NAME="setSize(long)"><!-- --></A><H3>
setSize</H3>
<PRE>
public void <B>setSize</B>(long&nbsp;size)</PRE>
<DL>
<DD>Set this entry's file size.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>size</CODE> - This entry's new file size.</DL>
</DD>
</DL>
<HR>

<A NAME="isGNULongNameEntry()"><!-- --></A><H3>
isGNULongNameEntry</H3>
<PRE>
public boolean <B>isGNULongNameEntry</B>()</PRE>
<DL>
<DD>Indicate if this entry is a GNU long name block
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>true if this is a long name extension provided by GNU tar</DL>
</DD>
</DL>
<HR>

<A NAME="isDirectory()"><!-- --></A><H3>
isDirectory</H3>
<PRE>
public boolean <B>isDirectory</B>()</PRE>
<DL>
<DD>Return whether or not this entry represents a directory.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>True if this entry is a directory.</DL>
</DD>
</DL>
<HR>

<A NAME="getDirectoryEntries()"><!-- --></A><H3>
getDirectoryEntries</H3>
<PRE>
public <A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</A>[] <B>getDirectoryEntries</B>()</PRE>
<DL>
<DD>If this entry represents a file, and the file is a directory, return
 an array of TarEntries for this entry's children.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>An array of TarEntry's for this entry's children.</DL>
</DD>
</DL>
<HR>

<A NAME="writeEntryHeader(byte[])"><!-- --></A><H3>
writeEntryHeader</H3>
<PRE>
public void <B>writeEntryHeader</B>(byte[]&nbsp;outbuf)</PRE>
<DL>
<DD>Write an entry's header information to a header buffer.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>outbuf</CODE> - The tar entry header buffer to fill in.</DL>
</DD>
</DL>
<HR>

<A NAME="parseTarHeader(byte[])"><!-- --></A><H3>
parseTarHeader</H3>
<PRE>
public void <B>parseTarHeader</B>(byte[]&nbsp;header)</PRE>
<DL>
<DD>Parse an entry's header information from a header buffer.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>header</CODE> - The tar entry header buffer to get information from.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarConstants.html" title="interface in org.apache.tools.tar"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarInputStream.html" title="class in org.apache.tools.tar"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/tar/TarEntry.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="TarEntry.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
