<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:24 EST 2006 -->
<TITLE>
Transform (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.Transform class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Transform (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Touch.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Tstamp.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Transform.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Transform.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_classes_inherited_from_class_org.apache.tools.ant.taskdefs.ExecuteOn">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.ExecuteOn">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#methods_inherited_from_class_org.apache.tools.ant.taskdefs.ExecuteOn">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;METHOD</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class Transform</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.ExecTask</A>
              <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.ExecuteOn</A>
                  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.Transform</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>Transform</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn</A></DL>
</PRE>

<P>
Has been merged into ExecuteOn, empty class for backwards compatibility.
 We leave that in case that external programs access this class direclty,
 for example via
   <tt> Transform tr = (Transform) getProject().createTask("apply") </tt>
<P>

<P>
<HR>

<P>
<!-- ======== NESTED CLASS SUMMARY ======== -->

<A NAME="nested_class_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Nested Class Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="nested_classes_inherited_from_class_org.apache.tools.ant.taskdefs.ExecuteOn"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Nested classes/interfaces inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.FileDirBoth.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn.FileDirBoth</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.ExecuteOn"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#destDir">destDir</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#filesets">filesets</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#mapper">mapper</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#mapperElement">mapperElement</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#srcFilePos">srcFilePos</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#srcIsFirst">srcIsFirst</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#targetFilePos">targetFilePos</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#type">type</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.ExecTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#cmdl">cmdl</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#failOnError">failOnError</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#newEnvironment">newEnvironment</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#redirector">redirector</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#redirectorElement">redirectorElement</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Transform.html#Transform()">Transform</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.ExecuteOn"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#add(org.apache.tools.ant.util.FileNameMapper)">add</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#add(org.apache.tools.ant.types.ResourceCollection)">add</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#addDirset(org.apache.tools.ant.types.DirSet)">addDirset</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#addFilelist(org.apache.tools.ant.types.FileList)">addFilelist</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#addFileset(org.apache.tools.ant.types.FileSet)">addFileset</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#checkConfiguration()">checkConfiguration</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#createHandler()">createHandler</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#createMapper()">createMapper</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#createSrcfile()">createSrcfile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#createTargetfile()">createTargetfile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#getCommandline(java.lang.String[], java.io.File[])">getCommandline</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#getCommandline(java.lang.String, java.io.File)">getCommandline</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#getDirs(java.io.File, org.apache.tools.ant.DirectoryScanner)">getDirs</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#getFiles(java.io.File, org.apache.tools.ant.DirectoryScanner)">getFiles</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#getFilesAndDirs(org.apache.tools.ant.types.FileList)">getFilesAndDirs</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#runExec(org.apache.tools.ant.taskdefs.Execute)">runExec</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#runParallel(org.apache.tools.ant.taskdefs.Execute, java.util.Vector, java.util.Vector)">runParallel</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setAddsourcefile(boolean)">setAddsourcefile</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setDest(java.io.File)">setDest</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setForce(boolean)">setForce</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setForwardslash(boolean)">setForwardslash</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setIgnoremissing(boolean)">setIgnoremissing</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setMaxParallel(int)">setMaxParallel</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setParallel(boolean)">setParallel</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setRelative(boolean)">setRelative</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setSkipEmptyFilesets(boolean)">setSkipEmptyFilesets</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setType(org.apache.tools.ant.taskdefs.ExecuteOn.FileDirBoth)">setType</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setupRedirector()">setupRedirector</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecuteOn.html#setVerbose(boolean)">setVerbose</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.ExecTask"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.<A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html" title="class in org.apache.tools.ant.taskdefs">ExecTask</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#addConfiguredRedirector(org.apache.tools.ant.types.RedirectorElement)">addConfiguredRedirector</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#addEnv(org.apache.tools.ant.types.Environment.Variable)">addEnv</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#createArg()">createArg</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#createWatchdog()">createWatchdog</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#execute()">execute</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#getResolveExecutable()">getResolveExecutable</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#isValidOs()">isValidOs</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#logFlush()">logFlush</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#maybeSetResultPropertyValue(int)">maybeSetResultPropertyValue</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#prepareExec()">prepareExec</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#resolveExecutable(java.lang.String, boolean)">resolveExecutable</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#runExecute(org.apache.tools.ant.taskdefs.Execute)">runExecute</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setAppend(boolean)">setAppend</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setCommand(org.apache.tools.ant.types.Commandline)">setCommand</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setDir(java.io.File)">setDir</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setError(java.io.File)">setError</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setErrorProperty(java.lang.String)">setErrorProperty</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setExecutable(java.lang.String)">setExecutable</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setFailIfExecutionFails(boolean)">setFailIfExecutionFails</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setFailonerror(boolean)">setFailonerror</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setInput(java.io.File)">setInput</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setInputString(java.lang.String)">setInputString</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setLogError(boolean)">setLogError</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setNewenvironment(boolean)">setNewenvironment</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setOs(java.lang.String)">setOs</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setOsFamily(java.lang.String)">setOsFamily</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setOutput(java.io.File)">setOutput</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setOutputproperty(java.lang.String)">setOutputproperty</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setResolveExecutable(boolean)">setResolveExecutable</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setResultProperty(java.lang.String)">setResultProperty</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setSearchPath(boolean)">setSearchPath</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setSpawn(boolean)">setSpawn</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setTimeout(java.lang.Integer)">setTimeout</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setTimeout(java.lang.Long)">setTimeout</A>, <A HREF="../../../../../org/apache/tools/ant/taskdefs/ExecTask.html#setVMLauncher(boolean)">setVMLauncher</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Transform()"><!-- --></A><H3>
Transform</H3>
<PRE>
public <B>Transform</B>()</PRE>
<DL>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Touch.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Tstamp.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Transform.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Transform.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;<A HREF="#nested_classes_inherited_from_class_org.apache.tools.ant.taskdefs.ExecuteOn">NESTED</A>&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.ExecuteOn">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#methods_inherited_from_class_org.apache.tools.ant.taskdefs.ExecuteOn">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;METHOD</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
