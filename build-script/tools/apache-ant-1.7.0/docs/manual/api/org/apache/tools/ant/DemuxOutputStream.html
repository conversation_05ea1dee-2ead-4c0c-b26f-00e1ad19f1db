<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:19 EST 2006 -->
<TITLE>
DemuxOutputStream (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.DemuxOutputStream class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="DemuxOutputStream (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/DemuxInputStream.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/Diagnostics.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/DemuxOutputStream.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="DemuxOutputStream.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class DemuxOutputStream</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by ">java.io.OutputStream
      <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.DemuxOutputStream</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.io.Closeable, java.io.Flushable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>DemuxOutputStream</B><DT>extends java.io.OutputStream</DL>
</PRE>

<P>
Logs content written by a thread and forwards the buffers onto the
 project object which will forward the content to the appropriate
 task.
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>1.4</DD>
</DL>
<HR>

<P>

<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DemuxOutputStream.html#DemuxOutputStream(org.apache.tools.ant.Project, boolean)">DemuxOutputStream</A></B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                  boolean&nbsp;isErrorStream)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Creates a new instance of this class.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DemuxOutputStream.html#close()">close</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Equivalent to flushing the stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DemuxOutputStream.html#flush()">flush</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Writes all remaining data in the buffer associated
 with the current thread to the project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DemuxOutputStream.html#processBuffer(java.io.ByteArrayOutputStream)">processBuffer</A></B>(java.io.ByteArrayOutputStream&nbsp;buffer)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Converts the buffer to a string and sends it to the project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DemuxOutputStream.html#processFlush(java.io.ByteArrayOutputStream)">processFlush</A></B>(java.io.ByteArrayOutputStream&nbsp;buffer)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Converts the buffer to a string and sends it to the project.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DemuxOutputStream.html#write(byte[], int, int)">write</A></B>(byte[]&nbsp;b,
      int&nbsp;off,
      int&nbsp;len)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Write a block of characters to the output stream</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DemuxOutputStream.html#write(int)">write</A></B>(int&nbsp;cc)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Writes the data to the buffer and flushes the buffer if a line
 separator is detected or if the buffer has reached its maximum size.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.io.OutputStream"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.io.OutputStream</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>write</CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="DemuxOutputStream(org.apache.tools.ant.Project, boolean)"><!-- --></A><H3>
DemuxOutputStream</H3>
<PRE>
public <B>DemuxOutputStream</B>(<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A>&nbsp;project,
                         boolean&nbsp;isErrorStream)</PRE>
<DL>
<DD>Creates a new instance of this class.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>project</CODE> - The project instance for which output is being
                demultiplexed. Must not be <code>null</code>.<DD><CODE>isErrorStream</CODE> - <code>true</code> if this is the error string,
                      otherwise a normal output stream. This is
                      passed to the project so it knows
                      which stream it is receiving.</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="write(int)"><!-- --></A><H3>
write</H3>
<PRE>
public void <B>write</B>(int&nbsp;cc)
           throws java.io.IOException</PRE>
<DL>
<DD>Writes the data to the buffer and flushes the buffer if a line
 separator is detected or if the buffer has reached its maximum size.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE>write</CODE> in class <CODE>java.io.OutputStream</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>cc</CODE> - data to log (byte).
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the data cannot be written to the stream</DL>
</DD>
</DL>
<HR>

<A NAME="processBuffer(java.io.ByteArrayOutputStream)"><!-- --></A><H3>
processBuffer</H3>
<PRE>
protected void <B>processBuffer</B>(java.io.ByteArrayOutputStream&nbsp;buffer)</PRE>
<DL>
<DD>Converts the buffer to a string and sends it to the project.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>buffer</CODE> - the ByteArrayOutputStream used to collect the output
 until a line separator is seen.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/Project.html#demuxOutput(java.lang.String, boolean)"><CODE>Project.demuxOutput(String,boolean)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="processFlush(java.io.ByteArrayOutputStream)"><!-- --></A><H3>
processFlush</H3>
<PRE>
protected void <B>processFlush</B>(java.io.ByteArrayOutputStream&nbsp;buffer)</PRE>
<DL>
<DD>Converts the buffer to a string and sends it to the project.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>buffer</CODE> - the ByteArrayOutputStream used to collect the output
 until a line separator is seen.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/Project.html#demuxOutput(java.lang.String, boolean)"><CODE>Project.demuxOutput(String,boolean)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="close()"><!-- --></A><H3>
close</H3>
<PRE>
public void <B>close</B>()
           throws java.io.IOException</PRE>
<DL>
<DD>Equivalent to flushing the stream.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE>close</CODE> in interface <CODE>java.io.Closeable</CODE><DT><B>Overrides:</B><DD><CODE>close</CODE> in class <CODE>java.io.OutputStream</CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there is a problem closing the stream.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/DemuxOutputStream.html#flush()"><CODE>flush()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="flush()"><!-- --></A><H3>
flush</H3>
<PRE>
public void <B>flush</B>()
           throws java.io.IOException</PRE>
<DL>
<DD>Writes all remaining data in the buffer associated
 with the current thread to the project.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE>flush</CODE> in interface <CODE>java.io.Flushable</CODE><DT><B>Overrides:</B><DD><CODE>flush</CODE> in class <CODE>java.io.OutputStream</CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if there is a problem flushing the stream.</DL>
</DD>
</DL>
<HR>

<A NAME="write(byte[], int, int)"><!-- --></A><H3>
write</H3>
<PRE>
public void <B>write</B>(byte[]&nbsp;b,
                  int&nbsp;off,
                  int&nbsp;len)
           throws java.io.IOException</PRE>
<DL>
<DD>Write a block of characters to the output stream
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>write</CODE> in class <CODE>java.io.OutputStream</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - the array containing the data<DD><CODE>off</CODE> - the offset into the array where data starts<DD><CODE>len</CODE> - the length of block
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the data cannot be written into the stream.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/DemuxInputStream.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/Diagnostics.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/DemuxOutputStream.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="DemuxOutputStream.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
