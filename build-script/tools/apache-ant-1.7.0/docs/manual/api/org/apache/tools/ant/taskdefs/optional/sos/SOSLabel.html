<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:30 EST 2006 -->
<TITLE>
SOSLabel (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.optional.sos.SOSLabel class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="SOSLabel (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSGet.html" title="class in org.apache.tools.ant.taskdefs.optional.sos"><B>PREV CLASS</B></A>&nbsp;
&nbsp;NEXT CLASS</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/sos/SOSLabel.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="SOSLabel.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.sos.SOS">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.optional.sos</FONT>
<BR>
Class SOSLabel</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Task</A>
          <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">org.apache.tools.ant.taskdefs.optional.sos.SOS</A>
              <IMG SRC="../../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.optional.sos.SOSLabel</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html" title="interface in org.apache.tools.ant.taskdefs.optional.sos">SOSCmd</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>SOSLabel</B><DT>extends <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOS</A></DL>
</PRE>

<P>
Labels Visual SourceSafe files via a SourceOffSite server.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.sos.SOS"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.optional.sos.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOS</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#commandLine">commandLine</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#target">target</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#taskName">taskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#taskType">taskType</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#wrapper">wrapper</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.sos.SOSCmd"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from interface org.apache.tools.ant.taskdefs.optional.sos.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html" title="interface in org.apache.tools.ant.taskdefs.optional.sos">SOSCmd</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_CHECKIN_FILE">COMMAND_CHECKIN_FILE</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_CHECKIN_PROJECT">COMMAND_CHECKIN_PROJECT</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_CHECKOUT_FILE">COMMAND_CHECKOUT_FILE</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_CHECKOUT_PROJECT">COMMAND_CHECKOUT_PROJECT</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_GET_FILE">COMMAND_GET_FILE</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_GET_PROJECT">COMMAND_GET_PROJECT</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_HISTORY">COMMAND_HISTORY</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_LABEL">COMMAND_LABEL</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_SOS_EXE">COMMAND_SOS_EXE</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_COMMAND">FLAG_COMMAND</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_COMMENT">FLAG_COMMENT</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_FILE">FLAG_FILE</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_LABEL">FLAG_LABEL</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_NO_CACHE">FLAG_NO_CACHE</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_NO_COMPRESSION">FLAG_NO_COMPRESSION</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_PASSWORD">FLAG_PASSWORD</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_PROJECT">FLAG_PROJECT</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_RECURSION">FLAG_RECURSION</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_SOS_HOME">FLAG_SOS_HOME</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_SOS_SERVER">FLAG_SOS_SERVER</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_USERNAME">FLAG_USERNAME</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_VERBOSE">FLAG_VERBOSE</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_VERSION">FLAG_VERSION</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_VSS_SERVER">FLAG_VSS_SERVER</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_WORKING_DIR">FLAG_WORKING_DIR</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#PROJECT_PREFIX">PROJECT_PREFIX</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSLabel.html#SOSLabel()">SOSLabel</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSLabel.html#buildCmdLine()">buildCmdLine</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Build the command line <br>
  AddLabel required parameters: -server -name -password -database -project -label<br>
  AddLabel optional parameters: -verbose -comment<br></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSLabel.html#setComment(java.lang.String)">setComment</A></B>(java.lang.String&nbsp;comment)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The comment to apply to all files being labelled.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSLabel.html#setLabel(java.lang.String)">setLabel</A></B>(java.lang.String&nbsp;label)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The label to apply the the files in SourceSafe.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSLabel.html#setVersion(java.lang.String)">setVersion</A></B>(java.lang.String&nbsp;version)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The version number to label.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.optional.sos.SOS"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.optional.sos.<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html" title="class in org.apache.tools.ant.taskdefs.optional.sos">SOS</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#execute()">execute</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getComment()">getComment</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getFilename()">getFilename</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getLabel()">getLabel</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getLocalPath()">getLocalPath</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getNoCache()">getNoCache</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getNoCompress()">getNoCompress</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getOptionalAttributes()">getOptionalAttributes</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getPassword()">getPassword</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getProjectPath()">getProjectPath</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getRecursive()">getRecursive</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getRequiredAttributes()">getRequiredAttributes</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getSosCommand()">getSosCommand</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getSosHome()">getSosHome</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getSosServerPath()">getSosServerPath</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getUsername()">getUsername</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getVerbose()">getVerbose</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getVersion()">getVersion</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#getVssServerPath()">getVssServerPath</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#run(org.apache.tools.ant.types.Commandline)">run</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#setInternalComment(java.lang.String)">setInternalComment</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#setInternalFilename(java.lang.String)">setInternalFilename</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#setInternalLabel(java.lang.String)">setInternalLabel</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#setInternalRecursive(boolean)">setInternalRecursive</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#setInternalVersion(java.lang.String)">setInternalVersion</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#setLocalPath(org.apache.tools.ant.types.Path)">setLocalPath</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#setNoCache(boolean)">setNoCache</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#setNoCompress(boolean)">setNoCompress</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#setPassword(java.lang.String)">setPassword</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#setProjectPath(java.lang.String)">setProjectPath</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#setSosCmd(java.lang.String)">setSosCmd</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#setSosHome(java.lang.String)">setSosHome</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#setSosServerPath(java.lang.String)">setSosServerPath</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#setUsername(java.lang.String)">setUsername</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#setVerbose(boolean)">setVerbose</A>, <A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOS.html#setVssServerPath(java.lang.String)">setVssServerPath</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.Task"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/Task.html#bindToOwner(org.apache.tools.ant.Task)">bindToOwner</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getOwningTarget()">getOwningTarget</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getRuntimeConfigurableWrapper()">getRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getTaskName()">getTaskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getTaskType()">getTaskType</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#getWrapper()">getWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleErrorFlush(java.lang.String)">handleErrorFlush</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleErrorOutput(java.lang.String)">handleErrorOutput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleFlush(java.lang.String)">handleFlush</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleInput(byte[], int, int)">handleInput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#handleOutput(java.lang.String)">handleOutput</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#init()">init</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#isInvalid()">isInvalid</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.String, java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#log(java.lang.Throwable, int)">log</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#maybeConfigure()">maybeConfigure</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#perform()">perform</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#reconfigure()">reconfigure</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setOwningTarget(org.apache.tools.ant.Target)">setOwningTarget</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setRuntimeConfigurableWrapper(org.apache.tools.ant.RuntimeConfigurable)">setRuntimeConfigurableWrapper</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setTaskName(java.lang.String)">setTaskName</A>, <A HREF="../../../../../../../org/apache/tools/ant/Task.html#setTaskType(java.lang.String)">setTaskType</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#clone()">clone</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="SOSLabel()"><!-- --></A><H3>
SOSLabel</H3>
<PRE>
public <B>SOSLabel</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setVersion(java.lang.String)"><!-- --></A><H3>
setVersion</H3>
<PRE>
public void <B>setVersion</B>(java.lang.String&nbsp;version)</PRE>
<DL>
<DD>The version number to label.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>version</CODE> - The new version value</DL>
</DD>
</DL>
<HR>

<A NAME="setLabel(java.lang.String)"><!-- --></A><H3>
setLabel</H3>
<PRE>
public void <B>setLabel</B>(java.lang.String&nbsp;label)</PRE>
<DL>
<DD>The label to apply the the files in SourceSafe.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>label</CODE> - The new label value</DL>
</DD>
</DL>
<HR>

<A NAME="setComment(java.lang.String)"><!-- --></A><H3>
setComment</H3>
<PRE>
public void <B>setComment</B>(java.lang.String&nbsp;comment)</PRE>
<DL>
<DD>The comment to apply to all files being labelled.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>comment</CODE> - The new comment value</DL>
</DD>
</DL>
<HR>

<A NAME="buildCmdLine()"><!-- --></A><H3>
buildCmdLine</H3>
<PRE>
protected <A HREF="../../../../../../../org/apache/tools/ant/types/Commandline.html" title="class in org.apache.tools.ant.types">Commandline</A> <B>buildCmdLine</B>()</PRE>
<DL>
<DD>Build the command line <br>
  AddLabel required parameters: -server -name -password -database -project -label<br>
  AddLabel optional parameters: -verbose -comment<br>
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>Commandline the generated command to be executed</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../../org/apache/tools/ant/taskdefs/optional/sos/SOSGet.html" title="class in org.apache.tools.ant.taskdefs.optional.sos"><B>PREV CLASS</B></A>&nbsp;
&nbsp;NEXT CLASS</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../../index.html?org/apache/tools/ant/taskdefs/optional/sos/SOSLabel.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="SOSLabel.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.taskdefs.optional.sos.SOS">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
