<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:23 EST 2006 -->
<TITLE>
Manifest.Section (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.Manifest.Section class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Manifest.Section (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestClassPath.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Manifest.Section.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Manifest.Section.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs</FONT>
<BR>
Class Manifest.Section</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.Manifest.Section</B>
</PRE>
<DL>
<DT><B>Enclosing class:</B><DD><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public static class <B>Manifest.Section</B><DT>extends java.lang.Object</DL>
</PRE>

<P>
A manifest section - you can nest attribute elements into sections.
 A section consists of a set of attribute values,
 separated from other sections by a blank line.
<P>

<P>
<HR>

<P>

<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html#Manifest.Section()">Manifest.Section</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html#addAttributeAndCheck(org.apache.tools.ant.taskdefs.Manifest.Attribute)">addAttributeAndCheck</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Attribute</A>&nbsp;attribute)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add an attribute to the section</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html#addConfiguredAttribute(org.apache.tools.ant.taskdefs.Manifest.Attribute)">addConfiguredAttribute</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Attribute</A>&nbsp;attribute)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add an attribute to the section.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.Object</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html#clone()">clone</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Clone this section</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html#equals(java.lang.Object)">equals</A></B>(java.lang.Object&nbsp;rhs)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Attribute</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html#getAttribute(java.lang.String)">getAttribute</A></B>(java.lang.String&nbsp;attributeName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get a attribute of the section</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Enumeration</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html#getAttributeKeys()">getAttributeKeys</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the attribute keys.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html#getAttributeValue(java.lang.String)">getAttributeValue</A></B>(java.lang.String&nbsp;attributeName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the value of the attribute with the name given.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html#getName()">getName</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the Section's name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Enumeration</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html#getWarnings()">getWarnings</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the warnings for this section.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html#hashCode()">hashCode</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html#merge(org.apache.tools.ant.taskdefs.Manifest.Section)">merge</A></B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</A>&nbsp;section)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Merge in another section</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html#read(java.io.BufferedReader)">read</A></B>(java.io.BufferedReader&nbsp;reader)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Read a section through a reader.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html#removeAttribute(java.lang.String)">removeAttribute</A></B>(java.lang.String&nbsp;attributeName)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Remove the given attribute from the section</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html#setName(java.lang.String)">setName</A></B>(java.lang.String&nbsp;name)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The name of the section; optional -default is the main section.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html#write(java.io.PrintWriter)">write</A></B>(java.io.PrintWriter&nbsp;writer)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Write the section out to a print writer.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>finalize, getClass, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="Manifest.Section()"><!-- --></A><H3>
Manifest.Section</H3>
<PRE>
public <B>Manifest.Section</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setName(java.lang.String)"><!-- --></A><H3>
setName</H3>
<PRE>
public void <B>setName</B>(java.lang.String&nbsp;name)</PRE>
<DL>
<DD>The name of the section; optional -default is the main section.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>name</CODE> - the section's name</DL>
</DD>
</DL>
<HR>

<A NAME="getName()"><!-- --></A><H3>
getName</H3>
<PRE>
public java.lang.String <B>getName</B>()</PRE>
<DL>
<DD>Get the Section's name.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>the section's name.</DL>
</DD>
</DL>
<HR>

<A NAME="read(java.io.BufferedReader)"><!-- --></A><H3>
read</H3>
<PRE>
public java.lang.String <B>read</B>(java.io.BufferedReader&nbsp;reader)
                      throws <A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A>,
                             java.io.IOException</PRE>
<DL>
<DD>Read a section through a reader.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>reader</CODE> - the reader from which the section is read
<DT><B>Returns:</B><DD>the name of the next section if it has been read as
         part of this section - This only happens if the
         Manifest is malformed.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></CODE> - if the section is not valid according
         to the JAR spec
<DD><CODE>java.io.IOException</CODE> - if the section cannot be read from the reader.</DL>
</DD>
</DL>
<HR>

<A NAME="merge(org.apache.tools.ant.taskdefs.Manifest.Section)"><!-- --></A><H3>
merge</H3>
<PRE>
public void <B>merge</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Section.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Section</A>&nbsp;section)
           throws <A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></PRE>
<DL>
<DD>Merge in another section
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>section</CODE> - the section to be merged with this one.
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></CODE> - if the sections cannot be merged.</DL>
</DD>
</DL>
<HR>

<A NAME="write(java.io.PrintWriter)"><!-- --></A><H3>
write</H3>
<PRE>
public void <B>write</B>(java.io.PrintWriter&nbsp;writer)
           throws java.io.IOException</PRE>
<DL>
<DD>Write the section out to a print writer.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>writer</CODE> - the Writer to which the section is written
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - if the section cannot be written</DL>
</DD>
</DL>
<HR>

<A NAME="getAttribute(java.lang.String)"><!-- --></A><H3>
getAttribute</H3>
<PRE>
public <A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Attribute</A> <B>getAttribute</B>(java.lang.String&nbsp;attributeName)</PRE>
<DL>
<DD>Get a attribute of the section
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>attributeName</CODE> - the name of the attribute
<DT><B>Returns:</B><DD>a Manifest.Attribute instance if the attribute is
         single-valued, otherwise a Vector of Manifest.Attribute
         instances.</DL>
</DD>
</DL>
<HR>

<A NAME="getAttributeKeys()"><!-- --></A><H3>
getAttributeKeys</H3>
<PRE>
public java.util.Enumeration <B>getAttributeKeys</B>()</PRE>
<DL>
<DD>Get the attribute keys.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>an Enumeration of Strings, each string being the lower case
         key of an attribute of the section.</DL>
</DD>
</DL>
<HR>

<A NAME="getAttributeValue(java.lang.String)"><!-- --></A><H3>
getAttributeValue</H3>
<PRE>
public java.lang.String <B>getAttributeValue</B>(java.lang.String&nbsp;attributeName)</PRE>
<DL>
<DD>Get the value of the attribute with the name given.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>attributeName</CODE> - the name of the attribute to be returned.
<DT><B>Returns:</B><DD>the attribute's value or null if the attribute does not exist
         in the section</DL>
</DD>
</DL>
<HR>

<A NAME="removeAttribute(java.lang.String)"><!-- --></A><H3>
removeAttribute</H3>
<PRE>
public void <B>removeAttribute</B>(java.lang.String&nbsp;attributeName)</PRE>
<DL>
<DD>Remove the given attribute from the section
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>attributeName</CODE> - the name of the attribute to be removed.</DL>
</DD>
</DL>
<HR>

<A NAME="addConfiguredAttribute(org.apache.tools.ant.taskdefs.Manifest.Attribute)"><!-- --></A><H3>
addConfiguredAttribute</H3>
<PRE>
public void <B>addConfiguredAttribute</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Attribute</A>&nbsp;attribute)
                            throws <A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></PRE>
<DL>
<DD>Add an attribute to the section.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>attribute</CODE> - the attribute to be added to the section
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></CODE> - if the attribute is not valid.</DL>
</DD>
</DL>
<HR>

<A NAME="addAttributeAndCheck(org.apache.tools.ant.taskdefs.Manifest.Attribute)"><!-- --></A><H3>
addAttributeAndCheck</H3>
<PRE>
public java.lang.String <B>addAttributeAndCheck</B>(<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs">Manifest.Attribute</A>&nbsp;attribute)
                                      throws <A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></PRE>
<DL>
<DD>Add an attribute to the section
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>attribute</CODE> - the attribute to be added.
<DT><B>Returns:</B><DD>the value of the attribute if it is a name
         attribute - null other wise
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">ManifestException</A></CODE> - if the attribute already
            exists in this section.</DL>
</DD>
</DL>
<HR>

<A NAME="clone()"><!-- --></A><H3>
clone</H3>
<PRE>
public java.lang.Object <B>clone</B>()</PRE>
<DL>
<DD>Clone this section
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>clone</CODE> in class <CODE>java.lang.Object</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the cloned Section<DT><B>Since:</B></DT>
  <DD>Ant 1.5.2</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="getWarnings()"><!-- --></A><H3>
getWarnings</H3>
<PRE>
public java.util.Enumeration <B>getWarnings</B>()</PRE>
<DL>
<DD>Get the warnings for this section.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>an Enumeration of warning strings.</DL>
</DD>
</DL>
<HR>

<A NAME="hashCode()"><!-- --></A><H3>
hashCode</H3>
<PRE>
public int <B>hashCode</B>()</PRE>
<DL>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>hashCode</CODE> in class <CODE>java.lang.Object</CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a hash value based on the attributes.<DT><B>See Also:</B><DD><CODE>Object.hashCode()</CODE></DL>
</DD>
</DL>
<HR>

<A NAME="equals(java.lang.Object)"><!-- --></A><H3>
equals</H3>
<PRE>
public boolean <B>equals</B>(java.lang.Object&nbsp;rhs)</PRE>
<DL>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>equals</CODE> in class <CODE>java.lang.Object</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>rhs</CODE> - the object to check for equality.
<DT><B>Returns:</B><DD>true if the attributes are the same.<DT><B>See Also:</B><DD><CODE>Object.equals(java.lang.Object)</CODE></DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/Manifest.Attribute.html" title="class in org.apache.tools.ant.taskdefs"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/taskdefs/ManifestClassPath.html" title="class in org.apache.tools.ant.taskdefs"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/taskdefs/Manifest.Section.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="Manifest.Section.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
