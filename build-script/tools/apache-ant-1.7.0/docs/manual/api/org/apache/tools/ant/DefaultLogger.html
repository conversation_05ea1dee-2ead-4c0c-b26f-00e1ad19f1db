<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:19 EST 2006 -->
<TITLE>
DefaultLogger (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.DefaultLogger class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="DefaultLogger (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/ComponentHelper.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/DemuxInputStream.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/DefaultLogger.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="DefaultLogger.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class DefaultLogger</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.DefaultLogger</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.util.EventListener, <A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A>, <A HREF="../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A></DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../org/apache/tools/ant/listener/AnsiColorLogger.html" title="class in org.apache.tools.ant.listener">AnsiColorLogger</A>, <A HREF="../../../../org/apache/tools/ant/listener/MailLogger.html" title="class in org.apache.tools.ant.listener">MailLogger</A>, <A HREF="../../../../org/apache/tools/ant/NoBannerLogger.html" title="class in org.apache.tools.ant">NoBannerLogger</A>, <A HREF="../../../../org/apache/tools/ant/listener/TimestampedLogger.html" title="class in org.apache.tools.ant.listener">TimestampedLogger</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>DefaultLogger</B><DT>extends java.lang.Object<DT>implements <A HREF="../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A></DL>
</PRE>

<P>
Writes build events to a PrintStream. Currently, it
 only writes which targets are being executed, and
 any messages that get logged.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#emacsMode">emacsMode</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Whether or not to use emacs-style output</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.PrintStream</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#err">err</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PrintStream to write error messages to</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#LEFT_COLUMN_SIZE">LEFT_COLUMN_SIZE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Size of left-hand column for right-justified task name.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#lSep">lSep</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Line separator</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#msgOutputLevel">msgOutputLevel</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Lowest level of message to write out</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.io.PrintStream</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#out">out</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PrintStream to write non-error messages to</TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#DefaultLogger()">DefaultLogger</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sole constructor.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#buildFinished(org.apache.tools.ant.BuildEvent)">buildFinished</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Prints whether the build succeeded or failed,
 any errors the occurred during the build, and
 how long the build took.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#buildStarted(org.apache.tools.ant.BuildEvent)">buildStarted</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Responds to a build being started by just remembering the current time.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#formatTime(long)">formatTime</A></B>(long&nbsp;millis)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Convenience method to format a specified length of time.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#getBuildFailedMessage()">getBuildFailedMessage</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This is an override point: the message that indicates whether a build failed.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#getBuildSuccessfulMessage()">getBuildSuccessfulMessage</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This is an override point: the message that indicates that a build succeeded.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#log(java.lang.String)">log</A></B>(java.lang.String&nbsp;message)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Empty implementation which allows subclasses to receive the
 same output that is generated here.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logs a message, if the priority is suitable.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#printMessage(java.lang.String, java.io.PrintStream, int)">printMessage</A></B>(java.lang.String&nbsp;message,
             java.io.PrintStream&nbsp;stream,
             int&nbsp;priority)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Prints a message to a PrintStream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#setEmacsMode(boolean)">setEmacsMode</A></B>(boolean&nbsp;emacsMode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets this logger to produce emacs (and other editor) friendly output.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#setErrorPrintStream(java.io.PrintStream)">setErrorPrintStream</A></B>(java.io.PrintStream&nbsp;err)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the output stream to which this logger is to send error messages.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#setMessageOutputLevel(int)">setMessageOutputLevel</A></B>(int&nbsp;level)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the highest level of message this logger should respond to.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#setOutputPrintStream(java.io.PrintStream)">setOutputPrintStream</A></B>(java.io.PrintStream&nbsp;output)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the output stream to which this logger is to send its output.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;No-op implementation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logs a message to say that the target has started if this
 logger allows information-level messages.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#taskFinished(org.apache.tools.ant.BuildEvent)">taskFinished</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;No-op implementation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#taskStarted(org.apache.tools.ant.BuildEvent)">taskStarted</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;No-op implementation.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="LEFT_COLUMN_SIZE"><!-- --></A><H3>
LEFT_COLUMN_SIZE</H3>
<PRE>
public static final int <B>LEFT_COLUMN_SIZE</B></PRE>
<DL>
<DD>Size of left-hand column for right-justified task name.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#messageLogged(org.apache.tools.ant.BuildEvent)"><CODE>messageLogged(BuildEvent)</CODE></A>, 
<A HREF="../../../../constant-values.html#org.apache.tools.ant.DefaultLogger.LEFT_COLUMN_SIZE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="out"><!-- --></A><H3>
out</H3>
<PRE>
protected java.io.PrintStream <B>out</B></PRE>
<DL>
<DD>PrintStream to write non-error messages to
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="err"><!-- --></A><H3>
err</H3>
<PRE>
protected java.io.PrintStream <B>err</B></PRE>
<DL>
<DD>PrintStream to write error messages to
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="msgOutputLevel"><!-- --></A><H3>
msgOutputLevel</H3>
<PRE>
protected int <B>msgOutputLevel</B></PRE>
<DL>
<DD>Lowest level of message to write out
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="lSep"><!-- --></A><H3>
lSep</H3>
<PRE>
protected static final java.lang.String <B>lSep</B></PRE>
<DL>
<DD>Line separator
<P>
<DL>
</DL>
</DL>
<HR>

<A NAME="emacsMode"><!-- --></A><H3>
emacsMode</H3>
<PRE>
protected boolean <B>emacsMode</B></PRE>
<DL>
<DD>Whether or not to use emacs-style output
<P>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="DefaultLogger()"><!-- --></A><H3>
DefaultLogger</H3>
<PRE>
public <B>DefaultLogger</B>()</PRE>
<DL>
<DD>Sole constructor.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setMessageOutputLevel(int)"><!-- --></A><H3>
setMessageOutputLevel</H3>
<PRE>
public void <B>setMessageOutputLevel</B>(int&nbsp;level)</PRE>
<DL>
<DD>Sets the highest level of message this logger should respond to.

 Only messages with a message level lower than or equal to the
 given level should be written to the log.
 <p>
 Constants for the message levels are in the
 <A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant"><CODE>Project</CODE></A> class. The order of the levels, from least
 to most verbose, is <code>MSG_ERR</code>, <code>MSG_WARN</code>,
 <code>MSG_INFO</code>, <code>MSG_VERBOSE</code>,
 <code>MSG_DEBUG</code>.
 <p>
 The default message level for DefaultLogger is Project.MSG_ERR.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildLogger.html#setMessageOutputLevel(int)">setMessageOutputLevel</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>level</CODE> - the logging level for the logger.</DL>
</DD>
</DL>
<HR>

<A NAME="setOutputPrintStream(java.io.PrintStream)"><!-- --></A><H3>
setOutputPrintStream</H3>
<PRE>
public void <B>setOutputPrintStream</B>(java.io.PrintStream&nbsp;output)</PRE>
<DL>
<DD>Sets the output stream to which this logger is to send its output.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildLogger.html#setOutputPrintStream(java.io.PrintStream)">setOutputPrintStream</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>output</CODE> - The output stream for the logger.
               Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setErrorPrintStream(java.io.PrintStream)"><!-- --></A><H3>
setErrorPrintStream</H3>
<PRE>
public void <B>setErrorPrintStream</B>(java.io.PrintStream&nbsp;err)</PRE>
<DL>
<DD>Sets the output stream to which this logger is to send error messages.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildLogger.html#setErrorPrintStream(java.io.PrintStream)">setErrorPrintStream</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>err</CODE> - The error stream for the logger.
            Must not be <code>null</code>.</DL>
</DD>
</DL>
<HR>

<A NAME="setEmacsMode(boolean)"><!-- --></A><H3>
setEmacsMode</H3>
<PRE>
public void <B>setEmacsMode</B>(boolean&nbsp;emacsMode)</PRE>
<DL>
<DD>Sets this logger to produce emacs (and other editor) friendly output.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildLogger.html#setEmacsMode(boolean)">setEmacsMode</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>emacsMode</CODE> - <code>true</code> if output is to be unadorned so that
                  emacs and other editors can parse files names, etc.</DL>
</DD>
</DL>
<HR>

<A NAME="buildStarted(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
buildStarted</H3>
<PRE>
public void <B>buildStarted</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Responds to a build being started by just remembering the current time.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#buildStarted(org.apache.tools.ant.BuildEvent)">buildStarted</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - Ignored.</DL>
</DD>
</DL>
<HR>

<A NAME="buildFinished(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
buildFinished</H3>
<PRE>
public void <B>buildFinished</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Prints whether the build succeeded or failed,
 any errors the occurred during the build, and
 how long the build took.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#buildFinished(org.apache.tools.ant.BuildEvent)">buildFinished</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - An event with any relevant extra information.
              Must not be <code>null</code>.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getException()"><CODE>BuildEvent.getException()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="getBuildFailedMessage()"><!-- --></A><H3>
getBuildFailedMessage</H3>
<PRE>
protected java.lang.String <B>getBuildFailedMessage</B>()</PRE>
<DL>
<DD>This is an override point: the message that indicates whether a build failed.
 Subclasses can change/enhance the message.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>The classic "BUILD FAILED"</DL>
</DD>
</DL>
<HR>

<A NAME="getBuildSuccessfulMessage()"><!-- --></A><H3>
getBuildSuccessfulMessage</H3>
<PRE>
protected java.lang.String <B>getBuildSuccessfulMessage</B>()</PRE>
<DL>
<DD>This is an override point: the message that indicates that a build succeeded.
 Subclasses can change/enhance the message.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>The classic "BUILD SUCCESSFUL"</DL>
</DD>
</DL>
<HR>

<A NAME="targetStarted(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
targetStarted</H3>
<PRE>
public void <B>targetStarted</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Logs a message to say that the target has started if this
 logger allows information-level messages.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - An event with any relevant extra information.
              Must not be <code>null</code>.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getTarget()"><CODE>BuildEvent.getTarget()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="targetFinished(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
targetFinished</H3>
<PRE>
public void <B>targetFinished</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>No-op implementation.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - Ignored.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getException()"><CODE>BuildEvent.getException()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="taskStarted(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
taskStarted</H3>
<PRE>
public void <B>taskStarted</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>No-op implementation.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#taskStarted(org.apache.tools.ant.BuildEvent)">taskStarted</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - Ignored.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getTask()"><CODE>BuildEvent.getTask()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="taskFinished(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
taskFinished</H3>
<PRE>
public void <B>taskFinished</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>No-op implementation.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#taskFinished(org.apache.tools.ant.BuildEvent)">taskFinished</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - Ignored.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getException()"><CODE>BuildEvent.getException()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="messageLogged(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
messageLogged</H3>
<PRE>
public void <B>messageLogged</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Logs a message, if the priority is suitable.
 In non-emacs mode, task level messages are prefixed by the
 task name which is right-justified.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - A BuildEvent containing message information.
              Must not be <code>null</code>.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getMessage()"><CODE>BuildEvent.getMessage()</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getException()"><CODE>BuildEvent.getException()</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getPriority()"><CODE>BuildEvent.getPriority()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="formatTime(long)"><!-- --></A><H3>
formatTime</H3>
<PRE>
protected static java.lang.String <B>formatTime</B>(long&nbsp;millis)</PRE>
<DL>
<DD>Convenience method to format a specified length of time.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>millis</CODE> - Length of time to format, in milliseconds.
<DT><B>Returns:</B><DD>the time as a formatted string.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/util/DateUtils.html#formatElapsedTime(long)"><CODE>DateUtils.formatElapsedTime(long)</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="printMessage(java.lang.String, java.io.PrintStream, int)"><!-- --></A><H3>
printMessage</H3>
<PRE>
protected void <B>printMessage</B>(java.lang.String&nbsp;message,
                            java.io.PrintStream&nbsp;stream,
                            int&nbsp;priority)</PRE>
<DL>
<DD>Prints a message to a PrintStream.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>message</CODE> - The message to print.
                 Should not be <code>null</code>.<DD><CODE>stream</CODE> - A PrintStream to print the message to.
                 Must not be <code>null</code>.<DD><CODE>priority</CODE> - The priority of the message.
                 (Ignored in this implementation.)</DL>
</DD>
</DL>
<HR>

<A NAME="log(java.lang.String)"><!-- --></A><H3>
log</H3>
<PRE>
protected void <B>log</B>(java.lang.String&nbsp;message)</PRE>
<DL>
<DD>Empty implementation which allows subclasses to receive the
 same output that is generated here.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>message</CODE> - Message being logged. Should not be <code>null</code>.</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/ComponentHelper.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/DemuxInputStream.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/DefaultLogger.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="DefaultLogger.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
