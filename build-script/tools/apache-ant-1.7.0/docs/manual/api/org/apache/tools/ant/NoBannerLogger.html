<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:20 EST 2006 -->
<TITLE>
NoBannerLogger (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.NoBannerLogger class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="NoBannerLogger (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/Main.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/PathTokenizer.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/NoBannerLogger.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="NoBannerLogger.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant</FONT>
<BR>
Class NoBannerLogger</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html" title="class in org.apache.tools.ant">org.apache.tools.ant.DefaultLogger</A>
      <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.NoBannerLogger</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.util.EventListener, <A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A>, <A HREF="../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>NoBannerLogger</B><DT>extends <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</A></DL>
</PRE>

<P>
Extends DefaultLogger to strip out empty targets.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/NoBannerLogger.html#targetName">targetName</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Name of the current target, if it should
 be displayed on the next message.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.DefaultLogger"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#emacsMode">emacsMode</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#err">err</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#LEFT_COLUMN_SIZE">LEFT_COLUMN_SIZE</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#lSep">lSep</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#msgOutputLevel">msgOutputLevel</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#out">out</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/NoBannerLogger.html#NoBannerLogger()">NoBannerLogger</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sole constructor.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/NoBannerLogger.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Logs a message for a target if it is of an appropriate
 priority, also logging the name of the target if this
 is the first message which needs to be logged for the
 target.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/NoBannerLogger.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Resets the current target name to <code>null</code>.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/ant/NoBannerLogger.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</A></B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Notes the name of the target so it can be logged
 if it generates any messages.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.DefaultLogger"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#buildFinished(org.apache.tools.ant.BuildEvent)">buildFinished</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#buildStarted(org.apache.tools.ant.BuildEvent)">buildStarted</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#formatTime(long)">formatTime</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#getBuildFailedMessage()">getBuildFailedMessage</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#getBuildSuccessfulMessage()">getBuildSuccessfulMessage</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#log(java.lang.String)">log</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#printMessage(java.lang.String, java.io.PrintStream, int)">printMessage</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#setEmacsMode(boolean)">setEmacsMode</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#setErrorPrintStream(java.io.PrintStream)">setErrorPrintStream</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#setMessageOutputLevel(int)">setMessageOutputLevel</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#setOutputPrintStream(java.io.PrintStream)">setOutputPrintStream</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#taskFinished(org.apache.tools.ant.BuildEvent)">taskFinished</A>, <A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#taskStarted(org.apache.tools.ant.BuildEvent)">taskStarted</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="targetName"><!-- --></A><H3>
targetName</H3>
<PRE>
protected java.lang.String <B>targetName</B></PRE>
<DL>
<DD>Name of the current target, if it should
 be displayed on the next message. This is
 set when a target starts building, and reset
 to <code>null</code> after the first message for
 the target is logged.
<P>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="NoBannerLogger()"><!-- --></A><H3>
NoBannerLogger</H3>
<PRE>
public <B>NoBannerLogger</B>()</PRE>
<DL>
<DD>Sole constructor.
<P>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="targetStarted(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
targetStarted</H3>
<PRE>
public void <B>targetStarted</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Notes the name of the target so it can be logged
 if it generates any messages.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE><DT><B>Overrides:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#targetStarted(org.apache.tools.ant.BuildEvent)">targetStarted</A></CODE> in class <CODE><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - A BuildEvent containing target information.
              Must not be <code>null</code>.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getTarget()"><CODE>BuildEvent.getTarget()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="targetFinished(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
targetFinished</H3>
<PRE>
public void <B>targetFinished</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Resets the current target name to <code>null</code>.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE><DT><B>Overrides:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#targetFinished(org.apache.tools.ant.BuildEvent)">targetFinished</A></CODE> in class <CODE><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - Ignored in this implementation.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getException()"><CODE>BuildEvent.getException()</CODE></A></DL>
</DD>
</DL>
<HR>

<A NAME="messageLogged(org.apache.tools.ant.BuildEvent)"><!-- --></A><H3>
messageLogged</H3>
<PRE>
public void <B>messageLogged</B>(<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">BuildEvent</A>&nbsp;event)</PRE>
<DL>
<DD>Logs a message for a target if it is of an appropriate
 priority, also logging the name of the target if this
 is the first message which needs to be logged for the
 target.
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</A></CODE> in interface <CODE><A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant">BuildListener</A></CODE><DT><B>Overrides:</B><DD><CODE><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html#messageLogged(org.apache.tools.ant.BuildEvent)">messageLogged</A></CODE> in class <CODE><A HREF="../../../../org/apache/tools/ant/DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>event</CODE> - A BuildEvent containing message information.
              Must not be <code>null</code>.<DT><B>See Also:</B><DD><A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getMessage()"><CODE>BuildEvent.getMessage()</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getException()"><CODE>BuildEvent.getException()</CODE></A>, 
<A HREF="../../../../org/apache/tools/ant/BuildEvent.html#getPriority()"><CODE>BuildEvent.getPriority()</CODE></A></DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/ant/Main.html" title="class in org.apache.tools.ant"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/PathTokenizer.html" title="class in org.apache.tools.ant"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/NoBannerLogger.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="NoBannerLogger.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
