<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:34 EST 2006 -->
<TITLE>
TarOutputStream (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.tar.TarOutputStream class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="TarOutputStream (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarInputStream.html" title="class in org.apache.tools.tar"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarUtils.html" title="class in org.apache.tools.tar"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/tar/TarOutputStream.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="TarOutputStream.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.tar</FONT>
<BR>
Class TarOutputStream</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../resources/inherit.gif" ALT="extended by ">java.io.OutputStream
      <IMG SRC="../../../../resources/inherit.gif" ALT="extended by ">java.io.FilterOutputStream
          <IMG SRC="../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.tar.TarOutputStream</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.io.Closeable, java.io.Flushable</DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>TarOutputStream</B><DT>extends java.io.FilterOutputStream</DL>
</PRE>

<P>
The TarOutputStream writes a UNIX tar archive as an OutputStream.
 Methods are provided to put entries, and then write their contents
 by writing to this stream using write().
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#assemBuf">assemBuf</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#assemLen">assemLen</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;<A HREF="../../../../org/apache/tools/tar/TarBuffer.html" title="class in org.apache.tools.tar">TarBuffer</A></CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#buffer">buffer</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#currBytes">currBytes</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#currName">currName</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;long</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#currSize">currSize</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#debug">debug</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#LONGFILE_ERROR">LONGFILE_ERROR</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Fail if a long file name is required in the archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#LONGFILE_GNU">LONGFILE_GNU</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;GNU tar extensions are used to store long file names in the archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#LONGFILE_TRUNCATE">LONGFILE_TRUNCATE</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Long paths will be truncated in the archive.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#longFileMode">longFileMode</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#oneBuf">oneBuf</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>protected &nbsp;byte[]</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#recordBuf">recordBuf</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_java.io.FilterOutputStream"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class java.io.FilterOutputStream</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>out</CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#TarOutputStream(java.io.OutputStream)">TarOutputStream</A></B>(java.io.OutputStream&nbsp;os)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for TarInputStream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#TarOutputStream(java.io.OutputStream, int)">TarOutputStream</A></B>(java.io.OutputStream&nbsp;os,
                int&nbsp;blockSize)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for TarInputStream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#TarOutputStream(java.io.OutputStream, int, int)">TarOutputStream</A></B>(java.io.OutputStream&nbsp;os,
                int&nbsp;blockSize,
                int&nbsp;recordSize)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Constructor for TarInputStream.</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#close()">close</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Ends the TAR archive and closes the underlying OutputStream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#closeEntry()">closeEntry</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Close an entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#finish()">finish</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Ends the TAR archive without closing the underlying OutputStream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;int</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#getRecordSize()">getRecordSize</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the record size being used by this stream's TarBuffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#putNextEntry(org.apache.tools.tar.TarEntry)">putNextEntry</A></B>(<A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</A>&nbsp;entry)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Put an entry on the output stream.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#setBufferDebug(boolean)">setBufferDebug</A></B>(boolean&nbsp;debug)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the debugging flag in this stream's TarBuffer.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#setDebug(boolean)">setDebug</A></B>(boolean&nbsp;debugF)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sets the debugging flag.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#setLongFileMode(int)">setLongFileMode</A></B>(int&nbsp;longFileMode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Set the long file mode.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#write(byte[])">write</A></B>(byte[]&nbsp;wBuf)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Writes bytes to the current tar archive entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#write(byte[], int, int)">write</A></B>(byte[]&nbsp;wBuf,
      int&nbsp;wOffset,
      int&nbsp;numToWrite)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Writes bytes to the current tar archive entry.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../org/apache/tools/tar/TarOutputStream.html#write(int)">write</A></B>(int&nbsp;b)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Writes a byte to the current tar archive entry.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.io.FilterOutputStream"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.io.FilterOutputStream</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>flush</CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="LONGFILE_ERROR"><!-- --></A><H3>
LONGFILE_ERROR</H3>
<PRE>
public static final int <B>LONGFILE_ERROR</B></PRE>
<DL>
<DD>Fail if a long file name is required in the archive.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarOutputStream.LONGFILE_ERROR">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LONGFILE_TRUNCATE"><!-- --></A><H3>
LONGFILE_TRUNCATE</H3>
<PRE>
public static final int <B>LONGFILE_TRUNCATE</B></PRE>
<DL>
<DD>Long paths will be truncated in the archive.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarOutputStream.LONGFILE_TRUNCATE">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="LONGFILE_GNU"><!-- --></A><H3>
LONGFILE_GNU</H3>
<PRE>
public static final int <B>LONGFILE_GNU</B></PRE>
<DL>
<DD>GNU tar extensions are used to store long file names in the archive.
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../constant-values.html#org.apache.tools.tar.TarOutputStream.LONGFILE_GNU">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="debug"><!-- --></A><H3>
debug</H3>
<PRE>
protected boolean <B>debug</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="currSize"><!-- --></A><H3>
currSize</H3>
<PRE>
protected long <B>currSize</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="currName"><!-- --></A><H3>
currName</H3>
<PRE>
protected java.lang.String <B>currName</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="currBytes"><!-- --></A><H3>
currBytes</H3>
<PRE>
protected long <B>currBytes</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="oneBuf"><!-- --></A><H3>
oneBuf</H3>
<PRE>
protected byte[] <B>oneBuf</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="recordBuf"><!-- --></A><H3>
recordBuf</H3>
<PRE>
protected byte[] <B>recordBuf</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="assemLen"><!-- --></A><H3>
assemLen</H3>
<PRE>
protected int <B>assemLen</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="assemBuf"><!-- --></A><H3>
assemBuf</H3>
<PRE>
protected byte[] <B>assemBuf</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="buffer"><!-- --></A><H3>
buffer</H3>
<PRE>
protected <A HREF="../../../../org/apache/tools/tar/TarBuffer.html" title="class in org.apache.tools.tar">TarBuffer</A> <B>buffer</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR>

<A NAME="longFileMode"><!-- --></A><H3>
longFileMode</H3>
<PRE>
protected int <B>longFileMode</B></PRE>
<DL>
<DL>
</DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="TarOutputStream(java.io.OutputStream)"><!-- --></A><H3>
TarOutputStream</H3>
<PRE>
public <B>TarOutputStream</B>(java.io.OutputStream&nbsp;os)</PRE>
<DL>
<DD>Constructor for TarInputStream.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>os</CODE> - the output stream to use</DL>
</DL>
<HR>

<A NAME="TarOutputStream(java.io.OutputStream, int)"><!-- --></A><H3>
TarOutputStream</H3>
<PRE>
public <B>TarOutputStream</B>(java.io.OutputStream&nbsp;os,
                       int&nbsp;blockSize)</PRE>
<DL>
<DD>Constructor for TarInputStream.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>os</CODE> - the output stream to use<DD><CODE>blockSize</CODE> - the block size to use</DL>
</DL>
<HR>

<A NAME="TarOutputStream(java.io.OutputStream, int, int)"><!-- --></A><H3>
TarOutputStream</H3>
<PRE>
public <B>TarOutputStream</B>(java.io.OutputStream&nbsp;os,
                       int&nbsp;blockSize,
                       int&nbsp;recordSize)</PRE>
<DL>
<DD>Constructor for TarInputStream.
<P>
<DL>
<DT><B>Parameters:</B><DD><CODE>os</CODE> - the output stream to use<DD><CODE>blockSize</CODE> - the block size to use<DD><CODE>recordSize</CODE> - the record size to use</DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="setLongFileMode(int)"><!-- --></A><H3>
setLongFileMode</H3>
<PRE>
public void <B>setLongFileMode</B>(int&nbsp;longFileMode)</PRE>
<DL>
<DD>Set the long file mode.
 This can be LONGFILE_ERROR(0), LONGFILE_TRUNCATE(1) or LONGFILE_GNU(2).
 This specifies the treatment of long file names (names >= TarConstants.NAMELEN).
 Default is LONGFILE_ERROR.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>longFileMode</CODE> - the mode to use</DL>
</DD>
</DL>
<HR>

<A NAME="setDebug(boolean)"><!-- --></A><H3>
setDebug</H3>
<PRE>
public void <B>setDebug</B>(boolean&nbsp;debugF)</PRE>
<DL>
<DD>Sets the debugging flag.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>debugF</CODE> - True to turn on debugging.</DL>
</DD>
</DL>
<HR>

<A NAME="setBufferDebug(boolean)"><!-- --></A><H3>
setBufferDebug</H3>
<PRE>
public void <B>setBufferDebug</B>(boolean&nbsp;debug)</PRE>
<DL>
<DD>Sets the debugging flag in this stream's TarBuffer.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>debug</CODE> - True to turn on debugging.</DL>
</DD>
</DL>
<HR>

<A NAME="finish()"><!-- --></A><H3>
finish</H3>
<PRE>
public void <B>finish</B>()
            throws java.io.IOException</PRE>
<DL>
<DD>Ends the TAR archive without closing the underlying OutputStream.
 The result is that the two EOF records of nulls are written.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="close()"><!-- --></A><H3>
close</H3>
<PRE>
public void <B>close</B>()
           throws java.io.IOException</PRE>
<DL>
<DD>Ends the TAR archive and closes the underlying OutputStream.
 This means that finish() is called followed by calling the
 TarBuffer's close().
<P>
<DD><DL>
<DT><B>Specified by:</B><DD><CODE>close</CODE> in interface <CODE>java.io.Closeable</CODE><DT><B>Overrides:</B><DD><CODE>close</CODE> in class <CODE>java.io.FilterOutputStream</CODE></DL>
</DD>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="getRecordSize()"><!-- --></A><H3>
getRecordSize</H3>
<PRE>
public int <B>getRecordSize</B>()</PRE>
<DL>
<DD>Get the record size being used by this stream's TarBuffer.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>The TarBuffer record size.</DL>
</DD>
</DL>
<HR>

<A NAME="putNextEntry(org.apache.tools.tar.TarEntry)"><!-- --></A><H3>
putNextEntry</H3>
<PRE>
public void <B>putNextEntry</B>(<A HREF="../../../../org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</A>&nbsp;entry)
                  throws java.io.IOException</PRE>
<DL>
<DD>Put an entry on the output stream. This writes the entry's
 header record and positions the output stream for writing
 the contents of the entry. Once this method is called, the
 stream is ready for calls to write() to write the entry's
 contents. Once the contents are written, closeEntry()
 <B>MUST</B> be called to ensure that all buffered data
 is completely written to the output stream.
<P>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>entry</CODE> - The TarEntry to be written to the archive.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="closeEntry()"><!-- --></A><H3>
closeEntry</H3>
<PRE>
public void <B>closeEntry</B>()
                throws java.io.IOException</PRE>
<DL>
<DD>Close an entry. This method MUST be called for all file
 entries that contain data. The reason is that we must
 buffer data written to the stream in order to satisfy
 the buffer's record based writes. Thus, there may be
 data fragments still being assembled that must be written
 to the output stream before this entry is closed and the
 next entry written.
<P>
<DD><DL>

<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="write(int)"><!-- --></A><H3>
write</H3>
<PRE>
public void <B>write</B>(int&nbsp;b)
           throws java.io.IOException</PRE>
<DL>
<DD>Writes a byte to the current tar archive entry.

 This method simply calls read( byte[], int, int ).
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>write</CODE> in class <CODE>java.io.FilterOutputStream</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>b</CODE> - The byte written.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="write(byte[])"><!-- --></A><H3>
write</H3>
<PRE>
public void <B>write</B>(byte[]&nbsp;wBuf)
           throws java.io.IOException</PRE>
<DL>
<DD>Writes bytes to the current tar archive entry.

 This method simply calls write( byte[], int, int ).
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>write</CODE> in class <CODE>java.io.FilterOutputStream</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>wBuf</CODE> - The buffer to write to the archive.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="write(byte[], int, int)"><!-- --></A><H3>
write</H3>
<PRE>
public void <B>write</B>(byte[]&nbsp;wBuf,
                  int&nbsp;wOffset,
                  int&nbsp;numToWrite)
           throws java.io.IOException</PRE>
<DL>
<DD>Writes bytes to the current tar archive entry. This method
 is aware of the current entry and will throw an exception if
 you attempt to write bytes past the length specified for the
 current entry. The method is also (painfully) aware of the
 record buffering required by TarBuffer, and manages buffers
 that are not a multiple of recordsize in length, including
 assembling records from small buffers.
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE>write</CODE> in class <CODE>java.io.FilterOutputStream</CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>wBuf</CODE> - The buffer to write to the archive.<DD><CODE>wOffset</CODE> - The offset in the buffer from which to get bytes.<DD><CODE>numToWrite</CODE> - The number of bytes to write.
<DT><B>Throws:</B>
<DD><CODE>java.io.IOException</CODE> - on error</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarInputStream.html" title="class in org.apache.tools.tar"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/tar/TarUtils.html" title="class in org.apache.tools.tar"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/tar/TarOutputStream.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="TarOutputStream.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
