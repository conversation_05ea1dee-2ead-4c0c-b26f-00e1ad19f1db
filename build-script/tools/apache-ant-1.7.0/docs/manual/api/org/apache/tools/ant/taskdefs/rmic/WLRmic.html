<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:31 EST 2006 -->
<TITLE>
WLRmic (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.taskdefs.rmic.WLRmic class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="WLRmic (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/SunRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/XNewRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../index.html?org/apache/tools/ant/taskdefs/rmic/WLRmic.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="WLRmic.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.taskdefs.rmic</FONT>
<BR>
Class WLRmic</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter</A>
      <IMG SRC="../../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.taskdefs.rmic.WLRmic</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/RmicAdapter.html" title="interface in org.apache.tools.ant.taskdefs.rmic">RmicAdapter</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>WLRmic</B><DT>extends <A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">DefaultRmicAdapter</A></DL>
</PRE>

<P>
The implementation of the rmic for WebLogic
<P>

<P>
<DL>
<DT><B>Since:</B></DT>
  <DD>Ant 1.4</DD>
</DL>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/WLRmic.html#COMPILER_NAME">COMPILER_NAME</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;the name of this adapter for users to select</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/WLRmic.html#ERROR_NO_WLRMIC_ON_CLASSPATH">ERROR_NO_WLRMIC_ON_CLASSPATH</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The error string to use if not able to find the weblogic rmic</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/WLRmic.html#ERROR_WLRMIC_FAILED">ERROR_WLRMIC_FAILED</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The error string to use if not able to start the weblogic rmic</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/WLRmic.html#WL_RMI_SKEL_SUFFIX">WL_RMI_SKEL_SUFFIX</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The skeleton suffix</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/WLRmic.html#WL_RMI_STUB_SUFFIX">WL_RMI_STUB_SUFFIX</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The stub suffix</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>static&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/WLRmic.html#WLRMIC_CLASSNAME">WLRMIC_CLASSNAME</A></B></CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;The classname of the weblogic rmic</TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.taskdefs.rmic.<A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">DefaultRmicAdapter</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#RMI_SKEL_SUFFIX">RMI_SKEL_SUFFIX</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#RMI_STUB_SUFFIX">RMI_STUB_SUFFIX</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#RMI_TIE_SUFFIX">RMI_TIE_SUFFIX</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#STUB_1_1">STUB_1_1</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#STUB_1_2">STUB_1_2</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#STUB_COMPAT">STUB_COMPAT</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/WLRmic.html#WLRmic()">WLRmic</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;boolean</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/WLRmic.html#execute()">execute</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Carry out the rmic compilation.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/WLRmic.html#getSkelClassSuffix()">getSkelClassSuffix</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the suffix for the rmic skeleton classes</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.lang.String</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/WLRmic.html#getStubClassSuffix()">getStubClassSuffix</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Get the suffix for the rmic stub classes</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.taskdefs.rmic.<A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">DefaultRmicAdapter</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#getClasspath()">getClasspath</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#getCompileClasspath()">getCompileClasspath</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#getMapper()">getMapper</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#getRmic()">getRmic</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#getTieClassSuffix()">getTieClassSuffix</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#logAndAddFilesToCompile(org.apache.tools.ant.types.Commandline)">logAndAddFilesToCompile</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#setRmic(org.apache.tools.ant.taskdefs.Rmic)">setRmic</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#setupRmicCommand()">setupRmicCommand</A>, <A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#setupRmicCommand(java.lang.String[])">setupRmicCommand</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ============ FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Field Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="WLRMIC_CLASSNAME"><!-- --></A><H3>
WLRMIC_CLASSNAME</H3>
<PRE>
public static final java.lang.String <B>WLRMIC_CLASSNAME</B></PRE>
<DL>
<DD>The classname of the weblogic rmic
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.WLRmic.WLRMIC_CLASSNAME">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="COMPILER_NAME"><!-- --></A><H3>
COMPILER_NAME</H3>
<PRE>
public static final java.lang.String <B>COMPILER_NAME</B></PRE>
<DL>
<DD>the name of this adapter for users to select
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.WLRmic.COMPILER_NAME">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_NO_WLRMIC_ON_CLASSPATH"><!-- --></A><H3>
ERROR_NO_WLRMIC_ON_CLASSPATH</H3>
<PRE>
public static final java.lang.String <B>ERROR_NO_WLRMIC_ON_CLASSPATH</B></PRE>
<DL>
<DD>The error string to use if not able to find the weblogic rmic
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.WLRmic.ERROR_NO_WLRMIC_ON_CLASSPATH">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="ERROR_WLRMIC_FAILED"><!-- --></A><H3>
ERROR_WLRMIC_FAILED</H3>
<PRE>
public static final java.lang.String <B>ERROR_WLRMIC_FAILED</B></PRE>
<DL>
<DD>The error string to use if not able to start the weblogic rmic
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.WLRmic.ERROR_WLRMIC_FAILED">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="WL_RMI_STUB_SUFFIX"><!-- --></A><H3>
WL_RMI_STUB_SUFFIX</H3>
<PRE>
public static final java.lang.String <B>WL_RMI_STUB_SUFFIX</B></PRE>
<DL>
<DD>The stub suffix
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.WLRmic.WL_RMI_STUB_SUFFIX">Constant Field Values</A></DL>
</DL>
<HR>

<A NAME="WL_RMI_SKEL_SUFFIX"><!-- --></A><H3>
WL_RMI_SKEL_SUFFIX</H3>
<PRE>
public static final java.lang.String <B>WL_RMI_SKEL_SUFFIX</B></PRE>
<DL>
<DD>The skeleton suffix
<P>
<DL>
<DT><B>See Also:</B><DD><A HREF="../../../../../../constant-values.html#org.apache.tools.ant.taskdefs.rmic.WLRmic.WL_RMI_SKEL_SUFFIX">Constant Field Values</A></DL>
</DL>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="WLRmic()"><!-- --></A><H3>
WLRmic</H3>
<PRE>
public <B>WLRmic</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="execute()"><!-- --></A><H3>
execute</H3>
<PRE>
public boolean <B>execute</B>()
                throws <A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Carry out the rmic compilation.
<P>
<DD><DL>

<DT><B>Returns:</B><DD>true if the compilation succeeded
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - on error</DL>
</DD>
</DL>
<HR>

<A NAME="getStubClassSuffix()"><!-- --></A><H3>
getStubClassSuffix</H3>
<PRE>
public java.lang.String <B>getStubClassSuffix</B>()</PRE>
<DL>
<DD>Get the suffix for the rmic stub classes
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#getStubClassSuffix()">getStubClassSuffix</A></CODE> in class <CODE><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">DefaultRmicAdapter</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the stub suffix</DL>
</DD>
</DL>
<HR>

<A NAME="getSkelClassSuffix()"><!-- --></A><H3>
getSkelClassSuffix</H3>
<PRE>
public java.lang.String <B>getSkelClassSuffix</B>()</PRE>
<DL>
<DD>Get the suffix for the rmic skeleton classes
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#getSkelClassSuffix()">getSkelClassSuffix</A></CODE> in class <CODE><A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">DefaultRmicAdapter</A></CODE></DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>the skeleton suffix</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/SunRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../../org/apache/tools/ant/taskdefs/rmic/XNewRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../../index.html?org/apache/tools/ant/taskdefs/rmic/WLRmic.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="WLRmic.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#field_summary">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;<A HREF="#field_detail">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
