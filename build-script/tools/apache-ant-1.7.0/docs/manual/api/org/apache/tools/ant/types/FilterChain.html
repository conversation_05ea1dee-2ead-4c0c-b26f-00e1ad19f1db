<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:31 EST 2006 -->
<TITLE>
FilterChain (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="org.apache.tools.ant.types.FilterChain class">

<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="FilterChain (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/FilterSet.html" title="class in org.apache.tools.ant.types"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/types/FilterChain.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="FilterChain.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.types.DataType">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
org.apache.tools.ant.types</FONT>
<BR>
Class FilterChain</H2>
<PRE>
java.lang.Object
  <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ProjectComponent</A>
      <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">org.apache.tools.ant.types.DataType</A>
          <IMG SRC="../../../../../resources/inherit.gif" ALT="extended by "><B>org.apache.tools.ant.types.FilterChain</B>
</PRE>
<DL>
<DT><B>All Implemented Interfaces:</B> <DD>java.lang.Cloneable</DD>
</DL>
<DL>
<DT><B>Direct Known Subclasses:</B> <DD><A HREF="../../../../../org/apache/tools/ant/types/mappers/FilterMapper.html" title="class in org.apache.tools.ant.types.mappers">FilterMapper</A></DD>
</DL>
<HR>
<DL>
<DT><PRE>public class <B>FilterChain</B><DT>extends <A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A><DT>implements java.lang.Cloneable</DL>
</PRE>

<P>
FilterChain may contain a chained set of filter readers.
<P>

<P>
<HR>

<P>
<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Field Summary</B></FONT></TH>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.types.DataType"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/DataType.html#checked">checked</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#ref">ref</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="fields_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Fields inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#description">description</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#location">location</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#project">project</A></CODE></TD>
</TR>
</TABLE>
&nbsp;
<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Constructor Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#FilterChain()">FilterChain</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Method Summary</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#add(org.apache.tools.ant.filters.ChainableReader)">add</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</A>&nbsp;filter)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a chainfilter filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addClassConstants(org.apache.tools.ant.filters.ClassConstants)">addClassConstants</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/ClassConstants.html" title="class in org.apache.tools.ant.filters">ClassConstants</A>&nbsp;classConstants)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a ClassConstants filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addContainsRegex(org.apache.tools.ant.filters.TokenFilter.ContainsRegex)">addContainsRegex</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/TokenFilter.ContainsRegex.html" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsRegex</A>&nbsp;filter)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a containsregex filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addDeleteCharacters(org.apache.tools.ant.filters.TokenFilter.DeleteCharacters)">addDeleteCharacters</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/TokenFilter.DeleteCharacters.html" title="class in org.apache.tools.ant.filters">TokenFilter.DeleteCharacters</A>&nbsp;filter)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a delete characters filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addEscapeUnicode(org.apache.tools.ant.filters.EscapeUnicode)">addEscapeUnicode</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/EscapeUnicode.html" title="class in org.apache.tools.ant.filters">EscapeUnicode</A>&nbsp;escapeUnicode)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add an EscapeUnicode filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addExpandProperties(org.apache.tools.ant.filters.ExpandProperties)">addExpandProperties</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/ExpandProperties.html" title="class in org.apache.tools.ant.filters">ExpandProperties</A>&nbsp;expandProperties)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add an ExpandProperties filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addFilterReader(org.apache.tools.ant.types.AntFilterReader)">addFilterReader</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/AntFilterReader.html" title="class in org.apache.tools.ant.types">AntFilterReader</A>&nbsp;filterReader)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add an AntFilterReader filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addHeadFilter(org.apache.tools.ant.filters.HeadFilter)">addHeadFilter</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/HeadFilter.html" title="class in org.apache.tools.ant.filters">HeadFilter</A>&nbsp;headFilter)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a HeadFilter filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addIgnoreBlank(org.apache.tools.ant.filters.TokenFilter.IgnoreBlank)">addIgnoreBlank</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/TokenFilter.IgnoreBlank.html" title="class in org.apache.tools.ant.filters">TokenFilter.IgnoreBlank</A>&nbsp;filter)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add an ignoreBlank filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addLineContains(org.apache.tools.ant.filters.LineContains)">addLineContains</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/LineContains.html" title="class in org.apache.tools.ant.filters">LineContains</A>&nbsp;lineContains)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a LineContains filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addLineContainsRegExp(org.apache.tools.ant.filters.LineContainsRegExp)">addLineContainsRegExp</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/LineContainsRegExp.html" title="class in org.apache.tools.ant.filters">LineContainsRegExp</A>&nbsp;lineContainsRegExp)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a LineContainsRegExp filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addPrefixLines(org.apache.tools.ant.filters.PrefixLines)">addPrefixLines</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/PrefixLines.html" title="class in org.apache.tools.ant.filters">PrefixLines</A>&nbsp;prefixLines)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a PrefixLines filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addReplaceRegex(org.apache.tools.ant.filters.TokenFilter.ReplaceRegex)">addReplaceRegex</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/TokenFilter.ReplaceRegex.html" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceRegex</A>&nbsp;filter)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a replaceregex filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addReplaceString(org.apache.tools.ant.filters.TokenFilter.ReplaceString)">addReplaceString</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/TokenFilter.ReplaceString.html" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceString</A>&nbsp;filter)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a replacestring filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addReplaceTokens(org.apache.tools.ant.filters.ReplaceTokens)">addReplaceTokens</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/ReplaceTokens.html" title="class in org.apache.tools.ant.filters">ReplaceTokens</A>&nbsp;replaceTokens)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a ReplaceTokens filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addStripJavaComments(org.apache.tools.ant.filters.StripJavaComments)">addStripJavaComments</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/StripJavaComments.html" title="class in org.apache.tools.ant.filters">StripJavaComments</A>&nbsp;stripJavaComments)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a StripJavaCommands filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addStripLineBreaks(org.apache.tools.ant.filters.StripLineBreaks)">addStripLineBreaks</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/StripLineBreaks.html" title="class in org.apache.tools.ant.filters">StripLineBreaks</A>&nbsp;stripLineBreaks)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a StripLineBreaks filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addStripLineComments(org.apache.tools.ant.filters.StripLineComments)">addStripLineComments</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/StripLineComments.html" title="class in org.apache.tools.ant.filters">StripLineComments</A>&nbsp;stripLineComments)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a StripLineComments filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addTabsToSpaces(org.apache.tools.ant.filters.TabsToSpaces)">addTabsToSpaces</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/TabsToSpaces.html" title="class in org.apache.tools.ant.filters">TabsToSpaces</A>&nbsp;tabsToSpaces)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a TabsToSpaces filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addTailFilter(org.apache.tools.ant.filters.TailFilter)">addTailFilter</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/TailFilter.html" title="class in org.apache.tools.ant.filters">TailFilter</A>&nbsp;tailFilter)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a TailFilter filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addTokenFilter(org.apache.tools.ant.filters.TokenFilter)">addTokenFilter</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/TokenFilter.html" title="class in org.apache.tools.ant.filters">TokenFilter</A>&nbsp;tokenFilter)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a TokenFilter filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#addTrim(org.apache.tools.ant.filters.TokenFilter.Trim)">addTrim</A></B>(<A HREF="../../../../../org/apache/tools/ant/filters/TokenFilter.Trim.html" title="class in org.apache.tools.ant.filters">TokenFilter.Trim</A>&nbsp;filter)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add a trim filter.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;java.util.Vector</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#getFilterReaders()">getFilterReaders</A></B>()</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Return the filters.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
<CODE>&nbsp;void</CODE></FONT></TD>
<TD><CODE><B><A HREF="../../../../../org/apache/tools/ant/types/FilterChain.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</A></B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)</CODE>

<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Makes this instance in effect a reference to another FilterChain
 instance.</TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.types.DataType"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.types.<A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/types/DataType.html#checkAttributesAllowed()">checkAttributesAllowed</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#checkChildrenAllowed()">checkChildrenAllowed</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#circularReference()">circularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#clone()">clone</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference()">dieOnCircularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference(org.apache.tools.ant.Project)">dieOnCircularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#dieOnCircularReference(java.util.Stack, org.apache.tools.ant.Project)">dieOnCircularReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef()">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(java.lang.Class, java.lang.String)">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(java.lang.Class, java.lang.String, org.apache.tools.ant.Project)">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getCheckedRef(org.apache.tools.ant.Project)">getCheckedRef</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getDataTypeName()">getDataTypeName</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#getRefid()">getRefid</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#invokeCircularReferenceCheck(org.apache.tools.ant.types.DataType, java.util.Stack, org.apache.tools.ant.Project)">invokeCircularReferenceCheck</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#isChecked()">isChecked</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#isReference()">isReference</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#noChildrenAllowed()">noChildrenAllowed</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#setChecked(boolean)">setChecked</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#tooManyAttributes()">tooManyAttributes</A>, <A HREF="../../../../../org/apache/tools/ant/types/DataType.html#toString()">toString</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_org.apache.tools.ant.ProjectComponent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class org.apache.tools.ant.<A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant">ProjectComponent</A></B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE><A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getDescription()">getDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getLocation()">getLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#getProject()">getProject</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String)">log</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#log(java.lang.String, int)">log</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setDescription(java.lang.String)">setDescription</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setLocation(org.apache.tools.ant.Location)">setLocation</A>, <A HREF="../../../../../org/apache/tools/ant/ProjectComponent.html#setProject(org.apache.tools.ant.Project)">setProject</A></CODE></TD>
</TR>
</TABLE>
&nbsp;<A NAME="methods_inherited_from_class_java.lang.Object"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left"><B>Methods inherited from class java.lang.Object</B></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><CODE>equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</CODE></TD>
</TR>
</TABLE>
&nbsp;
<P>

<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Constructor Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="FilterChain()"><!-- --></A><H3>
FilterChain</H3>
<PRE>
public <B>FilterChain</B>()</PRE>
<DL>
</DL>

<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Method Detail</B></FONT></TH>
</TR>
</TABLE>

<A NAME="addFilterReader(org.apache.tools.ant.types.AntFilterReader)"><!-- --></A><H3>
addFilterReader</H3>
<PRE>
public void <B>addFilterReader</B>(<A HREF="../../../../../org/apache/tools/ant/types/AntFilterReader.html" title="class in org.apache.tools.ant.types">AntFilterReader</A>&nbsp;filterReader)</PRE>
<DL>
<DD>Add an AntFilterReader filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filterReader</CODE> - an <code>AntFilterReader</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="getFilterReaders()"><!-- --></A><H3>
getFilterReaders</H3>
<PRE>
public java.util.Vector <B>getFilterReaders</B>()</PRE>
<DL>
<DD>Return the filters.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>

<DT><B>Returns:</B><DD>a <code>Vector</code> value containing the filters</DL>
</DD>
</DL>
<HR>

<A NAME="addClassConstants(org.apache.tools.ant.filters.ClassConstants)"><!-- --></A><H3>
addClassConstants</H3>
<PRE>
public void <B>addClassConstants</B>(<A HREF="../../../../../org/apache/tools/ant/filters/ClassConstants.html" title="class in org.apache.tools.ant.filters">ClassConstants</A>&nbsp;classConstants)</PRE>
<DL>
<DD>Add a ClassConstants filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>classConstants</CODE> - a <code>ClassConstants</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="addExpandProperties(org.apache.tools.ant.filters.ExpandProperties)"><!-- --></A><H3>
addExpandProperties</H3>
<PRE>
public void <B>addExpandProperties</B>(<A HREF="../../../../../org/apache/tools/ant/filters/ExpandProperties.html" title="class in org.apache.tools.ant.filters">ExpandProperties</A>&nbsp;expandProperties)</PRE>
<DL>
<DD>Add an ExpandProperties filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>expandProperties</CODE> - an <code>ExpandProperties</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="addHeadFilter(org.apache.tools.ant.filters.HeadFilter)"><!-- --></A><H3>
addHeadFilter</H3>
<PRE>
public void <B>addHeadFilter</B>(<A HREF="../../../../../org/apache/tools/ant/filters/HeadFilter.html" title="class in org.apache.tools.ant.filters">HeadFilter</A>&nbsp;headFilter)</PRE>
<DL>
<DD>Add a HeadFilter filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>headFilter</CODE> - a <code>HeadFilter</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="addLineContains(org.apache.tools.ant.filters.LineContains)"><!-- --></A><H3>
addLineContains</H3>
<PRE>
public void <B>addLineContains</B>(<A HREF="../../../../../org/apache/tools/ant/filters/LineContains.html" title="class in org.apache.tools.ant.filters">LineContains</A>&nbsp;lineContains)</PRE>
<DL>
<DD>Add a LineContains filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>lineContains</CODE> - a <code>LineContains</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="addLineContainsRegExp(org.apache.tools.ant.filters.LineContainsRegExp)"><!-- --></A><H3>
addLineContainsRegExp</H3>
<PRE>
public void <B>addLineContainsRegExp</B>(<A HREF="../../../../../org/apache/tools/ant/filters/LineContainsRegExp.html" title="class in org.apache.tools.ant.filters">LineContainsRegExp</A>&nbsp;lineContainsRegExp)</PRE>
<DL>
<DD>Add a LineContainsRegExp filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>lineContainsRegExp</CODE> - a <code>LineContainsRegExp</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="addPrefixLines(org.apache.tools.ant.filters.PrefixLines)"><!-- --></A><H3>
addPrefixLines</H3>
<PRE>
public void <B>addPrefixLines</B>(<A HREF="../../../../../org/apache/tools/ant/filters/PrefixLines.html" title="class in org.apache.tools.ant.filters">PrefixLines</A>&nbsp;prefixLines)</PRE>
<DL>
<DD>Add a PrefixLines filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>prefixLines</CODE> - a <code>PrefixLines</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="addReplaceTokens(org.apache.tools.ant.filters.ReplaceTokens)"><!-- --></A><H3>
addReplaceTokens</H3>
<PRE>
public void <B>addReplaceTokens</B>(<A HREF="../../../../../org/apache/tools/ant/filters/ReplaceTokens.html" title="class in org.apache.tools.ant.filters">ReplaceTokens</A>&nbsp;replaceTokens)</PRE>
<DL>
<DD>Add a ReplaceTokens filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>replaceTokens</CODE> - a <code>ReplaceTokens</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="addStripJavaComments(org.apache.tools.ant.filters.StripJavaComments)"><!-- --></A><H3>
addStripJavaComments</H3>
<PRE>
public void <B>addStripJavaComments</B>(<A HREF="../../../../../org/apache/tools/ant/filters/StripJavaComments.html" title="class in org.apache.tools.ant.filters">StripJavaComments</A>&nbsp;stripJavaComments)</PRE>
<DL>
<DD>Add a StripJavaCommands filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>stripJavaComments</CODE> - a <code>StripJavaComments</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="addStripLineBreaks(org.apache.tools.ant.filters.StripLineBreaks)"><!-- --></A><H3>
addStripLineBreaks</H3>
<PRE>
public void <B>addStripLineBreaks</B>(<A HREF="../../../../../org/apache/tools/ant/filters/StripLineBreaks.html" title="class in org.apache.tools.ant.filters">StripLineBreaks</A>&nbsp;stripLineBreaks)</PRE>
<DL>
<DD>Add a StripLineBreaks filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>stripLineBreaks</CODE> - a <code>StripLineBreaks</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="addStripLineComments(org.apache.tools.ant.filters.StripLineComments)"><!-- --></A><H3>
addStripLineComments</H3>
<PRE>
public void <B>addStripLineComments</B>(<A HREF="../../../../../org/apache/tools/ant/filters/StripLineComments.html" title="class in org.apache.tools.ant.filters">StripLineComments</A>&nbsp;stripLineComments)</PRE>
<DL>
<DD>Add a StripLineComments filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>stripLineComments</CODE> - a <code>StripLineComments</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="addTabsToSpaces(org.apache.tools.ant.filters.TabsToSpaces)"><!-- --></A><H3>
addTabsToSpaces</H3>
<PRE>
public void <B>addTabsToSpaces</B>(<A HREF="../../../../../org/apache/tools/ant/filters/TabsToSpaces.html" title="class in org.apache.tools.ant.filters">TabsToSpaces</A>&nbsp;tabsToSpaces)</PRE>
<DL>
<DD>Add a TabsToSpaces filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>tabsToSpaces</CODE> - a <code>TabsToSpaces</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="addTailFilter(org.apache.tools.ant.filters.TailFilter)"><!-- --></A><H3>
addTailFilter</H3>
<PRE>
public void <B>addTailFilter</B>(<A HREF="../../../../../org/apache/tools/ant/filters/TailFilter.html" title="class in org.apache.tools.ant.filters">TailFilter</A>&nbsp;tailFilter)</PRE>
<DL>
<DD>Add a TailFilter filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>tailFilter</CODE> - a <code>TailFilter</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="addEscapeUnicode(org.apache.tools.ant.filters.EscapeUnicode)"><!-- --></A><H3>
addEscapeUnicode</H3>
<PRE>
public void <B>addEscapeUnicode</B>(<A HREF="../../../../../org/apache/tools/ant/filters/EscapeUnicode.html" title="class in org.apache.tools.ant.filters">EscapeUnicode</A>&nbsp;escapeUnicode)</PRE>
<DL>
<DD>Add an EscapeUnicode filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>escapeUnicode</CODE> - an <code>EscapeUnicode</code> value<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addTokenFilter(org.apache.tools.ant.filters.TokenFilter)"><!-- --></A><H3>
addTokenFilter</H3>
<PRE>
public void <B>addTokenFilter</B>(<A HREF="../../../../../org/apache/tools/ant/filters/TokenFilter.html" title="class in org.apache.tools.ant.filters">TokenFilter</A>&nbsp;tokenFilter)</PRE>
<DL>
<DD>Add a TokenFilter filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>tokenFilter</CODE> - a <code>TokenFilter</code> value<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addDeleteCharacters(org.apache.tools.ant.filters.TokenFilter.DeleteCharacters)"><!-- --></A><H3>
addDeleteCharacters</H3>
<PRE>
public void <B>addDeleteCharacters</B>(<A HREF="../../../../../org/apache/tools/ant/filters/TokenFilter.DeleteCharacters.html" title="class in org.apache.tools.ant.filters">TokenFilter.DeleteCharacters</A>&nbsp;filter)</PRE>
<DL>
<DD>Add a delete characters filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filter</CODE> - a <code>TokenFilter.DeleteCharacters</code> value<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addContainsRegex(org.apache.tools.ant.filters.TokenFilter.ContainsRegex)"><!-- --></A><H3>
addContainsRegex</H3>
<PRE>
public void <B>addContainsRegex</B>(<A HREF="../../../../../org/apache/tools/ant/filters/TokenFilter.ContainsRegex.html" title="class in org.apache.tools.ant.filters">TokenFilter.ContainsRegex</A>&nbsp;filter)</PRE>
<DL>
<DD>Add a containsregex filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filter</CODE> - a <code>TokenFilter.ContainsRegex</code> value<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addReplaceRegex(org.apache.tools.ant.filters.TokenFilter.ReplaceRegex)"><!-- --></A><H3>
addReplaceRegex</H3>
<PRE>
public void <B>addReplaceRegex</B>(<A HREF="../../../../../org/apache/tools/ant/filters/TokenFilter.ReplaceRegex.html" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceRegex</A>&nbsp;filter)</PRE>
<DL>
<DD>Add a replaceregex filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filter</CODE> - a <code>TokenFilter.ReplaceRegex</code> value</DL>
</DD>
</DL>
<HR>

<A NAME="addTrim(org.apache.tools.ant.filters.TokenFilter.Trim)"><!-- --></A><H3>
addTrim</H3>
<PRE>
public void <B>addTrim</B>(<A HREF="../../../../../org/apache/tools/ant/filters/TokenFilter.Trim.html" title="class in org.apache.tools.ant.filters">TokenFilter.Trim</A>&nbsp;filter)</PRE>
<DL>
<DD>Add a trim filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filter</CODE> - a <code>TokenFilter.Trim</code> value<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addReplaceString(org.apache.tools.ant.filters.TokenFilter.ReplaceString)"><!-- --></A><H3>
addReplaceString</H3>
<PRE>
public void <B>addReplaceString</B>(<A HREF="../../../../../org/apache/tools/ant/filters/TokenFilter.ReplaceString.html" title="class in org.apache.tools.ant.filters">TokenFilter.ReplaceString</A>&nbsp;filter)</PRE>
<DL>
<DD>Add a replacestring filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filter</CODE> - a <code>TokenFilter.ReplaceString</code> value<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="addIgnoreBlank(org.apache.tools.ant.filters.TokenFilter.IgnoreBlank)"><!-- --></A><H3>
addIgnoreBlank</H3>
<PRE>
public void <B>addIgnoreBlank</B>(<A HREF="../../../../../org/apache/tools/ant/filters/TokenFilter.IgnoreBlank.html" title="class in org.apache.tools.ant.filters">TokenFilter.IgnoreBlank</A>&nbsp;filter)</PRE>
<DL>
<DD>Add an ignoreBlank filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filter</CODE> - a <code>TokenFilter.IgnoreBlank</code> value<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<HR>

<A NAME="setRefid(org.apache.tools.ant.types.Reference)"><!-- --></A><H3>
setRefid</H3>
<PRE>
public void <B>setRefid</B>(<A HREF="../../../../../org/apache/tools/ant/types/Reference.html" title="class in org.apache.tools.ant.types">Reference</A>&nbsp;r)
              throws <A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></PRE>
<DL>
<DD>Makes this instance in effect a reference to another FilterChain
 instance.

 <p>You must not set another attribute or nest elements inside
 this element if you make it a reference.</p>
<P>
<DD><DL>
<DT><B>Overrides:</B><DD><CODE><A HREF="../../../../../org/apache/tools/ant/types/DataType.html#setRefid(org.apache.tools.ant.types.Reference)">setRefid</A></CODE> in class <CODE><A HREF="../../../../../org/apache/tools/ant/types/DataType.html" title="class in org.apache.tools.ant.types">DataType</A></CODE></DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>r</CODE> - the reference to which this instance is associated
<DT><B>Throws:</B>
<DD><CODE><A HREF="../../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A></CODE> - if this instance already has been configured.</DL>
</DD>
</DL>
<HR>

<A NAME="add(org.apache.tools.ant.filters.ChainableReader)"><!-- --></A><H3>
add</H3>
<PRE>
public void <B>add</B>(<A HREF="../../../../../org/apache/tools/ant/filters/ChainableReader.html" title="interface in org.apache.tools.ant.filters">ChainableReader</A>&nbsp;filter)</PRE>
<DL>
<DD>Add a chainfilter filter.
<P>
<DD><DL>
</DL>
</DD>
<DD><DL>
<DT><B>Parameters:</B><DD><CODE>filter</CODE> - a <code>ChainableReader</code> value<DT><B>Since:</B></DT>
  <DD>Ant 1.6</DD>
</DL>
</DD>
</DL>
<!-- ========= END OF CLASS DATA ========= -->
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/FileSet.html" title="class in org.apache.tools.ant.types"><B>PREV CLASS</B></A>&nbsp;
&nbsp;<A HREF="../../../../../org/apache/tools/ant/types/FilterSet.html" title="class in org.apache.tools.ant.types"><B>NEXT CLASS</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?org/apache/tools/ant/types/FilterChain.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="FilterChain.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
  SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;<A HREF="#fields_inherited_from_class_org.apache.tools.ant.types.DataType">FIELD</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
