<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:35 EST 2006 -->
<TITLE>
org.apache.tools.ant Class Hierarchy (Apache Ant API)
</TITLE>


<LINK REL ="stylesheet" TYPE="text/css" HREF="../../../../stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="org.apache.tools.ant Class Hierarchy (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Tree</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/dispatch/package-tree.html"><B>NEXT</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/package-tree.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="package-tree.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<CENTER>
<H2>
Hierarchy For Package org.apache.tools.ant
</H2>
</CENTER>
<DL>
<DT><B>Package Hierarchies:</B><DD><A HREF="../../../../overview-tree.html">All Packages</A></DL>
<HR>
<H2>
Class Hierarchy
</H2>
<UL>
<LI TYPE="circle">java.lang.Object<UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/AntTypeDefinition.html" title="class in org.apache.tools.ant"><B>AntTypeDefinition</B></A><LI TYPE="circle">java.lang.ClassLoader<UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/AntClassLoader.html" title="class in org.apache.tools.ant"><B>AntClassLoader</B></A> (implements org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant">SubBuildListener</A>)
</UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/ComponentHelper.html" title="class in org.apache.tools.ant"><B>ComponentHelper</B></A><LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DefaultLogger.html" title="class in org.apache.tools.ant"><B>DefaultLogger</B></A> (implements org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A>)
<UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/NoBannerLogger.html" title="class in org.apache.tools.ant"><B>NoBannerLogger</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/Diagnostics.html" title="class in org.apache.tools.ant"><B>Diagnostics</B></A><LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DirectoryScanner.html" title="class in org.apache.tools.ant"><B>DirectoryScanner</B></A> (implements org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant">FileScanner</A>, org.apache.tools.ant.types.<A HREF="../../../../org/apache/tools/ant/types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</A>, org.apache.tools.ant.types.selectors.<A HREF="../../../../org/apache/tools/ant/types/selectors/SelectorScanner.html" title="interface in org.apache.tools.ant.types.selectors">SelectorScanner</A>)
<LI TYPE="circle">java.util.EventObject (implements java.io.Serializable)
<UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant"><B>BuildEvent</B></A></UL>
<LI TYPE="circle">java.io.InputStream (implements java.io.Closeable)
<UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DemuxInputStream.html" title="class in org.apache.tools.ant"><B>DemuxInputStream</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.html" title="class in org.apache.tools.ant"><B>IntrospectionHelper</B></A><LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/IntrospectionHelper.Creator.html" title="class in org.apache.tools.ant"><B>IntrospectionHelper.Creator</B></A><LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant"><B>Location</B></A> (implements java.io.Serializable)
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/MagicNames.html" title="class in org.apache.tools.ant"><B>MagicNames</B></A><LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/Main.html" title="class in org.apache.tools.ant"><B>Main</B></A> (implements org.apache.tools.ant.launch.<A HREF="../../../../org/apache/tools/ant/launch/AntMain.html" title="interface in org.apache.tools.ant.launch">AntMain</A>)
<LI TYPE="circle">java.io.OutputStream (implements java.io.Closeable, java.io.Flushable)
<UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DemuxOutputStream.html" title="class in org.apache.tools.ant"><B>DemuxOutputStream</B></A></UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/PathTokenizer.html" title="class in org.apache.tools.ant"><B>PathTokenizer</B></A><LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant"><B>Project</B></A> (implements org.apache.tools.ant.types.<A HREF="../../../../org/apache/tools/ant/types/ResourceFactory.html" title="interface in org.apache.tools.ant.types">ResourceFactory</A>)
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/ProjectComponent.html" title="class in org.apache.tools.ant"><B>ProjectComponent</B></A> (implements java.lang.Cloneable)
<UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant"><B>Task</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/TaskAdapter.html" title="class in org.apache.tools.ant"><B>TaskAdapter</B></A> (implements org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/TypeAdapter.html" title="interface in org.apache.tools.ant">TypeAdapter</A>)
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/UnknownElement.html" title="class in org.apache.tools.ant"><B>UnknownElement</B></A></UL>
</UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/ProjectHelper.html" title="class in org.apache.tools.ant"><B>ProjectHelper</B></A><LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/PropertyHelper.html" title="class in org.apache.tools.ant"><B>PropertyHelper</B></A><LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant"><B>RuntimeConfigurable</B></A> (implements java.io.Serializable)
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant"><B>Target</B></A> (implements org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant">TaskContainer</A>)
<LI TYPE="circle">java.lang.Throwable (implements java.io.Serializable)
<UL>
<LI TYPE="circle">java.lang.Exception<UL>
<LI TYPE="circle">java.lang.RuntimeException<UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant"><B>BuildException</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/ExitStatusException.html" title="class in org.apache.tools.ant"><B>ExitStatusException</B></A><LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/UnsupportedAttributeException.html" title="class in org.apache.tools.ant"><B>UnsupportedAttributeException</B></A><LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/UnsupportedElementException.html" title="class in org.apache.tools.ant"><B>UnsupportedElementException</B></A></UL>
<LI TYPE="circle">java.lang.SecurityException<UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/ExitException.html" title="class in org.apache.tools.ant"><B>ExitException</B></A></UL>
</UL>
</UL>
</UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/XmlLogger.html" title="class in org.apache.tools.ant"><B>XmlLogger</B></A> (implements org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant">BuildLogger</A>)
</UL>
</UL>
<H2>
Interface Hierarchy
</H2>
<UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DynamicAttribute.html" title="interface in org.apache.tools.ant"><B>DynamicAttribute</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DynamicConfigurator.html" title="interface in org.apache.tools.ant"><B>DynamicConfigurator</B></A> (also extends org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DynamicElement.html" title="interface in org.apache.tools.ant">DynamicElement</A>)
</UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DynamicAttributeNS.html" title="interface in org.apache.tools.ant"><B>DynamicAttributeNS</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DynamicConfiguratorNS.html" title="interface in org.apache.tools.ant"><B>DynamicConfiguratorNS</B></A> (also extends org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DynamicElementNS.html" title="interface in org.apache.tools.ant">DynamicElementNS</A>)
</UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DynamicElement.html" title="interface in org.apache.tools.ant"><B>DynamicElement</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DynamicConfigurator.html" title="interface in org.apache.tools.ant"><B>DynamicConfigurator</B></A> (also extends org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DynamicAttribute.html" title="interface in org.apache.tools.ant">DynamicAttribute</A>)
</UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DynamicElementNS.html" title="interface in org.apache.tools.ant"><B>DynamicElementNS</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DynamicConfiguratorNS.html" title="interface in org.apache.tools.ant"><B>DynamicConfiguratorNS</B></A> (also extends org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/DynamicAttributeNS.html" title="interface in org.apache.tools.ant">DynamicAttributeNS</A>)
</UL>
<LI TYPE="circle">java.util.EventListener<UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/BuildListener.html" title="interface in org.apache.tools.ant"><B>BuildListener</B></A><UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/BuildLogger.html" title="interface in org.apache.tools.ant"><B>BuildLogger</B></A><LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/SubBuildListener.html" title="interface in org.apache.tools.ant"><B>SubBuildListener</B></A></UL>
</UL>
<LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/Executor.html" title="interface in org.apache.tools.ant"><B>Executor</B></A><LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/FileScanner.html" title="interface in org.apache.tools.ant"><B>FileScanner</B></A><LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/TaskContainer.html" title="interface in org.apache.tools.ant"><B>TaskContainer</B></A><LI TYPE="circle">org.apache.tools.ant.<A HREF="../../../../org/apache/tools/ant/TypeAdapter.html" title="interface in org.apache.tools.ant"><B>TypeAdapter</B></A></UL>
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Tree</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV&nbsp;
&nbsp;<A HREF="../../../../org/apache/tools/ant/dispatch/package-tree.html"><B>NEXT</B></A></FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../index.html?org/apache/tools/ant/package-tree.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="package-tree.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="../../../../allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
