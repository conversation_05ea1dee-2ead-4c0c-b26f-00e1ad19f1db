<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:35 EST 2006 -->
<TITLE>
Serialized Form (Apache Ant API)
</TITLE>


<LINK REL ="stylesheet" TYPE="text/css" HREF="stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Serialized Form (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Package</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="overview-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV&nbsp;
&nbsp;NEXT</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="index.html?serialized-form.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="serialized-form.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<CENTER>
<H1>
Serialized Form</H1>
</CENTER>
<HR SIZE="4" NOSHADE>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="center"><FONT SIZE="+2">
<B>Package</B> <B>org.apache.tools.ant</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="org.apache.tools.ant.BuildEvent"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/ant/BuildEvent.html" title="class in org.apache.tools.ant">org.apache.tools.ant.BuildEvent</A> extends java.util.EventObject implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="serializedForm"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Serialized Fields</B></FONT></TH>
</TR>
</TABLE>

<H3>
project</H3>
<PRE>
<A HREF="org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A> <B>project</B></PRE>
<DL>
<DD>Project which emitted the event.
<P>
<DL>
</DL>
</DL>
<HR>
<H3>
target</H3>
<PRE>
<A HREF="org/apache/tools/ant/Target.html" title="class in org.apache.tools.ant">Target</A> <B>target</B></PRE>
<DL>
<DD>Target which emitted the event, if specified.
<P>
<DL>
</DL>
</DL>
<HR>
<H3>
task</H3>
<PRE>
<A HREF="org/apache/tools/ant/Task.html" title="class in org.apache.tools.ant">Task</A> <B>task</B></PRE>
<DL>
<DD>Task which emitted the event, if specified.
<P>
<DL>
</DL>
</DL>
<HR>
<H3>
message</H3>
<PRE>
java.lang.String <B>message</B></PRE>
<DL>
<DD>Message associated with the event. This is only used for
 "messageLogged" events.
<P>
<DL>
</DL>
</DL>
<HR>
<H3>
priority</H3>
<PRE>
int <B>priority</B></PRE>
<DL>
<DD>The priority of the message, for "messageLogged" events.
<P>
<DL>
</DL>
</DL>
<HR>
<H3>
exception</H3>
<PRE>
java.lang.Throwable <B>exception</B></PRE>
<DL>
<DD>The exception associated with this event, if any.
 This is only used for "messageLogged", "taskFinished", "targetFinished",
 and "buildFinished" events.
<P>
<DL>
</DL>
</DL>

<P>
<A NAME="org.apache.tools.ant.BuildException"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">org.apache.tools.ant.BuildException</A> extends java.lang.RuntimeException implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="serializedForm"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Serialized Fields</B></FONT></TH>
</TR>
</TABLE>

<H3>
cause</H3>
<PRE>
java.lang.Throwable <B>cause</B></PRE>
<DL>
<DD>Exception that might have caused this one.
<P>
<DL>
</DL>
</DL>
<HR>
<H3>
location</H3>
<PRE>
<A HREF="org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">Location</A> <B>location</B></PRE>
<DL>
<DD>Location in the build file where the exception occurred
<P>
<DL>
</DL>
</DL>

<P>
<A NAME="org.apache.tools.ant.ExitException"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/ant/ExitException.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ExitException</A> extends java.lang.SecurityException implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="serializedForm"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Serialized Fields</B></FONT></TH>
</TR>
</TABLE>

<H3>
status</H3>
<PRE>
int <B>status</B></PRE>
<DL>
<DD>Status code
<P>
<DL>
</DL>
</DL>

<P>
<A NAME="org.apache.tools.ant.ExitStatusException"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/ant/ExitStatusException.html" title="class in org.apache.tools.ant">org.apache.tools.ant.ExitStatusException</A> extends <A HREF="org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A> implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="serializedForm"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Serialized Fields</B></FONT></TH>
</TR>
</TABLE>

<H3>
status</H3>
<PRE>
int <B>status</B></PRE>
<DL>
<DD>Status code
<P>
<DL>
</DL>
</DL>

<P>
<A NAME="org.apache.tools.ant.Location"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/ant/Location.html" title="class in org.apache.tools.ant">org.apache.tools.ant.Location</A> extends java.lang.Object implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="serializedForm"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Serialized Fields</B></FONT></TH>
</TR>
</TABLE>

<H3>
fileName</H3>
<PRE>
java.lang.String <B>fileName</B></PRE>
<DL>
<DD>Name of the file.
<P>
<DL>
</DL>
</DL>
<HR>
<H3>
lineNumber</H3>
<PRE>
int <B>lineNumber</B></PRE>
<DL>
<DD>Line number within the file.
<P>
<DL>
</DL>
</DL>
<HR>
<H3>
columnNumber</H3>
<PRE>
int <B>columnNumber</B></PRE>
<DL>
<DD>Column number within the file.
<P>
<DL>
</DL>
</DL>

<P>
<A NAME="org.apache.tools.ant.RuntimeConfigurable"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/ant/RuntimeConfigurable.html" title="class in org.apache.tools.ant">org.apache.tools.ant.RuntimeConfigurable</A> extends java.lang.Object implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="serializedForm"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Serialized Fields</B></FONT></TH>
</TR>
</TABLE>

<H3>
elementTag</H3>
<PRE>
java.lang.String <B>elementTag</B></PRE>
<DL>
<DD>Name of the element to configure.
<P>
<DL>
</DL>
</DL>
<HR>
<H3>
children</H3>
<PRE>
java.util.List&lt;E&gt; <B>children</B></PRE>
<DL>
<DD>List of child element wrappers.
<P>
<DL>
</DL>
</DL>
<HR>
<H3>
attributeNames</H3>
<PRE>
java.util.List&lt;E&gt; <B>attributeNames</B></PRE>
<DL>
<DD>Attribute names and values. While the XML spec doesn't require
  preserving the order ( AFAIK ), some ant tests do rely on the
  exact order. The following code is copied from AttributeImpl.
  We could also just use SAX2 Attributes and convert to SAX1 ( DOM
  attribute Nodes can also be stored in SAX2 Attributes )
  XXX under JDK 1.4 you can just use a LinkedHashMap for this purpose -jglick
 The only exception to this order is the treatment of
 refid. A number of datatypes check if refid is set
 when other attributes are set. This check will not
 work if the build script has the other attribute before
 the "refid" attribute, so now (ANT 1.7) the refid
 attribute will be processed first.
<P>
<DL>
</DL>
</DL>
<HR>
<H3>
attributeMap</H3>
<PRE>
java.util.Map&lt;K,V&gt; <B>attributeMap</B></PRE>
<DL>
<DD>Map of attribute names to values
<P>
<DL>
</DL>
</DL>
<HR>
<H3>
characters</H3>
<PRE>
java.lang.StringBuffer <B>characters</B></PRE>
<DL>
<DD>Text appearing within the element.
<P>
<DL>
</DL>
</DL>
<HR>
<H3>
proxyConfigured</H3>
<PRE>
boolean <B>proxyConfigured</B></PRE>
<DL>
<DD>Indicates if the wrapped object has been configured
<P>
<DL>
</DL>
</DL>
<HR>
<H3>
polyType</H3>
<PRE>
java.lang.String <B>polyType</B></PRE>
<DL>
<DD>the polymorphic type
<P>
<DL>
</DL>
</DL>
<HR>
<H3>
id</H3>
<PRE>
java.lang.String <B>id</B></PRE>
<DL>
<DD>the "id" of this Element if it has one
<P>
<DL>
</DL>
</DL>

<P>
<A NAME="org.apache.tools.ant.UnsupportedAttributeException"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/ant/UnsupportedAttributeException.html" title="class in org.apache.tools.ant">org.apache.tools.ant.UnsupportedAttributeException</A> extends <A HREF="org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A> implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="serializedForm"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Serialized Fields</B></FONT></TH>
</TR>
</TABLE>

<H3>
attribute</H3>
<PRE>
java.lang.String <B>attribute</B></PRE>
<DL>
<DL>
</DL>
</DL>

<P>
<A NAME="org.apache.tools.ant.UnsupportedElementException"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/ant/UnsupportedElementException.html" title="class in org.apache.tools.ant">org.apache.tools.ant.UnsupportedElementException</A> extends <A HREF="org/apache/tools/ant/BuildException.html" title="class in org.apache.tools.ant">BuildException</A> implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="serializedForm"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Serialized Fields</B></FONT></TH>
</TR>
</TABLE>

<H3>
element</H3>
<PRE>
java.lang.String <B>element</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR SIZE="4" NOSHADE>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="center"><FONT SIZE="+2">
<B>Package</B> <B>org.apache.tools.ant.launch</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="org.apache.tools.ant.launch.LaunchException"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/ant/launch/LaunchException.html" title="class in org.apache.tools.ant.launch">org.apache.tools.ant.launch.LaunchException</A> extends java.lang.Exception implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>
<HR SIZE="4" NOSHADE>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="center"><FONT SIZE="+2">
<B>Package</B> <B>org.apache.tools.ant.taskdefs</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="org.apache.tools.ant.taskdefs.ManifestException"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/ant/taskdefs/ManifestException.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.ManifestException</A> extends java.lang.Exception implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>
<HR SIZE="4" NOSHADE>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="center"><FONT SIZE="+2">
<B>Package</B> <B>org.apache.tools.ant.taskdefs.optional.ejb</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.IPlanetEjbc.EjbcException"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/ant/taskdefs/optional/ejb/IPlanetEjbc.EjbcException.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">org.apache.tools.ant.taskdefs.optional.ejb.IPlanetEjbc.EjbcException</A> extends java.lang.Exception implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>
<HR SIZE="4" NOSHADE>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="center"><FONT SIZE="+2">
<B>Package</B> <B>org.apache.tools.ant.taskdefs.optional.junit</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.DOMUtil.NodeListImpl"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/ant/taskdefs/optional/junit/DOMUtil.NodeListImpl.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">org.apache.tools.ant.taskdefs.optional.junit.DOMUtil.NodeListImpl</A> extends java.util.Vector implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>
<HR SIZE="4" NOSHADE>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="center"><FONT SIZE="+2">
<B>Package</B> <B>org.apache.tools.ant.taskdefs.optional.starteam</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="org.apache.tools.ant.taskdefs.optional.starteam.TreeBasedTask.UnmatchedFileMap"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.UnmatchedFileMap.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam">org.apache.tools.ant.taskdefs.optional.starteam.TreeBasedTask.UnmatchedFileMap</A> extends java.util.Hashtable implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>
<HR SIZE="4" NOSHADE>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="center"><FONT SIZE="+2">
<B>Package</B> <B>org.apache.tools.ant.types.resources</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="org.apache.tools.ant.types.resources.ImmutableResourceException"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/ant/types/resources/ImmutableResourceException.html" title="class in org.apache.tools.ant.types.resources">org.apache.tools.ant.types.resources.ImmutableResourceException</A> extends java.io.IOException implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>
<HR SIZE="4" NOSHADE>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="center"><FONT SIZE="+2">
<B>Package</B> <B>org.apache.tools.ant.util</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="org.apache.tools.ant.util.IdentityStack"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/ant/util/IdentityStack.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.IdentityStack</A> extends java.util.Stack implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>

<P>
<A NAME="org.apache.tools.ant.util.LazyHashtable"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/ant/util/LazyHashtable.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.LazyHashtable</A> extends java.util.Hashtable implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="serializedForm"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
<B>Serialized Fields</B></FONT></TH>
</TR>
</TABLE>

<H3>
initAllDone</H3>
<PRE>
boolean <B>initAllDone</B></PRE>
<DL>
<DL>
</DL>
</DL>
<HR SIZE="4" NOSHADE>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="center"><FONT SIZE="+2">
<B>Package</B> <B>org.apache.tools.mail</B></FONT></TH>
</TR>
</TABLE>

<P>
<A NAME="org.apache.tools.mail.ErrorInQuitException"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Class <A HREF="org/apache/tools/mail/ErrorInQuitException.html" title="class in org.apache.tools.mail">org.apache.tools.mail.ErrorInQuitException</A> extends java.io.IOException implements Serializable</B></FONT></TH>
</TR>
</TABLE>

<P>

<P>
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Package</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="overview-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV&nbsp;
&nbsp;NEXT</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="index.html?serialized-form.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="serialized-form.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
