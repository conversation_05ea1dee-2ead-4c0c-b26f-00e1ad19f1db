<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:35 EST 2006 -->
<TITLE>
Overview (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="Overview, Apache Ant">

<LINK REL ="stylesheet" TYPE="text/css" HREF="stylesheet.css" TITLE="Style">


</HEAD>

<BODY BGCOLOR="white">

<TABLE BORDER="0" WIDTH="100%" SUMMARY="">
<TR>
<TH ALIGN="left" NOWRAP><FONT size="+1" CLASS="FrameTitleFont">
<B></B></FONT></TH>
</TR>
</TABLE>

<TABLE BORDER="0" WIDTH="100%" SUMMARY="">
<TR>
<TD NOWRAP><FONT CLASS="FrameItemFont"><A HREF="allclasses-frame.html" target="packageFrame">All Classes</A></FONT>
<P>
<FONT size="+1" CLASS="FrameHeadingFont">
Packages</FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/package-frame.html" target="packageFrame">org.apache.tools.ant</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/dispatch/package-frame.html" target="packageFrame">org.apache.tools.ant.dispatch</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/filters/package-frame.html" target="packageFrame">org.apache.tools.ant.filters</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/filters/util/package-frame.html" target="packageFrame">org.apache.tools.ant.filters.util</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/helper/package-frame.html" target="packageFrame">org.apache.tools.ant.helper</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/input/package-frame.html" target="packageFrame">org.apache.tools.ant.input</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/launch/package-frame.html" target="packageFrame">org.apache.tools.ant.launch</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/listener/package-frame.html" target="packageFrame">org.apache.tools.ant.listener</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/loader/package-frame.html" target="packageFrame">org.apache.tools.ant.loader</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/compilers/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.compilers</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/condition/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.condition</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/cvslib/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.cvslib</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/email/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.email</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.ccm</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.clearcase</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/depend/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.depend</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/depend/constantpool/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.depend.constantpool</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.dotnet</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.ejb</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/extension/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.extension</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/extension/resolvers/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.extension.resolvers</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/i18n/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.i18n</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/image/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.image</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/j2ee/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.j2ee</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/javacc/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.javacc</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/javah/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.javah</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/jdepend/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.jdepend</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/jlink/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.jlink</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/jsp/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.jsp</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/jsp/compilers/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.jsp.compilers</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/junit/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.junit</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/native2ascii/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.native2ascii</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/net/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.net</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/perforce/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.perforce</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/pvcs/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.pvcs</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/scm/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.scm</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/script/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.script</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/sos/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.sos</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/sound/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.sound</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/splash/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.splash</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/ssh/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.ssh</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/starteam/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.starteam</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/unix/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.unix</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/vss/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.vss</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/optional/windows/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.optional.windows</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/taskdefs/rmic/package-frame.html" target="packageFrame">org.apache.tools.ant.taskdefs.rmic</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/types/package-frame.html" target="packageFrame">org.apache.tools.ant.types</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/types/mappers/package-frame.html" target="packageFrame">org.apache.tools.ant.types.mappers</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/types/optional/package-frame.html" target="packageFrame">org.apache.tools.ant.types.optional</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/types/optional/depend/package-frame.html" target="packageFrame">org.apache.tools.ant.types.optional.depend</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/types/optional/image/package-frame.html" target="packageFrame">org.apache.tools.ant.types.optional.image</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/types/resolver/package-frame.html" target="packageFrame">org.apache.tools.ant.types.resolver</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/types/resources/package-frame.html" target="packageFrame">org.apache.tools.ant.types.resources</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/types/resources/comparators/package-frame.html" target="packageFrame">org.apache.tools.ant.types.resources.comparators</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/types/resources/selectors/package-frame.html" target="packageFrame">org.apache.tools.ant.types.resources.selectors</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/types/selectors/package-frame.html" target="packageFrame">org.apache.tools.ant.types.selectors</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/types/selectors/modifiedselector/package-frame.html" target="packageFrame">org.apache.tools.ant.types.selectors.modifiedselector</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/types/spi/package-frame.html" target="packageFrame">org.apache.tools.ant.types.spi</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/util/package-frame.html" target="packageFrame">org.apache.tools.ant.util</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/util/depend/package-frame.html" target="packageFrame">org.apache.tools.ant.util.depend</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/util/depend/bcel/package-frame.html" target="packageFrame">org.apache.tools.ant.util.depend.bcel</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/util/facade/package-frame.html" target="packageFrame">org.apache.tools.ant.util.facade</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/util/java15/package-frame.html" target="packageFrame">org.apache.tools.ant.util.java15</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/util/optional/package-frame.html" target="packageFrame">org.apache.tools.ant.util.optional</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/ant/util/regexp/package-frame.html" target="packageFrame">org.apache.tools.ant.util.regexp</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/bzip2/package-frame.html" target="packageFrame">org.apache.tools.bzip2</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/mail/package-frame.html" target="packageFrame">org.apache.tools.mail</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/tar/package-frame.html" target="packageFrame">org.apache.tools.tar</A></FONT>
<BR>
<FONT CLASS="FrameItemFont"><A HREF="org/apache/tools/zip/package-frame.html" target="packageFrame">org.apache.tools.zip</A></FONT>
<BR>
</TD>
</TR>
</TABLE>

<P>
&nbsp;
</BODY>
</HTML>
