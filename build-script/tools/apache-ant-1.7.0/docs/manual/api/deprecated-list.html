<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:38 EST 2006 -->
<TITLE>
Deprecated List (Apache Ant API)
</TITLE>


<LINK REL ="stylesheet" TYPE="text/css" HREF="stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Deprecated List (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Package</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="overview-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Deprecated</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV&nbsp;
&nbsp;NEXT</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="index.html?deprecated-list.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="deprecated-list.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<CENTER>
<H2>
<B>Deprecated API</B></H2>
</CENTER>
<HR SIZE="4" NOSHADE>
<B>Contents</B><UL>
<LI><A HREF="#class">Deprecated Classes</A>
<LI><A HREF="#field">Deprecated Fields</A>
<LI><A HREF="#method">Deprecated Methods</A>
<LI><A HREF="#constructor">Deprecated Constructors</A>
</UL>

<A NAME="class"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Deprecated Classes</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/loader/AntClassLoader2.html" title="class in org.apache.tools.ant.loader">org.apache.tools.ant.loader.AntClassLoader2</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7
             Just use <A HREF="org/apache/tools/ant/AntClassLoader.html" title="class in org.apache.tools.ant"><CODE>AntClassLoader</CODE></A> itself.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Copydir.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Copydir</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>The copydir task is deprecated since Ant 1.2.  Use copy instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Copyfile.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Copyfile</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>The copyfile task is deprecated since Ant 1.2.  Use
 copy instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Deltree.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Deltree</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>The deltree task is deprecated since Ant 1.2.  Use
 delete instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Exec.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Exec</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.2.
             delegate to <A HREF="org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs"><CODE>Execute</CODE></A>
             instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/FixCRLF.OneLiner.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.FixCRLF.OneLiner</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.0.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Jikes.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Jikes</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.2.
             Merged into the class Javac.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/JikesOutputParser.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.JikesOutputParser</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.2.
             Use Jikes' exit value to detect compilation failure.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/KeySubst.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.KeySubst</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>KeySubst is deprecated since Ant 1.1. Use Filter + Copy
 instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/optional/net/MimeMail.html" title="class in org.apache.tools.ant.taskdefs.optional.net">org.apache.tools.ant.taskdefs.optional.net.MimeMail</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             Use <A HREF="org/apache/tools/ant/taskdefs/email/EmailTask.html" title="class in org.apache.tools.ant.taskdefs.email"><CODE>EmailTask</CODE></A> instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Rename.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.Rename</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>The rename task is deprecated since Ant 1.2.  Use move instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/optional/RenameExtensions.html" title="class in org.apache.tools.ant.taskdefs.optional">org.apache.tools.ant.taskdefs.optional.RenameExtensions</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use &lt;move&gt; instead</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/util/ScriptRunner.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.ScriptRunner</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>Implementation moved to another location. Use
             that org.apache.tools.ant.types.optional.ScriptRunner instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/optional/StyleBook.html" title="class in org.apache.tools.ant.taskdefs.optional">org.apache.tools.ant.taskdefs.optional.StyleBook</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.
             This task is considered unsupported by the Ant developers</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/TaskOutputStream.html" title="class in org.apache.tools.ant.taskdefs">org.apache.tools.ant.taskdefs.TaskOutputStream</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.2.x.
 Use LogOutputStream instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/util/WeakishReference.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.WeakishReference</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>deprecated 1.7; will be removed in Ant1.8
             Just use <CODE>WeakReference</CODE> directly.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/util/WeakishReference.HardReference.html" title="class in org.apache.tools.ant.util">org.apache.tools.ant.util.WeakishReference.HardReference</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.
             Hopefully nobody is using this.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/util/optional/WeakishReference12.html" title="class in org.apache.tools.ant.util.optional">org.apache.tools.ant.util.optional.WeakishReference12</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.
             Just use <CODE>WeakReference</CODE> directly.
 Note that in ant1.7 is parent was changed to extend HardReference.
 This is because the latter has access to the (package scoped)
 WeakishReference(Object) constructor, and both that and this are thin
 facades on the underlying no-longer-abstract base class.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/optional/junit/Xalan2Executor.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">org.apache.tools.ant.taskdefs.optional.junit.Xalan2Executor</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since Ant 1.7</I>&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<P>
<A NAME="field"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Deprecated Fields</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/types/DataType.html#checked">org.apache.tools.ant.types.DataType.checked</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.
             The Employee should not be directly referencing
             variable. Please use <A HREF="org/apache/tools/ant/types/DataType.html#setChecked(boolean)"><CODE>DataType.setChecked(boolean)</CODE></A> or
             <A HREF="org/apache/tools/ant/types/DataType.html#isChecked()"><CODE>DataType.isChecked()</CODE></A> instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/DirectoryScanner.html#DEFAULTEXCLUDES">org.apache.tools.ant.DirectoryScanner.DEFAULTEXCLUDES</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             Use the <A HREF="org/apache/tools/ant/DirectoryScanner.html#getDefaultExcludes()"><CODE>getDefaultExcludes</CODE></A>
             method instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/ProjectComponent.html#description">org.apache.tools.ant.ProjectComponent.description</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Tar.html#FAIL">org.apache.tools.ant.taskdefs.Tar.FAIL</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Tar.FAIL is deprecated and is replaced with
             Tar.TarLongFileMode.FAIL</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Tar.html#GNU">org.apache.tools.ant.taskdefs.Tar.GNU</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Tar.GNU is deprecated and is replaced with
             Tar.TarLongFileMode.GNU</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#JAVA_1_0">org.apache.tools.ant.Project.JAVA_1_0</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use <A HREF="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_0"><CODE>JavaEnvUtils.JAVA_1_0</CODE></A> instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#JAVA_1_1">org.apache.tools.ant.Project.JAVA_1_1</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use <A HREF="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_1"><CODE>JavaEnvUtils.JAVA_1_1</CODE></A> instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#JAVA_1_2">org.apache.tools.ant.Project.JAVA_1_2</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use <A HREF="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_2"><CODE>JavaEnvUtils.JAVA_1_2</CODE></A> instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#JAVA_1_3">org.apache.tools.ant.Project.JAVA_1_3</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use <A HREF="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_3"><CODE>JavaEnvUtils.JAVA_1_3</CODE></A> instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#JAVA_1_4">org.apache.tools.ant.Project.JAVA_1_4</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use <A HREF="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_4"><CODE>JavaEnvUtils.JAVA_1_4</CODE></A> instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/ProjectComponent.html#location">org.apache.tools.ant.ProjectComponent.location</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <A HREF="org/apache/tools/ant/ProjectComponent.html#getLocation()"><CODE>ProjectComponent.getLocation()</CODE></A> method.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Tar.html#OMIT">org.apache.tools.ant.taskdefs.Tar.OMIT</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Tar.OMIT is deprecated and is replaced with
             Tar.TarLongFileMode.OMIT</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/ProjectComponent.html#project">org.apache.tools.ant.ProjectComponent.project</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             You should not be directly accessing this variable directly.
             You should access project object via the getProject()
             or setProject() accessor/mutators.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/types/DataType.html#ref">org.apache.tools.ant.types.DataType.ref</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.
             The Employee should not be directly referencing
             variable. Please use <A HREF="org/apache/tools/ant/types/DataType.html#getRefid()"><CODE>DataType.getRefid()</CODE></A> instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Task.html#target">org.apache.tools.ant.Task.target</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <A HREF="org/apache/tools/ant/Task.html#getOwningTarget()"><CODE>Task.getOwningTarget()</CODE></A> method.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Task.html#taskName">org.apache.tools.ant.Task.taskName</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <A HREF="org/apache/tools/ant/Task.html#getTaskName()"><CODE>Task.getTaskName()</CODE></A> method.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Task.html#taskType">org.apache.tools.ant.Task.taskType</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <A HREF="org/apache/tools/ant/Task.html#getTaskType()"><CODE>Task.getTaskType()</CODE></A> method.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Tar.html#TRUNCATE">org.apache.tools.ant.taskdefs.Tar.TRUNCATE</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Tar.TRUNCATE is deprecated and is replaced with
             Tar.TarLongFileMode.TRUNCATE</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Tar.html#WARN">org.apache.tools.ant.taskdefs.Tar.WARN</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Tar.WARN is deprecated and is replaced with
             Tar.TarLongFileMode.WARN</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Task.html#wrapper">org.apache.tools.ant.Task.wrapper</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             You should not be accessing this variable directly.
             Please use the <A HREF="org/apache/tools/ant/Task.html#getWrapper()"><CODE>Task.getWrapper()</CODE></A> method.</I>&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<P>
<A NAME="method"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Deprecated Methods</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/compilers/DefaultCompilerAdapter.html#addExtdirsToClasspath(org.apache.tools.ant.types.Path)">org.apache.tools.ant.taskdefs.compilers.DefaultCompilerAdapter.addExtdirsToClasspath(Path)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use org.apache.tools.ant.types.Path#addExtdirs instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#addFilter(java.lang.String, java.lang.String)">org.apache.tools.ant.Project.addFilter(String, String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.4.x.
             Use getGlobalFilterSet().addFilter(token,value)</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#addSysproperty(org.apache.tools.ant.types.Environment.Variable)">org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.addSysproperty(Environment.Variable)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since ant 1.6</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Tar.html#archiveIsUpToDate(java.lang.String[])">org.apache.tools.ant.taskdefs.Tar.archiveIsUpToDate(String[])</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             use the two-arg version instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/ProjectHelper.html#configure(java.lang.Object, org.xml.sax.AttributeList, org.apache.tools.ant.Project)">org.apache.tools.ant.ProjectHelper.configure(Object, AttributeList, Project)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             Use IntrospectionHelper for each property.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/XSLTProcess.html#configureLiaison(java.io.File)">org.apache.tools.ant.taskdefs.XSLTProcess.configureLiaison(File)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since Ant 1.7</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#copyFile(java.io.File, java.io.File)">org.apache.tools.ant.Project.copyFile(File, File)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.4.x</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#copyFile(java.io.File, java.io.File, boolean)">org.apache.tools.ant.Project.copyFile(File, File, boolean)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.4.x</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#copyFile(java.io.File, java.io.File, boolean, boolean)">org.apache.tools.ant.Project.copyFile(File, File, boolean, boolean)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.4.x</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#copyFile(java.io.File, java.io.File, boolean, boolean, boolean)">org.apache.tools.ant.Project.copyFile(File, File, boolean, boolean, boolean)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.4.x</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#copyFile(java.lang.String, java.lang.String)">org.apache.tools.ant.Project.copyFile(String, String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.4.x</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#copyFile(java.lang.String, java.lang.String, boolean)">org.apache.tools.ant.Project.copyFile(String, String, boolean)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.4.x</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#copyFile(java.lang.String, java.lang.String, boolean, boolean)">org.apache.tools.ant.Project.copyFile(String, String, boolean, boolean)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.4.x</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#copyFile(java.lang.String, java.lang.String, boolean, boolean, boolean)">org.apache.tools.ant.Project.copyFile(String, String, boolean, boolean, boolean)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.4.x</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/MacroInstance.html#createDynamicElement(java.lang.String)">org.apache.tools.ant.taskdefs.MacroInstance.createDynamicElement(String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/IntrospectionHelper.html#createElement(org.apache.tools.ant.Project, java.lang.Object, java.lang.String)">org.apache.tools.ant.IntrospectionHelper.createElement(Project, Object, String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             This is not a namespace aware method.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/optional/jdepend/JDependTask.html#createSourcespath()">org.apache.tools.ant.taskdefs.optional.jdepend.JDependTask.createSourcespath()</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/util/CollectionUtils.html#equals(java.util.Dictionary, java.util.Dictionary)">org.apache.tools.ant.util.CollectionUtils.equals(Dictionary, Dictionary)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/util/CollectionUtils.html#equals(java.util.Vector, java.util.Vector)">org.apache.tools.ant.util.CollectionUtils.equals(Vector, Vector)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/optional/script/ScriptDef.html#executeScript(java.util.Map, java.util.Map)">org.apache.tools.ant.taskdefs.optional.script.ScriptDef.executeScript(Map, Map)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.
             Use executeScript(attribute, elements, instance) instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/RuntimeConfigurable.html#getAttributes()">org.apache.tools.ant.RuntimeConfigurable.getAttributes()</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>Deprecated since Ant 1.6 in favor of <A HREF="org/apache/tools/ant/RuntimeConfigurable.html#getAttributeMap()"><CODE>RuntimeConfigurable.getAttributeMap()</CODE></A>.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/ProjectHelper.html#getContextClassLoader()">org.apache.tools.ant.ProjectHelper.getContextClassLoader()</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             Use LoaderUtils.getContextClassLoader()</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/types/ArchiveFileSet.html#getDirMode()">org.apache.tools.ant.types.ArchiveFileSet.getDirMode()</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/types/ArchiveFileSet.html#getFileMode()">org.apache.tools.ant.types.ArchiveFileSet.getFileMode()</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#getFilters()">org.apache.tools.ant.Project.getFilters()</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.4.x
             Use getGlobalFilterSet().getFilterHash().</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/types/ArchiveFileSet.html#getFullpath()">org.apache.tools.ant.types.ArchiveFileSet.getFullpath()</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#getJavaVersion()">org.apache.tools.ant.Project.getJavaVersion()</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use org.apache.tools.ant.util.JavaEnvUtils instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/util/FileUtils.html#getParentFile(java.io.File)">org.apache.tools.ant.util.FileUtils.getParentFile(File)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.
             Just use <CODE>File.getParentFile()</CODE> directly.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/types/ArchiveFileSet.html#getPrefix()">org.apache.tools.ant.types.ArchiveFileSet.getPrefix()</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/optional/jdepend/JDependTask.html#getSourcespath()">org.apache.tools.ant.taskdefs.optional.jdepend.JDependTask.getSourcespath()</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/optional/TraXLiaison.html#getSystemId(java.io.File)">org.apache.tools.ant.taskdefs.optional.TraXLiaison.getSystemId(File)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use org.apache.tools.ant.util.JAXPUtils#getSystemId instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/AntClassLoader.html#initializeClass(java.lang.Class)">org.apache.tools.ant.AntClassLoader.initializeClass(Class)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             Use Class.forName with initialize=true instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/util/FileUtils.html#newFileUtils()">org.apache.tools.ant.util.FileUtils.newFileUtils()</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.
             Use getFileUtils instead,
 FileUtils do not have state.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/ProjectHelper.html#parsePropertyString(java.lang.String, java.util.Vector, java.util.Vector)">org.apache.tools.ant.ProjectHelper.parsePropertyString(String, Vector, Vector)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             Use PropertyHelper.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/SQLExec.html#printResults(java.io.PrintStream)">org.apache.tools.ant.taskdefs.SQLExec.printResults(PrintStream)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             Use <A HREF="org/apache/tools/ant/taskdefs/SQLExec.html#printResults(java.sql.ResultSet, java.io.PrintStream)"><CODE>the two arg version</CODE></A> instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/util/CollectionUtils.html#putAll(java.util.Dictionary, java.util.Dictionary)">org.apache.tools.ant.util.CollectionUtils.putAll(Dictionary, Dictionary)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/ProjectHelper.html#replaceProperties(org.apache.tools.ant.Project, java.lang.String)">org.apache.tools.ant.ProjectHelper.replaceProperties(Project, String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             Use project.replaceProperties().</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/ProjectHelper.html#replaceProperties(org.apache.tools.ant.Project, java.lang.String, java.util.Hashtable)">org.apache.tools.ant.ProjectHelper.replaceProperties(Project, String, Hashtable)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             Use PropertyHelper.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#resolveFile(java.lang.String, java.io.File)">org.apache.tools.ant.Project.resolveFile(String, File)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.4.x</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/optional/net/FTP.html#setAction(java.lang.String)">org.apache.tools.ant.taskdefs.optional.net.FTP.setAction(String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             setAction(String) is deprecated and is replaced with
      setAction(FTP.Action) to make Ant's Introspection mechanism do the
      work and also to encapsulate operations on the type in its own
      class.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/RuntimeConfigurable.html#setAttributes(org.xml.sax.AttributeList)">org.apache.tools.ant.RuntimeConfigurable.setAttributes(AttributeList)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/optional/ReplaceRegExp.html#setByLine(java.lang.String)">org.apache.tools.ant.taskdefs.optional.ReplaceRegExp.setByLine(String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             Use setByLine(boolean).</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/zip/ZipEntry.html#setComprSize(long)">org.apache.tools.zip.ZipEntry.setComprSize(long)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.
             Use setCompressedSize directly.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/FixCRLF.html#setCr(org.apache.tools.ant.taskdefs.FixCRLF.AddAsisRemove)">org.apache.tools.ant.taskdefs.FixCRLF.setCr(FixCRLF.AddAsisRemove)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.4.x.
             Use <A HREF="org/apache/tools/ant/taskdefs/FixCRLF.html#setEol(org.apache.tools.ant.taskdefs.FixCRLF.CrLf)"><CODE>setEol</CODE></A> instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#setDefaultTarget(java.lang.String)">org.apache.tools.ant.Project.setDefaultTarget(String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use setDefault.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Unpack.html#setDest(java.lang.String)">org.apache.tools.ant.taskdefs.Unpack.setDest(String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             setDest(String) is deprecated and is replaced with
             setDest(File) to make Ant's Introspection
             mechanism do the work and also to encapsulate operations on
             the type in its own class.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Ear.html#setEarfile(java.io.File)">org.apache.tools.ant.taskdefs.Ear.setEarfile(File)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use setDestFile(destfile) instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Javadoc.html#setExtdirs(java.lang.String)">org.apache.tools.ant.taskdefs.Javadoc.setExtdirs(String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use the <A HREF="org/apache/tools/ant/taskdefs/Javadoc.html#setExtdirs(org.apache.tools.ant.types.Path)"><CODE>Javadoc.setExtdirs(Path)</CODE></A> version.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Zip.html#setFile(java.io.File)">org.apache.tools.ant.taskdefs.Zip.setFile(File)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use setDestFile(File) instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#setFileLastModified(java.io.File, long)">org.apache.tools.ant.Project.setFileLastModified(File, long)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.4.x</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/optional/ANTLR.html#setGlib(java.lang.String)">org.apache.tools.ant.taskdefs.optional.ANTLR.setGlib(String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since ant 1.6</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Jar.html#setJarfile(java.io.File)">org.apache.tools.ant.taskdefs.Jar.setJarfile(File)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use setDestFile(File) instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Tar.html#setLongfile(java.lang.String)">org.apache.tools.ant.taskdefs.Tar.setLongfile(String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             setLongFile(String) is deprecated and is replaced with
             setLongFile(Tar.TarLongFileMode) to make Ant's Introspection
             mechanism do the work and also to encapsulate operations on
             the mode in its own class.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/SendEmail.html#setMailport(java.lang.Integer)">org.apache.tools.ant.taskdefs.SendEmail.setMailport(Integer)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use <A HREF="org/apache/tools/ant/taskdefs/email/EmailTask.html#setMailport(int)"><CODE>EmailTask.setMailport(int)</CODE></A> instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/ExecuteJava.html#setOutput(java.io.PrintStream)">org.apache.tools.ant.taskdefs.ExecuteJava.setOutput(PrintStream)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.4.x.
             manage output at the task level.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Copy.html#setPreserveLastModified(java.lang.String)">org.apache.tools.ant.taskdefs.Copy.setPreserveLastModified(String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             setPreserveLastModified(String) has been deprecated and
             replaced with setPreserveLastModified(boolean) to
             consistently let the Introspection mechanism work.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/DefBase.html#setReverseLoader(boolean)">org.apache.tools.ant.taskdefs.DefBase.setReverseLoader(boolean)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.
             stop using this attribute</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Unpack.html#setSrc(java.lang.String)">org.apache.tools.ant.taskdefs.Unpack.setSrc(String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             setSrc(String) is deprecated and is replaced with
             setSrc(File) to make Ant's Introspection
             mechanism do the work and also to encapsulate operations on
             the type in its own class.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Tar.html#setTarfile(java.io.File)">org.apache.tools.ant.taskdefs.Tar.setTarfile(File)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             For consistency with other tasks, please use setDestFile().</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/PathConvert.html#setTargetos(java.lang.String)">org.apache.tools.ant.taskdefs.PathConvert.setTargetos(String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use the method taking a TargetOs argument instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Available.html#setType(java.lang.String)">org.apache.tools.ant.taskdefs.Available.setType(String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             setType(String) is deprecated and is replaced with
             setType(Available.FileDir) to make Ant's Introspection
             mechanism do the work and also to encapsulate operations on
             the type in its own class.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Tstamp.CustomFormat.html#setUnit(java.lang.String)">org.apache.tools.ant.taskdefs.Tstamp.CustomFormat.setUnit(String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             setUnit(String) is deprecated and is replaced with
             setUnit(Tstamp.Unit) to make Ant's
             Introspection mechanism do the work and also to
             encapsulate operations on the unit in its own
             class.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/optional/splash/SplashTask.html#setUseproxy(boolean)">org.apache.tools.ant.taskdefs.optional.splash.SplashTask.setUseproxy(boolean)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use org.apache.tools.ant.taskdefs.optional.SetProxy</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Property.html#setUserProperty(boolean)">org.apache.tools.ant.taskdefs.Property.setUserProperty(boolean)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             This was never a supported feature and has been
             deprecated without replacement.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/War.html#setWarfile(java.io.File)">org.apache.tools.ant.taskdefs.War.setWarfile(File)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use setDestFile(File) instead</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Zip.html#setZipfile(java.io.File)">org.apache.tools.ant.taskdefs.Zip.setZipfile(File)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use setDestFile(File) instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/types/CommandlineJava.html#size()">org.apache.tools.ant.types.CommandlineJava.size()</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.
             Please dont use this, it effectively creates the
             entire command.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/Touch.html#touch(java.io.File)">org.apache.tools.ant.taskdefs.Touch.touch(File)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Project.html#translatePath(java.lang.String)">org.apache.tools.ant.Project.translatePath(String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7
             Use FileUtils.translatePath instead.</I>&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<P>
<A NAME="constructor"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Deprecated Constructors</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/taskdefs/ExecuteWatchdog.html#ExecuteWatchdog(int)">org.apache.tools.ant.taskdefs.ExecuteWatchdog(int)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.5.x.
             Use constructor with a long type instead.
 (1.4.x compatibility)</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/Main.html#Main(java.lang.String[])">org.apache.tools.ant.Main(String[])</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.6.x</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/types/Reference.html#Reference()">org.apache.tools.ant.types.Reference()</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.
             Please use <A HREF="org/apache/tools/ant/types/Reference.html#Reference(org.apache.tools.ant.Project, java.lang.String)"><CODE>Reference.Reference(Project,String)</CODE></A>
             instead.</I>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD><A HREF="org/apache/tools/ant/types/Reference.html#Reference(java.lang.String)">org.apache.tools.ant.types.Reference(String)</A>
<BR>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<I>since 1.7.
             Please use <A HREF="org/apache/tools/ant/types/Reference.html#Reference(org.apache.tools.ant.Project, java.lang.String)"><CODE>Reference.Reference(Project,String)</CODE></A>
             instead.</I>&nbsp;</TD>
</TR>
</TABLE>
&nbsp;
<P>
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Package</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="overview-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Deprecated</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV&nbsp;
&nbsp;NEXT</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="index.html?deprecated-list.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="deprecated-list.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
