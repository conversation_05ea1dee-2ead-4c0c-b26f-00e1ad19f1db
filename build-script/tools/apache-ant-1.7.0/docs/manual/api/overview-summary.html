<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:38 EST 2006 -->
<TITLE>
Overview (Apache Ant API)
</TITLE>

<META NAME="keywords" CONTENT="Overview, Apache Ant">

<LINK REL ="stylesheet" TYPE="text/css" HREF="stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Overview (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Overview</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Package</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="overview-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV&nbsp;
&nbsp;NEXT</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="index.html?overview-summary.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="overview-summary.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<CENTER>
<H1>
Apache Ant
</H1>
</CENTER>

<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Apache Ant Core</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/package-summary.html">org.apache.tools.ant</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/dispatch/package-summary.html">org.apache.tools.ant.dispatch</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/filters/package-summary.html">org.apache.tools.ant.filters</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/filters/util/package-summary.html">org.apache.tools.ant.filters.util</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/helper/package-summary.html">org.apache.tools.ant.helper</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/input/package-summary.html">org.apache.tools.ant.input</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/launch/package-summary.html">org.apache.tools.ant.launch</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/listener/package-summary.html">org.apache.tools.ant.listener</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/loader/package-summary.html">org.apache.tools.ant.loader</A></B></TD>
<TD>&nbsp;</TD>
</TR>
</TABLE>

<P>
&nbsp;
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Core Tasks</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/package-summary.html">org.apache.tools.ant.taskdefs</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/compilers/package-summary.html">org.apache.tools.ant.taskdefs.compilers</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/condition/package-summary.html">org.apache.tools.ant.taskdefs.condition</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/cvslib/package-summary.html">org.apache.tools.ant.taskdefs.cvslib</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/email/package-summary.html">org.apache.tools.ant.taskdefs.email</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/rmic/package-summary.html">org.apache.tools.ant.taskdefs.rmic</A></B></TD>
<TD>&nbsp;</TD>
</TR>
</TABLE>

<P>
&nbsp;
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Core Types</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/types/package-summary.html">org.apache.tools.ant.types</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/types/mappers/package-summary.html">org.apache.tools.ant.types.mappers</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/types/resolver/package-summary.html">org.apache.tools.ant.types.resolver</A></B></TD>
<TD>Ant integration with xml-commons resolver.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/types/resources/package-summary.html">org.apache.tools.ant.types.resources</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/types/resources/comparators/package-summary.html">org.apache.tools.ant.types.resources.comparators</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/types/resources/selectors/package-summary.html">org.apache.tools.ant.types.resources.selectors</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/types/selectors/package-summary.html">org.apache.tools.ant.types.selectors</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/types/selectors/modifiedselector/package-summary.html">org.apache.tools.ant.types.selectors.modifiedselector</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/types/spi/package-summary.html">org.apache.tools.ant.types.spi</A></B></TD>
<TD>&nbsp;</TD>
</TR>
</TABLE>

<P>
&nbsp;
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Optional Tasks</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/package-summary.html">org.apache.tools.ant.taskdefs.optional</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/package-summary.html">org.apache.tools.ant.taskdefs.optional.ccm</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/package-summary.html">org.apache.tools.ant.taskdefs.optional.clearcase</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/depend/package-summary.html">org.apache.tools.ant.taskdefs.optional.depend</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/depend/constantpool/package-summary.html">org.apache.tools.ant.taskdefs.optional.depend.constantpool</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/package-summary.html">org.apache.tools.ant.taskdefs.optional.dotnet</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/package-summary.html">org.apache.tools.ant.taskdefs.optional.ejb</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/extension/package-summary.html">org.apache.tools.ant.taskdefs.optional.extension</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/extension/resolvers/package-summary.html">org.apache.tools.ant.taskdefs.optional.extension.resolvers</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/i18n/package-summary.html">org.apache.tools.ant.taskdefs.optional.i18n</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/image/package-summary.html">org.apache.tools.ant.taskdefs.optional.image</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/j2ee/package-summary.html">org.apache.tools.ant.taskdefs.optional.j2ee</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/javacc/package-summary.html">org.apache.tools.ant.taskdefs.optional.javacc</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/javah/package-summary.html">org.apache.tools.ant.taskdefs.optional.javah</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/jdepend/package-summary.html">org.apache.tools.ant.taskdefs.optional.jdepend</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/jlink/package-summary.html">org.apache.tools.ant.taskdefs.optional.jlink</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/jsp/package-summary.html">org.apache.tools.ant.taskdefs.optional.jsp</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/jsp/compilers/package-summary.html">org.apache.tools.ant.taskdefs.optional.jsp.compilers</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/junit/package-summary.html">org.apache.tools.ant.taskdefs.optional.junit</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/native2ascii/package-summary.html">org.apache.tools.ant.taskdefs.optional.native2ascii</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/net/package-summary.html">org.apache.tools.ant.taskdefs.optional.net</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/perforce/package-summary.html">org.apache.tools.ant.taskdefs.optional.perforce</A></B></TD>
<TD>ANT Tasks for Perforce integration.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/pvcs/package-summary.html">org.apache.tools.ant.taskdefs.optional.pvcs</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/scm/package-summary.html">org.apache.tools.ant.taskdefs.optional.scm</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/script/package-summary.html">org.apache.tools.ant.taskdefs.optional.script</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/sos/package-summary.html">org.apache.tools.ant.taskdefs.optional.sos</A></B></TD>
<TD>
    Ant tasks for working with a SourceOffSite source control system.</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/sound/package-summary.html">org.apache.tools.ant.taskdefs.optional.sound</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/splash/package-summary.html">org.apache.tools.ant.taskdefs.optional.splash</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/ssh/package-summary.html">org.apache.tools.ant.taskdefs.optional.ssh</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/starteam/package-summary.html">org.apache.tools.ant.taskdefs.optional.starteam</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/unix/package-summary.html">org.apache.tools.ant.taskdefs.optional.unix</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/vss/package-summary.html">org.apache.tools.ant.taskdefs.optional.vss</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/taskdefs/optional/windows/package-summary.html">org.apache.tools.ant.taskdefs.optional.windows</A></B></TD>
<TD>&nbsp;</TD>
</TR>
</TABLE>

<P>
&nbsp;
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Optional Types</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/types/optional/package-summary.html">org.apache.tools.ant.types.optional</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/types/optional/depend/package-summary.html">org.apache.tools.ant.types.optional.depend</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/types/optional/image/package-summary.html">org.apache.tools.ant.types.optional.image</A></B></TD>
<TD>&nbsp;</TD>
</TR>
</TABLE>

<P>
&nbsp;
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Ant Utilities</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/util/package-summary.html">org.apache.tools.ant.util</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/util/depend/package-summary.html">org.apache.tools.ant.util.depend</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/util/depend/bcel/package-summary.html">org.apache.tools.ant.util.depend.bcel</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/util/facade/package-summary.html">org.apache.tools.ant.util.facade</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/util/java15/package-summary.html">org.apache.tools.ant.util.java15</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/util/optional/package-summary.html">org.apache.tools.ant.util.optional</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/ant/util/regexp/package-summary.html">org.apache.tools.ant.util.regexp</A></B></TD>
<TD>&nbsp;</TD>
</TR>
</TABLE>

<P>
&nbsp;
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
<B>Other Packages</B></FONT></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/bzip2/package-summary.html">org.apache.tools.bzip2</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/mail/package-summary.html">org.apache.tools.mail</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/tar/package-summary.html">org.apache.tools.tar</A></B></TD>
<TD>&nbsp;</TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<TD WIDTH="20%"><B><A HREF="org/apache/tools/zip/package-summary.html">org.apache.tools.zip</A></B></TD>
<TD>&nbsp;</TD>
</TR>
</TABLE>

<P>
&nbsp;<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Overview</B></FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Package</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="overview-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV&nbsp;
&nbsp;NEXT</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="index.html?overview-summary.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="overview-summary.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
