<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!--NewPage-->
<HTML>
<HEAD>
<!-- Generated by javadoc (build 1.5.0_06) on Wed Dec 13 07:15:35 EST 2006 -->
<TITLE>
Constant Field Values (Apache Ant API)
</TITLE>


<LINK REL ="stylesheet" TYPE="text/css" HREF="stylesheet.css" TITLE="Style">

<SCRIPT type="text/javascript">
function windowTitle()
{
    parent.document.title="Constant Field Values (Apache Ant API)";
}
</SCRIPT>
<NOSCRIPT>
</NOSCRIPT>

</HEAD>

<BODY BGCOLOR="white" onload="windowTitle();">


<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<A HREF="#skip-navbar_top" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Package</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="overview-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV&nbsp;
&nbsp;NEXT</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="index.html?constant-values.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="constant-values.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_top"></A>
<!-- ========= END OF TOP NAVBAR ========= -->

<HR>
<CENTER>
<H1>
Constant Field Values</H1>
</CENTER>
<HR SIZE="4" NOSHADE>
<B>Contents</B><UL>
<LI><A HREF="#org.apache">org.apache.*</A>
</UL>

<A NAME="org.apache"><!-- --></A>
<TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
<TH ALIGN="left"><FONT SIZE="+2">
org.apache.*</FONT></TH>
</TR>
</TABLE>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.<A HREF="org/apache/tools/ant/ComponentHelper.html" title="class in org.apache.tools.ant">ComponentHelper</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.ComponentHelper.COMPONENT_HELPER_REFERENCE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/ComponentHelper.html#COMPONENT_HELPER_REFERENCE">COMPONENT_HELPER_REFERENCE</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.ComponentHelper"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.<A HREF="org/apache/tools/ant/DefaultLogger.html" title="class in org.apache.tools.ant">DefaultLogger</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.DefaultLogger.LEFT_COLUMN_SIZE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/DefaultLogger.html#LEFT_COLUMN_SIZE">LEFT_COLUMN_SIZE</A></CODE></TD>
<TD ALIGN="right"><CODE>12</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.<A HREF="org/apache/tools/ant/Diagnostics.html" title="class in org.apache.tools.ant">Diagnostics</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.Diagnostics.ERROR_PROPERTY_ACCESS_BLOCKED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/Diagnostics.html#ERROR_PROPERTY_ACCESS_BLOCKED">ERROR_PROPERTY_ACCESS_BLOCKED</A></CODE></TD>
<TD ALIGN="right"><CODE>"Access to this property blocked by a security manager"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.<A HREF="org/apache/tools/ant/MagicNames.html" title="class in org.apache.tools.ant">MagicNames</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.ANT_EXECUTOR_CLASSNAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#ANT_EXECUTOR_CLASSNAME">ANT_EXECUTOR_CLASSNAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.executor.class"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.ANT_EXECUTOR_REFERENCE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#ANT_EXECUTOR_REFERENCE">ANT_EXECUTOR_REFERENCE</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.executor"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.ANT_FILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#ANT_FILE">ANT_FILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.file"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.ANT_HOME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#ANT_HOME">ANT_HOME</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.home"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.ANT_JAVA_VERSION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#ANT_JAVA_VERSION">ANT_JAVA_VERSION</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.java.version"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.ANT_LIB"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#ANT_LIB">ANT_LIB</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.core.lib"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.ANT_VERSION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#ANT_VERSION">ANT_VERSION</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.version"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.ANTLIB_PREFIX"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#ANTLIB_PREFIX">ANTLIB_PREFIX</A></CODE></TD>
<TD ALIGN="right"><CODE>"antlib:"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.BUILD_JAVAC_SOURCE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#BUILD_JAVAC_SOURCE">BUILD_JAVAC_SOURCE</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.build.javac.source"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.BUILD_JAVAC_TARGET"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#BUILD_JAVAC_TARGET">BUILD_JAVAC_TARGET</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.build.javac.target"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.BUILD_SYSCLASSPATH"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#BUILD_SYSCLASSPATH">BUILD_SYSCLASSPATH</A></CODE></TD>
<TD ALIGN="right"><CODE>"build.sysclasspath"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.PROJECT_BASEDIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#PROJECT_BASEDIR">PROJECT_BASEDIR</A></CODE></TD>
<TD ALIGN="right"><CODE>"basedir"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.REFID_CLASSPATH_LOADER_PREFIX"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#REFID_CLASSPATH_LOADER_PREFIX">REFID_CLASSPATH_LOADER_PREFIX</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.loader."</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.REFID_CLASSPATH_REUSE_LOADER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#REFID_CLASSPATH_REUSE_LOADER">REFID_CLASSPATH_REUSE_LOADER</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.reuse.loader"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.REFID_PROPERTY_HELPER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#REFID_PROPERTY_HELPER">REFID_PROPERTY_HELPER</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.PropertyHelper"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.REGEXP_IMPL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#REGEXP_IMPL">REGEXP_IMPL</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.regexp.regexpimpl"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.REPOSITORY_DIR_PROPERTY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#REPOSITORY_DIR_PROPERTY">REPOSITORY_DIR_PROPERTY</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.maven.repository.dir"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.REPOSITORY_URL_PROPERTY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#REPOSITORY_URL_PROPERTY">REPOSITORY_URL_PROPERTY</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.maven.repository.url"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.SCRIPT_REPOSITORY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#SCRIPT_REPOSITORY">SCRIPT_REPOSITORY</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.apache.ant.scriptrepo"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.SYSTEM_LOADER_REF"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#SYSTEM_LOADER_REF">SYSTEM_LOADER_REF</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.coreLoader"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.TASKDEF_PROPERTIES_RESOURCE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#TASKDEF_PROPERTIES_RESOURCE">TASKDEF_PROPERTIES_RESOURCE</A></CODE></TD>
<TD ALIGN="right"><CODE>"/org/apache/tools/ant/taskdefs/defaults.properties"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.MagicNames.TYPEDEFS_PROPERTIES_RESOURCE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/MagicNames.html#TYPEDEFS_PROPERTIES_RESOURCE">TYPEDEFS_PROPERTIES_RESOURCE</A></CODE></TD>
<TD ALIGN="right"><CODE>"/org/apache/tools/ant/types/defaults.properties"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.<A HREF="org/apache/tools/ant/Main.html" title="class in org.apache.tools.ant">Main</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.Main.DEFAULT_BUILD_FILENAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/Main.html#DEFAULT_BUILD_FILENAME">DEFAULT_BUILD_FILENAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"build.xml"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.<A HREF="org/apache/tools/ant/Project.html" title="class in org.apache.tools.ant">Project</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.Project.JAVA_1_0"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/Project.html#JAVA_1_0">JAVA_1_0</A></CODE></TD>
<TD ALIGN="right"><CODE>"1.0"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.Project.JAVA_1_1"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/Project.html#JAVA_1_1">JAVA_1_1</A></CODE></TD>
<TD ALIGN="right"><CODE>"1.1"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.Project.JAVA_1_2"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/Project.html#JAVA_1_2">JAVA_1_2</A></CODE></TD>
<TD ALIGN="right"><CODE>"1.2"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.Project.JAVA_1_3"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/Project.html#JAVA_1_3">JAVA_1_3</A></CODE></TD>
<TD ALIGN="right"><CODE>"1.3"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.Project.JAVA_1_4"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/Project.html#JAVA_1_4">JAVA_1_4</A></CODE></TD>
<TD ALIGN="right"><CODE>"1.4"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.Project.MSG_DEBUG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/Project.html#MSG_DEBUG">MSG_DEBUG</A></CODE></TD>
<TD ALIGN="right"><CODE>4</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.Project.MSG_ERR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/Project.html#MSG_ERR">MSG_ERR</A></CODE></TD>
<TD ALIGN="right"><CODE>0</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.Project.MSG_INFO"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/Project.html#MSG_INFO">MSG_INFO</A></CODE></TD>
<TD ALIGN="right"><CODE>2</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.Project.MSG_VERBOSE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/Project.html#MSG_VERBOSE">MSG_VERBOSE</A></CODE></TD>
<TD ALIGN="right"><CODE>3</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.Project.MSG_WARN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/Project.html#MSG_WARN">MSG_WARN</A></CODE></TD>
<TD ALIGN="right"><CODE>1</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.Project.TOKEN_END"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/Project.html#TOKEN_END">TOKEN_END</A></CODE></TD>
<TD ALIGN="right"><CODE>"@"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.Project.TOKEN_START"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/Project.html#TOKEN_START">TOKEN_START</A></CODE></TD>
<TD ALIGN="right"><CODE>"@"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.<A HREF="org/apache/tools/ant/ProjectHelper.html" title="class in org.apache.tools.ant">ProjectHelper</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.ProjectHelper.ANT_CORE_URI"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/ProjectHelper.html#ANT_CORE_URI">ANT_CORE_URI</A></CODE></TD>
<TD ALIGN="right"><CODE>"antlib:org.apache.tools.ant"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.ProjectHelper.ANT_CURRENT_URI"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/ProjectHelper.html#ANT_CURRENT_URI">ANT_CURRENT_URI</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant:current"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.ProjectHelper.ANT_TYPE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/ProjectHelper.html#ANT_TYPE">ANT_TYPE</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant-type"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.ProjectHelper.ANTLIB_URI"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/ProjectHelper.html#ANTLIB_URI">ANTLIB_URI</A></CODE></TD>
<TD ALIGN="right"><CODE>"antlib:"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.ProjectHelper.HELPER_PROPERTY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/ProjectHelper.html#HELPER_PROPERTY">HELPER_PROPERTY</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.apache.tools.ant.ProjectHelper"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.ProjectHelper.PROJECTHELPER_REFERENCE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/ProjectHelper.html#PROJECTHELPER_REFERENCE">PROJECTHELPER_REFERENCE</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.projectHelper"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.ProjectHelper.SERVICE_ID"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/ProjectHelper.html#SERVICE_ID">SERVICE_ID</A></CODE></TD>
<TD ALIGN="right"><CODE>"META-INF/services/org.apache.tools.ant.ProjectHelper"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.input.<A HREF="org/apache/tools/ant/input/PropertyFileInputHandler.html" title="class in org.apache.tools.ant.input">PropertyFileInputHandler</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.input.PropertyFileInputHandler.FILE_NAME_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/input/PropertyFileInputHandler.html#FILE_NAME_KEY">FILE_NAME_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.input.properties"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.launch.<A HREF="org/apache/tools/ant/launch/Launcher.html" title="class in org.apache.tools.ant.launch">Launcher</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.launch.Launcher.ANT_PRIVATEDIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/launch/Launcher.html#ANT_PRIVATEDIR">ANT_PRIVATEDIR</A></CODE></TD>
<TD ALIGN="right"><CODE>".ant"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.launch.Launcher.ANT_PRIVATELIB"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/launch/Launcher.html#ANT_PRIVATELIB">ANT_PRIVATELIB</A></CODE></TD>
<TD ALIGN="right"><CODE>"lib"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.launch.Launcher.ANTHOME_PROPERTY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/launch/Launcher.html#ANTHOME_PROPERTY">ANTHOME_PROPERTY</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.home"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.launch.Launcher.ANTLIBDIR_PROPERTY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/launch/Launcher.html#ANTLIBDIR_PROPERTY">ANTLIBDIR_PROPERTY</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.library.dir"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.launch.Launcher.EXIT_CODE_ERROR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/launch/Launcher.html#EXIT_CODE_ERROR">EXIT_CODE_ERROR</A></CODE></TD>
<TD ALIGN="right"><CODE>2</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.launch.Launcher.MAIN_CLASS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/launch/Launcher.html#MAIN_CLASS">MAIN_CLASS</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.apache.tools.ant.Main"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.launch.Launcher.USER_HOMEDIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/launch/Launcher.html#USER_HOMEDIR">USER_HOMEDIR</A></CODE></TD>
<TD ALIGN="right"><CODE>"Employee.home"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.launch.<A HREF="org/apache/tools/ant/launch/Locator.html" title="class in org.apache.tools.ant.launch">Locator</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.launch.Locator.URI_ENCODING"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/launch/Locator.html#URI_ENCODING">URI_ENCODING</A></CODE></TD>
<TD ALIGN="right"><CODE>"UTF-8"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.listener.<A HREF="org/apache/tools/ant/listener/CommonsLoggingListener.html" title="class in org.apache.tools.ant.listener">CommonsLoggingListener</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.listener.CommonsLoggingListener.PROJECT_LOG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/listener/CommonsLoggingListener.html#PROJECT_LOG">PROJECT_LOG</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.apache.tools.ant.Project"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.listener.CommonsLoggingListener.TARGET_LOG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/listener/CommonsLoggingListener.html#TARGET_LOG">TARGET_LOG</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.apache.tools.ant.Target"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.listener.<A HREF="org/apache/tools/ant/listener/Log4jListener.html" title="class in org.apache.tools.ant.listener">Log4jListener</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.listener.Log4jListener.LOG_ANT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/listener/Log4jListener.html#LOG_ANT">LOG_ANT</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.apache.tools.ant"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/AbstractCvsTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractCvsTask</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.AbstractCvsTask.DEFAULT_COMPRESSION_LEVEL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/AbstractCvsTask.html#DEFAULT_COMPRESSION_LEVEL">DEFAULT_COMPRESSION_LEVEL</A></CODE></TD>
<TD ALIGN="right"><CODE>3</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html" title="class in org.apache.tools.ant.taskdefs">AbstractJarSignerTask</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.AbstractJarSignerTask.ERROR_NO_SOURCE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#ERROR_NO_SOURCE">ERROR_NO_SOURCE</A></CODE></TD>
<TD ALIGN="right"><CODE>"jar must be set through jar attribute or nested filesets"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.AbstractJarSignerTask.JARSIGNER_COMMAND"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/AbstractJarSignerTask.html#JARSIGNER_COMMAND">JARSIGNER_COMMAND</A></CODE></TD>
<TD ALIGN="right"><CODE>"jarsigner"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/Antlib.html" title="class in org.apache.tools.ant.taskdefs">Antlib</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Antlib.TAG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Antlib.html#TAG">TAG</A></CODE></TD>
<TD ALIGN="right"><CODE>"antlib"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/Apt.html" title="class in org.apache.tools.ant.taskdefs">Apt</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Apt.ERROR_IGNORING_COMPILER_OPTION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Apt.html#ERROR_IGNORING_COMPILER_OPTION">ERROR_IGNORING_COMPILER_OPTION</A></CODE></TD>
<TD ALIGN="right"><CODE>"Ignoring compiler attribute for the APT task, as it is fixed"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Apt.ERROR_WRONG_JAVA_VERSION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Apt.html#ERROR_WRONG_JAVA_VERSION">ERROR_WRONG_JAVA_VERSION</A></CODE></TD>
<TD ALIGN="right"><CODE>"Apt task requires Java 1.5+"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Apt.EXECUTABLE_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Apt.html#EXECUTABLE_NAME">EXECUTABLE_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"apt"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Apt.WARNING_IGNORING_FORK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Apt.html#WARNING_IGNORING_FORK">WARNING_IGNORING_FORK</A></CODE></TD>
<TD ALIGN="right"><CODE>"Apt only runs in its own JVM; fork=false option ignored"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/Classloader.html" title="class in org.apache.tools.ant.taskdefs">Classloader</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Classloader.SYSTEM_LOADER_REF"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Classloader.html#SYSTEM_LOADER_REF">SYSTEM_LOADER_REF</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.coreLoader"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/CopyPath.html" title="class in org.apache.tools.ant.taskdefs">CopyPath</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.CopyPath.ERROR_NO_DESTDIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/CopyPath.html#ERROR_NO_DESTDIR">ERROR_NO_DESTDIR</A></CODE></TD>
<TD ALIGN="right"><CODE>"No destDir specified"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.CopyPath.ERROR_NO_MAPPER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/CopyPath.html#ERROR_NO_MAPPER">ERROR_NO_MAPPER</A></CODE></TD>
<TD ALIGN="right"><CODE>"No mapper specified"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.CopyPath.ERROR_NO_PATH"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/CopyPath.html#ERROR_NO_PATH">ERROR_NO_PATH</A></CODE></TD>
<TD ALIGN="right"><CODE>"No path specified"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/Definer.Format.html" title="class in org.apache.tools.ant.taskdefs">Definer.Format</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Definer.Format.PROPERTIES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Definer.Format.html#PROPERTIES">PROPERTIES</A></CODE></TD>
<TD ALIGN="right"><CODE>0</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Definer.Format.XML"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Definer.Format.html#XML">XML</A></CODE></TD>
<TD ALIGN="right"><CODE>1</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/Definer.OnError.html" title="class in org.apache.tools.ant.taskdefs">Definer.OnError</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Definer.OnError.FAIL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Definer.OnError.html#FAIL">FAIL</A></CODE></TD>
<TD ALIGN="right"><CODE>0</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Definer.OnError.FAIL_ALL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Definer.OnError.html#FAIL_ALL">FAIL_ALL</A></CODE></TD>
<TD ALIGN="right"><CODE>3</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Definer.OnError.IGNORE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Definer.OnError.html#IGNORE">IGNORE</A></CODE></TD>
<TD ALIGN="right"><CODE>2</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Definer.OnError.POLICY_FAIL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Definer.OnError.html#POLICY_FAIL">POLICY_FAIL</A></CODE></TD>
<TD ALIGN="right"><CODE>"fail"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Definer.OnError.POLICY_FAILALL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Definer.OnError.html#POLICY_FAILALL">POLICY_FAILALL</A></CODE></TD>
<TD ALIGN="right"><CODE>"failall"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Definer.OnError.POLICY_IGNORE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Definer.OnError.html#POLICY_IGNORE">POLICY_IGNORE</A></CODE></TD>
<TD ALIGN="right"><CODE>"ignore"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Definer.OnError.POLICY_REPORT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Definer.OnError.html#POLICY_REPORT">POLICY_REPORT</A></CODE></TD>
<TD ALIGN="right"><CODE>"report"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Definer.OnError.REPORT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Definer.OnError.html#REPORT">REPORT</A></CODE></TD>
<TD ALIGN="right"><CODE>1</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/Execute.html" title="class in org.apache.tools.ant.taskdefs">Execute</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Execute.INVALID"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Execute.html#INVALID">INVALID</A></CODE></TD>
<TD ALIGN="right"><CODE>2147483647</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/ExecuteOn.FileDirBoth.html" title="class in org.apache.tools.ant.taskdefs">ExecuteOn.FileDirBoth</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.ExecuteOn.FileDirBoth.DIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/ExecuteOn.FileDirBoth.html#DIR">DIR</A></CODE></TD>
<TD ALIGN="right"><CODE>"dir"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.ExecuteOn.FileDirBoth.FILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/ExecuteOn.FileDirBoth.html#FILE">FILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"file"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/Expand.html" title="class in org.apache.tools.ant.taskdefs">Expand</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Expand.ERROR_MULTIPLE_MAPPERS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Expand.html#ERROR_MULTIPLE_MAPPERS">ERROR_MULTIPLE_MAPPERS</A></CODE></TD>
<TD ALIGN="right"><CODE>"Cannot define more than one mapper"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/FixCRLF.html" title="class in org.apache.tools.ant.taskdefs">FixCRLF</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.FixCRLF.ERROR_FILE_AND_SRCDIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/FixCRLF.html#ERROR_FILE_AND_SRCDIR">ERROR_FILE_AND_SRCDIR</A></CODE></TD>
<TD ALIGN="right"><CODE>"srcdir and file are mutually exclusive"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/MakeUrl.html" title="class in org.apache.tools.ant.taskdefs">MakeUrl</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.MakeUrl.ERROR_MISSING_FILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/MakeUrl.html#ERROR_MISSING_FILE">ERROR_MISSING_FILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"A source file is missing :"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.MakeUrl.ERROR_NO_FILES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/MakeUrl.html#ERROR_NO_FILES">ERROR_NO_FILES</A></CODE></TD>
<TD ALIGN="right"><CODE>"No files defined"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.MakeUrl.ERROR_NO_PROPERTY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/MakeUrl.html#ERROR_NO_PROPERTY">ERROR_NO_PROPERTY</A></CODE></TD>
<TD ALIGN="right"><CODE>"No property defined"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/Manifest.html" title="class in org.apache.tools.ant.taskdefs">Manifest</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Manifest.ATTRIBUTE_CLASSPATH"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Manifest.html#ATTRIBUTE_CLASSPATH">ATTRIBUTE_CLASSPATH</A></CODE></TD>
<TD ALIGN="right"><CODE>"Class-Path"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Manifest.ATTRIBUTE_FROM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Manifest.html#ATTRIBUTE_FROM">ATTRIBUTE_FROM</A></CODE></TD>
<TD ALIGN="right"><CODE>"From"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Manifest.ATTRIBUTE_MANIFEST_VERSION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Manifest.html#ATTRIBUTE_MANIFEST_VERSION">ATTRIBUTE_MANIFEST_VERSION</A></CODE></TD>
<TD ALIGN="right"><CODE>"Manifest-Version"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Manifest.ATTRIBUTE_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Manifest.html#ATTRIBUTE_NAME">ATTRIBUTE_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"Name"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Manifest.ATTRIBUTE_SIGNATURE_VERSION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Manifest.html#ATTRIBUTE_SIGNATURE_VERSION">ATTRIBUTE_SIGNATURE_VERSION</A></CODE></TD>
<TD ALIGN="right"><CODE>"Signature-Version"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Manifest.DEFAULT_MANIFEST_VERSION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Manifest.html#DEFAULT_MANIFEST_VERSION">DEFAULT_MANIFEST_VERSION</A></CODE></TD>
<TD ALIGN="right"><CODE>"1.0"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Manifest.EOL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Manifest.html#EOL">EOL</A></CODE></TD>
<TD ALIGN="right"><CODE>"\r\n"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Manifest.ERROR_FROM_FORBIDDEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Manifest.html#ERROR_FROM_FORBIDDEN">ERROR_FROM_FORBIDDEN</A></CODE></TD>
<TD ALIGN="right"><CODE>"Manifest attributes should not start with \"From\" in \""</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Manifest.JAR_ENCODING"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Manifest.html#JAR_ENCODING">JAR_ENCODING</A></CODE></TD>
<TD ALIGN="right"><CODE>"UTF-8"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Manifest.MAX_LINE_LENGTH"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Manifest.html#MAX_LINE_LENGTH">MAX_LINE_LENGTH</A></CODE></TD>
<TD ALIGN="right"><CODE>72</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Manifest.MAX_SECTION_LENGTH"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Manifest.html#MAX_SECTION_LENGTH">MAX_SECTION_LENGTH</A></CODE></TD>
<TD ALIGN="right"><CODE>70</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/Rmic.html" title="class in org.apache.tools.ant.taskdefs">Rmic</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Rmic.ERROR_BASE_NOT_SET"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Rmic.html#ERROR_BASE_NOT_SET">ERROR_BASE_NOT_SET</A></CODE></TD>
<TD ALIGN="right"><CODE>"base attribute must be set!"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Rmic.ERROR_LOADING_CAUSED_EXCEPTION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Rmic.html#ERROR_LOADING_CAUSED_EXCEPTION">ERROR_LOADING_CAUSED_EXCEPTION</A></CODE></TD>
<TD ALIGN="right"><CODE>". Loading caused Exception: "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Rmic.ERROR_NO_BASE_EXISTS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Rmic.html#ERROR_NO_BASE_EXISTS">ERROR_NO_BASE_EXISTS</A></CODE></TD>
<TD ALIGN="right"><CODE>"base does not exist: "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Rmic.ERROR_NOT_A_DIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Rmic.html#ERROR_NOT_A_DIR">ERROR_NOT_A_DIR</A></CODE></TD>
<TD ALIGN="right"><CODE>"base is not a directory:"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Rmic.ERROR_NOT_DEFINED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Rmic.html#ERROR_NOT_DEFINED">ERROR_NOT_DEFINED</A></CODE></TD>
<TD ALIGN="right"><CODE>". It is not defined."</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Rmic.ERROR_NOT_FOUND"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Rmic.html#ERROR_NOT_FOUND">ERROR_NOT_FOUND</A></CODE></TD>
<TD ALIGN="right"><CODE>". It could not be found."</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Rmic.ERROR_RMIC_FAILED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Rmic.html#ERROR_RMIC_FAILED">ERROR_RMIC_FAILED</A></CODE></TD>
<TD ALIGN="right"><CODE>"Rmic failed; see the compiler error output for details."</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Rmic.ERROR_UNABLE_TO_VERIFY_CLASS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Rmic.html#ERROR_UNABLE_TO_VERIFY_CLASS">ERROR_UNABLE_TO_VERIFY_CLASS</A></CODE></TD>
<TD ALIGN="right"><CODE>"Unable to verify class "</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/SignJar.html" title="class in org.apache.tools.ant.taskdefs">SignJar</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.SignJar.ERROR_BAD_MAP"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/SignJar.html#ERROR_BAD_MAP">ERROR_BAD_MAP</A></CODE></TD>
<TD ALIGN="right"><CODE>"Cannot map source file to anything sensible: "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.SignJar.ERROR_MAPPER_WITHOUT_DEST"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/SignJar.html#ERROR_MAPPER_WITHOUT_DEST">ERROR_MAPPER_WITHOUT_DEST</A></CODE></TD>
<TD ALIGN="right"><CODE>"The destDir attribute is required if a mapper is set"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.SignJar.ERROR_NO_ALIAS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/SignJar.html#ERROR_NO_ALIAS">ERROR_NO_ALIAS</A></CODE></TD>
<TD ALIGN="right"><CODE>"alias attribute must be set"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.SignJar.ERROR_NO_STOREPASS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/SignJar.html#ERROR_NO_STOREPASS">ERROR_NO_STOREPASS</A></CODE></TD>
<TD ALIGN="right"><CODE>"storepass attribute must be set"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.SignJar.ERROR_SIGNEDJAR_AND_PATHS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/SignJar.html#ERROR_SIGNEDJAR_AND_PATHS">ERROR_SIGNEDJAR_AND_PATHS</A></CODE></TD>
<TD ALIGN="right"><CODE>"You cannot specify the signed JAR when using paths or filesets"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.SignJar.ERROR_TODIR_AND_SIGNEDJAR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/SignJar.html#ERROR_TODIR_AND_SIGNEDJAR">ERROR_TODIR_AND_SIGNEDJAR</A></CODE></TD>
<TD ALIGN="right"><CODE>"\'destdir\' and \'signedjar\' cannot both be set"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.SignJar.ERROR_TOO_MANY_MAPPERS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/SignJar.html#ERROR_TOO_MANY_MAPPERS">ERROR_TOO_MANY_MAPPERS</A></CODE></TD>
<TD ALIGN="right"><CODE>"Too many mappers"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/SQLExec.DelimiterType.html" title="class in org.apache.tools.ant.taskdefs">SQLExec.DelimiterType</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.SQLExec.DelimiterType.NORMAL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/SQLExec.DelimiterType.html#NORMAL">NORMAL</A></CODE></TD>
<TD ALIGN="right"><CODE>"normal"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.SQLExec.DelimiterType.ROW"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/SQLExec.DelimiterType.html#ROW">ROW</A></CODE></TD>
<TD ALIGN="right"><CODE>"row"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/Tar.html" title="class in org.apache.tools.ant.taskdefs">Tar</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Tar.FAIL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Tar.html#FAIL">FAIL</A></CODE></TD>
<TD ALIGN="right"><CODE>"fail"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Tar.GNU"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Tar.html#GNU">GNU</A></CODE></TD>
<TD ALIGN="right"><CODE>"gnu"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Tar.OMIT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Tar.html#OMIT">OMIT</A></CODE></TD>
<TD ALIGN="right"><CODE>"omit"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Tar.TRUNCATE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Tar.html#TRUNCATE">TRUNCATE</A></CODE></TD>
<TD ALIGN="right"><CODE>"truncate"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Tar.WARN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Tar.html#WARN">WARN</A></CODE></TD>
<TD ALIGN="right"><CODE>"warn"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/Tar.TarLongFileMode.html" title="class in org.apache.tools.ant.taskdefs">Tar.TarLongFileMode</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Tar.TarLongFileMode.FAIL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Tar.TarLongFileMode.html#FAIL">FAIL</A></CODE></TD>
<TD ALIGN="right"><CODE>"fail"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Tar.TarLongFileMode.GNU"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Tar.TarLongFileMode.html#GNU">GNU</A></CODE></TD>
<TD ALIGN="right"><CODE>"gnu"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Tar.TarLongFileMode.OMIT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Tar.TarLongFileMode.html#OMIT">OMIT</A></CODE></TD>
<TD ALIGN="right"><CODE>"omit"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Tar.TarLongFileMode.TRUNCATE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Tar.TarLongFileMode.html#TRUNCATE">TRUNCATE</A></CODE></TD>
<TD ALIGN="right"><CODE>"truncate"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.Tar.TarLongFileMode.WARN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/Tar.TarLongFileMode.html#WARN">WARN</A></CODE></TD>
<TD ALIGN="right"><CODE>"warn"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/VerifyJar.html" title="class in org.apache.tools.ant.taskdefs">VerifyJar</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.VerifyJar.ERROR_NO_FILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/VerifyJar.html#ERROR_NO_FILE">ERROR_NO_FILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"Not found :"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.VerifyJar.ERROR_NO_VERIFY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/VerifyJar.html#ERROR_NO_VERIFY">ERROR_NO_VERIFY</A></CODE></TD>
<TD ALIGN="right"><CODE>"Failed to verify "</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/WaitFor.Unit.html" title="class in org.apache.tools.ant.taskdefs">WaitFor.Unit</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.WaitFor.Unit.DAY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/WaitFor.Unit.html#DAY">DAY</A></CODE></TD>
<TD ALIGN="right"><CODE>"day"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.WaitFor.Unit.HOUR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/WaitFor.Unit.html#HOUR">HOUR</A></CODE></TD>
<TD ALIGN="right"><CODE>"hour"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.WaitFor.Unit.MILLISECOND"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/WaitFor.Unit.html#MILLISECOND">MILLISECOND</A></CODE></TD>
<TD ALIGN="right"><CODE>"millisecond"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.WaitFor.Unit.MINUTE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/WaitFor.Unit.html#MINUTE">MINUTE</A></CODE></TD>
<TD ALIGN="right"><CODE>"minute"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.WaitFor.Unit.SECOND"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/WaitFor.Unit.html#SECOND">SECOND</A></CODE></TD>
<TD ALIGN="right"><CODE>"second"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.WaitFor.Unit.WEEK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/WaitFor.Unit.html#WEEK">WEEK</A></CODE></TD>
<TD ALIGN="right"><CODE>"week"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/XSLTLiaison.html" title="interface in org.apache.tools.ant.taskdefs">XSLTLiaison</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.XSLTLiaison.FILE_PROTOCOL_PREFIX"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/XSLTLiaison.html#FILE_PROTOCOL_PREFIX">FILE_PROTOCOL_PREFIX</A></CODE></TD>
<TD ALIGN="right"><CODE>"file://"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.<A HREF="org/apache/tools/ant/taskdefs/XSLTProcess.html" title="class in org.apache.tools.ant.taskdefs">XSLTProcess</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.XSLTProcess.PROCESSOR_TRAX"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/XSLTProcess.html#PROCESSOR_TRAX">PROCESSOR_TRAX</A></CODE></TD>
<TD ALIGN="right"><CODE>"trax"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.compilers.<A HREF="org/apache/tools/ant/taskdefs/compilers/AptCompilerAdapter.html" title="class in org.apache.tools.ant.taskdefs.compilers">AptCompilerAdapter</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.compilers.AptCompilerAdapter.APT_ENTRY_POINT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/compilers/AptCompilerAdapter.html#APT_ENTRY_POINT">APT_ENTRY_POINT</A></CODE></TD>
<TD ALIGN="right"><CODE>"com.sun.tools.apt.Main"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.compilers.AptCompilerAdapter.APT_METHOD_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/compilers/AptCompilerAdapter.html#APT_METHOD_NAME">APT_METHOD_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"process"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.compilers.<A HREF="org/apache/tools/ant/taskdefs/compilers/Javac12.html" title="class in org.apache.tools.ant.taskdefs.compilers">Javac12</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.compilers.Javac12.CLASSIC_COMPILER_CLASSNAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/compilers/Javac12.html#CLASSIC_COMPILER_CLASSNAME">CLASSIC_COMPILER_CLASSNAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"sun.tools.javac.Main"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.condition.<A HREF="org/apache/tools/ant/taskdefs/condition/IsReachable.html" title="class in org.apache.tools.ant.taskdefs.condition">IsReachable</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.IsReachable.DEFAULT_TIMEOUT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/IsReachable.html#DEFAULT_TIMEOUT">DEFAULT_TIMEOUT</A></CODE></TD>
<TD ALIGN="right"><CODE>30</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.IsReachable.ERROR_BAD_TIMEOUT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/IsReachable.html#ERROR_BAD_TIMEOUT">ERROR_BAD_TIMEOUT</A></CODE></TD>
<TD ALIGN="right"><CODE>"Invalid timeout value"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.IsReachable.ERROR_BAD_URL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/IsReachable.html#ERROR_BAD_URL">ERROR_BAD_URL</A></CODE></TD>
<TD ALIGN="right"><CODE>"Bad URL "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.IsReachable.ERROR_BOTH_TARGETS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/IsReachable.html#ERROR_BOTH_TARGETS">ERROR_BOTH_TARGETS</A></CODE></TD>
<TD ALIGN="right"><CODE>"Both url and host have been specified"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.IsReachable.ERROR_NO_HOST_IN_URL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/IsReachable.html#ERROR_NO_HOST_IN_URL">ERROR_NO_HOST_IN_URL</A></CODE></TD>
<TD ALIGN="right"><CODE>"No hostname in URL "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.IsReachable.ERROR_NO_HOSTNAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/IsReachable.html#ERROR_NO_HOSTNAME">ERROR_NO_HOSTNAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"No hostname defined"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.IsReachable.ERROR_ON_NETWORK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/IsReachable.html#ERROR_ON_NETWORK">ERROR_ON_NETWORK</A></CODE></TD>
<TD ALIGN="right"><CODE>"network error to "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.IsReachable.METHOD_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/IsReachable.html#METHOD_NAME">METHOD_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"isReachable"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.IsReachable.MSG_NO_REACHABLE_TEST"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/IsReachable.html#MSG_NO_REACHABLE_TEST">MSG_NO_REACHABLE_TEST</A></CODE></TD>
<TD ALIGN="right"><CODE>"cannot do a proper reachability test on this Java version"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.condition.<A HREF="org/apache/tools/ant/taskdefs/condition/Os.html" title="class in org.apache.tools.ant.taskdefs.condition">Os</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.Os.FAMILY_9X"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/Os.html#FAMILY_9X">FAMILY_9X</A></CODE></TD>
<TD ALIGN="right"><CODE>"win9x"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.Os.FAMILY_DOS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/Os.html#FAMILY_DOS">FAMILY_DOS</A></CODE></TD>
<TD ALIGN="right"><CODE>"dos"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.Os.FAMILY_MAC"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/Os.html#FAMILY_MAC">FAMILY_MAC</A></CODE></TD>
<TD ALIGN="right"><CODE>"mac"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.Os.FAMILY_NETWARE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/Os.html#FAMILY_NETWARE">FAMILY_NETWARE</A></CODE></TD>
<TD ALIGN="right"><CODE>"netware"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.Os.FAMILY_NT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/Os.html#FAMILY_NT">FAMILY_NT</A></CODE></TD>
<TD ALIGN="right"><CODE>"winnt"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.Os.FAMILY_OS2"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/Os.html#FAMILY_OS2">FAMILY_OS2</A></CODE></TD>
<TD ALIGN="right"><CODE>"os/2"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.Os.FAMILY_OS400"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/Os.html#FAMILY_OS400">FAMILY_OS400</A></CODE></TD>
<TD ALIGN="right"><CODE>"os/400"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.Os.FAMILY_TANDEM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/Os.html#FAMILY_TANDEM">FAMILY_TANDEM</A></CODE></TD>
<TD ALIGN="right"><CODE>"tandem"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.Os.FAMILY_UNIX"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/Os.html#FAMILY_UNIX">FAMILY_UNIX</A></CODE></TD>
<TD ALIGN="right"><CODE>"unix"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.Os.FAMILY_VMS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/Os.html#FAMILY_VMS">FAMILY_VMS</A></CODE></TD>
<TD ALIGN="right"><CODE>"openvms"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.Os.FAMILY_WINDOWS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/Os.html#FAMILY_WINDOWS">FAMILY_WINDOWS</A></CODE></TD>
<TD ALIGN="right"><CODE>"windows"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.Os.FAMILY_ZOS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/Os.html#FAMILY_ZOS">FAMILY_ZOS</A></CODE></TD>
<TD ALIGN="right"><CODE>"z/os"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.condition.<A HREF="org/apache/tools/ant/taskdefs/condition/ParserSupports.html" title="class in org.apache.tools.ant.taskdefs.condition">ParserSupports</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.ParserSupports.ERROR_BOTH_ATTRIBUTES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/ParserSupports.html#ERROR_BOTH_ATTRIBUTES">ERROR_BOTH_ATTRIBUTES</A></CODE></TD>
<TD ALIGN="right"><CODE>"Property and feature attributes are exclusive"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.ParserSupports.ERROR_NO_ATTRIBUTES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/ParserSupports.html#ERROR_NO_ATTRIBUTES">ERROR_NO_ATTRIBUTES</A></CODE></TD>
<TD ALIGN="right"><CODE>"Neither feature or property are set"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.ParserSupports.ERROR_NO_VALUE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/ParserSupports.html#ERROR_NO_VALUE">ERROR_NO_VALUE</A></CODE></TD>
<TD ALIGN="right"><CODE>"A value is needed when testing for property support"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.ParserSupports.FEATURE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/ParserSupports.html#FEATURE">FEATURE</A></CODE></TD>
<TD ALIGN="right"><CODE>"feature"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.ParserSupports.NOT_RECOGNIZED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/ParserSupports.html#NOT_RECOGNIZED">NOT_RECOGNIZED</A></CODE></TD>
<TD ALIGN="right"><CODE>" not recognized: "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.ParserSupports.NOT_SUPPORTED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/ParserSupports.html#NOT_SUPPORTED">NOT_SUPPORTED</A></CODE></TD>
<TD ALIGN="right"><CODE>" not supported: "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.condition.ParserSupports.PROPERTY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/condition/ParserSupports.html#PROPERTY">PROPERTY</A></CODE></TD>
<TD ALIGN="right"><CODE>"property"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.email.<A HREF="org/apache/tools/ant/taskdefs/email/EmailTask.html" title="class in org.apache.tools.ant.taskdefs.email">EmailTask</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.email.EmailTask.AUTO"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/email/EmailTask.html#AUTO">AUTO</A></CODE></TD>
<TD ALIGN="right"><CODE>"auto"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.email.EmailTask.MIME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/email/EmailTask.html#MIME">MIME</A></CODE></TD>
<TD ALIGN="right"><CODE>"mime"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.email.EmailTask.PLAIN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/email/EmailTask.html#PLAIN">PLAIN</A></CODE></TD>
<TD ALIGN="right"><CODE>"plain"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.email.EmailTask.UU"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/email/EmailTask.html#UU">UU</A></CODE></TD>
<TD ALIGN="right"><CODE>"uu"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.<A HREF="org/apache/tools/ant/taskdefs/optional/PropertyFile.Entry.Operation.html" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Entry.Operation</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.PropertyFile.Entry.Operation.DECREMENT_OPER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/PropertyFile.Entry.Operation.html#DECREMENT_OPER">DECREMENT_OPER</A></CODE></TD>
<TD ALIGN="right"><CODE>1</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.PropertyFile.Entry.Operation.EQUALS_OPER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/PropertyFile.Entry.Operation.html#EQUALS_OPER">EQUALS_OPER</A></CODE></TD>
<TD ALIGN="right"><CODE>2</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.PropertyFile.Entry.Operation.INCREMENT_OPER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/PropertyFile.Entry.Operation.html#INCREMENT_OPER">INCREMENT_OPER</A></CODE></TD>
<TD ALIGN="right"><CODE>0</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.<A HREF="org/apache/tools/ant/taskdefs/optional/PropertyFile.Entry.Type.html" title="class in org.apache.tools.ant.taskdefs.optional">PropertyFile.Entry.Type</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.PropertyFile.Entry.Type.DATE_TYPE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/PropertyFile.Entry.Type.html#DATE_TYPE">DATE_TYPE</A></CODE></TD>
<TD ALIGN="right"><CODE>1</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.PropertyFile.Entry.Type.INTEGER_TYPE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/PropertyFile.Entry.Type.html#INTEGER_TYPE">INTEGER_TYPE</A></CODE></TD>
<TD ALIGN="right"><CODE>0</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.PropertyFile.Entry.Type.STRING_TYPE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/PropertyFile.Entry.Type.html#STRING_TYPE">STRING_TYPE</A></CODE></TD>
<TD ALIGN="right"><CODE>2</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.<A HREF="org/apache/tools/ant/taskdefs/optional/SchemaValidate.html" title="class in org.apache.tools.ant.taskdefs.optional">SchemaValidate</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.SchemaValidate.ERROR_DUPLICATE_SCHEMA"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/SchemaValidate.html#ERROR_DUPLICATE_SCHEMA">ERROR_DUPLICATE_SCHEMA</A></CODE></TD>
<TD ALIGN="right"><CODE>"Duplicate declaration of schema "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.SchemaValidate.ERROR_NO_XSD_SUPPORT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/SchemaValidate.html#ERROR_NO_XSD_SUPPORT">ERROR_NO_XSD_SUPPORT</A></CODE></TD>
<TD ALIGN="right"><CODE>"Parser does not support Xerces or JAXP schema features"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.SchemaValidate.ERROR_PARSER_CREATION_FAILURE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/SchemaValidate.html#ERROR_PARSER_CREATION_FAILURE">ERROR_PARSER_CREATION_FAILURE</A></CODE></TD>
<TD ALIGN="right"><CODE>"Could not create parser"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.SchemaValidate.ERROR_SAX_1"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/SchemaValidate.html#ERROR_SAX_1">ERROR_SAX_1</A></CODE></TD>
<TD ALIGN="right"><CODE>"SAX1 parsers are not supported"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.SchemaValidate.ERROR_TOO_MANY_DEFAULT_SCHEMAS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/SchemaValidate.html#ERROR_TOO_MANY_DEFAULT_SCHEMAS">ERROR_TOO_MANY_DEFAULT_SCHEMAS</A></CODE></TD>
<TD ALIGN="right"><CODE>"Only one of defaultSchemaFile and defaultSchemaURL allowed"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.SchemaValidate.MESSAGE_ADDING_SCHEMA"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/SchemaValidate.html#MESSAGE_ADDING_SCHEMA">MESSAGE_ADDING_SCHEMA</A></CODE></TD>
<TD ALIGN="right"><CODE>"Adding schema "</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.<A HREF="org/apache/tools/ant/taskdefs/optional/SchemaValidate.SchemaLocation.html" title="class in org.apache.tools.ant.taskdefs.optional">SchemaValidate.SchemaLocation</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.SchemaValidate.SchemaLocation.ERROR_NO_FILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/SchemaValidate.SchemaLocation.html#ERROR_NO_FILE">ERROR_NO_FILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"File not found: "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.SchemaValidate.SchemaLocation.ERROR_NO_LOCATION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/SchemaValidate.SchemaLocation.html#ERROR_NO_LOCATION">ERROR_NO_LOCATION</A></CODE></TD>
<TD ALIGN="right"><CODE>"No file or URL supplied for the schema "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.SchemaValidate.SchemaLocation.ERROR_NO_URI"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/SchemaValidate.SchemaLocation.html#ERROR_NO_URI">ERROR_NO_URI</A></CODE></TD>
<TD ALIGN="right"><CODE>"No namespace URI"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.SchemaValidate.SchemaLocation.ERROR_NO_URL_REPRESENTATION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/SchemaValidate.SchemaLocation.html#ERROR_NO_URL_REPRESENTATION">ERROR_NO_URL_REPRESENTATION</A></CODE></TD>
<TD ALIGN="right"><CODE>"Cannot make a URL of "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.SchemaValidate.SchemaLocation.ERROR_TWO_LOCATIONS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/SchemaValidate.SchemaLocation.html#ERROR_TWO_LOCATIONS">ERROR_TWO_LOCATIONS</A></CODE></TD>
<TD ALIGN="right"><CODE>"Both URL and File were given for schema "</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.<A HREF="org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html" title="class in org.apache.tools.ant.taskdefs.optional">XMLValidateTask</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.XMLValidateTask.INIT_FAILED_MSG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#INIT_FAILED_MSG">INIT_FAILED_MSG</A></CODE></TD>
<TD ALIGN="right"><CODE>"Could not start xml validation: "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.XMLValidateTask.MESSAGE_FILES_VALIDATED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/XMLValidateTask.html#MESSAGE_FILES_VALIDATED">MESSAGE_FILES_VALIDATED</A></CODE></TD>
<TD ALIGN="right"><CODE>" file(s) have been successfully validated."</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.ccm.<A HREF="org/apache/tools/ant/taskdefs/optional/ccm/CCMCheck.html" title="class in org.apache.tools.ant.taskdefs.optional.ccm">CCMCheck</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.CCMCheck.FLAG_COMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/CCMCheck.html#FLAG_COMMENT">FLAG_COMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"/comment"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.CCMCheck.FLAG_TASK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/CCMCheck.html#FLAG_TASK">FLAG_TASK</A></CODE></TD>
<TD ALIGN="right"><CODE>"/task"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.ccm.<A HREF="org/apache/tools/ant/taskdefs/optional/ccm/CCMCheckinDefault.html" title="class in org.apache.tools.ant.taskdefs.optional.ccm">CCMCheckinDefault</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.CCMCheckinDefault.DEFAULT_TASK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/CCMCheckinDefault.html#DEFAULT_TASK">DEFAULT_TASK</A></CODE></TD>
<TD ALIGN="right"><CODE>"default"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.ccm.<A HREF="org/apache/tools/ant/taskdefs/optional/ccm/CCMCreateTask.html" title="class in org.apache.tools.ant.taskdefs.optional.ccm">CCMCreateTask</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.CCMCreateTask.FLAG_COMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/CCMCreateTask.html#FLAG_COMMENT">FLAG_COMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"/synopsis"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.CCMCreateTask.FLAG_PLATFORM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/CCMCreateTask.html#FLAG_PLATFORM">FLAG_PLATFORM</A></CODE></TD>
<TD ALIGN="right"><CODE>"/plat"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.CCMCreateTask.FLAG_RELEASE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/CCMCreateTask.html#FLAG_RELEASE">FLAG_RELEASE</A></CODE></TD>
<TD ALIGN="right"><CODE>"/release"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.CCMCreateTask.FLAG_RESOLVER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/CCMCreateTask.html#FLAG_RESOLVER">FLAG_RESOLVER</A></CODE></TD>
<TD ALIGN="right"><CODE>"/resolver"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.CCMCreateTask.FLAG_SUBSYSTEM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/CCMCreateTask.html#FLAG_SUBSYSTEM">FLAG_SUBSYSTEM</A></CODE></TD>
<TD ALIGN="right"><CODE>"/subsystem"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.CCMCreateTask.FLAG_TASK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/CCMCreateTask.html#FLAG_TASK">FLAG_TASK</A></CODE></TD>
<TD ALIGN="right"><CODE>"/task"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.ccm.<A HREF="org/apache/tools/ant/taskdefs/optional/ccm/CCMReconfigure.html" title="class in org.apache.tools.ant.taskdefs.optional.ccm">CCMReconfigure</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.CCMReconfigure.FLAG_PROJECT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/CCMReconfigure.html#FLAG_PROJECT">FLAG_PROJECT</A></CODE></TD>
<TD ALIGN="right"><CODE>"/project"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.CCMReconfigure.FLAG_RECURSE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/CCMReconfigure.html#FLAG_RECURSE">FLAG_RECURSE</A></CODE></TD>
<TD ALIGN="right"><CODE>"/recurse"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.CCMReconfigure.FLAG_VERBOSE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/CCMReconfigure.html#FLAG_VERBOSE">FLAG_VERBOSE</A></CODE></TD>
<TD ALIGN="right"><CODE>"/verbose"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.ccm.<A HREF="org/apache/tools/ant/taskdefs/optional/ccm/Continuus.html" title="class in org.apache.tools.ant.taskdefs.optional.ccm">Continuus</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.Continuus.COMMAND_CHECKIN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/Continuus.html#COMMAND_CHECKIN">COMMAND_CHECKIN</A></CODE></TD>
<TD ALIGN="right"><CODE>"ci"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.Continuus.COMMAND_CHECKOUT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/Continuus.html#COMMAND_CHECKOUT">COMMAND_CHECKOUT</A></CODE></TD>
<TD ALIGN="right"><CODE>"co"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.Continuus.COMMAND_CREATE_TASK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/Continuus.html#COMMAND_CREATE_TASK">COMMAND_CREATE_TASK</A></CODE></TD>
<TD ALIGN="right"><CODE>"create_task"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.Continuus.COMMAND_DEFAULT_TASK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/Continuus.html#COMMAND_DEFAULT_TASK">COMMAND_DEFAULT_TASK</A></CODE></TD>
<TD ALIGN="right"><CODE>"default_task"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ccm.Continuus.COMMAND_RECONFIGURE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ccm/Continuus.html#COMMAND_RECONFIGURE">COMMAND_RECONFIGURE</A></CODE></TD>
<TD ALIGN="right"><CODE>"reconfigure"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.clearcase.<A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckin.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCCheckin</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckin.FLAG_COMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckin.html#FLAG_COMMENT">FLAG_COMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-c"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckin.FLAG_COMMENTFILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckin.html#FLAG_COMMENTFILE">FLAG_COMMENTFILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-cfile"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckin.FLAG_IDENTICAL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckin.html#FLAG_IDENTICAL">FLAG_IDENTICAL</A></CODE></TD>
<TD ALIGN="right"><CODE>"-identical"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckin.FLAG_KEEPCOPY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckin.html#FLAG_KEEPCOPY">FLAG_KEEPCOPY</A></CODE></TD>
<TD ALIGN="right"><CODE>"-keep"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckin.FLAG_NOCOMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckin.html#FLAG_NOCOMMENT">FLAG_NOCOMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nc"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckin.FLAG_NOWARN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckin.html#FLAG_NOWARN">FLAG_NOWARN</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nwarn"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckin.FLAG_PRESERVETIME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckin.html#FLAG_PRESERVETIME">FLAG_PRESERVETIME</A></CODE></TD>
<TD ALIGN="right"><CODE>"-ptime"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.clearcase.<A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckout.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCCheckout</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckout.FLAG_BRANCH"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckout.html#FLAG_BRANCH">FLAG_BRANCH</A></CODE></TD>
<TD ALIGN="right"><CODE>"-branch"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckout.FLAG_COMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckout.html#FLAG_COMMENT">FLAG_COMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-c"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckout.FLAG_COMMENTFILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckout.html#FLAG_COMMENTFILE">FLAG_COMMENTFILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-cfile"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckout.FLAG_NOCOMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckout.html#FLAG_NOCOMMENT">FLAG_NOCOMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nc"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckout.FLAG_NODATA"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckout.html#FLAG_NODATA">FLAG_NODATA</A></CODE></TD>
<TD ALIGN="right"><CODE>"-ndata"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckout.FLAG_NOWARN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckout.html#FLAG_NOWARN">FLAG_NOWARN</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nwarn"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckout.FLAG_OUT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckout.html#FLAG_OUT">FLAG_OUT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-out"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckout.FLAG_RESERVED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckout.html#FLAG_RESERVED">FLAG_RESERVED</A></CODE></TD>
<TD ALIGN="right"><CODE>"-reserved"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckout.FLAG_UNRESERVED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckout.html#FLAG_UNRESERVED">FLAG_UNRESERVED</A></CODE></TD>
<TD ALIGN="right"><CODE>"-unreserved"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCCheckout.FLAG_VERSION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCCheckout.html#FLAG_VERSION">FLAG_VERSION</A></CODE></TD>
<TD ALIGN="right"><CODE>"-version"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.clearcase.<A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCLock.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCLock</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCLock.FLAG_COMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCLock.html#FLAG_COMMENT">FLAG_COMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-comment"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCLock.FLAG_NUSERS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCLock.html#FLAG_NUSERS">FLAG_NUSERS</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nusers"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCLock.FLAG_OBSOLETE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCLock.html#FLAG_OBSOLETE">FLAG_OBSOLETE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-obsolete"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCLock.FLAG_PNAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCLock.html#FLAG_PNAME">FLAG_PNAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"-pname"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCLock.FLAG_REPLACE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCLock.html#FLAG_REPLACE">FLAG_REPLACE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-replace"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.clearcase.<A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMkattr</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkattr.FLAG_COMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#FLAG_COMMENT">FLAG_COMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-c"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkattr.FLAG_COMMENTFILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#FLAG_COMMENTFILE">FLAG_COMMENTFILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-cfile"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkattr.FLAG_NOCOMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#FLAG_NOCOMMENT">FLAG_NOCOMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nc"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkattr.FLAG_RECURSE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#FLAG_RECURSE">FLAG_RECURSE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-recurse"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkattr.FLAG_REPLACE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#FLAG_REPLACE">FLAG_REPLACE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-replace"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkattr.FLAG_VERSION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkattr.html#FLAG_VERSION">FLAG_VERSION</A></CODE></TD>
<TD ALIGN="right"><CODE>"-version"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.clearcase.<A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkbl.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMkbl</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkbl.FLAG_COMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkbl.html#FLAG_COMMENT">FLAG_COMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-c"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkbl.FLAG_COMMENTFILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkbl.html#FLAG_COMMENTFILE">FLAG_COMMENTFILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-cfile"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkbl.FLAG_FULL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkbl.html#FLAG_FULL">FLAG_FULL</A></CODE></TD>
<TD ALIGN="right"><CODE>"-full"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkbl.FLAG_IDENTICAL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkbl.html#FLAG_IDENTICAL">FLAG_IDENTICAL</A></CODE></TD>
<TD ALIGN="right"><CODE>"-identical"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkbl.FLAG_INCREMENTAL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkbl.html#FLAG_INCREMENTAL">FLAG_INCREMENTAL</A></CODE></TD>
<TD ALIGN="right"><CODE>"-incremental"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkbl.FLAG_NLABEL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkbl.html#FLAG_NLABEL">FLAG_NLABEL</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nlabel"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkbl.FLAG_NOCOMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkbl.html#FLAG_NOCOMMENT">FLAG_NOCOMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nc"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.clearcase.<A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkdir.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMkdir</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkdir.FLAG_COMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkdir.html#FLAG_COMMENT">FLAG_COMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-c"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkdir.FLAG_COMMENTFILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkdir.html#FLAG_COMMENTFILE">FLAG_COMMENTFILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-cfile"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkdir.FLAG_NOCHECKOUT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkdir.html#FLAG_NOCHECKOUT">FLAG_NOCHECKOUT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nco"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkdir.FLAG_NOCOMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkdir.html#FLAG_NOCOMMENT">FLAG_NOCOMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nc"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.clearcase.<A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkelem.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMkelem</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkelem.FLAG_CHECKIN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkelem.html#FLAG_CHECKIN">FLAG_CHECKIN</A></CODE></TD>
<TD ALIGN="right"><CODE>"-ci"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkelem.FLAG_COMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkelem.html#FLAG_COMMENT">FLAG_COMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-c"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkelem.FLAG_COMMENTFILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkelem.html#FLAG_COMMENTFILE">FLAG_COMMENTFILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-cfile"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkelem.FLAG_ELTYPE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkelem.html#FLAG_ELTYPE">FLAG_ELTYPE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-eltype"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkelem.FLAG_MASTER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkelem.html#FLAG_MASTER">FLAG_MASTER</A></CODE></TD>
<TD ALIGN="right"><CODE>"-master"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkelem.FLAG_NOCHECKOUT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkelem.html#FLAG_NOCHECKOUT">FLAG_NOCHECKOUT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nco"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkelem.FLAG_NOCOMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkelem.html#FLAG_NOCOMMENT">FLAG_NOCOMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nc"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkelem.FLAG_NOWARN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkelem.html#FLAG_NOWARN">FLAG_NOWARN</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nwarn"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMkelem.FLAG_PRESERVETIME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMkelem.html#FLAG_PRESERVETIME">FLAG_PRESERVETIME</A></CODE></TD>
<TD ALIGN="right"><CODE>"-ptime"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.clearcase.<A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklabel.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMklabel</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMklabel.FLAG_COMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklabel.html#FLAG_COMMENT">FLAG_COMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-c"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMklabel.FLAG_COMMENTFILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklabel.html#FLAG_COMMENTFILE">FLAG_COMMENTFILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-cfile"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMklabel.FLAG_NOCOMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklabel.html#FLAG_NOCOMMENT">FLAG_NOCOMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nc"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMklabel.FLAG_RECURSE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklabel.html#FLAG_RECURSE">FLAG_RECURSE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-recurse"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMklabel.FLAG_REPLACE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklabel.html#FLAG_REPLACE">FLAG_REPLACE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-replace"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMklabel.FLAG_VERSION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklabel.html#FLAG_VERSION">FLAG_VERSION</A></CODE></TD>
<TD ALIGN="right"><CODE>"-version"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.clearcase.<A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklbtype.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCMklbtype</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMklbtype.FLAG_COMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklbtype.html#FLAG_COMMENT">FLAG_COMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-c"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMklbtype.FLAG_COMMENTFILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklbtype.html#FLAG_COMMENTFILE">FLAG_COMMENTFILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-cfile"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMklbtype.FLAG_GLOBAL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklbtype.html#FLAG_GLOBAL">FLAG_GLOBAL</A></CODE></TD>
<TD ALIGN="right"><CODE>"-global"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMklbtype.FLAG_NOCOMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklbtype.html#FLAG_NOCOMMENT">FLAG_NOCOMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nc"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMklbtype.FLAG_ORDINARY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklbtype.html#FLAG_ORDINARY">FLAG_ORDINARY</A></CODE></TD>
<TD ALIGN="right"><CODE>"-ordinary"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMklbtype.FLAG_PBRANCH"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklbtype.html#FLAG_PBRANCH">FLAG_PBRANCH</A></CODE></TD>
<TD ALIGN="right"><CODE>"-pbranch"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMklbtype.FLAG_REPLACE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklbtype.html#FLAG_REPLACE">FLAG_REPLACE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-replace"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCMklbtype.FLAG_SHARED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCMklbtype.html#FLAG_SHARED">FLAG_SHARED</A></CODE></TD>
<TD ALIGN="right"><CODE>"-shared"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.clearcase.<A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCRmtype.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCRmtype</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCRmtype.FLAG_COMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCRmtype.html#FLAG_COMMENT">FLAG_COMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-c"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCRmtype.FLAG_COMMENTFILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCRmtype.html#FLAG_COMMENTFILE">FLAG_COMMENTFILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-cfile"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCRmtype.FLAG_FORCE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCRmtype.html#FLAG_FORCE">FLAG_FORCE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-force"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCRmtype.FLAG_IGNORE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCRmtype.html#FLAG_IGNORE">FLAG_IGNORE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-ignore"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCRmtype.FLAG_NOCOMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCRmtype.html#FLAG_NOCOMMENT">FLAG_NOCOMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nc"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCRmtype.FLAG_RMALL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCRmtype.html#FLAG_RMALL">FLAG_RMALL</A></CODE></TD>
<TD ALIGN="right"><CODE>"-rmall"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.clearcase.<A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCUnCheckout.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCUnCheckout</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCUnCheckout.FLAG_KEEPCOPY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCUnCheckout.html#FLAG_KEEPCOPY">FLAG_KEEPCOPY</A></CODE></TD>
<TD ALIGN="right"><CODE>"-keep"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCUnCheckout.FLAG_RM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCUnCheckout.html#FLAG_RM">FLAG_RM</A></CODE></TD>
<TD ALIGN="right"><CODE>"-rm"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.clearcase.<A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCUnlock.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCUnlock</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCUnlock.FLAG_COMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCUnlock.html#FLAG_COMMENT">FLAG_COMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-comment"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCUnlock.FLAG_PNAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCUnlock.html#FLAG_PNAME">FLAG_PNAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"-pname"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.clearcase.<A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCUpdate.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">CCUpdate</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCUpdate.FLAG_CURRENTTIME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCUpdate.html#FLAG_CURRENTTIME">FLAG_CURRENTTIME</A></CODE></TD>
<TD ALIGN="right"><CODE>"-ctime"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCUpdate.FLAG_GRAPHICAL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCUpdate.html#FLAG_GRAPHICAL">FLAG_GRAPHICAL</A></CODE></TD>
<TD ALIGN="right"><CODE>"-graphical"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCUpdate.FLAG_LOG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCUpdate.html#FLAG_LOG">FLAG_LOG</A></CODE></TD>
<TD ALIGN="right"><CODE>"-log"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCUpdate.FLAG_NOVERWRITE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCUpdate.html#FLAG_NOVERWRITE">FLAG_NOVERWRITE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-noverwrite"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCUpdate.FLAG_OVERWRITE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCUpdate.html#FLAG_OVERWRITE">FLAG_OVERWRITE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-overwrite"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCUpdate.FLAG_PRESERVETIME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCUpdate.html#FLAG_PRESERVETIME">FLAG_PRESERVETIME</A></CODE></TD>
<TD ALIGN="right"><CODE>"-ptime"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.CCUpdate.FLAG_RENAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/CCUpdate.html#FLAG_RENAME">FLAG_RENAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"-rename"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.clearcase.<A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html" title="class in org.apache.tools.ant.taskdefs.optional.clearcase">ClearCase</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase.COMMAND_CHECKIN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_CHECKIN">COMMAND_CHECKIN</A></CODE></TD>
<TD ALIGN="right"><CODE>"checkin"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase.COMMAND_CHECKOUT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_CHECKOUT">COMMAND_CHECKOUT</A></CODE></TD>
<TD ALIGN="right"><CODE>"checkout"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase.COMMAND_LOCK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_LOCK">COMMAND_LOCK</A></CODE></TD>
<TD ALIGN="right"><CODE>"lock"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase.COMMAND_LSCO"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_LSCO">COMMAND_LSCO</A></CODE></TD>
<TD ALIGN="right"><CODE>"lsco"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase.COMMAND_MKATTR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_MKATTR">COMMAND_MKATTR</A></CODE></TD>
<TD ALIGN="right"><CODE>"mkattr"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase.COMMAND_MKBL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_MKBL">COMMAND_MKBL</A></CODE></TD>
<TD ALIGN="right"><CODE>"mkbl"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase.COMMAND_MKDIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_MKDIR">COMMAND_MKDIR</A></CODE></TD>
<TD ALIGN="right"><CODE>"mkdir"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase.COMMAND_MKELEM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_MKELEM">COMMAND_MKELEM</A></CODE></TD>
<TD ALIGN="right"><CODE>"mkelem"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase.COMMAND_MKLABEL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_MKLABEL">COMMAND_MKLABEL</A></CODE></TD>
<TD ALIGN="right"><CODE>"mklabel"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase.COMMAND_MKLBTYPE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_MKLBTYPE">COMMAND_MKLBTYPE</A></CODE></TD>
<TD ALIGN="right"><CODE>"mklbtype"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase.COMMAND_RMTYPE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_RMTYPE">COMMAND_RMTYPE</A></CODE></TD>
<TD ALIGN="right"><CODE>"rmtype"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase.COMMAND_UNCHECKOUT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_UNCHECKOUT">COMMAND_UNCHECKOUT</A></CODE></TD>
<TD ALIGN="right"><CODE>"uncheckout"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase.COMMAND_UNLOCK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_UNLOCK">COMMAND_UNLOCK</A></CODE></TD>
<TD ALIGN="right"><CODE>"unlock"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.clearcase.ClearCase.COMMAND_UPDATE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/clearcase/ClearCase.html#COMMAND_UPDATE">COMMAND_UPDATE</A></CODE></TD>
<TD ALIGN="right"><CODE>"update"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.depend.constantpool.<A HREF="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html" title="class in org.apache.tools.ant.taskdefs.optional.depend.constantpool">ConstantPoolEntry</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_CLASS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_CLASS">CONSTANT_CLASS</A></CODE></TD>
<TD ALIGN="right"><CODE>7</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_DOUBLE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_DOUBLE">CONSTANT_DOUBLE</A></CODE></TD>
<TD ALIGN="right"><CODE>6</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_FIELDREF"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_FIELDREF">CONSTANT_FIELDREF</A></CODE></TD>
<TD ALIGN="right"><CODE>9</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_FLOAT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_FLOAT">CONSTANT_FLOAT</A></CODE></TD>
<TD ALIGN="right"><CODE>4</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_INTEGER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_INTEGER">CONSTANT_INTEGER</A></CODE></TD>
<TD ALIGN="right"><CODE>3</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_INTERFACEMETHODREF"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_INTERFACEMETHODREF">CONSTANT_INTERFACEMETHODREF</A></CODE></TD>
<TD ALIGN="right"><CODE>11</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_LONG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_LONG">CONSTANT_LONG</A></CODE></TD>
<TD ALIGN="right"><CODE>5</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_METHODREF"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_METHODREF">CONSTANT_METHODREF</A></CODE></TD>
<TD ALIGN="right"><CODE>10</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_NAMEANDTYPE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_NAMEANDTYPE">CONSTANT_NAMEANDTYPE</A></CODE></TD>
<TD ALIGN="right"><CODE>12</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_STRING"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_STRING">CONSTANT_STRING</A></CODE></TD>
<TD ALIGN="right"><CODE>8</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.depend.constantpool.ConstantPoolEntry.CONSTANT_UTF8"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/depend/constantpool/ConstantPoolEntry.html#CONSTANT_UTF8">CONSTANT_UTF8</A></CODE></TD>
<TD ALIGN="right"><CODE>1</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.dotnet.<A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/DotnetCompile.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">DotnetCompile</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.DotnetCompile.REFERENCE_OPTION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/DotnetCompile.html#REFERENCE_OPTION">REFERENCE_OPTION</A></CODE></TD>
<TD ALIGN="right"><CODE>"/reference:"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.dotnet.<A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/Ilasm.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">Ilasm</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.Ilasm.exe_name"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/Ilasm.html#exe_name">exe_name</A></CODE></TD>
<TD ALIGN="right"><CODE>"ilasm"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.Ilasm.exe_title"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/Ilasm.html#exe_title">exe_title</A></CODE></TD>
<TD ALIGN="right"><CODE>"ilasm"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.Ilasm.file_ext"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/Ilasm.html#file_ext">file_ext</A></CODE></TD>
<TD ALIGN="right"><CODE>"il"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.Ilasm.file_pattern"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/Ilasm.html#file_pattern">file_pattern</A></CODE></TD>
<TD ALIGN="right"><CODE>"**/*.il"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.dotnet.<A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/Ildasm.EncodingTypes.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">Ildasm.EncodingTypes</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.Ildasm.EncodingTypes.ASCII"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/Ildasm.EncodingTypes.html#ASCII">ASCII</A></CODE></TD>
<TD ALIGN="right"><CODE>"ascii"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.Ildasm.EncodingTypes.UNICODE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/Ildasm.EncodingTypes.html#UNICODE">UNICODE</A></CODE></TD>
<TD ALIGN="right"><CODE>"unicode"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.Ildasm.EncodingTypes.UTF8"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/Ildasm.EncodingTypes.html#UTF8">UTF8</A></CODE></TD>
<TD ALIGN="right"><CODE>"utf8"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.dotnet.<A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/WsdlToDotnet.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">WsdlToDotnet</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.WsdlToDotnet.ERROR_DEST_FILE_IS_DIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/WsdlToDotnet.html#ERROR_DEST_FILE_IS_DIR">ERROR_DEST_FILE_IS_DIR</A></CODE></TD>
<TD ALIGN="right"><CODE>"destination file is a directory"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.WsdlToDotnet.ERROR_NO_DEST_FILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/WsdlToDotnet.html#ERROR_NO_DEST_FILE">ERROR_NO_DEST_FILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"destination file must be specified"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.dotnet.<A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/WsdlToDotnet.Compiler.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">WsdlToDotnet.Compiler</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.WsdlToDotnet.Compiler.COMPILER_MONO"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/WsdlToDotnet.Compiler.html#COMPILER_MONO">COMPILER_MONO</A></CODE></TD>
<TD ALIGN="right"><CODE>"mono"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.WsdlToDotnet.Compiler.COMPILER_MS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/WsdlToDotnet.Compiler.html#COMPILER_MS">COMPILER_MS</A></CODE></TD>
<TD ALIGN="right"><CODE>"microsoft"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.WsdlToDotnet.Compiler.COMPILER_MS_ON_MONO"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/WsdlToDotnet.Compiler.html#COMPILER_MS_ON_MONO">COMPILER_MS_ON_MONO</A></CODE></TD>
<TD ALIGN="right"><CODE>"microsoft-on-mono"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.WsdlToDotnet.Compiler.EXE_MONO"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/WsdlToDotnet.Compiler.html#EXE_MONO">EXE_MONO</A></CODE></TD>
<TD ALIGN="right"><CODE>"mono"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.WsdlToDotnet.Compiler.EXE_WSDL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/WsdlToDotnet.Compiler.html#EXE_WSDL">EXE_WSDL</A></CODE></TD>
<TD ALIGN="right"><CODE>"wsdl"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.dotnet.<A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/WsdlToDotnet.Schema.html" title="class in org.apache.tools.ant.taskdefs.optional.dotnet">WsdlToDotnet.Schema</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.WsdlToDotnet.Schema.ERROR_BOTH_DECLARED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/WsdlToDotnet.Schema.html#ERROR_BOTH_DECLARED">ERROR_BOTH_DECLARED</A></CODE></TD>
<TD ALIGN="right"><CODE>"Only one of file or url can be set"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.WsdlToDotnet.Schema.ERROR_FILE_IS_DIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/WsdlToDotnet.Schema.html#ERROR_FILE_IS_DIR">ERROR_FILE_IS_DIR</A></CODE></TD>
<TD ALIGN="right"><CODE>"File is a directory: "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.WsdlToDotnet.Schema.ERROR_FILE_NOT_FOUND"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/WsdlToDotnet.Schema.html#ERROR_FILE_NOT_FOUND">ERROR_FILE_NOT_FOUND</A></CODE></TD>
<TD ALIGN="right"><CODE>"Not found: "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.WsdlToDotnet.Schema.ERROR_NO_URL_CONVERT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/WsdlToDotnet.Schema.html#ERROR_NO_URL_CONVERT">ERROR_NO_URL_CONVERT</A></CODE></TD>
<TD ALIGN="right"><CODE>"Could not URL convert "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.dotnet.WsdlToDotnet.Schema.ERROR_NONE_DECLARED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/dotnet/WsdlToDotnet.Schema.html#ERROR_NONE_DECLARED">ERROR_NONE_DECLARED</A></CODE></TD>
<TD ALIGN="right"><CODE>"One of file and url must be set"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.ejb.<A HREF="org/apache/tools/ant/taskdefs/optional/ejb/BorlandDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">BorlandDeploymentTool</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.BorlandDeploymentTool.BAS_DD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/BorlandDeploymentTool.html#BAS_DD">BAS_DD</A></CODE></TD>
<TD ALIGN="right"><CODE>"ejb-inprise.xml"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.BorlandDeploymentTool.BES_DD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/BorlandDeploymentTool.html#BES_DD">BES_DD</A></CODE></TD>
<TD ALIGN="right"><CODE>"ejb-borland.xml"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.BorlandDeploymentTool.DEFAULT_BAS_DTD_LOCATION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/BorlandDeploymentTool.html#DEFAULT_BAS_DTD_LOCATION">DEFAULT_BAS_DTD_LOCATION</A></CODE></TD>
<TD ALIGN="right"><CODE>"/com/inprise/j2ee/xml/dtds/ejb-inprise.dtd"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.BorlandDeploymentTool.DEFAULT_BAS45_EJB11_DTD_LOCATION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/BorlandDeploymentTool.html#DEFAULT_BAS45_EJB11_DTD_LOCATION">DEFAULT_BAS45_EJB11_DTD_LOCATION</A></CODE></TD>
<TD ALIGN="right"><CODE>"/com/inprise/j2ee/xml/dtds/ejb-jar.dtd"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.BorlandDeploymentTool.JAVA2IIOP"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/BorlandDeploymentTool.html#JAVA2IIOP">JAVA2IIOP</A></CODE></TD>
<TD ALIGN="right"><CODE>"java2iiop"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.BorlandDeploymentTool.PUBLICID_BORLAND_EJB"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/BorlandDeploymentTool.html#PUBLICID_BORLAND_EJB">PUBLICID_BORLAND_EJB</A></CODE></TD>
<TD ALIGN="right"><CODE>"-//Inprise Corporation//DTD Enterprise JavaBeans 1.1//EN"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.BorlandDeploymentTool.VERIFY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/BorlandDeploymentTool.html#VERIFY">VERIFY</A></CODE></TD>
<TD ALIGN="right"><CODE>"com.inprise.ejb.util.Verify"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.ejb.<A HREF="org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.CMPVersion.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.CMPVersion</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.CMPVersion.CMP1_0"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.CMPVersion.html#CMP1_0">CMP1_0</A></CODE></TD>
<TD ALIGN="right"><CODE>"1.0"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.CMPVersion.CMP2_0"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.CMPVersion.html#CMP2_0">CMP2_0</A></CODE></TD>
<TD ALIGN="right"><CODE>"2.0"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.ejb.<A HREF="org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.NamingScheme.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">EjbJar.NamingScheme</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.NamingScheme.BASEJARNAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.NamingScheme.html#BASEJARNAME">BASEJARNAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"basejarname"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.NamingScheme.DESCRIPTOR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.NamingScheme.html#DESCRIPTOR">DESCRIPTOR</A></CODE></TD>
<TD ALIGN="right"><CODE>"descriptor"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.NamingScheme.DIRECTORY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.NamingScheme.html#DIRECTORY">DIRECTORY</A></CODE></TD>
<TD ALIGN="right"><CODE>"directory"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.EjbJar.NamingScheme.EJB_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/EjbJar.NamingScheme.html#EJB_NAME">EJB_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"ejb-name"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.ejb.<A HREF="org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">GenericDeploymentTool</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.ANALYZER_CLASS_FULL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#ANALYZER_CLASS_FULL">ANALYZER_CLASS_FULL</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.apache.tools.ant.util.depend.bcel.FullAnalyzer"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.ANALYZER_CLASS_SUPER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#ANALYZER_CLASS_SUPER">ANALYZER_CLASS_SUPER</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.apache.tools.ant.util.depend.bcel.AncestorAnalyzer"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.ANALYZER_FULL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#ANALYZER_FULL">ANALYZER_FULL</A></CODE></TD>
<TD ALIGN="right"><CODE>"full"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.ANALYZER_NONE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#ANALYZER_NONE">ANALYZER_NONE</A></CODE></TD>
<TD ALIGN="right"><CODE>"none"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.ANALYZER_SUPER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#ANALYZER_SUPER">ANALYZER_SUPER</A></CODE></TD>
<TD ALIGN="right"><CODE>"super"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.DEFAULT_ANALYZER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#DEFAULT_ANALYZER">DEFAULT_ANALYZER</A></CODE></TD>
<TD ALIGN="right"><CODE>"super"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.DEFAULT_BUFFER_SIZE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#DEFAULT_BUFFER_SIZE">DEFAULT_BUFFER_SIZE</A></CODE></TD>
<TD ALIGN="right"><CODE>1024</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.EJB_DD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#EJB_DD">EJB_DD</A></CODE></TD>
<TD ALIGN="right"><CODE>"ejb-jar.xml"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.JAR_COMPRESS_LEVEL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#JAR_COMPRESS_LEVEL">JAR_COMPRESS_LEVEL</A></CODE></TD>
<TD ALIGN="right"><CODE>9</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.MANIFEST"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#MANIFEST">MANIFEST</A></CODE></TD>
<TD ALIGN="right"><CODE>"META-INF/MANIFEST.MF"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.GenericDeploymentTool.META_DIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/GenericDeploymentTool.html#META_DIR">META_DIR</A></CODE></TD>
<TD ALIGN="right"><CODE>"META-INF/"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.ejb.<A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JbossDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">JbossDeploymentTool</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JbossDeploymentTool.JBOSS_CMP10D"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JbossDeploymentTool.html#JBOSS_CMP10D">JBOSS_CMP10D</A></CODE></TD>
<TD ALIGN="right"><CODE>"jaws.xml"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JbossDeploymentTool.JBOSS_CMP20D"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JbossDeploymentTool.html#JBOSS_CMP20D">JBOSS_CMP20D</A></CODE></TD>
<TD ALIGN="right"><CODE>"jbosscmp-jdbc.xml"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JbossDeploymentTool.JBOSS_DD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JbossDeploymentTool.html#JBOSS_DD">JBOSS_DD</A></CODE></TD>
<TD ALIGN="right"><CODE>"jboss.xml"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.ejb.<A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">JonasDeploymentTool</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.DAVID_ORB"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html#DAVID_ORB">DAVID_ORB</A></CODE></TD>
<TD ALIGN="right"><CODE>"DAVID"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.EJB_JAR_1_1_DTD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html#EJB_JAR_1_1_DTD">EJB_JAR_1_1_DTD</A></CODE></TD>
<TD ALIGN="right"><CODE>"ejb-jar_1_1.dtd"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.EJB_JAR_1_1_PUBLIC_ID"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html#EJB_JAR_1_1_PUBLIC_ID">EJB_JAR_1_1_PUBLIC_ID</A></CODE></TD>
<TD ALIGN="right"><CODE>"-//Sun Microsystems, Inc.//DTD Enterprise JavaBeans 1.1//EN"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.EJB_JAR_2_0_DTD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html#EJB_JAR_2_0_DTD">EJB_JAR_2_0_DTD</A></CODE></TD>
<TD ALIGN="right"><CODE>"ejb-jar_2_0.dtd"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.EJB_JAR_2_0_PUBLIC_ID"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html#EJB_JAR_2_0_PUBLIC_ID">EJB_JAR_2_0_PUBLIC_ID</A></CODE></TD>
<TD ALIGN="right"><CODE>"-//Sun Microsystems, Inc.//DTD Enterprise JavaBeans 2.0//EN"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.GENIC_CLASS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html#GENIC_CLASS">GENIC_CLASS</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.objectweb.jonas_ejb.genic.GenIC"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.JEREMIE_ORB"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html#JEREMIE_ORB">JEREMIE_ORB</A></CODE></TD>
<TD ALIGN="right"><CODE>"JEREMIE"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.JONAS_DD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html#JONAS_DD">JONAS_DD</A></CODE></TD>
<TD ALIGN="right"><CODE>"jonas-ejb-jar.xml"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.JONAS_EJB_JAR_2_4_DTD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html#JONAS_EJB_JAR_2_4_DTD">JONAS_EJB_JAR_2_4_DTD</A></CODE></TD>
<TD ALIGN="right"><CODE>"jonas-ejb-jar_2_4.dtd"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.JONAS_EJB_JAR_2_4_PUBLIC_ID"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html#JONAS_EJB_JAR_2_4_PUBLIC_ID">JONAS_EJB_JAR_2_4_PUBLIC_ID</A></CODE></TD>
<TD ALIGN="right"><CODE>"-//ObjectWeb//DTD JOnAS 2.4//EN"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.JONAS_EJB_JAR_2_5_DTD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html#JONAS_EJB_JAR_2_5_DTD">JONAS_EJB_JAR_2_5_DTD</A></CODE></TD>
<TD ALIGN="right"><CODE>"jonas-ejb-jar_2_5.dtd"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.JONAS_EJB_JAR_2_5_PUBLIC_ID"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html#JONAS_EJB_JAR_2_5_PUBLIC_ID">JONAS_EJB_JAR_2_5_PUBLIC_ID</A></CODE></TD>
<TD ALIGN="right"><CODE>"-//ObjectWeb//DTD JOnAS 2.5//EN"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.OLD_GENIC_CLASS_1"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html#OLD_GENIC_CLASS_1">OLD_GENIC_CLASS_1</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.objectweb.jonas_ejb.tools.GenWholeIC"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.OLD_GENIC_CLASS_2"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html#OLD_GENIC_CLASS_2">OLD_GENIC_CLASS_2</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.objectweb.jonas_ejb.tools.GenIC"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.JonasDeploymentTool.RMI_ORB"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/JonasDeploymentTool.html#RMI_ORB">RMI_ORB</A></CODE></TD>
<TD ALIGN="right"><CODE>"RMI"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.ejb.<A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WeblogicDeploymentTool</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.COMPILER_EJB11"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#COMPILER_EJB11">COMPILER_EJB11</A></CODE></TD>
<TD ALIGN="right"><CODE>"weblogic.ejbc"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.COMPILER_EJB20"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#COMPILER_EJB20">COMPILER_EJB20</A></CODE></TD>
<TD ALIGN="right"><CODE>"weblogic.ejbc20"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_COMPILER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#DEFAULT_COMPILER">DEFAULT_COMPILER</A></CODE></TD>
<TD ALIGN="right"><CODE>"default"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL51_DTD_LOCATION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#DEFAULT_WL51_DTD_LOCATION">DEFAULT_WL51_DTD_LOCATION</A></CODE></TD>
<TD ALIGN="right"><CODE>"/weblogic/ejb/deployment/xml/weblogic-ejb-jar.dtd"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL51_EJB11_DTD_LOCATION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#DEFAULT_WL51_EJB11_DTD_LOCATION">DEFAULT_WL51_EJB11_DTD_LOCATION</A></CODE></TD>
<TD ALIGN="right"><CODE>"/weblogic/ejb/deployment/xml/ejb-jar.dtd"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL60_51_DTD_LOCATION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#DEFAULT_WL60_51_DTD_LOCATION">DEFAULT_WL60_51_DTD_LOCATION</A></CODE></TD>
<TD ALIGN="right"><CODE>"/weblogic/ejb20/dd/xml/weblogic510-ejb-jar.dtd"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL60_DTD_LOCATION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#DEFAULT_WL60_DTD_LOCATION">DEFAULT_WL60_DTD_LOCATION</A></CODE></TD>
<TD ALIGN="right"><CODE>"/weblogic/ejb20/dd/xml/weblogic600-ejb-jar.dtd"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL60_EJB11_DTD_LOCATION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#DEFAULT_WL60_EJB11_DTD_LOCATION">DEFAULT_WL60_EJB11_DTD_LOCATION</A></CODE></TD>
<TD ALIGN="right"><CODE>"/weblogic/ejb20/dd/xml/ejb11-jar.dtd"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL60_EJB20_DTD_LOCATION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#DEFAULT_WL60_EJB20_DTD_LOCATION">DEFAULT_WL60_EJB20_DTD_LOCATION</A></CODE></TD>
<TD ALIGN="right"><CODE>"/weblogic/ejb20/dd/xml/ejb20-jar.dtd"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.DEFAULT_WL70_DTD_LOCATION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#DEFAULT_WL70_DTD_LOCATION">DEFAULT_WL70_DTD_LOCATION</A></CODE></TD>
<TD ALIGN="right"><CODE>"/weblogic/ejb20/dd/xml/weblogic700-ejb-jar.dtd"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.PUBLICID_EJB11"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#PUBLICID_EJB11">PUBLICID_EJB11</A></CODE></TD>
<TD ALIGN="right"><CODE>"-//Sun Microsystems, Inc.//DTD Enterprise JavaBeans 1.1//EN"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.PUBLICID_EJB20"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#PUBLICID_EJB20">PUBLICID_EJB20</A></CODE></TD>
<TD ALIGN="right"><CODE>"-//Sun Microsystems, Inc.//DTD Enterprise JavaBeans 2.0//EN"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.PUBLICID_WEBLOGIC_EJB510"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#PUBLICID_WEBLOGIC_EJB510">PUBLICID_WEBLOGIC_EJB510</A></CODE></TD>
<TD ALIGN="right"><CODE>"-//BEA Systems, Inc.//DTD WebLogic 5.1.0 EJB//EN"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.PUBLICID_WEBLOGIC_EJB600"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#PUBLICID_WEBLOGIC_EJB600">PUBLICID_WEBLOGIC_EJB600</A></CODE></TD>
<TD ALIGN="right"><CODE>"-//BEA Systems, Inc.//DTD WebLogic 6.0.0 EJB//EN"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.PUBLICID_WEBLOGIC_EJB700"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#PUBLICID_WEBLOGIC_EJB700">PUBLICID_WEBLOGIC_EJB700</A></CODE></TD>
<TD ALIGN="right"><CODE>"-//BEA Systems, Inc.//DTD WebLogic 7.0.0 EJB//EN"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.WL_CMP_DD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#WL_CMP_DD">WL_CMP_DD</A></CODE></TD>
<TD ALIGN="right"><CODE>"weblogic-cmp-rdbms-jar.xml"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WeblogicDeploymentTool.WL_DD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WeblogicDeploymentTool.html#WL_DD">WL_DD</A></CODE></TD>
<TD ALIGN="right"><CODE>"weblogic-ejb-jar.xml"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.ejb.<A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WebsphereDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WebsphereDeploymentTool</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WebsphereDeploymentTool.PUBLICID_EJB11"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WebsphereDeploymentTool.html#PUBLICID_EJB11">PUBLICID_EJB11</A></CODE></TD>
<TD ALIGN="right"><CODE>"-//Sun Microsystems, Inc.//DTD Enterprise JavaBeans 1.1//EN"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WebsphereDeploymentTool.PUBLICID_EJB20"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WebsphereDeploymentTool.html#PUBLICID_EJB20">PUBLICID_EJB20</A></CODE></TD>
<TD ALIGN="right"><CODE>"-//Sun Microsystems, Inc.//DTD Enterprise JavaBeans 2.0//EN"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WebsphereDeploymentTool.SCHEMA_DIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WebsphereDeploymentTool.html#SCHEMA_DIR">SCHEMA_DIR</A></CODE></TD>
<TD ALIGN="right"><CODE>"Schema/"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WebsphereDeploymentTool.WAS_BND"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WebsphereDeploymentTool.html#WAS_BND">WAS_BND</A></CODE></TD>
<TD ALIGN="right"><CODE>"ibm-ejb-jar-bnd.xmi"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WebsphereDeploymentTool.WAS_CMP_MAP"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WebsphereDeploymentTool.html#WAS_CMP_MAP">WAS_CMP_MAP</A></CODE></TD>
<TD ALIGN="right"><CODE>"Map.mapxmi"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WebsphereDeploymentTool.WAS_CMP_SCHEMA"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WebsphereDeploymentTool.html#WAS_CMP_SCHEMA">WAS_CMP_SCHEMA</A></CODE></TD>
<TD ALIGN="right"><CODE>"Schema.dbxmi"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WebsphereDeploymentTool.WAS_EXT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WebsphereDeploymentTool.html#WAS_EXT">WAS_EXT</A></CODE></TD>
<TD ALIGN="right"><CODE>"ibm-ejb-jar-ext.xmi"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.ejb.<A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WLRun.html" title="class in org.apache.tools.ant.taskdefs.optional.ejb">WLRun</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WLRun.DEFAULT_PROPERTIES_FILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WLRun.html#DEFAULT_PROPERTIES_FILE">DEFAULT_PROPERTIES_FILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"weblogic.properties"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WLRun.DEFAULT_WL51_POLICY_FILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WLRun.html#DEFAULT_WL51_POLICY_FILE">DEFAULT_WL51_POLICY_FILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"weblogic.policy"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.ejb.WLRun.DEFAULT_WL60_POLICY_FILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/ejb/WLRun.html#DEFAULT_WL60_POLICY_FILE">DEFAULT_WL60_POLICY_FILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"lib/weblogic.policy"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.j2ee.<A HREF="org/apache/tools/ant/taskdefs/optional/j2ee/HotDeploymentTool.html" title="interface in org.apache.tools.ant.taskdefs.optional.j2ee">HotDeploymentTool</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.j2ee.HotDeploymentTool.ACTION_DELETE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/j2ee/HotDeploymentTool.html#ACTION_DELETE">ACTION_DELETE</A></CODE></TD>
<TD ALIGN="right"><CODE>"delete"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.j2ee.HotDeploymentTool.ACTION_DEPLOY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/j2ee/HotDeploymentTool.html#ACTION_DEPLOY">ACTION_DEPLOY</A></CODE></TD>
<TD ALIGN="right"><CODE>"deploy"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.j2ee.HotDeploymentTool.ACTION_LIST"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/j2ee/HotDeploymentTool.html#ACTION_LIST">ACTION_LIST</A></CODE></TD>
<TD ALIGN="right"><CODE>"list"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.j2ee.HotDeploymentTool.ACTION_UNDEPLOY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/j2ee/HotDeploymentTool.html#ACTION_UNDEPLOY">ACTION_UNDEPLOY</A></CODE></TD>
<TD ALIGN="right"><CODE>"undeploy"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.j2ee.HotDeploymentTool.ACTION_UPDATE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/j2ee/HotDeploymentTool.html#ACTION_UPDATE">ACTION_UPDATE</A></CODE></TD>
<TD ALIGN="right"><CODE>"update"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.j2ee.<A HREF="org/apache/tools/ant/taskdefs/optional/j2ee/JonasHotDeploymentTool.html" title="class in org.apache.tools.ant.taskdefs.optional.j2ee">JonasHotDeploymentTool</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.j2ee.JonasHotDeploymentTool.DEFAULT_ORB"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/j2ee/JonasHotDeploymentTool.html#DEFAULT_ORB">DEFAULT_ORB</A></CODE></TD>
<TD ALIGN="right"><CODE>"RMI"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.javacc.<A HREF="org/apache/tools/ant/taskdefs/optional/javacc/JavaCC.html" title="class in org.apache.tools.ant.taskdefs.optional.javacc">JavaCC</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.COM_JAVACC_CLASS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/javacc/JavaCC.html#COM_JAVACC_CLASS">COM_JAVACC_CLASS</A></CODE></TD>
<TD ALIGN="right"><CODE>"javacc.Main"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.COM_JJDOC_CLASS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/javacc/JavaCC.html#COM_JJDOC_CLASS">COM_JJDOC_CLASS</A></CODE></TD>
<TD ALIGN="right"><CODE>"jjdoc.JJDocMain"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.COM_JJTREE_CLASS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/javacc/JavaCC.html#COM_JJTREE_CLASS">COM_JJTREE_CLASS</A></CODE></TD>
<TD ALIGN="right"><CODE>"jjtree.Main"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.COM_PACKAGE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/javacc/JavaCC.html#COM_PACKAGE">COM_PACKAGE</A></CODE></TD>
<TD ALIGN="right"><CODE>"COM.sun.labs."</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.ORG_JAVACC_CLASS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/javacc/JavaCC.html#ORG_JAVACC_CLASS">ORG_JAVACC_CLASS</A></CODE></TD>
<TD ALIGN="right"><CODE>"parser.Main"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.ORG_JJDOC_CLASS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/javacc/JavaCC.html#ORG_JJDOC_CLASS">ORG_JJDOC_CLASS</A></CODE></TD>
<TD ALIGN="right"><CODE>"jjdoc.JJDocMain"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.ORG_JJTREE_CLASS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/javacc/JavaCC.html#ORG_JJTREE_CLASS">ORG_JJTREE_CLASS</A></CODE></TD>
<TD ALIGN="right"><CODE>"jjtree.Main"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.ORG_PACKAGE_3_0"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/javacc/JavaCC.html#ORG_PACKAGE_3_0">ORG_PACKAGE_3_0</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.netbeans.javacc."</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.ORG_PACKAGE_3_1"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/javacc/JavaCC.html#ORG_PACKAGE_3_1">ORG_PACKAGE_3_1</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.javacc."</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.TASKDEF_TYPE_JAVACC"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/javacc/JavaCC.html#TASKDEF_TYPE_JAVACC">TASKDEF_TYPE_JAVACC</A></CODE></TD>
<TD ALIGN="right"><CODE>1</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.TASKDEF_TYPE_JJDOC"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/javacc/JavaCC.html#TASKDEF_TYPE_JJDOC">TASKDEF_TYPE_JJDOC</A></CODE></TD>
<TD ALIGN="right"><CODE>3</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.javacc.JavaCC.TASKDEF_TYPE_JJTREE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/javacc/JavaCC.html#TASKDEF_TYPE_JJTREE">TASKDEF_TYPE_JJTREE</A></CODE></TD>
<TD ALIGN="right"><CODE>2</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.javah.<A HREF="org/apache/tools/ant/taskdefs/optional/javah/Kaffeh.html" title="class in org.apache.tools.ant.taskdefs.optional.javah">Kaffeh</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.javah.Kaffeh.IMPLEMENTATION_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/javah/Kaffeh.html#IMPLEMENTATION_NAME">IMPLEMENTATION_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"kaffeh"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.javah.<A HREF="org/apache/tools/ant/taskdefs/optional/javah/SunJavah.html" title="class in org.apache.tools.ant.taskdefs.optional.javah">SunJavah</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.javah.SunJavah.IMPLEMENTATION_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/javah/SunJavah.html#IMPLEMENTATION_NAME">IMPLEMENTATION_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"sun"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.junit.<A HREF="org/apache/tools/ant/taskdefs/optional/junit/AggregateTransformer.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">AggregateTransformer</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.AggregateTransformer.FRAMES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/AggregateTransformer.html#FRAMES">FRAMES</A></CODE></TD>
<TD ALIGN="right"><CODE>"frames"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.AggregateTransformer.NOFRAMES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/AggregateTransformer.html#NOFRAMES">NOFRAMES</A></CODE></TD>
<TD ALIGN="right"><CODE>"noframes"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.junit.<A HREF="org/apache/tools/ant/taskdefs/optional/junit/FormatterElement.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">FormatterElement</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.FormatterElement.BRIEF_FORMATTER_CLASS_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/FormatterElement.html#BRIEF_FORMATTER_CLASS_NAME">BRIEF_FORMATTER_CLASS_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.apache.tools.ant.taskdefs.optional.junit.BriefJUnitResultFormatter"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.FormatterElement.PLAIN_FORMATTER_CLASS_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/FormatterElement.html#PLAIN_FORMATTER_CLASS_NAME">PLAIN_FORMATTER_CLASS_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.apache.tools.ant.taskdefs.optional.junit.PlainJUnitResultFormatter"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.FormatterElement.XML_FORMATTER_CLASS_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/FormatterElement.html#XML_FORMATTER_CLASS_NAME">XML_FORMATTER_CLASS_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.apache.tools.ant.taskdefs.optional.junit.XMLJUnitResultFormatter"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.junit.<A HREF="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.TESTLISTENER_PREFIX"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.html#TESTLISTENER_PREFIX">TESTLISTENER_PREFIX</A></CODE></TD>
<TD ALIGN="right"><CODE>"junit.framework.TestListener: "</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.junit.<A HREF="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.ForkMode.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">JUnitTask.ForkMode</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.ForkMode.ONCE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.ForkMode.html#ONCE">ONCE</A></CODE></TD>
<TD ALIGN="right"><CODE>"once"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.ForkMode.PER_BATCH"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.ForkMode.html#PER_BATCH">PER_BATCH</A></CODE></TD>
<TD ALIGN="right"><CODE>"perBatch"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.JUnitTask.ForkMode.PER_TEST"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/JUnitTask.ForkMode.html#PER_TEST">PER_TEST</A></CODE></TD>
<TD ALIGN="right"><CODE>"perTest"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.junit.<A HREF="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.JUnitTestRunnerMirror.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">JUnitTaskMirror.JUnitTestRunnerMirror</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.JUnitTaskMirror.JUnitTestRunnerMirror.ERRORS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.JUnitTestRunnerMirror.html#ERRORS">ERRORS</A></CODE></TD>
<TD ALIGN="right"><CODE>2</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.JUnitTaskMirror.JUnitTestRunnerMirror.FAILURES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.JUnitTestRunnerMirror.html#FAILURES">FAILURES</A></CODE></TD>
<TD ALIGN="right"><CODE>1</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.JUnitTaskMirror.JUnitTestRunnerMirror.IGNORED_FILE_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.JUnitTestRunnerMirror.html#IGNORED_FILE_NAME">IGNORED_FILE_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"IGNORETHIS"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.JUnitTaskMirror.JUnitTestRunnerMirror.SUCCESS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/JUnitTaskMirror.JUnitTestRunnerMirror.html#SUCCESS">SUCCESS</A></CODE></TD>
<TD ALIGN="right"><CODE>0</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.junit.<A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.junit">XMLConstants</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_CLASSNAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#ATTR_CLASSNAME">ATTR_CLASSNAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"classname"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_ERRORS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#ATTR_ERRORS">ATTR_ERRORS</A></CODE></TD>
<TD ALIGN="right"><CODE>"errors"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_FAILURES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#ATTR_FAILURES">ATTR_FAILURES</A></CODE></TD>
<TD ALIGN="right"><CODE>"failures"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_ID"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#ATTR_ID">ATTR_ID</A></CODE></TD>
<TD ALIGN="right"><CODE>"id"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_MESSAGE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#ATTR_MESSAGE">ATTR_MESSAGE</A></CODE></TD>
<TD ALIGN="right"><CODE>"message"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#ATTR_NAME">ATTR_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"name"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_PACKAGE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#ATTR_PACKAGE">ATTR_PACKAGE</A></CODE></TD>
<TD ALIGN="right"><CODE>"package"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_TESTS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#ATTR_TESTS">ATTR_TESTS</A></CODE></TD>
<TD ALIGN="right"><CODE>"tests"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_TIME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#ATTR_TIME">ATTR_TIME</A></CODE></TD>
<TD ALIGN="right"><CODE>"time"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_TYPE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#ATTR_TYPE">ATTR_TYPE</A></CODE></TD>
<TD ALIGN="right"><CODE>"type"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ATTR_VALUE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#ATTR_VALUE">ATTR_VALUE</A></CODE></TD>
<TD ALIGN="right"><CODE>"value"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.ERROR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#ERROR">ERROR</A></CODE></TD>
<TD ALIGN="right"><CODE>"error"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.FAILURE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#FAILURE">FAILURE</A></CODE></TD>
<TD ALIGN="right"><CODE>"failure"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.HOSTNAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#HOSTNAME">HOSTNAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"hostname"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.PROPERTIES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#PROPERTIES">PROPERTIES</A></CODE></TD>
<TD ALIGN="right"><CODE>"properties"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.PROPERTY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#PROPERTY">PROPERTY</A></CODE></TD>
<TD ALIGN="right"><CODE>"property"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.SYSTEM_ERR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#SYSTEM_ERR">SYSTEM_ERR</A></CODE></TD>
<TD ALIGN="right"><CODE>"system-err"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.SYSTEM_OUT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#SYSTEM_OUT">SYSTEM_OUT</A></CODE></TD>
<TD ALIGN="right"><CODE>"system-out"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.TESTCASE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#TESTCASE">TESTCASE</A></CODE></TD>
<TD ALIGN="right"><CODE>"testcase"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.TESTSUITE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#TESTSUITE">TESTSUITE</A></CODE></TD>
<TD ALIGN="right"><CODE>"testsuite"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.TESTSUITES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#TESTSUITES">TESTSUITES</A></CODE></TD>
<TD ALIGN="right"><CODE>"testsuites"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLConstants.TIMESTAMP"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLConstants.html#TIMESTAMP">TIMESTAMP</A></CODE></TD>
<TD ALIGN="right"><CODE>"timestamp"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.junit.<A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLResultAggregator.html" title="class in org.apache.tools.ant.taskdefs.optional.junit">XMLResultAggregator</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLResultAggregator.DEFAULT_DIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLResultAggregator.html#DEFAULT_DIR">DEFAULT_DIR</A></CODE></TD>
<TD ALIGN="right"><CODE>"."</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.junit.XMLResultAggregator.DEFAULT_FILENAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/junit/XMLResultAggregator.html#DEFAULT_FILENAME">DEFAULT_FILENAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"TESTS-TestSuites.xml"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.native2ascii.<A HREF="org/apache/tools/ant/taskdefs/optional/native2ascii/KaffeNative2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional.native2ascii">KaffeNative2Ascii</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.native2ascii.KaffeNative2Ascii.IMPLEMENTATION_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/native2ascii/KaffeNative2Ascii.html#IMPLEMENTATION_NAME">IMPLEMENTATION_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"kaffe"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.native2ascii.<A HREF="org/apache/tools/ant/taskdefs/optional/native2ascii/SunNative2Ascii.html" title="class in org.apache.tools.ant.taskdefs.optional.native2ascii">SunNative2Ascii</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.native2ascii.SunNative2Ascii.IMPLEMENTATION_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/native2ascii/SunNative2Ascii.html#IMPLEMENTATION_NAME">IMPLEMENTATION_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"sun"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.net.<A HREF="org/apache/tools/ant/taskdefs/optional/net/FTP.html" title="class in org.apache.tools.ant.taskdefs.optional.net">FTP</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.net.FTP.CHMOD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/net/FTP.html#CHMOD">CHMOD</A></CODE></TD>
<TD ALIGN="right"><CODE>5</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.net.FTP.DEFAULT_FTP_PORT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/net/FTP.html#DEFAULT_FTP_PORT">DEFAULT_FTP_PORT</A></CODE></TD>
<TD ALIGN="right"><CODE>21</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.net.FTP.DEL_FILES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/net/FTP.html#DEL_FILES">DEL_FILES</A></CODE></TD>
<TD ALIGN="right"><CODE>2</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.net.FTP.GET_FILES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/net/FTP.html#GET_FILES">GET_FILES</A></CODE></TD>
<TD ALIGN="right"><CODE>1</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.net.FTP.LIST_FILES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/net/FTP.html#LIST_FILES">LIST_FILES</A></CODE></TD>
<TD ALIGN="right"><CODE>3</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.net.FTP.MK_DIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/net/FTP.html#MK_DIR">MK_DIR</A></CODE></TD>
<TD ALIGN="right"><CODE>4</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.net.FTP.RM_DIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/net/FTP.html#RM_DIR">RM_DIR</A></CODE></TD>
<TD ALIGN="right"><CODE>6</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.net.FTP.SEND_FILES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/net/FTP.html#SEND_FILES">SEND_FILES</A></CODE></TD>
<TD ALIGN="right"><CODE>0</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.net.FTP.SITE_CMD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/net/FTP.html#SITE_CMD">SITE_CMD</A></CODE></TD>
<TD ALIGN="right"><CODE>7</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.net.<A HREF="org/apache/tools/ant/taskdefs/optional/net/TelnetTask.AntTelnetClient.html" title="class in org.apache.tools.ant.taskdefs.optional.net">TelnetTask.AntTelnetClient</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.net.TelnetTask.AntTelnetClient.TERMINAL_TYPE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>TERMINAL_TYPE</CODE></TD>
<TD ALIGN="right"><CODE>24</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.net.TelnetTask.AntTelnetClient.TERMINAL_TYPE_IS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>TERMINAL_TYPE_IS</CODE></TD>
<TD ALIGN="right"><CODE>0</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.net.TelnetTask.AntTelnetClient.TERMINAL_TYPE_SEND"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>TERMINAL_TYPE_SEND</CODE></TD>
<TD ALIGN="right"><CODE>1</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.scm.<A HREF="org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html" title="class in org.apache.tools.ant.taskdefs.optional.scm">AntStarTeamCheckOut</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.scm.AntStarTeamCheckOut.DEFAULT_INCLUDESETTING"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/scm/AntStarTeamCheckOut.html#DEFAULT_INCLUDESETTING">DEFAULT_INCLUDESETTING</A></CODE></TD>
<TD ALIGN="right"><CODE>"*"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.sos.<A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html" title="interface in org.apache.tools.ant.taskdefs.optional.sos">SOSCmd</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_CHECKIN_FILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_CHECKIN_FILE">COMMAND_CHECKIN_FILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"CheckInFile"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_CHECKIN_PROJECT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_CHECKIN_PROJECT">COMMAND_CHECKIN_PROJECT</A></CODE></TD>
<TD ALIGN="right"><CODE>"CheckInProject"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_CHECKOUT_FILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_CHECKOUT_FILE">COMMAND_CHECKOUT_FILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"CheckOutFile"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_CHECKOUT_PROJECT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_CHECKOUT_PROJECT">COMMAND_CHECKOUT_PROJECT</A></CODE></TD>
<TD ALIGN="right"><CODE>"CheckOutProject"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_GET_FILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_GET_FILE">COMMAND_GET_FILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"GetFile"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_GET_PROJECT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_GET_PROJECT">COMMAND_GET_PROJECT</A></CODE></TD>
<TD ALIGN="right"><CODE>"GetProject"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_HISTORY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_HISTORY">COMMAND_HISTORY</A></CODE></TD>
<TD ALIGN="right"><CODE>"GetFileHistory"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_LABEL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_LABEL">COMMAND_LABEL</A></CODE></TD>
<TD ALIGN="right"><CODE>"AddLabel"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.COMMAND_SOS_EXE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#COMMAND_SOS_EXE">COMMAND_SOS_EXE</A></CODE></TD>
<TD ALIGN="right"><CODE>"soscmd"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_COMMAND"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_COMMAND">FLAG_COMMAND</A></CODE></TD>
<TD ALIGN="right"><CODE>"-command"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_COMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_COMMENT">FLAG_COMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-log"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_FILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_FILE">FLAG_FILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-file"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_LABEL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_LABEL">FLAG_LABEL</A></CODE></TD>
<TD ALIGN="right"><CODE>"-label"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_NO_CACHE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_NO_CACHE">FLAG_NO_CACHE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nocache"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_NO_COMPRESSION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_NO_COMPRESSION">FLAG_NO_COMPRESSION</A></CODE></TD>
<TD ALIGN="right"><CODE>"-nocompress"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_PASSWORD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_PASSWORD">FLAG_PASSWORD</A></CODE></TD>
<TD ALIGN="right"><CODE>"-password"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_PROJECT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_PROJECT">FLAG_PROJECT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-project"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_RECURSION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_RECURSION">FLAG_RECURSION</A></CODE></TD>
<TD ALIGN="right"><CODE>"-recursive"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_SOS_HOME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_SOS_HOME">FLAG_SOS_HOME</A></CODE></TD>
<TD ALIGN="right"><CODE>"-soshome"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_SOS_SERVER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_SOS_SERVER">FLAG_SOS_SERVER</A></CODE></TD>
<TD ALIGN="right"><CODE>"-server"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_USERNAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_USERNAME">FLAG_USERNAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"-name"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_VERBOSE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_VERBOSE">FLAG_VERBOSE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-verbose"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_VERSION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_VERSION">FLAG_VERSION</A></CODE></TD>
<TD ALIGN="right"><CODE>"-revision"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_VSS_SERVER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_VSS_SERVER">FLAG_VSS_SERVER</A></CODE></TD>
<TD ALIGN="right"><CODE>"-database"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.FLAG_WORKING_DIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#FLAG_WORKING_DIR">FLAG_WORKING_DIR</A></CODE></TD>
<TD ALIGN="right"><CODE>"-workdir"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.sos.SOSCmd.PROJECT_PREFIX"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/sos/SOSCmd.html#PROJECT_PREFIX">PROJECT_PREFIX</A></CODE></TD>
<TD ALIGN="right"><CODE>"$"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.starteam.<A HREF="org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html" title="class in org.apache.tools.ant.taskdefs.optional.starteam">TreeBasedTask</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.starteam.TreeBasedTask.DEFAULT_INCLUDESETTING"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/starteam/TreeBasedTask.html#DEFAULT_INCLUDESETTING">DEFAULT_INCLUDESETTING</A></CODE></TD>
<TD ALIGN="right"><CODE>"*"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.optional.vss.<A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html" title="interface in org.apache.tools.ant.taskdefs.optional.vss">MSVSSConstants</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.COMMAND_ADD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#COMMAND_ADD">COMMAND_ADD</A></CODE></TD>
<TD ALIGN="right"><CODE>"Add"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.COMMAND_CHECKIN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#COMMAND_CHECKIN">COMMAND_CHECKIN</A></CODE></TD>
<TD ALIGN="right"><CODE>"Checkin"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.COMMAND_CHECKOUT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#COMMAND_CHECKOUT">COMMAND_CHECKOUT</A></CODE></TD>
<TD ALIGN="right"><CODE>"Checkout"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.COMMAND_CP"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#COMMAND_CP">COMMAND_CP</A></CODE></TD>
<TD ALIGN="right"><CODE>"CP"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.COMMAND_CREATE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#COMMAND_CREATE">COMMAND_CREATE</A></CODE></TD>
<TD ALIGN="right"><CODE>"Create"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.COMMAND_GET"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#COMMAND_GET">COMMAND_GET</A></CODE></TD>
<TD ALIGN="right"><CODE>"Get"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.COMMAND_HISTORY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#COMMAND_HISTORY">COMMAND_HISTORY</A></CODE></TD>
<TD ALIGN="right"><CODE>"History"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.COMMAND_LABEL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#COMMAND_LABEL">COMMAND_LABEL</A></CODE></TD>
<TD ALIGN="right"><CODE>"Label"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_AUTORESPONSE_DEF"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_AUTORESPONSE_DEF">FLAG_AUTORESPONSE_DEF</A></CODE></TD>
<TD ALIGN="right"><CODE>"-I-"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_AUTORESPONSE_NO"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_AUTORESPONSE_NO">FLAG_AUTORESPONSE_NO</A></CODE></TD>
<TD ALIGN="right"><CODE>"-I-N"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_AUTORESPONSE_YES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_AUTORESPONSE_YES">FLAG_AUTORESPONSE_YES</A></CODE></TD>
<TD ALIGN="right"><CODE>"-I-Y"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_BRIEF"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_BRIEF">FLAG_BRIEF</A></CODE></TD>
<TD ALIGN="right"><CODE>"-B"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_CODEDIFF"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_CODEDIFF">FLAG_CODEDIFF</A></CODE></TD>
<TD ALIGN="right"><CODE>"-D"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_COMMENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_COMMENT">FLAG_COMMENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-C"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_FILETIME_DEF"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_FILETIME_DEF">FLAG_FILETIME_DEF</A></CODE></TD>
<TD ALIGN="right"><CODE>"-GTC"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_FILETIME_MODIFIED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_FILETIME_MODIFIED">FLAG_FILETIME_MODIFIED</A></CODE></TD>
<TD ALIGN="right"><CODE>"-GTM"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_FILETIME_UPDATED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_FILETIME_UPDATED">FLAG_FILETIME_UPDATED</A></CODE></TD>
<TD ALIGN="right"><CODE>"-GTU"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_LABEL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_LABEL">FLAG_LABEL</A></CODE></TD>
<TD ALIGN="right"><CODE>"-L"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_LOGIN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_LOGIN">FLAG_LOGIN</A></CODE></TD>
<TD ALIGN="right"><CODE>"-Y"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_NO_FILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_NO_FILE">FLAG_NO_FILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-F-"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_NO_GET"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_NO_GET">FLAG_NO_GET</A></CODE></TD>
<TD ALIGN="right"><CODE>"-G-"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_OUTPUT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_OUTPUT">FLAG_OUTPUT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-O"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_OVERRIDE_WORKING_DIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_OVERRIDE_WORKING_DIR">FLAG_OVERRIDE_WORKING_DIR</A></CODE></TD>
<TD ALIGN="right"><CODE>"-GL"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_QUIET"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_QUIET">FLAG_QUIET</A></CODE></TD>
<TD ALIGN="right"><CODE>"-O-"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_RECURSION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_RECURSION">FLAG_RECURSION</A></CODE></TD>
<TD ALIGN="right"><CODE>"-R"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_REPLACE_WRITABLE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_REPLACE_WRITABLE">FLAG_REPLACE_WRITABLE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-GWR"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_SKIP_WRITABLE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_SKIP_WRITABLE">FLAG_SKIP_WRITABLE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-GWS"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_USER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_USER">FLAG_USER</A></CODE></TD>
<TD ALIGN="right"><CODE>"-U"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_VERSION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_VERSION">FLAG_VERSION</A></CODE></TD>
<TD ALIGN="right"><CODE>"-V"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_VERSION_DATE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_VERSION_DATE">FLAG_VERSION_DATE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-Vd"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_VERSION_LABEL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_VERSION_LABEL">FLAG_VERSION_LABEL</A></CODE></TD>
<TD ALIGN="right"><CODE>"-VL"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.FLAG_WRITABLE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#FLAG_WRITABLE">FLAG_WRITABLE</A></CODE></TD>
<TD ALIGN="right"><CODE>"-W"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.PROJECT_PREFIX"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#PROJECT_PREFIX">PROJECT_PREFIX</A></CODE></TD>
<TD ALIGN="right"><CODE>"$"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.SS_EXE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#SS_EXE">SS_EXE</A></CODE></TD>
<TD ALIGN="right"><CODE>"ss"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.STYLE_BRIEF"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#STYLE_BRIEF">STYLE_BRIEF</A></CODE></TD>
<TD ALIGN="right"><CODE>"brief"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.STYLE_CODEDIFF"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#STYLE_CODEDIFF">STYLE_CODEDIFF</A></CODE></TD>
<TD ALIGN="right"><CODE>"codediff"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.STYLE_DEFAULT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#STYLE_DEFAULT">STYLE_DEFAULT</A></CODE></TD>
<TD ALIGN="right"><CODE>"default"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.STYLE_NOFILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#STYLE_NOFILE">STYLE_NOFILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"nofile"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.TIME_CURRENT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#TIME_CURRENT">TIME_CURRENT</A></CODE></TD>
<TD ALIGN="right"><CODE>"current"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.TIME_MODIFIED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#TIME_MODIFIED">TIME_MODIFIED</A></CODE></TD>
<TD ALIGN="right"><CODE>"modified"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.TIME_UPDATED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#TIME_UPDATED">TIME_UPDATED</A></CODE></TD>
<TD ALIGN="right"><CODE>"updated"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.VALUE_FROMDATE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#VALUE_FROMDATE">VALUE_FROMDATE</A></CODE></TD>
<TD ALIGN="right"><CODE>"~d"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.VALUE_FROMLABEL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#VALUE_FROMLABEL">VALUE_FROMLABEL</A></CODE></TD>
<TD ALIGN="right"><CODE>"~L"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.VALUE_NO"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#VALUE_NO">VALUE_NO</A></CODE></TD>
<TD ALIGN="right"><CODE>"-N"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.VALUE_YES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#VALUE_YES">VALUE_YES</A></CODE></TD>
<TD ALIGN="right"><CODE>"-Y"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.WRITABLE_FAIL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#WRITABLE_FAIL">WRITABLE_FAIL</A></CODE></TD>
<TD ALIGN="right"><CODE>"fail"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.WRITABLE_REPLACE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#WRITABLE_REPLACE">WRITABLE_REPLACE</A></CODE></TD>
<TD ALIGN="right"><CODE>"replace"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.optional.vss.MSVSSConstants.WRITABLE_SKIP"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/optional/vss/MSVSSConstants.html#WRITABLE_SKIP">WRITABLE_SKIP</A></CODE></TD>
<TD ALIGN="right"><CODE>"skip"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.rmic.<A HREF="org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html" title="class in org.apache.tools.ant.taskdefs.rmic">DefaultRmicAdapter</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter.RMI_SKEL_SUFFIX"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#RMI_SKEL_SUFFIX">RMI_SKEL_SUFFIX</A></CODE></TD>
<TD ALIGN="right"><CODE>"_Skel"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter.RMI_STUB_SUFFIX"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#RMI_STUB_SUFFIX">RMI_STUB_SUFFIX</A></CODE></TD>
<TD ALIGN="right"><CODE>"_Stub"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter.RMI_TIE_SUFFIX"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#RMI_TIE_SUFFIX">RMI_TIE_SUFFIX</A></CODE></TD>
<TD ALIGN="right"><CODE>"_Tie"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter.STUB_1_1"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#STUB_1_1">STUB_1_1</A></CODE></TD>
<TD ALIGN="right"><CODE>"-v1.1"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter.STUB_1_2"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#STUB_1_2">STUB_1_2</A></CODE></TD>
<TD ALIGN="right"><CODE>"-v1.2"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.DefaultRmicAdapter.STUB_COMPAT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/DefaultRmicAdapter.html#STUB_COMPAT">STUB_COMPAT</A></CODE></TD>
<TD ALIGN="right"><CODE>"-vcompat"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.rmic.<A HREF="org/apache/tools/ant/taskdefs/rmic/ForkingSunRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic">ForkingSunRmic</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.ForkingSunRmic.COMPILER_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/ForkingSunRmic.html#COMPILER_NAME">COMPILER_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"forking"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.rmic.<A HREF="org/apache/tools/ant/taskdefs/rmic/KaffeRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic">KaffeRmic</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.KaffeRmic.COMPILER_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/KaffeRmic.html#COMPILER_NAME">COMPILER_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"kaffe"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.rmic.<A HREF="org/apache/tools/ant/taskdefs/rmic/RmicAdapterFactory.html" title="class in org.apache.tools.ant.taskdefs.rmic">RmicAdapterFactory</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.RmicAdapterFactory.DEFAULT_COMPILER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/RmicAdapterFactory.html#DEFAULT_COMPILER">DEFAULT_COMPILER</A></CODE></TD>
<TD ALIGN="right"><CODE>"default"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.RmicAdapterFactory.ERROR_NOT_RMIC_ADAPTER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/RmicAdapterFactory.html#ERROR_NOT_RMIC_ADAPTER">ERROR_NOT_RMIC_ADAPTER</A></CODE></TD>
<TD ALIGN="right"><CODE>"Class of unexpected Type: "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.RmicAdapterFactory.ERROR_UNKNOWN_COMPILER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/RmicAdapterFactory.html#ERROR_UNKNOWN_COMPILER">ERROR_UNKNOWN_COMPILER</A></CODE></TD>
<TD ALIGN="right"><CODE>"Class not found: "</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.rmic.<A HREF="org/apache/tools/ant/taskdefs/rmic/SunRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic">SunRmic</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.SunRmic.COMPILER_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/SunRmic.html#COMPILER_NAME">COMPILER_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"sun"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.SunRmic.ERROR_NO_RMIC_ON_CLASSPATH"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/SunRmic.html#ERROR_NO_RMIC_ON_CLASSPATH">ERROR_NO_RMIC_ON_CLASSPATH</A></CODE></TD>
<TD ALIGN="right"><CODE>"Cannot use SUN rmic, as it is not available.  A common solution is to set the environment variable JAVA_HOME or CLASSPATH."</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.SunRmic.ERROR_RMIC_FAILED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/SunRmic.html#ERROR_RMIC_FAILED">ERROR_RMIC_FAILED</A></CODE></TD>
<TD ALIGN="right"><CODE>"Error starting SUN rmic: "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.SunRmic.RMIC_CLASSNAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/SunRmic.html#RMIC_CLASSNAME">RMIC_CLASSNAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"sun.rmi.rmic.Main"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.SunRmic.RMIC_EXECUTABLE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/SunRmic.html#RMIC_EXECUTABLE">RMIC_EXECUTABLE</A></CODE></TD>
<TD ALIGN="right"><CODE>"rmic"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.rmic.<A HREF="org/apache/tools/ant/taskdefs/rmic/WLRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic">WLRmic</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.WLRmic.COMPILER_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/WLRmic.html#COMPILER_NAME">COMPILER_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"weblogic"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.WLRmic.ERROR_NO_WLRMIC_ON_CLASSPATH"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/WLRmic.html#ERROR_NO_WLRMIC_ON_CLASSPATH">ERROR_NO_WLRMIC_ON_CLASSPATH</A></CODE></TD>
<TD ALIGN="right"><CODE>"Cannot use WebLogic rmic, as it is not available.  A common solution is to set the environment variable CLASSPATH."</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.WLRmic.ERROR_WLRMIC_FAILED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/WLRmic.html#ERROR_WLRMIC_FAILED">ERROR_WLRMIC_FAILED</A></CODE></TD>
<TD ALIGN="right"><CODE>"Error starting WebLogic rmic: "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.WLRmic.WL_RMI_SKEL_SUFFIX"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/WLRmic.html#WL_RMI_SKEL_SUFFIX">WL_RMI_SKEL_SUFFIX</A></CODE></TD>
<TD ALIGN="right"><CODE>"_WLSkel"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.WLRmic.WL_RMI_STUB_SUFFIX"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/WLRmic.html#WL_RMI_STUB_SUFFIX">WL_RMI_STUB_SUFFIX</A></CODE></TD>
<TD ALIGN="right"><CODE>"_WLStub"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.WLRmic.WLRMIC_CLASSNAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/WLRmic.html#WLRMIC_CLASSNAME">WLRMIC_CLASSNAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"weblogic.rmic"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.taskdefs.rmic.<A HREF="org/apache/tools/ant/taskdefs/rmic/XNewRmic.html" title="class in org.apache.tools.ant.taskdefs.rmic">XNewRmic</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.taskdefs.rmic.XNewRmic.COMPILER_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/taskdefs/rmic/XNewRmic.html#COMPILER_NAME">COMPILER_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"xnew"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.types.<A HREF="org/apache/tools/ant/types/ArchiveFileSet.html" title="class in org.apache.tools.ant.types">ArchiveFileSet</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.ArchiveFileSet.DEFAULT_DIR_MODE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/ArchiveFileSet.html#DEFAULT_DIR_MODE">DEFAULT_DIR_MODE</A></CODE></TD>
<TD ALIGN="right"><CODE>16877</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.ArchiveFileSet.DEFAULT_FILE_MODE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/ArchiveFileSet.html#DEFAULT_FILE_MODE">DEFAULT_FILE_MODE</A></CODE></TD>
<TD ALIGN="right"><CODE>33188</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.types.<A HREF="org/apache/tools/ant/types/FilterSet.html" title="class in org.apache.tools.ant.types">FilterSet</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.FilterSet.DEFAULT_TOKEN_END"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/FilterSet.html#DEFAULT_TOKEN_END">DEFAULT_TOKEN_END</A></CODE></TD>
<TD ALIGN="right"><CODE>"@"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.FilterSet.DEFAULT_TOKEN_START"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/FilterSet.html#DEFAULT_TOKEN_START">DEFAULT_TOKEN_START</A></CODE></TD>
<TD ALIGN="right"><CODE>"@"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.types.<A HREF="org/apache/tools/ant/types/RegularExpression.html" title="class in org.apache.tools.ant.types">RegularExpression</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.RegularExpression.DATA_TYPE_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/RegularExpression.html#DATA_TYPE_NAME">DATA_TYPE_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"regexp"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.types.<A HREF="org/apache/tools/ant/types/Resource.html" title="class in org.apache.tools.ant.types">Resource</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.Resource.UNKNOWN_DATETIME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;long</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/Resource.html#UNKNOWN_DATETIME">UNKNOWN_DATETIME</A></CODE></TD>
<TD ALIGN="right"><CODE>0L</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.Resource.UNKNOWN_SIZE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;long</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/Resource.html#UNKNOWN_SIZE">UNKNOWN_SIZE</A></CODE></TD>
<TD ALIGN="right"><CODE>-1L</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.types.<A HREF="org/apache/tools/ant/types/Substitution.html" title="class in org.apache.tools.ant.types">Substitution</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.Substitution.DATA_TYPE_NAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/Substitution.html#DATA_TYPE_NAME">DATA_TYPE_NAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"substitition"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.types.<A HREF="org/apache/tools/ant/types/XMLCatalog.html" title="class in org.apache.tools.ant.types">XMLCatalog</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.XMLCatalog.APACHE_RESOLVER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/XMLCatalog.html#APACHE_RESOLVER">APACHE_RESOLVER</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.apache.tools.ant.types.resolver.ApacheCatalogResolver"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.XMLCatalog.CATALOG_RESOLVER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/XMLCatalog.html#CATALOG_RESOLVER">CATALOG_RESOLVER</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.apache.xml.resolver.tools.CatalogResolver"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.types.optional.depend.<A HREF="org/apache/tools/ant/types/optional/depend/DependScanner.html" title="class in org.apache.tools.ant.types.optional.depend">DependScanner</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.depend.DependScanner.DEFAULT_ANALYZER_CLASS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/depend/DependScanner.html#DEFAULT_ANALYZER_CLASS">DEFAULT_ANALYZER_CLASS</A></CODE></TD>
<TD ALIGN="right"><CODE>"org.apache.tools.ant.util.depend.bcel.FullAnalyzer"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.types.optional.image.<A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html" title="class in org.apache.tools.ant.types.optional.image">ColorMapper</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.image.ColorMapper.COLOR_BLACK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html#COLOR_BLACK">COLOR_BLACK</A></CODE></TD>
<TD ALIGN="right"><CODE>"black"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.image.ColorMapper.COLOR_BLUE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html#COLOR_BLUE">COLOR_BLUE</A></CODE></TD>
<TD ALIGN="right"><CODE>"blue"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.image.ColorMapper.COLOR_CYAN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html#COLOR_CYAN">COLOR_CYAN</A></CODE></TD>
<TD ALIGN="right"><CODE>"cyan"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.image.ColorMapper.COLOR_DARKGRAY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html#COLOR_DARKGRAY">COLOR_DARKGRAY</A></CODE></TD>
<TD ALIGN="right"><CODE>"darkgray"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.image.ColorMapper.COLOR_DARKGREY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html#COLOR_DARKGREY">COLOR_DARKGREY</A></CODE></TD>
<TD ALIGN="right"><CODE>"darkgrey"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.image.ColorMapper.COLOR_GRAY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html#COLOR_GRAY">COLOR_GRAY</A></CODE></TD>
<TD ALIGN="right"><CODE>"gray"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.image.ColorMapper.COLOR_GREEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html#COLOR_GREEN">COLOR_GREEN</A></CODE></TD>
<TD ALIGN="right"><CODE>"green"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.image.ColorMapper.COLOR_GREY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html#COLOR_GREY">COLOR_GREY</A></CODE></TD>
<TD ALIGN="right"><CODE>"grey"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.image.ColorMapper.COLOR_LIGHTGRAY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html#COLOR_LIGHTGRAY">COLOR_LIGHTGRAY</A></CODE></TD>
<TD ALIGN="right"><CODE>"lightgray"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.image.ColorMapper.COLOR_LIGHTGREY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html#COLOR_LIGHTGREY">COLOR_LIGHTGREY</A></CODE></TD>
<TD ALIGN="right"><CODE>"lightgrey"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.image.ColorMapper.COLOR_MAGENTA"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html#COLOR_MAGENTA">COLOR_MAGENTA</A></CODE></TD>
<TD ALIGN="right"><CODE>"magenta"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.image.ColorMapper.COLOR_ORANGE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html#COLOR_ORANGE">COLOR_ORANGE</A></CODE></TD>
<TD ALIGN="right"><CODE>"orange"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.image.ColorMapper.COLOR_PINK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html#COLOR_PINK">COLOR_PINK</A></CODE></TD>
<TD ALIGN="right"><CODE>"pink"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.image.ColorMapper.COLOR_RED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html#COLOR_RED">COLOR_RED</A></CODE></TD>
<TD ALIGN="right"><CODE>"red"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.image.ColorMapper.COLOR_WHITE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html#COLOR_WHITE">COLOR_WHITE</A></CODE></TD>
<TD ALIGN="right"><CODE>"white"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.optional.image.ColorMapper.COLOR_YELLOW"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/optional/image/ColorMapper.html#COLOR_YELLOW">COLOR_YELLOW</A></CODE></TD>
<TD ALIGN="right"><CODE>"yellow"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.types.selectors.<A HREF="org/apache/tools/ant/types/selectors/ContainsRegexpSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsRegexpSelector</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.ContainsRegexpSelector.EXPRESSION_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/ContainsRegexpSelector.html#EXPRESSION_KEY">EXPRESSION_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"expression"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.types.selectors.<A HREF="org/apache/tools/ant/types/selectors/ContainsSelector.html" title="class in org.apache.tools.ant.types.selectors">ContainsSelector</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.ContainsSelector.CASE_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/ContainsSelector.html#CASE_KEY">CASE_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"casesensitive"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.ContainsSelector.CONTAINS_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/ContainsSelector.html#CONTAINS_KEY">CONTAINS_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"text"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.ContainsSelector.EXPRESSION_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/ContainsSelector.html#EXPRESSION_KEY">EXPRESSION_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"expression"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.ContainsSelector.WHITESPACE_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/ContainsSelector.html#WHITESPACE_KEY">WHITESPACE_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"ignorewhitespace"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.types.selectors.<A HREF="org/apache/tools/ant/types/selectors/DateSelector.html" title="class in org.apache.tools.ant.types.selectors">DateSelector</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.DateSelector.CHECKDIRS_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/DateSelector.html#CHECKDIRS_KEY">CHECKDIRS_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"checkdirs"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.DateSelector.DATETIME_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/DateSelector.html#DATETIME_KEY">DATETIME_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"datetime"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.DateSelector.GRANULARITY_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/DateSelector.html#GRANULARITY_KEY">GRANULARITY_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"granularity"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.DateSelector.MILLIS_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/DateSelector.html#MILLIS_KEY">MILLIS_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"millis"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.DateSelector.PATTERN_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/DateSelector.html#PATTERN_KEY">PATTERN_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"pattern"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.DateSelector.WHEN_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/DateSelector.html#WHEN_KEY">WHEN_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"when"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.types.selectors.<A HREF="org/apache/tools/ant/types/selectors/DepthSelector.html" title="class in org.apache.tools.ant.types.selectors">DepthSelector</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.DepthSelector.MAX_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/DepthSelector.html#MAX_KEY">MAX_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"max"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.DepthSelector.MIN_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/DepthSelector.html#MIN_KEY">MIN_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"min"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.types.selectors.<A HREF="org/apache/tools/ant/types/selectors/FilenameSelector.html" title="class in org.apache.tools.ant.types.selectors">FilenameSelector</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.FilenameSelector.CASE_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/FilenameSelector.html#CASE_KEY">CASE_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"casesensitive"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.FilenameSelector.NAME_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/FilenameSelector.html#NAME_KEY">NAME_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"name"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.FilenameSelector.NEGATE_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/FilenameSelector.html#NEGATE_KEY">NEGATE_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"negate"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.types.selectors.<A HREF="org/apache/tools/ant/types/selectors/SizeSelector.html" title="class in org.apache.tools.ant.types.selectors">SizeSelector</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.SizeSelector.SIZE_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/SizeSelector.html#SIZE_KEY">SIZE_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"value"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.SizeSelector.UNITS_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/SizeSelector.html#UNITS_KEY">UNITS_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"units"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.SizeSelector.WHEN_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/SizeSelector.html#WHEN_KEY">WHEN_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"when"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.types.selectors.<A HREF="org/apache/tools/ant/types/selectors/TypeSelector.html" title="class in org.apache.tools.ant.types.selectors">TypeSelector</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.TypeSelector.TYPE_KEY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/TypeSelector.html#TYPE_KEY">TYPE_KEY</A></CODE></TD>
<TD ALIGN="right"><CODE>"type"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.types.selectors.<A HREF="org/apache/tools/ant/types/selectors/TypeSelector.FileType.html" title="class in org.apache.tools.ant.types.selectors">TypeSelector.FileType</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.TypeSelector.FileType.DIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/TypeSelector.FileType.html#DIR">DIR</A></CODE></TD>
<TD ALIGN="right"><CODE>"dir"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.types.selectors.TypeSelector.FileType.FILE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/types/selectors/TypeSelector.FileType.html#FILE">FILE</A></CODE></TD>
<TD ALIGN="right"><CODE>"file"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.util.<A HREF="org/apache/tools/ant/util/ClasspathUtils.html" title="class in org.apache.tools.ant.util">ClasspathUtils</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ClasspathUtils.REUSE_LOADER_REF"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ClasspathUtils.html#REUSE_LOADER_REF">REUSE_LOADER_REF</A></CODE></TD>
<TD ALIGN="right"><CODE>"ant.reuse.loader"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.util.<A HREF="org/apache/tools/ant/util/DateUtils.html" title="class in org.apache.tools.ant.util">DateUtils</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.DateUtils.ISO8601_DATE_PATTERN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/DateUtils.html#ISO8601_DATE_PATTERN">ISO8601_DATE_PATTERN</A></CODE></TD>
<TD ALIGN="right"><CODE>"yyyy-MM-dd"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.DateUtils.ISO8601_DATETIME_PATTERN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/DateUtils.html#ISO8601_DATETIME_PATTERN">ISO8601_DATETIME_PATTERN</A></CODE></TD>
<TD ALIGN="right"><CODE>"yyyy-MM-dd\'T\'HH:mm:ss"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.DateUtils.ISO8601_TIME_PATTERN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/DateUtils.html#ISO8601_TIME_PATTERN">ISO8601_TIME_PATTERN</A></CODE></TD>
<TD ALIGN="right"><CODE>"HH:mm:ss"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.util.<A HREF="org/apache/tools/ant/util/FileUtils.html" title="class in org.apache.tools.ant.util">FileUtils</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.FileUtils.FAT_FILE_TIMESTAMP_GRANULARITY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;long</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/FileUtils.html#FAT_FILE_TIMESTAMP_GRANULARITY">FAT_FILE_TIMESTAMP_GRANULARITY</A></CODE></TD>
<TD ALIGN="right"><CODE>2000L</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.FileUtils.NTFS_FILE_TIMESTAMP_GRANULARITY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;long</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/FileUtils.html#NTFS_FILE_TIMESTAMP_GRANULARITY">NTFS_FILE_TIMESTAMP_GRANULARITY</A></CODE></TD>
<TD ALIGN="right"><CODE>1L</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.FileUtils.UNIX_FILE_TIMESTAMP_GRANULARITY"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;long</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/FileUtils.html#UNIX_FILE_TIMESTAMP_GRANULARITY">UNIX_FILE_TIMESTAMP_GRANULARITY</A></CODE></TD>
<TD ALIGN="right"><CODE>1000L</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.util.<A HREF="org/apache/tools/ant/util/JavaEnvUtils.html" title="class in org.apache.tools.ant.util">JavaEnvUtils</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.JavaEnvUtils.JAVA_1_0"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_0">JAVA_1_0</A></CODE></TD>
<TD ALIGN="right"><CODE>"1.0"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.JavaEnvUtils.JAVA_1_1"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_1">JAVA_1_1</A></CODE></TD>
<TD ALIGN="right"><CODE>"1.1"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.JavaEnvUtils.JAVA_1_2"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_2">JAVA_1_2</A></CODE></TD>
<TD ALIGN="right"><CODE>"1.2"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.JavaEnvUtils.JAVA_1_3"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_3">JAVA_1_3</A></CODE></TD>
<TD ALIGN="right"><CODE>"1.3"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.JavaEnvUtils.JAVA_1_4"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_4">JAVA_1_4</A></CODE></TD>
<TD ALIGN="right"><CODE>"1.4"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.JavaEnvUtils.JAVA_1_5"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_5">JAVA_1_5</A></CODE></TD>
<TD ALIGN="right"><CODE>"1.5"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.JavaEnvUtils.JAVA_1_6"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/JavaEnvUtils.html#JAVA_1_6">JAVA_1_6</A></CODE></TD>
<TD ALIGN="right"><CODE>"1.6"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.util.<A HREF="org/apache/tools/ant/util/OutputStreamFunneler.html" title="class in org.apache.tools.ant.util">OutputStreamFunneler</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.OutputStreamFunneler.DEFAULT_TIMEOUT_MILLIS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;long</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/OutputStreamFunneler.html#DEFAULT_TIMEOUT_MILLIS">DEFAULT_TIMEOUT_MILLIS</A></CODE></TD>
<TD ALIGN="right"><CODE>1000L</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.util.<A HREF="org/apache/tools/ant/util/ProxySetup.html" title="class in org.apache.tools.ant.util">ProxySetup</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ProxySetup.FTP_NON_PROXY_HOSTS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ProxySetup.html#FTP_NON_PROXY_HOSTS">FTP_NON_PROXY_HOSTS</A></CODE></TD>
<TD ALIGN="right"><CODE>"ftp.nonProxyHosts"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ProxySetup.FTP_PROXY_HOST"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ProxySetup.html#FTP_PROXY_HOST">FTP_PROXY_HOST</A></CODE></TD>
<TD ALIGN="right"><CODE>"ftp.proxyHost"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ProxySetup.FTP_PROXY_PORT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ProxySetup.html#FTP_PROXY_PORT">FTP_PROXY_PORT</A></CODE></TD>
<TD ALIGN="right"><CODE>"ftp.proxyPort"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ProxySetup.HTTP_NON_PROXY_HOSTS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ProxySetup.html#HTTP_NON_PROXY_HOSTS">HTTP_NON_PROXY_HOSTS</A></CODE></TD>
<TD ALIGN="right"><CODE>"http.nonProxyHosts"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ProxySetup.HTTP_PROXY_HOST"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ProxySetup.html#HTTP_PROXY_HOST">HTTP_PROXY_HOST</A></CODE></TD>
<TD ALIGN="right"><CODE>"http.proxyHost"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ProxySetup.HTTP_PROXY_PASSWORD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ProxySetup.html#HTTP_PROXY_PASSWORD">HTTP_PROXY_PASSWORD</A></CODE></TD>
<TD ALIGN="right"><CODE>"http.proxyPassword"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ProxySetup.HTTP_PROXY_PORT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ProxySetup.html#HTTP_PROXY_PORT">HTTP_PROXY_PORT</A></CODE></TD>
<TD ALIGN="right"><CODE>"http.proxyPort"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ProxySetup.HTTP_PROXY_USERNAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ProxySetup.html#HTTP_PROXY_USERNAME">HTTP_PROXY_USERNAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"http.proxyUser"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ProxySetup.HTTPS_NON_PROXY_HOSTS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ProxySetup.html#HTTPS_NON_PROXY_HOSTS">HTTPS_NON_PROXY_HOSTS</A></CODE></TD>
<TD ALIGN="right"><CODE>"https.nonProxyHosts"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ProxySetup.HTTPS_PROXY_HOST"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ProxySetup.html#HTTPS_PROXY_HOST">HTTPS_PROXY_HOST</A></CODE></TD>
<TD ALIGN="right"><CODE>"https.proxyHost"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ProxySetup.HTTPS_PROXY_PORT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ProxySetup.html#HTTPS_PROXY_PORT">HTTPS_PROXY_PORT</A></CODE></TD>
<TD ALIGN="right"><CODE>"https.proxyPort"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ProxySetup.SOCKS_PROXY_HOST"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ProxySetup.html#SOCKS_PROXY_HOST">SOCKS_PROXY_HOST</A></CODE></TD>
<TD ALIGN="right"><CODE>"socksProxyHost"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ProxySetup.SOCKS_PROXY_PASSWORD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ProxySetup.html#SOCKS_PROXY_PASSWORD">SOCKS_PROXY_PASSWORD</A></CODE></TD>
<TD ALIGN="right"><CODE>"java.net.socks.password"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ProxySetup.SOCKS_PROXY_PORT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ProxySetup.html#SOCKS_PROXY_PORT">SOCKS_PROXY_PORT</A></CODE></TD>
<TD ALIGN="right"><CODE>"socksProxyPort"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ProxySetup.SOCKS_PROXY_USERNAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ProxySetup.html#SOCKS_PROXY_USERNAME">SOCKS_PROXY_USERNAME</A></CODE></TD>
<TD ALIGN="right"><CODE>"java.net.socks.username"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.ProxySetup.USE_SYSTEM_PROXIES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/ProxySetup.html#USE_SYSTEM_PROXIES">USE_SYSTEM_PROXIES</A></CODE></TD>
<TD ALIGN="right"><CODE>"java.net.useSystemProxies"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.util.<A HREF="org/apache/tools/ant/util/Retryable.html" title="interface in org.apache.tools.ant.util">Retryable</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.Retryable.RETRY_FOREVER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/Retryable.html#RETRY_FOREVER">RETRY_FOREVER</A></CODE></TD>
<TD ALIGN="right"><CODE>-1</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.util.<A HREF="org/apache/tools/ant/util/UUEncoder.html" title="class in org.apache.tools.ant.util">UUEncoder</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.UUEncoder.DEFAULT_MODE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/UUEncoder.html#DEFAULT_MODE">DEFAULT_MODE</A></CODE></TD>
<TD ALIGN="right"><CODE>644</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.util.<A HREF="org/apache/tools/ant/util/Watchdog.html" title="class in org.apache.tools.ant.util">Watchdog</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.Watchdog.ERROR_INVALID_TIMEOUT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/Watchdog.html#ERROR_INVALID_TIMEOUT">ERROR_INVALID_TIMEOUT</A></CODE></TD>
<TD ALIGN="right"><CODE>"timeout less than 1."</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.util.<A HREF="org/apache/tools/ant/util/XmlConstants.html" title="class in org.apache.tools.ant.util">XmlConstants</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.XmlConstants.FEATURE_DISALLOW_DTD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/XmlConstants.html#FEATURE_DISALLOW_DTD">FEATURE_DISALLOW_DTD</A></CODE></TD>
<TD ALIGN="right"><CODE>"http://apache.org/xml/features/disallow-doctype-decl"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.XmlConstants.FEATURE_EXTERNAL_ENTITIES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/XmlConstants.html#FEATURE_EXTERNAL_ENTITIES">FEATURE_EXTERNAL_ENTITIES</A></CODE></TD>
<TD ALIGN="right"><CODE>"http://xml.org/sax/features/external-general-entities"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.XmlConstants.FEATURE_JAXP12_SCHEMA_LANGUAGE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/XmlConstants.html#FEATURE_JAXP12_SCHEMA_LANGUAGE">FEATURE_JAXP12_SCHEMA_LANGUAGE</A></CODE></TD>
<TD ALIGN="right"><CODE>"http://java.sun.com/xml/jaxp/properties/schemaLanguage"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.XmlConstants.FEATURE_JAXP12_SCHEMA_SOURCE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/XmlConstants.html#FEATURE_JAXP12_SCHEMA_SOURCE">FEATURE_JAXP12_SCHEMA_SOURCE</A></CODE></TD>
<TD ALIGN="right"><CODE>"http://java.sun.com/xml/jaxp/properties/schemaSource"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.XmlConstants.FEATURE_NAMESPACES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/XmlConstants.html#FEATURE_NAMESPACES">FEATURE_NAMESPACES</A></CODE></TD>
<TD ALIGN="right"><CODE>"http://xml.org/sax/features/namespaces"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.XmlConstants.FEATURE_VALIDATION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/XmlConstants.html#FEATURE_VALIDATION">FEATURE_VALIDATION</A></CODE></TD>
<TD ALIGN="right"><CODE>"http://xml.org/sax/features/validation"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.XmlConstants.FEATURE_XSD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/XmlConstants.html#FEATURE_XSD">FEATURE_XSD</A></CODE></TD>
<TD ALIGN="right"><CODE>"http://apache.org/xml/features/validation/schema"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.XmlConstants.FEATURE_XSD_FULL_VALIDATION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/XmlConstants.html#FEATURE_XSD_FULL_VALIDATION">FEATURE_XSD_FULL_VALIDATION</A></CODE></TD>
<TD ALIGN="right"><CODE>"http://apache.org/xml/features/validation/schema-full-checking"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.XmlConstants.PROPERTY_NO_NAMESPACE_SCHEMA_LOCATION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/XmlConstants.html#PROPERTY_NO_NAMESPACE_SCHEMA_LOCATION">PROPERTY_NO_NAMESPACE_SCHEMA_LOCATION</A></CODE></TD>
<TD ALIGN="right"><CODE>"http://apache.org/xml/properties/schema/external-noNamespaceSchemaLocation"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.XmlConstants.PROPERTY_SCHEMA_LOCATION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/XmlConstants.html#PROPERTY_SCHEMA_LOCATION">PROPERTY_SCHEMA_LOCATION</A></CODE></TD>
<TD ALIGN="right"><CODE>"http://apache.org/xml/properties/schema/external-schemaLocation"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.XmlConstants.URI_XSD"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/XmlConstants.html#URI_XSD">URI_XSD</A></CODE></TD>
<TD ALIGN="right"><CODE>"http://www.w3.org/2001/XMLSchema"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.util.depend.<A HREF="org/apache/tools/ant/util/depend/AbstractAnalyzer.html" title="class in org.apache.tools.ant.util.depend">AbstractAnalyzer</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.depend.AbstractAnalyzer.MAX_LOOPS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/depend/AbstractAnalyzer.html#MAX_LOOPS">MAX_LOOPS</A></CODE></TD>
<TD ALIGN="right"><CODE>1000</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.util.java15.<A HREF="org/apache/tools/ant/util/java15/ProxyDiagnostics.html" title="class in org.apache.tools.ant.util.java15">ProxyDiagnostics</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.java15.ProxyDiagnostics.DEFAULT_DESTINATION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/java15/ProxyDiagnostics.html#DEFAULT_DESTINATION">DEFAULT_DESTINATION</A></CODE></TD>
<TD ALIGN="right"><CODE>"http://ant.apache.org/"</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.util.regexp.<A HREF="org/apache/tools/ant/util/regexp/Regexp.html" title="interface in org.apache.tools.ant.util.regexp">Regexp</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.regexp.Regexp.REPLACE_ALL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/regexp/Regexp.html#REPLACE_ALL">REPLACE_ALL</A></CODE></TD>
<TD ALIGN="right"><CODE>16</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.regexp.Regexp.REPLACE_FIRST"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/regexp/Regexp.html#REPLACE_FIRST">REPLACE_FIRST</A></CODE></TD>
<TD ALIGN="right"><CODE>1</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.ant.util.regexp.<A HREF="org/apache/tools/ant/util/regexp/RegexpMatcher.html" title="interface in org.apache.tools.ant.util.regexp">RegexpMatcher</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.regexp.RegexpMatcher.MATCH_CASE_INSENSITIVE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/regexp/RegexpMatcher.html#MATCH_CASE_INSENSITIVE">MATCH_CASE_INSENSITIVE</A></CODE></TD>
<TD ALIGN="right"><CODE>256</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.regexp.RegexpMatcher.MATCH_DEFAULT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/regexp/RegexpMatcher.html#MATCH_DEFAULT">MATCH_DEFAULT</A></CODE></TD>
<TD ALIGN="right"><CODE>0</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.regexp.RegexpMatcher.MATCH_MULTILINE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/regexp/RegexpMatcher.html#MATCH_MULTILINE">MATCH_MULTILINE</A></CODE></TD>
<TD ALIGN="right"><CODE>4096</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.ant.util.regexp.RegexpMatcher.MATCH_SINGLELINE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/ant/util/regexp/RegexpMatcher.html#MATCH_SINGLELINE">MATCH_SINGLELINE</A></CODE></TD>
<TD ALIGN="right"><CODE>65536</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.bzip2.<A HREF="org/apache/tools/bzip2/BZip2Constants.html" title="interface in org.apache.tools.bzip2">BZip2Constants</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.BZip2Constants.baseBlockSize"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/BZip2Constants.html#baseBlockSize">baseBlockSize</A></CODE></TD>
<TD ALIGN="right"><CODE>100000</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.BZip2Constants.G_SIZE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/BZip2Constants.html#G_SIZE">G_SIZE</A></CODE></TD>
<TD ALIGN="right"><CODE>50</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.BZip2Constants.MAX_ALPHA_SIZE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/BZip2Constants.html#MAX_ALPHA_SIZE">MAX_ALPHA_SIZE</A></CODE></TD>
<TD ALIGN="right"><CODE>258</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.BZip2Constants.MAX_CODE_LEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/BZip2Constants.html#MAX_CODE_LEN">MAX_CODE_LEN</A></CODE></TD>
<TD ALIGN="right"><CODE>23</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.BZip2Constants.MAX_SELECTORS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/BZip2Constants.html#MAX_SELECTORS">MAX_SELECTORS</A></CODE></TD>
<TD ALIGN="right"><CODE>18002</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.BZip2Constants.N_GROUPS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/BZip2Constants.html#N_GROUPS">N_GROUPS</A></CODE></TD>
<TD ALIGN="right"><CODE>6</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.BZip2Constants.N_ITERS"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/BZip2Constants.html#N_ITERS">N_ITERS</A></CODE></TD>
<TD ALIGN="right"><CODE>4</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.BZip2Constants.NUM_OVERSHOOT_BYTES"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/BZip2Constants.html#NUM_OVERSHOOT_BYTES">NUM_OVERSHOOT_BYTES</A></CODE></TD>
<TD ALIGN="right"><CODE>20</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.BZip2Constants.RUNA"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/BZip2Constants.html#RUNA">RUNA</A></CODE></TD>
<TD ALIGN="right"><CODE>0</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.BZip2Constants.RUNB"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/BZip2Constants.html#RUNB">RUNB</A></CODE></TD>
<TD ALIGN="right"><CODE>1</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.bzip2.<A HREF="org/apache/tools/bzip2/CBZip2OutputStream.html" title="class in org.apache.tools.bzip2">CBZip2OutputStream</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.CBZip2OutputStream.CLEARMASK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/CBZip2OutputStream.html#CLEARMASK">CLEARMASK</A></CODE></TD>
<TD ALIGN="right"><CODE>-2097153</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.CBZip2OutputStream.DEPTH_THRESH"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/CBZip2OutputStream.html#DEPTH_THRESH">DEPTH_THRESH</A></CODE></TD>
<TD ALIGN="right"><CODE>10</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.CBZip2OutputStream.GREATER_ICOST"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/CBZip2OutputStream.html#GREATER_ICOST">GREATER_ICOST</A></CODE></TD>
<TD ALIGN="right"><CODE>15</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.CBZip2OutputStream.LESSER_ICOST"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/CBZip2OutputStream.html#LESSER_ICOST">LESSER_ICOST</A></CODE></TD>
<TD ALIGN="right"><CODE>0</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.CBZip2OutputStream.MAX_BLOCKSIZE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/CBZip2OutputStream.html#MAX_BLOCKSIZE">MAX_BLOCKSIZE</A></CODE></TD>
<TD ALIGN="right"><CODE>9</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.CBZip2OutputStream.MIN_BLOCKSIZE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/CBZip2OutputStream.html#MIN_BLOCKSIZE">MIN_BLOCKSIZE</A></CODE></TD>
<TD ALIGN="right"><CODE>1</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.CBZip2OutputStream.QSORT_STACK_SIZE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/CBZip2OutputStream.html#QSORT_STACK_SIZE">QSORT_STACK_SIZE</A></CODE></TD>
<TD ALIGN="right"><CODE>1000</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.CBZip2OutputStream.SETMASK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/CBZip2OutputStream.html#SETMASK">SETMASK</A></CODE></TD>
<TD ALIGN="right"><CODE>2097152</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.CBZip2OutputStream.SMALL_THRESH"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/CBZip2OutputStream.html#SMALL_THRESH">SMALL_THRESH</A></CODE></TD>
<TD ALIGN="right"><CODE>20</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.bzip2.CBZip2OutputStream.WORK_FACTOR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>protected&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/bzip2/CBZip2OutputStream.html#WORK_FACTOR">WORK_FACTOR</A></CODE></TD>
<TD ALIGN="right"><CODE>30</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.mail.<A HREF="org/apache/tools/mail/MailMessage.html" title="class in org.apache.tools.mail">MailMessage</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.mail.MailMessage.DEFAULT_HOST"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/mail/MailMessage.html#DEFAULT_HOST">DEFAULT_HOST</A></CODE></TD>
<TD ALIGN="right"><CODE>"localhost"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.mail.MailMessage.DEFAULT_PORT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/mail/MailMessage.html#DEFAULT_PORT">DEFAULT_PORT</A></CODE></TD>
<TD ALIGN="right"><CODE>25</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.tar.<A HREF="org/apache/tools/tar/TarBuffer.html" title="class in org.apache.tools.tar">TarBuffer</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarBuffer.DEFAULT_BLKSIZE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarBuffer.html#DEFAULT_BLKSIZE">DEFAULT_BLKSIZE</A></CODE></TD>
<TD ALIGN="right"><CODE>10240</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarBuffer.DEFAULT_RCDSIZE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarBuffer.html#DEFAULT_RCDSIZE">DEFAULT_RCDSIZE</A></CODE></TD>
<TD ALIGN="right"><CODE>512</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.tar.<A HREF="org/apache/tools/tar/TarConstants.html" title="interface in org.apache.tools.tar">TarConstants</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.CHKSUMLEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#CHKSUMLEN">CHKSUMLEN</A></CODE></TD>
<TD ALIGN="right"><CODE>8</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.DEVLEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#DEVLEN">DEVLEN</A></CODE></TD>
<TD ALIGN="right"><CODE>8</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.GIDLEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#GIDLEN">GIDLEN</A></CODE></TD>
<TD ALIGN="right"><CODE>8</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.GNAMELEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#GNAMELEN">GNAMELEN</A></CODE></TD>
<TD ALIGN="right"><CODE>32</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.GNU_LONGLINK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#GNU_LONGLINK">GNU_LONGLINK</A></CODE></TD>
<TD ALIGN="right"><CODE>"././@LongLink"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.GNU_TMAGIC"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#GNU_TMAGIC">GNU_TMAGIC</A></CODE></TD>
<TD ALIGN="right"><CODE>"ustar  "</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.LF_BLK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;byte</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#LF_BLK">LF_BLK</A></CODE></TD>
<TD ALIGN="right"><CODE>52</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.LF_CHR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;byte</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#LF_CHR">LF_CHR</A></CODE></TD>
<TD ALIGN="right"><CODE>51</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.LF_CONTIG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;byte</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#LF_CONTIG">LF_CONTIG</A></CODE></TD>
<TD ALIGN="right"><CODE>55</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.LF_DIR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;byte</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#LF_DIR">LF_DIR</A></CODE></TD>
<TD ALIGN="right"><CODE>53</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.LF_FIFO"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;byte</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#LF_FIFO">LF_FIFO</A></CODE></TD>
<TD ALIGN="right"><CODE>54</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.LF_GNUTYPE_LONGNAME"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;byte</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#LF_GNUTYPE_LONGNAME">LF_GNUTYPE_LONGNAME</A></CODE></TD>
<TD ALIGN="right"><CODE>76</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.LF_LINK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;byte</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#LF_LINK">LF_LINK</A></CODE></TD>
<TD ALIGN="right"><CODE>49</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.LF_NORMAL"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;byte</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#LF_NORMAL">LF_NORMAL</A></CODE></TD>
<TD ALIGN="right"><CODE>48</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.LF_OLDNORM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;byte</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#LF_OLDNORM">LF_OLDNORM</A></CODE></TD>
<TD ALIGN="right"><CODE>0</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.LF_SYMLINK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;byte</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#LF_SYMLINK">LF_SYMLINK</A></CODE></TD>
<TD ALIGN="right"><CODE>50</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.MAGICLEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#MAGICLEN">MAGICLEN</A></CODE></TD>
<TD ALIGN="right"><CODE>8</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.MAXSIZE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;long</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#MAXSIZE">MAXSIZE</A></CODE></TD>
<TD ALIGN="right"><CODE>8589934591L</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.MODELEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#MODELEN">MODELEN</A></CODE></TD>
<TD ALIGN="right"><CODE>8</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.MODTIMELEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#MODTIMELEN">MODTIMELEN</A></CODE></TD>
<TD ALIGN="right"><CODE>12</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.NAMELEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#NAMELEN">NAMELEN</A></CODE></TD>
<TD ALIGN="right"><CODE>100</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.SIZELEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#SIZELEN">SIZELEN</A></CODE></TD>
<TD ALIGN="right"><CODE>12</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.TMAGIC"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;java.lang.String</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#TMAGIC">TMAGIC</A></CODE></TD>
<TD ALIGN="right"><CODE>"ustar"</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.UIDLEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#UIDLEN">UIDLEN</A></CODE></TD>
<TD ALIGN="right"><CODE>8</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarConstants.UNAMELEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarConstants.html#UNAMELEN">UNAMELEN</A></CODE></TD>
<TD ALIGN="right"><CODE>32</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.tar.<A HREF="org/apache/tools/tar/TarEntry.html" title="class in org.apache.tools.tar">TarEntry</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarEntry.DEFAULT_DIR_MODE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarEntry.html#DEFAULT_DIR_MODE">DEFAULT_DIR_MODE</A></CODE></TD>
<TD ALIGN="right"><CODE>16877</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarEntry.DEFAULT_FILE_MODE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarEntry.html#DEFAULT_FILE_MODE">DEFAULT_FILE_MODE</A></CODE></TD>
<TD ALIGN="right"><CODE>33188</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarEntry.MAX_NAMELEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarEntry.html#MAX_NAMELEN">MAX_NAMELEN</A></CODE></TD>
<TD ALIGN="right"><CODE>31</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarEntry.MILLIS_PER_SECOND"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarEntry.html#MILLIS_PER_SECOND">MILLIS_PER_SECOND</A></CODE></TD>
<TD ALIGN="right"><CODE>1000</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.tar.<A HREF="org/apache/tools/tar/TarOutputStream.html" title="class in org.apache.tools.tar">TarOutputStream</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarOutputStream.LONGFILE_ERROR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarOutputStream.html#LONGFILE_ERROR">LONGFILE_ERROR</A></CODE></TD>
<TD ALIGN="right"><CODE>0</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarOutputStream.LONGFILE_GNU"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarOutputStream.html#LONGFILE_GNU">LONGFILE_GNU</A></CODE></TD>
<TD ALIGN="right"><CODE>2</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.tar.TarOutputStream.LONGFILE_TRUNCATE"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/tar/TarOutputStream.html#LONGFILE_TRUNCATE">LONGFILE_TRUNCATE</A></CODE></TD>
<TD ALIGN="right"><CODE>1</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.zip.<A HREF="org/apache/tools/zip/UnixStat.html" title="interface in org.apache.tools.zip">UnixStat</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.UnixStat.DEFAULT_DIR_PERM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/zip/UnixStat.html#DEFAULT_DIR_PERM">DEFAULT_DIR_PERM</A></CODE></TD>
<TD ALIGN="right"><CODE>493</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.UnixStat.DEFAULT_FILE_PERM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/zip/UnixStat.html#DEFAULT_FILE_PERM">DEFAULT_FILE_PERM</A></CODE></TD>
<TD ALIGN="right"><CODE>420</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.UnixStat.DEFAULT_LINK_PERM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/zip/UnixStat.html#DEFAULT_LINK_PERM">DEFAULT_LINK_PERM</A></CODE></TD>
<TD ALIGN="right"><CODE>511</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.UnixStat.DIR_FLAG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/zip/UnixStat.html#DIR_FLAG">DIR_FLAG</A></CODE></TD>
<TD ALIGN="right"><CODE>16384</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.UnixStat.FILE_FLAG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/zip/UnixStat.html#FILE_FLAG">FILE_FLAG</A></CODE></TD>
<TD ALIGN="right"><CODE>32768</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.UnixStat.LINK_FLAG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/zip/UnixStat.html#LINK_FLAG">LINK_FLAG</A></CODE></TD>
<TD ALIGN="right"><CODE>40960</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.UnixStat.PERM_MASK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/zip/UnixStat.html#PERM_MASK">PERM_MASK</A></CODE></TD>
<TD ALIGN="right"><CODE>4095</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.zip.<A HREF="org/apache/tools/zip/ZipEntry.html" title="class in org.apache.tools.zip">ZipEntry</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENATT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENATT</CODE></TD>
<TD ALIGN="right"><CODE>36</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENATX"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENATX</CODE></TD>
<TD ALIGN="right"><CODE>38</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENCOM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENCOM</CODE></TD>
<TD ALIGN="right"><CODE>32</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENCRC"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENCRC</CODE></TD>
<TD ALIGN="right"><CODE>16</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENDSK"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENDSK</CODE></TD>
<TD ALIGN="right"><CODE>34</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENEXT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENEXT</CODE></TD>
<TD ALIGN="right"><CODE>30</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENFLG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENFLG</CODE></TD>
<TD ALIGN="right"><CODE>8</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENHDR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENHDR</CODE></TD>
<TD ALIGN="right"><CODE>46</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENHOW"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENHOW</CODE></TD>
<TD ALIGN="right"><CODE>10</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENLEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENLEN</CODE></TD>
<TD ALIGN="right"><CODE>24</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENNAM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENNAM</CODE></TD>
<TD ALIGN="right"><CODE>28</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENOFF"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENOFF</CODE></TD>
<TD ALIGN="right"><CODE>42</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENSIG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;long</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENSIG</CODE></TD>
<TD ALIGN="right"><CODE>33639248L</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENSIZ"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENSIZ</CODE></TD>
<TD ALIGN="right"><CODE>20</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENTIM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENTIM</CODE></TD>
<TD ALIGN="right"><CODE>12</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENVEM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENVEM</CODE></TD>
<TD ALIGN="right"><CODE>4</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.CENVER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>CENVER</CODE></TD>
<TD ALIGN="right"><CODE>6</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.ENDCOM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>ENDCOM</CODE></TD>
<TD ALIGN="right"><CODE>20</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.ENDHDR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>ENDHDR</CODE></TD>
<TD ALIGN="right"><CODE>22</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.ENDOFF"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>ENDOFF</CODE></TD>
<TD ALIGN="right"><CODE>16</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.ENDSIG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;long</CODE></FONT></TD>
<TD ALIGN="left"><CODE>ENDSIG</CODE></TD>
<TD ALIGN="right"><CODE>101010256L</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.ENDSIZ"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>ENDSIZ</CODE></TD>
<TD ALIGN="right"><CODE>12</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.ENDSUB"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>ENDSUB</CODE></TD>
<TD ALIGN="right"><CODE>8</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.ENDTOT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>ENDTOT</CODE></TD>
<TD ALIGN="right"><CODE>10</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.EXTCRC"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>EXTCRC</CODE></TD>
<TD ALIGN="right"><CODE>4</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.EXTHDR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>EXTHDR</CODE></TD>
<TD ALIGN="right"><CODE>16</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.EXTLEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>EXTLEN</CODE></TD>
<TD ALIGN="right"><CODE>12</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.EXTSIG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;long</CODE></FONT></TD>
<TD ALIGN="left"><CODE>EXTSIG</CODE></TD>
<TD ALIGN="right"><CODE>134695760L</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.EXTSIZ"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>EXTSIZ</CODE></TD>
<TD ALIGN="right"><CODE>8</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.LOCCRC"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>LOCCRC</CODE></TD>
<TD ALIGN="right"><CODE>14</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.LOCEXT"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>LOCEXT</CODE></TD>
<TD ALIGN="right"><CODE>28</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.LOCFLG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>LOCFLG</CODE></TD>
<TD ALIGN="right"><CODE>6</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.LOCHDR"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>LOCHDR</CODE></TD>
<TD ALIGN="right"><CODE>30</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.LOCHOW"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>LOCHOW</CODE></TD>
<TD ALIGN="right"><CODE>8</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.LOCLEN"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>LOCLEN</CODE></TD>
<TD ALIGN="right"><CODE>22</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.LOCNAM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>LOCNAM</CODE></TD>
<TD ALIGN="right"><CODE>26</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.LOCSIG"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;long</CODE></FONT></TD>
<TD ALIGN="left"><CODE>LOCSIG</CODE></TD>
<TD ALIGN="right"><CODE>67324752L</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.LOCSIZ"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>LOCSIZ</CODE></TD>
<TD ALIGN="right"><CODE>18</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.LOCTIM"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>LOCTIM</CODE></TD>
<TD ALIGN="right"><CODE>10</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipEntry.LOCVER"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE>LOCVER</CODE></TD>
<TD ALIGN="right"><CODE>4</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>

<TABLE BORDER="1" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
<TR BGCOLOR="#EEEEFF" CLASS="TableSubHeadingColor">
<TH ALIGN="left" COLSPAN="3">org.apache.tools.zip.<A HREF="org/apache/tools/zip/ZipOutputStream.html" title="class in org.apache.tools.zip">ZipOutputStream</A></TH>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipOutputStream.DEFAULT_COMPRESSION"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/zip/ZipOutputStream.html#DEFAULT_COMPRESSION">DEFAULT_COMPRESSION</A></CODE></TD>
<TD ALIGN="right"><CODE>-1</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipOutputStream.DEFLATED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/zip/ZipOutputStream.html#DEFLATED">DEFLATED</A></CODE></TD>
<TD ALIGN="right"><CODE>8</CODE></TD>
</TR>
<TR BGCOLOR="white" CLASS="TableRowColor">
<A NAME="org.apache.tools.zip.ZipOutputStream.STORED"><!-- --></A><TD ALIGN="right"><FONT SIZE="-1">
<CODE>public&nbsp;static&nbsp;final&nbsp;int</CODE></FONT></TD>
<TD ALIGN="left"><CODE><A HREF="org/apache/tools/zip/ZipOutputStream.html#STORED">STORED</A></CODE></TD>
<TD ALIGN="right"><CODE>0</CODE></TD>
</TR>
</FONT></TD>
</TR>
</TABLE>

<P>

<P>
<HR>


<!-- ======= START OF BOTTOM NAVBAR ====== -->
<A NAME="navbar_bottom"><!-- --></A>
<A HREF="#skip-navbar_bottom" title="Skip navigation links"></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_bottom_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Package</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <FONT CLASS="NavBarFont1">Class</FONT>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="overview-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
</EM>
</TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
&nbsp;PREV&nbsp;
&nbsp;NEXT</FONT></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="index.html?constant-values.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="constant-values.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<SCRIPT type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="allclasses-noframe.html"><B>All Classes</B></A>');
  }
  //-->
</SCRIPT>
<NOSCRIPT>
  <A HREF="allclasses-noframe.html"><B>All Classes</B></A>
</NOSCRIPT>


</FONT></TD>
</TR>
</TABLE>
<A NAME="skip-navbar_bottom"></A>
<!-- ======== END OF BOTTOM NAVBAR ======= -->

<HR>

</BODY>
</HTML>
