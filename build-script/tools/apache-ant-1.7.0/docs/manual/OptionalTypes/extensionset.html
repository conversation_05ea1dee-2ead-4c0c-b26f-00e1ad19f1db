<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>
<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="../stylesheets/style.css">
<title>ExtensionSet Type</title>
</head>

<body>
<h2><a>ExtensionSet</a></h2>
<p>Utility type that represents a set of Extensions.</p>
<p>Note that this type
works with extensions as defined by the "Optional Package" specification.
 For more information about optional packages, see the document
<em>Optional Package Versioning</em> in the documentation bundle for your
Java2 Standard Edition package, in file
<code>guide/extensions/versioning.html</code> or online at
<a href="http://java.sun.com/j2se/1.3/docs/guide/extensions/versioning.html">
http://java.sun.com/j2se/1.3/docs/guide/extensions/versioning.html</a>.</p>

<h3>Nested Elements</h3>

<h4>extension</h4>
<p><a href="extension.html">Extension</a> object to add to set.</p>

<h4>fileset</h4>
 <p><a href="../CoreTypes/fileset.html">FileSet</a>s all files contained
 contained within set that are jars and implement an extension are added
 to extension set.</p>

<h4>LibFileSet</h4>
 <p>All files contained contained within set that are jars and implement 
 an extension are added to extension set. However the extension information 
 may be modified by attributes of libfileset</p>

<h4>Examples</h4>
<blockquote><pre>
&lt;extension id=&quot;e1&quot;
    extensionName=&quot;MyExtensions&quot;
    specificationVersion=&quot;1.0&quot;
    specificationVendor=&quot;Peter Donald&quot;
    implementationVendorID=&quot;vv&quot;
    implementationVendor=&quot;Apache&quot;
    implementationVersion=&quot;2.0&quot;
    implementationURL=&quot;http://somewhere.com/myExt.jar&quot;/&gt;

&lt;libfileset id="lfs"
               includeUrl="true"
               includeImpl="false"
               dir="tools/lib"&gt;
  &lt;include name="*.jar"/&gt;
&lt;/libfileset&gt;

&lt;extensionSet id="exts"&gt;
  &lt;libfileset dir="lib"&gt;
    &lt;include name="*.jar"/&gt;
  &lt;/libfileset&gt;
  &lt;libfileset refid="lfs"/&gt;
  &lt;extension refid="e1"/&gt;
&lt;/extensionSet&gt;

</pre></blockquote>



</body>
</html>

