<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>Apache Ant User Manual - Introduction</title>
</head>

<body>
<h1><a name="introduction">Introduction</a></h1>
<p>Apache Ant is a Java-based build tool. In theory, it is kind of like
<i>make</i>, without <i>make</i>'s wrinkles.</p>
<h3>Why?</h3>
<p>Why another build tool when there is already
<i>make</i>,
<i>gnumake</i>,
<i>nmake</i>,
<i>jam</i>,
and
others? Because all those tools have limitations that Ant's original author
couldn't live with when developing software across multiple platforms.
Make-like
tools are inherently shell-based: they evaluate a set of dependencies,
then execute commands not unlike what you would issue on a shell.
This means that you
can easily extend these tools by using or writing any program for the OS that
you are working on; however, this also means that you limit yourself to the OS,
or at least the OS type, such as Unix, that you are working on.</p>
<p>Makefiles are inherently evil as well. Anybody who has worked on them for any
time has run into the dreaded tab problem. &quot;Is my command not executing
because I have a space in front of my tab?!!&quot; said the original author of
Ant way too many times. Tools like Jam took care of this to a great degree, but
still have yet another format to use and remember.</p>
<p>Ant is different. Instead of a model where it is extended with shell-based
commands, Ant is extended using Java classes. Instead of writing shell commands,
the configuration files are XML-based, calling out a target tree where various
tasks get executed. Each task is run by an object that implements a particular
Task interface.</p>
<p>Granted, this removes some of the expressive power that is inherent in being
able to construct a shell command such as
<nobr><code>`find . -name foo -exec rm {}`</code></nobr>, but it
gives you the ability to be cross-platform--to work anywhere and
everywhere. And
hey, if you really need to execute a shell command, Ant has an
<code>&lt;exec&gt;</code> task that
allows different commands to be executed based on the OS it is executing
on.</p>



</body>
</html>

