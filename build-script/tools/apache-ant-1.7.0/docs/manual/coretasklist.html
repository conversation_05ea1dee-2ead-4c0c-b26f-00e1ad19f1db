<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>Apache Ant User Manual</title>
<base target="mainFrame">
</head>

<body>

<h2><a href="toc.html" target="navFrame">Table of Contents</a></h2>

<a href="optionaltasklist.html" target="navFrame">Optional Tasks</a><br>
<a href="tasksoverview.html" target="mainFrame">Overview of Ant Tasks</a><br>
<a href="conceptstypeslist.html" target="navFrame">Concepts and Types</a><br>

<h3>Core Tasks</h3>
<a href="CoreTasks/ant.html">Ant</a><br>
<a href="CoreTasks/antcall.html">AntCall</a><br>
<a href="CoreTasks/antstructure.html">AntStructure</a><br>
<a href="CoreTasks/apply.html">Apply/<i>ExecOn</i></a><br>
<a href="CoreTasks/apt.html">Apt</a><br>
<a href="CoreTasks/available.html">Available</a><br>
<a href="CoreTasks/basename.html">Basename</a><br>
<a href="CoreTasks/buildnumber.html">BuildNumber</a><br>
<a href="CoreTasks/unpack.html">BUnzip2</a><br>
<a href="CoreTasks/pack.html">BZip2</a><br>
<a href="CoreTasks/checksum.html">Checksum</a><br>
<a href="CoreTasks/chmod.html">Chmod</a><br>
<a href="CoreTasks/concat.html">Concat</a><br>
<a href="CoreTasks/condition.html">Condition</a><br>
&nbsp;&nbsp;<a href="CoreTasks/conditions.html">Supported conditions</a><br>
<a href="CoreTasks/copy.html">Copy</a><br>
<a href="CoreTasks/copydir.html"><i>Copydir</i></a><br>
<a href="CoreTasks/copyfile.html"><i>Copyfile</i></a><br>
<a href="CoreTasks/cvs.html">Cvs</a><br>
<a href="CoreTasks/changelog.html">CvsChangeLog</a><br>
<a href="CoreTasks/cvsversion.html">CvsVersion</a><br>
<a href="CoreTasks/cvspass.html">CVSPass</a><br>
<a href="CoreTasks/cvstagdiff.html">CvsTagDiff</a><br>
<a href="CoreTasks/defaultexcludes.html">Defaultexcludes</a><br>
<a href="CoreTasks/delete.html">Delete</a><br>
<a href="CoreTasks/deltree.html"><i>Deltree</i></a><br>
<a href="CoreTasks/dependset.html">Dependset</a><br>
<a href="CoreTasks/diagnostics.html">Diagnostics</a><br>
<a href="CoreTasks/dirname.html">Dirname</a><br>
<a href="CoreTasks/ear.html">Ear</a><br>
<a href="CoreTasks/echo.html">Echo</a><br>
<a href="CoreTasks/echoxml.html">EchoXML</a><br>
<a href="CoreTasks/exec.html">Exec</a><br>
<a href="CoreTasks/fail.html">Fail</a><br>
<a href="CoreTasks/filter.html">Filter</a><br>
<a href="CoreTasks/fixcrlf.html">FixCRLF</a><br>
<a href="CoreTasks/genkey.html">GenKey</a><br>
<a href="CoreTasks/get.html">Get</a><br>
<a href="CoreTasks/unpack.html">GUnzip</a><br>
<a href="CoreTasks/pack.html">GZip</a><br>
<a href="CoreTasks/import.html">Import</a><br>
<a href="CoreTasks/input.html">Input</a><br>
<a href="CoreTasks/jar.html">Jar</a><br>
<a href="CoreTasks/java.html">Java</a><br>
<a href="CoreTasks/javac.html">Javac</a><br>
<a href="CoreTasks/javadoc.html">Javadoc/<i>Javadoc2</i></a><br>
<a href="CoreTasks/length.html">Length</a><br>
<a href="CoreTasks/loadfile.html">LoadFile</a><br>
<a href="CoreTasks/loadproperties.html">LoadProperties</a><br>
<a href="CoreTasks/loadresource.html">LoadResource</a><br>
<a href="CoreTasks/makeurl.html">MakeURL</a><br>
<a href="CoreTasks/mail.html">Mail</a><br>
<a href="CoreTasks/macrodef.html">MacroDef</a><br>
<a href="CoreTasks/manifest.html">Manifest</a><br>
<a href="CoreTasks/manifestclasspath.html">ManifestClassPath</a><br>
<a href="CoreTasks/mkdir.html">Mkdir</a><br>
<a href="CoreTasks/move.html">Move</a><br>
<a href="CoreTasks/nice.html">Nice</a><br>
<a href="CoreTasks/parallel.html">Parallel</a><br>
<a href="CoreTasks/patch.html">Patch</a><br>
<a href="CoreTasks/pathconvert.html">PathConvert</a><br>
<a href="CoreTasks/presetdef.html">PreSetDef</a><br>
<a href="CoreTasks/property.html">Property</a><br>
<a href="CoreTasks/recorder.html">Record</a><br>
<a href="CoreTasks/rename.html"><i>Rename</i></a><br>
<a href="CoreTasks/replace.html">Replace</a><br>
<a href="CoreTasks/resourcecount.html">ResourceCount</a><br>
<a href="CoreTasks/rmic.html">Rmic</a><br>
<a href="CoreTasks/sequential.html">Sequential</a><br>
<a href="CoreTasks/signjar.html">SignJar</a><br>
<a href="CoreTasks/sleep.html">Sleep</a><br>
<a href="CoreTasks/sql.html">Sql</a><br>
<a href="CoreTasks/subant.html">Subant</a><br>
<a href="CoreTasks/sync.html">Sync</a><br>
<a href="CoreTasks/tar.html">Tar</a><br>
<a href="CoreTasks/taskdef.html">Taskdef</a><br>
<a href="CoreTasks/tempfile.html">Tempfile</a><br>
<a href="CoreTasks/touch.html">Touch</a><br>
<a href="CoreTasks/tstamp.html">TStamp</a><br>
<a href="CoreTasks/typedef.html">Typedef</a><br>
<a href="CoreTasks/unzip.html">Unjar</a><br>
<a href="CoreTasks/unzip.html">Untar</a><br>
<a href="CoreTasks/unzip.html">Unwar</a><br>
<a href="CoreTasks/unzip.html">Unzip</a><br>
<a href="CoreTasks/uptodate.html">Uptodate</a><br>
<a href="CoreTasks/waitfor.html">Waitfor</a><br>
<a href="CoreTasks/whichresource.html">WhichResource</a><br>
<a href="CoreTasks/war.html">War</a><br>
<a href="CoreTasks/xmlproperty.html">XmlProperty</a><br>
<a href="CoreTasks/style.html">XSLT/<i>Style</i></a><br>
<a href="CoreTasks/zip.html">Zip</a><br>
</body>
</html>
