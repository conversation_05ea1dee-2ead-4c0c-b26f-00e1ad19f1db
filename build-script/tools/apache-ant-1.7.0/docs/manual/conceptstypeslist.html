<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>Apache Ant User Manual</title>
<base target="mainFrame">
</head>

<body>

<h2><a href="toc.html" target="navFrame">Table of Contents</a></h2>

<h3>Concepts</h3>
<a href="clonevm.html">ant.build.clonevm</a><br>
<a href="sysclasspath.html">build.sysclasspath</a><br>
<a href="javacprops.html">Ant properties controlling javac</a><br>
<a href="CoreTasks/common.html">Common Attributes</a><br>

<h3>Core Types</h3>
<a href="CoreTypes/description.html">Description Type</a><br>
<a href="dirtasks.html">Directory-based Tasks</a><br>
<a href="CoreTypes/dirset.html">DirSet</a><br>
<a href="CoreTypes/filelist.html">FileList</a><br>
<a href="CoreTypes/fileset.html">FileSet</a><br>
<a href="CoreTypes/mapper.html">File Mappers</a><br>
<a href="CoreTypes/filterchain.html">FilterChains and FilterReaders</a><br>
<a href="CoreTypes/filterset.html">FilterSet</a><br>
<a href="CoreTypes/patternset.html">PatternSet</a><br>
<a href="using.html#path">Path-like Structures</a><br>
<a href="CoreTypes/permissions.html">Permissions</a><br>
<a href="CoreTypes/propertyset.html">PropertySet</a><br>
<a href="CoreTypes/redirector.html">I/O Redirectors</a><br>
<a href="CoreTypes/regexp.html">Regexp</a><br>
<a href="CoreTypes/resources.html">Resources</a><br>
<a href="CoreTypes/resources.html#collection">Resource Collections</a><br>
<a href="CoreTypes/selectors.html">Selectors</a><br>
<a href="CoreTypes/tarfileset.html">TarFileSet</a><br>
<a href="CoreTypes/xmlcatalog.html">XMLCatalog</a><br>
<a href="CoreTypes/zipfileset.html">ZipFileSet</a><br>

<h3>Optional Types</h3>
<a href="OptionalTypes/classfileset.html">Class Fileset</a><br>
<a href="OptionalTypes/extension.html">Extension Package</a><br>
<a href="OptionalTypes/extensionset.html">Set of Extension Packages</a><br>

<h3>Namespace</h3>
<a href="CoreTypes/namespace.html">Namespace Support</a><br>
<h3>Antlib</h3>
<a href="CoreTypes/antlib.html">Antlib</a><br>
<a href="CoreTypes/antlib.html#antlibnamespace">Antlib namespace</a><br>
<a href="CoreTypes/antlib.html#currentnamespace">Current namespace</a><br>
                                            

<h3>Custom Components</h3>
<a href="CoreTypes/custom-programming.html">Custom Components</a><br>
<a href="CoreTypes/custom-programming.html#customconditions">Conditions</a><br>
<a href="CoreTypes/custom-programming.html#customselectors">Selectors</a><br>
<a href="CoreTypes/custom-programming.html#filterreaders">FilterReaders</a><br>

</body>
</html>
