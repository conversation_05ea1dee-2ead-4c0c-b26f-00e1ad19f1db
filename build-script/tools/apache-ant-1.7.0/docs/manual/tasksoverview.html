<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>
<head>
  <meta http-equiv="Content-Language" content="en-us">
  <link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>Overview of Ant Tasks</title>
  <base target="mainFrame">
</head>

<body>
<a name="top"></a>
<h2>Overview of Ant Tasks</h2>
<p>Given the large number of tasks available with Ant, it may be
difficult to get an overall view of what each task can do.  The following
tables provide a short description of each task and a link to the complete
documentation.</p>

<a href="#archive">Archive Tasks</a><br>
<a href="#audit">Audit/Coverage Tasks</a><br>
<a href="#compile">Compile Tasks</a><br>
<a href="#deploy">Deployment Tasks</a><br>
<a href="#doc">Documentation Tasks</a><br>
<a href="#ejb">EJB Tasks</a><br>
<a href="#exec">Execution Tasks</a><br>
<a href="#file">File Tasks</a><br>
<a href="#extensions">Java2 Extensions Tasks</a><br>
<a href="#log">Logging Tasks</a><br>
<a href="#mail">Mail Tasks</a><br>
<a href="#misc">Miscellaneous Tasks</a><br>
<a href="#net">.NET Tasks</a><br>
<a href="#preproc">Pre-process Tasks</a><br>
<a href="#prop">Property Tasks</a><br>
<a href="#remote">Remote Tasks</a><br>
<a href="#scm">SCM Tasks</a><br>
<a href="#testing">Testing Tasks</a><br>
<a href="#vaj">Visual Age for Java Tasks</a><br>


<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="archive">Archive Tasks</a>
</font>
</th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th nowrap>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/unpack.html">BUnzip2</a></td>
    <td><p>Expands a file packed using GZip or BZip2.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/pack.html">BZip2</a></td>
    <td><p>Packs a file using the GZip or BZip2 algorithm. This task
     does not do any dependency checking; the output file is always
     generated</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/cab.html">Cab</a></td>
    <td><p>Creates Microsoft CAB archive files. It is invoked
     similar to the <a href="CoreTasks/jar.html">Jar</a> or
     <a href="CoreTasks/zip.html">Zip</a> tasks. This task will work on
     Windows using the external <i>cabarc</i> tool (provided by Microsoft),
     which must be located in your executable path.</p></td>
   </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/ear.html">Ear</a></td>
    <td><p>An extension of the <a href="CoreTasks/jar.html">Jar</a> task with
     special treatment for files that should end up in an
     Enterprise Application archive.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/gunzip.html">GUnzip</a></td>
    <td><p>Expands a GZip file.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/gzip.html">GZip</a></td>
    <td><p>GZips a set of files.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/jar.html">Jar</a></td>
    <td><p>Jars a set of files.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/jlink.html">Jlink</a></td>
    <td><p><i>Deprecated.</i> Use the <code>zipfileset</code>
     and <code>zipgroupfileset</code> attributes of the
     <a href="CoreTasks/jar.html">Jar</a> or
     <a href="CoreTasks/zip.html">Zip</a> tasks instead.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/manifest.html">Manifest</a></td>
    <td><p>Creates a manifest file.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/rpm.html">Rpm</a></td>
    <td><p>Invokes the <i>rpm</i> executable to build a Linux installation
     file. This task currently only works on Linux or other Unix platforms
     with RPM support.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/signjar.html">SignJar</a></td>
    <td><p>Signs a jar or zip file with the <i>javasign</i>
     command-line tool.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/tar.html">Tar</a></td>
    <td><p>Creates a tar archive.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/unzip.html">Unjar</a></td>
    <td><p>Unzips a jarfile.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/untar.html">Untar</a></td>
    <td><p>Untars a tarfile.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/unzip.html">Unwar</a></td>
    <td><p>Unzips a warfile.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/unzip.html">Unzip</a></td>
    <td><p>Unzips a zipfile.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/war.html">War</a></td>
    <td><p>An extension of the <a href="CoreTasks/jar.html">Jar</a> task
     with special treatment for files that should end up in the
     <code>WEB-INF/lib</code>, <code>WEB-INF/classes</code>, or
     <code>WEB-INF</code> directories of the Web Application Archive.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/zip.html">Zip</a></td>
    <td><p>Creates a zipfile.</p></td>
  </tr>
</table>

<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="audit">Audit/Coverage Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/jdepend.html">JDepend</a></td>
    <td><p>Invokes the <a href="http://www.clarkware.com/software/JDepend.html">
     JDepend</a> parser. This parser &quot;traverses a set of Java source-file
     directories and generates design-quality metrics for each Java
     package&quot;.</p></td>
   </tr>

</table>

<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="compile">Compile Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/depend.html">Depend</a></td>
    <td><p>Determines which classfiles are out-of-date with respect to their
     source, removing the classfiles of any other classes that depend on the
     out-of-date classes, forcing the re-compile of the removed classfiles.
     Typically used in conjunction with the
     <a href="CoreTasks/javac.html">Javac</a> task.</p></td>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/javac.html">Javac</a></td>
    <td><p>Compiles the specified source file(s) within the running
     (Ant) VM, or in another VM if the <code>fork</code> attribute is
     specified.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/apt.html">Apt</a></td>
    <td><p>Runs the annotation processor tool (apt), and then optionally compiles
   the original code, and any generated source code.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/jspc.html">JspC</a></td>
    <td><p>Runs the JSP compiler. It can be used to precompile JSP pages
     for fast initial invocation of JSP pages, deployment on a server without
     the full JDK installed, or simply to syntax-check the pages without
     deploying them. The <a href="CoreTasks/javac.html">Javac</a> task
     can be used to compile the generated Java source.
     (For Weblogic JSP compiles,
     see the <a href="OptionalTasks/wljspc.html">Wljspc</a> task.)</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/netrexxc.html">NetRexxC</a></td>
    <td><p>Compiles a
     <a href="http://www2.hursley.ibm.com/netrexx" target="_top">NetRexx</a>
     source tree within the running (Ant) VM.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/rmic.html">Rmic</a></td>
    <td><p>Runs the <i>rmic</i> compiler on the specified file(s).</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/wljspc.html">Wljspc</a></td>
    <td><p>Compiles JSP pages using Weblogic's JSP compiler,
     <i>weblogic.jspc</i>. (For non-Weblogic JSP compiles, see the
     <a href="OptionalTasks/jspc.html">JspC</a> task.</p></td>
  </tr>

</table>

<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="deploy">Deployment Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/serverdeploy.html">ServerDeploy</a></td>
    <td><p>Task to run a &quot;hot&quot; deployment tool for vendor-specific
     J2EE server.
    </p></td>
  </tr>
</table>

<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="doc">Documentation Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/javadoc.html">Javadoc/<i>Javadoc2</i></a></td>
    <td><p>Generates code documentation using the <i>javadoc</i> tool.
     The Javadoc2 task is deprecated; use the Javadoc task instead.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/stylebook.html">Stylebook</a></td>
    <td><p>Executes the Apache Stylebook documentation generator.
     Unlike the command-line version of this tool, all three arguments
     are required to run the Stylebook task.</p></td>
  </tr>
</table>

<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="ejb">EJB Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/ejb.html">EJB Tasks</a></td>
    <td><p>(See the documentation describing the EJB tasks.)</p></td>
  </tr>
</table>

<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="exec">Execution Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/ant.html">Ant</a></td>
    <td><p>Runs Ant on a supplied buildfile, optionally
     passing properties (with possibly new values).
     This task can be used to build sub-projects.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/antcall.html">AntCall</a></td>
    <td><p>Runs another target within the same buildfile, optionally
     passing properties (with possibly new values).</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/apply.html">Apply/<i>ExecOn</i></a></td>
    <td><p>Executes a system command. When the <code>os</code> attribute is
     specified, the command is only executed when Ant is run on one
     of the specified operating systems.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/dependset.html">Dependset</a></td>
    <td><p>This task compares a set of source files with a set of target
     files.  If any of the source files is newer than any of
     the target files, all the target files are removed. </p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/exec.html">Exec</a></td>
    <td><p>Executes a system command. When the <code>os</code> attribute
     is specified, the command is only executed when Ant is run on one of
     the specified operating systems.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/java.html">Java</a></td>
    <td><p>Executes a Java class within the running (Ant) VM, or in
     another VM if the <code>fork</code> attribute is specified.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/parallel.html">Parallel</a></td>
    <td><p>A container task that can contain other Ant tasks.
     Each nested task specified within the <code>&lt;parallel&gt;</code>
     tag will be executed in its own thread.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/sequential.html">Sequential</a></td>
    <td><p>A container task that can contain other Ant tasks.
     The nested tasks are simply executed in sequence. Its primary use is
     to support the sequential execution of a subset of tasks within
     the <code>&lt;parallel&gt;</code> tag.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/sleep.html">Sleep</a></td>
    <td><p> A task for suspending execution for a specified period of time.
     Useful when a build or deployment process requires an interval between
     tasks.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/subant.html">Subant</a></td>
    <td><p> Calls a given target for all defined sub-builds. This is an extension of ant for bulk project execution.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/waitfor.html">Waitfor</a></td>
    <td><p>Blocks execution until a set of specified conditions become true.
     This task is intended to be used with the
     <a href="CoreTasks/parallel.html">Parallel</a> task to synchronize
     a set of processes.</p></td>
  </tr>
</table>

<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="file">File Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/attrib.html">Attrib</a></td>
    <td><p>Changes the permissions and/or attributes of a file or all
    files inside the specified directories. Currently, it has effect
    only under Windows.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/checksum.html">Checksum</a></td>
    <td><p>Generates a checksum for a file or set of files. This task can
     also be used to perform checksum verifications.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/chgrp.html">Chgrp</a></td>
    <td><p>Changes the group ownership of a file or all files inside
    the specified directories. Currently, it has effect only under
    Unix.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/chmod.html">Chmod</a></td>
    <td><p>Changes the permissions of a file or all files inside the
     specified directories. Currently, it has effect only under Unix.
     The permissions are also UNIX style, like the arguments for the
    <i>chmod</i> command.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/chown.html">Chown</a></td>
    <td><p>Changes the owner of a file or all files inside the
    specified directories. Currently, it has effect only under
    Unix.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/concat.html">Concat</a></td>
    <td><p>Concatenates multiple files into a single one or to Ant's
     logging system.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/copy.html">Copy</a></td>
    <td><p>Copies a file or Fileset to a new file or directory.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/copydir.html"><i>Copydir</i></a></td>
    <td><p><i>Deprecated.</i>  Use the
     <a href="CoreTasks/copy.html">Copy</a> task instead.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/copyfile.html"><i>Copyfile</i></a></td>
    <td><p><i>Deprecated.</i>  Use the
     <a href="CoreTasks/copy.html">Copy</a> task instead.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/delete.html">Delete</a></td>
    <td><p>Deletes either a single file, all files and sub-directories
     in a specified directory, or a set of files specified by one or more
     <a href="CoreTypes/fileset.html">FileSet</a>s.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/deltree.html"><i>Deltree</i></a></td>
    <td><p><i>Deprecated.</i>  Use the
     <a href="CoreTasks/delete.html">Delete</a> task instead.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/filter.html">Filter</a></td>
    <td><p>Sets a token filter for this project, or reads multiple token
     filters from a specified file and sets these as filters. Token filters
     are used by all tasks that perform file-copying operations.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/fixcrlf.html">FixCRLF</a></td>
    <td><p>Modifies a file to add or remove tabs, carriage returns, linefeeds,
     and EOF characters.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/get.html">Get</a></td>
    <td><p>Gets a file from a URL.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/mkdir.html">Mkdir</a></td>
    <td><p>Creates a directory. Non-existent parent directories are created,
     when necessary.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/move.html">Move</a></td>
    <td><p>Moves a file to a new file or directory, or a set(s) of file(s) to
     a new directory.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/patch.html">Patch</a></td>
    <td><p>Applies a &quot;diff&quot; file to originals.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/rename.html"><i>Rename</i></a></td>
    <td><p><i>Deprecated.</i>  Use the <a href="CoreTasks/move.html">Move</a>
    task instead.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/renameextensions.html">
     <i>RenameExtensions</i></a></td>
    <td><p><i>Deprecated</i>. Use the <a href="CoreTasks/move.html">Move</a>
     task with a <a href="CoreTypes/mapper.html#glob-mapper">glob mapper</a>
     instead.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/replace.html">Replace</a></td>
    <td><p>Replace is a directory-based task for replacing the occurrence
     of a given string with another string in selected file.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/replaceregexp.html">
     ReplaceRegExp</a></td>
    <td><p>Directory-based task for replacing the occurrence of a given
     regular expression with a substitution pattern in a file or set of
     files.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/sync.html">Sync</a></td>
    <td><p>Synchronize two directory trees.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/tempfile.html">Tempfile</a></td>
    <td><p>Generates a name for a new temporary file and sets the specified
     property to that name.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/touch.html">Touch</a></td>
    <td><p>Changes the modification time of a file and possibly creates it at
     the same time.</p></td>
  </tr>
</table>

<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="extensions">Java2 Extensions Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap>
    <a href="OptionalTasks/jarlib-available.html">Jarlib-available</a></td>
    <td><p>Check whether an extension is present in a FileSet or an
      ExtensionSet. If the extension is present, the specified property is
     set.</p>
    </td>
  </tr>

  <tr valign="top">
    <td nowrap>
    <a href="OptionalTasks/jarlib-display.html">Jarlib-display</a></td>
    <td><p>Display the &quot;Optional Package&quot; and
      &quot;Package Specification&quot; information contained within the
      specified jars.</p>
    </td>
  </tr>

  <tr valign="top">
    <td nowrap>
    <a href="OptionalTasks/jarlib-manifest.html">Jarlib-manifest</a></td>
    <td><p>Task to generate a manifest that declares all the dependencies
      in manifest. The dependencies are determined by looking in the
      specified path and searching for Extension/&quot;Optional Package&quot;
      specifications in the manifests of the jars.</p>
    </td>
  </tr>

  <tr valign="top">
    <td nowrap>
    <a href="OptionalTasks/jarlib-resolve.html">Jarlib-resolve</a></td>
    <td><p>Try to locate a jar to satisfy an extension, and place the
      location of the jar into the specified property.</p>
    </td>
  </tr>
</table>

<p></p>
<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="log">Logging Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/recorder.html">Record</a></td>
    <td><p>Runs a listener that records the logging output of the
     build-process events to a file. Several recorders can exist
     at the same time. Each recorder is associated with a file.</p></td>
  </tr>
</table>

<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="mail">Mail Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/mail.html">Mail</a></td>
    <td><p>A task to send SMTP email.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/mimemail.html">MimeMail</a></td>
    <td><p><i>Deprecated</i>. Use the <a href="CoreTasks/mail.html">Mail</a>
     task instead.</p></td>
  </tr>

</table>

<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="misc">Miscellaneous Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/defaultexcludes.html">Defaultexcludes</a></td>
    <td><p>Modify the list of default exclude patterns from within
    your build file.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/echo.html">Echo</a></td>
    <td><p>Echoes text to <code>System.out</code> or to a file.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/fail.html">Fail</a></td>
    <td><p>Exits the current build by throwing a BuildException,
     optionally printing additional information.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/genkey.html">GenKey</a></td>
    <td><p>Generates a key in keystore.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/input.html">Input</a></td>
    <td><p>Allows Employee interaction during the build process by displaying a
     message and reading a line of input from the console.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/script.html">Script</a></td>
    <td><p>Executes a script in a
     <a href="http://jakarta.apache.org/bsf/"
      target="_top">Apache BSF</a>-supported language.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/sound.html">Sound</a></td>
    <td><p>Plays a sound file at the end of the build, according to whether
     the build failed or succeeded.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/splash.html">Splash</a></td>
    <td><p>Displays a splash screen.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/sql.html">Sql</a></td>
    <td><p>Executes a series of SQL statements via JDBC to a database.
     Statements can either be read in from a text file using the
     <code>src</code> attribute, or from between the enclosing SQL
     tags.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/taskdef.html">Taskdef</a></td>
    <td><p>Adds a task definition to the current project, such that this
     new task can be used in the current project.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/tstamp.html">TStamp</a></td>
    <td><p>Sets the <code>DSTAMP</code>, <code>TSTAMP</code>, and
     <code>TODAY</code> properties in the current project, based on
     the current date and time.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/typedef.html">Typedef</a></td>
    <td><p>Adds a data-type definition to the current project, such that this
     new type can be used in the current project.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/xmlvalidate.html">XmlValidate</a></td>
    <td><p>Checks that XML files are valid (or only well-formed). This task
     uses the XML parser that is currently used by Ant by default, but any SAX1/2
     parser can be specified, if needed.</p></td>
  </tr>
</table>

<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="net">.NET Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/dotnet.html">.NET Tasks</a></td>
    <td><p>(See the documentation describing the .NET tasks.)</p></td>
  </tr>
</table>

<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="preproc">Pre-process Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/antlr.html">ANTLR</a></td>
    <td><p>Invokes the <a HREF="http://www.antlr.org/" target="_top">ANTLR</a>
     Translator generator on a grammar file.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/antstructure.html">AntStructure</a></td>
    <td><p>Generates a DTD for Ant buildfiles that contains information
     about all tasks currently known to Ant.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/import.html">Import</a></td>
    <td><p>Import another build file and potentially override targets
    in it with targets of your own.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/javacc.html">JavaCC</a></td>
    <td><p>Invokes the
     <a HREF="http://javacc.dev.java.net/" target="_top">
     JavaCC</a> compiler-compiler on a grammar file.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/javah.html">Javah</a></td>
    <td><p>Generates JNI headers from a Java class.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/jjdoc.html">JJDoc</a></td>
    <td><p>Invokes the <a href="http://javacc.dev.java.net/">
     JJDoc</a> documentation generator for the JavaCC compiler-compiler.
     JJDoc takes a JavaCC parser specification and produces documentation
     for the BNF grammar.  It can operate in three modes, determined by
     command line options. This task only invokes JJDoc if the grammar file
     is newer than the generated BNF grammar documentation.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/jjtree.html">JJTree</a></td>
    <td><p>Invokes the <a href="http://javacc.dev.java.net/">
     JJTree</a> preprocessor for the JavaCC compiler-compiler. It inserts
     parse-tree building actions at various places in the JavaCC source that
     it generates. The output of JJTree is run through JavaCC to create the
     parser. This task only invokes JJTree if the grammar file is newer than
     the generated JavaCC file.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/macrodef.html">Macrodef</a></td>
    <td><p>Define a new task as a macro built-up upon other tasks.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/native2ascii.html">
     Native2Ascii</a></td>
    <td><p>Converts files from native encodings to ASCII with escaped Unicode.
      A common usage is to convert source files maintained in a native
      operating system encoding to ASCII, prior to compilation.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/presetdef.html">Presetdef</a></td>
    <td><p>Define a new task by instrumenting an existing task with
    default values for attributes or child elements.</p>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/translate.html">Translate</a></td>
    <td><p>Identifies keys in files, delimited by special tokens, and
     translates them with values read from resource bundles.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/style.html">XSLT</a></td>
    <td><p>Processes a set of documents via XSLT.</p></td>
  </tr>

</table>

<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="prop">Property Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/available.html">Available</a></td>
    <td><p>Sets a property if a specified file, directory, class in the
     classpath, or JVM system resource is available at runtime.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/basename.html">Basename</a></td>
    <td><p>Sets a property to the last element of a specified path.
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/buildnumber.html">BuildNumber</a></td>
    <td><p>Task that can be used to track build numbers.
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/condition.html">Condition</a></td>
    <td><p>Sets a property if a certain condition holds true; this is a
     generalization of <a href="CoreTasks/available.html">Available</a> and
     <a href="CoreTasks/uptodate.html">Uptodate</a>.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/dirname.html">Dirname</a></td>
    <td><p>Sets a property to the value of the specified file up to,
     but not including, the last path element.
  </tr>

  <tr valign="top">
    <td nowrap>
    <a href="OptionalTasks/echoproperties.html">Echoproperties</a><br>

    </td>
    <td><p>Lists the current properties.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/loadfile.html">LoadFile</a></td>
    <td><p>Loads a file into a property.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/loadproperties.html">LoadProperties</a></td>
    <td><p>Load a file's contents as Ant properties. This task is
     equivalent to using <code>&lt;property file=&quot;...&quot;/&gt;</code>
     except that it supports nested <code>&lt;filterchain&gt;</code> elements,
     and it cannot be specified outside a target.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/makeurl.html">MakeURL</a></td>
    <td><p>Creates a URL (list) from a file/fileset or path</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/pathconvert.html">PathConvert</a></td>
    <td><p>Converts a nested path, path reference, filelist reference, or
     fileset reference to the form usable on a specified platform and/or
     to a list of items separated by the specified separator and stores
     the result in the specified property.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/property.html">Property</a></td>
    <td><p>Sets a property (by name and value), or set of properties
     (from a file or resource) in the project.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/propertyfile.html">
     PropertyFile</a></td>
    <td><p>Creates or modifies property files. Useful when wanting to make
     unattended modifications to configuration files for application
     servers and applications. Typically used for things such as
     automatically generating a build number and saving it to a build
     properties file, or doing date manipulation.<p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/uptodate.html">Uptodate</a></td>
    <td><p>Sets a property if a given target file is newer than a set of
     source files.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/whichresource.html">Whichresource</a></td>
    <td><p>Find a class or resource.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/xmlproperty.html">XmlProperty</a></td>
    <td><p>Loads property values from a well-formed XML file.</p></td>
  </tr>
</table>

<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="remote">Remote Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/ftp.html">FTP</a></td>
    <td><p>Implements a basic FTP client that can send, receive,
     list, and delete files, and create directories.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/rexec.html">Rexec</a></td>
    <td><p>Task to automate a remote rexec session.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/scp.html">Scp</a></td>
    <td><p>Copy files to or from a remote server using SSH.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/setproxy.html">setproxy</a></td>
    <td><p>Sets Java's web proxy properties, so that tasks and code run
 in the same JVM can have through-the-firewall access to remote web sites.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/sshexec.html">Sshexec</a></td>
    <td><p>Execute a command on a remote server using SSH.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/telnet.html">Telnet</a></td>
    <td><p>Task to automate a remote <i>telnet</i> session. This task uses
     nested <code>&lt;read&gt;</code> and <code>&lt;write&gt;</code> tags
     to indicate strings to wait for and specify text to send.</p></td>
  </tr>

</table>

<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="scm">SCM Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/cvs.html">Cvs</a></td>
    <td><p>Handles packages/modules retrieved from a
     <a href="http://www.nongnu.org/cvs/" target="_top">CVS</a>
     repository.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/changelog.html">CvsChangeLog</a></td>
    <td><p>Generates an XML report of the changes recorded in a
     <a href="http://www.nongnu.org/cvs/" target="_top">CVS</a>
     repository.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/cvspass.html">CVSPass</a></td>
    <td><p>Adds entries to a .cvspass file. Adding entries to this file
     has the same affect as a <i>cvs login</i> command.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="CoreTasks/cvstagdiff.html">CvsTagDiff</a></td>
    <td><p>Generates an XML-formatted report file of the changes between
    two tags or dates recorded in a <a href="http://www.nongnu.org/cvs/"
    target="_top">CVS</a> repository.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/clearcase.html">ClearCase</a></td>
    <td><p>Tasks to perform the ClearCase cleartool <i>checkin</i>, <i>checkout</i>,
     <i>uncheckout</i>, <i>update</i>, <i>lock</i>, <i>unlock</i>, <i>mklbtype</i>, <i>rmtype</i>, <i>mklabel</i>, <i>mkattr</i>, <i>mkdir</i>, <i>mkelem</i>, and <i>mkbl</i> commands.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/ccm.html">Continuus/Synergy</a></td>
    <td><p>Tasks to perform the Continuus <i>ccmcheckin</i>,
     <i>ccmcheckout</i>, <i>ccmcheckintask</i>, <i>ccmreconfigure</i>, and
     <i>ccmcreateTask</i> commands.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/vss.html">
     Microsoft Visual SourceSafe</a></td>
    <td><p>Tasks to perform the Visual SourceSafe <i>vssget</i>,
     <i>vsslabel</i>, <i>vsshistory</i>, <i>vsscheckin</i>,
     <i>vsscheckout</i>, <i>vssadd</i>, <i>vsscp</i>, and <i>vsscreate</i>
     commands.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/perforce.html">Perforce</a></td>
    <td><p>Tasks to perform the Perforce <i>p4sync</i>, <i>p4change</i>,
     <i>p4edit</i>, <i>p4submit</i>, <i>p4have</i>, <i>p4label</i>,
     <i>p4counter</i>, <i>p4reopen</i>, <i>p4revert</i>, and <i>p4add</i>
     commands.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/pvcstask.html">Pvcs</a></td>
    <td><p>Allows the Employee extract the latest edition of the source code
     from a PVCS repository.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/sos.html">SourceOffSite</a></td>
    <td><p>Tasks to perform the SourceOffSite <i>sosget</i>, <i>soslabel</i>,
     <i>soscheckin</i>, and <i>soscheckout</i> commands.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/starteam.html">StarTeam</a></td>
    <td><p>Tasks to perform the StarTeam <i>stcheckout</i>, <i>stcheckin</i>,
     <i>stlabel</i>, and <i>stlist</i> commands. The
     <a href="OptionalTasks/starteam.html#starteam">Starteam</a> task
     is deprecated; use
     <a href="OptionalTasks/starteam.html#stcheckout">STCheckout</a>
     instead.</p></td>
  </tr>
</table>

<p></p>
<table width="100%" border="0" cellpadding="4" cellspacing="0">
<th align="left">
<font size="+0" face="arial,helvetica,sanserif">
<a name="testing">Testing Tasks</a>
</font></th>
<font size="-1" face="arial,helvetica,sanserif">
<th align="right"><a href="#top">[Back to top]</a></th>
</font>
</table>
<table width="100%" border="1" cellpadding="4" cellspacing="0">
  <tr valign="top">
    <th nowrap>Task Name</th>
    <th>Description</th>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/junit.html">Junit</a></td>
    <td><p>Runs tests from the <a href="http://www.junit.org">Junit</a>
     testing framework. This task has been tested with JUnit 3.0 up to
     JUnit 3.7; it won't work with versions prior to JUnit 3.0.</p></td>
  </tr>

  <tr valign="top">
    <td nowrap><a href="OptionalTasks/junitreport.html">JunitReport</a></td>
    <td><p>Merges the individual XML files generated by the
     <a href="OptionalTasks/junit.html">Junit</a> task and applies a
     stylesheet on the resulting merged document to provide a browsable
     report of the testcases results.</p></td>
  </tr>

</table>




</body>
</html>
