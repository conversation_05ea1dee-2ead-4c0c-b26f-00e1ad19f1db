<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>ant.build.clonevm</title>
</head>

<body>

<h2><a name="clonevm">ant.build.clonevm</a></h2>

<p><em>Since Ant 1.7</em></p>

<p>The value of the ant.build.clonevm system property controls how Ant
instruments forked Java Virtual Machines.  The <a
href="CoreTasks/java.html">java</a> and <a
href="OptionalTasks/junit.html">junit</a> tasks support clonevm
attributes to control the VMs on a task-by-task basis while the system
property applies to all forked Java VMs.</p>

<p>If the value of the property is true, then all system properties of
the forked Java Virtual Machine will be the same as those of the Java
VM running Ant.  In addition, if you set ant.build.clonevm to true and <a
href="sysclasspath.html">build.sysclasspath</a> has not been set, the
bootclasspath of forked Java VMs gets constructed as if
build.sysclasspath had the value "last".</p>

<p>Note that this has to be a system property, so it cannot be
specified on the Ant command line.  Use the ANT_OPTS environment
variable instead.</p>


</body>
</html>

