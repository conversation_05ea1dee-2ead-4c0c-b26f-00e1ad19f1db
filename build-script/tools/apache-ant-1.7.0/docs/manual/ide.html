<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>IDE Integration</title>
<base target="mainFrame">
</head>

<body>

<h2><a href="toc.html" target="navFrame">Table of Contents</a></h2>

<h3>IDE Integration</h3>
<p>
All the modern Java IDEs support Ant almost out of the box.
</p>

<ul>
  <li>
    <a href="http://antrunner.sourceforge.net/">
      AntRunner For JBuilder (unbundled)
    </a>
  </li>
  <li>
    <a href="Integration/jext-plugin.html">
      AntWork Plugin for the Jext Java Text Editor (unbundled)
    </a>
  </li>
  <li>
    <a href="http://jdee.sunsite.dk/">
      JDEE (Java Development Environment for Emacs)
    </a> has built-in text ANT integration: selection of target through text
    field, execution, hyperlink to compilation errors. Installation: built-in
    JDEE 2.2.8 or later. Configuration: through customize menu
    "Jde Build Function"
  </li>
  <li>
    <a href="http://www.intellij.com/idea/">
      IDEA
    </a> has built-in GUI ANT integration: GUI selection of targets, execution,
    hyperlink to compilation errors
  </li>
  <li>
    <a href="http://ant.netbeans.org/">
      NetBeans
    </a>
    NetBeans IDE uses Ant as the basis for its project system starting with the 4.0 release.
  </li>
  <li>
    <a href="http://jedit.org/">
      jEdit
    </a>
    jEdit is an open source java IDE with some great plugins for Java dev, a
    good XML editor and the Antfarm plugin to execute targets in a build
    file.
  </li>
  <li>
    <a href="http://eclipse.org/">
      Eclipse
    </a>
    Eclipse is IBM's counterpoint to NetBeans; an open source IDE with
    Java and Ant support.
  </li>
  <li>
    <a href="Integration/VAJAntTool.html">
      VisualAge for Java</a>
  </li>
  <li>
    <a href="http://www7b.software.ibm.com/wsdd/library/techarticles/0203_searle/searle1.html">
      WebSphere Studio Application Developer
    </a>
  </li>
  <li>
    <a href="http://www.borland.com/jbuilder/pdf/jb9_feamatrix.pdf">
      JBuilder 9 Personal
    </a>
    JBuilder supports Ant with the following features. Add Ant nodes to
    projects and execute Ant targets from within JBuilder. Add custom Ant-based
    build tasks with custom Ant libraries to run Ant from within JBuilder.
    Rapid navigation from Ant build error messages to source files.
    Customize build menu and toolbar with custom build targets.
  </li>
</ul>



</body>
</html>
