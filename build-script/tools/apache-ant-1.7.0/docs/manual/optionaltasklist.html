<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>Apache Ant User Manual</title>
<base target="mainFrame">
</head>

<body>

<h2><a href="toc.html" target="navFrame">Table of Contents</a></h2>

<a href="coretasklist.html" target="navFrame">Core Tasks</a><br>
<a href="tasksoverview.html" target="mainFrame">Overview of Ant Tasks</a><br>
<a href="conceptstypeslist.html" target="navFrame">Concepts and Types</a><br>

<h3>Optional Tasks</h3>
<a href="OptionalTasks/dotnet.html"><i>.NET Tasks</i></a><br>
<a href="OptionalTasks/antlr.html">ANTLR</a><br>
<a href="OptionalTasks/attrib.html">Attrib</a><br>
<a href="OptionalTasks/cab.html">Cab</a><br>
<a href="OptionalTasks/chgrp.html">Chgrp</a><br>
<a href="OptionalTasks/chown.html">Chown</a><br>
<a href="OptionalTasks/clearcase.html">Clearcase Tasks</a><br>
<a href="OptionalTasks/ccm.html">Continuus/Synergy Tasks</a><br>
<a href="OptionalTasks/depend.html">Depend</a><br>
<a href="OptionalTasks/ejb.html">EJB Tasks</a><br>
<a href="OptionalTasks/echoproperties.html">Echoproperties</a><br>
<a href="OptionalTasks/ftp.html">FTP</a><br>
<a href="OptionalTasks/image.html">Image</a><br>
<a href="OptionalTasks/jarlib-available.html">Jarlib-available</a><br>
<a href="OptionalTasks/jarlib-display.html">Jarlib-display</a><br>
<a href="OptionalTasks/jarlib-manifest.html">Jarlib-manifest</a><br>
<a href="OptionalTasks/jarlib-resolve.html">Jarlib-resolve</a><br>
<a href="OptionalTasks/javacc.html">JavaCC</a><br>
<a href="OptionalTasks/javah.html">Javah</a><br>
<a href="OptionalTasks/jspc.html">JspC</a><br>
<a href="OptionalTasks/jdepend.html">JDepend</a><br>
<a href="OptionalTasks/jjdoc.html">JJDoc</a><br>
<a href="OptionalTasks/jjtree.html">JJTree</a><br>
<a href="OptionalTasks/jlink.html"><i>Jlink</i></a><br>
<a href="OptionalTasks/junit.html">JUnit</a><br>
<a href="OptionalTasks/junitreport.html">JUnitReport</a><br>
<a href="OptionalTasks/mimemail.html"><i>MimeMail</i></a><br>
<a href="OptionalTasks/native2ascii.html">Native2Ascii</a><br>
<a href="OptionalTasks/netrexxc.html">NetRexxC</a><br>
<a href="OptionalTasks/perforce.html">Perforce Tasks</a><br>
<a href="OptionalTasks/propertyfile.html">PropertyFile</a><br>
<a href="OptionalTasks/pvcstask.html">Pvcs</a><br>
<a href="OptionalTasks/renameextensions.html"><i>RenameExtensions</i></a><br>
<a href="OptionalTasks/replaceregexp.html">ReplaceRegExp</a><br>
<a href="OptionalTasks/rexec.html">RExec</a><br>
<a href="OptionalTasks/rpm.html">Rpm</a><br>
<a href="OptionalTasks/schemavalidate.html">SchemaValidate</a><br>
<a href="OptionalTasks/scp.html">Scp</a><br>
<a href="OptionalTasks/script.html">Script</a><br>
<a href="OptionalTasks/scriptdef.html">Scriptdef</a><br>
<a href="OptionalTasks/serverdeploy.html">ServerDeploy</a><br>
<a href="OptionalTasks/setproxy.html">Setproxy</a><br>
<a href="OptionalTasks/sound.html">Sound</a><br>
<a href="OptionalTasks/sos.html">SourceOffSite</a><br>
<a href="OptionalTasks/splash.html">Splash</a><br>
<a href="OptionalTasks/sshexec.html">Sshexec</a><br>
<a href="OptionalTasks/starteam.html">Starteam Tasks</a><br>
<a href="OptionalTasks/stylebook.html"><i>Stylebook</i></a><br>
<a href="OptionalTasks/symlink.html">Symlink</a><br>
<a href="OptionalTasks/telnet.html">Telnet</a><br>
<a href="OptionalTasks/translate.html">Translate</a><br>
<a href="OptionalTasks/vss.html#tasks">Microsoft Visual SourceSafe Tasks</a><br>
<a href="OptionalTasks/wljspc.html">Weblogic JSP Compiler</a><br>
<a href="OptionalTasks/xmlvalidate.html">XmlValidate</a><br>

</body>
</html>

