<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<html>

<head>
<meta http-equiv="Content-Language" content="en-us">
<link rel="stylesheet" type="text/css" href="stylesheets/style.css">
<title>Apache Ant User Manual</title>
<base target="mainFrame">
</head>

<body>

<h2><a href="toc.html" target="navFrame">Table of Contents</a></h2>

<h3>Developing with <PERSON>t</h3>

<a href="../ant_in_anger.html">Ant in Anger</a><br>
<a href="../ant_task_guidelines.html">Ant Task Guidelines</a><br>
<a href="develop.html#writingowntask">Writing Your Own Task</a><br>
<a href="base_task_classes.html">Tasks Designed for Extension</a><br>
<a href="develop.html#buildevents">Build Events</a><br>
<a href="develop.html#integration">Source-code Integration</a><br>
<a href="inputhandler.html">InputHandler</a><br>
<a href="antexternal.html">Using Ant Tasks Outside of Ant</a><br>
<br>Tutorials<br>
<a href="tutorial-HelloWorldWithAnt.html">Hello World with Ant</a><br>
<a href="tutorial-writing-tasks.html">Writing Tasks</a><br>
<a href="tutorial-tasks-filesets-properties.html">Tasks using Properties, Filesets &amp; Paths</a><br>

</body>
</html>
