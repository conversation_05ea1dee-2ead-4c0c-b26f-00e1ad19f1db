

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
        <html lang="en">
    <!-- GENERATED FILE, DO NOT EDIT, EDIT THE XML FILE IN xdocs INSTEAD! -->
    <head>
      <META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
        <title>Apache Ant - Having Problems?</title>
        <link type="text/css" href="./page.css" rel="stylesheet">
          <meta name="author" content="<PERSON>">
  <meta name="email" content="">
      </head>

    <body>
      <p class="navpath">
        <script src="./breadcrumbs.js" language="JavaScript" type="text/javascript"></script>
      </p>

      <div class="logobar">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td align="left"><img border="0" alt="Apache Ant site" src="./images/group-logo.gif"></td>
            <td align="center" width="100%"><img alt="Apache Ant logo" border="0" src="./images/project-logo.gif"></td>
            <td align="right">
              <form target="_blank" onsubmit="q.value = query.value + ' site:ant.apache.org'" action="http://www.google.com/search" method="get">
                <table summary="search" border="0" cellspacing="0" cellpadding="0" bgcolor="#4C6C8F">
                  <tr>
                    <td colspan="3"><img height="10" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td nowrap="nowrap" class="searchcaption">
                      <input name="q" type="hidden">
                      <input size="15" id="query" type="text">
                      <img height="1" width="5" alt="" src="./images/spacer.gif">
                      <input name="Search" value="Search" type="submit">
                      <br>
                      the Apache Ant site
                    </td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-left.gif"></td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-right.gif"></td>
                  </tr>
                </table>
              </form>
            </td>
          </tr>
        </table>
      </div>

                  <div class="tab">
              <table summary="tab bar" border="0" cellpadding="0" cellspacing="0">
                <tr>
                                  <td width="8"><img alt="" height="5" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="selected tab" style="height: 1.5em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                        <td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-left.gif"></td><td valign="middle" bgcolor="#4C6C8F"><font color="#ffffff" size="2" face="Arial, Helvetica, Sans-serif"><b>Home</b></font></td><td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                                    <td width="5"><img alt="" height="8" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="non selected tab" style="height: 1.4em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                          <td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-left.gif"></td><td valign="middle" bgcolor="#B2C4E0"><a href="./projects/index.html"><font size="2" face="Arial, Helvetica, Sans-serif">Projects</font></a></td><td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                            </tr>
              </table>
            </div>

      <div class="bluebar"></div>
                    
  <div class="menucontainer">

    <div class="menu">
      <ul>
              <li class="menuheader">Apache Ant
          <ul>
                            <li>
                                    <a href="./index.html">Welcome</a>
                                </li>
                            <li>
                                    <a href="./license.html">License</a>
                                </li>
                            <li>
                                    <a href="./antnews.html">News</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Documentation
          <ul>
                            <li>
                                    <a href="./manual/index.html">Manual</a>
                                </li>
                            <li>
                                    <a href="./projects.html">Related Projects</a>
                                </li>
                            <li>
                                    <a href="./external.html">External Tools and Tasks</a>
                                </li>
                            <li>
                                    <a href="./resources.html">Resources</a>
                                </li>
                            <li>
                                    <a href="./faq.html">Frequently Asked Questions</a>
                                </li>
                            <li>
                                    <a href="http://wiki.apache.org/ant/FrontPage">Wiki</a>
                                </li>
                            <li>
                              <span class="sel">Having Problems?</span>
                              </li>
                      </ul>
        </li>
              <li class="menuheader">Download
          <ul>
                            <li>
                                    <a href="http://ant.apache.org/bindownload.cgi">Binary Distributions</a>
                                </li>
                            <li>
                                    <a href="http://ant.apache.org/srcdownload.cgi">Source Distributions</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Contributing
          <ul>
                            <li>
                                    <a href="./mail.html">Mailing Lists</a>
                                </li>
                            <li>
                                    <a href="./svn.html">Subversion Repositories</a>
                                </li>
                            <li>
                                    <a href="./nightlies.html">Nightly Builds</a>
                                </li>
                            <li>
                                    <a href="./bugs.html">Bug Database</a>
                                </li>
                            <li>
                                    <a href="http://www.apache.org/foundation/contributing.html">Donations</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Project Management
          <ul>
                            <li>
                                    <a href="./contributors.html">Contributors</a>
                                </li>
                            <li>
                                    <a href="./mission.html">Apache Ant Mission</a>
                                </li>
                            <li>
                                    <a href="./bylaws.html">Project Bylaws</a>
                                </li>
                            <li>
                                    <a href="./legal.html">Legal</a>
                                </li>
                      </ul>
        </li>
            </ul>
    </div>
    <img style="float: left" height="10" width="10" border="0" alt="" src="./images/menu-left.gif">
    <img style="float: right" height="10" width="10" border="0" alt="" src="./images/menu-right.gif">
  </div>
      <div class="lightbluebar">&nbsp;</div>
  <div class="main">
  <div class="content">
    <h1 class="title">Having Problems?</h1>
            <h3 class="section">
      <a name="Having Problems?"></a>
      Having Problems?
    </h3>
                        <p>
           This page details some steps you can take to try and resolve
           any problems you may be having with Ant. If you find you can't
           resolve the problem, then this page will help you collect some of
           the relevant information to provide in a bug report. This information
           will help the Ant developers understand and resolve the problem.
           Of course, not all the steps here will make sense for every problem
           you may encounter - these are just some suggestions to point
           you in the right direction.
        </p>
                                      <h4 class="subsection">
        <a name="Ensure that you are actually running the version of Ant that you think you do"></a>
        Ensure that you are actually running the version of Ant that you think you do
      </h4>
                        <p>Many tools include a version of Ant and some Operating
        Systems even install it by default now, so you may have a
        version of Ant installed that you haven't been aware of.</p>
                                <p>One of the first things to do is to run
          <br /><br />
          <font face="verdana" size="-1">ant -version</font>
          <br /><br />
          and
          <br /><br />
          <font face="verdana" size="-1">ant -diagnostics</font>
          <br /><br />
          to be sure.  Also, we highly recommend that you run Ant with
          an empty CLASSPATH.  If any other version of Ant can be
          loaded from the CLASSPATH, many types of errors may happen
          because of incompatible classes being loaded.</p>
                                <p>See <a href="faq.html">the FAQ</a> for <a href="faq.html#NoClassDefFoundError">some</a> <a href="faq.html#InstantiationException">examples</a>, but many
        other problems are a result of an old version of Ant on your
        system as well.</p>
                                                          <h4 class="subsection">
        <a name="Read the Manual"></a>
        Read the Manual
      </h4>
                        <p>
            The first step to take when you have a problem with Ant is to read
            the <a href="manual/index.html">manual</a> entry for the task or
            concept that is giving you trouble. In particular, check the
            meaning of a task's attributes and nested elements. Perhaps an
            attribute is available that would provide the behavior you require.
            If you have problems with the manual itself, you can submit a
            documentation bug report (see below) to help us improve the Ant
            documentation.
         </p>
                                                          <h4 class="subsection">
        <a name="Examine Debug Output"></a>
        Examine Debug Output
      </h4>
                        <p>
            If you're still having a problem, the next step is to try and
            gather additional information about what Ant is doing.
            Try running Ant with the <code>verbose</code> flag:
            <br /><br />
            <font face="verdana" size="-1">ant -verbose</font>
            <br /><br />
            or
            <br /><br />
            <font face="verdana" size="-1">ant -v</font>
            <br /><br />

            This will produce output that starts like the following:</p>
                                      <table class="ForrestTable" cellspacing="1" cellpadding="4">
              <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          
Ant version 1.4.1 compiled on October 11 2001<br />
Buildfile: build.xml<br />
Detected Java version: 1.3 in: D:\usr\local\java\jdk13\jre<br />
Detected OS: Windows NT<br />
parsing buildfile D:\ant\build.xml
with URI = file:D:/ant/build.xml<br />
Project base dir set to: D:\ant<br />
  [property] Loading Environment env.<br />
  [property] Loading D:\ant\conf.properties<br />
Build sequence for target 'debug' is [debug]<br />
Complete build sequence is [debug, gensrc, compile, jar, test]<br />
. . .<br />

      </td>
      </tr>
          </table>
                                <p>
              You should be able to see from the trace more about what Ant
              is doing and why it's taking a particular course of action.
              If you need even more information, you can use the
              <code>-debug</code> flag rather than
              <code>-verbose</code>.
              This will generally produce so much
              output that you may want to save the output to a file and
              analyze it in an editor. You can save the output using the
              <code>-logfile &lt;filename&gt;</code> flag, or
              using redirection.
           </p>
                                <p>
              Once you have all this debug information, how can you use it
              to solve your problem?  That will depend on the task in question
              and the nature of your problem. Each task logs different aspects
              of its operation, but it should give you an idea of what is going
              on. For example, the <code>&lt;javac&gt;</code> task logs the
              reasons why it
              chooses to compile particular class files and not others, along
              with which compiler it is using and the arguments it will pass
              to that compiler. The following partial trace shows why
              <code>&lt;javac&gt;</code> is adding one class file but
              skipping another.
              This is followed by which compiler it will be using, the
              arguments that will get passed to the compiler,
              and a list of all the class files to be compiled.
           </p>
                                      <table class="ForrestTable" cellspacing="1" cellpadding="4">
              <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          
[javac] Test.java omitted as D:\classes\Test.class is up to date.<br />
[javac] Unset.java added as D:\classes\Unset.class is outdated.<br />
[javac] Compiling 1 source file to D:\classes<br />
[javac] Using classic compiler<br />
[javac] Compilation args: -d D:\classes -classpath D:\classes;<br />
D:\jdk118\classes.zip; -sourcepath D:\src\java -g:none<br />
[javac] File to be compiled:<br />
D:\src\java\Unset.java<br />

      </td>
      </tr>
          </table>
                                <p>
              In many cases, Ant tasks are wrappers around OS commands or
              other Java classes. In debug mode, many of these tasks will
              print out the equivalent command line, as the
              <code>&lt;javac&gt;</code> task
              output does. If you are having a problem, it is often useful to
              run the command directly from the command line, in the same way
              Ant is running it, and see if the problem occurs from there
              as well. The problem may be in the command that is being run,
              or it may be in the way the Ant task is running the command.
              You can also see the effect of changing attribute values on the
              generated command line. This can help you to understand whether
              you are using the correct attributes and values.
            </p>
                                                          <h4 class="subsection">
        <a name="Has It Been Fixed?"></a>
        Has It Been Fixed?
      </h4>
                        <p>
            After examining the debug output, if you still believe that the
            problem you are having is caused by Ant, chances are that someone
            else may have already encountered this problem, and perhaps it has
            been fixed. The next step, therefore, would be to download the
            sources of ant, see <a href="svn.html">svn</a>.
        </p>
                                <p>
          <a href="http://vmgump.apache.org/gump/public/index.html">Gump</a>
          is building ant every night and using the ant built from the
          latest source to build a long list of open source projects. However,
          the version of ant built by gump is not available for download. Even
          if it were, it would not include most of the optional tasks.
        </p>
                                <p>
            We currently do not have nightly builds including the optional tasks.
        </p>
                                            <h3 class="section">
      <a name="bugs"></a>
      bugs
    </h3>
                        <p>If you are convinced that you have identified an unfixed bug, please turn to
      our document concerning the <a href="bugs.html">bug database</a>.</p>
                
    </div>
  </div>

        <p class="copyright">
        <script type="text/javascript" language="JavaScript"><!--
                document.write(" - "+"Last Published: " + document.lastModified);
              //  -->
        </script>
      </p>
    </body>
  </html>











