

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
        <html lang="en">
    <!-- GENERATED FILE, DO NOT EDIT, EDIT THE XML FILE IN xdocs INSTEAD! -->
    <head>
      <META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
        <title>Apache Ant - Contributors</title>
        <link type="text/css" href="./page.css" rel="stylesheet">
          <meta name="author" content="Apache Ant PMC">
  <meta name="email" content="">
      </head>

    <body>
      <p class="navpath">
        <script src="./breadcrumbs.js" language="JavaScript" type="text/javascript"></script>
      </p>

      <div class="logobar">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td align="left"><img border="0" alt="Apache Ant site" src="./images/group-logo.gif"></td>
            <td align="center" width="100%"><img alt="Apache Ant logo" border="0" src="./images/project-logo.gif"></td>
            <td align="right">
              <form target="_blank" onsubmit="q.value = query.value + ' site:ant.apache.org'" action="http://www.google.com/search" method="get">
                <table summary="search" border="0" cellspacing="0" cellpadding="0" bgcolor="#4C6C8F">
                  <tr>
                    <td colspan="3"><img height="10" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td nowrap="nowrap" class="searchcaption">
                      <input name="q" type="hidden">
                      <input size="15" id="query" type="text">
                      <img height="1" width="5" alt="" src="./images/spacer.gif">
                      <input name="Search" value="Search" type="submit">
                      <br>
                      the Apache Ant site
                    </td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-left.gif"></td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-right.gif"></td>
                  </tr>
                </table>
              </form>
            </td>
          </tr>
        </table>
      </div>

                  <div class="tab">
              <table summary="tab bar" border="0" cellpadding="0" cellspacing="0">
                <tr>
                                  <td width="8"><img alt="" height="5" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="selected tab" style="height: 1.5em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                        <td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-left.gif"></td><td valign="middle" bgcolor="#4C6C8F"><font color="#ffffff" size="2" face="Arial, Helvetica, Sans-serif"><b>Home</b></font></td><td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                                    <td width="5"><img alt="" height="8" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="non selected tab" style="height: 1.4em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                          <td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-left.gif"></td><td valign="middle" bgcolor="#B2C4E0"><a href="./projects/index.html"><font size="2" face="Arial, Helvetica, Sans-serif">Projects</font></a></td><td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                            </tr>
              </table>
            </div>

      <div class="bluebar"></div>
                    
  <div class="menucontainer">

    <div class="menu">
      <ul>
              <li class="menuheader">Apache Ant
          <ul>
                            <li>
                                    <a href="./index.html">Welcome</a>
                                </li>
                            <li>
                                    <a href="./license.html">License</a>
                                </li>
                            <li>
                                    <a href="./antnews.html">News</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Documentation
          <ul>
                            <li>
                                    <a href="./manual/index.html">Manual</a>
                                </li>
                            <li>
                                    <a href="./projects.html">Related Projects</a>
                                </li>
                            <li>
                                    <a href="./external.html">External Tools and Tasks</a>
                                </li>
                            <li>
                                    <a href="./resources.html">Resources</a>
                                </li>
                            <li>
                                    <a href="./faq.html">Frequently Asked Questions</a>
                                </li>
                            <li>
                                    <a href="http://wiki.apache.org/ant/FrontPage">Wiki</a>
                                </li>
                            <li>
                                    <a href="./problems.html">Having Problems?</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Download
          <ul>
                            <li>
                                    <a href="http://ant.apache.org/bindownload.cgi">Binary Distributions</a>
                                </li>
                            <li>
                                    <a href="http://ant.apache.org/srcdownload.cgi">Source Distributions</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Contributing
          <ul>
                            <li>
                                    <a href="./mail.html">Mailing Lists</a>
                                </li>
                            <li>
                                    <a href="./svn.html">Subversion Repositories</a>
                                </li>
                            <li>
                                    <a href="./nightlies.html">Nightly Builds</a>
                                </li>
                            <li>
                                    <a href="./bugs.html">Bug Database</a>
                                </li>
                            <li>
                                    <a href="http://www.apache.org/foundation/contributing.html">Donations</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Project Management
          <ul>
                            <li>
                              <span class="sel">Contributors</span>
                              </li>
                            <li>
                                    <a href="./mission.html">Apache Ant Mission</a>
                                </li>
                            <li>
                                    <a href="./bylaws.html">Project Bylaws</a>
                                </li>
                            <li>
                                    <a href="./legal.html">Legal</a>
                                </li>
                      </ul>
        </li>
            </ul>
    </div>
    <img style="float: left" height="10" width="10" border="0" alt="" src="./images/menu-left.gif">
    <img style="float: right" height="10" width="10" border="0" alt="" src="./images/menu-right.gif">
  </div>
      <div class="lightbluebar">&nbsp;</div>
  <div class="main">
  <div class="content">
    <h1 class="title">Contributors</h1>
            <h3 class="section">
      <a name="Project Management Committee"></a>
      Project Management Committee
    </h3>
                              <h4 class="subsection">
        <a name="Active Members"></a>
        Active Members
      </h4>
                        <p>
                <b>Bruce Atherton</b> (bruce at callenish.com - <a href="http://www.callenish.com/~bruce">http://www.callenish.com/~bruce</a>)
<br />
Currently a Systems Architect with Avue Technologies, Bruce has been
working with Java since version 1.0a2. He also claims to be one of the first
people to mark up a FAQ with HTML, for a web browser of the distant past
called Cello.
            </p>
                                <p>
                <b>Stephane Bailliez</b><br />
            </p>
                                <p>
                <b>Matt Benson</b><br />
            </p>
                                <p>
                <b>Stefan Bodewig</b> (stefan.bodewig at freenet.de -
          <a href="http://stefan.samaflost.de/">http://stefan.samaflost.de/</a>)
<br />
</p>
                                <p>
                <b>Dominique Devienne</b> (ddevienne at apache.org)
<br />
Dominique has been involved non-stop with the Ant Employee community since
the 1.4 days, trying without success to answer posts as well or as often
as Diane Holt after she left the Employee list. He is opinionated, always
striving for the best possible design. While at Landmark Graphics, he
designed and implemented large Ant/CppTasks builds for mixed Java/C++ projects.
            </p>
                                <p>
                <b>Erik Hatcher</b> (ehatcher at apache.org)
<br />
Erik is the co-author of <a href="http://www.manning.com/hatcher">
Java Development with Ant</a> and speaks on Ant and other topics at
<a href="http://www.nofluffjuststuff.com">No Fluff, Just Stuff
symposiums</a> as well as other venues.  Erik is the President of
<a href="http://www.ehatchersolutions.com">eHatcher Solutions, Inc</a>.
</p>
                                <p>
                <b>Martijn (J.M.) Kruithof</b> (ant at kruithof xs4all nl)
<br />
Martijn Kruithof is a system engineer working with and on Java products
in a telecommunication network setting.
</p>
                                <p>
                <b>Antoine Levy-Lambert</b> (antoine at apache.org)
<br />
Antoine is working as a software engineer for 
<a href="http://www.arielpartners.com">Ariel Partners</a>.
He is specialized in builds and automation of deployment processes.

            </p>
                                <p>
                <b>Steve Loughran</b><br />
            </p>
                                <p>
                <b>Conor MacNeill</b> (conor at cortexebusiness.com.au)
<br />
Conor is a senior developer at Cortex eBusiness, where he develops
J2EE based systems. In his spare time he helps with the development of
the Ant build tool.  He is also serving as the Chairman of this PMC.
</p>
                                <p>
				<b>Jan Matèrne</b> (jhm at apache.org)
<br />
Jan is consultant for OOA/D in the computer centre of the government
of Northrhine Westfalia / Germany.
            </p>
                                <p>
                <b>Peter Reilly</b><br />
            </p>
                                <p>
                <b>
                    <a href="http://www.intertwingly.net/">Sam Ruby</a>
                </b>
                (rubys at us.ibm.com)
<br />
Sam takes a perverse pleasure in integrating disparate things.  He is
a member of the <a href="http://www.php.net/credits.php">PHP group</a>, Apache
<a href="http://xml.apache.org/whoweare.html">XML PMC</a>, Apache
sponsor for the <a href="http://xml.apache.org/soap">xml-soap</a> subproject
and convener of <a href="http://www.ecma.ch">ECMA</a> TC39 TG3.
</p>
                                <p>
                <b>Magesh Umasankar</b> (umagesh at apache.org)
<br />
Magesh is a lead software developer at
<a href="http://www.manugistics.com">Manugistics</a>, where
he is responsible for some of the Revenue Optimization
solutions.
</p>
                                <p>
                <b>Christoph Wilhelms</b> (christoph.wilhelms at t-online.de)
<br />
Christoph works as software engineer at the world's biggest travel company
<a href="http://www.tui.com">TUI</a>. His passion are all UI related things so
at the Ant-Project he takes care of Antidote - the Ant GUI.
            </p>
                                                          <h4 class="subsection">
        <a name="Emeritus Members"></a>
        Emeritus Members
      </h4>
                        <p>
                <b>James Duncan Davidson</b> (duncan at x180.net - <a href="http://x180.net/">http://x180.net/</a>)
<br />

By day, Duncan works in the Open Source Program Office at Sun
Microsystems where he helps various Open Source efforts within Sun
"do the right thing". Previously at Sun he was responsible
for the Servlet API Specifications 2.1 and 2.2 as well as the Java API
for XML Parsing 1.0 and was the original author of Tomcat and Ant. He
was one of the rabble-rousers within Sun that helped make the Jakarta
Project a reality and served as the first Chairman of the Jakarta PMC.
</p>
                                <p>
                <b>Diane Holt</b><br />
            </p>
                                <p>
                <b>Donald Leslie</b><br />
            </p>
                                <p>
                <b>Costin Monolache</b><br />
            </p>
                                <p>
                <b>Jon Skeet</b><br />
            </p>
                                            <h3 class="section">
      <a name="Committers"></a>
      Committers
    </h3>
                              <h4 class="subsection">
        <a name="Active Committers"></a>
        Active Committers
      </h4>
                        <p>
                <b>Steve Cohen</b>
            </p>
                                <p>
                <b>Jose Alberto Fernandez</b>
            </p>
                                <p>
                <b>Jesse Glick</b> (jesse dot glick at sun dot com)
<br />
Jesse has been using Java since 1998 and joined Sun Microsystems as
part of the company that produced the NetBeans IDE. After discovering
Ant in the 1.2 days, he wrote most of NetBeans' Ant integration.
Recently he has worked on the NetBeans 4.0 project system, based heavily
on Ant as a build tool.
</p>
                                <p>
  <b>Kevin Jackson</b> (foamdino at gmail.com)<br />
</p>
                                <p>
   <b>Alexey Solofnenko</b> (trelony at gmail.com)<br />
</p>
                                                          <h4 class="subsection">
        <a name="Emeritus Committers"></a>
        Emeritus Committers
      </h4>
                        <p>
                <b>Preston Bannister</b><br />
            </p>
                                <p>
                <b>Nick Davis</b><br />
            </p>
                                <p>
                <b>Darrell DeBoer</b><br />
            </p>
                                <p>
                <b>Peter Donald</b> (peter at apache.org)
<br />

Peter is an avid java developer who is active in the
<a href="http://jakarta.apache.org/avalon/">Avalon</a> and
<a href="http://ant.apache.org/">Ant</a> projects.
In his spare time he develops a distributed virtual environment
(ie military simulator or 3D game) using java technologies.
</p>
                                <p>
                <b>Danno Ferrin</b> (shemnon at yahoo.com)
<br />
Danno has been programming in Java since Summer 96. Danno wrote a JSP
engine on his own and released it the very same day Jakarta was
announced at JavaOne. Since then, he decided to join the Jakarta
project in a spirit of co-operation over competition.
</p>
                                <p>
                <b>Simeon H.K. Fitch</b> (simeon.fitch at mseedsoft.com)
<br />
Simeon is owner of Mustard Seed Software, which specializes in developing
distributed applications and Employee interfaces for the science, engineering,
and research oriented clients. He is the lead architect and developer for
Antidote, the GUI for Ant.
</p>
                                <p>
                <b>Thomas Haas</b> (tha at whitestein.com)
<br />
Tom is interested in distributed systems, Java middleware and worked on an
implementation of the JMS specification. At Whitestein Technologies he is
working on bringing software agent technology and J2EE together.
</p>
                                <p>

                <b>Jason Hunter</b> (jh at servlets.com)
<br />
Jason is author of "Java Servlet Programming" (O'Reilly) and publisher
of <a href="http://www.servlets.com/">http://www.servlets.com/</a>.
He works at <a href="http://www.collab.net">CollabNet</a>.
</p>
                                <p>
                <b>Justyna Horwat</b> (horwat at apache.org)
<br />
</p>
                                <p>
                <b>Arun Jamwal</b>
<br />
</p>
                                <p>
                <b>Arnout J. Kuiper</b> (ajkuiper at planet.nl)
<br />

Arnout J. Kuiper is a Java Architect with the Sun Java Center at Sun
Microsystems. His main focus is web-related technologies on the Java
platform (J2EE, XML, ...).
</p>
                                <p>
                <b>Stefano Mazzocchi</b> (stefano at apache.org)
<br />
Stefano is addicted to software design, Java programming and
open development. In the last 4 years, he has contributed way too much
time to Apache, expecially on JServ, JMeter, Avalon, JAMES, Ant, Cocoon
and helping to bring more projects into Apache-land, such as FOP, Batik,
POI and Xindice. The problem is that he's too picky to be satisfied :-)
</p>
                                <p>
                <b>Glenn McAllister</b> (glenn at somanetworks.com)
<br />
Glenn McAllister is a software developer at SOMA Networks, was formerly
the same at IBM (plus tech writer plus build guy), and does some writing
on the side for the VADD Technical Journal.
</p>
                                <p>
                <b>Craig McClanahan</b> (Craig.McClanahan at eng.sun.com)
<br />
Craig was involved in the Apache JServ project, focused on implementing
a next generation architecture and feature set for the core servlet
engine.  He has recently joined Sun as technical lead for the servlet
and JSP reference implementation.
          </p>
                                <p>
                <b>Adam Murdoch</b>
<br />
</p>
                                <p>
                <b>Harish Prabhandham</b> (harishp at onebox.com)
<br />
Harish is an engineer with the J2EE team at Sun, primarily responsible
for implementing security in the J2EE Reference Implementation
(RI). He integrated various technologies including servlet/JSP
implementations from Tomcat into the J2EE RI. These days, he hacks PHP
code during the day.
</p>
                                <p>
                <b>Nico Seessle</b><br />
            </p>
                                <p>
                <b>Gal Shachor</b> (shachor at il.ibm.com)
<br />
Gal Shachor is a research staff member at IBM. He wrote his first
Servlet container (ServletExpress) at the beginning of 1997. Later on
ServletExpress (and Gal) merged into WebSphere, and Gal participated
in the development of WebSphere 1, 2 and 3.
</p>
                                <p>
                <b>Jon S. Stevens</b> (jon at collab.net)
<br />

Jon is a Co-Founder of <a href="http://www.clearink.com/">Clear Ink
Corp</a> and recently left to work on <a href="http://scarab.tigris.org/">Scarab</a> a next generation Open
Source Java Servlet based Issue/Bug tracking system for <a href="http://www.collab.net/">CollabNet</a>. He is an active developer
of the <a href="http://java.apache.org/jserv/">Apache JServ Servlet
Engine</a> for the Apache Web Server and Co-Author of the <a href="http://java.apache.org/ecs/">Element Construction Set</a> as
well as the web application framework, <a href="http://java.apache.org/turbine/">Turbine</a>.
</p>
                                <p>
                <b>Jesse Stockall</b><br />
            </p>
                                <p>
                <b>James Todd</b> (jwtodd at pacbell.net)
<br />
James has developed real time customer oriented apps for roughly 10
years the last 5 of which have predominately been fully integrated,
front and back, extraNet implementations which have been based on
Apache, Java and Tcl.
</p>
                                <p>
                <b>Anil Vijendran</b> (akv at eng.sun.com)
<br />
Anil Vijendran is the principal developer of the JSP engine in
Tomcat. He's done some pretty scary things in his past life --
implementing the CORBA IDL to C++ 2.0 mapping, skydiving, IDL to Java
compilers, Object Databases (SIGSEV, you da man!) for C++, Java ORB
and EJB runtime environments -- in that order.
</p>
                                            <h3 class="section">
      <a name="Logo"></a>
      Logo
    </h3>
                        <p>Ant's logo is the result of a logo contest, it has been
        designed by</p>
                                <p>
                <b>Nick King</b>
<br />
</p>
                
    </div>
  </div>

        <p class="copyright">
        <script type="text/javascript" language="JavaScript"><!--
                document.write(" - "+"Last Published: " + document.lastModified);
              //  -->
        </script>
      </p>
    </body>
  </html>











