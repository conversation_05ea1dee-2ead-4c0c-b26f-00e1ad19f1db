

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
        <html lang="en">
    <!-- GENERATED FILE, DO NOT EDIT, EDIT THE XML FILE IN xdocs INSTEAD! -->
    <head>
      <META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
        <title>Apache Ant - Project Bylaws</title>
        <link type="text/css" href="./page.css" rel="stylesheet">
          <meta name="author" content="Apache Ant PMC">
  <meta name="email" content="">
      </head>

    <body>
      <p class="navpath">
        <script src="./breadcrumbs.js" language="JavaScript" type="text/javascript"></script>
      </p>

      <div class="logobar">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td align="left"><img border="0" alt="Apache Ant site" src="./images/group-logo.gif"></td>
            <td align="center" width="100%"><img alt="Apache Ant logo" border="0" src="./images/project-logo.gif"></td>
            <td align="right">
              <form target="_blank" onsubmit="q.value = query.value + ' site:ant.apache.org'" action="http://www.google.com/search" method="get">
                <table summary="search" border="0" cellspacing="0" cellpadding="0" bgcolor="#4C6C8F">
                  <tr>
                    <td colspan="3"><img height="10" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td nowrap="nowrap" class="searchcaption">
                      <input name="q" type="hidden">
                      <input size="15" id="query" type="text">
                      <img height="1" width="5" alt="" src="./images/spacer.gif">
                      <input name="Search" value="Search" type="submit">
                      <br>
                      the Apache Ant site
                    </td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-left.gif"></td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-right.gif"></td>
                  </tr>
                </table>
              </form>
            </td>
          </tr>
        </table>
      </div>

                  <div class="tab">
              <table summary="tab bar" border="0" cellpadding="0" cellspacing="0">
                <tr>
                                  <td width="8"><img alt="" height="5" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="selected tab" style="height: 1.5em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                        <td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-left.gif"></td><td valign="middle" bgcolor="#4C6C8F"><font color="#ffffff" size="2" face="Arial, Helvetica, Sans-serif"><b>Home</b></font></td><td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                                    <td width="5"><img alt="" height="8" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="non selected tab" style="height: 1.4em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                          <td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-left.gif"></td><td valign="middle" bgcolor="#B2C4E0"><a href="./projects/index.html"><font size="2" face="Arial, Helvetica, Sans-serif">Projects</font></a></td><td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                            </tr>
              </table>
            </div>

      <div class="bluebar"></div>
                    
  <div class="menucontainer">

    <div class="menu">
      <ul>
              <li class="menuheader">Apache Ant
          <ul>
                            <li>
                                    <a href="./index.html">Welcome</a>
                                </li>
                            <li>
                                    <a href="./license.html">License</a>
                                </li>
                            <li>
                                    <a href="./antnews.html">News</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Documentation
          <ul>
                            <li>
                                    <a href="./manual/index.html">Manual</a>
                                </li>
                            <li>
                                    <a href="./projects.html">Related Projects</a>
                                </li>
                            <li>
                                    <a href="./external.html">External Tools and Tasks</a>
                                </li>
                            <li>
                                    <a href="./resources.html">Resources</a>
                                </li>
                            <li>
                                    <a href="./faq.html">Frequently Asked Questions</a>
                                </li>
                            <li>
                                    <a href="http://wiki.apache.org/ant/FrontPage">Wiki</a>
                                </li>
                            <li>
                                    <a href="./problems.html">Having Problems?</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Download
          <ul>
                            <li>
                                    <a href="http://ant.apache.org/bindownload.cgi">Binary Distributions</a>
                                </li>
                            <li>
                                    <a href="http://ant.apache.org/srcdownload.cgi">Source Distributions</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Contributing
          <ul>
                            <li>
                                    <a href="./mail.html">Mailing Lists</a>
                                </li>
                            <li>
                                    <a href="./svn.html">Subversion Repositories</a>
                                </li>
                            <li>
                                    <a href="./nightlies.html">Nightly Builds</a>
                                </li>
                            <li>
                                    <a href="./bugs.html">Bug Database</a>
                                </li>
                            <li>
                                    <a href="http://www.apache.org/foundation/contributing.html">Donations</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Project Management
          <ul>
                            <li>
                                    <a href="./contributors.html">Contributors</a>
                                </li>
                            <li>
                                    <a href="./mission.html">Apache Ant Mission</a>
                                </li>
                            <li>
                              <span class="sel">Project Bylaws</span>
                              </li>
                            <li>
                                    <a href="./legal.html">Legal</a>
                                </li>
                      </ul>
        </li>
            </ul>
    </div>
    <img style="float: left" height="10" width="10" border="0" alt="" src="./images/menu-left.gif">
    <img style="float: right" height="10" width="10" border="0" alt="" src="./images/menu-right.gif">
  </div>
      <div class="lightbluebar">&nbsp;</div>
  <div class="main">
  <div class="content">
    <h1 class="title">Project Bylaws</h1>
            <h3 class="section">
      <a name="Apache Ant Project Bylaws"></a>
      Apache Ant Project Bylaws
    </h3>
                        <p>
      This document defines the bylaws under which the Apache Ant project
      operates. It defines the roles and responsibilities of the
      project, who may vote, how voting works, how conflicts are resolved,
      etc.
    </p>
                                <p>
      Ant is a project of the
      <a href="http://www.apache.org/foundation/">Apache Software
      Foundation</a>.  The foundation holds the copyright on Apache
      code including the code in the  Ant codebase. The
      <a href="http://www.apache.org/foundation/faq.html">foundation FAQ</a>
      explains the operation and background of the foundation.
    </p>
                                <p>
      Ant is typical of Apache projects in that it operates under a set of
      principles, known collectively as the "Apache Way". If you are
      new to Apache development, please refer to the
      <a href="http://incubator.apache.org">Incubator project</a>
      for more information on how Apache projects operate. <b>Note:</b> the
      incubator project has only been recently set up and does not yet explain
      the Apache Way in great detail.
    </p>
                                <ul>
        <li><a href="#Roles and Responsibilities">Roles and Responsibilities</a></li>
        <li><a href="#Decision Making">How decisions are made</a></li>
    </ul>
                        <h3 class="section">
      <a name="Roles and Responsibilities"></a>
      Roles and Responsibilities
    </h3>
                        <p>
      Apache projects define a set of roles with associated rights and
      responsibilities. These roles govern what tasks an individual may perform
      within the project. The roles are defined in the following sections
    </p>
                                <ul>
      <li><a href="#Users">Users</a></li>
      <li><a href="#Developers">Developers</a></li>
      <li><a href="#Committers">Committers</a></li>
      <li><a href="#Project Management Committee">
        Project Management Committee (PMC)</a>
      </li>
    </ul>
                                      <h4 class="subsection">
        <a name="Users"></a>
        Users
      </h4>
                        <p>
        The most important participants in the project are people who use our
        software. The majority of our developers start out as users and guide
        their development efforts from the Employee's perspective.
      </p>
                                <p>
        Users contribute to the Apache projects by providing feedback to
        developers in the form of bug reports and feature suggestions. As
        well, users participate in the Apache community by helping other users
        on mailing lists and Employee support forums.
      </p>
                                                          <h4 class="subsection">
        <a name="Developers"></a>
        Developers
      </h4>
                        <p>
        All of the volunteers who are contributing time, code, documentation,
        or resources to the Ant Project. A developer that makes sustained,
        welcome contributions to the project may be invited to become a
        Committer, though the exact timing of such invitations depends on many
        factors.
      </p>
                                                          <h4 class="subsection">
        <a name="Committers"></a>
        Committers
      </h4>
                        <p>
        The project's Committers are responsible for the project's technical
        management. All committers have write access to the project's source
        repositories. Committers may cast binding votes on any technical
        discussion regarding the project.
      </p>
                                <p>
        Committer access is by invitation only and must be approved by lazy
        consensus of the active PMC members. A Committer is considered emeritus
        by their own declaration or by not contributing in any form to the
        project for over six months. An emeritus committer may request
        reinstatement of commit access from the PMC. Such reinstatement is
        subject to lazy consensus of active PMC members.
      </p>
                                <p>
        Commit access can be revoked by a unanimous vote of all the active
        PMC members (except the committer in question if they are also a PMC member).
      </p>
                                <p>
        All Apache committers are required to have a signed Contributor License
        Agreement (CLA) on file with the Apache Software Foundation. There is a
        <a href="http://www.apache.org/dev/committers.html">Committer FAQ</a>
        which provides more details on the requirements for Committers
      </p>
                                <p>
        A committer who makes a sustained contribution to the project may be
        invited to become a member of the PMC. The form of contribution is
        not limited to code. It can also include code review, helping out
        users on the mailing lists, documentation, etc.
      </p>
                                                          <h4 class="subsection">
        <a name="Project Management Committee"></a>
        Project Management Committee
      </h4>
                        <p>
        The Project Management Committee (PMC) for Apache Ant was created by a
        <a href="mission.html">resolution</a> of the board of the Apache
        Software Foundation on 18<sup>th</sup> November 2002. The PMC is
        responsible to the board and the ASF for the management and oversight
        of the Apache Ant codebase. The responsibilities of the PMC include
      </p>
                                <ul>
        <li>Deciding what is distributed as products of the Apache Ant project.
            In particular all releases must be approved by the PMC
        </li>
        <li>Maintaining the project's shared resources, including the codebase
            repository, mailing lists, websites.
        </li>
        <li>Speaking on behalf of the project.
        </li>
        <li>Resolving license disputes regarding products of the project
        </li>
        <li>Nominating new PMC members and committers
        </li>
        <li>Maintaining these bylaws and other guidelines of the project
        </li>
      </ul>
                                <p>
        Membership of the PMC is by invitation only and must be approved by a
        lazy consensus of active PMC members. A PMC member is considered
        "emeritus" by their own declaration or by not contributing in
        any form to the project for over six months. An emeritus member may
        request reinstatement to the PMC. Such reinstatement is subject to lazy
        consensus of the active PMC members. Membership of the PMC can be
        revoked by an unanimous vote of all the active PMC members other than
        the member in question.
      </p>
                                <p>
        The chair of the PMC is appointed by the ASF board. The chair is an
        office holder of the Apache Software Foundation (Vice President,
        Apache Ant) and has primary responsibility to the board for the
        management of the projects within the scope of the Ant PMC. The chair
        reports to the board quarterly on developments within the Ant project.
        The PMC may consider the position of PMC chair annually and if
        supported by 2/3 Majority may recommend a new chair to the board.
        Ultimately, however, it is the board's responsibility who it chooses
        to appoint as the PMC chair.
      </p>
                                            <h3 class="section">
      <a name="Decision Making"></a>
      Decision Making
    </h3>
                        <p>
      Within the Ant project, different types of decisions require different
      forms of approval. For example, the
      <a href="#Roles and Responsibilities">previous section</a> describes
      several decisions which require "lazy consensus" approval. This
      section defines how voting is performed, the types of approvals, and which
      types of decision require which type of approval.
    </p>
                                      <h4 class="subsection">
        <a name="Voting"></a>
        Voting
      </h4>
                        <p>
        Decisions regarding the project are made by votes on the primary project
        development mailing list (<EMAIL>). Where necessary,
        PMC voting may take place on the private Ant PMC mailing list.
        Votes are clearly indicated by subject line starting with [VOTE] or
        [PMC-VOTE]. Votes may contain multiple items for approval and these
        should be clearly separated. Voting is carried out by replying to the
        vote mail. Voting may take four flavours
      </p>
                                      <table class="ForrestTable" cellspacing="1" cellpadding="4">
              <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>+1</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            "Yes," "Agree," or "the action should be
            performed." In general, this vote also indicates a willingness
            on the behalf of the voter in "making it happen"
          
      </td>
      </tr>
                  <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>+0</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            This vote indicates a willingness for the action under
            consideration to go ahead. The voter, however will not be able
            to help.
          
      </td>
      </tr>
                  <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>-0</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            This vote indicates that the voter does not, in general, agree with
            the proposed action but is not concerned enough to prevent the
            action going ahead.
          
      </td>
      </tr>
                  <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>-1</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            This is a negative vote. On issues where consensus is required,
            this vote counts as a <strong>veto</strong>. All vetoes must
            contain an explanation of why the veto is appropriate. Vetoes with
            no explanation are void. It may also be appropriate for a -1 vote
            to include an alternative course of action.
          
      </td>
      </tr>
          </table>
                                <p>
        All participants in the Ant project are encouraged to show their
        agreement with or against a particular action by voting. For technical
        decisions, only the votes of active committers are binding. Non binding
        votes are still useful for those with binding votes to understand the
        perception of an action in the wider Ant community. For PMC decisions,
        only the votes of PMC members are binding.
      </p>
                                <p>
        Voting can also be applied to changes made to the Ant codebase. These
        typically take the form of a veto (-1) in reply to the commit message
        sent when the commit is made.
      </p>
                                                          <h4 class="subsection">
        <a name="Approvals"></a>
        Approvals
      </h4>
                        <p>
        These are the types of approvals that can be sought. Different actions
        require different types of approvals
      </p>
                                      <table class="ForrestTable" cellspacing="1" cellpadding="4">
              <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>Consensus</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            For this to pass, all voters with binding votes must vote and there
            can be no binding vetoes (-1). Consensus votes are rarely required
            due to the impracticality of getting all eligible voters to cast a
            vote.
          
      </td>
      </tr>
                  <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>Lazy Consensus</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Lazy consensus requires 3 binding +1 votes and no binding vetoes.
          
      </td>
      </tr>
                  <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>Lazy Majority</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            A lazy majority vote requires 3 binding +1 votes and more binding +1
            votes that -1 votes.
          
      </td>
      </tr>
                  <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>Lazy Approval</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            An action with lazy approval is implicitly allowed unless a -1 vote
            is received, at which time, depending on the type of action, either
            lazy majority or lazy consensus approval must be obtained.
          
      </td>
      </tr>
                  <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>2/3 Majority</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Some actions require a 2/3 majority of active committers or PMC
            members to pass. Such actions typically affect the foundation
            of the project (e.g. adopting a new codebase to replace an existing
            product). The higher threshold is designed to ensure such changes
            are strongly supported. To pass this vote requires at least 2/3 of
            binding vote holders to vote +1
          
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Vetoes"></a>
        Vetoes
      </h4>
                        <p>
        A valid, binding veto cannot be overruled. If a veto is cast, it must be
        accompanied by a valid reason explaining the reasons for the veto. The
        validity of a veto, if challenged, can be confirmed by anyone who has
        a binding vote. This does not necessarily  signify agreement with the
        veto - merely that the veto is valid.
      </p>
                                <p>
        If you disagree with a valid veto, you must lobby the Employee casting
        the veto to withdraw their veto. If a veto is not withdrawn, the action
        that has been vetoed must be reversed in a timely manner.
      </p>
                                                          <h4 class="subsection">
        <a name="Actions"></a>
        Actions
      </h4>
                        <p>
        This section describes the various actions which are undertaken within
        the project, the corresponding approval required for that action and
        those who have binding votes over the action.
      </p>
                                      <table class="ForrestTable" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Action
      </th>
                          <th colspan="1" rowspan="1"
      valign="top" align="left">
          Description
      </th>
                          <th colspan="1" rowspan="1"
      valign="top" align="left">
          Approval
      </th>
                          <th colspan="1" rowspan="1"
      valign="top" align="left">
          Binding Votes
      </th>
      </tr>
                  <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>Code Change</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            A change made to a codebase of the project and committed
            by a committer. This includes source code, documentation, website
            content, etc.
          
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Lazy approval and then lazy consensus.
          
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Active committers.
          
      </td>
      </tr>
                  <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>Release Plan</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Defines the timetable and actions for a release. The plan also
            nominates a Release Manager.
          
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Lazy majority
          
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Active committers
          
      </td>
      </tr>
                  <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>Product Release</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            When a release of one of the project's products is ready, a vote is
            required to accept the release as an official release of the
            project.
          
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Lazy Majority
          
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Active PMC members
          
      </td>
      </tr>
                  <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>Adoption of New Codebase</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            <p>
            When the codebase for an existing, released product is to be
            replaced with an alternative codebase. If such a vote fails to
            gain approval, the existing code base will continue.
            </p>

            <p>
            This also covers the creation of new sub-projects
            within the project
            </p>
          
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            2/3 majority
          
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Active committers
          
      </td>
      </tr>
                  <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>New Committer</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            When a new committer is proposed for the project
          
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Lazy consensus
          
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Active PMC members
          
      </td>
      </tr>
                  <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>New PMC Member</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            When a committer is proposed for the PMC
          
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Lazy consensus
          
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Active PMC members
          
      </td>
      </tr>
                  <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>Committer Removal</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            <p>When removal of commit privileges is sought.</p>
            <p><b>Note: </b> Such actions will also be referred to the ASF
            board by the PMC chair</p>
          
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Consensus
          
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Active PMC members (excluding the committer in question if a
            member of the PMC).
          
      </td>
      </tr>
                  <tr>
                      <td colspan="1" rowspan="1"
      valign="top" align="left">
          <strong>PMC Member Removal</strong>
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            <p>When removal of a PMC member is sought.</p>
            <p><b>Note: </b> Such actions will also be referred to the
            ASF board by the PMC chair</p>
          
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Consensus
          
      </td>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
            Active PMC members (excluding the member in question).
          
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Voting Timeframes"></a>
        Voting Timeframes
      </h4>
                        <p>
        Votes are open for a period of 1 week to allow all active voters
        time to consider the vote. Votes relating to code changes are not
        subject to a strict timetable but should be made as timely as possible.
      </p>
                                    
    </div>
  </div>

        <p class="copyright">
        <script type="text/javascript" language="JavaScript"><!--
                document.write(" - "+"Last Published: " + document.lastModified);
              //  -->
        </script>
      </p>
    </body>
  </html>











