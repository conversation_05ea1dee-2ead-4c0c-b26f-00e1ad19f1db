

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
        <html lang="en">
    <!-- GENERATED FILE, DO NOT EDIT, EDIT THE XML FILE IN xdocs INSTEAD! -->
    <head>
      <META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
        <title>Apache Ant - Welcome</title>
        <link type="text/css" href="./page.css" rel="stylesheet">
          <meta name="author" content="<PERSON>">
  <meta name="email" content="">
        <meta name="author" content="<PERSON>">
  <meta name="email" content="<EMAIL>">
      </head>

    <body>
      <p class="navpath">
        <script src="./breadcrumbs.js" language="JavaScript" type="text/javascript"></script>
      </p>

      <div class="logobar">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td align="left"><img border="0" alt="Apache Ant site" src="./images/group-logo.gif"></td>
            <td align="center" width="100%"><img alt="Apache Ant logo" border="0" src="./images/project-logo.gif"></td>
            <td align="right">
              <form target="_blank" onsubmit="q.value = query.value + ' site:ant.apache.org'" action="http://www.google.com/search" method="get">
                <table summary="search" border="0" cellspacing="0" cellpadding="0" bgcolor="#4C6C8F">
                  <tr>
                    <td colspan="3"><img height="10" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td nowrap="nowrap" class="searchcaption">
                      <input name="q" type="hidden">
                      <input size="15" id="query" type="text">
                      <img height="1" width="5" alt="" src="./images/spacer.gif">
                      <input name="Search" value="Search" type="submit">
                      <br>
                      the Apache Ant site
                    </td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-left.gif"></td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-right.gif"></td>
                  </tr>
                </table>
              </form>
            </td>
          </tr>
        </table>
      </div>

                  <div class="tab">
              <table summary="tab bar" border="0" cellpadding="0" cellspacing="0">
                <tr>
                                  <td width="8"><img alt="" height="5" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="selected tab" style="height: 1.5em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                        <td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-left.gif"></td><td valign="middle" bgcolor="#4C6C8F"><font color="#ffffff" size="2" face="Arial, Helvetica, Sans-serif"><b>Home</b></font></td><td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                                    <td width="5"><img alt="" height="8" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="non selected tab" style="height: 1.4em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                          <td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-left.gif"></td><td valign="middle" bgcolor="#B2C4E0"><a href="./projects/index.html"><font size="2" face="Arial, Helvetica, Sans-serif">Projects</font></a></td><td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                            </tr>
              </table>
            </div>

      <div class="bluebar"></div>
                    
  <div class="menucontainer">

    <div class="menu">
      <ul>
              <li class="menuheader">Apache Ant
          <ul>
                            <li>
                              <span class="sel">Welcome</span>
                              </li>
                            <li>
                                    <a href="./license.html">License</a>
                                </li>
                            <li>
                                    <a href="./antnews.html">News</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Documentation
          <ul>
                            <li>
                                    <a href="./manual/index.html">Manual</a>
                                </li>
                            <li>
                                    <a href="./projects.html">Related Projects</a>
                                </li>
                            <li>
                                    <a href="./external.html">External Tools and Tasks</a>
                                </li>
                            <li>
                                    <a href="./resources.html">Resources</a>
                                </li>
                            <li>
                                    <a href="./faq.html">Frequently Asked Questions</a>
                                </li>
                            <li>
                                    <a href="http://wiki.apache.org/ant/FrontPage">Wiki</a>
                                </li>
                            <li>
                                    <a href="./problems.html">Having Problems?</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Download
          <ul>
                            <li>
                                    <a href="http://ant.apache.org/bindownload.cgi">Binary Distributions</a>
                                </li>
                            <li>
                                    <a href="http://ant.apache.org/srcdownload.cgi">Source Distributions</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Contributing
          <ul>
                            <li>
                                    <a href="./mail.html">Mailing Lists</a>
                                </li>
                            <li>
                                    <a href="./svn.html">Subversion Repositories</a>
                                </li>
                            <li>
                                    <a href="./nightlies.html">Nightly Builds</a>
                                </li>
                            <li>
                                    <a href="./bugs.html">Bug Database</a>
                                </li>
                            <li>
                                    <a href="http://www.apache.org/foundation/contributing.html">Donations</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Project Management
          <ul>
                            <li>
                                    <a href="./contributors.html">Contributors</a>
                                </li>
                            <li>
                                    <a href="./mission.html">Apache Ant Mission</a>
                                </li>
                            <li>
                                    <a href="./bylaws.html">Project Bylaws</a>
                                </li>
                            <li>
                                    <a href="./legal.html">Legal</a>
                                </li>
                      </ul>
        </li>
            </ul>
    </div>
    <img style="float: left" height="10" width="10" border="0" alt="" src="./images/menu-left.gif">
    <img style="float: right" height="10" width="10" border="0" alt="" src="./images/menu-right.gif">
  </div>
      <div class="lightbluebar">&nbsp;</div>
  <div class="main">
  <div class="content">
    <h1 class="title">Welcome</h1>
            <h3 class="section">
      <a name="Ant 1.7.0"></a>
      Ant 1.7.0
    </h3>
                        <h3>December 19, 2006 - Ant 1.7.0 Available</h3>
                                <p>Apache Ant 1.7.0 is now available for <a href="http://ant.apache.org/bindownload.cgi">download</a>.</p>
                                <p>Ant 1.7 introduces a resource framework. Some of the core ant 
    tasks such as &lt;copy/&gt; are now able to process not only file 
    system resources but also zip entries, tar entries, paths, ... 
    Resource collections group resources, and can be further 
    combined with operators such as union and intersection. This 
    can be extended by custom resources and custom tasks using resources.</p>
                                <p>
    Ant 1.7 starts outsourcing of optional tasks to Antlibs. 
    The .NET antlib in preparation will replace the .NET optional tasks which ship in Ant.
    Support for the version control system Subversion will be only provided as an antlib to
    be released shortly. 
    </p>
                                <p>Ant 1.7 fixes also a large number of bugs.</p>
                                <p>Ant 1.7 has some initial support for Java6 features.</p>
                        <h3 class="section">
      <a name=".NET Ant Library 1.0"></a>
      .NET Ant Library 1.0
    </h3>
                        <h3>November 6, 2006 - Apache .NET Ant Library 1.0 Available</h3>
                                <p>Apache .NET Ant Library 1.0 is now available for <a href="http://ant.apache.org/antlibs/bindownload.cgi">download</a>.</p>
                                <p>This Ant Library contains support for tools like NUnit as well
    as the "old .NET tasks of Ant's core.  It has been tested
    Microsoft's frameworks as well as Mono.</p>
                                <p>For more information see the <a href="antlibs/dotnet/">Antlib's
    home page</a></p>
                        <h3 class="section">
      <a name="AntUnit 1.0Beta2"></a>
      AntUnit 1.0Beta2
    </h3>
                        <h3>October 29, 2006 - Apache AntUnit 1.0Beta2 Available</h3>
                                <p>Apache AntUnit 1.0Beta1 is now available for <a href="http://ant.apache.org/antlibs/bindownload.cgi">download</a>.</p>
                                <p>This Ant Library contains tasks to test Ant tasks using Ant
    instead of JUnit.  For more information see the <a href="antlibs/antunit/">AntUnit home page</a>.</p>
                        <h3 class="section">
      <a name="Apache Ant"></a>
      Apache Ant
    </h3>
                        <p>
Apache Ant is a Java-based build tool. In theory, it is kind of like
Make, but without Make's wrinkles.
</p>
                                <p>
Why another build tool when there is already <em>make</em>, <em>gnumake</em>,
<em>nmake</em>, <em>jam</em>, and
others? Because all those tools have limitations that Ant's original author
couldn't live with when developing software across multiple platforms. Make-like
tools are inherently shell-based -- they evaluate a set of dependencies, then
execute commands not unlike what you would issue in a shell. This means that you
can easily extend these tools by using or writing any program for the OS that
you are working on. However, this also means that you limit yourself to the OS,
or at least the OS type such as Unix, that you are working on.
</p>
                                <p>
Makefiles are inherently evil as well. Anybody who has worked on them for any
time has run into the dreaded tab problem. "Is my command not executing
because I have a space in front of my tab!!!" said the original author of
Ant way too many times. Tools like Jam took care of this to a great degree, but
still have yet another format to use and remember.
</p>
                                <p>
Ant is different. Instead of a model where it is extended with shell-based
commands, Ant is extended using Java classes. Instead of writing shell commands,
the configuration files are XML-based, calling out a target tree where various
tasks get executed. Each task is run by an object that implements a particular
Task interface.
</p>
                                <p>
Granted, this removes some of the expressive power that is inherent by being
able to construct a shell command such as
<code>`find . -name foo -exec rm {}`</code>, but it
gives you the ability to be cross platform -- to work anywhere and everywhere.
And hey, if you really need to execute a shell command, Ant has an
<code>&lt;exec&gt;</code> task that
allows different commands to be executed based on the OS that it is executing
on.
</p>
                        <h3 class="section">
      <a name="Documentation"></a>
      Documentation
    </h3>
                        <p>
You can view the documentation for the current release (Apache Ant 1.6.5)
<a href="manual/index.html">online</a>
</p>
                                <p>
Comprehensive documentation is included in the source and binary distributions.
</p>
                        <h3 class="section">
      <a name="Get Involved"></a>
      Get Involved
    </h3>
                        <ul>
<li><a href="http://jakarta.apache.org/getinvolved/getinvolvedindex.html">Get Involved</a></li>
<li><a href="mail.html">Join Mailing Lists</a></li>
<li><a href="http://marc.theaimsgroup.com/?l=ant-dev&amp;r=1&amp;w=2">Search the Dev Mailing List</a>
</li>
<li><a href="http://marc.theaimsgroup.com/?l=ant-Employee&amp;r=1&amp;w=2">Search the User Mailing List</a>
</li>
</ul>
                
    </div>
  </div>

        <p class="copyright">
        <script type="text/javascript" language="JavaScript"><!--
                document.write(" - "+"Last Published: " + document.lastModified);
              //  -->
        </script>
      </p>
    </body>
  </html>











