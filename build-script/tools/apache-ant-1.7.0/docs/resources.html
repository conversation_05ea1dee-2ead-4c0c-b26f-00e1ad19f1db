

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
        <html lang="en">
    <!-- GENERATED FILE, DO NOT EDIT, EDIT THE XML FILE IN xdocs INSTEAD! -->
    <head>
      <META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
        <title>Apache Ant - Resources</title>
        <link type="text/css" href="./page.css" rel="stylesheet">
          <meta name="author" content="<PERSON>">
  <meta name="email" content="<EMAIL>">
      </head>

    <body>
      <p class="navpath">
        <script src="./breadcrumbs.js" language="JavaScript" type="text/javascript"></script>
      </p>

      <div class="logobar">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td align="left"><img border="0" alt="Apache Ant site" src="./images/group-logo.gif"></td>
            <td align="center" width="100%"><img alt="Apache Ant logo" border="0" src="./images/project-logo.gif"></td>
            <td align="right">
              <form target="_blank" onsubmit="q.value = query.value + ' site:ant.apache.org'" action="http://www.google.com/search" method="get">
                <table summary="search" border="0" cellspacing="0" cellpadding="0" bgcolor="#4C6C8F">
                  <tr>
                    <td colspan="3"><img height="10" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td nowrap="nowrap" class="searchcaption">
                      <input name="q" type="hidden">
                      <input size="15" id="query" type="text">
                      <img height="1" width="5" alt="" src="./images/spacer.gif">
                      <input name="Search" value="Search" type="submit">
                      <br>
                      the Apache Ant site
                    </td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                  </tr>
                  <tr>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-left.gif"></td>
                    <td><img height="1" width="1" alt="" src="./images/spacer.gif"></td>
                    <td><img alt="" border="0" height="10" width="9" src="./images/search-right.gif"></td>
                  </tr>
                </table>
              </form>
            </td>
          </tr>
        </table>
      </div>

                  <div class="tab">
              <table summary="tab bar" border="0" cellpadding="0" cellspacing="0">
                <tr>
                                  <td width="8"><img alt="" height="5" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="selected tab" style="height: 1.5em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                        <td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-left.gif"></td><td valign="middle" bgcolor="#4C6C8F"><font color="#ffffff" size="2" face="Arial, Helvetica, Sans-serif"><b>Home</b></font></td><td valign="top" width="5" bgcolor="#4C6C8F"><img height="5" width="5" alt="" src="./images/tabSel-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                                    <td width="5"><img alt="" height="8" width="8" src="./images/spacer.gif"></td><td valign="bottom">
                      <table summary="non selected tab" style="height: 1.4em" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                          <td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-left.gif"></td><td valign="middle" bgcolor="#B2C4E0"><a href="./projects/index.html"><font size="2" face="Arial, Helvetica, Sans-serif">Projects</font></a></td><td valign="top" width="5" bgcolor="#B2C4E0"><img height="5" width="5" alt="" src="./images/tab-right.gif"></td>
                        </tr>
                      </table>
                    </td>
                            </tr>
              </table>
            </div>

      <div class="bluebar"></div>
                    
  <div class="menucontainer">

    <div class="menu">
      <ul>
              <li class="menuheader">Apache Ant
          <ul>
                            <li>
                                    <a href="./index.html">Welcome</a>
                                </li>
                            <li>
                                    <a href="./license.html">License</a>
                                </li>
                            <li>
                                    <a href="./antnews.html">News</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Documentation
          <ul>
                            <li>
                                    <a href="./manual/index.html">Manual</a>
                                </li>
                            <li>
                                    <a href="./projects.html">Related Projects</a>
                                </li>
                            <li>
                                    <a href="./external.html">External Tools and Tasks</a>
                                </li>
                            <li>
                              <span class="sel">Resources</span>
                              </li>
                            <li>
                                    <a href="./faq.html">Frequently Asked Questions</a>
                                </li>
                            <li>
                                    <a href="http://wiki.apache.org/ant/FrontPage">Wiki</a>
                                </li>
                            <li>
                                    <a href="./problems.html">Having Problems?</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Download
          <ul>
                            <li>
                                    <a href="http://ant.apache.org/bindownload.cgi">Binary Distributions</a>
                                </li>
                            <li>
                                    <a href="http://ant.apache.org/srcdownload.cgi">Source Distributions</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Contributing
          <ul>
                            <li>
                                    <a href="./mail.html">Mailing Lists</a>
                                </li>
                            <li>
                                    <a href="./svn.html">Subversion Repositories</a>
                                </li>
                            <li>
                                    <a href="./nightlies.html">Nightly Builds</a>
                                </li>
                            <li>
                                    <a href="./bugs.html">Bug Database</a>
                                </li>
                            <li>
                                    <a href="http://www.apache.org/foundation/contributing.html">Donations</a>
                                </li>
                      </ul>
        </li>
              <li class="menuheader">Project Management
          <ul>
                            <li>
                                    <a href="./contributors.html">Contributors</a>
                                </li>
                            <li>
                                    <a href="./mission.html">Apache Ant Mission</a>
                                </li>
                            <li>
                                    <a href="./bylaws.html">Project Bylaws</a>
                                </li>
                            <li>
                                    <a href="./legal.html">Legal</a>
                                </li>
                      </ul>
        </li>
            </ul>
    </div>
    <img style="float: left" height="10" width="10" border="0" alt="" src="./images/menu-left.gif">
    <img style="float: right" height="10" width="10" border="0" alt="" src="./images/menu-right.gif">
  </div>
      <div class="lightbluebar">&nbsp;</div>
  <div class="main">
  <div class="content">
    <h1 class="title">Resources</h1>
            <h3 class="section">
      <a name="FAQs"></a>
      FAQs
    </h3>
                              <h4 class="subsection">
        <a name="At Ant's website"></a>
        At Ant's website
      </h4>
                        <p>Starting with the release of Ant 1.4 the Ant's FAQ is
        bundled with the distribution, the most recent version can
        always be found at the website.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          FAQ:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="faq.html">http://ant.apache.org/faq.html</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="jGuru"></a>
        jGuru
      </h4>
                        <p>jGuru hosts an interactive Ant discussion forum and FAQ system</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Forum:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.jguru.com/forums/home.jsp?topic=Ant">http://www.jguru.com/forums/home.jsp?topic=Ant</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          FAQ:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.jguru.com/faq/home.jsp?topic=Ant">http://www.jguru.com/faq/home.jsp?topic=Ant</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="FAQ about Borland Application Server tasks"></a>
        FAQ about Borland Application Server tasks
      </h4>
                        <p>Benoit Moussaud, the original author of the Borland
        Application Server specific <a href="manual/OptionalTasks/ejb.html#ejbtasks">EJB tasks</a> has put
        together a FAQ for this specific subtask.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          FAQ:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.moussaud.org/ejbjar.html">http://www.moussaud.org/ejbjar.html</a>
      </td>
      </tr>
          </table>
                                            <h3 class="section">
      <a name="WIKIs"></a>
      WIKIs
    </h3>
                              <h4 class="subsection">
        <a name="Apache"></a>
        Apache
      </h4>
                        <p>The ASF provides a Wiki farm for Apache projects.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Main page:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://wiki.apache.org/general">Apache Wiki Farm</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Ant Wiki:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://wiki.apache.org/ant/">Ant Wiki</a>
      </td>
      </tr>
          </table>
                                            <h3 class="section">
      <a name="Books"></a>
      Books
    </h3>
                        <p>The most recent books come first</p>
                                      <h4 class="subsection">
        <a name="Ant in Action"></a>
        Ant in Action
      </h4>
                        <p>Published April/May 2007, and covering Ant 1.7.</p>
                                <p>This is a major rewrite of the first edition; still 600 pages long.
        </p>
                                <p>
          This book moves up from Ant1.5 to Java1.5 and 1.7, with a near-complete
          rewrite of the applied-ant section, covering new topics such as
          antlibs, repository management with Ivy, Xml Schema validation,
          EJB3.0/Java EE development and advanced deployment using SmartFrog.
          The ant coding section looks at AntUnit, antlib authoring and
          scripting languages, while the beginners chapters, the first third
          of the book, still shows developers how to build, test, package and
          redistribute a Java application.
        </p>
                                <p>
          If you are one of the 20,000+ owners of the first edition, it is now
          obsolete. Sorry :)
        </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Steve Loughran and Erik Hatcher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Publisher URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://www.manning.com/loughran/">
                http://www.manning.com/loughran/
              </a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Book URL
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://antbook.org/">
                http://antbook.org/
              </a>
            
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Source code repository
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://sourceforge.net/projects/antbook">
                http://sourceforge.net/projects/antbook
              </a>
            
      </td>
      </tr>
            <tr><th>ISBN:</th><td>1-932394-80-X</td></tr>
    </table>
                            <p><b>Available from:</b><br>
    <a href="http://www.amazon.com/exec/obidos/tg/detail/-/1-932394-80-X/apachesoftwar-20/" target="_blank">Amazon.com</a>
  | <a href="http://service.bfast.com/bfast/click?bfmid=2181&amp;bfmtype=book&amp;sourceid=41462544&amp;bfpid=1-932394-80-X" target="_blank">Barnes &amp; Noble</a>
  | <a href="http://www.booksense.com/product/info.jsp?affiliateId=Apache&amp;isbn=1-932394-80-X" target="_blank">Book Sense</a>
  | <a href="http://www.powells.com/cgi-bin/biblio?isbn=1-932394-80-X&amp;partner_id=29693" target="_blank">Powells.com</a>
<br></p>
                                            <h4 class="subsection">
        <a name="Ant: The Definitive Guide, 2nd edition"></a>
        Ant: The Definitive Guide, 2nd edition
      </h4>
                        <p>Published April 2005, and covers Ant release 1.6.1.</p>
                                <p>This is a complete rewrite of the first edition; this book is
        now 290 pages and so covers Ant in more depth than its predecessor.
        </p>
                                <p>It also mixes reference information (tables) with text explanation
        on how to use the tasks. Contents includes JUnit, CVS, execution, basic
        deployment, Web application development and XDoclet. There is also coverage
        of XDoclet, and a chapter on how to extend Ant in Java.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Steve Holzner
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://www.oreilly.com/catalog/anttdg2/">http://www.oreilly.com/catalog/anttdg2/</a>
            
      </td>
      </tr>
            <tr><th>ISBN:</th><td>0596006098</td></tr>
    </table>
                            <p><b>Available from:</b><br>
    <a href="http://www.amazon.com/exec/obidos/tg/detail/-/0596006098/apachesoftwar-20/" target="_blank">Amazon.com</a>
  | <a href="http://service.bfast.com/bfast/click?bfmid=2181&amp;bfmtype=book&amp;sourceid=41462544&amp;bfpid=0596006098" target="_blank">Barnes &amp; Noble</a>
  | <a href="http://www.booksense.com/product/info.jsp?affiliateId=Apache&amp;isbn=0596006098" target="_blank">Book Sense</a>
  | <a href="http://www.powells.com/cgi-bin/biblio?isbn=0596006098&amp;partner_id=29693" target="_blank">Powells.com</a>
<br></p>
                                            <h4 class="subsection">
        <a name="Pragmatic Project Automation"></a>
        Pragmatic Project Automation
      </h4>
                        <p>

        How to Build, Deploy, and Monitor Java Applications.
        Published: July 2004 ISBN:        0-9745140-3-9
        </p>
                                <p>
          This is not a reference guide to Ant, but a book on how to automate the build process.
          The core build, continuous integration, reporting and release management
          are all covered. Ant is of course central to this. This is a fun read!
        </p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Mike Clark
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          
              <a href="http://www.pragmaticprogrammer.com/sk/auto/">http://www.pragmaticprogrammer.com/sk/auto//</a>
            
      </td>
      </tr>
            <tr><th>ISBN:</th><td>0974514039</td></tr>
    </table>
                            <p><b>Available from:</b><br>
    <a href="http://www.amazon.com/exec/obidos/tg/detail/-/0974514039/apachesoftwar-20/" target="_blank">Amazon.com</a>
  | <a href="http://service.bfast.com/bfast/click?bfmid=2181&amp;bfmtype=book&amp;sourceid=41462544&amp;bfpid=0974514039" target="_blank">Barnes &amp; Noble</a>
  | <a href="http://www.booksense.com/product/info.jsp?affiliateId=Apache&amp;isbn=0974514039" target="_blank">Book Sense</a>
  | <a href="http://www.powells.com/cgi-bin/biblio?isbn=0974514039&amp;partner_id=29693" target="_blank">Powells.com</a>
<br></p>
                                            <h4 class="subsection">
        <a name="Extreme Programming with Ant"></a>
        Extreme Programming with Ant
      </h4>
                        <p> This book shows how to implement an XP project using Ant 1.5.3, and many other 3rd party tools.  Covers:</p>
                                <ul>
               <li>The fundamentals of Ant: concepts, core and optional tasks</li>
               <li>How to write custom Ant components, including custom Tasks, Loggers, Listeners, Input Handlers, Selectors, Filters, Mappers and Data Types</li>
               <li>Mitigating risks by creating spike tests with Ant buildfiles</li>
               <li>Add CVS version control and testing with JUnit</li>
               <li>Automate nightly builds and reporting</li>
               <li>Deploy applications dynamically using XDoclet</li>
               <li>Enforcing Code Standards with Jalopy, PMD, CheckStyle, iContract, JDepend</li>
               <li>Using Remote Ant (Rant) and CruiseControl</li>
               <li>Generating project documentation</li>
               <li>Adapting an XP process for use by other teams or across an enterprise</li>
               <li>Custom Task examples to generating UML diagrams, creating reports and metrics on-the-fly</li>
               <li>Follows a case-study of a team that implements an XP Project</li>
            </ul>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Authors: <AUTHORS>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Glenn Niemeyer and Jeremy Poteet
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.sams.com/catalog/product.asp?product_id=%7BFB825A48-BC04-4C55-BD8C-DF93C6BBF920%7D">http://www.sams.com/catalog/product.asp?product_id=%7BFB825A48-BC04-4C55-BD8C-DF93C6BBF920%7D</a>
      </td>
      </tr>
            <tr><th>ISBN:</th><td>0672325624</td></tr>
    </table>
                            <p><b>Available from:</b><br>
    <a href="http://www.amazon.com/exec/obidos/tg/detail/-/0672325624/apachesoftwar-20/" target="_blank">Amazon.com</a>
  | <a href="http://service.bfast.com/bfast/click?bfmid=2181&amp;bfmtype=book&amp;sourceid=41462544&amp;bfpid=0672325624" target="_blank">Barnes &amp; Noble</a>
  | <a href="http://www.booksense.com/product/info.jsp?affiliateId=Apache&amp;isbn=0672325624" target="_blank">Book Sense</a>
  | <a href="http://www.powells.com/cgi-bin/biblio?isbn=0672325624&amp;partner_id=29693" target="_blank">Powells.com</a>
<br></p>
                                            <h4 class="subsection">
        <a name="Ant. Das Java-Build-Tool in der Praxis"></a>
        Ant. Das Java-Build-Tool in der Praxis
      </h4>
                        <p>A German language book on Ant that covers Ant 1.5.
        This is the original description:</p>
                                <pre class="code">
        Das Build-Tool Ant ist das Open-Source-Werkzeug, das den Entwicklungsprozess einer Java-
        oder J2EE-Anwendung wesentlich vereinfacht. Gesteuert durch XML-basierte Skripte fï¿½hrt es
        nahezu alle Aufgaben aus, die nach dem Kodieren einer Anwendung anfallen.</pre>
                                <p>Some topics:</p>
                                <ul>
                <li>creating archives (zip, jar)</li>
                <li>call the java compiler</li>
                <li>edit property files</li>
                <li>file operation</li>
                <li>source code control systems</li>
            </ul>
                                <p>The book is available in English as "Ant: The Java Build Tool in Practice"</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Authors: <AUTHORS>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Bernd Matzke
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.addison-wesley.de/main/main.asp?page=home/bookdetails&amp;ProductID=13459">http://www.addison-wesley.de/main/main.asp?page=home/bookdetails&amp;ProductID=13459</a>
      </td>
      </tr>
            <tr><th>ISBN:</th><td>3827320666</td></tr>
    </table>
                            <p><b>Available from:</b><br>
    <a href="http://www.amazon.com/exec/obidos/tg/detail/-/3827320666/apachesoftwar-20/" target="_blank">Amazon.com</a>
  | <a href="http://service.bfast.com/bfast/click?bfmid=2181&amp;bfmtype=book&amp;sourceid=41462544&amp;bfpid=3827320666" target="_blank">Barnes &amp; Noble</a>
  | <a href="http://www.booksense.com/product/info.jsp?affiliateId=Apache&amp;isbn=3827320666" target="_blank">Book Sense</a>
  | <a href="http://www.powells.com/cgi-bin/biblio?isbn=3827320666&amp;partner_id=29693" target="_blank">Powells.com</a>
<br></p>
                                            <h4 class="subsection">
        <a name="Java Development with Ant"></a>
        Java Development with Ant
      </h4>
                        <p>Published 2002. This book covers Ant 1.5, including:</p>
                                <ul>
                <li>The new Ant 1.5 features</li>
                <li>Ant's datatypes and property handling</li>
                <li>JUnit testing and reporting</li>
                <li>Continuous integration techniques</li>
                <li>XDoclet for attribute-oriented programming</li>
                <li>EJB generation, building, and packaging</li>
                <li>Writing and testing native code</li>
                <li>Building Web Services with Apache Axis</li>
                <li>Deploying your system to multiple remote servers</li>
                <li>Using and writing
                    <ul>
                        <li>Loggers</li>
                        <li>Listeners</li>
                        <li>Selectors</li>
                        <li>Custom tasks</li>
                    </ul>
                </li>
            </ul>
                                <p>Also available in Korean and German editions</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Authors: <AUTHORS>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Erik Hatcher and Steve Loughran
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.manning.com/antbook/">http://www.manning.com/antbook/</a>
      </td>
      </tr>
            <tr><th>ISBN:</th><td>1930110588</td></tr>
    </table>
                            <p><b>Available from:</b><br>
    <a href="http://www.amazon.com/exec/obidos/tg/detail/-/1930110588/apachesoftwar-20/" target="_blank">Amazon.com</a>
  | <a href="http://service.bfast.com/bfast/click?bfmid=2181&amp;bfmtype=book&amp;sourceid=41462544&amp;bfpid=1930110588" target="_blank">Barnes &amp; Noble</a>
  | <a href="http://www.booksense.com/product/info.jsp?affiliateId=Apache&amp;isbn=1930110588" target="_blank">Book Sense</a>
  | <a href="http://www.powells.com/cgi-bin/biblio?isbn=1930110588&amp;partner_id=29693" target="_blank">Powells.com</a>
<br></p>
                                            <h4 class="subsection">
        <a name="Ant: The Definitive Guide, 1st edition"></a>
        Ant: The Definitive Guide, 1st edition
      </h4>
                        <p>Published 2002, Covers Ant release 1.4.1.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Authors: <AUTHORS>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Jesse E. Tilly and Eric M. Burke
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.oreilly.com/catalog/anttdg/">http://www.oreilly.com/catalog/anttdg/</a>
      </td>
      </tr>
            <tr><th>ISBN:</th><td>0596001843</td></tr>
    </table>
                            <p><b>Available from:</b><br>
    <a href="http://www.amazon.com/exec/obidos/tg/detail/-/0596001843/apachesoftwar-20/" target="_blank">Amazon.com</a>
  | <a href="http://service.bfast.com/bfast/click?bfmid=2181&amp;bfmtype=book&amp;sourceid=41462544&amp;bfpid=0596001843" target="_blank">Barnes &amp; Noble</a>
  | <a href="http://www.booksense.com/product/info.jsp?affiliateId=Apache&amp;isbn=0596001843" target="_blank">Book Sense</a>
  | <a href="http://www.powells.com/cgi-bin/biblio?isbn=0596001843&amp;partner_id=29693" target="_blank">Powells.com</a>
<br></p>
                                            <h4 class="subsection">
        <a name="Ant. Kurz und Gut."></a>
        Ant. Kurz und Gut.
      </h4>
                        <p>A German language short reference for Ant that covers Ant
        1.4.  This is the original description:</p>
                                <pre class="code">
  Ant kurz &amp; gut enthält eine vollständige Referenz der Built-in Tasks
  und ihrer jeweiligen Attribute sowie kurze Beispiele für ihre Verwendung.
  Daneben bietet das Buch eine knappe Einführung in die Arbeit mit Ant und
  eine Erläuterung der Ant-Basiselemente (Projekte, Properties, Targets und Tasks).
  Behandelt werden außerdem grundlegende Konzepte wie Filesets, Patternsets und
  Pfadstrukturen, das Schreiben eigener Tasks, die Aufruf-Syntax und Optional Tasks. </pre>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Stefan Edlich
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.amazon.de/exec/obidos/ASIN/3897212412/">http://www.amazon.de/exec/obidos/ASIN/3897212412/</a>
      </td>
      </tr>
            <tr><th>ISBN:</th><td>3897212412</td></tr>
    </table>
                            <p><b>Available from:</b><br>
    <a href="http://www.amazon.com/exec/obidos/tg/detail/-/3897212412/apachesoftwar-20/" target="_blank">Amazon.com</a>
  | <a href="http://service.bfast.com/bfast/click?bfmid=2181&amp;bfmtype=book&amp;sourceid=41462544&amp;bfpid=3897212412" target="_blank">Barnes &amp; Noble</a>
  | <a href="http://www.booksense.com/product/info.jsp?affiliateId=Apache&amp;isbn=3897212412" target="_blank">Book Sense</a>
  | <a href="http://www.powells.com/cgi-bin/biblio?isbn=3897212412&amp;partner_id=29693" target="_blank">Powells.com</a>
<br></p>
                                            <h4 class="subsection">
        <a name="Java Tools for eXtreme Programming"></a>
        Java Tools for eXtreme Programming
      </h4>
                        <p>This book covers the following XP subjects:</p>
                                <ul>
                <li>Automated unit and functional testing</li>
                <li>Continuous integration through build and deployment automation</li>
                <li>The value of refactoring and continuous integration</li>
                <li>How Ant, JUnit, JUnitPerf, Cactus, HTTPUnit, and JMeter
                can be used to achieve the goals of the XP methodology</li>
            </ul>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Authors: <AUTHORS>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Richard Hightower and Nicholas Lesiecki
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.wiley.com/cda/product/0,,047120708X,00.html">http://www.wiley.com/cda/product/0,,047120708X,00.html</a>
      </td>
      </tr>
            <tr><th>ISBN:</th><td>047120708X</td></tr>
    </table>
                            <p><b>Available from:</b><br>
    <a href="http://www.amazon.com/exec/obidos/tg/detail/-/047120708X/apachesoftwar-20/" target="_blank">Amazon.com</a>
  | <a href="http://service.bfast.com/bfast/click?bfmid=2181&amp;bfmtype=book&amp;sourceid=41462544&amp;bfpid=047120708X" target="_blank">Barnes &amp; Noble</a>
  | <a href="http://www.booksense.com/product/info.jsp?affiliateId=Apache&amp;isbn=047120708X" target="_blank">Book Sense</a>
  | <a href="http://www.powells.com/cgi-bin/biblio?isbn=047120708X&amp;partner_id=29693" target="_blank">Powells.com</a>
<br></p>
                              <h3 class="section">
      <a name="Articles and Presentations"></a>
      Articles and Presentations
    </h3>
                        <p>The following sections list articles and presentations
      written about Apache Ant.  If you've written something that
      should be included, please post it to one of the mailing
      lists.</p>
                        <h3 class="section">
      <a name="Articles"></a>
      Articles
    </h3>
                              <h4 class="subsection">
        <a name="Extending Ant Input Abilities"></a>
        Extending Ant Input Abilities
      </h4>
                        <p>The contents of this document is the following:<ul>
           <li>Section 2 provides a simple example how InputHandlers are created,</li>
           <li>Section 3 develops an inputhandler that masks the passwords typed on the command line,</li>
           <li>Section 4 gives two handlers, whose input is typed in graphical components,</li>
           <li>Section 5 extends Input task so that we can use dierent input handlers on different uses of &lt;input&gt;,</li>
           <li>Section 6 describes a problem found while writing this document,</li>
           <li>Section 7 summarizes some dark corners the author do not understand.</li>
         </ul></p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Ivan Ivanov
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="https://sourceforge.net/project/showfiles.php?group_id=103509">https://sourceforge.net/project/showfiles.php?group_id=103509 (Download ZIP+PDF from Sourceforge)</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Keep the Ant, Hold the XML"></a>
        Keep the Ant, Hold the XML
      </h4>
                        <p>Key G. Gauthier talks about writing "buildfiles" in Java.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Key G. Gauthier
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.ftponline.com/javapro/2004_06/magazine/features/kgauthier/">http://www.ftponline.com/javapro/2004_06/magazine/features/kgauthier/</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Ant 1.6 for Task Writers"></a>
        Ant 1.6 for Task Writers
      </h4>
                        <p>This article talks about XML namespace handling, Ant
        libraries and the newly introduced type polymorphism.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Stefan Bodewig
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://otn.oracle.com/pub/articles/bodewig_taskwriters.html">http://otn.oracle.com/pub/articles/bodewig_taskwriters.html</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Managing Build Complexity with Apache Ant 1.6"></a>
        Managing Build Complexity with Apache Ant 1.6
      </h4>
                        <p>As Apache Ant is applied to increasingly difficult tasks,
        its users are creating more complex and less legible build
        files. This is due, in part, to the limited tools for
        decomposition and code reuse within previous versions of
        Ant.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Geoffrey Wiseman
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.sys-con.com/story/?storyid=45078&amp;DE=1">http://www.sys-con.com/story/?storyid=45078&amp;DE=1</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="New Ant 1.6 Features for Big Projects"></a>
        New Ant 1.6 Features for Big Projects
      </h4>
                        <p>This article describes the &lt;macrodef&gt;, &lt;import&gt;
          and &lt;subant&gt; tasks in detail and shows how they help in
          building bigger systems.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Stefan Bodewig
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://otn.oracle.com/pub/articles/bodewig_ant1.6.html">http://otn.oracle.com/pub/articles/bodewig_ant1.6.html</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Programmieren für Ant"></a>
        Programmieren für Ant
      </h4>
                        <p>This article describes the main topics of programming your own tasks.
        Description is done on five examples.</p>
                                <p>This article is written in German and published in
        <a href="http://www.sigs-datacom.de/sd/publications/js/index.htm">Java-Spektrum</a>
        5/2004.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Bernd Matzke
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.sigs-datacom.de/sd/news/document?PID=216">http://www.sigs-datacom.de/sd/news/document?PID=216</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Ant in Anger: Using Ant in a Production Development System"></a>
        Ant in Anger: Using Ant in a Production Development System
      </h4>
                        <p>This document describes strategies and some basic examples of how to
        use Ant in larger team development projects.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Steve Loughran
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="ant_in_anger.html">http://ant.apache.org/ant_in_anger.html</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Ant Task Guidelines"></a>
        Ant Task Guidelines
      </h4>
                        <p>This document describes how to write custom Ant tasks, and how to submit
        them to potentially be included in Ant.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Steve Loughran
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="ant_task_guidelines.html">http://ant.apache.org/ant_task_guidelines.html</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Build a Better Robot with Ant"></a>
        Build a Better Robot with Ant
      </h4>
                        <p>This article describes the gory details of writing custom
           Ant tasks.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Erik Hatcher
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.fawcette.com/javapro/2003_02/magazine/features/ehatcher/">http://www.fawcette.com/javapro/2003_02/magazine/features/ehatcher/l</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Automating the build and test process"></a>
        Automating the build and test process
      </h4>
                        <p>This article demonstrates an approach to the automated build and test process. Working with Ant 1.3 and the JUnit test framework, it shows how to automate a process that captures pertinent information about each test suite run, generates an attractive report, and e-mails the report.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Erik Hatcher</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.ibm.com/developerworks/java/library/j-junitmail/">http://www.ibm.com/developerworks/java/library/j-junitmail/</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Incremental development with Ant and JUnit"></a>
        Incremental development with Ant and JUnit
      </h4>
                        <p>This article explores the benefits of unit testing with Ant and
        JUnit, detailing how to develop automated unit tests and integrate them
        into your build process.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Malcolm Davis</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www-106.ibm.com/developerworks/library/j-ant/?dwzone=java">http://www-106.ibm.com/developerworks/library/j-ant/?dwzone=java</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Automate your build process using Java and Ant"></a>
        Automate your build process using Java and Ant
      </h4>
                        <p>This article provides an introduction to using Ant with some basic
        examples and by highlighting some of the important tasks.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Michael Cymerman</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.javaworld.com/javaworld/jw-10-2000/jw-1020-ant.html">http://www.javaworld.com/javaworld/jw-10-2000/jw-1020-ant.html</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Short tutorial in Cactus' (formerly J2EEUnit)       documentation"></a>
        Short tutorial in Cactus' (formerly J2EEUnit)       documentation
      </h4>
                        <p>There is a short tutorial on how to use Ant in Cactus'
        documentation.  It has a slant towards build files that will be used
        with Cactus.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Cactus development team
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://jakarta.apache.org/cactus/howto_ant_primer.html">http://jakarta.apache.org/cactus/howto_ant_primer.html</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Developing custom Ant tasks with VisualAge for Java"></a>
        Developing custom Ant tasks with VisualAge for Java
      </h4>
                        <p>This article outlines how to integrate Ant into VisualAge for Java,
        and how to write and debug custom tasks using the IDE and the
        integrated debugger.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Glenn McAllister</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www7.software.ibm.com/vad.nsf/data/document2366?OpenDocument&amp;p=1&amp;BCT=1&amp;Footer=1">http://www7.software.ibm.com/vad.nsf/data/document2366?OpenDocument&amp;p=1&amp;BCT=1&amp;Footer=1</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Automated builds with VisualAge for Java and Ant"></a>
        Automated builds with VisualAge for Java and Ant
      </h4>
                        <p>This article shows how you can perform command line builds with a
        VisualAge for Java repository.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Glenn McAllister</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www7.software.ibm.com/vad.nsf/Data/Document4366?OpenDocument&amp;p=1&amp;BCT=3&amp;Footer=1">http://www7.software.ibm.com/vad.nsf/Data/Document4366?OpenDocument&amp;p=1&amp;BCT=3&amp;Footer=1</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Ant: A Build Tool from the Jakarta Project"></a>
        Ant: A Build Tool from the Jakarta Project
      </h4>
                        <p>This article is from the "Best Practices" section of
        Sun's Dot-Com Builder Site.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Laura Geele Wang
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://dcb.sun.com/practices/profiles/ant.jsp">http://dcb.sun.com/practices/profiles/ant.jsp</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Making a Mountain Out of an Anthill"></a>
        Making a Mountain Out of an Anthill
      </h4>
                        <p>This article is from the June 2001 issue of the Java Developer'
        Journal.  You need to be a registered JDJ subscriber to view this
        article.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Neal Ford
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.sys-con.com/java/archivesa.cfm?volume=06&amp;issue=06">http://www.sys-con.com/java/archivesa.cfm?volume=06&amp;issue=06</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Using Ant and Weblogic EJBs"></a>
        Using Ant and Weblogic EJBs
      </h4>
                        <p>This article describes how to use Ant to create Weblogic EJBs, and
        some workarounds for issues you may encounter.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Jesse E. Tilly
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.onjava.com/pub/a/onjava/2001/06/25/antejb.html">http://www.onjava.com/pub/a/onjava/2001/06/25/antejb.html</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Using JavaScript with Ant"></a>
        Using JavaScript with Ant
      </h4>
                        <p>A tutorial about using JavaScript and XSLT with Ant.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Dylan Schiemann
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.sitepen.com/ant/javascript.html">http://www.sitepen.com/ant/javascript.html</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Building with Ant"></a>
        Building with Ant
      </h4>
                        <p>Series of articles that describe a framework for web
        application development based on Ant and JUnit.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Alex Chaffee</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Introduction: <a href="http://softwaredev.earthweb.com/sdtech/article/0,,12065_989631,00.html">http://softwaredev.earthweb.com/sdtech/article/0,,12065_989631,00.html</a><br />
                Directory Structure: <a href="http://softwaredev.earthweb.com/sdtech/article/0,,12082_994991,00.html">http://softwaredev.earthweb.com/sdtech/article/0,,12082_994991,00.html</a><br />
                Deployment and Distribution: <a href="http://softwaredev.earthweb.com/sdtech/article/0,,12077_998241,00.html">http://softwaredev.earthweb.com/sdtech/article/0,,12077_998241,00.html</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Developing with JAXB and Ant"></a>
        Developing with JAXB and Ant
      </h4>
                        <p>Series of articles that shows how to use Ant together with
        the Java API for XML Binding (JAXB).</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Joseph Shelby
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.onjava.com/pub/a/onjava/2002/03/06/jaxant1.html">http://www.onjava.com/pub/a/onjava/2002/03/06/jaxant1.html</a><br />
                <a href="http://www.onjava.com/pub/a/onjava/2002/03/13/jaxbant2.html">http://www.onjava.com/pub/a/onjava/2002/03/13/jaxbant2.html</a>
      </td>
      </tr>
          </table>
                                            <h3 class="section">
      <a name="Presentations"></a>
      Presentations
    </h3>
                              <h4 class="subsection">
        <a name="Ant Build Tool"></a>
        Ant Build Tool
      </h4>
                        <p>A PowerPoint presentation on Ant 1.2.  It provides a basic overview
        of Ant's capabilities.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="mailto:<EMAIL>">Patrick Chanezon</a>
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://people.netscape.com/chanezon/tech/ant/ant_preso.ppt">http://people.netscape.com/chanezon/tech/ant/ant_preso.ppt</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Ant"></a>
        Ant
      </h4>
                        <p>A detailed Ant 1.3 PowerPoint presentation, made at the St. Louis Java Users Group
        meeting in March 2001.  Includes a detailed build file and basic
        descriptions of all the built in and optional tasks.  Updated for Ant 1.4 in October 2001.
        Available in PDF format now.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Mark Volkmann
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.ociweb.com/jnb/files/Ant.pdf">http://www.ociweb.com/jnb/files/Ant.pdf</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="Modern Development Crises"></a>
        Modern Development Crises
      </h4>
                        <p>This presentation is an overview of the state of software
        development in 2001.  There are a couple of slides that briefly cover
        Ant.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Steve Loughran
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.iseran.com/Steve/modern_development_processes.html">http://www.iseran.com/Steve/modern_development_processes.html</a>
      </td>
      </tr>
          </table>
                                                          <h4 class="subsection">
        <a name="When Web Services Go Bad"></a>
        When Web Services Go Bad
      </h4>
                        <p>A presentation from the <a href="http://www.sellsbrothers.com/conference/">Web Services
        DevCon</a> in March 2002.</p>
                                      <table class="externals" cellspacing="1" cellpadding="4">
              <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          Author:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          Steve Loughran
      </td>
      </tr>
                  <tr>
                      <th colspan="1" rowspan="1"
      valign="top" align="left">
          URL:
      </th>
                          <td colspan="1" rowspan="1"
      valign="top" align="left">
          <a href="http://www.iseran.com/Steve/papers/when_web_services_go_bad.html">http://www.iseran.com/Steve/papers/when_web_services_go_bad.html</a>
      </td>
      </tr>
          </table>
                                    
    </div>
  </div>

        <p class="copyright">
        <script type="text/javascript" language="JavaScript"><!--
                document.write(" - "+"Last Published: " + document.lastModified);
              //  -->
        </script>
      </p>
    </body>
  </html>











