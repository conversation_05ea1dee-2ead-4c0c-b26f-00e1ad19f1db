<project name="thirdLdap" default="all" basedir=".">
	<property environment="SystemVariable" />
	<property name="git.revision" value="${SystemVariable.GIT_REVISION}" />
	<property name="libra.version" value="${git.revision}" />
	<property name="dist.dir" value="${basedir}/dist/${libra.version}" />
	<target name="all" depends="thirdLdap" />
    <target name="thirdLdap">
        <property name="to.dir" value="${dist.dir}/thirdLdap"/>
        <delete dir="${to.dir}" />
        <mkdir dir="${to.dir}" />
        <property name="thirdLdap.scripts.dir" value="${basedir}/scripts" />
        <exec executable="bash" dir="${basedir}/../../" failonerror="true" >
            <arg line="build.sh" />
        </exec>
        <copy todir="${to.dir}/tomcat/webapps">
            <fileset dir="${basedir}/../../build/libs" includes="**/ROOT.war"/>
        </copy>
        <copy todir="${to.dir}/scripts">
            <fileset dir="${thirdLdap.scripts.dir}">
            </fileset>
        </copy>
        <exec executable="bash" dir="${to.dir}/scripts">
            <arg value="-c"/>
            <arg value="chmod +x *" />
        </exec>
        <tar destfile="${to.dir}/thirdLdap.tar" basedir="${to.dir}" />
        <delete includeemptydirs="true">
            <fileset dir="${to.dir}" excludes="**/thirdLdap.tar" />
        </delete>
        <move todir="${to.dir}/thirdLdap" file="${to.dir}/thirdLdap.tar" />
        <echo file="${to.dir}/thirdLdap/version.properties" >thirdLdap.revision=${git.revision}${line.separator}</echo>
        <tar destfile="${dist.dir}/thirdLdap_${libra.version}.tgz" compression="gzip">
            <tarfileset dir="${to.dir}" includes="**/thirdLdap/"/>
        </tar>
        <delete dir="${to.dir}" />
    </target>
</project>
