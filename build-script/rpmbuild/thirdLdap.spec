Summary:   contact package
Name:      ainemo-contact
Version:   1.0.0
Release:   1
License:   Ainemo.com
Group:     System
Source:    contact.tar
Url:       http://www.ainemo.com
Packager:  Build
Prefix:    %{_prefix}
Prefix:    %{_sysconfdir}
%define    installpath /usr/libra/contact/
%define __prelink_undo_cmd      /bin/cat prelink library

%description
contact

%prep
%setup -c
%install
install -d -m 755 $RPM_BUILD_ROOT%{installpath}
cp -rf ${RPM_BUILD_ROOT}/../../BUILD/%{name}-%{version}/* $RPM_BUILD_ROOT%{installpath}
chmod +x $RPM_BUILD_ROOT%{installpath}scripts/*

%clean
rm -rf $RPM_BUILD_ROOT
rm -rf $RPM_BUILD_DIR/%{name}-%{version}

%files
%defattr(-,root,root,-)
%{installpath}

%post
rm -rf %{installpath}tomcat/webapps/ROOT
