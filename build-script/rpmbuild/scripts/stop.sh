#!/bin/bash
LIBRA_HOME=/usr/libra
SERVER_HOME=$LIBRA_HOME/thirdLdap
echo -n "Stopping thirdLdap(tomcat)..." >> $SERVER_HOME/thirdLdap.log 2>&1

export CATALINA_BASE=$SERVER_HOME/tomcat
$LIBRA_HOME/tomcat/bin/catalina.sh stop -force >> $SERVER_HOME/thirdLdap.log 2>&1

LOOP=0
while [ $LOOP -lt 100 ]
do
    usleep 100000
    pid=`ps -f -C java |grep tomcat|awk '{print $2}'`
    if [ -z "$pid" ]
    then
        echo "OK - loop $LOOP" >> $SERVER_HOME/thirdLdap.log 2>&1
        exit 0
    fi
    LOOP=`expr $LOOP + 1`
done

echo -n "fail to kill thirdLdap gracefully and force kill it" >> $SERVER_HOME/thirdLdap.log 2>&1
ps -f -C java | grep tomcat |grep thirdLdap| awk '{print $2}'| xargs kill -9  >/dev/null 2>&1
echo "OK"


