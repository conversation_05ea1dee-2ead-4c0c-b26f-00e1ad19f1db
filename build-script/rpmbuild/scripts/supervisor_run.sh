#!/bin/bash
LIBRA_HOME=/usr/libra
SERVER_HOME=$LIBRA_HOME/thirdLdap
function shutdown()
{
   echo "shuting down thirdLdap" >>/tmp/thirdLdap_trace.log
   $SERVER_HOME/scripts/stop.sh
}

ulimit -n 65535

pid=`ps -ef|grep tomcat |grep -v grep|grep thirdLdap| awk '{print $2}'`
if [ ! -z "$pid" ]; then
    echo "thirdLdap is running, will not start again".
    exit 0
fi
# initial and max memory size for the jvm
## Unit is kB
SYSMEM=`grep MemTotal /proc/meminfo | sed 's/[^0-9]*//g'`

## basically total mem retrieved is lower than n * 1024 * 1024
SERVER_1GB=1024000
SERVER_2GB_MEM_SIZE=$(( 2 * ${SERVER_1GB} ))
SERVER_3GB_MEM_SIZE=$(( 3 * ${SERVER_1GB} ))
SERVER_5GB_MEM_SIZE=$(( 5 * ${SERVER_1GB} ))
SERVER_7GB_MEM_SIZE=$(( 7 * ${SERVER_1GB} ))
SERVER_11GB_MEM_SIZE=$(( 11 * ${SERVER_1GB} ))
if [ ${SYSMEM} -ge ${SERVER_11GB_MEM_SIZE} ]; then
   echo This machine appears to have ${SYSMEM} kB.  Using production memory tuning parameters
   JVM_MIN_MEM=8192
   JVM_MAX_MEM=8192
   JVM_YOUNG_MEM=3072
elif [ ${SYSMEM} -ge ${SERVER_7GB_MEM_SIZE} ]; then
   echo This machine appears to have ${SYSMEM} kB.  Using production memory tuning parameters
   JVM_MIN_MEM=3072
   JVM_MAX_MEM=5120
   JVM_YOUNG_MEM=1536
elif [ ${SYSMEM} -ge ${SERVER_5GB_MEM_SIZE} ]; then
   echo This machine appears to have ${SYSMEM} kB.  Using production memory tuning parameters
   JVM_MIN_MEM=3072
   JVM_MAX_MEM=3072
   JVM_YOUNG_MEM=1024
elif [ ${SYSMEM} -ge ${SERVER_3GB_MEM_SIZE} ]; then
   echo This machine appears to have ${SYSMEM} kB.  Using production memory tuning parameters
   JVM_MIN_MEM=2048
   JVM_MAX_MEM=2048
   JVM_YOUNG_MEM=512
elif [ ${SYSMEM} -gt ${SERVER_2GB_MEM_SIZE} ]; then
   echo This machine appears to have ${SYSMEM} kB.  Using production memory tuning parameters
   JVM_MIN_MEM=1024
   JVM_MAX_MEM=1024
   JVM_YOUNG_MEM=384
else
   echo This machine appears to have ${SYSMEM} kB.  Using development memory tuning parameters
   JVM_MIN_MEM=512
   JVM_MAX_MEM=512
   ENABLE_DEBUG=1
fi

# garbage collector/memory tunning
JAVA_OPTS="$JAVA_OPTS -Xmx${JVM_MAX_MEM}m"

if [ "x$JVM_MIN_MEM" != "x" ]; then
   JAVA_OPTS="$JAVA_OPTS -Xms${JVM_MIN_MEM}m"
fi

if [ "x$JVM_YOUNG_MEM" != "x" ]; then
   JAVA_OPTS="$JAVA_OPTS -Xmn${JVM_YOUNG_MEM}m"
fi


JAVA_OPTS="$JAVA_OPTS -XX:MetaspaceSize=64m -XX:MaxMetaspaceSize=256m -XX:MaxDirectMemorySize=1g -XX:MaxHeapFreeRatio=70 -Dsun.net.http.allowRestrictedHeaders=true"

# database setting for hibernate
echo "libra cofiguration file is $SERVER_HOME/config/application.properties" 
JAVA_OPTS="$JAVA_OPTS -Dspring.config.location=$SERVER_HOME/config/application.properties,$SERVER_HOME/config/order.properties"
JPDA_ADDRESS=9012
export JPDA_ADDRESS
export JAVA_OPTS
echo  "Starting thirdLdap(tomcat)..."
export CATALINA_BASE=$SERVER_HOME/tomcat
$LIBRA_HOME/tomcat/bin/catalina.sh jpda run & 
CATALINA_PID=`ps -ef |grep tomcat|grep thirdLdap|grep -v grep|awk '{print $2}'`
trap shutdown HUP INT QUIT KILL TERM USR1 USR2
echo "wait..."
wait $CATALINA_PID
#rm -f /tmp/$$
echo "done"

