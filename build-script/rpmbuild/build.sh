#!/usr/bin/env bash

set -x

#+100 for lost some commit
export GIT_REVISION=`expr $(git rev-list --all | wc -l) + 100`
#first ant dataaccess project,using ant here
export ANT_HOME=../tools/apache-ant-1.7.0
export PATH=$ANT_HOME/bin:$PATH

if test "$#" == 0
then
        ant
else
        ant "$1"
fi

RPM_DIR=`pwd`

RPMSPEC=${RPM_DIR}/SPECS
RPMSOURCE=${RPM_DIR}/SOURCES
RPMDST=${RPM_DIR}/RPMS
mkdir -p ${RPMSPEC}
mkdir -p ${RPMSOURCE}
mkdir -p ${RPMDST}

cp -rf thirdLdap.spec ${RPMSPEC}

source ../info.dat
APP_VERSION=$major.$minor.$revision

if [ ! -n "$private_cloud" ]; then
 echo "private_cloud is null"
else
 sed -i "s/ainemo-/${private_cloud}ainemo-/g" ${RPMSPEC}/thirdLdap.spec
fi

VER_LINE=`grep -n "Version" thirdLdap.spec | cut -d: -f1 | sed  's/^[[:space:]]*//'`
sed -i -e''${VER_LINE}'c\Version : '${APP_VERSION}'' ${RPMSPEC}/thirdLdap.spec

REL_LINE=`grep -n "Release" thirdLdap.spec | cut -d: -f1 | sed  's/^[[:space:]]*//'`
sed -i -e''${REL_LINE}'c\Release : '${GIT_REVISION}'' ${RPMSPEC}/thirdLdap.spec

sed -i "/Packager/c\Packager: `git rev-list --all | head -1`" ${RPMSPEC}/thirdLdap.spec

tar -zxvf dist/${GIT_REVISION}/thirdLdap_${GIT_REVISION}.tgz
cp -rf thirdLdap/thirdLdap.tar ${RPMSOURCE}
rm -rf thirdLdap
echo "------------------------------------------------------------------------------------------------1---$_topdir"
rpmbuild --define "_topdir ${RPM_DIR}" -vv -bb ${RPMSPEC}/thirdLdap.spec
HWPF=`uname -i | sed  's/^[[:space:]]*//'`

mv ${RPMDST}/${HWPF}/*.rpm .
