<project name="libra" default="all" basedir=".">

    <property environment="SystemVariable" />
    <property name="git.revision" value="${SystemVariable.GIT_REVISION}" />

    <property name="libra.version" value="${git.revision}" />
    <property name="dist.dir" value="${basedir}/dist/${libra.version}" />

    <target name="all" depends="contact,sqlupgrade" />


	<target name="contact">
        <property name="contact.to.dir" value="${dist.dir}/contact" />
		<delete dir="${contact.to.dir}" />
		<mkdir dir="${contact.to.dir}" />
		<property name="contact.rpm.dir" value="${basedir}/../rpmbuild" />
		<delete>
			<fileset dir="${contact.rpm.dir}" includes="**/*.rpm" />
		</delete>
		<exec executable="bash" dir="${contact.rpm.dir}" failonerror="true" >
			<arg line="build.sh" />
		</exec>
		<copy todir="${contact.to.dir}">
			<fileset dir="${contact.rpm.dir}" includes="**/*.rpm" />
		</copy>
	</target>  
	  
    <target name="sqlupgrade">
        <property name="sqlupgrade.to.dir" value="${dist.dir}/sqlupgrade" />
        <delete dir="${sqlupgrade.to.dir}" />
        <mkdir dir="${sqlupgrade.to.dir}" />

        <property name="sqlupgrade.rpm.dir" value="${basedir}/../sqlupgrade_rpmbuild" />
        <delete>
            <fileset dir="${sqlupgrade.rpm.dir}" includes="**/*.rpm" />
        </delete>

        <exec executable="bash" dir="${sqlupgrade.rpm.dir}" failonerror="true" >
            <arg line="build.sh" />
        </exec>

        <copy todir="${sqlupgrade.to.dir}">
            <fileset dir="${sqlupgrade.rpm.dir}" includes="**/*.rpm" />
        </copy>

    </target>   	                                                                                 
	
</project>
