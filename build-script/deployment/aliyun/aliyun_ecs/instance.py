import sys
import urllib

from time import gmtime, strftime
import datetime, time
from ecs import *


def CreateInstance(RegionId, InstanceName, InstanceType, SecurityGroupId, ImageId='centos6u5_64_20G_aliaegis_20140926.vhd', InternetChargeType='PayByTraffic', InternetMaxBandwidthOut='100', PassWord='Wow1nemo'):
	user_params = {}
	user_params['Action'] = 'CreateInstance'
	user_params['InstanceName'] = InstanceName
	user_params['InstanceType'] = InstanceType
	user_params['InternetChargeType'] = InternetChargeType
	user_params['InternetMaxBandwidthOut'] = InternetMaxBandwidthOut
	user_params['PassWord'] = PassWord
	user_params['RegionId'] = RegionId
	user_params['SecurityGroupId'] = SecurityGroupId
	user_params['ImageId'] = ImageId

	response = make_request(user_params, True)
        return response

def DeleteInstance(InstanceId):
	user_params = {}
	user_params['Action'] = 'DeleteInstance'
	user_params['InstanceId'] = InstanceId
	
	response = make_request(user_params, True)
        return response

def StartInstance(InstanceId):
        user_params = {}
        user_params['Action'] = 'StartInstance'
        user_params['InstanceId'] = InstanceId
        
        response = make_request(user_params, True)
        return response

def StopInstance(InstanceId):
        user_params = {}
        user_params['Action'] = 'StopInstance'
        user_params['InstanceId'] = InstanceId

        response = make_request(user_params, True)
        return response

def DescribeInstanceStatus(RegionId):
	user_params = {}
        user_params['Action'] = 'DescribeInstanceStatus'
        user_params['RegionId'] = RegionId

	response = make_request(user_params, True)
        return response


def DescribeInstanceAttribute(InstanceId):
	user_params = {}
        user_params['Action'] = 'DescribeInstanceAttribute'
        user_params['InstanceId'] = InstanceId

        response = make_request(user_params, True)
        return response
