from instance import *
from ecs import *
import ConfigParser

#setup_credentials()
#CreateInstance('cn-beijing', 'stress-test-vm1', 'ecs.s1.small', 'sg-25jcfeue4')
#DeleteInstance('i-25jrsugjl')
#DescribeInstanceStatus('cn-beijing')
#DescribeInstanceAttribute('i-25vnpwlxz')

mainConfig = ConfigParser.ConfigParser()
mainConfig.read("config.ini")

serverConfig = ConfigParser.ConfigParser()
serverConfig.read("servers.ini")

serverType = serverConfig.get('servers', 'serverType')
serverEnv = serverConfig.get('servers', 'serverEnv')
serverCount = int(serverConfig.get('servers', 'serverCount'))
serverStartIndex = int(serverConfig.get('servers', 'startIndex'))
serverInstanceType = mainConfig.get('serverInstanceType', serverType)

setup_credentials()

index = 0
while index < serverCount:
	#response = CreateInstance(mainConfig.get('main','regionId'), serverEnv+'-'+serverType+str(serverStartIndex), serverInstanceType, 'sg-25jcfeue4')
	#print response
	index = index + 1
	serverStartIndex = serverStartIndex + 1


#setup_credentials()
#response = CreateInstance(mainConfig.get('main','regionId'), serverEnv+'-'+serverType+serverStartIndex, serverInstanceType, 'sg-25jcfeue4')
#print response
DeleteInstance('i-256mi635b')
DeleteInstance('i-25697496i')
DeleteInstance('i-25wqkip75')
#DescribeInstanceStatus('cn-beijing')
#DescribeInstanceAttribute('i-25vnpwlxz')

