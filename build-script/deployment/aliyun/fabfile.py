from fabric.api import env, run, put, parallel

#fab -H ********** bootupAndApply:your-hostname
#fab -H ********** bootupWithoutApply:your-hostname
#fab -H ********** kickerApply
#fab -H ********** restartService

env.use_ssh_config = True
env.disable_known_hosts = True

def dev_ali_dev0_set_host():
    env.hosts = [
            '*************',  # databse
            '**************', # gateway
            '**************', # stun server
            '*************',  # nettool
            '**************', # sig  !!! should be restart at first
            '**************', # recording manager server
            '**************', # xmcu1
#            '*************',  # xmcu2
            '**************', # nginx
            '*************',  # pivot1
            '*************',  # pivot2
            '************',   # vod
            '*************',  # vodnginx
            '**************', # access1
            '*************',  # access2
            '************',   # familyinfo
            '**************', # face
            '*************',  # redis
            '*************',  # vodbroker
            '*************',  # statis
            '*************',  # vodshare
            '************',   # pushserver
            '************',   # gamecall server
            '**************', # page server  
            '**************', # gamejob server
            '**************', # content server
            '*************',  # image server
            '*************',  # charge server
            ]

def pre_ali_set_host():
    env.hosts = [
            '*************',  # databse
            '*************',  # sig  !!! should be restart at first
            '*************',  # xmcu
            '***********',    # xmcu1
            '*************',  # nginx
            '**************', # pivot1
            '*************',  # pivot
            '*************',  # vod
            '**************', # vodnginx
            '**************', # access  !!! must be restarted after sig restart
            '*************',  # access1
       #     '************',   # redis
            '*************',  # vodbroker
            '**************', # statis
            '*************',  # vodshare
            '************',   # pushserver  !!! must be restarted after sig restart
            '************',   # gamecall  !!! every 60 seconds regist to sig server
            '*************',  # pageserver
            '**************', # gamejob 
            '************',   # gateway
            '************',   #sig gateway
            '*************',  # nettool
            '***********',     # image server
           # '***********',    #content source
           # '**************',  #semanticse
        #    '************',   # stun server
            ]

def pre_ali_set_host_check():
    env.hosts = [
            '*************',  # databse
            '*************',  # sig  !!! should be restart at first
            '*************',  # xmcu
            '***********',    # xmcu1
#            '*************',  # nginx
            '**************', # pivot1
            '*************',  # pivot
            '*************',  # vod
#            '**************', # vodnginx
            '**************', # access  !!! must be restarted after sig restart
            '*************',  # access1
#            '************',   # redis
            '*************',  # vodbroker
            '**************', # statis
            '*************',  # vodshare
            '************',   # pushserver  !!! must be restarted after sig restart
            '************',   # gamecall  !!! every 60 seconds regist to sig server
            '*************',  # pageserver
            '**************', # gamejob
            '************',   # gateway
            '************',   #sig gateway1
            '*************',  # nettool
            '***********',     #image server
       #     '***********',    #content source
        #    '**************',  #semantics
            '************',   # stun server
            ]

def prd_ali_set_host():
    env.hosts = [
            '*************',  # databse
          #  '************',   # amq
            '**************', # sig  !!! should be restart at first
            '**************', # xmcu
            '**************', # xmcu1
            '**************', #xmcu2
            '************',   # nginx
            '************',   # pivot
            '*************',  # pivot1
            '************',   # vod
            '**************', # vodnginx
            '*************',  # access
            '**************', # access1
        #    '************',   # redis
            '*************',  # vodbroker
            '**************', # statis
            '10.173.55.42',   # vodshare
            '10.173.55.88',   # nconsole
            '10.170.218.248', # pushserver
            '10.251.5.98',    # gamecall !!! every 60 seconds regist to sig server
            '10.171.103.183', # pageserver
            '10.172.219.173', # gamejob
            '10.171.28.54',   # gateway
            '10.170.182.76', #sig gateway3
            '10.172.233.184', # nettool
            '10.171.101.164',  # image server 
           '10.173.8.229',    # content server
         #  '10.171.115.187',  #content source
         #  '10.172.233.115',  #semantics
         #   '10.171.128.22',  # stun server
            ]
def prd_ali_set_host_check():
    env.hosts = [
            '*************',  # databse
            '************',   # amq
            '**************', # sig  !!! should be restart at first
            '**************', # xmcu
            '**************', # xmcu1
            '**************', #xmcu2
#            '************',   # nginx
            '************',   # pivot
            '*************',  # pivot1
            '************',   # vod
#            '**************', # vodnginx
            '*************',  # access
            '**************', # access1
#            '************',   # redis
            '*************',  # vodbroker
            '**************', # statis
            '10.173.55.42',   # vodshare
            '10.173.55.88',   # nconsole
            '10.170.218.248', # pushserver
            '10.251.5.98',    # gamecall !!! every 60 seconds regist to sig server
            '10.171.103.183', # pageserver
            '10.172.219.173', # gamejob
            '10.171.28.54',   # gateway
            '10.170.182.76', #sig gateway1
            '10.172.233.184',  # nettool
            '10.171.101.164',  # image server
            '10.173.8.229',    # content server
            #'10.171.115.187',  #content source
            #'10.172.233.115',  #semantics
            '10.171.128.22',  # stun server
            ]

def dev_ali_dev1_set_host():
    env.hosts = [
            '10.170.190.190', # databse
            '10.162.204.28',  # sig  !!! should be restart at first
            '10.162.197.53',  # xmcu
            '10.170.185.108', # pivot
            '10.170.187.57',  # vod
#            '10.170.191.49', # access
            '10.170.190.137', # redis
            '10.173.42.82',   # vodbroker
#            '10.170.176.66', # statis
            '10.173.51.63'    # vodshare
            ]

def dev_ali_dev2_set_host():
    env.hosts = [
            '10.165.67.218',  # gateway
            '10.172.169.48',  # sig  !!! should be restart at first
            '10.170.220.232', # xmcu
            '10.170.222.56',  # pivot
            '10.170.223.226', # vod
            '10.251.2.104',   # access
            '10.251.2.120',   # redis
            '10.172.170.104', # vodbroker
            '10.172.171.25',  # statis
            '10.171.43.172',   # vodshare
            '10.171.42.32'    #gamecall
            ]

def dev_ali_nconsole():
    env.host = [
            '10.163.8.126',
            ]

def hosts():
    env.Employee = 'root'
    env.hosts = ['dev.ali.dev.stun'] # xmcu

def updateAliyunRepo():
    put('update_source.sh', '/usr/bin/update_source.sh', mode=0700)
    run("/usr/bin/update_source.sh")

def setupPuppetLabRepo():
    run('rpm -qa | grep puppetlabs-release || rpm -ivh https://yum.puppetlabs.com/el/6/products/x86_64/puppetlabs-release-6-7.noarch.rpm')

def setupOurCollectRepo():
    put('ourcollect.repo' , '/etc/yum.repos.d/ourcollect.repo')
    run("/usr/bin/yum --disablerepo=* --enablerepo=ourcollect clean all")

def setupLabOurCollectRepo():
    put('ourcollect_lab.repo' , '/etc/yum.repos.d/ourcollect_lab.repo')
    run("/usr/bin/yum --disablerepo=* --enablerepo=ourcollect clean all")

def setupEpelAndRpmForgeRepo():
    run('rpm -ivh http://dl.fedoraproject.org/pub/epel/6Server/x86_64/epel-release-6-8.noarch.rpm')
    run('rpm -ivh http://pkgs.repoforge.org/rpmforge-release/rpmforge-release-0.5.3-1.el6.rf.x86_64.rpm')
    put('mongodb.repo' , '/etc/yum.repos.d/mongodb.repo')
    run("/usr/bin/yum clean all")

def installPuppet():
    run("yum install -y puppet hiera factor")

def setupHostname(hn):
    run("hostname "+hn)
    run("sed -i 's/HOSTNAME.*$/HOSTNAME="+hn+"/g' /etc/sysconfig/network")
    run("echo '127.0.0.1 "+hn+"' >>/etc/hosts")

def setupPuppetClient():
    run("mkdir -p /root/.ssh")
    run("chmod 700 /root/.ssh")
    run("yum install -y git")
    put("./git_rsa","/root/.ssh/git_rsa", mode=0600)
    put("./git_rsa","/root/.ssh/id_rsa", mode=0600)
    put("./pgitupdate.sh","/usr/bin/pgitupdate.sh", mode=0700)
    put("./papply.sh","/usr/bin/papply.sh", mode=0700)

def initPrdPapply():
    run("cd /etc/ && if [ -e /etc/puppet ];then rm -rf /etc/puppet;fi && git clone -b master git@10.171.12.26:/opt/git/puppet.git")
    run("/usr/bin/papply.sh")

def initDevPapply():
    run("cd /etc/ && if [ -e /etc/puppet ];then rm -rf /etc/puppet;fi && git clone git@10.171.12.26:/opt/git/puppet.git")
    run("/usr/bin/papply.sh")

def initOutPapply():
    put("./sshconfig","/root/.ssh/config", mode=0600)
    run("cd /etc/ && if [ -e /etc/puppet ];then rm -rf /etc/puppet;fi && git clone git@10.171.12.26:/opt/git/puppet.git")
    run("/usr/bin/papply.sh")
#def initOutPapply():
    #run("cd /etc/ && if [ -e /etc/puppet ];then rm -rf /etc/puppet;fi && git clone git@10.171.12.26:/opt/git/puppet.git")
    #run("/usr/bin/papply.sh")


def bootupAndApply(hn):
    env.password = 'Wow1nemo' 
    setupHostname(hn)
    setupPuppetClient()
    if  hn.startswith("dev") or hn.startswith("stress"):
        updateAliyunRepo()
        setupOurCollectRepo()
        installPuppet()
        initDevPapply()
    elif hn.startswith("lab"):
        setupPuppetLabRepo()
        installPuppet()
        setupLabOurCollectRepo()
        setupEpelAndRpmForgeRepo()
        initOutPapply()
    else: #pre && prd
        updateAliyunRepo()
        setupOurCollectRepo()
        installPuppet()
        initPrdPapply()

@parallel
def kickerApply():
    run('sudo /usr/bin/pgitupdate.sh')
    run('sudo /usr/bin/papply.sh')

def restartService():
    run('sudo /usr/bin/restart_service.sh')

def test():
    run('ls /usr')
    
def listAinemoBundle():
    run('rpm -qa | grep ainemo')

def listSuperStatus():
    run('supervisorctl status')
